package com.sankuai.meituan.waimai.customer.service.sign.biz;

import com.alibaba.fastjson.JSONObject;
import com.google.common.base.MoreObjects;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.sankuai.it.iam.common.base.gson.bridge.JSON;
import com.sankuai.meituan.waimai.customer.dao.WmEcontractSignManualTaskDBMapper;
import com.sankuai.meituan.waimai.customer.domain.SignBatchQueryParam;
import com.sankuai.meituan.waimai.customer.domain.WmEcontractSignManualTaskDB;
import com.sankuai.meituan.waimai.thrift.constant.WmContractErrorCodeConstant;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractTaskApplyTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.ManualTaskApplyBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.ManualTaskSettleContextBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.SignBatchOverTimeQueryThriftParam;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.annotations.Param;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Objects;
import java.util.Set;

@Slf4j
@Service
public class WmEcontractManualTaskBizService {
    private static final Logger LOGGER = LoggerFactory.getLogger(WmEcontractManualTaskBizService.class);

    @Autowired
    private WmEcontractSignManualTaskDBMapper wmEcontractSignManualTaskDBMapper;

    static Set<EcontractTaskApplyTypeEnum> needToAddBizIdTypeSet = Sets.newHashSet(
            EcontractTaskApplyTypeEnum.POIFEE,
            EcontractTaskApplyTypeEnum.GROUP_MEAL,
            EcontractTaskApplyTypeEnum.BUSINESS_CUSTOMER_E_CONTRACT,
            EcontractTaskApplyTypeEnum.POI_PROMOTION_SERVICE,
            EcontractTaskApplyTypeEnum.QUA_REAL_LETTER,
            EcontractTaskApplyTypeEnum.C2CONTRACT,
            EcontractTaskApplyTypeEnum.MED_DEPOSIT,
            EcontractTaskApplyTypeEnum.FOODCITY_STATEMENT,
            EcontractTaskApplyTypeEnum.MEDIC_ORDER_SPLIT,
            EcontractTaskApplyTypeEnum.SUBJECT_CHANGE_SUPPLEMENT,
            EcontractTaskApplyTypeEnum.AGENT_SQS_STANDARD,
            EcontractTaskApplyTypeEnum.FOUR_WHEEL_PERFORMANCE_SUPPLEMENT_AGREEMENT,
            EcontractTaskApplyTypeEnum.SPEEDY_DELIVERY_COOPERATION_AGREEMENT,
            EcontractTaskApplyTypeEnum.DRONE_DELIVERY,
            EcontractTaskApplyTypeEnum.FRUIT_TOGETHER_DELIVERY,
            EcontractTaskApplyTypeEnum.VIP_CARD_CONTRACT_AGREEMENT,
            EcontractTaskApplyTypeEnum.NATIONAL_SUBSIDY_PURCHASE,
            EcontractTaskApplyTypeEnum.NATIONAL_SUBSIDY_DISTRIBUTOR_DELIVERY,
            EcontractTaskApplyTypeEnum.NATIONAL_SUBSIDY_HEADQUARTERS_DELIVERY,
            EcontractTaskApplyTypeEnum.DAOCAN_SERVICE_C1_CONTRACT,
            EcontractTaskApplyTypeEnum.DAOCAN_SERVICE_C2_CONTRACT,
            EcontractTaskApplyTypeEnum.COMMON_CONFIG_FRAME_CONTRACT_AGREEMENT
    );

    private long getWmPoiIdFromManualTaskApplyBo(ManualTaskApplyBo manualTaskApplyBo) {
        return needToAddBizIdTypeSet.contains(manualTaskApplyBo.getApplyTypeEnum()) ? manualTaskApplyBo.getBizId() : 0L;
    }


    public void checkDuplicate(ManualTaskApplyBo manualTaskApplyBo) throws WmCustomerException {
        long wmPoiId = getWmPoiIdFromManualTaskApplyBo(manualTaskApplyBo);

        int count = calculateCountOfManualTask(manualTaskApplyBo, wmPoiId);

        if (count > 0) {
            throw new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR, "待发起列表已有任务，请勿重复提交");
        }
    }

    private int calculateCountOfManualTask(ManualTaskApplyBo manualTaskApplyBo, long wmPoiId) {
        EcontractTaskApplyTypeEnum applyTypeEnum = manualTaskApplyBo.getApplyTypeEnum();
        if (EcontractTaskApplyTypeEnum.COMMON_CONFIG_FRAME_CONTRACT_AGREEMENT == applyTypeEnum) {
            return wmEcontractSignManualTaskDBMapper.getCountByCustomerIdAndModuleWithWmPoiId(
                    manualTaskApplyBo.getCustomerId(), manualTaskApplyBo.getModule(), wmPoiId);
        } else {
            return wmEcontractSignManualTaskDBMapper.getCountByCustomerIdAndModuleWithWmPoiId(
                    manualTaskApplyBo.getCustomerId(), manualTaskApplyBo.getApplyTypeEnum().getName(), wmPoiId);
        }
    }

    public long insertManualTask(ManualTaskApplyBo manualTaskApplyBo) {
        WmEcontractSignManualTaskDB taskDB = new WmEcontractSignManualTaskDB();
        taskDB.setCustomerId(manualTaskApplyBo.getCustomerId());
        taskDB.setWmPoiId(getWmPoiIdFromManualTaskApplyBo(manualTaskApplyBo));
        taskDB.setModule(extractModule(manualTaskApplyBo));
        taskDB.setApplyContext(MoreObjects.firstNonNull(manualTaskApplyBo.getApplyContext(),""));
        taskDB.setCommitUid(manualTaskApplyBo.getCommitUid());
        taskDB.setValid((byte)1);
        wmEcontractSignManualTaskDBMapper.insertSelective(taskDB);
        return taskDB.getId();
    }

    private String extractModule(ManualTaskApplyBo manualTaskApplyBo) {
        EcontractTaskApplyTypeEnum applyTypeEnum = manualTaskApplyBo.getApplyTypeEnum();
        if (EcontractTaskApplyTypeEnum.COMMON_CONFIG_FRAME_CONTRACT_AGREEMENT == applyTypeEnum) {
            return manualTaskApplyBo.getModule();
        } else {
            return applyTypeEnum.getName();
        }
    }

    public int deleteManualTask(long manualTaskId) {
        return wmEcontractSignManualTaskDBMapper.deleteByPrimaryKey(manualTaskId);
    }

    public int deleteManualTask(int wmCustomerId, String module) {
        return wmEcontractSignManualTaskDBMapper.deleteByCustomerIdAndModule(wmCustomerId,module);
    }

    public int deleteManualTaskWithBizId(int wmCustomerId, long bizId, String module) {
        return wmEcontractSignManualTaskDBMapper.deleteByCustomerIdAndModuleAndBizId(wmCustomerId, bizId, module);
    }

    public int batchDeleteManualTask(List<Long> manulTaskIds){
        return wmEcontractSignManualTaskDBMapper.batchDeleteByPrimaryKey(manulTaskIds);
    }

    public List<Long> getManualTaskByWmCustomerIdAndModule(int wmCustomerId, String module) {
        return wmEcontractSignManualTaskDBMapper.getManualTaskIdByCustomerIdAndModule(wmCustomerId, module);
    }

    public Long getManualTaskByWmCustomerIdAndModuleAndBizId(int wmCustomerId, long bizId, String module) {
        return wmEcontractSignManualTaskDBMapper.getManualTaskIdByCustomerIdAndModuleAndBizId(wmCustomerId,
            bizId,
            module);
    }

    public WmEcontractSignManualTaskDB getManualTaskByCustomerIdAndModuleAndBizId(int wmCustomerId, long bizId, String module, long manualBatchId) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_1", "com.sankuai.meituan.waimai.customer.service.sign.biz.WmEcontractManualTaskBizService.getManualTaskByCustomerIdAndModuleAndBizId(int,long,java.lang.String,long)");
        log.info("WmEcontractManualTaskBizService#getManualTaskByCustomerIdAndModuleAndBizId, wmCustomerId: {}, bizId: {}, module: {}, manualBatchId: {}",
                wmCustomerId, bizId, module);
        WmEcontractSignManualTaskDB signManualTaskDB = wmEcontractSignManualTaskDBMapper.getManualTaskByCustomerIdAndModuleAndBizId(wmCustomerId, bizId, module, manualBatchId);
        log.info("WmEcontractManualTaskBizService#getManualTaskByCustomerIdAndModuleAndBizId, signManualTaskDB: {}", JSON.toJSONString(signManualTaskDB));
        return signManualTaskDB;
    }

    public List<WmEcontractSignManualTaskDB> batchGetByManualTaskIds(List<Long> manualTaskIds) {
        return wmEcontractSignManualTaskDBMapper.batchGetByManualTaskIds(manualTaskIds);
    }

    public List<WmEcontractSignManualTaskDB> batchGetByManualTaskIdsRT(List<Long> manualTaskIds) {
        return wmEcontractSignManualTaskDBMapper.batchGetByManualTaskIdsRT(manualTaskIds);
    }

    public List<WmEcontractSignManualTaskDB> batchGetByManualTaskIdsIgnoreValid(List<Long> manualTaskIds) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.sign.biz.WmEcontractManualTaskBizService.batchGetByManualTaskIdsIgnoreValid(java.util.List)");
        return wmEcontractSignManualTaskDBMapper.batchGetByManualTaskIdsIgnoreValid(manualTaskIds);
    }

    public List<WmEcontractSignManualTaskDB> batchGetByManualBatchIds(List<Long> manualBatchIds) {
        return wmEcontractSignManualTaskDBMapper.batchGetByManualBatchIds(manualBatchIds);
    }

    public List<WmEcontractSignManualTaskDB> batchGetByManualBatchIdsRT(List<Long> manualBatchIds) {
        return wmEcontractSignManualTaskDBMapper.batchGetByManualBatchIdsRT(manualBatchIds);
    }

    public List<WmEcontractSignManualTaskDB> queryByManualBatchIdAndModule(Long manualBatchId, String module) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.service.sign.biz.WmEcontractManualTaskBizService.queryByManualBatchIdAndModule(java.lang.Long,java.lang.String)");
        return wmEcontractSignManualTaskDBMapper.queryByManualBatchIdAndModule(manualBatchId, module);
    }

    public List<WmEcontractSignManualTaskDB> getWaitManualTaskIdsByModuleRT(long wmCustomerId, String module) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.service.sign.biz.WmEcontractManualTaskBizService.getWaitManualTaskIdsByModuleRT(long,java.lang.String)");
        return wmEcontractSignManualTaskDBMapper.getWaitManualTaskIdsByModuleRT(wmCustomerId, module);
    }

    public int updateManualTaskBatchId(List<Long> manualTaskIds, long batchId) {
        return wmEcontractSignManualTaskDBMapper.updateManualTaskBatchId(manualTaskIds, batchId);
    }

    public void deleteByCustomerId(Integer customerId) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.sign.biz.WmEcontractManualTaskBizService.deleteByCustomerId(java.lang.Integer)");
        wmEcontractSignManualTaskDBMapper.deleteByCustomerId(customerId);
    }

    //仅处理配送模块+结算模块
    public void cancelByUnbindingPoi(Integer customerId, Long wmPoiId) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_1", "com.sankuai.meituan.waimai.customer.service.sign.biz.WmEcontractManualTaskBizService.cancelByUnbindingPoi(java.lang.Integer,java.lang.Long)");
        List<WmEcontractSignManualTaskDB> poiFeeTaskInfo = wmEcontractSignManualTaskDBMapper
            .getManualTaskByCustomerIdAndModule(customerId,
                        EcontractTaskApplyTypeEnum.POIFEE.getName());
        List<WmEcontractSignManualTaskDB> settleTaskInfo = wmEcontractSignManualTaskDBMapper
                .getManualTaskByCustomerIdAndModule(customerId,
                        EcontractTaskApplyTypeEnum.SETTLE.getName());
        List<WmEcontractSignManualTaskDB> taskInfo  = Lists.newArrayList();
        taskInfo.addAll(poiFeeTaskInfo);
        taskInfo.addAll(settleTaskInfo);

        if (CollectionUtils.isEmpty(taskInfo)) {
            return;
        }
        for(WmEcontractSignManualTaskDB temp : taskInfo){
            if(temp.getWmPoiId().equals(wmPoiId)){
                wmEcontractSignManualTaskDBMapper.deleteByPrimaryKey(temp.getId());
            }
        }
    }

    public List<WmEcontractSignManualTaskDB> queryWithParam(SignBatchQueryParam queryParam) {
        return wmEcontractSignManualTaskDBMapper.queryWithParam(queryParam);
    }

    public List<Long> getManualTaskIdByCustomerId(int wmCustomerId) {
        return wmEcontractSignManualTaskDBMapper.getManualTaskIdByCustomerId(wmCustomerId);
    }

    public List<Long> getManualTaskIdList(int wmCustomerId, int startTime) {
        return wmEcontractSignManualTaskDBMapper.getManualTaskIdList(wmCustomerId, startTime);
    }

    public List<WmEcontractSignManualTaskDB> getManualTaskInfoByCustomerId(int wmCustomerId) {
        return wmEcontractSignManualTaskDBMapper.getManualTaskInfoByCustomerId(wmCustomerId);
    }

    public WmEcontractSignManualTaskDB getManualTaskInfoByCustomerIdAndModuleRT(int wmCustomerId,String module) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_1", "com.sankuai.meituan.waimai.customer.service.sign.biz.WmEcontractManualTaskBizService.getManualTaskInfoByCustomerIdAndModuleRT(int,java.lang.String)");
        return wmEcontractSignManualTaskDBMapper.getManualTaskInfoByCustomerIdAndModuleRT(wmCustomerId,module);
    }

    public List<WmEcontractSignManualTaskDB> getManualTaskIdByCustomerIdList(List<Integer> wmCustomerIdList) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.sign.biz.WmEcontractManualTaskBizService.getManualTaskIdByCustomerIdList(java.util.List)");
        return wmEcontractSignManualTaskDBMapper.getManualTaskIdByCustomerIdList(wmCustomerIdList);
    }

    public List<WmEcontractSignManualTaskDB> getManualTaskByCustomerIdAndModule(Integer wmCustomerId, String module) {
        return wmEcontractSignManualTaskDBMapper.getManualTaskByCustomerIdAndModule(wmCustomerId, module);
    }
    public List<WmEcontractSignManualTaskDB> queryOverTimeTaskWithParam(SignBatchOverTimeQueryThriftParam queryParam) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_2", "com.sankuai.meituan.waimai.customer.service.sign.biz.WmEcontractManualTaskBizService.queryOverTimeTaskWithParam(SignBatchOverTimeQueryThriftParam)");
        return wmEcontractSignManualTaskDBMapper.queryOverTimeTaskWithParam(queryParam);
    }

    public Integer queryDeliveryTaskNumByCustomerId(Integer wmCustomerId, Long manualBatchId, String module){
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_1", "com.sankuai.meituan.waimai.customer.service.sign.biz.WmEcontractManualTaskBizService.queryDeliveryTaskNumByCustomerId(java.lang.Integer,java.lang.Long,java.lang.String)");
        return wmEcontractSignManualTaskDBMapper.getCountByCustomerIdAndMoudleAndManualBatchId(wmCustomerId,module,manualBatchId);
    }

    public String getMoudleByManualTaskId(Long manualTaskId){
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_2", "com.sankuai.meituan.waimai.customer.service.sign.biz.WmEcontractManualTaskBizService.getMoudleByManualTaskId(java.lang.Long)");
        WmEcontractSignManualTaskDB wmEcontractSignManualTaskDB = wmEcontractSignManualTaskDBMapper.selectByPrimaryKey(manualTaskId);
        if (Objects.nonNull(wmEcontractSignManualTaskDB)) {
            return wmEcontractSignManualTaskDB.getModule();
        }

        return null;
    }

    public WmEcontractSignManualTaskDB getManualTaskByManualTaskId(Long manualTaskId){
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_2", "com.sankuai.meituan.waimai.customer.service.sign.biz.WmEcontractManualTaskBizService.getManualTaskByManualTaskId(java.lang.Long)");
        return wmEcontractSignManualTaskDBMapper.selectByPrimaryKey(manualTaskId);
    }
}