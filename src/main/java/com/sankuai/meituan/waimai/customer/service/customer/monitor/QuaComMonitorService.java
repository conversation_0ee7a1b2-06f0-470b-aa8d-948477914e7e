package com.sankuai.meituan.waimai.customer.service.customer.monitor;

import com.sankuai.meituan.waimai.customer.adapter.WmPoiFlowlineLabelThriftServiceAdapter;
import com.sankuai.meituan.waimai.customer.config.MccCustomerConfig;
import com.sankuai.meituan.waimai.customer.dao.WmCustomerPoiDBMapper;
import com.sankuai.meituan.waimai.customer.ddd.adapter.WmPoiQueryAdapter;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerDB;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerPoiDB;
import com.sankuai.meituan.waimai.customer.service.customer.CusPoiRelEsBusinessService;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerMSCLabelService;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerPlatformDataParseService;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerPoiListEsService;
import com.sankuai.meituan.waimai.label.constants.LabelSubjectTypeEnum;
import com.sankuai.meituan.waimai.thrift.constants.WmPoiFieldQueryConstant;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerRealTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerRelationStatusEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.customer.CustomerPoiChildEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.WmCustomerPoiListConditionDTO;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.WmCustomerPoiListInfoDTO;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.WmCustomerPoiListPageData;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.monitor.PoiQuaComCheckDTO;
import com.sankuai.meituan.waimai.thrift.domain.WmPoiAggre;
import com.sankuai.meituan.waimai.thrift.domain.WmPoiLabelRel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Set;

@Service
@Slf4j
public class QuaComMonitorService {


    @Autowired
    protected WmCustomerPlatformDataParseService wmCustomerPlatformDataParseService;

    @Autowired
    private WmPoiFlowlineLabelThriftServiceAdapter wmPoiFlowlineLabelThriftServiceAdapter;

    @Autowired
    private WmCustomerMSCLabelService wmCustomerMSCLabelService;

    @Autowired
    private WmCustomerPoiDBMapper wmCustomerPoiDBMapper;

    @Autowired
    private WmPoiQueryAdapter wmPoiQueryAdapter;

    @Autowired
    private CusPoiRelEsBusinessService cusPoiRelEsBusinessService;

    @Autowired
    private WmCustomerPoiListEsService wmCustomerPoiListEsService;

    protected static final Set<String> WM_POI_FIELDS = com.google.common.collect.Sets.newHashSet(WmPoiFieldQueryConstant.WM_POI_FIELD_WM_POI_ID,
            WmPoiFieldQueryConstant.WM_POI_FIELD_NAME,
            WmPoiFieldQueryConstant.WM_POI_FIELD_FIRST_TAG,
            WmPoiFieldQueryConstant.WM_POI_FIELD_POI_BIZ_TYPES,
            WmPoiFieldQueryConstant.WM_POI_FIELD_BIZ_ORG_CODE,
            WmPoiFieldQueryConstant.WM_POI_FIELD_LABEL_IDS,
            WmPoiFieldQueryConstant.WM_POI_FIELD_CITY_LOCATION_ID,
            WmPoiFieldQueryConstant.WM_POI_FIELD_VALID
    );


    /**
     * 绑定或解绑校验门店的资质共用标签
     *
     * @param poiQuaComCheckDTO
     * @return
     */
    public String checkBindOrUnBindPoiQuaComTag(PoiQuaComCheckDTO poiQuaComCheckDTO) {
        Integer valid = poiQuaComCheckDTO.getValid();
        Integer oldValid = poiQuaComCheckDTO.getOldValid();
        Integer status = poiQuaComCheckDTO.getStatus();
        Integer oldStatus = poiQuaComCheckDTO.getOldStatus();
        Integer customerId = poiQuaComCheckDTO.getCustomerId();
        Integer wmPoiId = poiQuaComCheckDTO.getWmPoiId().intValue();

        //只关注已绑定或解绑事件，中间流程数据不关注
        if (status != CustomerRelationStatusEnum.BIND.getCode() && valid != 0) {
            return null;
        }
        //状态变更校验且新状态为已绑定
        if (oldStatus != status && status == CustomerRelationStatusEnum.BIND.getCode()) {
            return checkBindMscHasQuaComTag(wmPoiId, customerId);
        }
        //更新为无效，客户门店关系解绑
        if (valid != oldValid && valid == 0) {
            return checkUnBindPoiQuaComTag(wmPoiId, customerId);
        }
        return null;
    }


    /**
     * 美食城客户绑定门店，如果客户有资质共用标判断门店是否有
     *
     * @param wmPoiId
     * @param customerId
     * @return
     */
    private String checkBindMscHasQuaComTag(Integer wmPoiId, Integer customerId) {
        try {
            WmCustomerDB wmCustomerDB = wmCustomerPlatformDataParseService.selectCustomerFromPlatformById(customerId);
            if (wmCustomerDB == null) {
                return null;
            }
            if (CustomerRealTypeEnum.MEISHICHENG.getValue() != wmCustomerDB.getCustomerRealType()) {
                return null;
            }
            boolean hasCustomerQuaComTag = wmCustomerMSCLabelService.checkHasQuaComCustomerLabel(wmCustomerDB.getMtCustomerId());
            if (!hasCustomerQuaComTag) {
                return null;
            }
            //判断门店是否有资质共用标签
            long poiLabelId = MccCustomerConfig.getQuaCommonPoiLabel();
            WmPoiLabelRel poiLabel = wmPoiFlowlineLabelThriftServiceAdapter.selectLabelRelCache(wmPoiId, poiLabelId, LabelSubjectTypeEnum.POI.getCode());
            if (poiLabel == null || (poiLabel != null && poiLabel.getId() <= 0L)) {
                return String.format("美食城客户有资质共用标，但绑定门店无资质共用标签,门店ID:%s,客户ID:%s", wmPoiId, customerId);
            }
        } catch (Exception e) {
            log.error("客户绑定门店，判断资质共用标发生异常,wmPoiId={},customerId={}", wmPoiId, customerId, e);
        }
        return null;
    }

    /**
     * 解绑如果美食城客户有资质共用标签，需要判断门店是否还有资质共用标
     *
     * @param wmPoiId
     * @param customerId
     * @return
     */
    private String checkUnBindPoiQuaComTag(Integer wmPoiId, Integer customerId) {
        try {
            WmCustomerDB wmCustomerDB = wmCustomerPlatformDataParseService.selectCustomerFromPlatformById(customerId);
            if (wmCustomerDB == null) {
                return null;
            }
            if (CustomerRealTypeEnum.MEISHICHENG.getValue() != wmCustomerDB.getCustomerRealType()) {
                return null;
            }
            boolean hasCustomerQuaComTag = wmCustomerMSCLabelService.checkHasQuaComCustomerLabel(wmCustomerDB.getMtCustomerId());
            if (!hasCustomerQuaComTag) {
                return null;
            }
            //判断门店是否有资质共用标签
            long poiLabelId = MccCustomerConfig.getQuaCommonPoiLabel();
            WmPoiLabelRel poiLabel = wmPoiFlowlineLabelThriftServiceAdapter.selectLabelRelCache(wmPoiId, poiLabelId, LabelSubjectTypeEnum.POI.getCode());
            if (poiLabel != null && poiLabel.getId() > 0L) {
                return String.format("美食城客户解绑门店，但门店仍有资质共用标,门店ID:%s,客户ID:%s", wmPoiId, customerId);
            }
        } catch (Exception e) {
            log.error("客户解绑门店，判断资质共用标发生异常,wmPoiId={},customerId={}", wmPoiId, customerId, e);
        }
        return null;
    }


    /**
     * 校验客户是否还有资质共用标签
     *
     * @param customerId
     * @return
     */
    public String checkCustomerQuaComTag(Integer customerId) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.service.customer.monitor.QuaComMonitorService.checkCustomerQuaComTag(java.lang.Integer)");
        try {
            WmCustomerDB wmCustomerDB = wmCustomerPlatformDataParseService.selectCustomerFromPlatformById(customerId);
            if (wmCustomerDB == null) {
                return null;
            }
            boolean hasCustomerQuaComTag = wmCustomerMSCLabelService.checkHasQuaComCustomerLabel(wmCustomerDB.getMtCustomerId());
            if (hasCustomerQuaComTag) {
                return String.format("客户类型由美食城修改为非美食城，客户仍有资质共用标签，请关注,customerId={},wmCustomerId={}", customerId, wmCustomerDB.getMtCustomerId());
            }
        } catch (Exception e) {
            log.error("客户绑定门店，判断资质共用标发生异常,customerId={}", customerId, e);
        }
        return null;
    }

    /**
     * 根据门店与变更标签校验ES中是否子门店数据
     *
     * @param wmPoiId
     * @param labelId
     * @return
     */
    public String checkChildPoiFlagWithEs(Long wmPoiId, Integer labelId) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.customer.monitor.QuaComMonitorService.checkChildPoiFlagWithEs(java.lang.Long,java.lang.Integer)");
        WmPoiAggre wmPoiAggre = wmPoiQueryAdapter.getWmPoiAggre(wmPoiId.longValue(), WM_POI_FIELDS);
        if (wmPoiAggre == null) {
            log.error("checkChildPoiFlagWithEs,门店ID不存在，未查询到有效记录,wmPoiId={}", wmPoiId);
            return String.format("门店标签发生变更，但未查询到门店(%s)信息", wmPoiId);
        }

        List<WmCustomerPoiDB> list = wmCustomerPoiDBMapper.listCustomerPoiRelByWmPoiId(wmPoiId);
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        Integer childPoiFlagNew = null;
        if (MccCustomerConfig.getSubPoiIdentifySwitch()) {
            childPoiFlagNew = cusPoiRelEsBusinessService.checkChildPoiByPoiType(wmPoiAggre.getSub_wm_poi_type()) ? CustomerPoiChildEnum.IS_CHILD_POI.getCode() : CustomerPoiChildEnum.NOT_CHILD_POI.getCode();
        } else {
            childPoiFlagNew = cusPoiRelEsBusinessService.checkChildPoiByTagId(wmPoiAggre.getLabel_ids()) ? CustomerPoiChildEnum.IS_CHILD_POI.getCode() : CustomerPoiChildEnum.NOT_CHILD_POI.getCode();
        }
        WmCustomerPoiListConditionDTO conditionDTO = new WmCustomerPoiListConditionDTO();
        conditionDTO.setWmPoiId(wmPoiId);
        WmCustomerPoiListPageData poiListPageData = wmCustomerPoiListEsService.queryData(conditionDTO);
        if (poiListPageData == null || CollectionUtils.isEmpty(poiListPageData.getList())) {
            return String.format("ES中未查询到门店关联客户关系,wmPoiId=%s", wmPoiId);
        }

        List<WmCustomerPoiListInfoDTO> poiListInfoDTOS = poiListPageData.getList();
        for (WmCustomerPoiListInfoDTO customerPoiListInfoDTO : poiListInfoDTOS) {
            Integer childPoiFlagEs = customerPoiListInfoDTO.getChildPoiFlag();
            if (childPoiFlagEs != childPoiFlagNew) {
                return String.format("ES中是否子门店字段有误，门店%s,当前是%s,应该是%s", wmPoiId, childPoiFlagEs == 1 ? "是" : "否", childPoiFlagNew == 1 ? "是" : "否");
            }
        }
        return null;
    }
}
