package com.sankuai.meituan.waimai.customer.service.kp.operatorv2.dboperatorv2;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.sankuai.meituan.util.DateUtil;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerDB;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerPlatformNoticeService;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerSensitiveWordsService;
import com.sankuai.meituan.waimai.customer.service.gray.WmCustomerGrayService;
import com.sankuai.meituan.waimai.customer.service.kp.operatorv2.preverifierv2.KpDataVerify;
import com.sankuai.meituan.waimai.customer.service.kp.operatorv2.preverifierv2.KpPreverify;
import com.sankuai.meituan.waimai.customer.service.kp.operatorv2.preverifierv2.KpTypeNumVerify;
import com.sankuai.meituan.waimai.customer.service.kp.statemachine.ability.KpLegalFlowAbility;
import com.sankuai.meituan.waimai.customer.service.kp.statemachine.context.KpLegalStatusMachineContext;
import com.sankuai.meituan.waimai.customer.util.ThrowUtil;
import com.sankuai.meituan.waimai.thrift.bo.WmCustomerDiffCellBo;
import com.sankuai.meituan.waimai.thrift.customer.constant.*;
import com.sankuai.meituan.waimai.thrift.customer.constant.customer.KpVersionEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.customer.PreAuthResultTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.PreAuthResultBO;
import com.sankuai.meituan.waimai.thrift.customer.domain.kp.WmCustomerKp;
import com.sankuai.meituan.waimai.thrift.customer.domain.kp.WmCustomerKpTemp;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import com.sankuai.meituan.waimai.thrift.exception.WmServerException;
import com.sankuai.meituan.waimai.thrift.util.DiffUtil;
import org.apache.commons.lang3.StringUtils;
import org.apache.thrift.TException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;

/**
 * Author: 窦旭蒙
 * MIS: douxumeng
 * Date: 2020-07-06 17:16
 * Email: <EMAIL>
 * Desc:
 */
@Service
public class LegalDBOperator extends KpDBOperator {

    private static final Logger LOGGER = LoggerFactory.getLogger(LegalDBOperator.class);

    @Autowired
    private WmCustomerSensitiveWordsService wmCustomerSensitiveWordsService;

    @Autowired
    private WmCustomerPlatformNoticeService noticeService;

    @Autowired
    public KpDataVerify kpDataVerify;

    @Autowired
    private KpLegalFlowAbility kpLegalFlowAbility;

    @Override
    public Object insert(WmCustomerDB wmCustomer, List<WmCustomerKp> oldCustomerKpList, List<WmCustomerKp> insertKpList, int uid, String uname) throws WmCustomerException, TException, WmServerException {
        WmCustomerKp insertKp = getOperateKp(insertKpList);

        if (null == insertKp) {
            ThrowUtil.throwClientError("待新增签约人KP信息为空");
        }
        WmCustomerKp oldCustomerKp = getOldKp(oldCustomerKpList, insertKp.getId());
        if (oldCustomerKp != null) {
            ThrowUtil.throwClientError("待新增KP已存在，请更新");
        }

        // 命中状态机模型,则执行初始化创建事件
        KpLegalStatusMachineContext kpLegalStatusMachineContext = KpLegalStatusMachineContext.builder()
                .customerId(insertKp.getCustomerId()).opType(KpOperationTypeConstant.INSERT).wmCustomer(wmCustomer)
                .wmCustomerKp(insertKp).opUid(uid).opUName(uname).existEffectiveFlag(false).oldCustomerKp(null).build();
        return kpLegalFlowAbility.insertKpWithSM(kpLegalStatusMachineContext);
      
    }

    @Override
    public Object update(WmCustomerDB wmCustomer, List<WmCustomerKp> oldCustomerKpList, List<WmCustomerKp> updateKpList, int uid, String uname) throws WmCustomerException, TException, WmServerException {
        WmCustomerKp updateKp = getOperateKp(updateKpList);

        WmCustomerKp oldCustomerKp = getOldKp(oldCustomerKpList, updateKp.getId());
        if (oldCustomerKp == null) {
            ThrowUtil.throwClientError("无法获取待更新KP信息");
        }
        if (updateKp == null) {
            return null;
        }
        Byte oldState = oldCustomerKp.getState();
        if (oldCustomerKp == null) {
            LOGGER.warn("未找到旧的KP法人，kpId={}", updateKp.getId());
            ThrowUtil.throwClientError("未找到旧的KP信息");
        }
        if (updateKp.getWdcClueId() == null) {
            oldCustomerKp.setWdcClueId(0l);
        }
        if (updateKp.getWdcClueId() == null) {
            updateKp.setWdcClueId(0l);
        }

        if (updateKp.getCertType() != CertTypeEnum.ID_CARD.getType()) {
            ThrowUtil.throwClientError("KP类型法人的证件类型只允许身份证");
        }

        //不符合规则需要操作删除并记录操作日志
        if (!checkLegalKp(wmCustomer, updateKp)) {
            String content = String.format("您负责的客户（%s%s），法人信息不符合要求，需要重新录入，请及时处理。", wmCustomer.getMtCustomerId(), wmCustomer.getCustomerName());
            //删除法人
            boolean deleteKp = deleteLegalKpAndSendMsg(oldCustomerKp, uid, uname, content);
            if (deleteKp) {
                //发送KP删除的消息给客户责任人
                noticeService.sendDeleteLegalKpMsg(wmCustomer, oldCustomerKp, content);
            }
            return null;
        }

        List<WmCustomerDiffCellBo> kpUpdateFields = differentCustomerKpService.getKpLegalUpdateFields(oldCustomerKp, updateKp);
        // 页面信息未修改
        if (kpUpdateFields.isEmpty()) {
            //说明没有更新
            if (KpLegalStateMachine.EFFECT.getState() == oldCustomerKp.getState()) {
                //授权失败状态保存需要清空标识
                WmCustomerKpTemp kpTempDB = wmCustomerKpTempDBMapper.selectByKpId(oldCustomerKp.getId());
                if (kpTempDB != null && kpTempDB.getState() == KpLegalStateMachine.PREAUTH_FAIL.getState()) {
                    LOGGER.info("未修改任何记录，清空临时数据，customerId:{},kpId={}, tempId={}", wmCustomer.getId(), oldCustomerKp.getId(), kpTempDB.getId());
                    wmCustomerKpTempDBMapper.deleteByPrimaryKey(kpTempDB.getId());
                    wmCustomerKpLogService.insertOplog(oldCustomerKp.getCustomerId(), oldCustomerKp, "【删除临时变更数据Id:" + kpTempDB.getId() + "】", uid, uname);
                }
                return null;
            } else if (KpLegalStateMachine.PREAUTH_FAIL.getState() != oldCustomerKp.getState()
                    && KpLegalStateMachine.RECORDED.getState() != oldCustomerKp.getState()) {
                //新建时，如果状态为认证失败，则提交保存时需要重新认证，否则这里直接返回（状态为非认证失败且无修改）
                return null;
            }
        }

        // KP法人未生效且客户未生效，KP签约人信息暂存
        if (KpLegalStateMachine.EFFECT.getState() != oldCustomerKp.getState() && wmCustomer.isUnEffectived()) {
            LOGGER.info("客户未生效，KP法人信息暂存 customerId:{},id:{},info:{}", wmCustomer.getId(), updateKp.getId(), JSON.toJSONString(updateKp));
            List<WmCustomerDiffCellBo> diffCellBos = DiffUtil.compare(oldCustomerKp, updateKp, differentCustomerKpService.getKpDiffFieldsMap());
            kpDBOperate.kpPropertiesSet(oldCustomerKp, updateKp);
            wmCustomerSensitiveWordsService.writeKpWhenInsertOrUpdate(oldCustomerKp);
            oldCustomerKp.setState(KpLegalStateMachine.RECORDED.getState());
            oldCustomerKp.setVersion(KpVersionEnum.V3.getCode());
            wmCustomerKpDBMapper.updateByPrimaryKeySelective(oldCustomerKp);
            wmCustomerKpLogService.updateKpLog(oldCustomerKp, diffCellBos, uid, uname);
            return null;
        }


        // KP法人操作状态机流程
        KpLegalStatusMachineContext kpLegalStatusMachineContext = KpLegalStatusMachineContext.builder()
                .customerId(oldCustomerKp.getCustomerId()).opType(KpOperationTypeConstant.UPDATE).wmCustomer(wmCustomer)
                .wmCustomerKp(updateKp).opUid(uid).opUName(uname)
                .existEffectiveFlag(oldCustomerKp.getState() == KpLegalStateMachine.EFFECT.getState())
                .oldCustomerKp(oldCustomerKp).build();
        return kpLegalFlowAbility.updateKpWithSM(kpLegalStatusMachineContext);

    }

    /**
     * 校验KP类型法人是否满足继续认证条件
     * 客户资质是营业执照+客户法人与KP法人姓名一致+KP证件类型是身份证
     *
     * @param wmCustomer
     * @param customerKp
     * @return
     */
    private Boolean checkLegalKp(WmCustomerDB wmCustomer, WmCustomerKp customerKp) {
        //客户资质是营业执照+客户法人与KP法人姓名一致+KP证件类型是身份证
        if ((wmCustomer.getCustomerType() == CustomerType.CUSTOMER_TYPE_BUSINESS.getCode()
                || wmCustomer.getCustomerType() == CustomerType.CUSTOMER_TYPE_BUSINESS_ABROAD.getCode())
                && StringUtils.isNotBlank(wmCustomer.getLegalPerson())
                && wmCustomer.getLegalPerson().equals(customerKp.getCompellation())
                && customerKp.getCertType() == CertTypeEnum.ID_CARD.getType()) {
            return true;
        }
        return false;
    }

    /**
     * 实名认证法人KP
     *
     * @param customerKp
     * @param opUId
     * @param uName
     * @return
     */
    private WmCustomerKp preAuthLegalKp(WmCustomerKp customerKp, Integer opUId, String uName) {
        //实名认证流程
        PreAuthResultBO result = kpDBOperate.preAuthAndErrorMsgNew(customerKp, opUId, uName);
        String preAuthErrorMsg = (result == null ? null : (result.getResult() == PreAuthResultTypeEnum.FAIL.getType() ? result.getMsg() : null));
        //实名成功
        if (StringUtils.isEmpty(preAuthErrorMsg)) {
            customerKp.setState(KpLegalStateMachine.EFFECT.getState());
            customerKp.setEffective(KpConstants.EFFECTIVE);
            customerKp.setVersion(KpVersionEnum.V3.getCode());
            customerKp.setFailReason("");
        } else {
            customerKp.setState(KpLegalStateMachine.PREAUTH_FAIL.getState());
            customerKp.setEffective(KpConstants.UN_EFFECTIVE);
            customerKp.setVersion(KpVersionEnum.V3.getCode());
            customerKp.setFailReason("实名认证失败");
        }
        return customerKp;
    }

    @Override
    public Object delete(WmCustomerDB wmCustomer, List<WmCustomerKp> oldCustomerKpList, List<WmCustomerKp> deleteKpList, int uid, String uname) throws WmCustomerException, TException, WmServerException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_1", "com.sankuai.meituan.waimai.customer.service.kp.operatorv2.dboperatorv2.LegalDBOperator.delete(WmCustomerDB,List,List,int,String)");
        WmCustomerKp deleteKp = getOperateKp(deleteKpList);
        if (null != deleteKp) {
            deleteKp(wmCustomer, deleteKpList.get(0), uid, uname);
        }
        return null;
    }

    /**
     * 删除法人KP并添加操作日志
     *
     * @param customerKp
     * @param opUId
     * @param uName
     */
    public boolean deleteLegalKpAndSendMsg(WmCustomerKp customerKp, Integer opUId, String uName, String content) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_2", "com.sankuai.meituan.waimai.customer.service.kp.operatorv2.dboperatorv2.LegalDBOperator.deleteLegalKpAndSendMsg(WmCustomerKp,Integer,String,String)");
        if (customerKp != null) {
            deleteKpWithContent(customerKp, opUId, uName, content);
            return true;
        }
        return false;
    }

    /**
     * 已录入KP法人数据继续流程-只走KP法人的流程
     *
     * @param legalKp
     * @return
     */
    public WmCustomerKp recordLegalKpAutoFlow(WmCustomerKp legalKp, WmCustomerDB wmCustomer) {
        try {
            LOGGER.info("recordLegalKpAutoFlow,客户生效，开始处理KP法人数据，customerId={},legalKp={}", wmCustomer.getId(), JSON.toJSONString(legalKp));
            if (legalKp == null || legalKp.getState() != KpLegalStateMachine.RECORDED.getState()) {
                LOGGER.info("recordLegalKpAutoFlow,KP法人数据不存在或者非已录入状态,legalKp={}", JSON.toJSONString(legalKp));
                return null;
            }
            // 客户未生效，KP签约人信息暂存
            if (wmCustomer.isUnEffectived()) {
                LOGGER.info("recordLegalKpAutoFlow,客户未生效，KP签约人信息暂存 legalKp:{}", JSON.toJSONString(legalKp));
                return null;
            }
            //不符合规则需要操作删除并记录操作日志
            if (!checkLegalKp(wmCustomer, legalKp)) {
                String content = String.format("您负责的客户（%s%s），法人信息不符合要求，需要重新录入，请及时处理。", wmCustomer.getMtCustomerId(), wmCustomer.getCustomerName());
                //删除法人
                boolean deleteKp = deleteLegalKpAndSendMsg(legalKp, 0, "系统自动删除", content);
                if (deleteKp) {
                    //发送KP删除的消息给客户责任人
                    noticeService.sendDeleteLegalKpMsg(wmCustomer, legalKp, content);
                }
                return null;
            }

            //对法人KP进行实名认证处理
            legalKp = preAuthLegalKp(legalKp, 0, "系统自动");
            // 操作完成后更新db中的数据
            wmCustomerKpDBMapper.updateByPrimaryKeySelective(legalKp);
            wmCustomerKpLogService.addKpStatusChangeLog(legalKp, 0, "系统自动创建", KpLegalStateMachine.RECORDED.getState());
        } catch (Exception e) {
            LOGGER.error("recordLegalKpAutoFlow,客户生效已录入KP法人数据继续流程发生异常,legalKp={}", JSON.toJSONString(legalKp), e);
        }
        return legalKp;
    }
}
