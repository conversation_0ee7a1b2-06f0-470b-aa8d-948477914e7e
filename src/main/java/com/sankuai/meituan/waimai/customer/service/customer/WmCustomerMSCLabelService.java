package com.sankuai.meituan.waimai.customer.service.customer;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.sankuai.meituan.waimai.customer.adapter.WmPoiFlowlineLabelThriftServiceAdapter;
import com.sankuai.meituan.waimai.customer.config.MccCustomerConfig;
import com.sankuai.meituan.waimai.label.constants.LabelSubjectTypeEnum;
import com.sankuai.meituan.waimai.label.thrift.domain.WmLabelRel;
import com.sankuai.meituan.waimai.thrift.domain.WmPoiLabelRel;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class WmCustomerMSCLabelService {

    @Autowired
    private WmPoiFlowlineLabelThriftServiceAdapter wmPoiFlowlineLabelThriftServiceAdapter;

    public boolean checkCustomerHasMSCLabel(Long mtCustomerId) throws WmCustomerException {
        if (mtCustomerId == null || mtCustomerId <= 0L) {
            return false;
        }
        long customerLabelId = MccCustomerConfig.getAuditedMSCCustomerLabel();
        WmPoiLabelRel customerLabel = wmPoiFlowlineLabelThriftServiceAdapter.selectLabelRelCache(mtCustomerId, customerLabelId, LabelSubjectTypeEnum.CUSTOMER.getCode());
        if (customerLabel != null && customerLabel.getId() > 0L) {
            return true;
        }
        return false;
    }

    public void addMSCPoiTag(Set<Long> wmPoiIdSet, Integer opUid, String opName) {
        log.info("addMSCPoiTag wmPoiIdSet={},opUid={},opName={}", JSONObject.toJSONString(wmPoiIdSet), opUid, opName);
        long poiLabelId = MccCustomerConfig.getMSCWmPoiLabel();
        List<Long> waitAddList = Lists.newArrayList();
        for (Long wmPoiId : wmPoiIdSet) {
            WmPoiLabelRel poiLabel = wmPoiFlowlineLabelThriftServiceAdapter.selectLabelRelCache(wmPoiId, poiLabelId, LabelSubjectTypeEnum.POI.getCode());
            log.info("addMSCPoiTag wmPoiIdSet={},poiLabelId={},poiLabel={}", JSONObject.toJSONString(wmPoiIdSet), poiLabelId, JSONObject.toJSONString(poiLabel));
            if (poiLabel == null || (poiLabel != null && poiLabel.getId() <= 0L)) {
                waitAddList.add(wmPoiId);
            }
        }
        if (!CollectionUtils.isEmpty(waitAddList)) {
            wmPoiFlowlineLabelThriftServiceAdapter.batchAddLabelRel(poiLabelId, waitAddList, opUid, opName, LabelSubjectTypeEnum.POI.getCode());
        }
    }

    public void deleteMSCPoiTag(Set<Long> wmPoiIdSet, Integer opUid, String opName){
        log.info("deleteMSCPoiTag wmPoiIdSet={},opUid={},opName={}", JSONObject.toJSONString(wmPoiIdSet), opUid, opName);
        long poiLabelId = MccCustomerConfig.getMSCWmPoiLabel();
        List<Long> waitAddList = Lists.newArrayList();
        for (Long wmPoiId : wmPoiIdSet) {
            WmPoiLabelRel poiLabel = wmPoiFlowlineLabelThriftServiceAdapter.selectLabelRelCache(wmPoiId, poiLabelId, LabelSubjectTypeEnum.POI.getCode());
            log.info("deleteMSCPoiTag wmPoiIdSet={},poiLabelId={},poiLabel={}", JSONObject.toJSONString(wmPoiIdSet), poiLabelId, JSONObject.toJSONString(poiLabel));
            if (poiLabel != null && poiLabel.getId() > 0L) {
                waitAddList.add(wmPoiId);
            }
        }
        if (!CollectionUtils.isEmpty(waitAddList)) {
            wmPoiFlowlineLabelThriftServiceAdapter.batchDeleteLabelToPois(poiLabelId,waitAddList,opUid,opName);
        }
    }


    /**
     * 给门店打"资质共用-商家标"
     *
     * @param wmPoiIdSet
     * @param opUid
     * @param opName
     */
    public void addMscQuaCommonPoiTag(Set<Long> wmPoiIdSet, Integer opUid, String opName) {
        log.info("addMscQuaCommonPoiTag wmPoiIdSet={},opUid={},opName={}", JSONObject.toJSONString(wmPoiIdSet), opUid, opName);
        long poiLabelId = MccCustomerConfig.getQuaCommonPoiLabel();
        List<Long> waitAddList = Lists.newArrayList();
        for (Long wmPoiId : wmPoiIdSet) {
            WmPoiLabelRel poiLabel = wmPoiFlowlineLabelThriftServiceAdapter.selectLabelRelCache(wmPoiId, poiLabelId, LabelSubjectTypeEnum.POI.getCode());
            log.info("addMscQuaCommonPoiTag wmPoiIdSet={},poiLabelId={},poiLabel={}", JSONObject.toJSONString(wmPoiIdSet), poiLabelId, JSONObject.toJSONString(poiLabel));
            if (poiLabel == null || (poiLabel != null && poiLabel.getId() <= 0L)) {
                waitAddList.add(wmPoiId);
            }
        }
        if (!CollectionUtils.isEmpty(waitAddList)) {
            wmPoiFlowlineLabelThriftServiceAdapter.batchAddLabelRel(poiLabelId, waitAddList, opUid, opName, LabelSubjectTypeEnum.POI.getCode());
        }
    }

    /**
     * 删除 "资质共用-商家标"
     *
     * @param wmPoiIdSet
     * @param opUid
     * @param opName
     */
    public void deleteMscQuaCommonPoiTag(Set<Long> wmPoiIdSet, Integer opUid, String opName) {
        log.info("deleteMscQuaCommonPoiTag wmPoiIdSet={},opUid={},opName={}", JSONObject.toJSONString(wmPoiIdSet), opUid, opName);
        long poiLabelId = MccCustomerConfig.getQuaCommonPoiLabel();
        List<Long> waitAddList = Lists.newArrayList();
        for (Long wmPoiId : wmPoiIdSet) {
            WmPoiLabelRel poiLabel = wmPoiFlowlineLabelThriftServiceAdapter.selectLabelRelCache(wmPoiId, poiLabelId, LabelSubjectTypeEnum.POI.getCode());
            log.info("deleteMscQuaCommonPoiTag wmPoiIdSet={},poiLabelId={},poiLabel={}", JSONObject.toJSONString(wmPoiIdSet), poiLabelId, JSONObject.toJSONString(poiLabel));
            if (poiLabel != null && poiLabel.getId() > 0L) {
                waitAddList.add(wmPoiId);
            }
        }
        //查询到有资质共用标签则需要删除
        if (!CollectionUtils.isEmpty(waitAddList)) {
            wmPoiFlowlineLabelThriftServiceAdapter.batchDeleteLabelToPois(poiLabelId, waitAddList, opUid, opName);
        }
    }

    /**
     * 删除 "资质共用-客户标"
     *
     * @param mtCustomerId
     * @param opUid
     * @param opName
     */
    public void deleteMscQuaCommonCustomerTag(Long mtCustomerId, Integer opUid, String opName) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.customer.WmCustomerMSCLabelService.deleteMscQuaCommonCustomerTag(java.lang.Long,java.lang.Integer,java.lang.String)");
        log.info("deleteMscQuaCommonCustomerTag mtCustomerId={},opUid={},opName={}", mtCustomerId, opUid, opName);
        Boolean checkHasQuaCommonCusTag = checkHasQuaComCustomerLabel(mtCustomerId);
        if (!checkHasQuaCommonCusTag) {
            log.info("deleteMscQuaCommonCustomerTag,当前客户无资质共用-客户标签，不需要删除,mtCustomerId={}", mtCustomerId);
            return;
        }
        Integer quaCommonCustomerTagId = MccCustomerConfig.getQuaCommonCustomerLabel();
        wmPoiFlowlineLabelThriftServiceAdapter.delCustomerLabelRel(quaCommonCustomerTagId, mtCustomerId, opUid, opName);
    }


    /**
     * 校验客户是否有资质共用-客户标
     *
     * @param mtCustomerId
     * @return
     * @throws WmCustomerException
     */
    public boolean checkHasQuaComCustomerLabel(Long mtCustomerId) {
        if (mtCustomerId == null || mtCustomerId <= 0L) {
            return false;
        }
        Integer quaCommonCusTagId = MccCustomerConfig.getQuaCommonCustomerLabel();
        List<WmLabelRel> labelRelList = wmPoiFlowlineLabelThriftServiceAdapter.selectLabelRel(mtCustomerId, LabelSubjectTypeEnum.CUSTOMER.getCode(), quaCommonCusTagId);
        if (CollectionUtils.isNotEmpty(labelRelList)) {
            return true;
        }
        return false;
    }

    /**
     * 客户类型变更：美食城客户->非美食城客户 场景使用
     * 客户如果有资质共用特殊-客户标，掉标
     * 客户如果有资质共用特殊-客户标，客户下所有门店掉标
     *
     * @param mtCustomerId
     * @param wmPoiIds
     */
    public void deleteQuaComTagOnCusAndPoi(Long mtCustomerId, List<Long> wmPoiIds) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_1", "com.sankuai.meituan.waimai.customer.service.customer.WmCustomerMSCLabelService.deleteQuaComTagOnCusAndPoi(java.lang.Long,java.util.List)");
        boolean checkQuaComCus = checkHasQuaComCustomerLabel(mtCustomerId);
        //有资质共用特殊场景-客户 标需要掉标
        if (checkQuaComCus) {
            //客户掉标
            deleteMscQuaCommonCustomerTag(mtCustomerId, 0, "系统");
            //商家掉标
            List<List<Long>> wmPoiIdList = Lists.partition(wmPoiIds, 100);
            for (List<Long> partWmPoiIds : wmPoiIdList) {
                deleteMscQuaCommonPoiTag(Sets.newHashSet(partWmPoiIds), 0, "系统");
            }
        }
    }

}
