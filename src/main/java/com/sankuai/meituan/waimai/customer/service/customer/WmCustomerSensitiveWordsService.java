package com.sankuai.meituan.waimai.customer.service.customer;

import com.sankuai.meituan.waimai.customer.constant.KmsKeyNameEnum;
import com.sankuai.meituan.waimai.customer.service.kp.sensitive.encrypt.EncryptResult;
import com.sankuai.meituan.waimai.customer.service.kp.sensitive.encrypt.KeyEncrypt;
import com.sankuai.meituan.waimai.customer.service.kp.sensitive.encrypt.KeyEncryptHandleService;
import com.sankuai.meituan.waimai.customer.service.kp.sensitive.read.KpRead;
import com.sankuai.meituan.waimai.customer.service.kp.sensitive.read.KpReadHandleService;
import com.sankuai.meituan.waimai.customer.service.kp.sensitive.write.IdentifyIdKpEntryWriteHandle;
import com.sankuai.meituan.waimai.customer.service.kp.sensitive.write.KpEntryWrite;
import com.sankuai.meituan.waimai.customer.service.kp.sensitive.write.KpEntryWriteHandleService;
import com.sankuai.meituan.waimai.thrift.customer.constant.CertTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.kp.WmCustomerKp;
import com.sankuai.meituan.waimai.thrift.customer.domain.kp.WmCustomerKpTemp;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 客户敏感词处理
 *
 * <AUTHOR>
 * @date 2021/8/3
 */
@Service
@Slf4j
public class WmCustomerSensitiveWordsService {


    @Autowired
    private KpReadHandleService kpReadHandleService;


    @Autowired
    private KpEntryWriteHandleService kpEntryWriteHandleService;

    @Autowired
    private KeyEncryptHandleService keyEncryptHandleService;

    @Autowired
    private IdentifyIdKpEntryWriteHandle identifyIdKpEntryWriteHandle;


    /**
     * 客户KP是否写原字段 原字段写开关控制
     *
     * @param kp  客户KP对象
     * @param key 原字段标记
     */
    private void writeKpSourceWhenUpdate(WmCustomerKp kp, KmsKeyNameEnum key) {
        KpEntryWrite kpEntryWrite = new KpEntryWrite();
        kpEntryWrite.setKeyName(key);
        kpEntryWrite.setKp(kp);
        kpEntryWriteHandleService.writeKpSourceWhenUpdate(kpEntryWrite);
    }

    /**
     * 客户KP是否写原字段 原字段写开关控制
     *
     * @param kp 客户KP对象
     */
    public void writeKpSourceWhenUpdate(WmCustomerKp kp) {
        writeKpSourceWhenUpdate(kp, KmsKeyNameEnum.PHONE_NO);
        writeKpSourceWhenUpdate(kp, KmsKeyNameEnum.BANK_CARD_NO);
        writeKpSourceWhenUpdate(kp, KmsKeyNameEnum.IDENTIFY_ID);
    }


    /**
     * 客户KP是否写原字段 原字段写开关控制
     *
     * @param kp  客户KP对象
     * @param key 原字段标记
     */
    private void writeKpSourceWhenUpdate(WmCustomerKpTemp kp, KmsKeyNameEnum key) {
        KpEntryWrite kpEntryWrite = new KpEntryWrite();
        kpEntryWrite.setKeyName(key);
        kpEntryWrite.setKpTemp(kp);
        kpEntryWriteHandleService.writeKpSourceWhenUpdate(kpEntryWrite);
    }


    /**
     * 客户KP是否写原字段 原字段写开关控制
     *
     * @param kp 客户KP对象
     */
    public void writeKpSourceWhenUpdate(WmCustomerKpTemp kp) {
        writeKpSourceWhenUpdate(kp, KmsKeyNameEnum.PHONE_NO);
        writeKpSourceWhenUpdate(kp, KmsKeyNameEnum.BANK_CARD_NO);
        writeKpSourceWhenUpdate(kp, KmsKeyNameEnum.IDENTIFY_ID);
    }


    /**
     * 客户KP敏感词新老字段写控制
     * 是否写原字段 原字段写开关控制，通过变量控制（变量传入Mapper文件中进行判断）
     * 是否写新字段 新字段写开关控制
     * <p>
     * 通过变量控制原因：有的地方InsertSelective后，后面逻辑还会使用kp对象中的phoneNum字段做一些逻辑，减小改动所以使用变量控制
     *
     * @param kp
     * @param key
     */
    public void writeKpWhenInsertOrUpdate(WmCustomerKp kp, KmsKeyNameEnum key) throws WmCustomerException {
        KpEntryWrite kpEntryWrite = new KpEntryWrite();
        kpEntryWrite.setKeyName(key);
        kpEntryWrite.setKp(kp);
        kpEntryWriteHandleService.doWriteKpWhenInsertOrUpdate(kpEntryWrite);
    }


    /**
     * 客户KP敏感词新老字段写控制
     *
     * @param kp
     * @throws WmCustomerException
     */
    public void writeKpWhenInsertOrUpdate(WmCustomerKp kp) throws WmCustomerException {
        writeKpWhenInsertOrUpdate(kp, KmsKeyNameEnum.PHONE_NO);
        writeKpWhenInsertOrUpdate(kp, KmsKeyNameEnum.BANK_CARD_NO);
        writeKpWhenInsertOrUpdate(kp, KmsKeyNameEnum.IDENTIFY_ID);
    }


    /**
     * 客户KP敏感词新老字段写控制
     * 是否写原字段 原字段写开关控制
     * 是否写新字段 新字段写开关控制
     *
     * @param kp
     * @param key
     */
    public void writeKpWhenInsertOrUpdate(WmCustomerKpTemp kp, KmsKeyNameEnum key) throws WmCustomerException {
        KpEntryWrite kpEntryWrite = new KpEntryWrite();
        kpEntryWrite.setKeyName(key);
        kpEntryWrite.setKpTemp(kp);
        kpEntryWriteHandleService.doWriteKpWhenInsertOrUpdate(kpEntryWrite);
    }


    /**
     * 客户KP敏感词新老字段写控制
     *
     * @param kp
     * @throws WmCustomerException
     */
    public void writeKpWhenInsertOrUpdate(WmCustomerKpTemp kp) throws WmCustomerException {
        writeKpWhenInsertOrUpdate(kp, KmsKeyNameEnum.PHONE_NO);
        writeKpWhenInsertOrUpdate(kp, KmsKeyNameEnum.BANK_CARD_NO);
        writeKpWhenInsertOrUpdate(kp, KmsKeyNameEnum.IDENTIFY_ID);
    }


    /**
     * 客户KP敏感词读处理
     *
     * @param kpList
     */
    private void readKpWhenSelect(List<WmCustomerKp> kpList, KmsKeyNameEnum key) {
        if (CollectionUtils.isEmpty(kpList)) {
            return;
        }
        for (WmCustomerKp kp : kpList) {
            if (kp == null) {
                continue;
            }
            readKpWhenSelect(kp, key);
        }
    }

    /**
     * 客户KP敏感词读处理
     *
     * @param kpList
     */
    public void readKpWhenSelect(List<WmCustomerKp> kpList) {
        readKpWhenSelect(kpList, KmsKeyNameEnum.PHONE_NO);
        readKpWhenSelect(kpList, KmsKeyNameEnum.BANK_CARD_NO);
        readKpWhenSelect(kpList, KmsKeyNameEnum.IDENTIFY_ID);
    }

    /**
     * 客户KP敏感词读处理
     *
     * @param kp
     */
    private void readKpWhenSelect(WmCustomerKp kp, KmsKeyNameEnum key) {
        if (kp == null) {
            return;
        }
        KpRead kpRead = new KpRead();
        kpRead.setKeyName(key);
        kpRead.setKp(kp);
        kpReadHandleService.doReadChoiceEncryptToDecrypt(kpRead);
    }

    /**
     * 客户KP敏感词读处理
     *
     * @param kp
     */
    public void readKpWhenSelect(WmCustomerKp kp) {
        if (kp == null) {
            return;
        }
        readKpWhenSelect(kp, KmsKeyNameEnum.PHONE_NO);
        readKpWhenSelect(kp, KmsKeyNameEnum.BANK_CARD_NO);
        readKpWhenSelect(kp, KmsKeyNameEnum.IDENTIFY_ID);
    }


    /**
     * 客户KP敏感词读处理
     *
     * @param kpList
     */
    private void readKpTempWhenSelect(List<WmCustomerKpTemp> kpList, KmsKeyNameEnum key) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_2", "com.sankuai.meituan.waimai.customer.service.customer.WmCustomerSensitiveWordsService.readKpTempWhenSelect(java.util.List,com.sankuai.meituan.waimai.customer.constant.KmsKeyNameEnum)");
        if (CollectionUtils.isEmpty(kpList)) {
            return;
        }
        for (WmCustomerKpTemp kp : kpList) {
            if (kp == null) {
                continue;
            }
            readKpWhenSelect(kp, key);
        }
    }


    /**
     * 客户KP敏感词读处理
     *
     * @param kpList
     */
    public void readKpTempWhenSelect(List<WmCustomerKpTemp> kpList) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_2", "com.sankuai.meituan.waimai.customer.service.customer.WmCustomerSensitiveWordsService.readKpTempWhenSelect(java.util.List)");
        if (CollectionUtils.isEmpty(kpList)) {
            return;
        }
        readKpTempWhenSelect(kpList, KmsKeyNameEnum.PHONE_NO);
        readKpTempWhenSelect(kpList, KmsKeyNameEnum.BANK_CARD_NO);
        readKpTempWhenSelect(kpList, KmsKeyNameEnum.IDENTIFY_ID);

    }

    /**
     * 客户KP敏感词读处理
     *
     * @param kp
     */
    public void readKpWhenSelect(WmCustomerKpTemp kp, KmsKeyNameEnum key) {
        if (kp == null) {
            return;
        }
        KpRead kpRead = new KpRead();
        kpRead.setKeyName(key);
        kpRead.setKpTemp(kp);
        kpReadHandleService.doReadChoiceEncryptToDecrypt(kpRead);
    }


    /**
     * 客户KP敏感词读处理
     *
     * @param kp
     */
    public void readKpWhenSelect(WmCustomerKpTemp kp) {
        if (kp == null) {
            return;
        }
        readKpWhenSelect(kp, KmsKeyNameEnum.PHONE_NO);
        readKpWhenSelect(kp, KmsKeyNameEnum.BANK_CARD_NO);
        readKpWhenSelect(kp, KmsKeyNameEnum.IDENTIFY_ID);
    }


    /**
     * 读新字段，新字段解密返回(还原字段使用)
     *
     * @param targetEncryption 新字段值
     * @param key              字段Key
     * @return
     */
    public String getReadEncryption(String targetEncryption, KmsKeyNameEnum key) {
        log.info("getReadEncryption::targetEncryption = {}", targetEncryption);
        KpRead kpRead = new KpRead();
        kpRead.setKeyName(key);
        kpRead.setEncryptionValue(targetEncryption);
        return kpReadHandleService.doReadEncryptToDecrypt(kpRead);
    }

    /**
     * 明文加密
     *
     * @param source 明文源值
     * @param key    字段Key
     * @return
     */
    public String encryption(String source, KmsKeyNameEnum key, int certType) {
        log.info("encryption::source = {}, key = {}, certType = {}", source, key.getName(), certType);
        try {
            if (StringUtils.isBlank(source) || key == null) {
                return null;
            }
            KeyEncrypt keyEncrypt = new KeyEncrypt();
            keyEncrypt.setKeyName(key);
            keyEncrypt.setValueForEncrypt(source);
            keyEncrypt.setCertType(identifyIdKpEntryWriteHandle.getCertificateType(CertTypeEnum.getByType(certType), source));
            EncryptResult result = keyEncryptHandleService.execute(keyEncrypt);
            if (result != null && StringUtils.isNotBlank(result.getEncryption())) {
                return result.getToken();
            }
        } catch (WmCustomerException e) {
            log.info("encryption::source = {}, key = {}, certType = {},msg={}", source, key.getName(), certType, e.getMsg(), e);
        } catch (Exception e) {
            log.error("encryption::source = {}, key = {},certType = {}", source, key.getName(), certType, e);
        }
        return null;
    }


    public void writeKpWhenInsertOrUpdateNoEx(WmCustomerKp kp, KmsKeyNameEnum key) {
        try {
            writeKpWhenInsertOrUpdate(kp, key);
        } catch (WmCustomerException e) {
            log.warn("writeKpWhenInsertOrUpdate::kpId = {}，msg{}", kp.getId(), e.getMsg(), e);
        } catch (Exception e) {
            log.error("writeKpWhenInsertOrUpdate::kpId = {}", kp.getId(), e);
        }
    }

    /**
     * 客户KP敏感词新老字段写控制
     *
     * @param kp
     * @throws WmCustomerException
     */
    public void writeKpWhenInsertOrUpdateNoEx(WmCustomerKp kp) {
        writeKpSourceWhenUpdate(kp);
        if (StringUtils.isNotBlank(kp.getPhoneNum())) {
            writeKpWhenInsertOrUpdateNoEx(kp, KmsKeyNameEnum.PHONE_NO);
        }
        if (StringUtils.isNotBlank(kp.getCreditCard())) {
            writeKpWhenInsertOrUpdateNoEx(kp, KmsKeyNameEnum.BANK_CARD_NO);
        }
        if (StringUtils.isNotBlank(kp.getCertNumber())) {
            writeKpWhenInsertOrUpdateNoEx(kp, KmsKeyNameEnum.IDENTIFY_ID);
        }
    }


    private void writeKpWhenInsertOrUpdateNoEx(WmCustomerKpTemp kp, KmsKeyNameEnum key) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.service.customer.WmCustomerSensitiveWordsService.writeKpWhenInsertOrUpdateNoEx(WmCustomerKpTemp,KmsKeyNameEnum)");
        try {
            writeKpWhenInsertOrUpdate(kp, key);
        } catch (WmCustomerException e) {
            log.warn("writeKpWhenInsertOrUpdate::kpId = {}，msg{}", kp.getId(), e.getMsg(), e);
        } catch (Exception e) {
            log.error("writeKpWhenInsertOrUpdate::kpId = {}", kp.getId(), e);
        }
    }

    /**
     * 客户KP敏感词新老字段写控制
     *
     * @param kp
     * @throws WmCustomerException
     */
    public void writeKpWhenInsertOrUpdateNoEx(WmCustomerKpTemp kp) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.service.customer.WmCustomerSensitiveWordsService.writeKpWhenInsertOrUpdateNoEx(com.sankuai.meituan.waimai.thrift.customer.domain.kp.WmCustomerKpTemp)");
        writeKpWhenInsertOrUpdateNoEx(kp, KmsKeyNameEnum.PHONE_NO);
        writeKpWhenInsertOrUpdateNoEx(kp, KmsKeyNameEnum.BANK_CARD_NO);
        writeKpWhenInsertOrUpdateNoEx(kp, KmsKeyNameEnum.IDENTIFY_ID);
    }
}
