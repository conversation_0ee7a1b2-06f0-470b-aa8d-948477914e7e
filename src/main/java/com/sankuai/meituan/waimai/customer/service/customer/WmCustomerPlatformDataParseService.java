package com.sankuai.meituan.waimai.customer.service.customer;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.MoreObjects;
import com.google.common.collect.Lists;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.meituan.mtrace.thread.pool.TraceExecutors;
import com.sankuai.meituan.waimai.customer.config.MccCustomerConfig;
import com.sankuai.meituan.waimai.customer.config.MccScConfig;
import com.sankuai.meituan.waimai.customer.dao.WmCustomerDBMapper;
import com.sankuai.meituan.waimai.customer.dao.WmCustomerPoiDBMapper;
import com.sankuai.meituan.waimai.customer.ddd.adapter.MtCustomerThriftServiceAdapterImpl;
import com.sankuai.meituan.waimai.customer.domain.*;
import com.sankuai.meituan.waimai.customer.service.customer.poi.CustomerPoiUnBindService;
import com.sankuai.meituan.waimai.customer.util.trans.WmCustomerTransUtil;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerAuditStatus;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerConstants;
import com.sankuai.meituan.waimai.thrift.exception.MtCustomerException;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;

import java.io.IOException;
import java.lang.reflect.Field;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * Author: 窦旭蒙
 * MIS: douxumeng
 * Date: 2020-05-19 14:24
 * Email: <EMAIL>
 * Desc: 单一职责原则：客户系统提供客户平台客户数据服务,客户本身提供的能力迁移至WmCustomerService
 */
@Service
@Slf4j
public class WmCustomerPlatformDataParseService {

    @Autowired
    protected WmCustomerDBMapper wmCustomerDBMapper;

    @Autowired
    protected WmCustomerPoiDBMapper wmCustomerPoiDBMapper;


    @Autowired
    protected MtCustomerThriftServiceAdapterImpl mtCustomerThriftServiceAdapter;

    @Autowired
    private CustomerPoiUnBindService customerPoiUnBindService;

    @Autowired
    private WmCustomerESService wmCustomerESService;

    private static final ThreadFactory threadFactory = new ThreadFactoryBuilder().setNameFormat("customer_change_pool_%d").build();
    private static final ExecutorService executorService = TraceExecutors.getTraceExecutorService(
            new ThreadPoolExecutor(2, 2, 30L, TimeUnit.MINUTES, new ArrayBlockingQueue<Runnable>(10000), threadFactory, new RejectedExecutionHandler() {
                @Override
                public void rejectedExecution(Runnable r, ThreadPoolExecutor executor) {
                    if (!executorService.isShutdown()) {
                        try {
                            executor.getQueue().put(r);
                        } catch (InterruptedException e) {
                            throw new RejectedExecutionException("Reject from " + executor.toString());
                        }
                    } else {
                        throw new RejectedExecutionException("Executor " + executor.toString() + " have shutdown.");
                    }
                }
            }));

    /**
     * selectPlateCustomerByIds
     *根据客户id查询
     * @param customerIdSet
     * @return
     */
    public List<WmCustomerDB> selectCustomerFromPlatformByIds(Set<Integer> customerIdSet) throws WmCustomerException {
        log.info("selectCustomerFromPlatformByIds迁移平台入口");
        if (CollectionUtils.isEmpty(customerIdSet)) {
            return Lists.newArrayList();
        }
        List<WmCustomerDB> customerList = wmCustomerDBMapper.selectCustomerByIds(customerIdSet);
        if (CollectionUtils.isEmpty(customerList)) {
            log.info("客户{}查询为空", customerIdSet.toString());
            return customerList;
        }
        coverCustomerByPlatformCustomerBatch(customerList, "selectCustomerFromPlatformByIds适配请求平台异常");
        return customerList;
    }

    /**
     * selectCustomerBySuperCustomerId dao层适配
     *
     * @param superCustomerId
     * @return
     */
    public List<WmCustomerDB> selectCustomerFromPlatformBySuperCustomerId(Integer superCustomerId) throws WmCustomerException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.service.customer.WmCustomerPlatformDataParseService.selectCustomerFromPlatformBySuperCustomerId(java.lang.Integer)");
        log.info("selectCustomerFromPlatformBySuperCustomerId迁移平台入口");
        List<WmCustomerDB> superCustomers = wmCustomerDBMapper.selectCustomerBySuperCustomerId(superCustomerId);
        if (CollectionUtils.isEmpty(superCustomers)) {
            log.info("客户{}无下级用户", superCustomerId);
            return superCustomers;
        }
        coverCustomerByPlatformCustomerBatch(superCustomers, "selectCustomerFromPlatformBySuperCustomerId适配请求平台异常");
        return superCustomers;
    }

    /**
     * selectCustomerById dao层适配
     *
     * @param wmCustomerId
     * @return
     */
    public WmCustomerDB selectCustomerFromPlatformById(Integer wmCustomerId) throws WmCustomerException {
        WmCustomerDB wmCustomerDB = wmCustomerDBMapper.selectCustomerById(wmCustomerId);
        if (null == wmCustomerDB) {
            return null;
        }
        coverCustomerByPlatformCustomer(wmCustomerDB, "selectCustomerFromPlatformById适配请求平台异常");
        return wmCustomerDB;
    }

    /**
     * 根据从库客户id查询客户平台客户信息
     *
     * @param wmCustomerId
     * @return
     */
    public WmCustomerDB selectCustomerFromPlatformByIdFromSlave(Integer wmCustomerId) throws WmCustomerException {
        WmCustomerDB wmCustomerDB = wmCustomerDBMapper.selectCustomerByIdFromSlave(wmCustomerId);
        if (null == wmCustomerDB) {
            return null;
        }
        coverCustomerByPlatformCustomer(wmCustomerDB, "selectCustomerFromPlatformByIdFromSlave适配请求平台异常");
        return wmCustomerDB;
    }

    /**
     * selectCustomerByIdRT dao层适配
     *
     * @param wmCustomerId
     * @return
     */
    public WmCustomerDB selectCustomerFromPlatformByIdRT(Integer wmCustomerId) throws WmCustomerException {
//        log.info("selectCustomerByIdRT迁移平台入口");
        WmCustomerDB wmCustomerDB = wmCustomerDBMapper.selectCustomerByIdRT(wmCustomerId);
        if (null == wmCustomerDB) {
            return wmCustomerDB;
        }
        coverCustomerByPlatformCustomer(wmCustomerDB, "selectCustomerFromPlatformByIdRT适配请求平台异常");
        return wmCustomerDB;
    }

    /**
     * selectCustomerListByIdOrMtCustomerId dao层适配
     *
     * @param idSet
     * @return
     */
    public List<WmCustomerDB> selectCustomerFromPlatformByIdOrMtCustomerId(Set<Long> idSet) throws WmCustomerException {
        List<WmCustomerDB> wmCustomerDBList = wmCustomerDBMapper.selectCustomerListByIdOrMtCustomerId(idSet);

        if (CollectionUtils.isEmpty(wmCustomerDBList)) {
            return wmCustomerDBList;
        }
        coverCustomerByPlatformCustomerBatch(wmCustomerDBList, "selectCustomerFromPlatformByIdOrMtCustomerId适配请求平台异常");
        return wmCustomerDBList;
    }

    /**
     * selectCustomerListByIdOrMtCustomerId dao层适配
     *
     * @param customerIdSet
     * @return
     */
    public List<WmCustomerDB> selectCustomerFromPlatformByMtCustomerId(Set<Long> customerIdSet) throws WmCustomerException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.customer.WmCustomerPlatformDataParseService.selectCustomerFromPlatformByMtCustomerId(java.util.Set)");
//        log.info("selectCustomerListByIdOrMtCustomerId迁移平台入口");
        WmCustomerSelectConditionFormDB condition = new WmCustomerSelectConditionFormDB();
        condition.setMtCustomerIdList(new ArrayList<>(customerIdSet));
        //合同编号校验合同编号唯一性
        List<WmCustomerDB> wmCustomerDBList = wmCustomerDBMapper.selectByCondition(condition);
        if (CollectionUtils.isEmpty(wmCustomerDBList)) {
            return wmCustomerDBList;
        }
        coverCustomerByPlatformCustomerBatch(wmCustomerDBList, "selectCustomerFromPlatformByMtCustomerId适配请求平台异常");
        return wmCustomerDBList;
    }

    /**
     * selectCustomerByWmPoiId dao层适配
     *
     * @param wmPoiId
     * @return
     */
    public WmCustomerDB selectCustomerFromPlatformByWmPoiId(Long wmPoiId) throws WmCustomerException {
//        log.info("selectCustomerByWmPoiId迁移平台入口");
        WmCustomerDB wmCustomerDB = wmCustomerDBMapper.selectCustomerByWmPoiId(wmPoiId);
        if (null == wmCustomerDB) {
            return wmCustomerDB;
        }
        coverCustomerByPlatformCustomer(wmCustomerDB, "selectCustomerFromPlatformByWmPoiId适配请求平台异常");
        return wmCustomerDB;
    }

    public WmCustomerDB selectCustomerFromPlatformByWmPoiIdByRT(Long wmPoiId) throws WmCustomerException {

        List<WmCustomerPoiDB> wmCustomerPoiDBList = wmCustomerPoiDBMapper.selectCustomerPoiRelByWmPoiIdRT(wmPoiId);
        // 门店绑定多个客户，校验不通过
        if (!org.springframework.util.CollectionUtils.isEmpty(wmCustomerPoiDBList) && wmCustomerPoiDBList.size() > 1) {
            return null;
        }
        if (org.springframework.util.CollectionUtils.isEmpty(wmCustomerPoiDBList)) {
            return null;
        }
        WmCustomerDB wmCustomerDB = wmCustomerDBMapper.selectCustomerByIdRT(wmCustomerPoiDBList.get(0).getCustomerId());
        if (null == wmCustomerDB) {
            return null;
        }
        coverCustomerByPlatformCustomer(wmCustomerDB, "selectCustomerFromPlatformByWmPoiIdByRT适配请求平台异常");
        return wmCustomerDB;
    }

    /**
     * checkCustomerEffect dao层适配
     *
     * @param wmCustomerId
     * @return
     */
    public Integer checkCustomerEffect(Integer wmCustomerId) throws WmCustomerException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.service.customer.WmCustomerPlatformDataParseService.checkCustomerEffect(java.lang.Integer)");
//        log.info("checkCustomerEffect迁移平台入口");
        WmCustomerDB wmCustomerDB = wmCustomerDBMapper.selectCustomerById(wmCustomerId);
        if (null == wmCustomerDB) {
            return null;
        }
        coverCustomerByPlatformCustomer(wmCustomerDB, "checkCustomerEffect适配请求平台异常");
        return wmCustomerDB.getEffective();
    }

    /**
     * selectCustomerListByKeyword dao层适配
     *
     * @param keyword
     * @param searchType
     * @param isLeaf
     * @return
     * @throws WmCustomerException
     */
    public List<WmCustomerDB> selectCustomerFromPlatformByKeyword(String keyword, Integer searchType, Integer isLeaf) throws WmCustomerException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.service.customer.WmCustomerPlatformDataParseService.selectCustomerFromPlatformByKeyword(java.lang.String,java.lang.Integer,java.lang.Integer)");
//        log.info("selectCustomerListByKeyword迁移平台入口");
        List<WmCustomerDB> wmCustomerDBList = wmCustomerDBMapper.selectCustomerListByKeyword(keyword, searchType, isLeaf);
        if (CollectionUtils.isEmpty(wmCustomerDBList)) {
            return wmCustomerDBList;
        }
        coverCustomerByPlatformCustomerBatch(wmCustomerDBList, "selectCustomerFromPlatformByKeyword适配请求平台异常");
        return wmCustomerDBList;
    }

    /**
     * 删除leaf客户
     *
     * @param wmCustomerId
     * @param opUid
     * @param opName
     * @throws WmCustomerException
     * @throws TException
     */
    public void deleteCustomerForLeaf(Integer wmCustomerId, Integer opUid, String opName) throws WmCustomerException, TException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_2", "com.sankuai.meituan.waimai.customer.service.customer.WmCustomerPlatformDataParseService.deleteCustomerForLeaf(java.lang.Integer,java.lang.Integer,java.lang.String)");
        // 数据读取
        WmCustomerDB wmCustomerDB = selectCustomerFromPlatformById(wmCustomerId);
        if (null == wmCustomerDB) {
            log.info("外卖客户id:{}无对应客户", wmCustomerId);
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "无目标客户");
        }
        customerPoiUnBindService.customerDeleteUnBind(wmCustomerId, opUid, opName);
        wmCustomerDBMapper.deleteCustomer(wmCustomerId);
    }

    /**
     * 删除super客户
     *
     * @param wmCustomerId
     * @throws WmCustomerException
     * @throws TException
     */
    public void deleteCustomerForSuper(Integer wmCustomerId) throws WmCustomerException, TException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.customer.WmCustomerPlatformDataParseService.deleteCustomerForSuper(java.lang.Integer)");
        // 数据读取
        WmCustomerDB wmCustomerDB = selectCustomerFromPlatformById(wmCustomerId);
        if (null == wmCustomerDB) {
            log.info("外卖客户id:{}无对应客户", wmCustomerId);
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "无目标客户");
        }
        List<WmCustomerDB> customerList = selectCustomerFromPlatformBySuperCustomerId(wmCustomerId);
        if (CollectionUtils.isNotEmpty(customerList)) {
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "该客户下关联了下级客户,无法直接删除");
        }
        if (MccCustomerConfig.deleteCustomerSynPlatform()) {
            //同步删除客户平台中客户信息(保证原子性)
            boolean platformDeleteResult = mtCustomerThriftServiceAdapter.updateCustomerValid(wmCustomerDB.getMtCustomerId(), CustomerConstants.UNVALID);
            if (platformDeleteResult) {
                //删除平台成功，删本地
                try {
                    wmCustomerDBMapper.deleteCustomer(wmCustomerId);
                } catch (Exception e) {
                    log.error("调用本地逻辑删除用户信息异常,开始回滚平台，外卖顾客id:{}", wmCustomerId, e);
                    boolean platformDeleteRollbackResult = mtCustomerThriftServiceAdapter.updateCustomerValid(wmCustomerDB.getMtCustomerId(), wmCustomerDB.getValid());
                    if (!platformDeleteRollbackResult) {
                        log.error("逻辑删除客户回滚平台失败，请手动进行数据一致比对，外卖顾客id:{}", wmCustomerId);
                    }
                }
            } else {
                log.error("调用客户平台删除用户执行失败,外卖顾客id:{}", wmCustomerId);
                throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "客户平台删除客户信息发生异常");
            }
        } else {
            wmCustomerDBMapper.deleteCustomer(wmCustomerId);
        }
    }

    /**
     * getCustomerByIdOrMtCustomerId适配
     *
     * @param customerId
     * @return
     * @throws WmCustomerException
     */
    public WmCustomerDB getCustomerByIdOrMtCustomerId(long customerId) throws WmCustomerException {
//        log.info("getCustomerByIdOrMtCustomerId迁移平台入口");
        WmCustomerDB wmCustomerDB = wmCustomerDBMapper.selectCustomerByIdOrMtCustomerId(customerId);
        if (null == wmCustomerDB) {
            return null;
        }
        coverCustomerByPlatformCustomer(wmCustomerDB, "getCustomerByIdOrMtCustomerId适配请求平台异常");
        return wmCustomerDB;
    }

    /**
     * getCustomerByIdOrMtCustomerId适配
     *
     * @param customerId
     * @return
     * @throws WmCustomerException
     */
    public WmCustomerDB getCustomerByIdOrMtCustomerIdRT(long customerId) throws WmCustomerException {
        WmCustomerDB wmCustomerDB = wmCustomerDBMapper.selectCustomerByIdOrMtCustomerIdRT(customerId);
        if (null == wmCustomerDB) {
            return null;
        }
        coverCustomerByPlatformCustomer(wmCustomerDB, "getCustomerByIdOrMtCustomerIdRT适配请求平台异常");
        return wmCustomerDB;
    }

    /**
     * selectCustomerOwnUidList适配
     *
     * @param customerIdList
     * @return
     * @throws WmCustomerException
     */
    public List<WmCustomerDB> getCustomerOwnUidList(List<Integer> customerIdList) throws WmCustomerException {
//        log.info("selectCustomerOwnUidList迁移平台入口");
        List<WmCustomerDB> wmCustomerList = wmCustomerDBMapper.selectCustomerOwnUidList(customerIdList);
        // 客户平台迁移适配
        if (CollectionUtils.isEmpty(wmCustomerList)) {
            return wmCustomerList;
        }
        coverCustomerByPlatformCustomerBatch(wmCustomerList, "getCustomerOwnUidList适配请求平台异常");
        return wmCustomerList;
    }

    /**
     * getCustomerByCustomerNumberAndType适配
     * 1.通过资质编号查询客户平台
     * 2.通过客户平台ID查询外卖客户ID
     * 3.外卖客户ID查客户详情
     *
     * @param customerNumber
     * @param customerType
     * @return
     * @throws WmCustomerException
     */
    public List<WmCustomerDB> getCustomerByCustomerNumberAndType(String customerNumber, Integer customerType, boolean throwException) throws
            WmCustomerException {
        List<WmCustomerDB> wmCustomerListFromPlat = Lists.newArrayList();
        if (throwException) {
            wmCustomerListFromPlat = mtCustomerThriftServiceAdapter.getCustomerByNumberAndTypeThrowException(customerNumber, customerType);
        } else {
            wmCustomerListFromPlat = mtCustomerThriftServiceAdapter.getCustomerByNumberAndType(customerNumber, customerType);
        }
        if (CollectionUtils.isEmpty(wmCustomerListFromPlat)) {
            return wmCustomerListFromPlat;
        }
        List<Long> mtCustomerIds = wmCustomerListFromPlat.stream().map(x -> x.getMtCustomerId()).collect(Collectors.toList());
        return selectPlateCustomerByMtCustomerIds(mtCustomerIds);
    }

    /**
     * 根据资质编号查询客户信息
     * 
     * @param customerNumber
     * @return
     * @throws WmCustomerException
     */
    public List<WmCustomerDB> getCustomerByCustomerNumber(String customerNumber) throws WmCustomerException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.service.customer.WmCustomerPlatformDataParseService.getCustomerByCustomerNumber(java.lang.String)");
        List<WmCustomerDB> wmCustomerListFromPlat = mtCustomerThriftServiceAdapter.getCustomerByCustomerNumber(customerNumber);
        if (CollectionUtils.isEmpty(wmCustomerListFromPlat)) {
            return wmCustomerListFromPlat;
        }
        List<Long> mtCustomerIds = wmCustomerListFromPlat.stream().map(x -> x.getMtCustomerId())
                .collect(Collectors.toList());
        return selectPlateCustomerByMtCustomerIds(mtCustomerIds);
    }

    /**
     * selectCustomerDBList
     *
     * @param customerIdList
     * @return
     * @throws WmCustomerException
     */
    public List<WmCustomerDB> getCustomerDBList(List<Integer> customerIdList) throws WmCustomerException {
//        log.info("selectCustomerDBList迁移平台入口");
        List<WmCustomerDB> wmCustomerList = wmCustomerDBMapper.selectCustomerDBList(customerIdList);
        // 客户平台迁移适配
        if (CollectionUtils.isEmpty(wmCustomerList)) {
            return wmCustomerList;
        }
        coverCustomerByPlatformCustomerBatch(wmCustomerList, "getCustomerDBList适配请求平台异常");
        return wmCustomerList;
    }

    /**
     * getSuperCustomerNameList适配
     *
     * @param customerIds
     * @return
     * @throws WmCustomerException
     */
    public List<WmCustomerListDB> getSuperCustomerNameList(List<Integer> customerIds) throws WmCustomerException {
        // 初始化返回结果
        List<WmCustomerListDB> result = new ArrayList<>();
        // 平台数据
        if (CollectionUtils.isNotEmpty(customerIds)) {
            Set<Integer> greyCustomerSet = new HashSet<>(customerIds);
            List<WmCustomerDB> customers = wmCustomerDBMapper.selectCustomerByIds(greyCustomerSet);
            for (WmCustomerDB db : customers) {
                WmCustomerDB superCutomer = coverByPlateForSuperCustomer(db, "getSuperCustomerNameList适配平台异常");
                if (null == superCutomer) {
                    throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "获取上级客户异常");
                }
                WmCustomerListDB wmCustomerListDB = new WmCustomerListDB();
                // 客户id
                wmCustomerListDB.setId(db.getId());
                // 上级客户name
                wmCustomerListDB.setSuperCustomerName(superCutomer.getCustomerName());
                result.add(wmCustomerListDB);
            }
        }
        return result;
    }

    public Pair<Integer, String> getCustomerNameFromMtCustomer(Long mtCustomerId) throws WmCustomerException {
        try {
            WmCustomerDB wmCustomerDB = mtCustomerThriftServiceAdapter.getCustomerByMtCustomerId(mtCustomerId);
            if (null == wmCustomerDB) {
                return null;
            }
            // 获取客户名称同步至ES
            Integer wmCustomerId = wmCustomerDBMapper.selectWmCustomerIdByMtCustomerIdMaster(mtCustomerId);
            if (wmCustomerId == null || wmCustomerId == 0) {
                return null;
            }

            log.info("updateMsgToEs wmCustomerId:{}", wmCustomerId);
            String customerName = wmCustomerDB.getCustomerName();
            if (StringUtils.isBlank(customerName)) {
                log.warn("反查平台客户名称为空，不进行ES更新，mtCustomerId:{}", mtCustomerId);
                return null;
            }
            log.info("updateMsgToEs customerName:{}", customerName);
            return Pair.of(wmCustomerId, customerName);
        } catch (Exception e) {
            log.error("同步客户名称至ES查询平台信息异常，mtCustomerId:{}", mtCustomerId);
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "同步客户名称至ES查询平台信息异常");
        }
    }

    public void unBindSuperCustomer(Integer customerId) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.service.customer.WmCustomerPlatformDataParseService.unBindSuperCustomer(java.lang.Integer)");
        wmCustomerDBMapper.unBindSuperCustomer(customerId);
    }

    public Integer selectCustomerById4Grey(Integer id) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.service.customer.WmCustomerPlatformDataParseService.selectCustomerById4Grey(java.lang.Integer)");
        return wmCustomerDBMapper.selectOnwerUidByWmCustomerId(id);
    }

    public List<Integer> selectCustomerIdsByOwnerUid(Integer ownerUid) {
        return wmCustomerDBMapper.selectCustomerIdsByOwnerUid(ownerUid);
    }

    public void distributeCustomer(List<Integer> customerIdList, int userId) {
        wmCustomerDBMapper.distributeCustomer(customerIdList, userId);
    }

    public void updateSignMode(Integer cusId, Integer signMode) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.service.customer.WmCustomerPlatformDataParseService.updateSignMode(java.lang.Integer,java.lang.Integer)");
        wmCustomerDBMapper.updateSignMode(cusId, signMode);
    }

    public void updateCustomerRealType(Integer cusId, Integer customerRealType) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.customer.WmCustomerPlatformDataParseService.updateCustomerRealType(java.lang.Integer,java.lang.Integer)");
        wmCustomerDBMapper.updateCustomerRealType(cusId, customerRealType);
    }

    public List<WmCustomerListDB> countsubCustomerList(List<Integer> customerIds) throws WmCustomerException {
        return wmCustomerDBMapper.countsubCustomerList(customerIds);
    }


    private void coverCustomerByPlatformCustomer(WmCustomerDB customer, String errorLog) throws WmCustomerException {
        if (null == customer) {
            return;
        }
        try {
            mtCustomerThriftServiceAdapter.getCustomerByMtCustomerId(customer);
        } catch (Exception e) {
            log.error(errorLog, e);
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, errorLog+"：客户平台获取客户信息异常");
        }
    }

    private void coverCustomerByPlatformCustomerV2(WmCustomerDB customer, String errorLog) throws WmCustomerException {
        if (null == customer) {
            return;
        }
        try {
            mtCustomerThriftServiceAdapter.getCustomerByMtCustomerIdV2(customer);
        } catch (Exception e) {
            log.error(errorLog, e);
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, errorLog+"：客户平台获取客户信息异常");
        }
    }

    private WmCustomerDB coverByPlateForSuperCustomer(WmCustomerDB customer, String errorLog) throws WmCustomerException {
        if (null == customer) {
            return null;
        }
        WmCustomerDB superCustomer = new WmCustomerDB();
        if (customer.getSuperCustomerId() == 0) {
            return superCustomer;
        }
        List<WmCustomerDB> superCustomerList = wmCustomerDBMapper.selectCustomerBySuperId(customer.getSuperCustomerId());
        if (CollectionUtils.isNotEmpty(superCustomerList)) {
            superCustomer = superCustomerList.get(0);
        } else {
            log.error("wmCustomerDBMapper selectCustomerBySuperId查询结果为空");
            return null;
        }
        try {
            mtCustomerThriftServiceAdapter.getCustomerByMtCustomerId(superCustomer);
        } catch (Exception e) {
            log.error(errorLog, e.getMessage());
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "客户平台获取客户信息异常");
        }
        return superCustomer;
    }


    private void coverCustomerByPlatformCustomerBatch(List<WmCustomerDB> customerList, String errorLog) throws WmCustomerException {
        try {
            // 客户平台迁移适配
            List<Long> greyCustomer = customerList.stream().map(x -> x.getMtCustomerId()).collect(Collectors.toList());
            // 用平台数据覆盖
            mtCustomerThriftServiceAdapter.getCustomersByMtCustomerIds(greyCustomer, customerList);
        } catch (TException e) {
            log.warn(errorLog, e.getMessage());
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, errorLog+"：客户平台获取客户信息异常（批量）");
        }
    }

    public List<WmContractorExportDB> selectContractorAll() throws WmCustomerException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_2", "com.sankuai.meituan.waimai.customer.service.customer.WmCustomerPlatformDataParseService.selectContractorAll()");
        log.info("#selectContractorAll");
        // 从es里获取contractIds
        String contractIds = exportContractIdsV2();
        List<Integer> ids = Lists.newArrayList();
        if (StringUtils.isNotEmpty(contractIds)) {
            String[] strings = contractIds.split("\n");
            ArrayList<String> arrayList = new ArrayList<String>(strings.length);
            Collections.addAll(arrayList, strings);
            for (String idString : arrayList) {
                ids.add(Integer.parseInt(idString.trim()));
            }
        }
        List<WmCustomerDB> customerList = null;
        List<List<Integer>> batchIds = Lists.partition(ids, MccScConfig.getQuerySqlBathSize());
        for (List<Integer> groupids : batchIds) {
            List<WmCustomerDB> tempCustomerList = wmCustomerDBMapper.selectContractorByIds(groupids);
            if (customerList != null) {
                customerList.addAll(tempCustomerList);
            } else {
                customerList = tempCustomerList;
            }
        }

        if (CollectionUtils.isEmpty(customerList)) {
            return Lists.newArrayList();
        }
        coverCustomerByPlatformCustomerBatch(customerList, "selectContractorAll适配请求平台异常");
        List<WmContractorExportDB> result = WmCustomerTransUtil.tansCustomerListToWmContractorExportDBList(customerList);
        log.info("#selectContractorAll,result={}", JSONObject.toJSONString(result));
        return result;
    }

    public String exportContractIdsV2() throws WmCustomerException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.customer.WmCustomerPlatformDataParseService.exportContractIdsV2()");
        List<String> strings = null;
        StringBuilder sb = new StringBuilder();
        WmCustomerEsQueryVo wmCustomerEsQueryVo = new WmCustomerEsQueryVo();
        wmCustomerEsQueryVo.setCustomerRealType(15);
        // 分批效果，每次查询500条数据，直到查询完所在的数据
        int pageNo = 1;
        int pageSize = 500;
        boolean hasMoreData = true;
        while (hasMoreData) {
            wmCustomerEsQueryVo.setPageNo(pageNo);
            wmCustomerEsQueryVo.setPageSize(pageSize);
            try {
                strings = wmCustomerESService.queryCustomerIdList(wmCustomerEsQueryVo);
            } catch (IOException e) {
                log.error("查询es客户id列表异常, wmCustomerEsQueryVo = {}", wmCustomerEsQueryVo, e);
                hasMoreData = false;
                continue;
            }
            for (String string : strings) {
                sb.append(string);
                sb.append("\n");
            }
            // 判断是否还有数据需要查询
            if (strings.size() < pageSize) {
                hasMoreData = false;
            } else {
                pageNo++;
            }
        }

        return sb.toString();
    }

    public boolean existByRealTypeAndCustomerNameAndAuditStatusAndCustomerType(int customerRealType, String customerName, int auditStatus, int customerType) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.service.customer.WmCustomerPlatformDataParseService.existByRealTypeAndCustomerNameAndAuditStatusAndCustomerType(int,java.lang.String,int,int)");
        log.info("#existByRealTypeAndCustomerNameAndAuditStatusAndCustomerType, customerRealType={}, customerName={}, auditStatus={}, customerType={}", customerRealType, customerName, auditStatus, customerType);
        return wmCustomerDBMapper.existByRealTypeAndCustomerNameAndAuditStatusAndCustomerType(customerRealType, customerName, auditStatus, customerType) > 0;
    }

    public WmCustomerDB getWmCustomerByMtCustomerId(long mtCustomerId) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.customer.WmCustomerPlatformDataParseService.getWmCustomerByMtCustomerId(long)");
        return wmCustomerDBMapper.selectCustomerByIdOrMtCustomerId(mtCustomerId);
    }

    public long getWmCustomerIdByMtCustomerId(long mtCustomerId) {
        if (mtCustomerId <= 0) {
            return 0;
        }
        Integer result = wmCustomerDBMapper.selectWmCustomerIdByMtCustomerId(mtCustomerId);
        return MoreObjects.firstNonNull(result, 0).longValue();
    }

    public long getWmCustomerIdByMtCustomerIdRT(long mtCustomerId) {
        if (mtCustomerId <= 0) {
            return 0;
        }
        Integer result = wmCustomerDBMapper.selectWmCustomerIdByMtCustomerIdMaster(mtCustomerId);
        return MoreObjects.firstNonNull(result, 0).longValue();
    }


    public long getMtCustomerIdByWmCustomerId(long wmCustomerId) {
        if (wmCustomerId <= 0) {
            return 0;
        }
        Long result = wmCustomerDBMapper.selectMtCustomerIdByWmCustomerId(wmCustomerId);
        return MoreObjects.firstNonNull(result, 0).longValue();
    }

    public long syncMtCustomerQua(long mtCustomerId) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_1", "com.sankuai.meituan.waimai.customer.service.customer.WmCustomerPlatformDataParseService.syncMtCustomerQua(long)");
        log.info("syncMtCustomerQua mtCustomerId={}", mtCustomerId);
        try {
            WmCustomerDB wmCustomerDB = mtCustomerThriftServiceAdapter.getCustomerByMtCustomerId(mtCustomerId);
            if (wmCustomerDB == null) {
                log.info("syncMtCustomerQua 未找到对应的客户信息", mtCustomerId);
                return -1;
            }
            List<WmCustomerDB> wmCustomerDBS = wmCustomerDBMapper.selectCustomerDBListByMtCustomerId(Lists.newArrayList(mtCustomerId));
            if (CollectionUtils.isEmpty(wmCustomerDBS)) {
                log.info("syncMtCustomerQua 未找到对应的客户信息", mtCustomerId);
                return -1;
            }
            log.info("syncMtCustomerQua wmCustomerDB={}", JSONObject.toJSONString(wmCustomerDB));
            mtCustomerThriftServiceAdapter.updateQualification(wmCustomerDB, true);
            return wmCustomerDB.getMtCustomerId();
        } catch (TException e) {
            log.error("syncMtCustomerQua 失败 mtCustomerId={}", mtCustomerId, e);
        } catch (Exception e) {
            log.error("syncMtCustomerQua 失败 mtCustomerId={}", mtCustomerId, e);
        }
        return -1;

    }
    /**
     * selectCustomerByIds dao层适配
     *
     * @param customerList
     * @return
     */
    public List<WmCustomerDB> selectPlateCustomerByIds(List<WmCustomerDB> customerList) throws WmCustomerException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.service.customer.WmCustomerPlatformDataParseService.selectPlateCustomerByIds(java.util.List)");
        log.info("selectCustomerByIds迁移平台入口");
        if (CollectionUtils.isEmpty(customerList)) {
            return Lists.newArrayList();
        }
        if (CollectionUtils.isEmpty(customerList)) {
            log.info("客户{}查询为空",JSONObject.toJSONString(customerList));
            return customerList;
        }
        coverCustomerByPlatformCustomerBatch(customerList, "selectCustomerByIds适配请求平台异常");
        return customerList;
    }




    /**
     * validateCustomerNumber dao层适配
     *
     * @param wmCustomerNumberRepeatVo
     * @return
     */
    public List<WmCustomerDB> validateCustomerNumber(WmCustomerNumberRepeatVo wmCustomerNumberRepeatVo) throws WmCustomerException {
        List<WmCustomerDB> wmCustomerList = getCustomerByCustomerNumberAndType(wmCustomerNumberRepeatVo.getCustomerNumber(), wmCustomerNumberRepeatVo.getCustomerType(), wmCustomerNumberRepeatVo.getThrowException());
        if (CollectionUtils.isEmpty(wmCustomerList)) {
            return wmCustomerList;
        }

        List<WmCustomerDB> repeatWmCustomerDBs = Lists.newArrayList();
        for (WmCustomerDB wmCustomerDB : wmCustomerList) {
            if (wmCustomerDB.getIsLeaf().intValue() != wmCustomerNumberRepeatVo.getIsLeaf()) {
                continue;
            }
            if (wmCustomerNumberRepeatVo.getId().equals(wmCustomerDB.getId().intValue())) {
                continue;
            }
            if (!validRegistryStateSame(wmCustomerDB.getRegistryState(), wmCustomerNumberRepeatVo.getRegistryState())) {
                continue;
            }
            repeatWmCustomerDBs.add(wmCustomerDB);
        }
        return repeatWmCustomerDBs;
    }

    /**
     * 校验两个注册国家是否一致
     *
     * @param registryStateA
     * @param registryStateB
     * @return
     */
    private boolean validRegistryStateSame(Integer registryStateA, Integer registryStateB) {
        if ((registryStateA == null || registryStateA.intValue() <= 0) && (registryStateB == null || registryStateB.intValue() <= 0)) {
            return true;
        }
        if (registryStateA != null && registryStateB != null && registryStateA.intValue() == registryStateB.intValue()) {
            return true;
        }
        return false;
    }

    /**
     * selectCustomerByMtCustomerIds dao层适配
     *
     * @param mtCustomerIds
     * @return
     */
    public List<WmCustomerDB> selectPlateCustomerByMtCustomerIds(List<Long> mtCustomerIds) throws WmCustomerException {
        if (CollectionUtils.isEmpty(mtCustomerIds)) {
            return Lists.newArrayList();
        }
        List<WmCustomerDB> customerList = wmCustomerDBMapper.selectCustomerByMtCustomerIds(mtCustomerIds);
        if (CollectionUtils.isEmpty(customerList)) {
            log.info("客户{}查询为空", mtCustomerIds.toString());
            return customerList;
        }
        coverCustomerByPlatformCustomerBatch(customerList, "selectCustomerByMtCustomerIds适配请求平台异常");
        return customerList;
    }

    public List<Integer> cleanMtCustomerStatus(Set<Integer> customerIds, int status) throws WmCustomerException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_2", "com.sankuai.meituan.waimai.customer.service.customer.WmCustomerPlatformDataParseService.cleanMtCustomerStatus(java.util.Set,int)");
        List<WmCustomerDB> wmCustomerDBList = selectCustomerFromPlatformByIds(customerIds);
        List<Integer> succWmCustomerId = Lists.newArrayList();
        for (WmCustomerDB wmCustomerDB : wmCustomerDBList) {
            try {
                log.info("cleanMtCustomerStatus customerId={},effective={},auditStatus={}", wmCustomerDB.getId(), wmCustomerDB.getEffective(), wmCustomerDB.getAuditStatus());
                if (wmCustomerDB.getEffective() != CustomerConstants.EFFECT && wmCustomerDB.getEffective() != CustomerConstants.UNEFFECT) {
                    if (wmCustomerDB.getAuditStatus() != null) {
                        if (wmCustomerDB.getAuditStatus().intValue() == CustomerAuditStatus.CUSTOMER_AUDIT_NEW.getCode()) {
                            status = CustomerConstants.UNEFFECT;
                        }
                        if (wmCustomerDB.getAuditStatus().intValue() == CustomerAuditStatus.CUSTOMER_AUDIT_PASS.getCode()) {
                            status = CustomerConstants.EFFECT;
                        }
                    }
                } else if (wmCustomerDB.getEffective() == CustomerConstants.EFFECT && status == CustomerAuditStatus.CUSTOMER_AUDIT_PASS.getCode()) {
                    wmCustomerDBMapper.updateAuditStatus(wmCustomerDB.getId(), CustomerAuditStatus.CUSTOMER_AUDIT_PASS.getCode());
                    status = CustomerConstants.EFFECT;
                }
                mtCustomerThriftServiceAdapter.cleanMtCustomerStatus(wmCustomerDB, status);
            } catch (MtCustomerException e) {
                log.error("cleanMtCustomerStatus 失败 wmCustomerDB={},status={}", JSONObject.toJSONString(wmCustomerDB), status);
            }
            succWmCustomerId.add(wmCustomerDB.getId());
        }
        return succWmCustomerId;

    }

    public WmCustomerDB selectCustomerFromPlatformByIdV2(Integer customerId) throws WmCustomerException {
        WmCustomerDB wmCustomerDB = wmCustomerDBMapper.selectCustomerById(customerId);
        if (null == wmCustomerDB) {
            return null;
        }
        coverCustomerByPlatformCustomerV2(wmCustomerDB, "selectCustomerFromPlatformById适配请求平台异常");
        // 异步调用selectCustomerFromPlatformById接口来比对查询结果是否一致
        compareSelectCustomerFromPlatformById(wmCustomerDB);
        return wmCustomerDB;
    }



    private void compareSelectCustomerFromPlatformById(WmCustomerDB wmCustomerDB) {
        if (wmCustomerDB == null) {
            return;
        }
        // 此处需要先深拷贝一份，因为是异步比对，在此方法异步之后，还有方法对wmCustomerDB进行修改，会导致比对不一致
        WmCustomerDB wmCustomerDBCopy = wmCustomerDB.deepCopy();

        // 使用线程池异步比对查询结果是否一致
        executorService.execute(() -> {
            WmCustomerDB wmCustomerDBFromPlatform = new WmCustomerDB();
            try {
                wmCustomerDBFromPlatform = selectCustomerFromPlatformById(wmCustomerDBCopy.getId());
                if (!compareWmCustomerDB(wmCustomerDBCopy, wmCustomerDBFromPlatform)){
                    log.error("compareSelectCustomerFromPlatformById比对不一致-old:{},new:{}",JSON.toJSONString(wmCustomerDBFromPlatform),JSON.toJSONString(wmCustomerDBCopy));
                }
            }catch (Exception e){
                log.error("compareSelectCustomerFromPlatformById比对异常-new:{},old:{}", JSON.toJSONString(wmCustomerDBCopy),JSON.toJSONString(wmCustomerDBFromPlatform));
            }
        });
    }

    private boolean compareWmCustomerDB(WmCustomerDB wmCustomerDB1, WmCustomerDB wmCustomerDB2) throws WmCustomerException {
        if (wmCustomerDB1 == null && wmCustomerDB2 == null) {
            return true;
        }
        if (wmCustomerDB1 == null || wmCustomerDB2 == null) {
            return false;
        }
        // 比较wmCustomerDB1和wmCustomerDB2的每个字段是否相同
        Class<?> clazz = WmCustomerDB.class;
        Field[] fields = clazz.getDeclaredFields();

        for (Field field : fields) {
            field.setAccessible(true);
            try {
                Object value1 = field.get(wmCustomerDB1);
                Object value2 = field.get(wmCustomerDB2);

                if (!Objects.equals(value1, value2)) {
                    return false;
                }
            } catch (IllegalAccessException e) {
                throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR,"compareWmCustomerDBS异常");
            }
        }
        return true;
    }
}



