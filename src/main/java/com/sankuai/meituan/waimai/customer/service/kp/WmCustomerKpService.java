package com.sankuai.meituan.waimai.customer.service.kp;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.dianping.cat.Cat;
import com.dianping.cat.util.MetricHelper;
import com.google.common.collect.Lists;
import com.sankuai.meituan.util.StringUtil;
import com.sankuai.meituan.waimai.customer.adapter.WmAuditServiceAdaptor;
import com.sankuai.meituan.waimai.customer.adapter.WmPoiClient;
import com.sankuai.meituan.waimai.customer.aspect.RepeatSubmission;
import com.sankuai.meituan.waimai.customer.bo.WmPoiDomain;
import com.sankuai.meituan.waimai.customer.config.MccConfig;
import com.sankuai.meituan.waimai.customer.config.MccCustomerConfig;
import com.sankuai.meituan.waimai.customer.constant.WmCustomerConstant;
import com.sankuai.meituan.waimai.customer.constant.customer.KpCertTypeRelEnum;
import com.sankuai.meituan.waimai.customer.domain.*;
import com.sankuai.meituan.waimai.thrift.customer.constant.BusinessTypeEnum;
import com.sankuai.meituan.waimai.customer.constant.customer.CustomerMetricEnum;
import com.sankuai.meituan.waimai.customer.constant.customer.DeleteSignerSceneEnum;
import com.sankuai.meituan.waimai.customer.constant.customer.MetricConstant;
import com.sankuai.meituan.waimai.customer.contract.service.impl.WmCustomerOplogService;
import com.sankuai.meituan.waimai.customer.dao.*;
import com.sankuai.meituan.waimai.customer.mq.service.MafkaMessageSendManager;
import com.sankuai.meituan.waimai.customer.service.common.SendDaxiangService;
import com.sankuai.meituan.waimai.customer.service.customer.*;
import com.sankuai.meituan.waimai.customer.service.customer.dto.DcCustomerKpQueryDto;
import com.sankuai.meituan.waimai.customer.service.customer.check.blackListCheck.CustomerBlackListValidator;
import com.sankuai.meituan.waimai.customer.service.gray.WmCustomerGrayService;
import com.sankuai.meituan.waimai.customer.service.kp.operatorv2.*;
import com.sankuai.meituan.waimai.customer.service.kp.operatorv2.dboperatorv2.LegalDBOperator;
import com.sankuai.meituan.waimai.customer.service.kp.operatorv2.dboperatorv2.SignerDBOperator;
import com.sankuai.meituan.waimai.customer.service.kp.operatorv2.preverifierv2.OpmanagerRelPoiBindVerify;
import com.sankuai.meituan.waimai.customer.service.kp.operatorv2.preverifierv2.SignerCertTypeVerify;
import com.sankuai.meituan.waimai.customer.service.kp.preverifier.KpPreverifier;
import com.sankuai.meituan.waimai.customer.service.sign.WmEcontractSignBzService;
import com.sankuai.meituan.waimai.customer.service.version.VersionCheckUtil;
import com.sankuai.meituan.waimai.customer.util.ThrowUtil;
import com.sankuai.meituan.waimai.thrift.bo.WmCustomerDiffCellBo;
import com.sankuai.meituan.waimai.thrift.constatnt.WmAuditTaskBizTypeConstant;
import com.sankuai.meituan.waimai.thrift.customer.constant.*;
import com.sankuai.meituan.waimai.thrift.customer.constant.customer.*;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.EffectiveStatusEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.WmCustomerOplogBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.base.BooleanResult;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.*;
import com.sankuai.meituan.waimai.thrift.customer.domain.kp.WmCustomerKp;
import com.sankuai.meituan.waimai.thrift.customer.domain.kp.WmCustomerKpSearchCondition;
import com.sankuai.meituan.waimai.thrift.customer.domain.kp.WmCustomerKpTemp;
import com.sankuai.meituan.waimai.thrift.customer.domain.kp.WmCustomerKpTempSearchCondition;
import com.sankuai.meituan.waimai.thrift.customer.domain.kp.*;
import com.sankuai.meituan.waimai.thrift.customer.domain.mq.CustomerMQBody;
import com.sankuai.meituan.waimai.thrift.customer.domain.mq.CustomerMQEventEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.RetrySmsResponse;
import com.sankuai.meituan.waimai.thrift.domain.WmAuditCommitObj;
import com.sankuai.meituan.waimai.thrift.domain.WmAuditMsg;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import com.sankuai.meituan.waimai.thrift.exception.WmServerException;
import com.sankuai.meituan.waimai.thrift.service.WmAuditApiService;
import com.sankuai.meituan.waimai.thrift.util.DiffUtil;
import com.sankuai.meituan.waimai.thrift.util.ObjectUtil;
import com.sankuai.meituan.waimai.thrift.vo.WmAuditAgentAuthCommitData;
import com.sankuai.meituan.waimai.thrift.vo.WmAuditSpecialAuthCommitData;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.thrift.TException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.sankuai.meituan.waimai.thrift.customer.constant.KpConstants.EFFECTIVE;
import static com.sankuai.meituan.waimai.thrift.customer.constant.KpOperationTypeConstant.DELETE;
import static com.sankuai.meituan.waimai.thrift.customer.constant.KpOperationTypeConstant.INSERT;
import static com.sankuai.meituan.waimai.thrift.customer.constant.KpOperationTypeConstant.UPDATE;
import static com.sankuai.meituan.waimai.thrift.customer.constant.KpSignerTypeEnum.AGENT;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class WmCustomerKpService {

    private static final Logger LOGGER = LoggerFactory.getLogger(WmCustomerKpService.class);

    @Autowired
    private CustomerBlackListValidator customerBlackListValidator;

    @Autowired
    private WmCustomerKpDBMapper wmCustomerKpDBMapper;

    @Autowired
    private List<KpPreverifier> kpPreverifier;

    @Autowired
    private DifferentCustomerKpService differentCustomerKpService;

    @Autowired
    private WmCustomerKpTempDBMapper wmCustomerKpTempDBMapper;

    @Autowired
    private WmCustomerKpLogService wmCustomerKpLogService;

    @Autowired
    private WmEcontractSignBzService wmEcontractSignBzService;

    @Autowired
    private WmCustomerKpAuditMapper wmCustomerKpAuditMapper;

    @Autowired
    private MafkaMessageSendManager mafkaMessageSendManager;

    @Autowired
    protected WmCustomerPlatformDataParseService wmCustomerPlatformDataParseService;

    @Autowired
    private WmAuditApiService.Iface wmAuditApiService;

    @Autowired
    private SignerOperate signerOperate;

    @Autowired
    private LegalOperate legalOperate;

    @Autowired
    private OtherOperate otherOperate;

    @Autowired
    private VisitkpOperate visitkpOperate;

    @Autowired
    private OpmanagerOperate opmanagerOperate;

    @Autowired
    private OpmanagerRelPoiBindVerify opmanagerRelPoiBindVerify;

    @Autowired
    private WmCustomerOplogService wmCustomerOplogService;

    @Autowired
    private WmCustomerSensitiveWordsService wmCustomerSensitiveWordsService;

    @Autowired
    private SignerCertTypeVerify signerCertTypeVerify;

    @Autowired
    private SendDaxiangService sendDaxiangService;

    @Autowired
    private SignerDBOperator signerDBOperator;

    @Autowired
    private UpmAuthCheckService upmAuthCheckService;

    @Autowired
    private WmCustomerGrayService wmCustomerGrayService;

    @Autowired
    private WmCustomerKpPoiService wmCustomerKpPoiService;

    @Autowired
    private WmCustomerKpAuditService wmCustomerKpAuditService;

    @Autowired
    private WmCustomerPoiAttributeService wmCustomerPoiAttributeService;

    @Autowired
    private WmCustomerKpRealAuthService wmCustomerKpRealAuthService;

    @Autowired
    private WmCustomerService wmCustomerService;

    @Autowired
    private BusinessSignerOperate businessSignerOperate;

    @Autowired
    private WmPoiClient wmPoiClient;

    @Autowired
    private WmCustomerKpBuryingPointService wmCustomerKpBuryingPointService;

    @Autowired
    private WmCustomerPoiDBMapper wmCustomerPoiDBMapper;

    @Autowired
    private WmCustomerKpService wmCustomerKpService;

    @Autowired
    private WmAuditServiceAdaptor wmAuditServiceAdaptor;

    @Resource
    private WmCustomerLegalKpService wmCustomerLegalKpService;

    @Autowired
    private LegalDBOperator legalDBOperator;

    @Autowired
    private DcCustomerKpService dcCustomerKpService;

    @Autowired
    private WmCustomerPoiService wmCustomerPoiService;

    /**
     * 证件类型为身份证
     */
    private static final List<Integer> ID_CERT_TYPE = Lists.newArrayList(Integer.valueOf(CertTypeEnum.ID_CARD.getType()),
            Integer.valueOf(CertTypeEnum.ID_CARD_TEMP.getType()),
            Integer.valueOf(CertTypeEnum.ID_CARD_COPY.getType()));

    private static final Comparator<WmCustomerKp> KPTYPE_COMPARABLE = new Comparator<WmCustomerKp>() {
        @Override
        public int compare(WmCustomerKp o1, WmCustomerKp o2) {
            return o1.getKpType() - o2.getKpType();
        }
    };

    @PostConstruct
    public void init() {
        Collections.sort(kpPreverifier, new Comparator<KpPreverifier>() {
            @Override
            public int compare(KpPreverifier o1, KpPreverifier o2) {
                return o1.order() - o2.order();
            }
        });
    }

    /**
     * 获取客户生效的签约人KP，后期可能会改成一个客户有多个签约人，所以返回值用List
     *
     * @param customerId 客户ID
     * @return 返回生效的签约人KP信息，没有则返回null
     */
    public WmCustomerKp getCustomerKpOfEffectiveSigner(int customerId) {
        List<WmCustomerKp> wmCustomerKps = wmCustomerKpDBMapper.selectByCustomerId(customerId, KpTypeEnum.SIGNER.getType());
        if (CollectionUtils.isNotEmpty(wmCustomerKps)) {
            for (WmCustomerKp wmCustomerKp : wmCustomerKps) {
                if (KpSignerStateMachine.EFFECT.getState() == wmCustomerKp.getState()) {
                    wmCustomerSensitiveWordsService.readKpWhenSelect(wmCustomerKp);
                    return wmCustomerKp;
                }
            }
        }
        return null;
    }

    /**
     * 获取客户生效的签约人KP，后期可能会改成一个客户有多个签约人，所以返回值用List
     *
     * @param customerId 客户ID
     * @return 返回生效的签约人KP信息，没有则返回null
     */
    public WmCustomerKp getCustomerKpOfEffectiveSignerRT(int customerId) {
        List<Integer> customerIdList = new ArrayList<>();
        customerIdList.add(customerId);
        List<WmCustomerKp> WmCustomerKpList = getCustomerKp(customerIdList, (int) KpTypeEnum.SIGNER.getType(), true);
        if (CollectionUtils.isNotEmpty(WmCustomerKpList)) {
            for (WmCustomerKp wmCustomerKp : WmCustomerKpList) {
                if (KpSignerStateMachine.EFFECT.getState() == wmCustomerKp.getState()) {
                    wmCustomerSensitiveWordsService.readKpWhenSelect(wmCustomerKp);
                    return wmCustomerKp;
                }
            }
        }
        return null;
    }

    /**
     * 获取客户KP列表
     *
     * @param customerId 客户ID
     * @return 客户KP列表对象
     */
    public List<WmCustomerKp> getCustomerKpList(int customerId) {
        List<WmCustomerKp> kpList = wmCustomerKpDBMapper.selectByCustomerId(customerId, 0);
        wmCustomerSensitiveWordsService.readKpWhenSelect(kpList);
        return kpList;
    }

    public List<WmCustomerKp> getCustomerKpListByCustomerRealType(int customerId) throws WmCustomerException, TException {
        log.info("#getCustomerKpListByCustomerRealType-req:{}",customerId);
        // 查询客户 判断客户类型，如果是仅到餐，单独处理
        WmCustomerDB wmCustomerDB = wmCustomerService.selectCustomerById(customerId);
        if(wmCustomerDB == null){
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR,"客户不存在");
        }
        if (wmCustomerDB.getCustomerRealType() == CustomerRealTypeEnum.DAOCAN.getValue()) {
            QueryCustomerKpDTO dto = new QueryCustomerKpDTO();
            dto.setWmCustomerId(customerId);
            dto.setBusinessTypeEnum(BusinessTypeEnum.DAOCAN);
            return getCustomerSignerEffectiveByBusiness(dto);
        }
        List<WmCustomerKp> kpList = wmCustomerKpDBMapper.selectByCustomerId(customerId, 0);
        wmCustomerSensitiveWordsService.readKpWhenSelect(kpList);
        log.info("#getCustomerKpListByCustomerRealType-req:{},resp:{}",customerId,JSON.toJSONString(kpList));
        return kpList;
    }

    /**
     * 判断符合条件的签约人KP以及法人KP是否存在
     *
     * @param customerId
     * @param kpType
     * @return
     */
    public Boolean checkCustomerKpExist(Integer customerId, Byte kpType, Byte signerType) {
        try {
            if (customerId == null || kpType == null) {
                return false;
            }
            WmCustomerKp customerKp = wmCustomerKpDBMapper.getKpByCustomerIdAndKpType(customerId, kpType);
            if (customerKp != null) {
                if (kpType == KpTypeEnum.LEGAL.getType()) {
                    return true;
                }
                if (kpType == KpTypeEnum.SIGNER.getType() && customerKp.getSignerType() == KpSignerTypeEnum.SIGNER.getType()
                        && (customerKp.getState() == KpSignerStateMachine.RECORDED.getState()
                        || customerKp.getState() == KpSignerStateMachine.EFFECT.getState())) {
                    return true;
                }
            }
        } catch (Exception e) {
            LOGGER.error("根据客户ID和KP类型查询KP列表发生异常,customerId={},kpType={}", customerId, kpType, e);
        }
        return false;
    }

    /**
     * 获取单个客户KP信息
     *
     * @param kpId KPID
     * @return 客户KP列表对象
     */
    public WmCustomerKp getCustomerKpInfo(int kpId) {
        WmCustomerKp kpInfo = wmCustomerKpDBMapper.selectByIdRT(kpId);
        if (kpInfo == null) {
            LOGGER.info("kpId:{}无对应kp信息", kpId);
            return null;
        }
        //客户KP手机号读处理
        wmCustomerSensitiveWordsService.readKpWhenSelect(kpInfo);
        return kpInfo;
    }

    /**
     * 批量获取客户KP信息
     *
     * @param kpIdList KPID列表
     * @return 客户KP列表对象
     */
    public Map<Integer, WmCustomerKp> batchGetCustomerKpInfo(List<Integer> kpIdList) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.meituan.waimai.customer.service.kp.WmCustomerKpService.batchGetCustomerKpInfo(java.util.List)");
        List<WmCustomerKp> kpInfoList = wmCustomerKpDBMapper.selectByIdList(kpIdList);
        if (CollectionUtils.isEmpty(kpInfoList)) {
            LOGGER.info("kpIdList:{}无对应kp信息", JSON.toJSONString(kpInfoList));
            return null;
        }

        wmCustomerSensitiveWordsService.readKpWhenSelect(kpInfoList);

        Map<Integer, WmCustomerKp> kpInfoMap = kpInfoList.stream().collect(Collectors.toMap(WmCustomerKp::getId, wmCustomerKp -> wmCustomerKp));
        return kpInfoMap;
    }

    /**
     * 获取客户签约人KP,未生效也会查到
     *
     * @param customerId 客户ID
     * @return 签约人KP, 未找到返回null
     */
    public WmCustomerKp getCustomerSignerKp(int customerId) {
        List<WmCustomerKp> customerKpList = wmCustomerKpDBMapper.selectByCustomerId(customerId, KpTypeEnum.SIGNER.getType());
        if (CollectionUtils.isEmpty(customerKpList)) {
            return null;
        }
        for (WmCustomerKp wmCustomerKp : customerKpList) {
            if (KpTypeEnum.SIGNER.getType() == wmCustomerKp.getKpType()) {
                wmCustomerSensitiveWordsService.readKpWhenSelect(wmCustomerKp);
                return wmCustomerKp;
            }
        }
        return null;
    }

    /**
     * 根据业务线和外卖客户ID查询生效的签约人
     * @return
     */
    public List<WmCustomerKp> getCustomerSignerEffectiveByBusiness(QueryCustomerKpDTO queryCustomerKpDTO) throws WmCustomerException, TException {
        log.info("#getCustomerSignerEffectiveByBusiness-根据外卖客户ID和客户经营形式查询生效的签约人信息-query:{}",JSON.toJSONString(queryCustomerKpDTO));
        if (BusinessTypeEnum.WAIMAI.equals(queryCustomerKpDTO.getBusinessTypeEnum())){
            //外卖走原查询逻辑
            return Collections.singletonList(getCustomerKpOfEffectiveSignerRT(queryCustomerKpDTO.getWmCustomerId()));
        }else if (BusinessTypeEnum.DAOCAN.equals(queryCustomerKpDTO.getBusinessTypeEnum())){
            //优先使用入参中的平台ID查询，对应的生效到餐kp只会返回一个
            if (queryCustomerKpDTO.getMtCustomerId() != null){
                List<DcCustomerKp> dcCustomerKpList = dcCustomerKpService.getDcEffectiveCustomerKp(DcCustomerKpQueryDto.builder().mtCustomerId(queryCustomerKpDTO.getMtCustomerId()).build());
                return convertDcKpToWmKP(dcCustomerKpList);
            }
            //根据外卖客户ID查询到餐kp
            //先查外卖库，获取客户的平台ID和资质
            //再根据客户类型匹配使用平台ID查询还是使用资质查询
            WmCustomerDB wmCustomerDB = wmCustomerPlatformDataParseService.selectCustomerFromPlatformByIdRT(queryCustomerKpDTO.getWmCustomerId());
            if (wmCustomerDB == null){
                throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR,"客户ID非法");
            }
            //查询到餐客户
            List<DcCustomerKp> dcCustomerKpList = dcCustomerKpService.getDcEffectiveCustomerKp(buildDcCustomerKpQueryDto(wmCustomerDB));
            return convertDcKpToWmKP(dcCustomerKpList);
        }
        throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR,"客户经营业务参数非法");
    }

    private List<WmCustomerKp> convertDcKpToWmKP(List<DcCustomerKp> dcCustomerKpList){
        if (CollectionUtils.isEmpty(dcCustomerKpList)){
            return new ArrayList<>();
        }
        List<WmCustomerKp> wmCustomerKpList = new ArrayList<>();
        dcCustomerKpList.forEach(dcCustomerKp -> {
            WmCustomerKp wmCustomerKp = new WmCustomerKp();
            // 转换dcCustomerKp为wmCustomerKp
            wmCustomerKp.setId(dcCustomerKp.getId().intValue());
            wmCustomerKp.setCompellation(dcCustomerKp.getName());
            //到餐客户查询的都是签约人
            wmCustomerKp.setKpType(KpTypeEnum.SIGNER.getType());
            wmCustomerKp.setCertNumber(dcCustomerKp.getIdentityCardNumber());
            wmCustomerKp.setPhoneNum(dcCustomerKp.getMobileNo());
            //到餐客户KP查询的都是已生效的
            wmCustomerKp.setEffective(EFFECTIVE);
            wmCustomerKp.setState(KpSignerStateMachine.EFFECT.getState());
            wmCustomerKp.setMtCustomerId(dcCustomerKp.getDcPlatformId());
            CertTypeEnum certTypeEnum = KpCertTypeRelEnum.getWmCertTypeByDcCertType(dcCustomerKp.getCertificateType());
            wmCustomerKp.setCertType(certTypeEnum == null? 0 : certTypeEnum.getType());
            wmCustomerKpList.add(wmCustomerKp);
        });
        return wmCustomerKpList;
    }

    private DcCustomerKpQueryDto buildDcCustomerKpQueryDto(WmCustomerDB wmCustomerDB){
        // 还需要提供一个根据外卖客户ID查询经营形式的接口
        // 如果客户类型为仅到餐 可以直接用平台ID查
        DcCustomerKpQueryDto dto = new DcCustomerKpQueryDto();
        if (CustomerRealTypeEnum.DAOCAN.getValue() ==  wmCustomerDB.getCustomerRealType()){
            dto.setMtCustomerId(wmCustomerDB.getMtCustomerId());
        }else {
            // 外卖和到餐共用的情况下，需要用资质去查
            dto.setCustomerNumber(wmCustomerDB.getCustomerNumber());
        }
        return dto;
    }

    /**
     * 批量添加或修改客户KP信息，蜜蜂
     * @param wmCustomerKpList
     */
    @RepeatSubmission(seedExp = "customerId")
    public void batchSaveOrUpdateCustomerKp(int customerId, List<WmCustomerKp> wmCustomerKpList, int uid, String uname) throws WmCustomerException, TException {
        VersionCheckUtil.versionCheck(customerId, 0);
        LOGGER.info("batchSaveOrUpdateCustomerKp customerId={}, wmCustomerKpList = {}", customerId, JSON.toJSONString(wmCustomerKpList));
        Collections.sort(wmCustomerKpList, KPTYPE_COMPARABLE);
        //获取客户信息
        WmCustomerDB wmCustomer = wmCustomerPlatformDataParseService.selectCustomerFromPlatformById(customerId);
        if (wmCustomer == null) {
            ThrowUtil.throwClientError("不存在的客户,Id：" + customerId);
        }

        if (CollectionUtils.isNotEmpty(wmCustomerKpList)) {
            for (WmCustomerKp wmCustomerKp : wmCustomerKpList) {
                CustomerDeviceType customerDeviceType = CustomerDeviceType.getByCode(wmCustomerKp.getOperateSource());
                //设置来源为蜜蜂
                wmCustomerKp.setOperateSource(customerDeviceType == null ? CustomerDeviceType.APP.getCode() : wmCustomerKp.getOperateSource());
                // 注意：一定要重新设置客户id，自入驻渠道过来新建客户及kp，kp上的客户id为0，所以必须重设
                wmCustomerKp.setCustomerId(customerId);
                if (wmCustomerKp.getSignerType() == AGENT.getType()) {
                    wmCustomerKp.setHaveAgentAuth(StringUtils.isBlank(wmCustomerKp.getAgentAuth()) ? HaveAgentAuthEnum.NONE.getCode() : HaveAgentAuthEnum.HAVE.getCode());
                } else {
                    wmCustomerKp.setHaveAgentAuth(HaveAgentAuthEnum.NOT_SETTING.getCode());
                }
                ObjectUtil.defaultValue(wmCustomerKp);
            }
        }

        //获取DB中现有的客户KP信息
        List<WmCustomerKp> oldCustomerKpList = getCustomerKpList(customerId);

        //过滤运营经理KP数据
        oldCustomerKpList = oldCustomerKpList.stream().filter(kp -> KpTypeEnum.OPMANAGER != KpTypeEnum.getByType(kp.getKpType())).collect(
                Collectors.toList());
        wmCustomerKpList = wmCustomerKpList.stream().filter(kp -> KpTypeEnum.OPMANAGER != KpTypeEnum.getByType(kp.getKpType())).collect(
                Collectors.toList());

        //待添加的KP列表
        Collection<WmCustomerKp> addKpList = differentCustomerKpService.getAddKpList(wmCustomerKpList);
        //待删除的KP列表
        Collection<WmCustomerKp> deleteKpList = differentCustomerKpService.getDeleteKpList(oldCustomerKpList, wmCustomerKpList);

        //待更新的KP列表
        Collection<WmCustomerKp> upgradeKpList = differentCustomerKpService.getUpgradeKpList(wmCustomerKpList, deleteKpList);

        LOGGER.info("\noldCustomerKpList={},\naddKpList={},\ndeleteKpList={},\nupgradeKpList={}", JSON.toJSONString(oldCustomerKpList), JSON.toJSONString(addKpList), JSON.toJSONString(deleteKpList), JSON.toJSONString(upgradeKpList));
        WmCustomerKpOperateParam param = new WmCustomerKpOperateParam();
        param.setWmCustomer(wmCustomer);
        param.setUid(uid);
        param.setUname(uname);
        param.setOldCustomerKpList(oldCustomerKpList);
        operateKpBatch(addKpList, deleteKpList, upgradeKpList, param);
        //企客KP消息通知,关注所有kp变更
        mafkaMessageSendManager.send(new CustomerMQBody(wmCustomer.getId(), CustomerMQEventEnum.CUSTOMER_OTHER_KP_EFFECTIVE, ""));

    }

    /**
     * 批量修改KP信息
     *
     * @param addKpList
     * @param deleteKpList
     * @param upgradeKpList
     * @param operateParam
     * @throws TException
     * @throws WmCustomerException
     */
    private void operateKpBatch(Collection<WmCustomerKp> addKpList, Collection<WmCustomerKp> deleteKpList, Collection<WmCustomerKp> upgradeKpList, WmCustomerKpOperateParam operateParam) throws TException, WmCustomerException {
        WmCustomerDB wmCustomer = operateParam.getWmCustomer();
        int uid = operateParam.getUid();
        String uname = operateParam.getUname();
        List<WmCustomerKp> oldCustomerKpList = operateParam.getOldCustomerKpList();

        List<WmCustomerKpOperateParam> paramList = Lists.newArrayList();

        buildKpOperateParam(addKpList, KpOperationTypeConstant.INSERT, paramList);
        buildKpOperateParam(deleteKpList, KpOperationTypeConstant.DELETE, paramList);
        buildKpOperateParam(upgradeKpList, KpOperationTypeConstant.UPDATE, paramList);

        for (WmCustomerKpOperateParam param : paramList) {
            param.setWmCustomer(wmCustomer);
            param.setUid(uid);
            param.setUname(uname);
            param.setOldCustomerKpList(oldCustomerKpList);
            try {
                operateKp(param);
            } catch (WmServerException e) {
                LOGGER.warn("operateKpBatch失败 param = {}", JSONObject.toJSONString(param), e);
                ThrowUtil.throwClientError("实名认证错误");
            }
        }
    }

    private void buildKpOperateParam(Collection<WmCustomerKp> infoList, int operateType, List<WmCustomerKpOperateParam> paramList) {
        if (CollectionUtils.isEmpty(infoList)) {
            return;
        }
        for (WmCustomerKp kp : infoList) {
            paramList.add(new WmCustomerKpOperateParam(kp, operateType));
        }
    }



    @Transactional(rollbackFor = Exception.class)
    public void forceAuth(int kpId, int uid, String uname) throws WmCustomerException, TException {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.service.kp.WmCustomerKpService.forceAuth(int,int,java.lang.String)");
        LOGGER.info("forceAuth kpId = {}, uid = {}, uname = {}", kpId, uid, uname);
        WmCustomerKp customerKp = wmCustomerKpDBMapper.selectByPrimaryKey(kpId);
        wmCustomerSensitiveWordsService.readKpWhenSelect(customerKp);
        if (customerKp == null) {
            ThrowUtil.throwClientError("原签约KP不存在");
        }
        if (KpSignerStateMachine.EFFECT.getState() != customerKp.getState()) {
            ThrowUtil.throwClientError("原签约KP未生效");
        }
        WmCustomerKpTemp kpTemp = wmCustomerKpTempDBMapper.selectByKpId(kpId);
        wmCustomerSensitiveWordsService.readKpWhenSelect(kpTemp);
        if (kpTemp == null) {
            ThrowUtil.throwClientError("变更签约KP不存在");
        }
        if (KpSignerStateMachine.CHANGE_ORIGIN_SIGNER_AUTHING.getState() != kpTemp.getState()) {
            ThrowUtil.throwClientError("非原签约人授权中，不允许强制授权");
        }

        boolean hasAuth = upmAuthCheckService.hasRolePermission(uid, CustomerRoleTypeEnum.AGENT_FORCE_AUTH_ROLE.getCode());
        if (!hasAuth) {
            ThrowUtil.throwClientError("强制授权指当原签约人无法完成授权时使用，可不通过商家直接完成授权。");
        }

        List<WmCustomerDiffCellBo> diffCellBos = DiffUtil.compare(customerKp, kpTemp, differentCustomerKpService.getKpDiffFieldsMap());
        List<WmCustomerDiffCellBo> diffMessCellBos = DiffUtil.compare(customerKp, kpTemp, differentCustomerKpService.getKpMessDiffFieldsMap());
        String diffLog = DiffUtil.getDiffLog(diffCellBos);

        wmCustomerKpBuryingPointService.afterSingKp(diffMessCellBos, 0, transformWmCustomerKpTemp(kpTemp), customerKp);
//        kpDBOperate.tempKpEffect(kpTemp, customerKp);
        wmCustomerKpService.tempKpEffect(kpTemp, customerKp);
        wmCustomerKpDBMapper.updateCertNumberModify(CertNumberModifyEnum.YES.getCode(), customerKp.getId());
        wmCustomerKpTempDBMapper.deleteByPrimaryKey(kpTemp.getId());
        wmCustomerKpAuditMapper.deleteByKpId(customerKp.getId());
        wmCustomerKpLogService.insertOplog(customerKp.getCustomerId(), customerKp, "【强制授权】\n" + diffLog, uid, uname);
        //更新门店客户属性
        wmCustomerPoiAttributeService.updateForKpUpdateAsy(customerKp, false);
        /**
         * 满足如下条件掉客户四要素标签
         *  1.授权通过
         *  2.生效
         *  3.签约类型为非签约人或者证件类型为非身份证
         */
        if (customerKp.getSignerType() != KpSignerTypeEnum.SIGNER.getType()
                || !ID_CERT_TYPE.contains((int) customerKp.getCertType())) {
            wmCustomerKpRealAuthService.deleteFourEleTag(customerKp.getCustomerId());
        }
        WmCustomerKpAudit wmCustomerKpAudit = wmCustomerKpAuditMapper.selectByKpIdAndType(kpId, Integer.valueOf(KpAuditConstants.TYPE_AUTH));
        if (wmCustomerKpAudit == null) {
            LOGGER.warn("未找到授权签约记录，kpId={}", kpId);
            mafkaMessageSendManager.send(new CustomerMQBody(customerKp.getCustomerId(), CustomerMQEventEnum.CUSTOMER_KP_EFFECTIVE, null));
            return;
        }
        try {
            LOGGER.info("wmEcontractSignBzService.cancelSign commitId={}", wmCustomerKpAudit.getCommitId());
            BooleanResult result = wmEcontractSignBzService.cancelSign(Long.valueOf(wmCustomerKpAudit.getCommitId()));
            LOGGER.info("wmEcontractSignBzService.cancelSign result={}", result.isRes());
        } catch (Exception e) {
            LOGGER.error("取消短信授权异常, commitId={}", wmCustomerKpAudit.getCommitId(), e);
        }
        mafkaMessageSendManager.send(new CustomerMQBody(customerKp.getCustomerId(), CustomerMQEventEnum.CUSTOMER_KP_EFFECTIVE, null));
    }

    @Transactional(rollbackFor = Exception.class)
    public void cancelAuth(int kpId, int uid, String uname) throws WmCustomerException {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.meituan.waimai.customer.service.kp.WmCustomerKpService.cancelAuth(int,int,java.lang.String)");
        LOGGER.info("cancelAuth kpId = {}, uid = {}, uname = {}", kpId, uid, uname);
        WmCustomerKp customerKp = wmCustomerKpDBMapper.selectByPrimaryKey(kpId);
        wmCustomerSensitiveWordsService.readKpWhenSelect(customerKp);
        if (customerKp == null) {
            ThrowUtil.throwClientError("原签约KP不存在");
        }
        if (KpSignerStateMachine.EFFECT.getState() != customerKp.getState()) {
            ThrowUtil.throwClientError("原签约KP未生效");
        }
        WmCustomerKpTemp kpTemp = wmCustomerKpTempDBMapper.selectByKpId(kpId);
        if (kpTemp == null) {
            ThrowUtil.throwClientError("变更签约KP不存在");
        }
        if (KpSignerStateMachine.CHANGE_ORIGIN_SIGNER_AUTHING.getState() != kpTemp.getState()) {
            ThrowUtil.throwClientError("非原签约人授权中，不允许取消授权");
        }
        kpTemp.setState(KpSignerStateMachine.CHANGE_ORIGIN_SIGNER_CANCEL.getState());
        wmCustomerSensitiveWordsService.writeKpSourceWhenUpdate(kpTemp);
        wmCustomerKpTempDBMapper.updateByPrimaryKey(kpTemp);
        wmCustomerKpLogService.insertOplog(customerKp.getCustomerId(), customerKp, "【取消授权】BD主动取消授权，状态重置为授权失败\n tempId:" + kpTemp.getId(), uid, uname);
        WmCustomerKpAudit wmCustomerKpAudit = wmCustomerKpAuditMapper.selectByKpIdAndType(kpId, Integer.valueOf(KpAuditConstants.TYPE_AUTH));
        LOGGER.info("wmCustomerKpAudit={}", JSON.toJSONString(wmCustomerKpAudit));
        if (wmCustomerKpAudit == null) {
            return;
        }
        wmCustomerKpAudit.setValid(KpConstants.UN_VALID);
        wmCustomerKpAudit.setResult("手动取消授权");
        wmCustomerKpAuditMapper.updateByPrimaryKey(wmCustomerKpAudit);
        //取消短信授权
        Integer commitId = wmCustomerKpAudit.getCommitId();
        try {
            wmCustomerKpDBMapper.updateCertNumberModify(CertNumberModifyEnum.YES.getCode(), customerKp.getId());
            LOGGER.info("wmEcontractSignBzService.cancelSign commitId={}", commitId);
            BooleanResult result = wmEcontractSignBzService.cancelSign(Long.valueOf(commitId));
            LOGGER.info("wmEcontractSignBzService.cancelSign result={}", result.isRes());
        } catch (Exception e) {
            LOGGER.error("取消短信授权异常, commitId={}", commitId, e);
        }
        wmCustomerKpAuditMapper.deleteByKpId(customerKp.getId());
    }

    public void reSendAuthSms(Integer kpId, int uid, String uname) throws WmCustomerException {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.service.kp.WmCustomerKpService.reSendAuthSms(java.lang.Integer,int,java.lang.String)");
        LOGGER.info("reSendAuthSms kpId = {}, uid = {}, uname = {}", kpId, uid, uname);
        WmCustomerKp customerKp = wmCustomerKpDBMapper.selectByPrimaryKey(kpId);
        wmCustomerSensitiveWordsService.readKpWhenSelect(customerKp);
        if (customerKp == null) {
            ThrowUtil.throwClientError("原签约KP不存在");
        }
        if (KpSignerStateMachine.EFFECT.getState() != customerKp.getState()) {
            ThrowUtil.throwClientError("原签约KP未生效");
        }
        WmCustomerKpTemp kpTemp = wmCustomerKpTempDBMapper.selectByKpId(kpId);
        if (kpTemp == null) {
            ThrowUtil.throwClientError("变更签约KP不存在");
        }
        if (KpSignerStateMachine.CHANGE_ORIGIN_SIGNER_AUTHING.getState() != kpTemp.getState()) {
            ThrowUtil.throwClientError("非原签约人授权中，不能重新发送短信");
        }
        WmCustomerKpAudit wmCustomerKpAudit = wmCustomerKpAuditMapper.selectByKpIdAndType(kpId, Integer.valueOf(KpAuditConstants.TYPE_AUTH));
        if (wmCustomerKpAudit == null) {
            ThrowUtil.throwClientError("未找到变更授权记录");
        }
        try {
            wmCustomerKpDBMapper.updateCertNumberModify(CertNumberModifyEnum.YES.getCode(), customerKp.getId());
            LOGGER.info("wmEcontractSignBzService.resendMsg commitId={}", wmCustomerKpAudit.getCommitId());
            RetrySmsResponse result = wmEcontractSignBzService.resendMsg(Long.valueOf(wmCustomerKpAudit.getCommitId()));
            LOGGER.info("wmEcontractSignBzService.resendMsg result={}", result.isOk());
            if (!result.isOk()) {
                ThrowUtil.throwSeverError("变更授权重发短信失败，请重试");
            }
        } catch (WmCustomerException e) {
            throw e;
        } catch (Exception e) {
            LOGGER.warn("变更授权重发短信异常, kpId={}, commitId={}", kpId, wmCustomerKpAudit.getCommitId(), e);
            ThrowUtil.throwSeverError("变更授权重发短信失败，请重试");
        }
    }

    public void commitAudit(WmCustomerKp wmCustomerKp, byte auditType, int uid, String uname) throws WmServerException, TException {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.meituan.waimai.customer.service.kp.WmCustomerKpService.commitAudit(com.sankuai.meituan.waimai.thrift.customer.domain.kp.WmCustomerKp,byte,int,java.lang.String)");
        LOGGER.info("commitAudit wmCustomerKp = {}, auditType = {}, uid = {}, uname = {}", JSON.toJSONString(wmCustomerKp), auditType, uid, uname);
        WmCustomerKpAudit audit = new WmCustomerKpAudit();
        audit.setKpId(wmCustomerKp.getId());
        audit.setType(auditType);
        audit.setValid(KpConstants.VALID);
        audit.setUid(uid);
        audit.setUname(uname);
        audit.setExtra("");
        ObjectUtil.defaultValue(audit);
        wmCustomerKpAuditMapper.insert(audit);
        String log;
        int bizType;
        String dataJson;
        if (auditType == KpAuditConstants.TYPE_AGENT) {
            log = "代理人提交审核，auditId:" + audit.getId();
            bizType = WmAuditTaskBizTypeConstant.AGENT_AUTH;
            //构造代理人提审数据
            WmAuditAgentAuthCommitData data = new WmAuditAgentAuthCommitData();
            data.setName(wmCustomerKp.getCompellation());
            data.setCardType(Integer.valueOf(wmCustomerKp.getCertType()));
            CertTypeEnum certTypeEnum = CertTypeEnum.getByType(wmCustomerKp.getCertType());
            data.setCardTypeStr(certTypeEnum == null ? "未知证件类型" : certTypeEnum.getName());
            data.setCardNo(wmCustomerKp.getCertNumber());
            data.setPhoneNum(wmCustomerKp.getPhoneNum());
            data.setBankNum(wmCustomerKp.getCreditCard());
            data.setAgentAuthUrl(wmCustomerKp.getAgentAuth());
            data.setAgentCardUrl(wmCustomerKp.getAgentFrontIdcard());
            data.setAgentCardBackUrl(wmCustomerKp.getAgentBackIdcard());
            try {
                WmCustomerDB wmCustomer = wmCustomerPlatformDataParseService.selectCustomerFromPlatformById(wmCustomerKp.getCustomerId());
                if (wmCustomer != null) {
                    if (!StringUtil.isBlank(wmCustomer.getLegalPerson())) {
                        data.setLegalPerson(wmCustomer.getLegalPerson());
                    }
                    if (wmCustomer.getBizOrgCode() != null && wmCustomer.getBizOrgCode().intValue() > 0) {
                        data.setCustomerBusinessLine(wmCustomer.getBizOrgCode());
                    }
                    CustomerRealTypeEnum customerRealTypeEnum = CustomerRealTypeEnum.getByValue(wmCustomer.getCustomerRealType());
                    data.setCustomerType(customerRealTypeEnum.getName());
                    data.setCustomerTypeCode(customerRealTypeEnum.getValue());
                }
            } catch (WmCustomerException e) {
                LOGGER.error("代理人提审获取客户信息失败 wmCustomerKp={}", JSONObject.toJSONString(wmCustomerKp), e);
            }
            data.setLegalPersonIdCardCopy(wmCustomerKp.getLegalIdcardCopy());
            dataJson = JSON.toJSONString(data);
        } else if (auditType == KpAuditConstants.TYPE_SPECIAL) {
            log = "特批认证提交审核，auditId:" + audit.getId();
            bizType = WmAuditTaskBizTypeConstant.SPECIAL_AUTH;
            //构造特批提审数据
            WmAuditSpecialAuthCommitData data = new WmAuditSpecialAuthCommitData();
            data.setName(wmCustomerKp.getCompellation());
            CertTypeEnum certTypeEnum = CertTypeEnum.getByType(wmCustomerKp.getCertType());
            data.setCardType(Integer.valueOf(wmCustomerKp.getCertType()));
            data.setCardTypeStr(certTypeEnum != null ? certTypeEnum.getName() : "未知证件类型");
            data.setCardNo(wmCustomerKp.getCertNumber());
            data.setPhoneNum(wmCustomerKp.getPhoneNum());
            data.setBankNum(wmCustomerKp.getCreditCard());
            data.setSpecialAuthUrl(Arrays.asList(wmCustomerKp.getSpecialAttachment().split(";")));
            try {
                WmCustomerDB wmCustomer = wmCustomerPlatformDataParseService.selectCustomerFromPlatformById(wmCustomerKp.getCustomerId());
                if (wmCustomer != null) {
                    if (wmCustomer.getBizOrgCode() != null && wmCustomer.getBizOrgCode().intValue() > 0) {
                        data.setCustomerBusinessLine(wmCustomer.getBizOrgCode());
                    }
                    CustomerRealTypeEnum customerRealTypeEnum = CustomerRealTypeEnum.getByValue(wmCustomer.getCustomerRealType());
                    data.setCustomerType(customerRealTypeEnum.getName());
                    data.setCustomerTypeCode(customerRealTypeEnum.getValue());
                }
            } catch (WmCustomerException e) {
                LOGGER.error("特批认证提审获取客户信息失败 wmCustomerKp={}", JSONObject.toJSONString(wmCustomerKp), e);
            }
            dataJson = JSON.toJSONString(data);
        } else {
            throw new WmServerException(500, "不合法的审核类型：" + auditType);
        }
        LOGGER.info(log);
        WmAuditCommitObj commitObj = new WmAuditCommitObj()
                .setBiz_type(bizType)
                .setBiz_id(audit.getId())
                .setWm_poi_id(0)
                .setCustomer_id(wmCustomerKp.getCustomerId())
                .setSubmit_uid(uid)
                .setData(dataJson);
        try {
            LOGGER.info("commitAudit commitObj={}", JSON.toJSONString(commitObj));
            WmAuditMsg auditMsg = wmAuditApiService.commitAudit(commitObj);
            // 客户KP提审埋点
            try{
            MetricHelper.build()
                    .name(MetricConstant.METRIC_CUSTOMER_KP_COMMIT_AUDIT_COUNT)
                    .tag("biz_type",String.valueOf(bizType))
                    .tag("success", String.valueOf(auditMsg.isResult()))
                    .count();
            }catch (Exception e){
                LOGGER.warn("metricCustomerKpAudit error", e);
            }
            LOGGER.info("commitAudit result={}", JSON.toJSONString(auditMsg));
            if (!auditMsg.isResult()) {
                LOGGER.error("提审KP失败:{}", auditMsg.getMsg());
            }
        } catch (WmServerException e) {
            LOGGER.warn("提审KP审核失败, auditType={}, wmCustomerKp={}, uid={}, uname={}", auditType, JSON.toJSONString(wmCustomerKp), uid, uname, e);
            throw e;
        } catch (Exception e) {
            LOGGER.error("提交KP审核异常, auditType={}, wmCustomerKp={}, uid={}, uname={}", auditType, JSON.toJSONString(wmCustomerKp), uid, uname, e);
            throw e;
        }
    }

    public List<WmCustomerKp> batchGetWmCustomerKpByKpIdList(List<Integer> kpIdList, int kpType) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.kp.WmCustomerKpService.batchGetWmCustomerKpByKpIdList(java.util.List,int)");
        List<WmCustomerKp> result = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(kpIdList)) {
            result = wmCustomerKpDBMapper.selectByIdListByKpType(kpIdList, kpType);
        }
        wmCustomerSensitiveWordsService.readKpWhenSelect(result);
        return result;
    }

    public void modifyKpInfo(int customerId, WmCustomerKp wmCustomerKp, int operateType, int uid, String uname) throws TException, WmCustomerException, WmServerException {
        LOGGER.info("modifyKpInfo#customerId={},wmCustomerKp={},operateType={},uid={},uname={}", customerId,
                JSONObject.toJSONString(wmCustomerKp), operateType, uid, uname);

        // 判断类型，调用不同KP类型处理器
        WmCustomerDB wmCustomer = wmCustomerPlatformDataParseService.selectCustomerFromPlatformById(customerId);
        if (wmCustomer == null) {
            wmCustomer = wmCustomerPlatformDataParseService.selectCustomerFromPlatformByIdRT(customerId);
        }
        if (wmCustomer == null) {
            ThrowUtil.throwClientError("不存在的客户,Id：" + customerId);
        }
        // 对于KP信息修改，需要先执行客户黑名单校验
        checkCustomerBlackList(wmCustomer);
        //KP中客户ID兜底逻辑，发现com.sankuai.sgmerchant.ruzhu服务调用modifyKpInfo接口的时候，wmCustomerKp对象中的customerId传的值为0
        if (wmCustomerKp.getCustomerId() <= 0) {
            wmCustomerKp.setCustomerId(customerId);
        }
        if (wmCustomerKp.getSignerType() == AGENT.getType()) {
            wmCustomerKp.setHaveAgentAuth(StringUtils.isBlank(wmCustomerKp.getAgentAuth()) ? HaveAgentAuthEnum.NONE.getCode() : HaveAgentAuthEnum.HAVE.getCode());
        } else {
            wmCustomerKp.setHaveAgentAuth(HaveAgentAuthEnum.NOT_SETTING.getCode());
        }

        //客户类型为签约人或法人，版本号为空，默认处理为V3版本号
        if ((wmCustomerKp.getKpType() == KpTypeEnum.SIGNER.getType() || wmCustomerKp.getKpType() == KpTypeEnum.LEGAL.getType())
                && wmCustomerKp.getVersion() == null) {
            log.info("modifyKpInfo,KP版本号为空，默认处理为v3版本号,customerId={},wmCustomerKp={}", customerId, wmCustomerKp);
            wmCustomerKp.setVersion(KpVersionEnum.V3.getCode());
        }

        //获取DB中现有的客户KP信息
        List<WmCustomerKp> oldCustomerKpList = getCustomerKpList(customerId);
        LOGGER.info("modifyKpInfo#KP修改前的信息为:{}", JSON.toJSONString(oldCustomerKpList));
        //请求路由
        operateKp(new WmCustomerKpOperateParam(wmCustomer, wmCustomerKp, operateType, uid, uname, oldCustomerKpList));
    }

    private void checkCustomerBlackList(WmCustomerDB wmCustomer) throws WmCustomerException {
        WmCustomerBasicBo wmCustomerBasicBo = new WmCustomerBasicBo();
        wmCustomerBasicBo.setCustomerType(wmCustomer.getCustomerType());
        wmCustomerBasicBo.setCustomerSecondType(wmCustomer.getCustomerSecondType());
        wmCustomerBasicBo.setCustomerNumber(wmCustomer.getCustomerNumber());
        wmCustomerBasicBo.setId(wmCustomer.getId());
        log.info("KP修改入口：执行客户资质黑名单校验：CustomerType={}, CustomerSecondType={}, CustomerNumber={}",wmCustomerBasicBo.getCustomerType(), wmCustomerBasicBo.getCustomerSecondType(), wmCustomerBasicBo.getCustomerNumber());
        customerBlackListValidator.checkCustomerBlackList(wmCustomerBasicBo);
    }

    public void modifyKpInfo(WmCustomerDB wmCustomer, WmCustomerKp wmCustomerKp, int operateType, int uid, String uname) throws TException, WmCustomerException, WmServerException {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.meituan.waimai.customer.service.kp.WmCustomerKpService.modifyKpInfo(WmCustomerDB,WmCustomerKp,int,int,String)");
        LOGGER.info("modifyKpInfo#wmCustomer={},wmCustomerKp={},operateType={},uid={},uname={}", JSONObject.toJSONString(wmCustomer),
                JSONObject.toJSONString(wmCustomerKp), operateType, uid, uname);
        // 判断类型，调用不同KP类型处理器
        if (wmCustomer == null) {
            ThrowUtil.throwClientError("不存在的客户");
        }
        //KP中客户ID兜底逻辑，发现com.sankuai.sgmerchant.ruzhu服务调用modifyKpInfo接口的时候，wmCustomerKp对象中的customerId传的值为0
        if (wmCustomerKp.getCustomerId() <= 0) {
            wmCustomerKp.setCustomerId(wmCustomer.getId());
        }
        if (wmCustomerKp.getSignerType() == AGENT.getType()) {
            wmCustomerKp.setHaveAgentAuth(StringUtils.isBlank(wmCustomerKp.getAgentAuth()) ? HaveAgentAuthEnum.NONE.getCode() : HaveAgentAuthEnum.HAVE.getCode());
        } else {
            wmCustomerKp.setHaveAgentAuth(HaveAgentAuthEnum.NOT_SETTING.getCode());
        }
        //获取DB中现有的客户KP信息
        List<WmCustomerKp> oldCustomerKpList = getCustomerKpList(wmCustomer.getId());
        LOGGER.info("modifyKpInfo#KP修改前的信息为:{}", JSON.toJSONString(oldCustomerKpList));
        //请求路由
        operateKp(new WmCustomerKpOperateParam(wmCustomer, wmCustomerKp, operateType, uid, uname, oldCustomerKpList));
    }

    public void businessModifyKpInfo(int customerId, WmCustomerKp wmCustomerKp, int operateType, int uid, String uname) throws TException, WmCustomerException, WmServerException {
        LOGGER.info("modifyBusinessKpInfo#customerId={},wmCustomerKp={},operateType={},uid={},uname={}", customerId,
                JSONObject.toJSONString(wmCustomerKp), operateType, uid, uname);
        // 判断类型，调用不同KP类型处理器
        WmCustomerDB wmCustomer = wmCustomerPlatformDataParseService.selectCustomerFromPlatformById(customerId);
        if (wmCustomer == null) {
            ThrowUtil.throwClientError("不存在的客户,Id：" + customerId);
        }
        //商家端修改，代理人必须要有代理人授权书
        if (wmCustomerKp.getKpType() == KpTypeEnum.SIGNER.getType()
                && wmCustomerKp.getSignerType() == AGENT.getType()
                && (wmCustomerKp.getLegalAuthType() == null || wmCustomerKp.getLegalAuthType() != LegalAuthTypeEnum.MESSAGE_AUTH.getCode())) {
            if (StringUtils.isBlank(wmCustomerKp.getAgentAuth())) {
                ThrowUtil.throwClientError("代理人修改必须上传授权书");
            }
            wmCustomerKp.setHaveAgentAuth(HaveAgentAuthEnum.HAVE.getCode());
        } else {
            wmCustomerKp.setHaveAgentAuth(HaveAgentAuthEnum.NOT_SETTING.getCode());
        }

        if (wmCustomerKp.getKpType() == KpTypeEnum.LEGAL.getType()) {
            if (wmCustomerKp.getCertType() != CertTypeEnum.ID_CARD.getType()) {
                ThrowUtil.throwClientError("KP法人证件类型只能是身份证");
            }
            if (!wmCustomerKp.getCompellation().equals(wmCustomer.getLegalPerson())) {
                ThrowUtil.throwClientError("KP法人名称需要与资质法人姓名一致");
            }
        }

        //获取DB中现有的客户KP信息
        List<WmCustomerKp> oldCustomerKpList = getCustomerKpList(customerId);
        LOGGER.info("modifyBusinessKpInfo#KP修改前的信息为:{}", JSONObject.toJSONString(oldCustomerKpList));
        //请求路由
        businessOperateKp(new WmCustomerKpOperateParam(wmCustomer, wmCustomerKp, operateType, uid, uname, oldCustomerKpList));
    }

    private void businessOperateKp(WmCustomerKpOperateParam param) throws TException, WmServerException, WmCustomerException {
        String status = WmCustomerConstant.SUCCESS;
        String reason = "";
        try {
            WmCustomerDB wmCustomer = param.getWmCustomer();

            WmCustomerKp wmCustomerKp = param.getWmCustomerKp();
            int operateType = param.getOperateType();
            int uid = param.getUid();
            String uname = param.getUname();
            List<WmCustomerKp> oldCustomerKpList = param.getOldCustomerKpList();
            switch (KpTypeEnum.getByType(wmCustomerKp.getKpType())) {
                case SIGNER:
                    switch (operateType) {
                        case UPDATE:
                            businessSignerOperate.update(wmCustomer, oldCustomerKpList, wmCustomerKp, uid, uname);
                            break;
                        default:
                            ThrowUtil.throwClientError("商家端只能进行修改操作");
                            break;
                    }
                    break;
                case LEGAL:
                    switch (operateType) {
                        case INSERT:
                            legalOperate.insert(wmCustomer, oldCustomerKpList, wmCustomerKp, uid, uname);
                            break;
                        case UPDATE:
                            legalOperate.update(wmCustomer, oldCustomerKpList, wmCustomerKp, uid, uname);
                            break;
                        default:
                            ThrowUtil.throwClientError("商家端只能新增和修改KP法人");
                            break;

                    }
                    break;
                default:
                    ThrowUtil.throwClientError("商家端只能操作签约人KP和法人KP");
            }
        } catch (WmCustomerException e) {
            status = WmCustomerConstant.BUSINESS_EXCEPTION;
            reason = e.getMsg();
            throw e;
        } catch (WmServerException e) {
            status = WmCustomerConstant.BUSINESS_EXCEPTION;
            reason = e.getMsg();
            throw e;
        } catch (Exception e) {
            status = WmCustomerConstant.SYSTEM_EXCEPTION;
            reason = WmCustomerConstant.SYSTEM_EXCEPTION_MSG;
            throw e;
        } finally {
            String typeName = "UNKNOW";
            KpTypeEnum kpTypeEnum = KpTypeEnum.getByType(param.getWmCustomerKp().getKpType());
            if (kpTypeEnum != null) {
                typeName = kpTypeEnum.name();
            }
            Cat.logEvent(CustomerMetricEnum.CUSTOMER_KP_SAVE.getName(), typeName, status, reason);
            MetricHelper.build().name(CustomerMetricEnum.CUSTOMER_KP_SAVE.getName()).tag(CustomerMetricEnum.CUSTOMER_KP_SAVE.getTag(), typeName)
                    .tag(CustomerMetricEnum.CUSTOMER_KP_SAVE.getStatus(), status).count();
        }
    }
    private void operateKp(WmCustomerKpOperateParam param) throws TException, WmServerException, WmCustomerException {
        WmCustomerDB wmCustomer = param.getWmCustomer();
        WmCustomerKp wmCustomerKp = param.getWmCustomerKp();
        int operateType = param.getOperateType();
        int uid = param.getUid();
        String uname = param.getUname();
        List<WmCustomerKp> oldCustomerKpList = param.getOldCustomerKpList();
        String status = WmCustomerConstant.SUCCESS;
        String reason = "";
        try {
            switch (KpTypeEnum.getByType(wmCustomerKp.getKpType())) {
                case SIGNER:
                    switch (operateType) {
                        case INSERT:
                            signerOperate.insert(wmCustomer, oldCustomerKpList, wmCustomerKp, uid, uname);
                            break;
                        case UPDATE:
                            signerOperate.update(wmCustomer, oldCustomerKpList, wmCustomerKp, uid, uname);
                            break;
                        case DELETE:
                            signerOperate.delete(wmCustomer, oldCustomerKpList, wmCustomerKp, uid, uname);
                            break;
                        default:
                            ThrowUtil.throwClientError("非法的操作类型");
                    }
                    break;
                case LEGAL:
                    switch (operateType) {
                        case INSERT:
                            legalOperate.insert(wmCustomer, oldCustomerKpList, wmCustomerKp, uid, uname);
                            break;
                        case UPDATE:
                            legalOperate.update(wmCustomer, oldCustomerKpList, wmCustomerKp, uid, uname);
                            break;
                        case DELETE:
                            legalOperate.delete(wmCustomer, oldCustomerKpList, wmCustomerKp, uid, uname);
                            break;
                        default:
                            ThrowUtil.throwClientError("非法的操作类型");
                    }
                    break;
                case OTHER:
                    switch (operateType) {
                        case INSERT:
                            otherOperate.insert(wmCustomer, oldCustomerKpList, wmCustomerKp, uid, uname);
                            break;
                        case UPDATE:
                            otherOperate.update(wmCustomer, oldCustomerKpList, wmCustomerKp, uid, uname);
                            break;
                        case DELETE:
                            otherOperate.delete(wmCustomer, oldCustomerKpList, wmCustomerKp, uid, uname);
                            break;
                        default:
                            ThrowUtil.throwClientError("非法的操作类型");
                    }
                    break;
                case VISITKP:
                    switch (operateType) {
                        case INSERT:
                            visitkpOperate.insert(wmCustomer, oldCustomerKpList, wmCustomerKp, uid, uname);
                            break;
                        case UPDATE:
                            visitkpOperate.update(wmCustomer, oldCustomerKpList, wmCustomerKp, uid, uname);
                            break;
                        case DELETE:
                            visitkpOperate.delete(wmCustomer, oldCustomerKpList, wmCustomerKp, uid, uname);
                            break;
                        default:
                            ThrowUtil.throwClientError("非法的操作类型");
                    }
                    break;
                case OPMANAGER:
                    switch (operateType) {
                        case INSERT:
                            opmanagerOperate.insert(wmCustomer, oldCustomerKpList, wmCustomerKp, uid, uname);
                            break;
                        case UPDATE:
                            opmanagerOperate.update(wmCustomer, oldCustomerKpList, wmCustomerKp, uid, uname);
                            break;
                        case DELETE:
                            opmanagerOperate.delete(wmCustomer, oldCustomerKpList, wmCustomerKp, uid, uname);
                            break;
                        default:
                            ThrowUtil.throwClientError("非法的操作类型");
                    }
                    break;
                default:
                    ThrowUtil.throwClientError("非法的KP类型");
            }
        } catch (WmCustomerException e) {
            status = WmCustomerConstant.BUSINESS_EXCEPTION;
            reason = e.getMsg();
            throw e;
        } catch (WmServerException e) {
            status = WmCustomerConstant.BUSINESS_EXCEPTION;
            reason = e.getMsg();
            throw e;
        } catch (Exception e) {
            status = WmCustomerConstant.SYSTEM_EXCEPTION;
            reason = WmCustomerConstant.SYSTEM_EXCEPTION_MSG;
            throw e;
        } finally {
            String typeName = "UNKNOW";
            KpTypeEnum kpTypeEnum = KpTypeEnum.getByType(wmCustomerKp.getKpType());
            if (kpTypeEnum != null) {
                typeName = kpTypeEnum.name();
            }
            Cat.logEvent(CustomerMetricEnum.CUSTOMER_KP_SAVE.getName(), typeName, status, reason);
            MetricHelper.build().name(CustomerMetricEnum.CUSTOMER_KP_SAVE.getName()).tag(CustomerMetricEnum.CUSTOMER_KP_SAVE.getTag(), typeName)
                    .tag(CustomerMetricEnum.CUSTOMER_KP_SAVE.getStatus(), status).count();
        }
    }


    public List<Long> batchBindWmPoi(Integer customerId, Integer kpId, String wmPoiIds) throws WmCustomerException {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.service.kp.WmCustomerKpService.batchBindWmPoi(java.lang.Integer,java.lang.Integer,java.lang.String)");
        return opmanagerRelPoiBindVerify.batchBindWmPoi(customerId, kpId, wmPoiIds);
    }

    public boolean batchUnbindWmPoi(Integer customerId, String wmPoiIds) throws WmCustomerException {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.service.kp.WmCustomerKpService.batchUnbindWmPoi(java.lang.Integer,java.lang.String)");
        return opmanagerRelPoiBindVerify.batchUnbindWmPoi(customerId, wmPoiIds);
    }

    public boolean singleQueryWmPoiInfo(Integer customerId, Integer kpId, Long wmPoiId) throws WmCustomerException {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.service.kp.WmCustomerKpService.singleQueryWmPoiInfo(java.lang.Integer,java.lang.Integer,java.lang.Long)");
        return opmanagerRelPoiBindVerify.singleQueryWmPoiInfo(customerId, kpId, wmPoiId);
    }

    public List<WmCustomerKp> getCustomerKpOfEffectiveOpmanager(int customerId) {
        List<WmCustomerKp> wmCustomerKpList = wmCustomerKpDBMapper.selectByCustomerId(customerId, KpTypeEnum.OPMANAGER.getType());
        wmCustomerSensitiveWordsService.readKpWhenSelect(wmCustomerKpList);
        return wmCustomerKpList;
    }

    public WmCustomerKp getLatestSignerKp(Integer customerId) {
        LOGGER.info("getLatestSignerKp#查询最新的签约人KP customerId = {}", customerId);
        WmCustomerKp wmCustomerKp = getCustomerSignerKp(customerId);
        if (wmCustomerKp == null) {
            return null;
        }

        WmCustomerKpTemp wmCustomerKpTemp = wmCustomerKpTempDBMapper.selectByKpId(wmCustomerKp.getId());
        if (wmCustomerKpTemp != null) {
            wmCustomerSensitiveWordsService.readKpWhenSelect(wmCustomerKpTemp);
            BeanUtils.copyProperties(wmCustomerKpTemp, wmCustomerKp);
            wmCustomerKp.setId(wmCustomerKpTemp.getKpId());
        }
        return wmCustomerKp;
    }

    public WmCustomerKp getEffectSignerKp(long customerId) throws WmCustomerException {
        LOGGER.info("getEffectSignerKp customerId = {}", customerId);
        WmCustomerDB wmCustomerDB = wmCustomerPlatformDataParseService.getCustomerByIdOrMtCustomerId(customerId);
        if (wmCustomerDB == null) {
            return null;
        }
        return getCustomerKpOfEffectiveSigner(wmCustomerDB.getId());
    }

    public List<WmCustomerKp> batchGetCustomerKpOfEffectiveSigner(List<Integer> customerIdList) {
        LOGGER.info("batchGetCustomerKpOfEffectiveSigner customerIdList = {}", JSON.toJSONString(customerIdList));
        if (CollectionUtils.isEmpty(customerIdList)) {
            return Lists.newArrayList();
        }

        List<WmCustomerKp> wmCustomerKpList = wmCustomerKpDBMapper.selectByCustomerIdList(customerIdList, KpTypeEnum.SIGNER.getType());
        wmCustomerSensitiveWordsService.readKpWhenSelect(wmCustomerKpList);
        return wmCustomerKpList.stream().filter(item -> KpSignerStateMachine.EFFECT.getState() == item.getState()).collect(Collectors.toList());
    }

    /**
     * 查询客户信息的KP信息
     *
     * @param customerIdList 客户主键ID集合
     * @param kpType         KP类型
     * @param isMaster       是否查询主库
     * @return
     */
    public List<WmCustomerKp> getCustomerKp(List<Integer> customerIdList, Integer kpType, boolean isMaster) {
        LOGGER.info("getCustomerKp::customerIdList = {}", JSON.toJSONString(customerIdList));
        List<WmCustomerKp> result = new ArrayList<>();
        if (CollectionUtils.isEmpty(customerIdList)) {
            return result;
        }
        WmCustomerKpSearchCondition condition = new WmCustomerKpSearchCondition();
        condition.setValid(ValidEnum.VALID_YES.getValue());
        condition.setKpType(kpType);
        if (customerIdList.size() == 1) {
            condition.setCustomerId(customerIdList.get(0));
        } else {
            condition.setCustomerIdList(customerIdList);
        }
        if (isMaster) {
            List<WmCustomerKp> kpList = wmCustomerKpDBMapper.selectByConditionMaster(condition);
            wmCustomerSensitiveWordsService.readKpWhenSelect(kpList);
            return kpList;
        }
        List<WmCustomerKp> kpList = wmCustomerKpDBMapper.selectByCondition(condition);
        wmCustomerSensitiveWordsService.readKpWhenSelect(kpList);
        return kpList;
    }

    public List<WmCustomerKp> getWmCustomerKpByCondition(WmCustomerKpSearchCondition condition) throws TException, WmCustomerException {
        List<WmCustomerKp> result = wmCustomerKpDBMapper.selectByCondition(condition);
        wmCustomerSensitiveWordsService.readKpWhenSelect(result);
        return result;
    }

    public List<WmCustomerKpTemp> getWmCustomerKpTempByCondition(WmCustomerKpTempSearchCondition condition) throws TException, WmCustomerException {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.service.kp.WmCustomerKpService.getWmCustomerKpTempByCondition(com.sankuai.meituan.waimai.thrift.customer.domain.kp.WmCustomerKpTempSearchCondition)");
        List<WmCustomerKpTemp> result = wmCustomerKpTempDBMapper.selectByCondition(condition);
        wmCustomerSensitiveWordsService.readKpTempWhenSelect(result);
        return result;
    }

    /**
     * 专为商家端查询提供接口
     * 根据wmPoiId查询关联的客户及签约人KPID
     * 返回客户类型为单店的客户信息
     * 过滤掉，客户绑定了多个门店的情况
     * @param wmPoiId
     * @return 返回信息中有可能不存在kp信息
     * @throws TException
     * @throws WmCustomerException
     */
    public WmCustomerKpBusinessBO getSignerAuth(Long wmPoiId) throws TException, WmCustomerException {
        //查询门店关联客户
        LOGGER.info("通过wmPoi:{}查询关联客户和KP信息", wmPoiId);
        WmCustomerKpBusinessBO businessBO = new WmCustomerKpBusinessBO();
        if(wmPoiId == null || wmPoiId < 1){
            return businessBO;
        }
        businessBO.setWmPoiId(wmPoiId.intValue());
        WmPoiDomain poiDomain = wmPoiClient.getWmPoiById(wmPoiId);
        if(poiDomain == null || poiDomain.getCityId() < 1){
            return businessBO;
        }

        //是否启用此功能
        if(!MccConfig.getGrayPoiCitySwitch()){
            return businessBO;
        }
        //不在灰度范围
        if(CollectionUtils.isNotEmpty(MccConfig.getGrayPoiCityList())
                && !MccConfig.getGrayPoiCityList().contains(poiDomain.getCityId())){
            LOGGER.info("不符合灰度逻辑, 不显示页签, wmPoiId:{} ", wmPoiId);
            businessBO.setAuth(false);
            return businessBO;
        }

//        //灰度门店城市
//        if(MccConfig.getGrayPoiCitySwitch() &&
//                (CollectionUtils.isEmpty(MccConfig.getGrayPoiCityList())
//                        || !MccConfig.getGrayPoiCityList().contains(poiDomain.getCityId()))){
//            return businessBO;
//        }

        Integer customerId = wmCustomerService.selectWmCustomerIdByWmPoiId(wmPoiId);
        if(customerId == null || customerId < 1){
            return businessBO;
        }
        WmCustomerDB wmCustomerDB = wmCustomerService.selectCustomerById(customerId);
        if(CustomerRealTypeEnum.DANDIAN.getValue() != wmCustomerDB.getCustomerRealType()){
            return businessBO;
        }
        Integer countCustomerPoi;
        if (customerId % 100 < MccCustomerConfig.bigCustomerQueryPercent()) {
            List<Long> wmPoiIds = wmCustomerPoiService.selectWmPoiIdsByCustomerId(customerId);
            countCustomerPoi =  wmPoiIds == null ? 0 :wmPoiIds.size();
        } else {
            countCustomerPoi = wmCustomerPoiDBMapper.countCustomerPoi(customerId);
        }
        if(countCustomerPoi > 1){
            return businessBO;
        }
        businessBO.setCustomerId(customerId);

        //查询客户签约人KPID
        List<WmCustomerKp> wmCustomerKps = wmCustomerKpDBMapper.selectByCustomerIdFromSlave(customerId, KpTypeEnum.SIGNER.getType());
        if(CollectionUtils.isEmpty(wmCustomerKps)){
            return businessBO;
        }
        WmCustomerKp wmCustomerKp = wmCustomerKps.get(0);
        if (wmCustomerKp != null && wmCustomerKp.getEffective() == EffectiveStatusEnum.EFFECTIVE.getType()) {
            businessBO.setKpId(wmCustomerKp.getId());
            WmCustomerKp legalKp = getKpLegalByCustomerId(customerId);
            if (legalKp != null) {
                businessBO.setLegalKpId(legalKp.getId());
            }
        }

        LOGGER.info("通过wmPoi:{}查询关联客户和KP信息完成, customerId:{}, kpId:{}", wmPoiId, customerId, wmCustomerKp.getId());
        return businessBO;
    }

    /**
     * 客户生效-如果有KP法人数据是已录入，则记录生效流程
     *
     * @param wmCustomerDB
     */
    public WmCustomerKp customerEffectiveDrivenKpLegalFlow(WmCustomerDB wmCustomerDB) {
        log.info("customerEffectiveDrivenKpLegalFlow，客户生效开始处理KP法人的流程，wmCustomerDB={}", JSON.toJSONString(wmCustomerDB));
        try {
            WmCustomerKp legalKp = getKpLegalByCustomerId(wmCustomerDB.getId());
            if (legalKp == null || legalKp.getState() != KpLegalStateMachine.RECORDED.getState()) {
                log.info("customerEffectiveDrivenKpLegalFlow,未查询到KP法人数据或KP法人数据不是已录入状态，不需要处理");
                return null;
            }
            //已录入状态可以记录走实名流程
            legalKp = legalDBOperator.recordLegalKpAutoFlow(legalKp, wmCustomerDB);
            return legalKp;
        } catch (Exception e) {
            log.info("customerEffectiveDrivenKpLegalFlow,客户生效走KP法人的流程发生异常,wmCustomerDB={}", JSON.toJSONString(wmCustomerDB), e);
        }
        return null;
    }

    /**
     * 客户状态是生效，签约人流程推动
     *
     * @param after
     */
    @RepeatSubmission(seedExp = "after.id+'_effect_kp'")
    public void customerEffectDrivenKpSigner(WmCustomerDB after) {
        // 客户生效，法定代表人变更之后需要进行法人信息删除
        customerEffectForLegalKpOperate(after, null, WmCustomerConstant.CUSTOMER_SYSTEM_UPDATE);

        //KP法人如果存在已录入数据则需要继续走流程
        WmCustomerKp legalKp = customerEffectiveDrivenKpLegalFlow(after);

        /** 获取kp签约人信息 **/
        WmCustomerKp wmCustomerKp = getCustomerSignerKp(after.getId());
        if (wmCustomerKp == null) {
            return;
        }
        if (wmCustomerKp.getState() != KpSignerStateMachine.RECORDED.getState()) {
            return;
        }
        //代理人授权&&纸面授权&&代理人授权书为空或少于2个
        if (wmCustomerKp.getSignerType() == AGENT.getType()
                && wmCustomerKp.getLegalAuthType() != null
                && wmCustomerKp.getLegalAuthType() == LegalAuthTypeEnum.PAPER_AUTH.getCode()
                && StringUtils.isBlank(wmCustomerKp.getAgentAuth())
                && wmCustomerKp.getOperateSource() != null
                && wmCustomerKp.getOperateSource() == CustomerDeviceType.MERCHANT_SYS.getCode()) {
            log.info("customerEffectDrivenKpSigner,签约人签约方式为代理人纸面授权，操作来源是电销系统且只授权书信息为空，则不继续走流程,customerId={},kpId={}", after.getId(), wmCustomerKp.getId());
            return;
        }

        //如果是签约代理人+短信授权则需要先走KP法人流程
        if (wmCustomerKp.getSignerType() == AGENT.getType()
                && wmCustomerKp.getLegalAuthType() != null
                && wmCustomerKp.getLegalAuthType() == LegalAuthTypeEnum.MESSAGE_AUTH.getCode()) {
            if (legalKp == null || legalKp.getState() != KpLegalStateMachine.EFFECT.getState()) {
                log.info("customerEffectDrivenKpSigner,KP类型法人数据非生效状态，不需要继续走签约人KP流程,after={},legalKp={}", JSON.toJSONString(after), JSON.toJSONString(legalKp));
                return;
            }
        }

        /** KP签约人信息校验 **/
        try {
            LOGGER.info("customerEffectDrivenKpSigner,after={}", JSONObject.toJSONString(after));
            signerCertTypeVerify.verify(after, null, wmCustomerKp, null, null, 0, "系统同步");
        } catch (WmCustomerException e) {
            LOGGER.warn("signerCertTypeVerify.verify不通过,wmCustomerKp={}", JSONObject.toJSONString(wmCustomerKp), e);
            deleteKpSignerForSignerCorrect(wmCustomerKp.getId(), wmCustomerKp);
            sendDaxiangService.kpSingerDeleteForSignerCorrect(after);
            return;
        }
        /** KP签约人原流程推动 **/
        try {
            signerDBOperator.recordedSignerDriven(after, wmCustomerKp, false);
        } catch (WmCustomerException e) {
            LOGGER.warn("signerDBOperator.recordedSignerDriven失败,wmCustomerKp={}", JSONObject.toJSONString(wmCustomerKp), e);
        } catch (WmServerException e) {
            LOGGER.error("signerDBOperator.recordedSignerDriven失败,wmCustomerKp={}", JSONObject.toJSONString(wmCustomerKp), e);
        } catch (Exception e) {
            LOGGER.error("signerDBOperator.recordedSignerDriven失败,wmCustomerKp={}", JSONObject.toJSONString(wmCustomerKp), e);
        }
    }


    /**
     * 客户信息法人或个人变更，则签约人KP信息自动删除，需要补录
     *
     * @param customerName
     * @param legalPerson
     * @param after
     */
    public void customerEffectForKpOperate(String customerName, String legalPerson, WmCustomerDB after) {
        LOGGER.info("customerEffectForKpOperate customerName={},legalPerson={},after={}", customerName, legalPerson, JSONObject.toJSONString(after));
        // 修改客户信息保存时如果【法定代表人/经营者】字段有变更：（客户生效状态下）
        if (after.getCustomerType() == null || after.getId() == null) {
            return;
        }
        /* 客户资质法人变更-删除法人KP */
        customerEffectForLegalKpOperate(after, legalPerson, WmCustomerConstant.CUSTOMER_SYSTEM_UPDATE);

        /** 获取签约人信息 **/
        WmCustomerKp wmCustomerKp = getCustomerSignerKp(after.getId());
        if (wmCustomerKp == null) {
            return;
        }

        if (!((wmCustomerKp.getVersion() != null && wmCustomerKp.getVersion() == KpVersionEnum.V2.getCode()) || wmCustomerGrayService.isNewForCustomerKp())) {
            return;
        }

        /** 判断签约人是否是有法人的客户资质主体 **/
        boolean hasLegalPersonCustomerType = after.getCustomerType().intValue() == CustomerType.CUSTOMER_TYPE_BUSINESS.getCode() ||
                after.getCustomerType().intValue() == CustomerType.CUSTOMER_TYPE_BUSINESS_ABROAD.getCode();
        if (hasLegalPersonCustomerType && StringUtils.isNotBlank(legalPerson) && StringUtils.isNotBlank(after.getLegalPerson())
                && !legalPerson.equals(after.getLegalPerson())) {
            /** 判断签约人是否为法人/个人 **/
            if (wmCustomerKp.getSignerType() != KpSignerTypeEnum.LEGAL.getType() && wmCustomerKp.getSignerType() != KpSignerTypeEnum.SIGNER.getType()) {
                return;
            }

            /** 删除签约人信息,记录操作日志,删除原签约任务进行中任务 **/
            deleteKpSignerForCustomerUpdate(wmCustomerKp.getId(), wmCustomerKp, CustomerType.CUSTOMER_TYPE_BUSINESS);
            /** 发送大象通知 **/
            sendDaxiangService.kpSingerDeleteForCustomerUpdate(legalPerson, after);
            /** 签约人删除后掉客户企业四要素标签 **/
            wmCustomerKpRealAuthService.deleteFourEleTag(after.getMtCustomerId());

        } else if (after.getCustomerType().intValue() == CustomerType.CUSTOMER_TYPE_IDCARD.getCode() && StringUtils.isNotBlank(customerName) && StringUtils.isNotBlank(after.getCustomerName())
                && !customerName.equals(after.getCustomerName())) {
            /** 删除签约人信息,记录操作日志,删除原签约任务进行中任务 **/
            deleteKpSignerForCustomerUpdate(wmCustomerKp.getId(), wmCustomerKp, CustomerType.CUSTOMER_TYPE_IDCARD);
            /** 签约人删除后掉客户企业四要素标签 **/
            wmCustomerKpRealAuthService.deleteFourEleTag(after.getMtCustomerId());
        }
    }

    public void customerEffectForLegalKpOperate(WmCustomerDB after, String beforLegalPerson, int sourceType) {
        log.info("客户生效，判断法人KP是否需要删除 customer:{}", JSONObject.toJSONString(after));
        List<WmCustomerKp> kpList = getCustomerKpList(after.getId());
        for (WmCustomerKp kp : kpList) {
            if (kp.getKpType() == KpTypeEnum.LEGAL.getType()) {
                wmCustomerLegalKpService.customerEffectForLegalKpOperate(after, kp, beforLegalPerson,sourceType);
            }
        }
    }

    /**
     * 删除签约人信息-客户法人/姓名变更
     *
     * @param kpId
     * @param wmCustomerKp
     */
    public void deleteKpSignerForCustomerUpdate(int kpId, WmCustomerKp wmCustomerKp, CustomerType customerType) {
        LOGGER.info("deleteKpSignerForCustomerUpdate kpId={},wmCustomerKp={},customerType={}", kpId, JSON.toJSONString(wmCustomerKp), customerType);

        List<WmCustomerKpAudit> wmCustomerKpAuditList = wmCustomerKpAuditMapper.selectByKpId(kpId);

        if (CollectionUtils.isNotEmpty(wmCustomerKpAuditList)) {
            // 取消签约授权中任务
            Set<Integer> taskIdSet = wmCustomerKpAuditList.stream().map(WmCustomerKpAudit::getCommitId).collect(Collectors.toSet());
            taskIdSet.stream().forEach(taskId -> {
                if (taskId == null || taskId <= 0) {
                    return;
                }
                try {
                    wmEcontractSignBzService.cancelSign(taskId.longValue());
                } catch (Exception e) {
                    LOGGER.error("删除签约人KP, 取消签约授权中任务异常, taskId = {}", taskId, e);
                }
            });
            // 删除审核表数据
            wmCustomerKpAuditMapper.deleteByKpId(kpId);
        }

        List<Integer> kpIdList = Lists.newArrayList(kpId);

        WmCustomerKpTemp wmCustomerKpTemp = wmCustomerKpTempDBMapper.selectByKpId(kpId);
        if (wmCustomerKpTemp != null) {
            wmCustomerSensitiveWordsService.readKpWhenSelect(wmCustomerKpTemp);

            // 删除临时表数据
            wmCustomerKpTempDBMapper.deleteByKpIdList(kpIdList);
            wmCustomerKpPoiService.deleteTempByKpIdList(kpIdList);
            String bankInfo = "";
            if (customerType == CustomerType.CUSTOMER_TYPE_BUSINESS) {
                bankInfo = String.format("、银行名称: %s、银行卡号: %s", wmCustomerKpTemp.getBankName(), wmCustomerKpTemp.getCreditCard());
            }
            WmCustomerOplogBo wmCustomerOplogBo = new WmCustomerOplogBo(wmCustomerKp.getCustomerId(), WmCustomerOplogBo.OpModuleType.KP, WmCustomerOplogBo.OpType.DELETE, kpId, 0, "系统");
            wmCustomerOplogBo.setLog(String.format("===KP类型:%s=== \n客户信息法定代表人变更，删除原签约人流程中信息。\n姓名: %s、签约类型：%s、证件类型: %s、证件编号: %s、手机号: %s%s。",
                    KpTypeEnum.getByType(wmCustomerKpTemp.getKpType()).getName(), wmCustomerKpTemp.getCompellation(), KpSignerTypeEnum.getByType(wmCustomerKpTemp.getSignerType()).getName(),
                    CertTypeEnum.getByType(wmCustomerKpTemp.getCertType()).getName(), wmCustomerKpTemp.getCertNumber(), wmCustomerKpTemp.getPhoneNum(), bankInfo));
            try {
                wmCustomerOplogService.insert(wmCustomerOplogBo);
            } catch (WmCustomerException e) {
                LOGGER.warn("deleteKpSigner 记录操作日志失败 wmCustomerOplogBo={}", JSONObject.toJSONString(wmCustomerOplogBo));
            }
        }

        // 删除正式表数据
        wmCustomerKpDBMapper.deleteByIdList(kpIdList);
        wmCustomerKpPoiService.deleteByKpIdList(kpIdList);

        String bankInfo = "";
        if (customerType == CustomerType.CUSTOMER_TYPE_BUSINESS) {
            bankInfo = String.format("、银行名称: %s、银行卡号: %s", wmCustomerKp.getBankName(), wmCustomerKp.getCreditCard());
        }

        WmCustomerOplogBo wmCustomerOplogBo = new WmCustomerOplogBo(wmCustomerKp.getCustomerId(), WmCustomerOplogBo.OpModuleType.KP, WmCustomerOplogBo.OpType.DELETE, kpId, 0, "系统");
        wmCustomerOplogBo.setLog(String.format("===KP类型:%s=== \n客户信息法定代表人变更，删除原签约人信息。\n姓名: %s、签约类型：%s、证件类型: %s、证件编号: %s、手机号: %s%s。",
                KpTypeEnum.getByType(wmCustomerKp.getKpType()).getName(), wmCustomerKp.getCompellation(), KpSignerTypeEnum.getByType(wmCustomerKp.getSignerType()).getName(),
                CertTypeEnum.getByType(wmCustomerKp.getCertType()).getName(), wmCustomerKp.getCertNumber(), wmCustomerKp.getPhoneNum(), bankInfo));
        try {
            wmCustomerOplogService.insert(wmCustomerOplogBo);
        } catch (WmCustomerException e) {
            LOGGER.warn("deleteKpSigner 记录操作日志失败 wmCustomerOplogBo={}", JSONObject.toJSONString(wmCustomerOplogBo));
        }
    }

    /**
     * 删除签约人信息
     *
     * @param scene
     * @param wmCustomerKp
     * @param customerType
     * @param opUid
     * @param opName
     */
    public void deleteKpSignerForCustomerUpdate(DeleteSignerSceneEnum scene, WmCustomerKp wmCustomerKp, CustomerType customerType, int opUid, String opName) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.kp.WmCustomerKpService.deleteKpSignerForCustomerUpdate(DeleteSignerSceneEnum,WmCustomerKp,CustomerType,int,String)");
        LOGGER.info("deleteKpSignerForCustomerUpdate scene={},wmCustomerKp={},customerType={}", JSON.toJSONString(scene), JSON.toJSONString(wmCustomerKp), customerType);
        if (scene == null || wmCustomerKp == null) {
            return;
        }
        List<WmCustomerKpAudit> wmCustomerKpAuditList = wmCustomerKpAuditMapper.selectByKpId(wmCustomerKp.getId());
        if (CollectionUtils.isNotEmpty(wmCustomerKpAuditList)) {
            // 取消签约授权中任务
            Set<Integer> taskIdSet = wmCustomerKpAuditList.stream().map(WmCustomerKpAudit::getCommitId).collect(Collectors.toSet());
            taskIdSet.stream().forEach(taskId -> {
                if (taskId == null || taskId <= 0) {
                    return;
                }
                try {
                    wmEcontractSignBzService.cancelSign(taskId.longValue());
                } catch (Exception e) {
                    LOGGER.error("删除签约人KP, 取消签约授权中任务异常, taskId = {}", taskId, e);
                }
            });
            // 删除审核表数据
            wmCustomerKpAuditMapper.deleteByKpId(wmCustomerKp.getId());
        }

        List<Integer> kpIdList = Lists.newArrayList(wmCustomerKp.getId());

        WmCustomerKpTemp wmCustomerKpTemp = wmCustomerKpTempDBMapper.selectByKpId(wmCustomerKp.getId());
        if (wmCustomerKpTemp != null) {
            wmCustomerSensitiveWordsService.readKpWhenSelect(wmCustomerKpTemp);

            // 删除临时表数据
            wmCustomerKpTempDBMapper.deleteByKpIdList(kpIdList);
            wmCustomerKpPoiService.deleteTempByKpIdList(kpIdList);
            String bankInfo = "";
            if (customerType == CustomerType.CUSTOMER_TYPE_BUSINESS) {
                bankInfo = String.format("、银行名称: %s、银行卡号: %s", wmCustomerKpTemp.getBankName(), wmCustomerKpTemp.getCreditCard());
            }
            WmCustomerOplogBo wmCustomerOplogBo = new WmCustomerOplogBo(wmCustomerKp.getCustomerId(), WmCustomerOplogBo.OpModuleType.KP, WmCustomerOplogBo.OpType.DELETE, wmCustomerKp.getId(), opUid, opName);
            try {
                wmCustomerOplogBo.setLog(String.format("===KP类型:%s=== \n%s，删除原签约人流程中信息。\n[当前状态:%s], 姓名: %s、签约类型：%s、证件类型: %s、证件编号: %s、手机号: %s%s。",
                        KpTypeEnum.getByType(wmCustomerKpTemp.getKpType()).getName(),
                        scene.getDesc(),
                        KpSignerStateMachine.getByState(wmCustomerKpTemp.getState()).getDes(),
                        wmCustomerKpTemp.getCompellation(),
                        KpSignerTypeEnum.getByType(wmCustomerKpTemp.getSignerType()).getName(),
                        CertTypeEnum.getByType(wmCustomerKpTemp.getCertType()).getName(),
                        wmCustomerKpTemp.getCertNumber(),
                        wmCustomerKpTemp.getPhoneNum(),
                        bankInfo));
                wmCustomerOplogService.insert(wmCustomerOplogBo);
            } catch (WmCustomerException e) {
                LOGGER.warn("deleteKpSignerForCustomerUpdate 记录操作日志失败 wmCustomerOplogBo={}", JSONObject.toJSONString(wmCustomerOplogBo));
            }
        }

        // 删除正式表数据
        wmCustomerKpDBMapper.deleteByIdList(kpIdList);
        wmCustomerKpPoiService.deleteByKpIdList(kpIdList);

        String bankInfo = "";
        if (customerType == CustomerType.CUSTOMER_TYPE_BUSINESS) {
            bankInfo = String.format("、银行名称: %s、银行卡号: %s", wmCustomerKp.getBankName(), wmCustomerKp.getCreditCard());
        }
        WmCustomerOplogBo wmCustomerOplogBo = new WmCustomerOplogBo(wmCustomerKp.getCustomerId(), WmCustomerOplogBo.OpModuleType.KP, WmCustomerOplogBo.OpType.DELETE, wmCustomerKp.getId(), opUid, opName);
        try {
            wmCustomerOplogBo.setLog(String.format("===KP类型:%s=== \n%s，删除原签约人信息。\n[当前状态:%s], 姓名: %s、签约类型：%s、证件类型: %s、证件编号: %s、手机号: %s%s。",
                    KpTypeEnum.getByType(wmCustomerKp.getKpType()).getName(),
                    scene.getDesc(),
                    KpSignerStateMachine.getByState(wmCustomerKp.getState()).getDes(),
                    wmCustomerKp.getCompellation(),
                    KpSignerTypeEnum.getByType(wmCustomerKp.getSignerType()).getName(),
                    CertTypeEnum.getByType(wmCustomerKp.getCertType()).getName(),
                    wmCustomerKp.getCertNumber(),
                    wmCustomerKp.getPhoneNum(),
                    bankInfo));
            wmCustomerOplogService.insert(wmCustomerOplogBo);
        } catch (WmCustomerException e) {
            LOGGER.warn("deleteKpSignerForCustomerMultiplexUpdate 记录操作日志失败 wmCustomerOplogBo={}", JSONObject.toJSONString(wmCustomerOplogBo));
        }
    }

    /**
     * 删除签约人信息-客户生效签约人校验不通过
     *
     * @param kpId
     * @param wmCustomerKp
     */
    public void deleteKpSignerForSignerCorrect(int kpId, WmCustomerKp wmCustomerKp) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.service.kp.WmCustomerKpService.deleteKpSignerForSignerCorrect(int,com.sankuai.meituan.waimai.thrift.customer.domain.kp.WmCustomerKp)");
        wmCustomerKpDBMapper.deleteByPrimaryKey(kpId);
        WmCustomerOplogBo wmCustomerOplogBo = new WmCustomerOplogBo(wmCustomerKp.getCustomerId(), WmCustomerOplogBo.OpModuleType.KP, WmCustomerOplogBo.OpType.DELETE, kpId, 0, "系统");
        wmCustomerOplogBo.setLog(String.format("===KP类型:%s=== \n客户信息已生效，已录入的签约人信息不符合要求已删除。\n姓名: %s、签约类型：%s、证件类型: %s、证件编号: %s、手机号: %s。",
                KpTypeEnum.getByType(wmCustomerKp.getKpType()).getName(), wmCustomerKp.getCompellation(), KpSignerTypeEnum.getByType(wmCustomerKp.getSignerType()).getName(),
                CertTypeEnum.getByType(wmCustomerKp.getCertType()).getName(), wmCustomerKp.getCertNumber(), wmCustomerKp.getPhoneNum()));
        try {
            wmCustomerOplogService.insert(wmCustomerOplogBo);
        } catch (WmCustomerException e) {
            LOGGER.warn("deleteKpSigner 记录操作日志失败 wmCustomerOplogBo={}", JSONObject.toJSONString(wmCustomerOplogBo));
        }
    }


    public WmCustomerKpSignerProgressResult getKpSignerProgress(WmCustomerKpSignerProgressParam progressParam) throws TException, WmCustomerException {
        LOGGER.info("getKpSignerProgress progressParam={}", JSONObject.toJSONString(progressParam));
        if (progressParam == null || progressParam.getCustomerId() == null) {
            ThrowUtil.throwClientError("参数错误");
        }
        if (progressParam.getKpId() == null && (progressParam.getSignerType() == null || progressParam.getCertType() == null)) {
            ThrowUtil.throwClientError("参数错误");
        }

        WmCustomerKpSignerProgressResult result = new WmCustomerKpSignerProgressResult();
        result.setProgressList(Lists.newArrayList());
        WmCustomerDB wmCustomer = wmCustomerPlatformDataParseService.selectCustomerFromPlatformById(progressParam.getCustomerId());
        if (wmCustomer == null) {
            return result;
        }

        WmCustomerKp wmCustomerKp = wmCustomerKpDBMapper.selectByPrimaryKey(progressParam.getKpId());
        //不是V2、V3的直接返回
        if (wmCustomerKp != null && (wmCustomerKp.getVersion() == null
                || (wmCustomerKp.getVersion() != KpVersionEnum.V2.getCode() && wmCustomerKp.getVersion() != KpVersionEnum.V3.getCode()))) {
            return result;
        }

        // 当前kp状态
        byte state = getKpState(progressParam.getKpId(), progressParam, wmCustomerKp);
        result.setState(state);

        KpSignerTypeEnum kpSignerTypeEnum = KpSignerTypeEnum.getByType(progressParam.getSignerType());
        CertTypeEnum certTypeEnum = CertTypeEnum.getByType(progressParam.getCertType());
        HaveAgentAuthEnum haveAgentAuthEnum = HaveAgentAuthEnum.of(progressParam.getHaveAgentAuth());

        LegalAuthTypeEnum legalAuthTypeEnum = LegalAuthTypeEnum.UN_SETTING;
        if (progressParam.getLegalAuthType() != null) {
            legalAuthTypeEnum = LegalAuthTypeEnum.getByCode(progressParam.getLegalAuthType());
        } else if (wmCustomerKp != null && wmCustomerKp.getLegalAuthType() != null) {
            legalAuthTypeEnum = LegalAuthTypeEnum.getByCode(wmCustomerKp.getLegalAuthType());
        }

        KpSignerProgressEnum progressEnum = null;
        log.info("getKpSignerProgress,progressParam={}", JSON.toJSONString(progressParam));
        if (!wmCustomerGrayService.hitDxKpSignerFirstAuditSucGray(progressParam.getCustomerId())) {
            progressEnum = KpSignerProgressEnum.getByCondition(kpSignerTypeEnum, certTypeEnum, haveAgentAuthEnum, progressParam.getIsModify(), progressParam.getIsNotCertNumberModify(), legalAuthTypeEnum);
        } else {
            //命中灰度则使用V2状态图
            progressEnum = getKpSignerProgressByConditionV2(progressParam, wmCustomerKp);
        }

        if (progressEnum == null || CollectionUtils.isEmpty(progressEnum.getProgress())) {
            return result;
        }

        KpSignerStateMachine signerStateMachine = KpSignerStateMachine.getByState(state);
        int lastNodeCode = signerStateMachine.getMapSignerNode();
        int lastNodeStatus = signerStateMachine.getMapSignerOperate();

        boolean haveLegalAuth = false;
        int currentNodeOrder = 1;
        // 未生效查主表，已生效查临时表
        List<KpSignerNodeEnum> nodeEnumList = progressEnum.getProgress();
        int progressCount = progressEnum.getProgress().size();
        for (int i = 0; i < progressCount; i++) {
            KpSignerNodeEnum nodeEnum = nodeEnumList.get(i);
            if (nodeEnum.getCode() == lastNodeCode) {
                currentNodeOrder = i + 1;
            }
            //是否有法人授权节点
            if (nodeEnum == KpSignerNodeEnum.LEGAL_AUTH) {
                haveLegalAuth = true;
            }
            WmCustomerKpSignerNodeBo bo = new WmCustomerKpSignerNodeBo();
            bo.setOrder(i + 1);
            bo.setCode(nodeEnum.getCode());
            bo.setDesc(nodeEnum.getDesc());
            result.getProgressList().add(bo);
        }
        result.setCurrentNodeOrder(currentNodeOrder);
        if (lastNodeStatus == KpSignerNodeStatusEnum.FINISH.getCode() && currentNodeOrder < progressCount) {
            lastNodeStatus = KpSignerNodeStatusEnum.WAIT.getCode();
        }
        KpSignerNodeStatusEnum nodeStatusEnum = KpSignerNodeStatusEnum.of(lastNodeStatus);
        if (nodeStatusEnum != null) {
            result.setCurrentNodeStatusName(nodeStatusEnum.getName());
        }
        //有法人授权节点，需要查询KP类型法人数据
        if (haveLegalAuth) {
            WmCustomerKpAudit wmCustomerKpAudit = wmCustomerKpAuditMapper.getLatestLegalAuthTaskByKpId(progressParam.getKpId());
            if (wmCustomerKpAudit != null && StringUtils.isNotBlank(wmCustomerKpAudit.getExtra())) {
                String[] extra = wmCustomerKpAudit.getExtra().split(",");
                log.info("getKpSignerProgress,进度条中的数据从审核记录中读取,extra={}", wmCustomerKpAudit.getExtra());
                result.setLegalKpName(extra[0]);
                result.setLegalKpPhoneNum(extra[1]);
            }
        }
        return result;
    }

    /**
     * 获取法人KP的进度流程
     *
     * @param progressParam
     * @return
     * @throws TException
     * @throws WmCustomerException
     */
    public WmCustomerKpProgressResult getKpLegalProgress(WmCustomerKpProgressReq progressParam) throws TException, WmCustomerException {
        LOGGER.info("getKpSignerProgress progressParam={}", JSONObject.toJSONString(progressParam));
        if (progressParam == null || progressParam.getCustomerId() == null || progressParam.getKpType() == null) {
            ThrowUtil.throwClientError("参数错误");
        }

        WmCustomerKpProgressResult result = new WmCustomerKpProgressResult();
        result.setProgressList(Lists.newArrayList());
        WmCustomerDB wmCustomer = wmCustomerPlatformDataParseService.selectCustomerFromPlatformById(progressParam.getCustomerId());
        if (wmCustomer == null) {
            return result;
        }

        Byte state = null;
        if (progressParam.getKpId() == null) {
            state = KpLegalStateMachine.NO_DATA.getState();
        } else {
            WmCustomerKp wmCustomerKp = wmCustomerKpDBMapper.selectByPrimaryKey(progressParam.getKpId());
            if (wmCustomerKp != null && (wmCustomerKp.getVersion() == null || wmCustomerKp.getVersion() != KpVersionEnum.V3.getCode())) {
                return result;
            }
            // 当前kp状态
            state = wmCustomerKp.getState();
            WmCustomerKpTemp wmCustomerKpTemp = wmCustomerKpTempDBMapper.selectByKpId(wmCustomerKp.getId());
            if (wmCustomerKpTemp != null) {
                state = wmCustomerKpTemp.getState();
            }
        }
        result.setState(state);

        KpLegalStateMachine signerStateMachine = KpLegalStateMachine.getByState(state);
        int lastNodeCode = signerStateMachine.getMapLegalNode();
        int lastNodeStatus = signerStateMachine.getMapLegalOperate();

        int currentNodeOrder = 1;
        List<KpLegalNodeEnum> nodeEnumList = KpLegalProgressEnum.LEGAL_ID_CARD_FLOW.getProgress();
        int progressCount = nodeEnumList.size();
        for (int i = 0; i < progressCount; i++) {
            KpLegalNodeEnum nodeEnum = nodeEnumList.get(i);
            if (nodeEnum.getCode() == lastNodeCode) {
                currentNodeOrder = i + 1;
            }
            WmCustomerKpSignerNodeBo bo = new WmCustomerKpSignerNodeBo();
            bo.setOrder(i + 1);
            bo.setCode(nodeEnum.getCode());
            bo.setDesc(nodeEnum.getDesc());
            result.getProgressList().add(bo);
        }
        result.setCurrentNodeOrder(currentNodeOrder);
        if (lastNodeStatus == KpLegalNodeStatusEnum.FINISH.getCode() && currentNodeOrder < progressCount) {
            lastNodeStatus = KpLegalNodeStatusEnum.WAIT.getCode();
        }
        KpLegalNodeStatusEnum nodeStatusEnum = KpLegalNodeStatusEnum.of(lastNodeStatus);
        if (nodeStatusEnum != null) {
            result.setCurrentNodeStatusName(nodeStatusEnum.getName());
        }
        return result;
    }

    private boolean checkIsAdd(Integer kpId, WmCustomerKp wmCustomerKp) {
        if (kpId == null) {
            return true;
        }
        if (wmCustomerKp == null) {
            return true;
        }
        return false;
    }

    private void buildParam(WmCustomerKpSignerProgressParam progressParam, Byte signerType, Byte certType, Integer haveAgentAuth) {
        // 编辑查看页面页面未改动，要展示进行中的流程
        if (progressParam.getSignerType() == null && signerType != null) {
            progressParam.setSignerType(signerType);
        }
        if (progressParam.getCertType() == null && certType != null) {
            progressParam.setCertType(certType);
        }
        if (progressParam.getHaveAgentAuth() == null && haveAgentAuth != null) {
            progressParam.setHaveAgentAuth(haveAgentAuth);
        }
    }

    private byte getKpState(Integer kpId, WmCustomerKpSignerProgressParam progressParam, WmCustomerKp wmCustomerKp) {
        byte state = KpSignerStateMachine.NO_DATA.getState();

        if (checkIsAdd(kpId, wmCustomerKp)) {
            //新增场景，授权方式为短信授权&证件类型非身份证,需要设置证件号变更
            if (progressParam.getLegalAuthType() != null
                    && progressParam.getLegalAuthType() == LegalAuthTypeEnum.MESSAGE_AUTH.getCode()
                    && !ID_CERT_TYPE.contains(progressParam.getCertType())) {
                progressParam.setIsNotCertNumberModify(false);
            }
            return state;
        }

        state = wmCustomerKp.getState();
        Byte signerType = wmCustomerKp.getSignerType();
        Byte certType = wmCustomerKp.getCertType();
        Integer haveAgentAuth = null;
        wmCustomerSensitiveWordsService.readKpWhenSelect(wmCustomerKp);
        String certNumber = wmCustomerKp.getCertNumber();
        if (wmCustomerKp.getHaveAgentAuth() != null && wmCustomerKp.getHaveAgentAuth().intValue() != HaveAgentAuthEnum.NOT_SETTING.getCode()) {
            haveAgentAuth = wmCustomerKp.getHaveAgentAuth();
        }


        // 有临时表信息，说明在流程处理中
        if (state == KpSignerStateMachine.EFFECT.getState()) {
            WmCustomerKpTemp wmCustomerKpTemp = wmCustomerKpTempDBMapper.selectByKpId(kpId);
            if (wmCustomerKpTemp != null) {
                state = wmCustomerKpTemp.getState();
                signerType = wmCustomerKpTemp.getSignerType();
                certType = wmCustomerKpTemp.getCertType();
                if (wmCustomerKpTemp.getHaveAgentAuth() != null && wmCustomerKpTemp.getHaveAgentAuth().intValue() != HaveAgentAuthEnum.NOT_SETTING.getCode()) {
                    haveAgentAuth = wmCustomerKpTemp.getHaveAgentAuth();
                }
                //重置法人授权方式
                progressParam.setLegalAuthType(wmCustomerKpTemp.getLegalAuthType());
            }
        }

        // 编辑查看页面页面未改动，要展示进行中的流程
        buildParam(progressParam, signerType, certType, haveAgentAuth);

        // 页面或者流程中
        boolean isfromUnAgentToAgent = wmCustomerKpAuditService.isFromUnAgentToAgent(signerType, progressParam.getSignerType());
        boolean isfromAgentToAgent = wmCustomerKpAuditService.isFromAgentToAgent(signerType, progressParam.getSignerType());

        // 代理有无授权书调整
        boolean isNotModifyhaveAgentAuth = true;
        if (isfromAgentToAgent) {
            isNotModifyhaveAgentAuth = (HaveAgentAuthEnum.of(haveAgentAuth) == HaveAgentAuthEnum.of(progressParam.getHaveAgentAuth()));
        }

        boolean updateCurrent = false;
        boolean updateCertNumberCurrent = false;

        // 判断页面kp类型、证件类型、代理人授权书是否有调整，没有调整展示原流程，否则要重新发起流程
        if (!(signerType.equals(progressParam.getSignerType()) && certType.equals(progressParam.getCertType()) && isNotModifyhaveAgentAuth)) {
            state = KpSignerStateMachine.NO_DATA.getState();
            updateCurrent = true;
            updateCertNumberCurrent = StringUtils.isNotBlank(progressParam.getCertNumber()) && !progressParam.getCertNumber().equals(certNumber);
        }

        if (progressParam.getSignerType() != AGENT.getType()) {
            return state;
        }


        // 页面修改，以页面的值为准
        boolean currentUpdateAgentModify = updateCurrent && (isfromUnAgentToAgent || (isfromAgentToAgent && updateCertNumberCurrent));
        // 页面未修改，以数据库的值为准
        boolean currentNotUpdateAgentModify = !updateCurrent && (FromUnagentToAgentEnum.of(wmCustomerKp.getFromUnagentAgent()) == FromUnagentToAgentEnum.YES ||
                (isfromAgentToAgent && CertNumberModifyEnum.of(wmCustomerKp.getCertNumberModify()) == CertNumberModifyEnum.YES));

        boolean isModifyProcess = currentUpdateAgentModify || currentNotUpdateAgentModify;

        // 如果KP的签约人由非代理人变更为代理人/或代理人的身份证号发生变更，则进入代理人修改流程
        boolean isModify = wmCustomerKp.getState() == KpSignerStateMachine.EFFECT.getState() && isModifyProcess;
        // 走代理人修改流程
        progressParam.setIsModify(isModify);


        // 页面修改，以页面的值为准
        boolean currentUpdateCertNumberNotModify = updateCurrent && !updateCertNumberCurrent;

        // 页面未修改，以数据库的值为准
        boolean currentNotUpdateCertNumberNotModify = !updateCurrent && CertNumberModifyEnum.of(wmCustomerKp.getCertNumberModify()) == CertNumberModifyEnum.NO;

        boolean isNotCertNumberModifyProcess = currentUpdateCertNumberNotModify || currentNotUpdateCertNumberNotModify;

        // 代理人切代理人，且资质编号未修改
        boolean isNotCertNumberModify = isfromAgentToAgent && isNotCertNumberModifyProcess && wmCustomerKp.getState() == KpSignerStateMachine.EFFECT.getState();
        // 走代理人切代理人，资质编号未修改流程
        progressParam.setIsNotCertNumberModify(isNotCertNumberModify);
        return state;
    }

    public Map<Long, Pair<Integer, String>> kpInfoHandler(List<WmCustomerKpPoi> wmCustomerKpPoiList) {
        Map<Long, Pair<Integer, String>> poiInfoMap = new HashMap<>();
        if (CollectionUtils.isEmpty(wmCustomerKpPoiList)) {
            return poiInfoMap;
        }
        List<Integer> kpIdList = Lists.newArrayList();
        for (WmCustomerKpPoi wmCustomerKpPoi : wmCustomerKpPoiList) {
            kpIdList.add(wmCustomerKpPoi.getKpId());
        }
        Map<Integer, WmCustomerKp> kpInfoMap = batchGetCustomerKpInfo(kpIdList);
        for (WmCustomerKpPoi wmCustomerKpPoi : wmCustomerKpPoiList) {
            WmCustomerKp wmCustomerKp = kpInfoMap.get(wmCustomerKpPoi.getKpId());
            if (null != kpInfoMap.get(wmCustomerKpPoi.getKpId())) {
                Pair<Integer, String> pair = Pair.of(wmCustomerKp.getId(), wmCustomerKp.getCompellation());
                poiInfoMap.put(wmCustomerKpPoi.getWmPoiId(), pair);
            }
        }
        return poiInfoMap;
    }

    /**
     * 历史上是否存在生效过的签约人信息
     *
     * @param customerId
     * @return
     * @throws WmCustomerException
     */
    public boolean existEffectiveSignerKpIncludeHistory(Integer customerId) throws WmCustomerException {
        if (customerId == null || customerId <= 0) {
            return false;
        }
        WmCustomerKpSearchCondition condition = new WmCustomerKpSearchCondition();
        condition.setCustomerId(customerId);
        condition.setState(KpSignerStateMachine.EFFECT.getState());
        condition.setKpType((int) KpTypeEnum.SIGNER.getType());
        condition.setLimit(1);
        List<WmCustomerKp> list = wmCustomerKpDBMapper.selectByCondition(condition);
        if (CollectionUtils.isNotEmpty(list)) {
            return true;
        }
        return false;
    }

    private WmCustomerKp transformWmCustomerKpTemp(WmCustomerKpTemp kpTemp) {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.service.kp.WmCustomerKpService.transformWmCustomerKpTemp(com.sankuai.meituan.waimai.thrift.customer.domain.kp.WmCustomerKpTemp)");
        WmCustomerKp kp = new WmCustomerKp();
        BeanUtils.copyProperties(kpTemp, kp);
        kp.setId(kpTemp.getKpId());
        kp.setEffective(EFFECTIVE);
        return kp;
    }

    /**
     * 生效KP临时表中的数据
     * @param kpTempDB
     * @param oldCustomerKp
     */
    public void tempKpEffect(WmCustomerKpTemp kpTempDB, WmCustomerKp oldCustomerKp) {
        oldCustomerKp.setSignerType(kpTempDB.getSignerType());
        oldCustomerKp.setCompellation(kpTempDB.getCompellation());
        oldCustomerKp.setCertType(kpTempDB.getCertType());
        oldCustomerKp.setCertNumber(kpTempDB.getCertNumber());
        oldCustomerKp.setPhoneNum(kpTempDB.getPhoneNum());
        oldCustomerKp.setBankId(kpTempDB.getBankId());
        oldCustomerKp.setBankName(kpTempDB.getBankName());
        oldCustomerKp.setCreditCard(kpTempDB.getCreditCard());
        oldCustomerKp.setEmail(kpTempDB.getEmail());
        /**
         * 图片单独处理，存在清空情况
         */
        oldCustomerKp.setSpecialAttachment(StringUtils.isNotBlank(kpTempDB.getSpecialAttachment())? kpTempDB.getSpecialAttachment(): "");
        oldCustomerKp.setAgentFrontIdcard(StringUtils.isNotBlank(kpTempDB.getAgentFrontIdcard())? kpTempDB.getAgentFrontIdcard(): "");
        oldCustomerKp.setAgentBackIdcard(StringUtils.isNotBlank(kpTempDB.getAgentBackIdcard())? kpTempDB.getAgentBackIdcard(): "");
        oldCustomerKp.setAgentAuth(StringUtils.isNotBlank(kpTempDB.getAgentAuth())? kpTempDB.getAgentAuth(): "");
        oldCustomerKp.setLegalIdcardCopy(StringUtils.isNotBlank(kpTempDB.getLegalIdcardCopy())? kpTempDB.getLegalIdcardCopy(): "");

        oldCustomerKp.setSignTaskType(kpTempDB.getSignTaskType());
        oldCustomerKp.setRemark(kpTempDB.getRemark());
        oldCustomerKp.setPhoneNumEncryption(kpTempDB.getPhoneNumEncryption());
        oldCustomerKp.setPhoneNumToken(kpTempDB.getPhoneNumToken());
        oldCustomerKp.setCertNumberEncryption(kpTempDB.getCertNumberEncryption());
        oldCustomerKp.setCertNumberToken(kpTempDB.getCertNumberToken());
        oldCustomerKp.setCreditCardEncryption(kpTempDB.getCreditCardEncryption());
        oldCustomerKp.setCreditCardToken(kpTempDB.getCreditCardToken());
        oldCustomerKp.setHaveAgentAuth(kpTempDB.getHaveAgentAuth());
        // 【法人授权类型】兜底设置
        // 签约人KP变更信息为签约人KP && 签约类型为代理人；如果不满足，应该清空法人授权类型字段
        if (kpTempDB.getKpType() == KpTypeEnum.SIGNER.getType() && kpTempDB.getSignerType() == AGENT.getType()) {
            // 变更信息未设置法人授权类型，默认纸质授权；否则更新为变更信息的法人授权类型
            if (kpTempDB.getLegalAuthType() == null || kpTempDB.getLegalAuthType().equals(LegalAuthTypeEnum.UN_SETTING.getCode())) {
                oldCustomerKp.setLegalAuthType(LegalAuthTypeEnum.PAPER_AUTH.getCode());
            } else {
                oldCustomerKp.setLegalAuthType(kpTempDB.getLegalAuthType());
            }
        } else {
            oldCustomerKp.setLegalAuthType(LegalAuthTypeEnum.UN_SETTING.getCode());
        }
        wmCustomerSensitiveWordsService.writeKpWhenInsertOrUpdateNoEx(oldCustomerKp);
        wmCustomerKpDBMapper.updateByPrimaryKeySelective(oldCustomerKp);
    }

    /**
     * 查询法人授权信息以及权限信息
     *
     * @param kpLegalAuthRequest
     * @return
     */
    public CustomerKpLegalAuthBo getCustomerKpLegalAuthInfo(CustomerKpLegalAuthRequest kpLegalAuthRequest) throws TException, WmCustomerException {
        LOGGER.info("getCustomerKpLegalAuthInfo,kpLegalAuthRequest={}", JSON.toJSONString(kpLegalAuthRequest));
        CustomerKpLegalAuthBo customerKpLegalAuthBo = new CustomerKpLegalAuthBo();
        customerKpLegalAuthBo.setHaveValidSpecialLegalTask(false);
        customerKpLegalAuthBo.setSpecialLegalAuthTaskId(null);
        customerKpLegalAuthBo.setDefaultLegalAuthType(LegalAuthTypeEnum.MESSAGE_AUTH.getCode());
        customerKpLegalAuthBo.setShowLegalAuthTypeFlag(false);
        customerKpLegalAuthBo.setHaveLegalPaperAuth(false);

        // 判断类型，调用不同KP类型处理器
        WmCustomerDB wmCustomer = wmCustomerPlatformDataParseService.selectCustomerFromPlatformById(kpLegalAuthRequest.getCustomerId());
        if (kpLegalAuthRequest.getKpId() != null && kpLegalAuthRequest.getKpId() > 0) {
            WmCustomerKp customerKp = wmCustomerKpDBMapper.selectByPrimaryKey(kpLegalAuthRequest.getKpId());
        }

        //营业执照或海外营业执照查询特批任务
        if (wmCustomer.getCustomerType() == CustomerType.CUSTOMER_TYPE_BUSINESS.getCode()
                || wmCustomer.getCustomerType() == CustomerType.CUSTOMER_TYPE_BUSINESS_ABROAD.getCode()) {
            log.info("getCustomerKpLegalAuthInfo,开始查询特批法人授权的任务是否存在,wmCustomer={}", JSON.toJSONString(wmCustomer));
            Long specialTaskId = wmAuditServiceAdaptor.getLegalAuthSpecialTask(wmCustomer.getCustomerNumber());
            if (specialTaskId != null && specialTaskId > 0) {
                customerKpLegalAuthBo.setHaveValidSpecialLegalTask(true);
                customerKpLegalAuthBo.setSpecialLegalAuthTaskId(specialTaskId.intValue());
                customerKpLegalAuthBo.setDefaultLegalAuthType(LegalAuthTypeEnum.PAPER_AUTH.getCode());
                customerKpLegalAuthBo.setHaveLegalPaperAuth(true);
            }
        }

        if (kpLegalAuthRequest.getOpSource() == CustomerDeviceType.PC.getCode()
                || kpLegalAuthRequest.getOpSource() == CustomerDeviceType.APP.getCode()) {
            //查询是否有纸面授权权限
            boolean hasAuth = upmAuthCheckService.hasRolePermission(kpLegalAuthRequest.getOpUId(), CustomerRoleTypeEnum.SIGNER_AGENT_KP_PAPER_AUTH_ROLE.getCode());
            //有权限或者有特批任务，默认纸面授权
            if (hasAuth && !customerKpLegalAuthBo.getHaveValidSpecialLegalTask()) {
                customerKpLegalAuthBo.setDefaultLegalAuthType(LegalAuthTypeEnum.PAPER_AUTH.getCode());
                customerKpLegalAuthBo.setHaveLegalPaperAuth(true);
            }
        } else if (kpLegalAuthRequest.getOpSource() == CustomerDeviceType.BUSINESS_PC.getCode()
                || kpLegalAuthRequest.getOpSource() == CustomerDeviceType.BUSINESS_APP.getCode()) {
            Boolean highRiskCityFlag = wmCustomerGrayService.checkWmPoiIdHighRiskCityId(kpLegalAuthRequest.getWmPoiId());
            if (!customerKpLegalAuthBo.getHaveValidSpecialLegalTask() && !highRiskCityFlag) {
                customerKpLegalAuthBo.setDefaultLegalAuthType(LegalAuthTypeEnum.PAPER_AUTH.getCode());
                customerKpLegalAuthBo.setHaveLegalPaperAuth(true);
            }
        }

        //命中灰度+新版本KP数据 则需要展示授权方式
        customerKpLegalAuthBo.setShowLegalAuthTypeFlag(true);
        customerKpLegalAuthBo.setLegalKpExistFlag(checkCustomerKpExist(kpLegalAuthRequest.getCustomerId(), KpTypeEnum.LEGAL.getType(), null));
        Boolean customerCertifyIdCard = wmCustomer.getCertificateType().equals(CertTypeEnum.ID_CARD.getType());
        Boolean signerLegalKpExist = checkCustomerKpExist(kpLegalAuthRequest.getCustomerId(), KpTypeEnum.SIGNER.getType(), KpSignerTypeEnum.SIGNER.getType());
        customerKpLegalAuthBo.setSignerLegalKpExistFlag(customerCertifyIdCard && signerLegalKpExist);
        //如果不展示授权方式则需要设置授权方式为-未设置
        if (!customerKpLegalAuthBo.getShowLegalAuthTypeFlag()) {
            customerKpLegalAuthBo.setDefaultLegalAuthType(LegalAuthTypeEnum.UN_SETTING.getCode());
        }

        return customerKpLegalAuthBo;
    }

    /**
     * 查询KP的流程进度条
     *
     * @param kpProgressReq
     * @return
     */
    public WmCustomerKpProgressResult getKpProgressResult(WmCustomerKpProgressReq kpProgressReq) throws TException, WmCustomerException {
        WmCustomerKpProgressResult customerKpProgressResult = new WmCustomerKpProgressResult();
        if (kpProgressReq.getKpType() == KpTypeEnum.SIGNER.getType()) {
            WmCustomerKpSignerProgressParam kpSignerProgressParam = new WmCustomerKpSignerProgressParam();
            kpSignerProgressParam.setKpId(kpProgressReq.getKpId());
            kpSignerProgressParam.setCustomerId(kpProgressReq.getCustomerId());
            kpSignerProgressParam.setCertNumber(kpProgressReq.getCertNumber());
            kpSignerProgressParam.setCertType(kpProgressReq.getCertType());
            kpSignerProgressParam.setHaveAgentAuth(kpProgressReq.getHaveAgentAuth());
            kpSignerProgressParam.setIsModify(kpProgressReq.getIsModify());
            kpSignerProgressParam.setKpType(kpProgressReq.getKpType());
            kpSignerProgressParam.setSignerType(kpProgressReq.getSignerType());
            kpSignerProgressParam.setLegalAuthType(kpProgressReq.getLegalAuthType());
            kpSignerProgressParam.setOpUId(kpProgressReq.getOpUId());
            WmCustomerKpSignerProgressResult kpSignerProgressResult =
                    wmCustomerKpService.getKpSignerProgress(kpSignerProgressParam);

            customerKpProgressResult.setCurrentNodeOrder(kpSignerProgressResult.getCurrentNodeOrder());
            customerKpProgressResult.setCurrentNodeStatusName(kpSignerProgressResult.getCurrentNodeStatusName());
            customerKpProgressResult.setProgressList(kpSignerProgressResult.getProgressList());
            customerKpProgressResult.setState(kpSignerProgressResult.getState());
            customerKpProgressResult.setLegalKpName(kpSignerProgressResult.getLegalKpName());
            customerKpProgressResult.setLegalKpPhoneNum(kpSignerProgressResult.getLegalKpPhoneNum());
        } else {
            customerKpProgressResult = wmCustomerKpService.getKpLegalProgress(kpProgressReq);
        }
        return customerKpProgressResult;
    }

    /**
     * 查询是否自动带入KP类型法人信息
     *
     * @param kpBaseRequest
     * @return
     */
    public LegalKpShowInfoRes getLegalKpShowInfo(CustomerKpBaseRequest kpBaseRequest) {
        log.info("getLegalKpShowInfo,kpBaseRequest={}", JSON.toJSONString(kpBaseRequest));
        LegalKpShowInfoRes legalKpShowInfoRes = new LegalKpShowInfoRes();
        legalKpShowInfoRes.setShowAutoFlag(false);
        legalKpShowInfoRes.setSignerKpId(null);

        try {
            //查询KP类型是法人的数据
            List<WmCustomerKp> customerLegalKpList = wmCustomerKpDBMapper.selectByCustomerId(kpBaseRequest.getCustomerId(),
                    KpTypeEnum.LEGAL.getType());
            if (CollectionUtils.isNotEmpty(customerLegalKpList)) {
                return legalKpShowInfoRes;
            }
            //查询最新的签约人KP数据
            WmCustomerKp wmCustomerKp = getLatestSignerKp(kpBaseRequest.getCustomerId());
            //查不到KP直接返回
            if (wmCustomerKp == null) {
                return legalKpShowInfoRes;
            }
            //非法人代表&&身份证不处理
            if (wmCustomerKp.getSignerType() != KpSignerTypeEnum.SIGNER.getType()
                    || wmCustomerKp.getCertType() != CertTypeEnum.ID_CARD.getType()) {
                return legalKpShowInfoRes;
            }
            //已录入或者生效状态则需要自动带入
            if (wmCustomerKp.getState() == KpSignerStateMachine.EFFECT.getState()
                    || wmCustomerKp.getState() == KpSignerStateMachine.RECORDED.getState()) {
                legalKpShowInfoRes.setShowAutoFlag(true);
                legalKpShowInfoRes.setSignerKpId(wmCustomerKp.getId());
                return legalKpShowInfoRes;
            }
        } catch (Exception e) {
            log.error("getLegalKpShowInfo,根据客户ID信息查询是否需要自动带入KP法人发生异常,customerId={}", kpBaseRequest.getCustomerId(), e);
        }
        return legalKpShowInfoRes;
    }

    /**
     * 根据客户ID查询KP法人信息-主表数据
     *
     * @param customerId
     * @return
     */
    public WmCustomerKp getKpLegalByCustomerId(Integer customerId) {
        LOGGER.info("getLatestLegalKp#查询最新的KP类型法人数据,customerId = {}", customerId);
        WmCustomerKp customerKp = wmCustomerKpDBMapper.getLegalKpByCustomerId(customerId);
        if (customerKp == null) {
            return null;
        }
        wmCustomerSensitiveWordsService.readKpWhenSelect(customerKp);
        return customerKp;
    }

    /**
     * @param customerId
     * @return
     */
    public WmCustomerKp getLatestLegalKp(Integer customerId) {
        LOGGER.info("getLatestLegalKp#查询最新的KP类型法人数据,customerId = {}", customerId);
        WmCustomerKp customerKp = wmCustomerKpDBMapper.getLegalKpByCustomerId(customerId);
        if (customerKp == null) {
            return null;
        }
        wmCustomerSensitiveWordsService.readKpWhenSelect(customerKp);
        WmCustomerKpTemp wmCustomerKpTemp = wmCustomerKpTempDBMapper.selectByKpId(customerKp.getId());
        if (wmCustomerKpTemp != null) {
            wmCustomerSensitiveWordsService.readKpWhenSelect(wmCustomerKpTemp);
            BeanUtils.copyProperties(wmCustomerKpTemp, customerKp);
            customerKp.setId(wmCustomerKpTemp.getKpId());
        }
        return customerKp;
    }


    /**
     * @param customerId
     * @return
     */
    public WmCustomerKp getEffectiveLegalKp(Integer customerId) {
        LOGGER.info("getEffectiveLegalKp#查询生效的KP类型法人数据,customerId = {}", customerId);
        WmCustomerKp customerKp = wmCustomerKpDBMapper.getLegalKpByCustomerId(customerId);
        if (customerKp == null || customerKp.getState() != KpLegalStateMachine.EFFECT.getState()) {
            return null;
        }
        wmCustomerSensitiveWordsService.readKpWhenSelect(customerKp);
        return customerKp;
    }

    /**
     * 签约人法人KP或者KP类型是法人是否存在
     *
     * @param customerId
     * @return
     */
    public Boolean checkKpLegalExist(Integer customerId) {
        if (customerId == null || customerId <= 0) {
            return false;
        }
        WmCustomerKp legalCustomerKp = getLatestLegalKp(customerId);
        if (legalCustomerKp != null) {
            return true;
        }
        return false;
    }

    /**
     * 校验是否有生效的法人数据
     *
     * @param customerId
     * @return
     */
    public Boolean checkHaveEffectiveLegalKp(Integer customerId) {
        try {
            WmCustomerKp legalKp = wmCustomerKpDBMapper.getLegalKpByCustomerId(customerId);
            if (legalKp != null && legalKp.getState() == KpLegalStateMachine.EFFECT.getState()) {
                return true;
            }
        } catch (Exception e) {
            LOGGER.error("checkHaveEffectiveLegalKp,校验是否存在生效的法人数据发生异常,customerId={}", customerId, e);
        }
        return false;
    }

    public WmCustomerKp selectEffectSignerByCustomerIdRT(Integer customerId) {
        WmCustomerKpSearchCondition condition = new WmCustomerKpSearchCondition();
        condition.setCustomerId(customerId);
        condition.setKpType((int) KpTypeEnum.SIGNER.getType());
        condition.setEffective(KpConstants.EFFECTIVE);
        condition.setValid(CustomerConstants.VALID);
        List<WmCustomerKp> list = wmCustomerKpDBMapper.selectByConditionMaster(condition);
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        if (list.size() > 1) {
            log.error("selectEffectSignerByCustomerIdRT 返回多条记录，请关注 list={}", JSONObject.toJSONString(list));
        }
        return list.get(0);
    }

    /**
     * 校验是否存在不可编辑状态KP记录
     *
     * @param kpId
     * @return
     */
    public boolean checkExistUnEditKpRecord(Integer kpId) {
        WmCustomerKp customerKp = wmCustomerKpDBMapper.selectByIdRT(kpId);
        Byte currentState = customerKp.getState();
        if (customerKp.getState() == KpSignerStateMachine.EFFECT.getState()) {
            WmCustomerKpTemp wmCustomerKpTemp = wmCustomerKpTempDBMapper.getCustomerKpTempByKpIdFromRT(kpId);
            if (wmCustomerKpTemp != null) {
                currentState = wmCustomerKpTemp.getState();
            }
        }
        if (MccCustomerConfig.getSignerKpUnEditStateList().contains(currentState.intValue())) {
            return true;
        }
        return false;
    }

    /**
     * 允许不上传代理人授权书证件
     *
     * @param wmCustomerDB
     * @param wmCustomerKp
     * @return
     */
    public boolean checkAllowNoAgentAuthPic(WmCustomerDB wmCustomerDB, WmCustomerKp wmCustomerKp) throws WmCustomerException {
        //外卖单店客户&&电销系统&&新版本V4->允许不上传代理人授权书附件
        if (wmCustomerDB.getCustomerRealType() == CustomerRealTypeEnum.DANDIAN.getValue()
                && wmCustomerKp.getVersion() != null
                && wmCustomerKp.getVersion() == KpVersionEnum.V4.getCode()
                && wmCustomerKp.getOperateSource() != null
                && wmCustomerKp.getOperateSource() == CustomerDeviceType.MERCHANT_SYS.getCode()
                && wmCustomerDB.getCustomerType() == CustomerType.CUSTOMER_TYPE_BUSINESS.getCode()) {
            return true;
        }
        return false;
    }

    /**
     * 根据条件获取状态图V2版本
     * 从枚举定义中抽出来单店方法，不再维护再枚举中
     *
     * @return
     */
    public KpSignerProgressEnum getKpSignerProgressByConditionV2(WmCustomerKpSignerProgressParam progressParam, WmCustomerKp wmCustomerKp) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.service.kp.WmCustomerKpService.getKpSignerProgressByConditionV2(WmCustomerKpSignerProgressParam,WmCustomerKp)");

        KpSignerTypeEnum kpSignerTypeEnum = KpSignerTypeEnum.getByType(progressParam.getSignerType());
        CertTypeEnum certTypeEnum = CertTypeEnum.getByType(progressParam.getCertType());
        HaveAgentAuthEnum haveAgentAuthEnum = HaveAgentAuthEnum.of(progressParam.getHaveAgentAuth());
        Boolean isNotCertNumberModify = progressParam.getIsNotCertNumberModify();
        Boolean isModify = progressParam.getIsModify();

        LegalAuthTypeEnum legalAuthTypeEnum = LegalAuthTypeEnum.UN_SETTING;
        if (progressParam.getLegalAuthType() != null) {
            legalAuthTypeEnum = LegalAuthTypeEnum.getByCode(progressParam.getLegalAuthType());
        } else if (wmCustomerKp != null && wmCustomerKp.getLegalAuthType() != null) {
            legalAuthTypeEnum = LegalAuthTypeEnum.getByCode(wmCustomerKp.getLegalAuthType());
        }

        if (kpSignerTypeEnum == KpSignerTypeEnum.SIGNER || kpSignerTypeEnum == KpSignerTypeEnum.LEGAL) {
            if (CertTypeEnum.checkIdCardSet(certTypeEnum.getType())) {
                return KpSignerProgressEnum.NOT_AGENT_IDCARD;
            } else {
                return KpSignerProgressEnum.NOT_AGENT_NOT_IDCARD;
            }
        } else if (kpSignerTypeEnum == KpSignerTypeEnum.AGENT) {
            if (isModify != null && isModify) {
                if (CertTypeEnum.checkIdCardSet(certTypeEnum.getType())) {
                    //授权方式是否为短信授权
                    if (legalAuthTypeEnum != null && legalAuthTypeEnum == LegalAuthTypeEnum.MESSAGE_AUTH) {
                        return KpSignerProgressEnum.AGENT_IDCARD_INSERT_MSG_AUTH;
                    } else {
                        if (haveAgentAuthEnum == HaveAgentAuthEnum.HAVE) {
                            return KpSignerProgressEnum.AGENT_IDCARD_INSERT;
                        } else {
                            return KpSignerProgressEnum.AGENT_IDCARD_NOT_AUTH_UPDATE;
                        }
                    }
                } else {
                    //授权方式是否为短信授权
                    if (legalAuthTypeEnum != null && legalAuthTypeEnum == LegalAuthTypeEnum.MESSAGE_AUTH) {
                        return KpSignerProgressEnum.AGENT_NOT_IDCARD_INSERT_MSG_AUTH;
                    } else {
                        if (haveAgentAuthEnum == HaveAgentAuthEnum.HAVE) {
                            return KpSignerProgressEnum.AGENT_NOT_IDCARD_INSERT;
                        } else {
                            return KpSignerProgressEnum.AGENT_NOT_IDCARD_NOT_AUTH_UPDATE;
                        }
                    }
                }
            } else {
                if (CertTypeEnum.checkIdCardSet(certTypeEnum.getType())) {
                    if (isNotCertNumberModify != null && isNotCertNumberModify) {
                        return KpSignerProgressEnum.AGENT_IDCARD_NOT_UPDATE;
                    } else {
                        if (legalAuthTypeEnum != null && legalAuthTypeEnum == LegalAuthTypeEnum.MESSAGE_AUTH) {
                            //非空且为电销创建则展示
                            if (checkDxAgentFirstCommitProgress(wmCustomerKp)) {
                                return KpSignerProgressEnum.AGENT_IDCARD_INSERT;
                            }
                            //为空或非电销创建
                            return KpSignerProgressEnum.AGENT_IDCARD_INSERT_MSG_AUTH;
                        }
                        return KpSignerProgressEnum.AGENT_IDCARD_INSERT;
                    }

                } else {
                    //短信授权
                    if (legalAuthTypeEnum != null && legalAuthTypeEnum == LegalAuthTypeEnum.MESSAGE_AUTH
                            && isNotCertNumberModify != null && !isNotCertNumberModify) {
                        return KpSignerProgressEnum.AGENT_NOT_IDCARD_INSERT_MSG_AUTH;
                    }
                    return KpSignerProgressEnum.AGENT_NOT_IDCARD_INSERT;
                }
            }
        }
        return null;
    }

    /**
     * 校验是否电销首次提审/首次提审至成功
     *
     * @param wmCustomerKp
     * @return
     */
    private boolean checkDxAgentFirstCommitProgress(WmCustomerKp wmCustomerKp) {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.service.kp.WmCustomerKpService.checkDxAgentFirstCommitProgress(com.sankuai.meituan.waimai.thrift.customer.domain.kp.WmCustomerKp)");
        //兼容新增场景还无KP情况
        if (wmCustomerKp == null) {
            return false;
        }
        //渠道标识为空或非电销
        if (wmCustomerKp.getOperateSource() == null || wmCustomerKp.getOperateSource() != CustomerDeviceType.MERCHANT_SYS.getCode()) {
            return false;
        }

        //临时表有记录则不满足 电销省略短信授权场景
        WmCustomerKpTemp wmCustomerKpTemp = wmCustomerKpTempDBMapper.selectByKpId(wmCustomerKp.getId());
        if (wmCustomerKpTemp != null) {
            return false;
        }
        //当前KP状态为认证失败、代理人驳回或授权失败则不满足 电销省略短信授权场景
        if (wmCustomerKp.getState() == KpSignerStateMachine.PREAUTH_FAIL.getState()
                || wmCustomerKp.getState() == KpSignerStateMachine.AGENT_AUDIT_REJECT.getState()
                || wmCustomerKp.getState() == KpSignerStateMachine.CHANGE_ORIGIN_SIGNER_CANCEL.getState()) {
            return false;
        }
        //提审记录为空或只能有一条且操作来源为电销，进度条才不展示短信授权
        List<WmCustomerKpAudit> kpAuditList = wmCustomerKpAuditMapper.listKpAuditByKpId(wmCustomerKp.getId());
        if (CollectionUtils.isEmpty(kpAuditList) || (CollectionUtils.isNotEmpty(kpAuditList)
                && kpAuditList.size() == 1
                && kpAuditList.get(0).getOperateSource() != null
                && kpAuditList.get(0).getOperateSource() == CustomerDeviceType.MERCHANT_SYS.getCode())) {
            return true;
        }
        return false;
    }



}
