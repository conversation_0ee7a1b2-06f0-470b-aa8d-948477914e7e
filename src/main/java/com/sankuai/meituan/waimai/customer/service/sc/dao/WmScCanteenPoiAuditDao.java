package com.sankuai.meituan.waimai.customer.service.sc.dao;

import com.sankuai.meituan.waimai.customer.dao.sc.WmScCanteenPoiAuditMapper;
import com.sankuai.meituan.waimai.customer.domain.sc.WmScCanteenPoiAuditDB;
import com.sankuai.meituan.waimai.thrift.constants.WmYesNoCons;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class WmScCanteenPoiAuditDao {

    @Autowired
    private WmScCanteenPoiAuditMapper wmScCanteenPoiAuditMapper;

    public WmScCanteenPoiAuditDB getById(Long id){
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_1", "com.sankuai.meituan.waimai.customer.service.sc.dao.WmScCanteenPoiAuditDao.getById(java.lang.Long)");
        return wmScCanteenPoiAuditMapper.selectByPrimaryKey(id);
    }

    public int update(WmScCanteenPoiAuditDB poiAuditDB) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.service.sc.dao.WmScCanteenPoiAuditDao.update(com.sankuai.meituan.waimai.customer.domain.sc.WmScCanteenPoiAuditDB)");
        return wmScCanteenPoiAuditMapper.updateByPrimaryKeySelective(poiAuditDB);
    }

    /**
     * 查询最近一次审核的记录
     * @param canteenId 食堂ID
     * @return 审核信息
     */
    public WmScCanteenPoiAuditDB getLatestInValidCanteenPoiAuditLog(Integer canteenId) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.sc.dao.WmScCanteenPoiAuditDao.getLatestInValidCanteenPoiAuditLog(java.lang.Integer)");
        return wmScCanteenPoiAuditMapper.getLatestCanteenPoiAudit(canteenId, null);
    }

    /**
     * 查询最近一次有效的审核记录
     * @param canteenId 食堂ID
     * @return 审核信息
     */
    public WmScCanteenPoiAuditDB getLatestValidPoiAuditByCateenId(Integer canteenId) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.sc.dao.WmScCanteenPoiAuditDao.getLatestValidPoiAuditByCateenId(java.lang.Integer)");
        return wmScCanteenPoiAuditMapper.getLatestCanteenPoiAudit(canteenId, WmYesNoCons.YES.getValue());
    }

    public void insert(WmScCanteenPoiAuditDB auditDB) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.service.sc.dao.WmScCanteenPoiAuditDao.insert(com.sankuai.meituan.waimai.customer.domain.sc.WmScCanteenPoiAuditDB)");
        wmScCanteenPoiAuditMapper.insertSelective(auditDB);
    }

}
