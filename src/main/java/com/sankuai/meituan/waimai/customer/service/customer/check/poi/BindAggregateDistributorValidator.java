package com.sankuai.meituan.waimai.customer.service.customer.check.poi;

import com.google.common.collect.Maps;
import com.sankuai.meituan.waimai.customer.constant.customer.CustomerPoiBindDecideResultEnum;
import com.sankuai.meituan.waimai.customer.domain.poi.WmCustomerPoiCheckBo;
import com.sankuai.meituan.waimai.customer.service.customer.check.CustomerPoiBaseValidator;
import com.sankuai.meituan.waimai.customer.service.customer.check.ICustomerPoiValidator;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerConstants;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerRealTypeEnum;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import lombok.extern.slf4j.Slf4j;
import org.apache.thrift.TException;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * 门店要绑定聚合配送商类型的客户校验
 */
@Slf4j
@Service
public class BindAggregateDistributorValidator extends CustomerPoiBaseValidator implements ICustomerPoiValidator {
    @Override
    public boolean canCheck(int customerRealType) {
        if (customerRealType == CustomerRealTypeEnum.AGGREGATE_DISTRIBUTOR.getValue()) {
            return true;
        }
        return false;
    }

    @Override
    public Map<CustomerPoiBindDecideResultEnum, List<Long>> validBind(WmCustomerPoiCheckBo wmCustomerPoiCheckBo) throws WmCustomerException, TException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_2", "com.sankuai.meituan.waimai.customer.service.customer.check.poi.BindAggregateDistributorValidator.validBind(com.sankuai.meituan.waimai.customer.domain.poi.WmCustomerPoiCheckBo)");
        checkBind(wmCustomerPoiCheckBo);
        Integer customerRealType = wmCustomerPoiCheckBo.getWmCustomerDB().getCustomerRealType();
        if (customerRealType == CustomerRealTypeEnum.AGGREGATE_DISTRIBUTOR.getValue()) {
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "'聚合配送商'客户不允许绑定门店");
        }
        return Maps.newHashMap();
    }
}
