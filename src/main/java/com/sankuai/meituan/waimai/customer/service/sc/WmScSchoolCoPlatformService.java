package com.sankuai.meituan.waimai.customer.service.sc;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.sankuai.meituan.waimai.customer.dao.sc.WmScSchoolCoPlatformMapper;
import com.sankuai.meituan.waimai.customer.dao.sc.WmScSchoolCoPlatformMarketMapper;
import com.sankuai.meituan.waimai.customer.domain.sc.*;
import com.sankuai.meituan.waimai.thrift.bo.WmCustomerDiffCellBo;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.OptTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.WmScCodeConstants;
import com.sankuai.meituan.waimai.thrift.customer.domain.sc.*;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import com.sankuai.meituan.waimai.thrift.exception.WmSchCantException;
import com.sankuai.meituan.waimai.thrift.util.DiffUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import com.sankuai.meituan.waimai.customer.util.WmScTransUtil;
import org.apache.commons.lang3.StringUtils;
import org.apache.thrift.TException;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import static com.sankuai.meituan.waimai.customer.constant.ScFieldConstant.schoolCoPlatformField;
import static com.sankuai.meituan.waimai.customer.constant.ScFieldConstant.schoolCoPlatformMarketField;
import static com.sankuai.meituan.waimai.customer.service.sc.WmScSchoolPlatformService.SCHOOL_PLATFROM_EXTRA_CODE_V2;
import static com.sankuai.meituan.waimai.thrift.customer.constant.sc.ScConstants.SC_SCHOOL_PLATFORM_LOG;
import static com.sankuai.meituan.waimai.thrift.customer.constant.sc.WmScCodeConstants.BIZ_PARA_ERROR;

/**
 * 学校平台合作信息处理逻辑V2
 * <AUTHOR>
 * @date 2024/06/14
 * @email <EMAIL>
 */
@Slf4j
@Service
public class WmScSchoolCoPlatformService {
    @Autowired
    private WmScSchoolCoPlatformMapper wmScSchoolCoPlatformMapper;

    @Autowired
    private WmScSchoolCoPlatformMarketMapper wmScSchoolCoPlatformMarketMapper;

    @Autowired
    private WmScLogSchoolInfoService wmScLogSchoolInfoService;

    /**
     * 保存学校平台合作信息(单条)V2
     * @param wmScSchoolCoPlatformSaveDTO wmScSchoolCoPlatformSaveDTO
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     * @throws TException org.apache.thrift.TException
     */
    public void saveSchoolPlatformV2(WmScSchoolCoPlatformSaveDTO wmScSchoolCoPlatformSaveDTO) throws WmSchCantException, TException {
        log.info("[wmScSchoolCoPlatformService.saveSchoolPlatformV2] input param: wmScSchoolCoPlatformSaveDTO = {}", JSONObject.toJSONString(wmScSchoolCoPlatformSaveDTO));
        if (wmScSchoolCoPlatformSaveDTO == null
                || wmScSchoolCoPlatformSaveDTO.getSchoolPrimaryId() == null
                || wmScSchoolCoPlatformSaveDTO.getSchoolPrimaryId() <= 0) {
            log.error("[wmScSchoolCoPlatformService.saveSchoolPlatformV2] input error. wmScSchoolCoPlatformSaveDTO = {}", JSONObject.toJSONString(wmScSchoolCoPlatformSaveDTO));
            throw new WmSchCantException(BIZ_PARA_ERROR, "保存平台合作信息参数异常");
        }
        // 校验学校平台合作信息
        checkSchoolCoPlatformInfo(wmScSchoolCoPlatformSaveDTO);

        // 若平台合作信息主键ID为空, 则执行新增操作; 否则执行修改操作
        if (wmScSchoolCoPlatformSaveDTO.getId() == null) {
            insertSchoolPlatformV2(wmScSchoolCoPlatformSaveDTO);
        } else {
            WmScSchoolCoPlatformDO wmScSchoolCoPlatformDO = wmScSchoolCoPlatformMapper.selectByPrimaryKey(wmScSchoolCoPlatformSaveDTO.getId());
            if (wmScSchoolCoPlatformDO == null) {
                log.error("[wmScSchoolCoPlatformService.saveSchoolPlatformV2] select error. primaryKey = {}", wmScSchoolCoPlatformSaveDTO.getId());
                throw new WmSchCantException(BIZ_PARA_ERROR, "查询平台合作信息参数异常");
            }
            List<WmScSchoolCoPlatformMarketDO> wmScSchoolCoPlatformMarketDOList = wmScSchoolCoPlatformMarketMapper.selectByPlatformPrimaryId(wmScSchoolCoPlatformDO.getId());
            if (CollectionUtils.isNotEmpty(wmScSchoolCoPlatformMarketDOList)) {
                wmScSchoolCoPlatformDO.setWmScSchoolCoPlatformMarketDOList(wmScSchoolCoPlatformMarketDOList);
            }
            updateSchoolPlatform(wmScSchoolCoPlatformSaveDTO, wmScSchoolCoPlatformDO);
        }
    }

    /**
     * 校验学校平台合作信息
     * @param wmScSchoolCoPlatformSaveDTO wmScSchoolCoPlatformSaveDTO
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     * @throws TException org.apache.thrift.TException
     */
    public void checkSchoolCoPlatformInfo(WmScSchoolCoPlatformSaveDTO wmScSchoolCoPlatformSaveDTO) throws WmSchCantException, TException {
        List<WmScSchoolCoPlatformDO> wmScSchoolCoPlatformDOList = wmScSchoolCoPlatformMapper.selectBySchoolPrimaryIdAndCooperationPlatform(
                wmScSchoolCoPlatformSaveDTO.getSchoolPrimaryId(), wmScSchoolCoPlatformSaveDTO.getCooperationPlatform());
        // 合作平台code列表
        List<Integer> cooperationPlatfromCodeList = wmScSchoolCoPlatformDOList.stream()
                .map(WmScSchoolCoPlatformDO::getCooperationPlatform)
                .collect(Collectors.toList());
        // 除了"其他"，合作平台只能录入一个
        if (wmScSchoolCoPlatformSaveDTO.getId() == null
                && !wmScSchoolCoPlatformSaveDTO.getCooperationPlatform().equals(SCHOOL_PLATFROM_EXTRA_CODE_V2)
                && CollectionUtils.isNotEmpty(cooperationPlatfromCodeList)) {
            throw new WmSchCantException(BIZ_PARA_ERROR, "此平台在本校已录入过, 无法新增");
        }
    }

    /**
     * 添加保存学校平台合作信息(单条)V2
     * @param wmScSchoolCoPlatformSaveDTO wmScSchoolCoPlatformSaveDTO
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     * @throws TException org.apache.thrift.TException
     */
    private void insertSchoolPlatformV2(WmScSchoolCoPlatformSaveDTO wmScSchoolCoPlatformSaveDTO) throws WmSchCantException, TException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.sc.WmScSchoolCoPlatformService.insertSchoolPlatformV2(com.sankuai.meituan.waimai.thrift.customer.domain.sc.WmScSchoolCoPlatformSaveDTO)");
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.sc.WmScSchoolCoPlatformService.insertSchoolPlatformV2(com.sankuai.meituan.waimai.thrift.customer.domain.sc.WmScSchoolCoPlatformSaveDTO)");
        log.info("[wmScSchoolCoPlatformService.insertSchoolPlatformV2] input param: wmScSchoolCoPlatformSaveDTO = {}"
                , JSONObject.toJSONString(wmScSchoolCoPlatformSaveDTO));
        if (wmScSchoolCoPlatformSaveDTO == null
                || wmScSchoolCoPlatformSaveDTO.getSchoolPrimaryId() == null
                || wmScSchoolCoPlatformSaveDTO.getSchoolPrimaryId() <= 0) {
            log.error("[wmScSchoolCoPlatformService.insertSchoolPlatformV2] input error. wmScSchoolCoPlatformSaveDTO = {}"
                    , JSONObject.toJSONString(wmScSchoolCoPlatformSaveDTO));
            throw new WmSchCantException(BIZ_PARA_ERROR, "请求参数为空");
        }

        WmScSchoolCoPlatformDO wmScSchoolCoPlatformDOInsert = WmScTransUtil.transWmScSchoolCoPlatformSaveDTOToDO(wmScSchoolCoPlatformSaveDTO);
        // 插入到学校平台合作信息新表wm_school_cooperation_platform一条数据
        log.info("[wmScSchoolCoPlatformService.insertSchoolPlatformV2] insertSelective. wmScSchoolCoPlatformDOInsert = {}"
                , JSONObject.toJSONString(wmScSchoolCoPlatformDOInsert));
        int result = wmScSchoolCoPlatformMapper.insertSelective(wmScSchoolCoPlatformDOInsert);
        // 插入到学校合作平台营销活动新表wm_school_cooperation_platform_market N条数据（N=学校合作平台营销活动数）
        for (WmScSchoolCoPlatformMarketDO wmScSchoolCoPlatformMarketDo : wmScSchoolCoPlatformDOInsert.getWmScSchoolCoPlatformMarketDOList()) {
            wmScSchoolCoPlatformMarketDo.setPlatformPrimaryId(wmScSchoolCoPlatformDOInsert.getId());
        }
        log.info("[wmScSchoolCoPlatformService.insertSchoolPlatformV2] batchInsertSelective. wmScSchoolCoPlatformMarketDoList = {}"
                , JSONObject.toJSONString(wmScSchoolCoPlatformDOInsert.getWmScSchoolCoPlatformMarketDOList()));
        wmScSchoolCoPlatformMarketMapper.batchInsertSelective(wmScSchoolCoPlatformDOInsert.getWmScSchoolCoPlatformMarketDOList());
        if (result > 0) {
            try {
                // 记录操作日志
                WmScSchoolCoPlatformLogBO wmScSchoolCoPlatformLogBO = new WmScSchoolCoPlatformLogBO.Builder()
                        .userId(wmScSchoolCoPlatformSaveDTO.getUserId())
                        .userName(wmScSchoolCoPlatformSaveDTO.getUserName())
                        .wmScSchoolCoPlatformDOInsert(wmScSchoolCoPlatformDOInsert)
                        .build();
                wmScLogSchoolInfoService.recordSchoolCoPlatformInsertLog(wmScSchoolCoPlatformLogBO);
            } catch (Exception e) {
                log.error("[WmScSchoolPlatformService.insertSchoolPlatformV2] Record operation logs error, without business impact. wmScSchoolCoPlatformDOInsert = {}"
                        , JSONObject.toJSONString(wmScSchoolCoPlatformDOInsert), e);
            }
        }
        log.info("[WmScSchoolPlatformService.insertSchoolPlatformV2] insert success. wmScSchoolCoPlatformDOInsert = {}"
                , JSONObject.toJSONString(wmScSchoolCoPlatformDOInsert));
    }

    /**
     * 修改保存学校平台合作信息(单条)
     * @param wmScSchoolCoPlatformSaveDTO wmScSchoolCoPlatformSaveDTO
     * @param wmScSchoolCoPlatformDO wmScSchoolCoPlatformDO
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     * @throws TException org.apache.thrift.TException
     */
    private void updateSchoolPlatform(WmScSchoolCoPlatformSaveDTO wmScSchoolCoPlatformSaveDTO, WmScSchoolCoPlatformDO wmScSchoolCoPlatformDO) throws WmSchCantException, TException {
        log.info("[wmScSchoolCoPlatformService.updateSchoolPlatform] input param: wmScSchoolCoPlatformSaveDTO = {}, wmScSchoolCoPlatformDO = {}",
                JSONObject.toJSONString(wmScSchoolCoPlatformSaveDTO), JSONObject.toJSONString(wmScSchoolCoPlatformDO));
        if (wmScSchoolCoPlatformSaveDTO == null || wmScSchoolCoPlatformDO == null) {
            log.error("[wmScSchoolCoPlatformService.updateSchoolPlatform] input error. wmScSchoolCoPlatformSaveDTO = {}, wmScSchoolCoPlatformDO = {}",
                    JSONObject.toJSONString(wmScSchoolCoPlatformSaveDTO), JSONObject.toJSONString(wmScSchoolCoPlatformDO));
             throw new WmSchCantException(BIZ_PARA_ERROR, "请求参数为空");
        }
        // 校验保存信息和当前信息是否一致
        if (isSchoolPlatformInfoSame(wmScSchoolCoPlatformSaveDTO, wmScSchoolCoPlatformDO)) {
            log.info("[wmScSchoolCoPlatformService.updateSchoolPlatform] the same info, return now. wmScSchoolCoPlatformSaveDTO = {}, wmScSchoolCoPlatformDO = {}",
                    JSONObject.toJSONString(wmScSchoolCoPlatformSaveDTO), JSONObject.toJSONString(wmScSchoolCoPlatformDO));

            WmScSchoolCoPlatformDO wmScSchoolCoPlatformDOUpdate = new WmScSchoolCoPlatformDO();
            wmScSchoolCoPlatformDOUpdate.setId(wmScSchoolCoPlatformSaveDTO.getId());
            wmScSchoolCoPlatformDOUpdate.setMuid(wmScSchoolCoPlatformSaveDTO.getUserId().longValue());
            int result = wmScSchoolCoPlatformMapper.updateByPrimaryKeySelective(wmScSchoolCoPlatformDOUpdate);

            String log = "操作：修改\\n" + "平台合作信息ID：" + wmScSchoolCoPlatformSaveDTO.getId() + "\\n";
            wmScLogSchoolInfoService.insertScOptLog(OptTypeEnum.UPDATE.getType(), SC_SCHOOL_PLATFORM_LOG, wmScSchoolCoPlatformSaveDTO.getSchoolPrimaryId(),
                    wmScSchoolCoPlatformSaveDTO.getUserId(), wmScSchoolCoPlatformSaveDTO.getUserName(), log, "");
            return;
        }

        WmScSchoolCoPlatformDO wmScSchoolCoPlatformDOUpdate = new WmScSchoolCoPlatformDO();
        wmScSchoolCoPlatformDOUpdate.setId(wmScSchoolCoPlatformSaveDTO.getId());
        wmScSchoolCoPlatformDOUpdate.setMuid(wmScSchoolCoPlatformSaveDTO.getUserId().longValue());
        composeSchoolPlarformUpdate(wmScSchoolCoPlatformSaveDTO, wmScSchoolCoPlatformDO, wmScSchoolCoPlatformDOUpdate);

        // 执行更新操作
        log.info("[wmScSchoolCoPlatformService.updateSchoolPlatform] updateByPrimaryKeySelective wmScSchoolCoPlatformDOUpdate = {}",
                JSONObject.toJSONString(wmScSchoolCoPlatformDOUpdate));
        int result = wmScSchoolCoPlatformMapper.updateByPrimaryKeySelective(wmScSchoolCoPlatformDOUpdate);
        if (CollectionUtils.isNotEmpty(wmScSchoolCoPlatformDO.getWmScSchoolCoPlatformMarketDOList())) {
            log.info("[wmScSchoolCoPlatformService.updateSchoolPlatform] batchInvalidByPrimaryKey wmScSchoolCoPlatformMarketDoList = {}",
                    JSONObject.toJSONString(wmScSchoolCoPlatformDO.getWmScSchoolCoPlatformMarketDOList()));
            wmScSchoolCoPlatformMarketMapper.batchInvalidByPrimaryKey(wmScSchoolCoPlatformDO.getWmScSchoolCoPlatformMarketDOList(), wmScSchoolCoPlatformSaveDTO.getUserId().longValue());
        }
        WmScSchoolCoPlatformDO wmScSchoolCoPlatformDOInsert = WmScTransUtil.transWmScSchoolCoPlatformSaveDTOToDO(wmScSchoolCoPlatformSaveDTO);
        if (CollectionUtils.isNotEmpty(wmScSchoolCoPlatformDOInsert.getWmScSchoolCoPlatformMarketDOList())) {
            log.info("[wmScSchoolCoPlatformService.updateSchoolPlatform] batchInsertSelective wmScSchoolCoPlatformMarketDoList = {}",
                    JSONObject.toJSONString(wmScSchoolCoPlatformDOInsert.getWmScSchoolCoPlatformMarketDOList()));
            wmScSchoolCoPlatformMarketMapper.batchInsertSelective(wmScSchoolCoPlatformDOInsert.getWmScSchoolCoPlatformMarketDOList());
        }

        if (result > 0) {
            try {
                // 记录到操作日志
                BeanUtils.copyProperties(wmScSchoolCoPlatformDOInsert, wmScSchoolCoPlatformDOUpdate);
                composeSchoolPlarformMarketUpdate(wmScSchoolCoPlatformSaveDTO, wmScSchoolCoPlatformDO, wmScSchoolCoPlatformDOUpdate);
                WmScSchoolCoPlatformLogBO wmScSchoolCoPlatformLogBO = new WmScSchoolCoPlatformLogBO.Builder()
                        .userId(wmScSchoolCoPlatformSaveDTO.getUserId())
                        .userName(wmScSchoolCoPlatformSaveDTO.getUserName())
                        .wmScSchoolCoPlatformDOBefore(wmScSchoolCoPlatformDO)
                        .wmScSchoolCoPlatformDOAfter(wmScSchoolCoPlatformDOUpdate)
                        .build();
                wmScLogSchoolInfoService.recordSchoolCoPlatformUpdateLog(wmScSchoolCoPlatformLogBO);
            } catch (Exception e) {
                log.error("[wmScSchoolCoPlatformService.updateSchoolPlatform] Record operation logs error, without business impact. wmScSchoolCoPlatformDOUpdate = {}",
                        JSONObject.toJSONString(wmScSchoolCoPlatformDOUpdate));
            }
        }
        log.info("[wmScSchoolCoPlatformService.updateSchoolPlatform] update success. wmScSchoolCoPlatformDOUpdate = {}", JSONObject.toJSONString(wmScSchoolCoPlatformDOUpdate));
    }

    /**
     * 校验要保存的平台合作信息和当前信息是否一致
     * @param wmScSchoolCoPlatformSaveDTO wmScSchoolCoPlatformSaveDTO
     * @param wmScSchoolCoPlatformDO wmScSchoolCoPlatformDO
     * @return true: 一致 / false: 不一致
     */
    private Boolean isSchoolPlatformInfoSame(WmScSchoolCoPlatformSaveDTO wmScSchoolCoPlatformSaveDTO, WmScSchoolCoPlatformDO wmScSchoolCoPlatformDO) throws WmSchCantException {
        if (wmScSchoolCoPlatformSaveDTO == null || wmScSchoolCoPlatformDO == null) {
            return false;
        }

        // 将wmScSchoolCoPlatformSaveDTO转为DO
        WmScSchoolCoPlatformDO wmScSchoolCoPlatformDOUpdate = WmScTransUtil.transWmScSchoolCoPlatformSaveDTOToDO(wmScSchoolCoPlatformSaveDTO);

        List<WmScSchoolCoPlatformMarketDO> wmScSchoolCoPlatformMarketDOUpdateList = wmScSchoolCoPlatformDOUpdate.getWmScSchoolCoPlatformMarketDOList();
        List<WmScSchoolCoPlatformMarketDO> wmScSchoolCoPlatformMarketDOList = wmScSchoolCoPlatformDO.getWmScSchoolCoPlatformMarketDOList();
        if (CollectionUtils.isEmpty(wmScSchoolCoPlatformMarketDOUpdateList) || CollectionUtils.isEmpty(wmScSchoolCoPlatformMarketDOList)) {
            return false;
        }
        int doUpdateSize = wmScSchoolCoPlatformMarketDOUpdateList.size();
        int doSize = wmScSchoolCoPlatformMarketDOList.size();
        if (doUpdateSize != doSize) {
            return false;
        }

        List<WmCustomerDiffCellBo> diffList = Lists.newArrayList();
        try {
            diffList = DiffUtil.compare(wmScSchoolCoPlatformDO, wmScSchoolCoPlatformDOUpdate, schoolCoPlatformField);
        } catch (WmCustomerException e) {
            log.error("[WmScSchoolCoPlatformService.isSchoolPlatformInfoSame] diff is error. wmScSchoolCoPlatformDO = {},wmScSchoolCoPlatformDOUpdate = {} "
                    ,JSONObject.toJSONString(wmScSchoolCoPlatformDO), JSONObject.toJSONString(wmScSchoolCoPlatformDOUpdate), e);
            throw new WmSchCantException(e.getCode(), e.getMsg());
        }
        if (CollectionUtils.isNotEmpty(diffList)) {
            return false;
        }

        for (int i = 0; i < doUpdateSize; i++) {
            if (wmScSchoolCoPlatformMarketDOUpdateList.get(i) == null || wmScSchoolCoPlatformMarketDOList.get(i) == null) {
                return false;
            }
            try {
                diffList = DiffUtil.compare(wmScSchoolCoPlatformMarketDOList.get(i), wmScSchoolCoPlatformMarketDOUpdateList.get(i), schoolCoPlatformMarketField);
            } catch (WmCustomerException e) {
                log.error("[WmScSchoolCoPlatformService.isSchoolPlatformInfoSame] diff is error. wmScSchoolCoPlatformMarketDO = {},wmScSchoolCoPlatformMarketDOUpdate = {} "
                        , JSONObject.toJSONString(wmScSchoolCoPlatformMarketDOUpdateList.get(i)), JSONObject.toJSONString(wmScSchoolCoPlatformMarketDOList.get(i)), e);
                throw new WmSchCantException(e.getCode(), e.getMsg());
            }
            if (CollectionUtils.isNotEmpty(diffList)) {
                return false;
            }
        }

        return true;

    }

    /**
     * 设置学校平台合作信息修改DO
     * @param wmScSchoolCoPlatformSaveDTO wmScSchoolCoPlatformSaveDTO
     * @param wmScSchoolCoPlatformDOBefore wmScSchoolCoPlatformDOBefore
     * @param wmScSchoolCoPlatformDOUpdate wmScSchoolCoPlatformDOUpdate
     */
    private void composeSchoolPlarformUpdate(WmScSchoolCoPlatformSaveDTO wmScSchoolCoPlatformSaveDTO,
                                                             WmScSchoolCoPlatformDO wmScSchoolCoPlatformDOBefore,
                                                             WmScSchoolCoPlatformDO wmScSchoolCoPlatformDOUpdate) {
        // 平台名称
        if (!wmScSchoolCoPlatformDOBefore.getPlatformName().equals(wmScSchoolCoPlatformSaveDTO.getPlatformName())) {
            wmScSchoolCoPlatformDOUpdate.setPlatformName(wmScSchoolCoPlatformSaveDTO.getPlatformName());
        }
        // 校内商家单量
        if (!wmScSchoolCoPlatformDOBefore.getSchoolInPoiOrderCount().equals(wmScSchoolCoPlatformSaveDTO.getSchoolInPoiOrderCount())) {
            wmScSchoolCoPlatformDOUpdate.setSchoolInPoiOrderCount(wmScSchoolCoPlatformSaveDTO.getSchoolInPoiOrderCount());
        }
        // 校外商家单量
        if (!wmScSchoolCoPlatformDOBefore.getSchoolOutPoiOrderCount().equals(wmScSchoolCoPlatformSaveDTO.getSchoolOutPoiOrderCount())) {
            wmScSchoolCoPlatformDOUpdate.setSchoolOutPoiOrderCount(wmScSchoolCoPlatformSaveDTO.getSchoolOutPoiOrderCount());
        }
        // 校内在线商家数
        if (!wmScSchoolCoPlatformDOBefore.getSchoolInOnlinePoiCount().equals(wmScSchoolCoPlatformSaveDTO.getSchoolInOnlinePoiCount())) {
            wmScSchoolCoPlatformDOUpdate.setSchoolInOnlinePoiCount(wmScSchoolCoPlatformSaveDTO.getSchoolInOnlinePoiCount());
        }
        // 校外在线商家数
        if (!wmScSchoolCoPlatformDOBefore.getSchoolOutOnlinePoiCount().equals(wmScSchoolCoPlatformSaveDTO.getSchoolOutOnlinePoiCount())) {
            wmScSchoolCoPlatformDOUpdate.setSchoolOutOnlinePoiCount(wmScSchoolCoPlatformSaveDTO.getSchoolOutOnlinePoiCount());
        }
        // 合作平台的收费方式其他内容
        if (!wmScSchoolCoPlatformDOBefore.getDeliveryFeeTypeInfo().equals(wmScSchoolCoPlatformSaveDTO.getDeliveryFeeTypeInfo())) {
            wmScSchoolCoPlatformDOUpdate.setDeliveryFeeTypeInfo(wmScSchoolCoPlatformSaveDTO.getDeliveryFeeTypeInfo());
        }
        // 美团收费和合作平台对比
        if (!wmScSchoolCoPlatformDOBefore.getCompareCooperationPlatform().equals(wmScSchoolCoPlatformSaveDTO.getCompareCooperationPlatform())) {
            wmScSchoolCoPlatformDOUpdate.setCompareCooperationPlatform(wmScSchoolCoPlatformSaveDTO.getCompareCooperationPlatform());
        }
        // 当美团收费和合作平台对比选择了“无法比较”时填写的内容
        if (!wmScSchoolCoPlatformDOBefore.getCompareCooperationPlatformInfo().equals(wmScSchoolCoPlatformSaveDTO.getCompareCooperationPlatformInfo())) {
            wmScSchoolCoPlatformDOUpdate.setCompareCooperationPlatformInfo(wmScSchoolCoPlatformSaveDTO.getCompareCooperationPlatformInfo());
        }
        // 合作平台是否可进校
        if (!wmScSchoolCoPlatformDOBefore.getPlatformAllowToSchool().equals(wmScSchoolCoPlatformSaveDTO.getPlatformAllowToSchool())) {
            wmScSchoolCoPlatformDOUpdate.setPlatformAllowToSchool(wmScSchoolCoPlatformSaveDTO.getPlatformAllowToSchool());
        }
        // 合作平台支持送餐上楼
        if (!wmScSchoolCoPlatformDOBefore.getSupportFoodUpstairs().equals(wmScSchoolCoPlatformSaveDTO.getSupportFoodUpstairs())) {
            wmScSchoolCoPlatformDOUpdate.setSupportFoodUpstairs(wmScSchoolCoPlatformSaveDTO.getSupportFoodUpstairs());
        }
        // 当合作平台支持送餐上楼选择了“其他”时填写的内容
        if (!wmScSchoolCoPlatformDOBefore.getSupportFoodUpstairsInfo().equals(wmScSchoolCoPlatformSaveDTO.getSupportFoodUpstairsInfo())) {
            wmScSchoolCoPlatformDOUpdate.setSupportFoodUpstairsInfo(wmScSchoolCoPlatformSaveDTO.getSupportFoodUpstairsInfo());
        }
        // 送餐上楼费用
        if (!wmScSchoolCoPlatformDOBefore.getFoodUpstairsFee().equals(wmScSchoolCoPlatformSaveDTO.getFoodUpstairsFee())) {
            wmScSchoolCoPlatformDOUpdate.setFoodUpstairsFee(wmScSchoolCoPlatformSaveDTO.getFoodUpstairsFee());
        }
        // 当送餐上楼原因选择了其他时填写的内容
        if (!wmScSchoolCoPlatformDOBefore.getFoodUpstairsReasonInfo().equals(wmScSchoolCoPlatformSaveDTO.getFoodUpstairsReasonInfo())) {
            wmScSchoolCoPlatformDOUpdate.setFoodUpstairsReasonInfo(wmScSchoolCoPlatformSaveDTO.getFoodUpstairsReasonInfo());
        }
        // 合作平台与学校达成合作时间(年-月)
        if (!wmScSchoolCoPlatformDOBefore.getPlatformEstablishTime().equals(wmScSchoolCoPlatformSaveDTO.getPlatformEstablishTime())) {
            wmScSchoolCoPlatformDOUpdate.setPlatformEstablishTime(wmScSchoolCoPlatformSaveDTO.getPlatformEstablishTime());
        }
        // 当合作平台的优势选择了“有用户增值服务（如代取快递）”填写的内容
        if (!wmScSchoolCoPlatformDOBefore.getAdvantageAddedServiceInfo().equals(wmScSchoolCoPlatformSaveDTO.getAdvantageAddedServiceInfo())) {
            wmScSchoolCoPlatformDOUpdate.setAdvantageAddedServiceInfo(wmScSchoolCoPlatformSaveDTO.getAdvantageAddedServiceInfo());
        }
        // 当合作平台的优势选择了“配送体验好（时效等）”填写的内容
        if (!wmScSchoolCoPlatformDOBefore.getAdvantageGoodExperienceInfo().equals(wmScSchoolCoPlatformSaveDTO.getAdvantageGoodExperienceInfo())) {
            wmScSchoolCoPlatformDOUpdate.setAdvantageGoodExperienceInfo(wmScSchoolCoPlatformSaveDTO.getAdvantageGoodExperienceInfo());
        }
        // 当合作平台的优势选择了“有吸引力的C端活动（如发券）”填写的内容
        if (!wmScSchoolCoPlatformDOBefore.getAdvantageAttractionInfo().equals(wmScSchoolCoPlatformSaveDTO.getAdvantageAttractionInfo())) {
            wmScSchoolCoPlatformDOUpdate.setAdvantageAttractionInfo(wmScSchoolCoPlatformSaveDTO.getAdvantageAttractionInfo());
        }
        // 当合作平台的优势选择了“宣传力度大（如私域流量，扫楼等）”填写的内容
        if (!wmScSchoolCoPlatformDOBefore.getAdvantagePropagandaInfo().equals(wmScSchoolCoPlatformSaveDTO.getAdvantagePropagandaInfo())) {
            wmScSchoolCoPlatformDOUpdate.setAdvantagePropagandaInfo(wmScSchoolCoPlatformSaveDTO.getAdvantagePropagandaInfo());
        }
        // 合作平台的优势是其他时填写的内容
        if (!wmScSchoolCoPlatformDOBefore.getAdvantageExtraInfo().equals(wmScSchoolCoPlatformSaveDTO.getAdvantageExtraInfo())) {
            wmScSchoolCoPlatformDOUpdate.setAdvantageExtraInfo(wmScSchoolCoPlatformSaveDTO.getAdvantageExtraInfo());
        }
        // 供给分布
        if (!wmScSchoolCoPlatformDOBefore.getSupplyDistribution().equals(wmScSchoolCoPlatformSaveDTO.getSupplyDistribution())) {
            wmScSchoolCoPlatformDOUpdate.setSupplyDistribution(wmScSchoolCoPlatformSaveDTO.getSupplyDistribution());
        }
        // 垄断形式
        if (!wmScSchoolCoPlatformDOBefore.getMonopolyForm().equals(wmScSchoolCoPlatformSaveDTO.getMonopolyForm())) {
            wmScSchoolCoPlatformDOUpdate.setMonopolyForm(wmScSchoolCoPlatformSaveDTO.getMonopolyForm());
        }
        // 其他信息
        if (!wmScSchoolCoPlatformDOBefore.getExtraInfo().equals(wmScSchoolCoPlatformSaveDTO.getExtraInfo())) {
            wmScSchoolCoPlatformDOUpdate.setExtraInfo(wmScSchoolCoPlatformSaveDTO.getExtraInfo());
        }

        // 合作平台的收费方式
        List<WmScSchoolCoPlatformFeeTypeSaveDTO> deliveryFeeTypeList = wmScSchoolCoPlatformSaveDTO.getDeliveryFeeType();
        String deliveryFeeType = CollectionUtils.isEmpty(deliveryFeeTypeList) ? "" : JSONObject.toJSONString(deliveryFeeTypeList);
        if (!deliveryFeeType.equals(wmScSchoolCoPlatformDOBefore.getDeliveryFeeType())) {
            wmScSchoolCoPlatformDOUpdate.setDeliveryFeeType(deliveryFeeType);
        }

        // 送餐上楼原因
        List<Integer> reasonList = wmScSchoolCoPlatformSaveDTO.getFoodUpstairsReason();
        String foodUpstairsReason = CollectionUtils.isEmpty(reasonList) ? "" : StringUtils.join(reasonList, ",");
        if (!foodUpstairsReason.equals(wmScSchoolCoPlatformDOBefore.getFoodUpstairsReason())) {
            wmScSchoolCoPlatformDOUpdate.setFoodUpstairsReason(foodUpstairsReason);
        }

        // 合作平台的优势
        List<Integer> advantageList = wmScSchoolCoPlatformSaveDTO.getPlatformEstablishAdvantage();
        String establishAdvantage = CollectionUtils.isEmpty(advantageList) ? "" : StringUtils.join(advantageList, ",");
        if (!establishAdvantage.equals(wmScSchoolCoPlatformDOBefore.getPlatformEstablishAdvantage())) {
            wmScSchoolCoPlatformDOUpdate.setPlatformEstablishAdvantage(establishAdvantage);
        }

    }

    /**
     * 设置学校平台合作信息修改DO
     *
     * @param wmScSchoolCoPlatformSaveDTO  wmScSchoolCoPlatformSaveDTO
     * @param wmScSchoolCoPlatformDOBefore wmScSchoolCoPlatformDOBefore
     * @param wmScSchoolCoPlatformDOUpdate wmScSchoolCoPlatformDOUpdate
     */
    private void composeSchoolPlarformMarketUpdate(WmScSchoolCoPlatformSaveDTO wmScSchoolCoPlatformSaveDTO,
                                             WmScSchoolCoPlatformDO wmScSchoolCoPlatformDOBefore,
                                             WmScSchoolCoPlatformDO wmScSchoolCoPlatformDOUpdate) {
        List<WmScSchoolCoPlatformMarketDO> wmScSchoolCoPlatformMarketDOBeforeList = wmScSchoolCoPlatformDOBefore.getWmScSchoolCoPlatformMarketDOList();
        List<WmScSchoolCoPlatformMarketDO> wmScSchoolCoPlatformMarketDOUpdateList = wmScSchoolCoPlatformDOUpdate.getWmScSchoolCoPlatformMarketDOList();

        // 对齐修改前后的营销活动信息，缺失的数据用初始化的对象替代
        if (CollectionUtils.isEmpty(wmScSchoolCoPlatformMarketDOUpdateList)) {
            return;
        }
        if (CollectionUtils.isEmpty(wmScSchoolCoPlatformMarketDOBeforeList)) {
            for (WmScSchoolCoPlatformMarketDO wmScSchoolCoPlatformMarketDo : wmScSchoolCoPlatformMarketDOUpdateList) {
                wmScSchoolCoPlatformMarketDOBeforeList.add(new WmScSchoolCoPlatformMarketDO());
            }
            return;
        }
        for (int i = 0; i < wmScSchoolCoPlatformMarketDOBeforeList.size(); i++) {
            if (i >= wmScSchoolCoPlatformMarketDOUpdateList.size()) {
                wmScSchoolCoPlatformMarketDOUpdateList.add(new WmScSchoolCoPlatformMarketDO());
            }
            if (!wmScSchoolCoPlatformMarketDOBeforeList.get(i).getId().equals(wmScSchoolCoPlatformMarketDOUpdateList.get(i).getId())) {
                wmScSchoolCoPlatformMarketDOUpdateList.add(i, new WmScSchoolCoPlatformMarketDO());
            }
        }
        for (int i = wmScSchoolCoPlatformMarketDOBeforeList.size(); i < wmScSchoolCoPlatformMarketDOUpdateList.size(); i++) {
            wmScSchoolCoPlatformMarketDOBeforeList.add(new WmScSchoolCoPlatformMarketDO());
        }
    }

    /**
     * 删除学校平台合作信息(单条)V2
     * @param id 平台合作信息主键ID
     * @param userId 用户ID
     * @param userName 用户名称
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     * @throws TException org.apache.thrift.TException
     */
    public void deleteSchoolPlatformByIdV2(Integer id, Integer userId, String userName) throws WmSchCantException, TException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_2", "com.sankuai.meituan.waimai.customer.service.sc.WmScSchoolCoPlatformService.deleteSchoolPlatformByIdV2(java.lang.Integer,java.lang.Integer,java.lang.String)");
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_2", "com.sankuai.meituan.waimai.customer.service.sc.WmScSchoolCoPlatformService.deleteSchoolPlatformByIdV2(java.lang.Integer,java.lang.Integer,java.lang.String)");
        log.info("[WmScSchoolPlatformService.deleteSchoolPlatformByIdV2] input param: primaryId = {}, userId = {}, userName = {}",
                id, userId, userName);
        if (id == null || id <= 0) {
            log.error("[WmScSchoolPlatformService.deleteSchoolPlatformByIdV2] primary id is null.");
            throw new WmSchCantException(BIZ_PARA_ERROR, "请求参数为空");
        }

        WmScSchoolCoPlatformDO wmScSchoolCoPlatformDO = wmScSchoolCoPlatformMapper.selectByPrimaryKey(id.longValue());
        if (wmScSchoolCoPlatformDO == null) {
            log.error("[WmScSchoolPlatformService.deleteSchoolPlatformByIdV2] wmScSchoolPlatformDO id is null.]");
            throw new WmSchCantException(BIZ_PARA_ERROR, "平台合作信息不存在");
        }
        List<WmScSchoolCoPlatformMarketDO> wmScSchoolCoPlatformMarketDOList = wmScSchoolCoPlatformMarketMapper.selectByPlatformPrimaryId(wmScSchoolCoPlatformDO.getId());
        if (CollectionUtils.isEmpty(wmScSchoolCoPlatformMarketDOList)) {
            log.error("[WmScSchoolPlatformService.deleteSchoolPlatformByIdV2] wmScSchoolCoPlatformMarketDoList id is null.]");
            throw new WmSchCantException(BIZ_PARA_ERROR, "平台合作信息营销活动列表不存在");
        }
        wmScSchoolCoPlatformDO.setWmScSchoolCoPlatformMarketDOList(wmScSchoolCoPlatformMarketDOList);

        // 执行逻辑删除
        log.info("[WmScSchoolPlatformService.deleteSchoolPlatformByIdV2] primaryKey = {}", id.longValue());
        int result = wmScSchoolCoPlatformMapper.invalidByPrimaryKey(id.longValue(), userId.longValue());
        // 逻辑删除子表
        log.info("[WmScSchoolPlatformService.deleteSchoolPlatformByIdV2] wmScSchoolCoPlatformMarketDoList = {}", wmScSchoolCoPlatformDO.getWmScSchoolCoPlatformMarketDOList());
        int market_result = wmScSchoolCoPlatformMarketMapper.batchInvalidByPrimaryKey(wmScSchoolCoPlatformDO.getWmScSchoolCoPlatformMarketDOList(), userId.longValue());
        if (result > 0 && market_result > 0 ) {
            // 记录操作日志
            String logInfo = wmScLogSchoolInfoService.composeSchoolCoPlatformDeleteLog(wmScSchoolCoPlatformDO);
            if (StringUtils.isNotBlank(logInfo)) {
                wmScLogSchoolInfoService.insertScOptLog(OptTypeEnum.DELETE.getType(), SC_SCHOOL_PLATFORM_LOG, wmScSchoolCoPlatformDO.getSchoolPrimaryId(),
                        userId, userName, logInfo, "");
            }
        }
    }

    /**
     * 获取学校平台合作信息列表V2
     * @param schoolPrimaryId 学校主键ID
     * @return 平台合作信息列表
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     * @throws TException org.apache.thrift.TException
     */
    public List<WmScSchoolCoPlatformDTO> getSchoolPlatformListV2(Integer schoolPrimaryId) throws WmSchCantException, TException {
        log.info("[WmScSchoolPlatformService.getSchoolPlatformListV2] input param: schoolPrimaryId = {}", schoolPrimaryId);
        if (schoolPrimaryId == null || schoolPrimaryId <= 0) {
            log.error("[WmScSchoolPlatformService.getSchoolPlatformListV2] schoolPrimaryId error.");
            throw new WmSchCantException(BIZ_PARA_ERROR, "学校主键ID为空");
        }

        List<WmScSchoolCoPlatformDTO> wmScSchoolCoPlatformDTOList = new ArrayList<>();
        List<WmScSchoolCoPlatformDO> wmScSchoolCoPlatformDOList = wmScSchoolCoPlatformMapper.selectBySchoolPrimaryId(schoolPrimaryId);
        if (CollectionUtils.isEmpty(wmScSchoolCoPlatformDOList)) {
            return wmScSchoolCoPlatformDTOList;
        }
        for (WmScSchoolCoPlatformDO wmScSchoolCoPlatformDO : wmScSchoolCoPlatformDOList) {
            List<WmScSchoolCoPlatformMarketDO> wmScSchoolCoPlatformMarketDOList = wmScSchoolCoPlatformMarketMapper.selectByPlatformPrimaryId(wmScSchoolCoPlatformDO.getId());
            wmScSchoolCoPlatformDO.setWmScSchoolCoPlatformMarketDOList(wmScSchoolCoPlatformMarketDOList);
        }

        return WmScTransUtil.transSchoolCoPlatformDOListToDTOList(wmScSchoolCoPlatformDOList);
    }

}
