package com.sankuai.meituan.waimai.customer.service.customer.monitor;

import com.dianping.frog.sdk.alarm.DXUtil;
import com.dianping.frog.sdk.data.BinlogRawData;
import com.dianping.frog.sdk.data.DmlType;
import com.dianping.frog.sdk.data.RawData;
import com.dianping.frog.sdk.rpc.RpcService;
import com.dianping.frog.sdk.rpc.RpcServiceFactory;
import com.dianping.frog.sdk.runner.DefaultRuleRunner;
import com.dianping.frog.sdk.util.BcpLoggerUtils;
import com.dianping.lion.client.util.JsonUtils;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.meituan.service.mobile.mtthrift.util.json.JacksonUtils;
import org.apache.commons.lang3.StringUtils;

import java.lang.reflect.Array;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Map;

public class WmCustomerPoiBindRuleRunner extends DefaultRuleRunner {
    @Override
    public boolean filter(RawData rawData, RawData... targetData) throws Exception {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.customer.monitor.WmCustomerPoiBindRuleRunner.filter(com.dianping.frog.sdk.data.RawData,com.dianping.frog.sdk.data.RawData[])");
        BinlogRawData binlogRawData = (BinlogRawData) rawData;
        //valid是失效的不做告警，放行
        Integer valid = null;
        if (binlogRawData.getColumnInfoMap().get("valid").getNewValue().toString() != null){
            valid = Integer.parseInt(binlogRawData.getColumnInfoMap().get("valid").getNewValue().toString());
        }
        if (valid == null || valid == 0) {
            return false;
        }
        //对插入/更新操作进行check
        return binlogRawData.getDmlType() == DmlType.UPDATE || binlogRawData.getDmlType() == DmlType.INSERT;
    }

    @Override
    public String check(RawData rawData, RawData... rawData2) throws Exception {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.service.customer.monitor.WmCustomerPoiBindRuleRunner.check(com.dianping.frog.sdk.data.RawData,com.dianping.frog.sdk.data.RawData[])");
        BinlogRawData binlogRawData = (BinlogRawData) rawData;

        Integer customerId = Integer.parseInt(binlogRawData.getColumnInfoMap().get("customer_id").getNewValue().toString());
        Long wmPoiId = Long.parseLong(binlogRawData.getColumnInfoMap().get("wm_poi_id").getNewValue().toString());
        try {
            Map<String, Object> params = Maps.newHashMap();
            params.put("customerId", customerId);
            params.put("wmPoiIdList", new ArrayList<Long>(Arrays.asList(wmPoiId)));
            RpcService rpcService = RpcServiceFactory.getThriftInstance("com.sankuai.meituan.waimai.thrift.customer.service.WmCustomerPoiThriftService",
                    "com.sankuai.waimai.e.customer", 10000, null, "8435");
            String result = rpcService.invoke("monitorPoiBind",
                    Lists.newArrayList("com.sankuai.meituan.waimai.thrift.customer.domain.customer.monitor.MonitorCustomerPoiDTO"),
                    Lists.newArrayList(JsonUtils.toJson(params)));
            if (!StringUtils.isBlank(result) && !"\"\"".equals(result) && !"null".equals(result)) {
                return result;
            }
        } catch (Exception e) {
            BcpLoggerUtils.info("类型转换失败:msg={}" + JacksonUtils.serialize(binlogRawData) + ";异常日志:" + e.getStackTrace());    // 打印日志
        }
        return null;
    }

    @Override
    public void alarm(String s, RawData rawData, RawData... rawData2) throws Exception {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_1", "com.sankuai.meituan.waimai.customer.service.customer.monitor.WmCustomerPoiBindRuleRunner.alarm(java.lang.String,com.dianping.frog.sdk.data.RawData,com.dianping.frog.sdk.data.RawData[])");
        //通过大象公众号发送告警消息, 收件人为告警配置中的告警接收人。
        DXUtil.sendAlarm(s);
    }

}
