package com.sankuai.meituan.waimai.customer.service.customer.monitor;

import com.dianping.frog.sdk.alarm.DXUtil;
import com.dianping.frog.sdk.data.BinlogRawData;
import com.dianping.frog.sdk.data.DmlType;
import com.dianping.frog.sdk.data.RawData;
import com.dianping.frog.sdk.rpc.RpcService;
import com.dianping.frog.sdk.rpc.RpcServiceFactory;
import com.dianping.frog.sdk.runner.DefaultRuleRunner;
import com.dianping.frog.sdk.util.BcpLoggerUtils;
import com.dianping.frog.sdk.util.JsonUtils;
import com.dianping.frog.sdk.util.StringUtils;
import com.dianping.lion.shade.org.apache.curator.shaded.com.google.common.collect.Lists;
import com.dianping.lion.shade.org.apache.curator.shaded.com.google.common.collect.Maps;
import com.meituan.service.mobile.mtthrift.util.json.JacksonUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Map;

public class WmCustomerBindRuleRunner extends DefaultRuleRunner {
    @Override
    public boolean filter(RawData rawData, RawData... targetData) throws Exception {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.service.customer.monitor.WmCustomerBindRuleRunner.filter(com.dianping.frog.sdk.data.RawData,com.dianping.frog.sdk.data.RawData[])");
        BinlogRawData binlogRawData = (BinlogRawData) rawData;
        //valid是失效的不做告警，放行
        Integer valid = null;
        if (binlogRawData.getColumnInfoMap().get("valid").getNewValue().toString() != null){
            valid = Integer.parseInt(binlogRawData.getColumnInfoMap().get("valid").getNewValue().toString());
        }
        if (valid == null || valid == 0) {
            return false;
        }
        Integer customerRealType = null;
        if (binlogRawData.getColumnInfoMap().get("customer_real_type").getNewValue().toString() != null){
            customerRealType = Integer.parseInt(binlogRawData.getColumnInfoMap().get("customer_real_type").getNewValue().toString());
        }
        // 只看单店
        if (valid == null || (customerRealType != 1 && customerRealType != 16 && customerRealType != 20)) {
            return false;
        }
        //对更新操作进行check
        return binlogRawData.getDmlType() == DmlType.UPDATE;
    }
    @Override
    public String check(RawData rawData, RawData... rawData2) throws Exception {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_2", "com.sankuai.meituan.waimai.customer.service.customer.monitor.WmCustomerBindRuleRunner.check(com.dianping.frog.sdk.data.RawData,com.dianping.frog.sdk.data.RawData[])");
        BinlogRawData binlogRawData = (BinlogRawData) rawData;
        Integer customerId = Integer.parseInt(binlogRawData.getColumnInfoMap().get("id").getNewValue().toString());
        Integer customerRealTypeNew = null;
        if (binlogRawData.getColumnInfoMap().get("customer_real_type").getNewValue() != null) {
            customerRealTypeNew = Integer.valueOf(binlogRawData.getColumnInfoMap().get("customer_real_type").getNewValue().toString());
        } else {
            return null;
        }
        try {
            Map<String, Object> params = Maps.newHashMap();
            params.put("customerId", customerId);
            params.put("customerRealTypeNew", customerRealTypeNew);
            RpcService rpcService = RpcServiceFactory.getThriftInstance("com.sankuai.meituan.waimai.thrift.customer.service.WmCustomerThriftServiceImpl",
                    "com.sankuai.waimai.e.customer", 10000, null, "8433");
            String result = rpcService.invoke("monitorCustomerRealType",
                    Lists.newArrayList("com.sankuai.meituan.waimai.thrift.customer.domain.customer.monitor.MonitorCustomerDTO"),
                    Lists.newArrayList(JsonUtils.toJson(params)));
            if (!StringUtils.isBlank(result) && !"\"\"".equals(result) && !"null".equals(result)) {
                return result;
            }
        } catch (Exception e) {
            BcpLoggerUtils.info("类型转换失败:msg={}" + JacksonUtils.serialize(binlogRawData) + ";异常日志:" + e.getStackTrace());    // 打印日志
        }
        return null;
    }
    @Override
    public void alarm(String s, RawData rawData, RawData... rawData2) throws Exception {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.service.customer.monitor.WmCustomerBindRuleRunner.alarm(java.lang.String,com.dianping.frog.sdk.data.RawData,com.dianping.frog.sdk.data.RawData[])");
        //通过大象公众号发送告警消息, 收件人为告警配置中的告警接收人。
        DXUtil.sendAlarm(s);
    }
}
