package com.sankuai.meituan.waimai.customer.service.sc;

import com.sankuai.meituan.waimai.customer.dao.sc.WmScEnumDictMapper;
import com.sankuai.meituan.waimai.customer.domain.sc.WmScEnumDictDO;
import com.sankuai.meituan.waimai.thrift.customer.domain.sc.WmScSchoolCooperationPlatformDTO;
import com.sankuai.meituan.waimai.thrift.exception.WmSchCantException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

import static com.sankuai.meituan.waimai.thrift.customer.constant.sc.WmScCodeConstants.BIZ_PARA_ERROR;

/**
 * 学校楼宇信息处理逻辑
 * <AUTHOR>
 * @date 2023/09/15
 * @email <EMAIL>
 */
@Slf4j
@Service
public class WmScSchoolPlatformService {
    /**
     * 学校平台合作信息-"其他"codeV2
     */
    public static final int SCHOOL_PLATFROM_EXTRA_CODE_V2 = 999;

    @Autowired
    private WmScEnumDictMapper wmScEnumDictMapper;

    /**
     * 模糊查询学校合作平台信息列表
     * @param content 模糊搜索内容
     * @return  合作平台信息列表
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     * @throws TException org.apache.thrift.TException
     */
    public List<WmScSchoolCooperationPlatformDTO> getSchoolCooperationPlatformListByFuzzySearch(String content)
            throws WmSchCantException, TException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_1", "com.sankuai.meituan.waimai.customer.service.sc.WmScSchoolPlatformService.getSchoolCooperationPlatformListByFuzzySearch(java.lang.String)");
        log.info("[WmScSchoolService.getSchoolCooperationPlatformListByFuzzySearch] input param: content = {}", content);
        if (StringUtils.isBlank(content)) {
            throw new WmSchCantException(BIZ_PARA_ERROR, "查询内容为空");
        }

        List<WmScSchoolCooperationPlatformDTO> dtoList = new ArrayList<>();
        List<WmScEnumDictDO> doList = wmScEnumDictMapper.selectCooperationPlatformByDescWithFuzzy(content);
        if (CollectionUtils.isEmpty(doList)) {
            return dtoList;
        }

        for (WmScEnumDictDO wmScEnumDictDO : doList) {
            WmScSchoolCooperationPlatformDTO dto = new WmScSchoolCooperationPlatformDTO();
            dto.setCooperationPlatformId(wmScEnumDictDO.getEnumCode());
            dto.setCooperationPlatformName(wmScEnumDictDO.getEnumDesc());
            dtoList.add(dto);
        }
        return dtoList;
    }


}
