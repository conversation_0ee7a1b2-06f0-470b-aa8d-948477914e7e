package com.sankuai.meituan.waimai.customer.service.customer;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Function;
import com.google.common.collect.Lists;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.google.common.util.concurrent.Uninterruptibles;
import com.meituan.mtrace.thread.TraceRunnable;
import com.sankuai.meituan.util.ConfigUtilAdapter;
import com.sankuai.meituan.waimai.customer.config.MccCustomerConfig;
import com.sankuai.meituan.waimai.customer.dao.WmCustomerPoiAttributeMapper;
import com.sankuai.meituan.waimai.customer.ddd.domain.customer.aggre.WmCustomerPoiAggre;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerDB;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerPoiAttributeDO;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerPoiAttributeSearchCondition;
import com.sankuai.meituan.waimai.customer.service.kp.WmCustomerKpService;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerConstants;
import com.sankuai.meituan.waimai.thrift.customer.constant.KpConstants;
import com.sankuai.meituan.waimai.thrift.customer.constant.KpSignerStateMachine;
import com.sankuai.meituan.waimai.thrift.customer.constant.KpTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.WmCustomerPoiAttributeResultDTO;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.WmCustomerPoiAttributeSearchConditionDTO;
import com.sankuai.meituan.waimai.thrift.customer.domain.kp.WmCustomerKp;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Nullable;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * 门店客户属性业务逻辑
 */
@Slf4j
@Service
public class WmCustomerPoiAttributeService {


    @Autowired
    private WmCustomerPoiAttributeMapper wmCustomerPoiAttributeMapper;

    @Autowired
    private WmCustomerService wmCustomerService;

    @Autowired
    private WmCustomerKpService wmCustomerKpService;


    private final String LOG_PREFIX = "WmCustomerPoiAttributeService";

    private final Integer BATCH_INSERT_SIZE = 100;

    private ExecutorService executorService = new ThreadPoolExecutor(10, 30, 0L, TimeUnit.MILLISECONDS,
            new LinkedBlockingQueue<Runnable>(100),
            new ThreadFactoryBuilder().setNameFormat("WmCustomerPoiAttributeService" + "-pool-%d").build());

    /**
     * 更新客户门店属性信息（签约人KP信息变更时调用）
     *
     * @param wmCustomerKp           KP信息
     * @param reSelectSignerKpFromDb 是否从DB中查询签约人KP
     */
    public void updateForKpUpdateAsy(WmCustomerKp wmCustomerKp, boolean reSelectSignerKpFromDb) {
        log.info("updateForKpUpdate wmCustomerKp={}", JSON.toJSONString(wmCustomerKp));
        try {
            if (wmCustomerKp == null
                    || wmCustomerKp.getCustomerId() <= 0
                    || KpTypeEnum.SIGNER.getType() != wmCustomerKp.getKpType()
                    || KpSignerStateMachine.EFFECT.getState() != wmCustomerKp.getState()) {
                //KP不合法或者非签约人或者非生效状态不处理
                return;
            }
            executorService.execute(new TraceRunnable(new Runnable() {
                @Override
                public void run() {
                    if (reSelectSignerKpFromDb) {
                        //重新查询签约人KP信息后根据查询的信息更新门店客户属性
                        updateSignerKp(wmCustomerKp.getCustomerId());
                    } else {
                        if (wmCustomerKp.getValid() == KpConstants.UN_VALID) {
                            //如果签约人已删除则再次查询一下生效签约人KP信息（防止有多条签约人KP信息）
                            updateSignerKp(wmCustomerKp.getCustomerId());
                        } else {
                            updateCustomerPoiAttributeSignerKp(wmCustomerKp.getCustomerId(), wmCustomerKp);
                        }
                    }
                }
            }));
        } catch (Exception e) {
            log.error("updateForKpUpdate kpId={}", wmCustomerKp.getId(), e);
        }
    }

    /**
     * 更新客户门店属性的签约人KP信息
     *
     * @param customerId
     */
    private void updateSignerKp(Integer customerId) {
        try {
            if (customerId == null || customerId <= 0) {
                return;
            }
            WmCustomerKp wmCustomerKpSigner = wmCustomerKpService.getCustomerKpOfEffectiveSignerRT(customerId);
            updateCustomerPoiAttributeSignerKp(customerId, wmCustomerKpSigner);
        } catch (Exception e) {
            log.error("updateSignerKp customerId={}", customerId, e);
        }
    }

    /**
     * 异步更新客户门店属性的签约人KP信息
     *
     * @param customerId
     */
    public void updateSignerKpAsy(Integer customerId) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.service.customer.WmCustomerPoiAttributeService.updateSignerKpAsy(java.lang.Integer)");
        try {
            executorService.execute(new TraceRunnable(new Runnable() {
                @Override
                public void run() {
                    updateSignerKp(customerId);
                }
            }));

        } catch (Exception e) {
            log.error("updateSignerKp customerId={}", customerId, e);
        }
    }

    /**
     * 更新客户门店属性记录中的签约人KP信息
     *
     * @param wmCustomerKpSigner
     */
    private void updateCustomerPoiAttributeSignerKp(int customerId, WmCustomerKp wmCustomerKpSigner) {
        List<WmCustomerPoiAttributeDO> list = searchEffectAttribute((long) customerId, false);
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        if (wmCustomerKpSigner == null) {
            //KP不存在生效的签约人信息则清空客户门店属性信息上的签约人信息
            cleanSignerKp(list);
            return;
        }
        boolean cusTrafficSpikeSwitch = MccCustomerConfig.getCustomerTrafficSpikeSwitch();
        for (WmCustomerPoiAttributeDO wmCustomerPoiAttributeDo : list) {
            // 如果存在变化的签约人KP信息
            boolean flag = !(wmCustomerKpSigner.getCompellation().equals(wmCustomerPoiAttributeDo.getKpCompellation())
                    && wmCustomerKpSigner.getPhoneNumToken().equals(wmCustomerPoiAttributeDo.getPhoneNumToken())
                    && wmCustomerKpSigner.getPhoneNumEncryption().equals(wmCustomerPoiAttributeDo.getPhoneNumEncryption()));
            if (flag) {
                // 限制更新customer_poi_attribute表的写QPS, 目前最大为500
                if (cusTrafficSpikeSwitch) {
                    Uninterruptibles.sleepUninterruptibly(2, TimeUnit.MILLISECONDS);
                }
                doUpdateSignKp(wmCustomerPoiAttributeDo, wmCustomerKpSigner);
            } else {
                return;
            }
        }
    }

    /**
     * 清空客户门店属性的KP信息
     *
     * @param list
     */
    private void cleanSignerKp(List<WmCustomerPoiAttributeDO> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        for (WmCustomerPoiAttributeDO wmCustomerPoiAttributeDo : list) {
            doCleanSignKp(wmCustomerPoiAttributeDo);
        }
    }

    /**
     * 清空客户门店属性的签约人信息
     *
     * @param wmCustomerPoiAttributeDo
     */
    private void doCleanSignKp(WmCustomerPoiAttributeDO wmCustomerPoiAttributeDo) {
        if (wmCustomerPoiAttributeDo == null) {
            return;
        }
        wmCustomerPoiAttributeDo.setKpCompellation("");
        wmCustomerPoiAttributeDo.setPhoneNumToken("");
        wmCustomerPoiAttributeDo.setPhoneNumEncryption("");
        wmCustomerPoiAttributeMapper.updateByPrimaryKeySelective(wmCustomerPoiAttributeDo);
    }

    /**
     * 更新客户门店属性的签约人信息
     *
     * @param wmCustomerPoiAttributeDo
     * @param wmCustomerKpSigner
     */
    private void doUpdateSignKp(WmCustomerPoiAttributeDO wmCustomerPoiAttributeDo, WmCustomerKp wmCustomerKpSigner) {
        if (wmCustomerPoiAttributeDo == null || wmCustomerKpSigner == null) {
            return;
        }
        wmCustomerPoiAttributeDo.setKpCompellation(wmCustomerKpSigner.getCompellation());
        wmCustomerPoiAttributeDo.setPhoneNumToken(wmCustomerKpSigner.getPhoneNumToken());
        wmCustomerPoiAttributeDo.setPhoneNumEncryption(wmCustomerKpSigner.getPhoneNumEncryption());
        wmCustomerPoiAttributeMapper.updateByPrimaryKeySelective(wmCustomerPoiAttributeDo);
    }


    /**
     * 删除客户门店属性(客户门店解绑时调用)
     *
     * @param customerId
     * @param wmPoiId
     */
    public void deleteCustomerPoiAttribute(Integer customerId, Long wmPoiId) {
        log.info("{} deleteCustomerPoiAttribute customerId={},wmPoiId={}", LOG_PREFIX, customerId, wmPoiId);
        try {
            if (customerId == null || customerId <= 0 || wmPoiId == null || wmPoiId <= 0) {
                return;
            }
            wmCustomerPoiAttributeMapper.deleteByWmPoiIdAndCustomerId(wmPoiId, customerId);
        } catch (Exception e) {
            log.error("deleteCustomerPoiAttribute::customerId = {}, wmPoiId = {}", customerId, wmPoiId, e);
        }
    }
    /**
     * 删除客户门店属性
     *
     * @param id
     */
    public void deleteCustomerPoiAttribute(Long id) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_1", "com.sankuai.meituan.waimai.customer.service.customer.WmCustomerPoiAttributeService.deleteCustomerPoiAttribute(java.lang.Long)");
        log.info("{} deleteCustomerPoiAttribute id={}", LOG_PREFIX, id);
        try {
            if (id == null || id <= 0) {
                return;
            }
            wmCustomerPoiAttributeMapper.deleteById(id);
        } catch (Exception e) {
            log.error("{} deleteCustomerPoiAttribute id={}", LOG_PREFIX, id, e);
        }
    }

    /**
     * 删除客户门店属性
     *
     * @param list
     */
    private void deleteCustomerPoiAttribute(List<WmCustomerPoiAttributeDO> list) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_1", "com.sankuai.meituan.waimai.customer.service.customer.WmCustomerPoiAttributeService.deleteCustomerPoiAttribute(java.util.List)");
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        for (WmCustomerPoiAttributeDO wmCustomerPoiAttributeDo : list) {
            if (wmCustomerPoiAttributeDo == null) {
                continue;
            }
            deleteCustomerPoiAttribute(wmCustomerPoiAttributeDo.getCustomerId().intValue(), wmCustomerPoiAttributeDo.getWmPoiId());
        }
    }

    /**
     * 删除客户门店属性(客户门店解绑时调用)
     *
     * @param customerId
     * @param wmPoiIdList
     */
    public void deleteCustomerPoiAttribute(Integer customerId, Set<Long> wmPoiIdList) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_1", "com.sankuai.meituan.waimai.customer.service.customer.WmCustomerPoiAttributeService.deleteCustomerPoiAttribute(java.lang.Integer,java.util.Set)");
        log.info("{} deleteCustomerPoiAttribute customerId={},wmPoiId={}", LOG_PREFIX, customerId, JSON.toJSONString(wmPoiIdList));
        try {
            if (customerId == null || customerId <= 0 || CollectionUtils.isEmpty(wmPoiIdList)) {
                return;
            }
            wmCustomerPoiAttributeMapper.deleteByWmPoiIdsAndCustomerId(wmPoiIdList, customerId);
        } catch (Exception e) {
            log.error("deleteCustomerPoiAttribute::customerId = {}, wmPoiIdList = {}", customerId, JSON.toJSONString(wmPoiIdList), e);
        }
    }

    /**
     * 新增或者修改客户门店属性（客户门店绑定时调用）
     *
     * @param customerId
     * @param wmPoiId
     */
    public void upsertCustomerPoiAttribute(Integer customerId, Long wmPoiId) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.service.customer.WmCustomerPoiAttributeService.upsertCustomerPoiAttribute(java.lang.Integer,java.lang.Long)");
        if (customerId == null || customerId <= 0 || wmPoiId == null || wmPoiId <= 0) {
            return;
        }
        Set<Long> wmPoiIdSet = new HashSet();
        wmPoiIdSet.add(wmPoiId);
        upsertCustomerPoiAttribute(customerId, wmPoiIdSet);
    }

    /**
     * 新增或者修改客户门店属性(增量变更)（客户门店绑定时调用）
     *
     * @param customerId
     * @param wmPoiIdSet
     */
    public void upsertCustomerPoiAttribute(Integer customerId, Set<Long> wmPoiIdSet) {
        log.info("{} insertCustomerPoiAttribute customerId={},wmPoiIdSet={}", LOG_PREFIX, customerId, JSON.toJSONString(wmPoiIdSet));
        try {
            if (customerId == null || customerId <= 0 || CollectionUtils.isEmpty(wmPoiIdSet)) {
                return;
            }
            WmCustomerDB wmCustomerDb = getCustomerRT(customerId);
            WmCustomerKp wmCustomerKpSigner = wmCustomerKpService.getCustomerKpOfEffectiveSignerRT(customerId);
            List<WmCustomerPoiAttributeDO> list = new ArrayList<>();
            for (Long wmPoiId : wmPoiIdSet) {
                if (wmPoiId == null || wmPoiId <= 0) {
                    continue;
                }
                WmCustomerPoiAttributeDO attribute = new WmCustomerPoiAttributeDO();
                attribute.setCustomerId(customerId.longValue());
                attribute.setMtCustomerId(wmCustomerDb == null ? 0 : wmCustomerDb.getMtCustomerId());
                attribute.setWmPoiId(wmPoiId);
                attribute.setKpCompellation(wmCustomerKpSigner == null ? "" : wmCustomerKpSigner.getCompellation());
                attribute.setPhoneNumEncryption(wmCustomerKpSigner == null ? "" : wmCustomerKpSigner.getPhoneNumEncryption());
                attribute.setPhoneNumToken(wmCustomerKpSigner == null ? "" : wmCustomerKpSigner.getPhoneNumToken());
                list.add(attribute);
            }
            List<List<WmCustomerPoiAttributeDO>> partitionList = Lists.partition(list, BATCH_INSERT_SIZE);
            for (List<WmCustomerPoiAttributeDO> partition : partitionList) {
                if (ConfigUtilAdapter.getBoolean("customer_poi_attribute_insert_switch", true)) {
                    wmCustomerPoiAttributeMapper.batchInsert(partition);
                } else {
                    /**
                     * 新增门店客户属性之前是否需要先删除门店客户属性
                     * 前期wm_customer_poi_attribute表中有valid字段同时存在valid=0的历史数据，所以插入之前需要先删除
                     */
                    List<Long> wmPoiIdList = Lists.transform(partition, new Function<WmCustomerPoiAttributeDO, Long>() {
                        @Nullable
                        @Override
                        public Long apply(@Nullable WmCustomerPoiAttributeDO wmCustomerPoiAttributeDO) {
                            return wmCustomerPoiAttributeDO.getWmPoiId();
                        }
                    });
                    if (ConfigUtilAdapter.getBoolean("delete_customer_poi_attribute_before", true)) {
                        wmCustomerPoiAttributeMapper.deleteByWmPoiIdsAndCustomerId(new HashSet<>(wmPoiIdList), customerId);
                    }
                    wmCustomerPoiAttributeMapper.batchInsertV2(partition);
                }
            }
        } catch (Exception e) {
            log.error("upsertCustomerPoiAttribute::customerId = {}, wmPoiIdList = {}", customerId, JSON.toJSONString(wmPoiIdSet), e);
        }
    }

    /**
     * 新逻辑--新增或者修改客户门店属性(增量变更)（客户门店绑定时调用）
     *
     * @param customerId
     * @param wmPoiIdSet
     */
    public void insertUpdateCustomerPoiAttribute(Integer customerId, Set<Long> wmPoiIdSet) throws WmCustomerException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_2", "com.sankuai.meituan.waimai.customer.service.customer.WmCustomerPoiAttributeService.insertUpdateCustomerPoiAttribute(java.lang.Integer,java.util.Set)");
        log.info("{} insertUpdateCustomerPoiAttribute customerId={},wmPoiIdSet={}", LOG_PREFIX, customerId, JSON.toJSONString(wmPoiIdSet));
        if (customerId == null || customerId <= 0 || CollectionUtils.isEmpty(wmPoiIdSet)) {
            return;
        }
        WmCustomerDB wmCustomerDb = getCustomerRT(customerId);
        WmCustomerKp wmCustomerKpSigner = wmCustomerKpService.getCustomerKpOfEffectiveSignerRT(customerId);
        for (Long wmPoiId : wmPoiIdSet) {
            if (wmPoiId == null || wmPoiId <= 0) {
                continue;
            }
            WmCustomerPoiAttributeDO attribute = new WmCustomerPoiAttributeDO();
            attribute.setCustomerId(customerId.longValue());
            attribute.setMtCustomerId(wmCustomerDb == null ? 0 : wmCustomerDb.getMtCustomerId());
            attribute.setWmPoiId(wmPoiId);
            attribute.setKpCompellation(wmCustomerKpSigner == null ? "" : wmCustomerKpSigner.getCompellation());
            attribute.setPhoneNumEncryption(wmCustomerKpSigner == null ? "" : wmCustomerKpSigner.getPhoneNumEncryption());
            attribute.setPhoneNumToken(wmCustomerKpSigner == null ? "" : wmCustomerKpSigner.getPhoneNumToken());
            List<Long> ids = wmCustomerPoiAttributeMapper.selectByPoiIdRT(wmPoiId);
            if (ids.size() == 0){
                wmCustomerPoiAttributeMapper.insert(attribute);
            } else if (ids.size() == 1){
                attribute.setId(ids.get(0));
                wmCustomerPoiAttributeMapper.updateByPrimaryKeySelective(attribute);
            } else {
                log.error("新增/修改客户门店属性异常，客户门店属性表门店id不唯一 customerId={},wmPoiIdSet={}", customerId, JSON.toJSONString(wmPoiIdSet));
            }
        }
    }

    /**
     * 查询客户
     *
     * @param customerPrimaryId
     * @return
     */
    private WmCustomerDB getCustomerRT(Integer customerPrimaryId) {
        try {
            if (customerPrimaryId != null || customerPrimaryId >= 0) {
                return wmCustomerService.selectCustomerByIdRT(customerPrimaryId);
            }
        } catch (Exception e) {
            log.error("{} getCustomerRT customerPrimaryId={}", LOG_PREFIX, JSON.toJSONString(customerPrimaryId), e);
        }
        return null;
    }

    /**
     * 查询客户属性
     *
     * @param customerId 客户物理主键ID
     * @param isRealTime 是否查询主库
     * @return
     */
    public List<WmCustomerPoiAttributeDO> searchEffectAttribute(Long customerId, boolean isRealTime) {
        if (customerId == null || customerId <= 0) {
            return Lists.newArrayList();
        }
        WmCustomerPoiAttributeSearchCondition condition = new WmCustomerPoiAttributeSearchCondition();
        condition.setCustomerId(customerId);
        if (isRealTime) {
            return wmCustomerPoiAttributeMapper.selectByConditionRT(condition);
        }
        return wmCustomerPoiAttributeMapper.selectByCondition(condition);
    }

    /**
     * 根据门店ID查询客户门店属性记录
     *
     * @param wmPoiId
     * @return
     */
    public List<WmCustomerPoiAttributeDO> searchEffectAttributeByWmPoiId(Long wmPoiId) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.customer.WmCustomerPoiAttributeService.searchEffectAttributeByWmPoiId(java.lang.Long)");
        if (wmPoiId == null || wmPoiId <= 0) {
            return null;
        }
        WmCustomerPoiAttributeSearchCondition condition = new WmCustomerPoiAttributeSearchCondition();
        condition.setWmPoiId(wmPoiId);
        return wmCustomerPoiAttributeMapper.selectByCondition(condition);
    }

    /**
     * 查询客户下绑定的门店数量和关联的食堂属性数量
     *
     * @param customerId
     * @return
     */
    public Map<String, Object> selectCustomerRelAttributeCount(Integer customerId) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_1", "com.sankuai.meituan.waimai.customer.service.customer.WmCustomerPoiAttributeService.selectCustomerRelAttributeCount(java.lang.Integer)");
        if (customerId == null) {
            return null;
        }
        return wmCustomerPoiAttributeMapper.selectCustomerPoiRelAttributeCount(customerId);
    }

    /**
     * 查询客户下绑定的门店属性数量
     *
     * @param customerId
     * @return
     */
    public int  countCustomerPoiRelAttribute(int customerId) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_1", "com.sankuai.meituan.waimai.customer.service.customer.WmCustomerPoiAttributeService.countCustomerPoiRelAttribute(int)");
        return wmCustomerPoiAttributeMapper.countCustomerPoiRelAttribute(customerId);
    }

    /**
     * 查询客户门店属性以及客户签约人信息
     *
     * @param customerId
     * @param wmPoiId
     * @return
     */
    public Map<String, Object> selectCustomerPoiAttributeKp(Integer customerId, Long wmPoiId) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_1", "com.sankuai.meituan.waimai.customer.service.customer.WmCustomerPoiAttributeService.selectCustomerPoiAttributeKp(java.lang.Integer,java.lang.Long)");
        if (customerId == null || wmPoiId == null) {
            return null;
        }
        return wmCustomerPoiAttributeMapper.selectCustomerPoiAttributeKp(customerId, wmPoiId);
    }


    /**
     * 根据门店ID查询客户门店属性记录
     *
     * @param wmPoiId 外卖门店ID
     * @return
     */
    public List<WmCustomerPoiAttributeDO> searchAttributeByWmPoiId(Long wmPoiId) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_2", "com.sankuai.meituan.waimai.customer.service.customer.WmCustomerPoiAttributeService.searchAttributeByWmPoiId(java.lang.Long)");
        if (wmPoiId == null || wmPoiId <= 0) {
            return null;
        }
        WmCustomerPoiAttributeSearchCondition condition = new WmCustomerPoiAttributeSearchCondition();
        condition.setWmPoiId(wmPoiId);
        return wmCustomerPoiAttributeMapper.selectByCondition(condition);
    }

    /**
     * 查询客户门店属性
     *
     * @param info
     * @return
     */
    public List<WmCustomerPoiAttributeResultDTO> selectCustomerPoiAttribute(WmCustomerPoiAttributeSearchConditionDTO info) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_2", "com.sankuai.meituan.waimai.customer.service.customer.WmCustomerPoiAttributeService.selectCustomerPoiAttribute(WmCustomerPoiAttributeSearchConditionDTO)");
        log.info("selectCustomerPoiAttribute info={}", JSON.toJSONString(info));
        WmCustomerPoiAttributeSearchCondition condition = new WmCustomerPoiAttributeSearchCondition();
        condition.setId(info.getId());
        condition.setCustomerId(info.getCustomerId());
        condition.setMinId(info.getMinId());
        condition.setMaxId(info.getMaxId());
        condition.setPageFrom(info.getPageFrom());
        condition.setPageSize(info.getPageSize());
        List<WmCustomerPoiAttributeDO> list = wmCustomerPoiAttributeMapper.selectByCondition(condition);
        if (CollectionUtils.isEmpty(list)) {
            return Lists.newArrayList();
        }
        List<WmCustomerPoiAttributeResultDTO> result = new ArrayList<>();
        for (WmCustomerPoiAttributeDO wmCustomerPoiAttributeDo : list) {
            WmCustomerPoiAttributeResultDTO wmCustomerPoiAttributeResultDto = new WmCustomerPoiAttributeResultDTO();
            wmCustomerPoiAttributeResultDto.setId(wmCustomerPoiAttributeDo.getId());
            wmCustomerPoiAttributeResultDto.setCustomerId(wmCustomerPoiAttributeDo.getCustomerId());
            wmCustomerPoiAttributeResultDto.setMtCustomerId(wmCustomerPoiAttributeDo.getMtCustomerId());
            wmCustomerPoiAttributeResultDto.setWmPoiId(wmCustomerPoiAttributeDo.getWmPoiId());
            wmCustomerPoiAttributeResultDto.setKpCompellation(wmCustomerPoiAttributeDo.getKpCompellation());
            wmCustomerPoiAttributeResultDto.setPhoneNumEncryption(wmCustomerPoiAttributeDo.getPhoneNumEncryption());
            wmCustomerPoiAttributeResultDto.setPhoneNumToken(wmCustomerPoiAttributeDo.getPhoneNumToken());
            result.add(wmCustomerPoiAttributeResultDto);
        }
        return result;
    }

}
