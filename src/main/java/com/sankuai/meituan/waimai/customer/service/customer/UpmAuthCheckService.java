package com.sankuai.meituan.waimai.customer.service.customer;

import com.google.common.collect.Lists;
import com.sankuai.meituan.auth.service.UpmService;
import com.sankuai.meituan.auth.vo.AuthResult;
import com.sankuai.meituan.auth.vo.Role;
import com.sankuai.meituan.auth.vo.RoleOrg;
import com.sankuai.meituan.auth.vo.UserRole;
import com.sankuai.meituan.waimai.customer.config.MccConfig;
import com.sankuai.meituan.waimai.customer.config.MccCustomerConfig;
import com.sankuai.meituan.waimai.customer.domain.AdminUser;
import com.sankuai.meituan.waimai.infra.domain.WmEmploy;
import com.sankuai.meituan.waimai.infra.service.WmEmployService;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerConstants;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerRoleTypeEnum;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

@Slf4j
@Service
public class UpmAuthCheckService {

    public static final String UPM_CLIENT_ID = "xianfu_waimai";

    @Autowired
    private UpmService upmService;

    @Autowired
    private WmEmployService.Iface wmEmployService;

    public boolean hasRolePermission(int userId, int roleType) {
        CustomerRoleTypeEnum roleTypeEnum = CustomerRoleTypeEnum.getByCode(roleType);
        if (roleTypeEnum == null) {
            return false;
        }
        if (userId % 100 < MccCustomerConfig.getUserRoleNewGrayPercent()) {
            return checkUpmRoleByIdNoOrgId(userId, MccConfig.getCustomerAuthRoleCode(roleTypeEnum.getKey()));
        } else {
            return checkUpmRoleById(userId, MccConfig.getCustomerAuthRoleCode(roleTypeEnum.getKey()));
        }
    }

    /**
     * 根据角色ID判断UPM角色
     */
    public boolean checkUpmRoleById(int userId, Integer roleId) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_1", "com.sankuai.meituan.waimai.customer.service.customer.UpmAuthCheckService.checkUpmRoleById(int,java.lang.Integer)");
        WmEmploy wmEmploy;
        try {
            wmEmploy = wmEmployService.getById(userId);
            if (wmEmploy == null) {
                return false;
            }
            List<RoleOrg> roleOrgs = upmService.getRoles(UPM_CLIENT_ID, userId);
            AdminUser adminUser =
                    new AdminUser.Builder(userId)
                            .employeeInfo(wmEmploy)
                            .roleOrgList(roleOrgs).builder();
            return adminUser.checkUpmRoleById(roleId);
        } catch (Exception e) {
            log.error("调用checkUpmRoleById异常", e);
        }
        return false;
    }

    /**
     * 根据角色ID判断UPM角色
     */
    public boolean checkUpmRoleByIdAndUserId(int userId, Integer roleId) throws WmCustomerException {
        WmEmploy wmEmploy;
        try {
            wmEmploy = wmEmployService.getById(userId);
            if (wmEmploy == null) {
                return false;
            }
            List<RoleOrg> roleOrgs = upmService.getRoles(UPM_CLIENT_ID, userId);
            AdminUser adminUser =
                    new AdminUser.Builder(userId)
                            .employeeInfo(wmEmploy)
                            .roleOrgList(roleOrgs).builder();
            return adminUser.checkUpmRoleById(roleId);
        } catch (Exception e) {
            log.error("调用checkUpmRoleById异常", e);
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "emp服务异常");
        }
    }



    /**
     * 根据角色ID判断UPM角色
     */
    private boolean checkUpmRoleByIdNoOrgId(int userId, Integer roleId) {
        WmEmploy wmEmploy;
        try {
            wmEmploy = wmEmployService.getById(userId);
            if (wmEmploy == null) {
                return false;
            }
            List<UserRole> userRoles = upmService.getRolesByUser(UPM_CLIENT_ID, Lists.newArrayList(userId));
            if (CollectionUtils.isEmpty(userRoles)) {
                return false;
            }
            for (UserRole userRole : userRoles) {
                if (userRole.getUserId() == null || userRole.getUserId().intValue() != userId) {
                    continue;
                }
                List<Role> roles = userRole.getRoles();
                Optional<Role> optionalRole = roles.stream().filter(x -> x.getId() != null && x.getId().intValue() == roleId).findFirst();
                if (optionalRole.isPresent()) {
                    return true;
                }
            }
            return false;
        } catch (Exception e) {
            log.error("调用checkUpmRoleByIdNoOrgId异常 userId={},roleId={}", userId, roleId, e);
        }
        return false;
    }

    /**
     * 查询是否有权限
     *
     * @param userId
     * @param authCode
     * @return
     * @throws WmCustomerException
     */
    public boolean checkHasUpmAuthByCode(int userId, String authCode) throws WmCustomerException {
        WmEmploy wmEmploy;
        try {
            wmEmploy = wmEmployService.getById(userId);
            if (wmEmploy == null) {
                return false;
            }
            AuthResult authResult = upmService.authCode(UPM_CLIENT_ID, userId, authCode);
            return authResult.isAuth();
        } catch (Exception e) {
            log.error("调用checkUpmRoleById异常", e);
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "emp服务异常");
        }
    }
}
