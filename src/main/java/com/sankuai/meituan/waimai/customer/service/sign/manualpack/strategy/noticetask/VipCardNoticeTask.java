package com.sankuai.meituan.waimai.customer.service.sign.manualpack.strategy.noticetask;

import com.alibaba.fastjson.JSON;
import com.sankuai.meituan.waimai.customer.adapter.WmLogisticsGatewayThriftServiceAdapter;
import com.sankuai.meituan.waimai.customer.service.sign.applytask.context.ManualPackNoticeContext;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.param.HeronContractOperator;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.param.flow.sign.HeronContractManualBatchSignParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.param.flow.sign.HeronContractManualSignItem;
import com.sankuai.meituan.waimai.thrift.constant.WmContractErrorCodeConstant;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.thrift.TException;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @description: 会员卡协议打包签约
 * @author: liuyunjie05
 * @create: 2025/3/10 17:59
 */
@Slf4j
@Service
public class VipCardNoticeTask implements NoticeTask{
    
    @Resource
    private WmLogisticsGatewayThriftServiceAdapter wmLogisticsGatewayThriftServiceAdapter;
    
    @Override
    public void notice(String module, List<Long> bizIdList, ManualPackNoticeContext context, List<Long> taskIds) throws WmCustomerException, TException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.sign.manualpack.strategy.noticetask.VipCardNoticeTask.notice(String,List,ManualPackNoticeContext,List)");
        log.info("VipCardNoticeTask#notice, module: {}, bizIdList: {}, context:{}", module, JSON.toJSONString(bizIdList), JSON.toJSONString(context));
        if (MapUtils.isEmpty(context.getTaskInfo()) || CollectionUtils.isEmpty(taskIds)) {
            throw new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR, "会员卡协议参数不合法");
        }
        try {
            // 会员卡任务列表
            Map<Long, Long> vipCardTaskWmPoiIdMap = context.getVipCardTaskWmPoiIdMap();
            if (MapUtils.isEmpty(vipCardTaskWmPoiIdMap)) {
                log.warn("VipCardNoticeTask#notice, vipCardTaskWmPoiIdMap为空");
                throw new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR, "会员卡协议任务数据组装异常");
            }
            // 构建手动签约项列表
            List<HeronContractManualSignItem> manualSignItemList = buildManualSignItemList(vipCardTaskWmPoiIdMap);
            
            // 构建操作人信息
            HeronContractOperator operator = buildOperator(context.getCommitUid());
            
            // 构建批量签约参数
            HeronContractManualBatchSignParam batchSignParam = buildBatchSignParam(context.getManualBatchId(), manualSignItemList, module, operator);
            
            // 调用批量签约接口
            wmLogisticsGatewayThriftServiceAdapter.deliveryBatchApplySignUseNewIface(batchSignParam);
        } catch (Exception e) {
            log.error("VipCardNoticeTask#notice, error", e);
            throw new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR, "会员卡协议打包签约发起异常");
        }
    }

    // 抽取的私有方法
    private List<HeronContractManualSignItem> buildManualSignItemList(Map<Long, Long> taskWmPoiIdMap) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_2", "com.sankuai.meituan.waimai.customer.service.sign.manualpack.strategy.noticetask.VipCardNoticeTask.buildManualSignItemList(java.util.Map)");
        return taskWmPoiIdMap.entrySet()
                .stream()
                .map(entry -> buildHeronContractManualSignItem(entry.getValue(), entry.getKey()))
                .collect(Collectors.toList());
    }
    
    private HeronContractManualSignItem buildHeronContractManualSignItem(Long wmPoiId, Long manualConfirmId) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.service.sign.manualpack.strategy.noticetask.VipCardNoticeTask.buildHeronContractManualSignItem(java.lang.Long,java.lang.Long)");
        HeronContractManualSignItem signItem = new HeronContractManualSignItem();
        signItem.setWmPoiId(wmPoiId);
        signItem.setManualConfirmId(manualConfirmId);
        return signItem;
    }

    private HeronContractOperator buildOperator(int commitUid) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.service.sign.manualpack.strategy.noticetask.VipCardNoticeTask.buildOperator(int)");
        return HeronContractOperator.builder()
                .opId((long) commitUid)
                .build();
    }

    private HeronContractManualBatchSignParam buildBatchSignParam(Long manualBatchId, List<HeronContractManualSignItem> signItemList,
                                                                  String applyType, HeronContractOperator operator) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.sign.manualpack.strategy.noticetask.VipCardNoticeTask.buildBatchSignParam(Long,List,String,HeronContractOperator)");
        return HeronContractManualBatchSignParam.builder()
                .batchManualConfirmId(manualBatchId)
                .signItemList(signItemList)
                .applyType(applyType)
                .operator(operator)
                .build();
    }

}
