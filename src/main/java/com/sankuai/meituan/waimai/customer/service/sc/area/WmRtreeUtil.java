package com.sankuai.meituan.waimai.customer.service.sc.area;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.infomatiq.jsi.Rectangle;
import com.infomatiq.jsi.rtree.RTree;
import com.sankuai.meituan.waimai.customer.domain.sc.area.WmScArea;
import com.sankuai.meituan.waimai.customer.domain.sc.area.WmScPoint;
import gnu.trove.TIntProcedure;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.util.*;

/**
 * 校园食堂范围工具类
 */
@Slf4j
public class WmRtreeUtil {

    /**
     * 获取最大最小经纬度
     *
     * @param businessId 业务ID, 唯一ID
     * @param wmScPointList 坐标点集合
     * @return WmScArea
     */
    public static WmScArea getArea(int businessId, List<WmScPoint> wmScPointList) {
        if (businessId <= 0 || CollectionUtils.isEmpty(wmScPointList)) {
            return null;
        }
        int minLat = wmScPointList.get(0).getX();
        int maxLat = wmScPointList.get(0).getX();
        int minLng = wmScPointList.get(0).getY();
        int maxLng = wmScPointList.get(0).getY();
        for (WmScPoint wmScPoint : wmScPointList) {
            int latitude = wmScPoint.getX();
            int longitude = wmScPoint.getY();
            minLat = Math.min(minLat, latitude);
            maxLat = Math.max(maxLat, latitude);
            minLng = Math.min(minLng, longitude);
            maxLng = Math.max(maxLng, longitude);
        }
        return new WmScArea(businessId, minLat, minLng, maxLat, maxLng, wmScPointList);
    }

    /**
     * 构造Rtree
     * 功能：以多边形为叶子节点创建Rtree
     * 输入：多边形的list
     * 输出：创建好的Rtree对象
     */
    public static RTree buildRtreeByAreas(List<WmScArea> areaList) {
        if (CollectionUtils.isEmpty(areaList)) {
            return null;
        }
        Set<Integer> ids = Sets.newHashSet();

        RTree rTree = new RTree();
        rTree.init(null);

        for (WmScArea area : areaList) {
            if (ids.contains(area.getBusinessId())) {
                continue;
            }

            if (area.getMaxLat() < area.getMinLat() || area.getMaxLng() < area.getMinLng()) {
                continue;
            }

            ids.add(area.getBusinessId());

            if (area.getMinLat() == 0 && area.getMinLng() == 0 && area.getMaxLat() == 0 && area.getMaxLng() == 0) {
                continue;
            }
            rTree.add(new Rectangle(area.getMinLat(), area.getMinLng(), area.getMaxLat(), area.getMaxLng()), area.getBusinessId());
        }
        return rTree;
    }

    /**
     * 功能：添加多个多边形更新当前的Rtree
     * 输入：Rtree：待查询的Rtree，areaList：待查询多边形的list
     * 输出：无
     */
    public static void addAreas(RTree rTree, List<WmScArea> areaList) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.service.sc.area.WmRtreeUtil.addAreas(com.infomatiq.jsi.rtree.RTree,java.util.List)");
        if (rTree == null) {
            return;
        }

        if (CollectionUtils.isEmpty(areaList)) {
            return;
        }

        for (WmScArea area : areaList) {
            rTree.add(new Rectangle(area.getMinLat(), area.getMinLng(), area.getMaxLat(), area.getMaxLng()), area.getBusinessId());
        }
    }


    // 删除节点
    public static void deleteAreas(RTree rTree, List<WmScArea> areaList) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.sc.area.WmRtreeUtil.deleteAreas(com.infomatiq.jsi.rtree.RTree,java.util.List)");
        if (rTree == null) {
            return;
        }

        if (CollectionUtils.isEmpty(areaList)) {
            return;
        }

        for (WmScArea area : areaList) {
            rTree.delete(new Rectangle(area.getMinLat(), area.getMinLng(), area.getMaxLat(), area.getMaxLng()), area.getBusinessId());
        }
    }


    /**
     * 功能：在Rtree中查询指定包含指定点的多边形区域
     * 输入：Rtree：待查询的Rtree，point：待查询的点
     * 输出：查询到的多边形id的list
     */
    public static List<Integer> search(RTree rTree, WmScPoint point, Map<Integer, WmScArea> areaMap) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.sc.area.WmRtreeUtil.search(com.infomatiq.jsi.rtree.RTree,com.sankuai.meituan.waimai.customer.domain.sc.area.WmScPoint,java.util.Map)");
        if (rTree == null || point == null) {
            return Lists.newArrayList();
        }
        SearchResult result = new SearchResult();
        rTree.intersects(new Rectangle(point.getX(), point.getY(), point.getX(), point.getY()), result);
        Set<Integer> set = result.getResult();
        if (CollectionUtils.isEmpty(set)) {
            return Lists.newArrayList();
        }

        List<Integer> list = Lists.newArrayList();
        log.info("[WmRtreeUtil.search] set = {}", JSONObject.toJSONString(set));
        for (Integer id : set) {
            WmScArea wmScArea = areaMap.get(id);
            if (wmScArea != null && wmScArea.getPolygon() != null) {
                if (wmScArea.getWmScAreaPolygon().contains(point.getX(), point.getY(), wmScArea.getMinLat(), wmScArea.getMinLng(), wmScArea.getMaxLat(), wmScArea.getMaxLng())) {
                    list.add(id);
                }
            }
        }

        return list;
    }

    /**
     * 功能：在查询该点是否处于范围内
     * 输入：rtree：待查询的rtree wmScPoint:待查询的坐标点
     */
    public static Set<Integer> search(RTree rTree, WmScPoint wmScPoint){
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_2", "com.sankuai.meituan.waimai.customer.service.sc.area.WmRtreeUtil.search(com.infomatiq.jsi.rtree.RTree,com.sankuai.meituan.waimai.customer.domain.sc.area.WmScPoint)");
        if (rTree == null || wmScPoint == null) {
            return Sets.newHashSet();
        }
        SearchResult result = new SearchResult();
        rTree.intersects(new Rectangle(wmScPoint.getX(), wmScPoint.getY(), wmScPoint.getX(), wmScPoint.getY()), result);
        return result.getResult();
    }

    /**
     * 判断以latitude,longitude为坐标的点是否在area范围内
     * @param latitude 纬度
     * @param longitude 经度
     * @param area 范围
     * @return true: 点在范围内 / false: 点不在范围内
     * todo：使用说明
     */
    public static boolean withinArea(int latitude, int longitude, String area) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.sc.area.WmRtreeUtil.withinArea(int,int,java.lang.String)");
        if (latitude <= 0 || longitude <= 0 || StringUtils.isBlank(area)) {
            return false;
        }
        // 将范围转换成顶点
        List<WmScPoint> wmScPoints = JSONObject.parseArray(area, WmScPoint.class);
        // 构建Rtree和多边形
        WmScArea wmScArea = getArea(1, wmScPoints);
        if (wmScArea == null || wmScArea.getWmScAreaPolygon() == null) {
            return false;
        }
        return wmScArea.getWmScAreaPolygon().contains(latitude, longitude,
                wmScArea.getMinLat(), wmScArea.getMinLng(), wmScArea.getMaxLat(), wmScArea.getMaxLng());
    }

    /**
     * 判断以latitude,longitude为坐标的点是否在area范围列表内
     * 即: 只要在一个范围内就返回true; 若不在任一范围内就返回false
     * @param latitude 纬度
     * @param longitude 经度
     * @param areaList 范围列表
     * @return true: 在其中一个范围内; false: 不在任一范围内
     */
    public static boolean withinAreaList(int latitude, int longitude, List<String> areaList) {
        if (latitude <= 0 || longitude <= 0 || CollectionUtils.isEmpty(areaList)) {
            return false;
        }

        for (String area : areaList) {
            // 将范围转换成顶点
            List<WmScPoint> wmScPoints = JSONObject.parseArray(area, WmScPoint.class);
            // 构建Rtree和多边形
            WmScArea wmScArea = getArea(1, wmScPoints);
            if (wmScArea == null || wmScArea.getWmScAreaPolygon() == null) {
                continue;
            }
            if (wmScArea.getWmScAreaPolygon().contains(latitude, longitude,
                    wmScArea.getMinLat(), wmScArea.getMinLng(), wmScArea.getMaxLat(), wmScArea.getMaxLng())) {
                return true;
            }
        }
        return false;
    }

    public static class SearchResult implements TIntProcedure {
        private Set<Integer> result = new HashSet<Integer>();

        @Override
        public boolean execute(int i) {
            result.add((int) i);
            return true;
        }

        public Set<Integer> getResult() {
            return result;
        }

        public void setResult(Set<Integer> result) {
            this.result = result;
        }
    }
}
