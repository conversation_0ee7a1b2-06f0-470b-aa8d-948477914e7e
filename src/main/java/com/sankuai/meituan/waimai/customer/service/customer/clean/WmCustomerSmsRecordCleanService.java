package com.sankuai.meituan.waimai.customer.service.customer.clean;


import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Stopwatch;
import com.google.common.collect.Lists;
import com.sankuai.meituan.waimai.customer.config.MccConfig;
import com.sankuai.meituan.waimai.customer.dao.WmCustomerPoiDBMapper;
import com.sankuai.meituan.waimai.customer.dao.WmCustomerPoiSmsRecordMapper;
import com.sankuai.meituan.waimai.customer.ddd.domain.customer.aggre.WmCustomerPoiAggre;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerPoiDB;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerPoiSmsRecordDB;
import com.sankuai.meituan.waimai.customer.service.customer.Impl.AbstractWmCustomerRealService;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerPoiService;
import com.sankuai.meituan.waimai.customer.service.sign.WmEcontractSignManagerBzService;
import com.sankuai.meituan.waimai.thrift.customer.constant.*;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractBatchContextBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.SignBatchQueryThriftParam;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.WmEcontractSignBatchBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.thrift.TException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

@Service
@Slf4j
public class WmCustomerSmsRecordCleanService {
    private static Logger logger = LoggerFactory.getLogger(AbstractWmCustomerRealService.class);

    @Resource
    private WmCustomerPoiSmsRecordMapper wmCustomerPoiSmsRecordMapper;

    @Resource
    private WmCustomerPoiDBMapper wmCustomerPoiDBMapper;

    @Resource
    private WmEcontractSignManagerBzService wmEcontractSignManagerBzService;

    @Autowired
    private WmCustomerPoiService wmCustomerPoiService;

    private static final int MAX_PAGE_SIZE = 1000;

    public int cleanBindCustomerRel(Integer idStart, Integer pageSize, Integer idEnd) throws WmCustomerException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_2", "com.sankuai.meituan.waimai.customer.service.customer.clean.WmCustomerSmsRecordCleanService.cleanBindCustomerRel(java.lang.Integer,java.lang.Integer,java.lang.Integer)");
        logger.info("[cleanBindCustomerRel存量清洗][start] idStart:{}, pageSize:{}", idStart, pageSize);

        if (pageSize > MAX_PAGE_SIZE) {
            logger.warn("[cleanBindCustomerRel存量清洗] ID区间过大,调整pageSize=maxPageSize，pageSize={}, maxPageSize={}", pageSize, MAX_PAGE_SIZE);
            pageSize = MAX_PAGE_SIZE;
        }

        Stopwatch sw = Stopwatch.createStarted();
        List<WmCustomerPoiDB> wmCustomerPoiDBList = new ArrayList<>();
        int maxId = idStart;
        //记录修改数
        int count = 0;
        try {
            wmCustomerPoiDBList = wmCustomerPoiDBMapper.selectUnbindingCustomerPoiRelByIdSection(idStart, pageSize, idEnd);
            if (CollectionUtils.isEmpty(wmCustomerPoiDBList)) {
                logger.info("[cleanBindCustomerRel存量清洗] 获取客户门店关系列表为空");
                return -1;
            }
            for (WmCustomerPoiDB wmCustomerPoiDB : wmCustomerPoiDBList) {
                if (wmCustomerPoiDB.getId() > idEnd) {
                    logger.info("[cleanBindCustomerRel存量清洗][end] 任务执行结束, 累计处理客户:{}个, 任务执行总耗时:{}ms", count, sw.elapsed(TimeUnit.MILLISECONDS));
                    return -1;
                }
                maxId = Math.max(maxId, wmCustomerPoiDB.getId());
                if (correctCustomerPoiBindingStatus(wmCustomerPoiDB)) {
                    count++;
                }
            }
        } catch (Exception e) {
            logger.error("[cleanBindCustomerRel存量清洗] 查询客户门店关系列表异常, idStart={}, pageSize={}，error={}", idStart, pageSize, e);
            return -1;
        }
        logger.info("[cleanBindCustomerRel存量清洗][end] 任务执行结束, 累计处理客户:{}个, 任务执行总耗时:{}ms", count, sw.elapsed(TimeUnit.MILLISECONDS));
        return Math.max(idStart+pageSize, maxId+1);
    }

    private boolean correctCustomerPoiBindingStatus(WmCustomerPoiDB wmCustomerPoiDB) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.customer.clean.WmCustomerSmsRecordCleanService.correctCustomerPoiBindingStatus(com.sankuai.meituan.waimai.customer.domain.WmCustomerPoiDB)");
        List<WmCustomerPoiSmsRecordDB> smsRecords = wmCustomerPoiSmsRecordMapper.selectUnBindSmsRecordByPoiId(wmCustomerPoiDB.getWmPoiId());
        if (smsRecords.size() >= 1){
            return false;
        }
        logger.error("[correctCustomerPoiBindingStatus]门店{}绑定状态(正在解绑中)异常，请校正", wmCustomerPoiDB.getWmPoiId());
        if (MccConfig.getOpenAlignSignStatus()) {
            wmCustomerPoiDB.setIsUnbinding(CustomerConstants.IS_UNBINDING_NO);
            wmCustomerPoiDB.setRelationStatus(CustomerRelationStatusEnum.BIND.getCode());
            wmCustomerPoiDBMapper.batchUpdateCustomerPoiRelationByList(new ArrayList<WmCustomerPoiDB>(){{add(wmCustomerPoiDB);}});
        }
        return true;
    }

    public int cleanSmsRecordTask(Integer idStart, Integer pageSize, Integer idEnd) throws WmCustomerException, TException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.service.customer.clean.WmCustomerSmsRecordCleanService.cleanSmsRecordTask(java.lang.Integer,java.lang.Integer,java.lang.Integer)");
        logger.info("[cleanSmsRecordTask存量清洗][start] idStart:{}, pageSize:{}", idStart, pageSize);
        if (pageSize > MAX_PAGE_SIZE) {
            logger.warn("[cleanSmsRecordTask存量清洗] ID区间过大,调整pageSize=maxPageSize, pageSize={}, maxPageSize={}", pageSize, MAX_PAGE_SIZE);
            pageSize = MAX_PAGE_SIZE;
        }

        Stopwatch sw = Stopwatch.createStarted();
        List<WmCustomerPoiSmsRecordDB> smsRecordList = new ArrayList<>();
        int maxId = idStart;
        //记录修改数
        int count = 0;
        try {
            smsRecordList = wmCustomerPoiSmsRecordMapper.selectUnbindSmsRecordListByIdSection(idStart, pageSize, idEnd);
            if (CollectionUtils.isEmpty(smsRecordList)) {
                logger.info("[cleanSmsRecordTask存量清洗] 获取短信签约任务列表为空");
                return -1;
            }
            for (WmCustomerPoiSmsRecordDB smsRecord : smsRecordList) {
                if (smsRecord.getId() > idEnd) {
                    logger.info("[cleanSmsRecordTask存量清洗][end] 任务执行结束, 累计处理短信任务:{}个, 任务执行总耗时:{}ms", count, sw.elapsed(TimeUnit.MILLISECONDS));
                    return -1;
                }
                maxId = Math.max(maxId, smsRecord.getId());
                if(alignSignStatus(smsRecord)){
                    count++;
                }
            }
        } catch (Exception e) {
            logger.error("[cleanSmsRecordTask存量清洗] 查询短信签约任务列表异常, idStart={}, pageSize={}，error={}", idStart, pageSize, e);
            return -1;
        }
        logger.info("[cleanSmsRecordTask存量清洗][end] 任务执行结束, 累计处理短信任务:{}个, 任务执行总耗时:{}ms", count, sw.elapsed(TimeUnit.MILLISECONDS));
        return Math.max(idStart+pageSize, maxId+1);
    }

    public boolean alignSignStatus(WmCustomerPoiSmsRecordDB smsRecord) throws TException, WmCustomerException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.service.customer.clean.WmCustomerSmsRecordCleanService.alignSignStatus(com.sankuai.meituan.waimai.customer.domain.WmCustomerPoiSmsRecordDB)");
        SignBatchQueryThriftParam thriftParam = new SignBatchQueryThriftParam();
        thriftParam.setWmCustomerId(smsRecord.getCustomerId());
        thriftParam.setStartTime(smsRecord.getCtime().longValue() - 5);
        thriftParam.setEndTime(smsRecord.getUtime().longValue() + 5);
        List<Long> batchIds = wmEcontractSignManagerBzService.queryBatchBoIdListWithoutPack(thriftParam);
        if (CollectionUtils.isEmpty(batchIds)) {
            logger.error("[alignSignStatus]原客户确认解绑未找到签约任务:taskId:{},wmPoiIds:{}", smsRecord.getTaskId(), smsRecord.getWmPoiIds());
        }
        List<Integer> econtractTaskIdList = Lists.newArrayList();
        List<WmEcontractSignBatchBo> taskList = wmEcontractSignManagerBzService.batchQuerySimpleBatchResultByBatchId(batchIds);
        for (WmEcontractSignBatchBo wmEcontractSignBatchBo : taskList) {
            if (StringUtils.isBlank(wmEcontractSignBatchBo.getBatchState()) || !wmEcontractSignBatchBo.getBatchState().equals(EcontractBatchStateEnum.IN_PROCESSING.getName())) {
                continue;
            }
            String batchContext = wmEcontractSignBatchBo.getBatchContext();
            if (StringUtils.isBlank(batchContext)) {
                continue;
            }
            EcontractBatchContextBo econtractBatchContextBo = JSONObject.parseObject(batchContext, EcontractBatchContextBo.class);
            if (econtractBatchContextBo.getBatchTypeEnum() == null || econtractBatchContextBo.getBatchTypeEnum() != EcontractBatchTypeEnum.CANCEL_CONFIRM
                    || CollectionUtils.isEmpty(econtractBatchContextBo.getWmPoiIdList())) {
                continue;
            }
            String[] list = smsRecord.getWmPoiIds().split(",");
            List<Long> smsRecordPoiIds = new ArrayList<>();
            for (String s : list) {
                smsRecordPoiIds.add(Long.parseLong(s));
            }
            int size = smsRecordPoiIds.size();
            econtractBatchContextBo.getWmPoiIdList().retainAll(smsRecordPoiIds);
            if (econtractBatchContextBo.getWmPoiIdList().size() == size) {
                econtractTaskIdList.add(wmEcontractSignBatchBo.getBatchId());
            }
        }
        if (econtractTaskIdList.size() > 1) {
            logger.error("[alignSignStatus]并发产生多条签约任务:taskId:{},wmPoiIds:{},econtractTaskIdList:{}", smsRecord.getTaskId(),
                    smsRecord.getWmPoiIds(), JSONObject.toJSONString(econtractTaskIdList));
        } else if (econtractTaskIdList.size() == 0) {
            logger.error("[alignSignStatus]打包签约任务与短信签约任务{}状态不一致或打包签约任务不存在，请校正", smsRecord.getTaskId());
            if (MccConfig.getOpenAlignSignStatus()) {
                wmCustomerPoiService.cancelUnBind(smsRecord.getCustomerId(), smsRecord.getTaskId(), 0, "数据清洗");
            }
            return true;
        }
        return false;
    }


}
