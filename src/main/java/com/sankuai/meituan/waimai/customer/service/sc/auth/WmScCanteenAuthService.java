package com.sankuai.meituan.waimai.customer.service.sc.auth;

import com.alibaba.fastjson.JSONObject;
import com.dianping.cat.Cat;
import com.sankuai.meituan.waimai.customer.adapter.AuthenticateServiceAdapter;
import com.sankuai.meituan.waimai.customer.adapter.WmAuditApiAdaptor;
import com.sankuai.meituan.waimai.customer.adapter.WmAuditServiceAdaptor;
import com.sankuai.meituan.waimai.customer.config.MccConfig;
import com.sankuai.meituan.waimai.customer.constant.sc.WmScMetricConstant;
import com.sankuai.meituan.waimai.customer.service.sc.check.WmCanteenPoiCheckService;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.ScGrayEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.WmScCodeConstants;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.canteenstall.CanteenStallClueBindStatusEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.canteenstall.CanteenStallWmPoiBindStatusEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.sc.CanteenBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sc.WmCanteenAuditDTO;
import com.sankuai.meituan.waimai.thrift.customer.domain.sc.canteenstall.WmCanteenStallBindDTO;
import com.sankuai.meituan.waimai.thrift.customer.service.sc.WmCanteenThriftService;
import com.sankuai.meituan.waimai.thrift.domain.WmAuditTask;
import com.sankuai.meituan.waimai.thrift.exception.WmSchCantException;
import com.sankuai.waimai.crm.authenticate.client.constant.DSLTypeEnum;
import com.sankuai.waimai.crm.authenticate.client.service.request.AuthenticateAssertBatchOperationRequest;
import com.sankuai.waimai.crm.authenticate.client.service.request.AuthenticateAssertRequest;
import com.sankuai.waimai.crm.authenticate.client.service.request.AuthenticateQueryRequest;
import com.sankuai.waimai.crm.authenticate.client.service.response.AssertResult;
import com.sankuai.waimai.crm.authenticate.client.service.response.AuthenticateAssertBatchOperationResponse;
import com.sankuai.waimai.crm.authenticate.client.service.response.AuthenticateAssertResponse;
import com.sankuai.waimai.crm.authenticate.client.service.response.AuthenticateQueryResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

import static com.sankuai.meituan.waimai.thrift.customer.constant.sc.WmScCodeConstants.BIZ_AUTH_ERROR;
import static com.sankuai.meituan.waimai.thrift.customer.constant.sc.WmScCodeConstants.SERVER_ERROR;

/**
 * 食堂权限服务相关
 * <AUTHOR>
 * @date 2023/11/16
 * @email <EMAIL>
 */
@Slf4j
@Service
public class WmScCanteenAuthService {

    @Autowired
    private AuthenticateServiceAdapter authenticateServiceAdapter;

    @Autowired
    private WmAuditServiceAdaptor auditServiceAdaptor;

    @Autowired
    private WmCanteenPoiCheckService wmCanteenPoiCheckService;

    @Autowired
    private WmCanteenThriftService canteenThriftService;


    /**
     * 外卖租户ID
     */
    private static final Integer WM_TENANT_ID = 1000008;
    /**
     * 学校业务对象CODE
     */
    private static final String CANTEEN_OBJECT_CODE = "waimai_canteen";
    /**
     * 学校列表查询操作CODE
     */
    private static final String CANTEEN_LIST_QUERY_OPERATION_CODE = "canteen_list_query";
    /**
     * 食堂列表修改按钮操作CODE
     */
    private static final String CANTEEN_LIST_EDIT_BUTTON_OPERATION_CODE = "canteen_list_edit_button";
    /**
     * 食堂列表分配责任人按钮操作CODE
     */
    private static final String CANTEEN_LIST_OWNER_BUTTON_OPERATION_CODE = "canteen_list_owner_button";
    /**
     * 食堂列表操作记录按钮操作CODE
     */
    private static final String CANTEEN_LIST_LOG_BUTTON_OPERATION_CODE = "canteen_list_log_button";
    /**
     * 食堂列表取消合作按钮操作CODE
     */
    private static final String CANTEEN_LIST_UNCOOPERATE_BUTTON_OPERATION_CODE = "canteen_list_uncooperate_button";

    /**
     * 食堂档口绑定任务-解绑按钮
     */
    public static final String CANTEEN_STALL_BIND_UNBIND_BUTTON = "stall_bind_unbind_button";
    /**
     * 食堂档口绑定任务-去上单按钮
     */
    public static final String CANTEEN_STALL_BIND_PLACE_ORDER_BUTTON = "stall_bind_place_order_button";
    /**
     * 食堂档口绑定任务-标记正常按钮
     */
    public static final String CANTEEN_STALL_BIND_NORMAL_BUTTON = "stall_bind_normal_button";
    /**
     * 食堂档口绑定任务-标记异常按钮
     */
    public static final String CANTEEN_STALL_BIND_ABNORMAL_BUTTON = "stall_bind_abnormal_button";
    /**
     * 食堂档口绑定任务-跟进状态审批中按钮
     */
    public static final String CANTEEN_STALL_BIND_AUDITING_BUTTON = "stall_bind_auditing_button";
    /**
     * 食堂档口绑定任务-操作记录按钮
     */
    public static final String CANTEEN_STALL_BIND_LOG_BUTTON = "stall_bind_log_button";

    private static final String STALL_BIND_LIST_CHANGEBIND_BUTTON = "stall_bind_list_changebind_button";

    private static final String STALL_BIND_LIST_BATCH_CHANGEBIND_BUTTON = "stall_bind_list_batch_changebind_button";

    private static final String STALL_BIND_LIST_BATCH_UNBIND_BUTTON = "stall_bind_list_batch_unbind_button";

    /**
     * 食堂列表删除按钮操作CODE
     */
    private static final String CANTEEN_DELETE_BUTTON_OPERATION_CODE = "canteen_delete_button";
    /**

    /**
     * 根据用户UID获取"食堂列表查询"操作的DSL查询语句
     * @param uid 用户ID
     * @return DSL查询语句
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    public String getCanteenListQueryDSL(Integer uid) throws WmSchCantException {
        log.info("[WmScSchoolAuthService.getCanteenListQueryDSL] input param: uid = {}", uid);
        if (uid == null || uid <= 0) {
            log.warn("[WmScSchoolAuthService.getCanteenListQueryDSL] uid is null. uid = {}", uid);
            return null;
        }

        AuthenticateQueryRequest queryRequest = new AuthenticateQueryRequest();
        queryRequest.setDslType(DSLTypeEnum.MY_SQL_DSL.code());
        queryRequest.setTenantId(WM_TENANT_ID);
        queryRequest.setUid(uid);
        queryRequest.setObjectCode(CANTEEN_OBJECT_CODE);
        queryRequest.setOperation(CANTEEN_LIST_QUERY_OPERATION_CODE);
        AuthenticateQueryResponse authenticateQueryResponse = authenticateServiceAdapter.getAuthQueryResult(queryRequest);
        log.info("[WmScSchoolAuthService.getCanteenListQueryDSL] authenticateQueryResponse = {}", JSONObject.toJSONString(authenticateQueryResponse));
        if (authenticateQueryResponse == null
                || authenticateQueryResponse.getCode() != 0
                || StringUtils.isBlank(authenticateQueryResponse.getDsl())) {
            log.error("[WmScSchoolAuthService.getCanteenListQueryDSL] query error. uid = {}, authenticateQueryResponse = {}",
                    uid, JSONObject.toJSONString(authenticateQueryResponse));
            Cat.logEvent(WmScMetricConstant.METRIC_AUTH_QUERY, "getCanteenListQueryDSL");
            throw new WmSchCantException(WmScCodeConstants.SERVER_ERROR, "权限获取异常, 请稍后刷新后再试");
        }
        return authenticateQueryResponse.getDsl();
    }

    /**
     * 食堂列表操作鉴权查询
     * @param canteenBoList 食堂对象列表
     * @param uid 用户ID
     * @return 鉴权结果列表
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    public List<AssertResult> getCanteenListOperationAssertResult(List<CanteenBo> canteenBoList, Integer uid) throws WmSchCantException {
        log.info("[WmScCanteenAuthService.getCanteenListOperationAssertResult] input param: canteenBoList = {}, uid = {}",
                JSONObject.toJSONString(canteenBoList), uid);
        if (uid == null || uid <= 0) {
            log.error("[WmScCanteenAuthService.getCanteenListOperationAssertResult] uid is null. canteenBoList = {}, uid = {}",
                    JSONObject.toJSONString(canteenBoList), uid);
            throw new WmSchCantException(WmScCodeConstants.BIZ_PARA_ERROR, "用户信息为空");
        }

        AuthenticateAssertBatchOperationRequest assertRequest = new AuthenticateAssertBatchOperationRequest();
        assertRequest.setTenantId(WM_TENANT_ID);
        assertRequest.setUid(uid);
        assertRequest.setObjectCode(CANTEEN_OBJECT_CODE);
        // 操作类型code列表
        Set<String> operationList = new HashSet<>();
        operationList.add(CANTEEN_LIST_EDIT_BUTTON_OPERATION_CODE);
        operationList.add(CANTEEN_LIST_LOG_BUTTON_OPERATION_CODE);
        operationList.add(CANTEEN_LIST_OWNER_BUTTON_OPERATION_CODE);
        operationList.add(CANTEEN_LIST_UNCOOPERATE_BUTTON_OPERATION_CODE);
        assertRequest.setOperations(operationList);
        // 业务对象ID列表
        Set<String> canteenPrimaryIdList = canteenBoList.stream()
                .map(x -> String.valueOf(x.getId()))
                .collect(Collectors.toSet());
        assertRequest.setObjectIds(canteenPrimaryIdList);

        AuthenticateAssertBatchOperationResponse assertResponse = authenticateServiceAdapter.getAuthAssertResultByBatch(assertRequest);
        if (assertResponse == null || assertResponse.getCode() != 0) {
            log.error("[WmScSchoolAuthService.getCanteenListOperationAssertResult] error. schoolBoList = {}, uid = {}, getSchoolListOperationAssertResult = {}",
                    JSONObject.toJSONString(canteenBoList), uid, JSONObject.toJSONString(assertResponse));
            throw new WmSchCantException(WmScCodeConstants.SERVER_ERROR, "权限获取异常, 请稍后刷新后再试");
        }

        return assertResponse.getAssertResults();
    }

    /**
     * 设置食堂操作鉴权结果
     * @param assertResult 鉴权结果
     * @param canteenBo 食堂对象
     * @return 食堂对象
     */
    public CanteenBo setCanteenListAssertResult(AssertResult assertResult, CanteenBo canteenBo) {
        switch (assertResult.getOperation()) {
            case CANTEEN_LIST_EDIT_BUTTON_OPERATION_CODE:
                canteenBo.setEditButtonAuth(assertResult.getResult() ? ScGrayEnum.CAN_EDIT.getCode() : ScGrayEnum.NOT_DISPLAY.getCode());
                break;
            case CANTEEN_LIST_LOG_BUTTON_OPERATION_CODE:
                canteenBo.setLogButtonAuth(assertResult.getResult() ? ScGrayEnum.CAN_EDIT.getCode() : ScGrayEnum.NOT_DISPLAY.getCode());
                break;
            case CANTEEN_LIST_UNCOOPERATE_BUTTON_OPERATION_CODE:
                canteenBo.setUncooperateButtonAuth(assertResult.getResult() ? ScGrayEnum.CAN_EDIT.getCode() : ScGrayEnum.NOT_DISPLAY.getCode());
                break;
            case CANTEEN_LIST_OWNER_BUTTON_OPERATION_CODE:
                canteenBo.setAssignOwnerButtonAuth(assertResult.getResult() ? ScGrayEnum.CAN_EDIT.getCode() : ScGrayEnum.NOT_DISPLAY.getCode());
                break;
            default:
                break;
        }

        return canteenBo;
    }

    /**
     * 获取用户对食堂某一操作的鉴权结果
     * @param uid 用户ID
     * @param operationCode 操作code
     * @param canteenPrimaryId 食堂主键ID
     * @return true: 有权限 / false: 无权限
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     * @throws TException org.apache.thrift.TException
     */
    public Boolean getOperationAuthAssertResult(Integer uid, String operationCode, Integer canteenPrimaryId) throws WmSchCantException, TException {
        log.info("[WmScCanteenAuthService.getOperationAuthAssertResult] input param: uid = {}, operationCode = {}, canteenPrimaryId = {}",
                uid, operationCode, canteenPrimaryId);
        if (uid == null || uid <= 0 || StringUtils.isBlank(operationCode) || canteenPrimaryId == null || canteenPrimaryId <= 0) {
            log.error("[WmScCanteenAuthService.getOperationAuthAssertResult] input param invalid. uid = {}, operationCode = {}, canteenPrimaryId = {}",
                    uid, operationCode, canteenPrimaryId);
            return false;
        }

        AuthenticateAssertRequest assertRequest = new AuthenticateAssertRequest();
        assertRequest.setObjectCode(CANTEEN_OBJECT_CODE);
        assertRequest.setTenantId(WM_TENANT_ID);
        assertRequest.setUid(uid);
        // 操作类型code
        assertRequest.setOperation(operationCode);
        // 业务对象ID列表
        List<String> schoolPrimaryIdList = new ArrayList<>();
        schoolPrimaryIdList.add(String.valueOf(canteenPrimaryId));
        assertRequest.setObjectIds(schoolPrimaryIdList);
        AuthenticateAssertResponse assertResponse = authenticateServiceAdapter.getAuthAssertResult(assertRequest);
        log.info("[WmScCanteenAuthService.getOperationAuthAssertResult] assertResponse = {}", JSONObject.toJSONString(assertResponse));

        if (assertResponse == null) {
            log.error("[WmScCanteenAuthService.getOperationAuthAssertResult] auth assert failed. canteenPrimaryId = {}, uid = {}", canteenPrimaryId, uid);
            throw new WmSchCantException(BIZ_AUTH_ERROR, "权限获取异常, 请稍后刷新后再试");
        }
        return assertResponse.getResult(String.valueOf(canteenPrimaryId));
    }

    /**
     * 查询单个食堂批量操作的鉴权结果
     * @param operationList 操作列表
     * @param canteenPrimaryId 食堂主键ID
     * @param uid 用户ID
     * @return 鉴权结果Map: key->operationCode, value->assert result
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    public Map<String, Boolean> batchGetOperationAuthAssertResult(Set<String> operationList, Integer canteenPrimaryId, Integer uid)
            throws WmSchCantException, TException {
        log.info("[WmScCanteenAuthService.batchGetOperationAuthAssertResult] input param: operationList = {}, canteenPrimaryId = {}, uid = {}",
                JSONObject.toJSONString(operationList), canteenPrimaryId, uid);
        if (CollectionUtils.isEmpty(operationList)) {
            log.warn("[WmScCanteenAuthService.batchGetOperationAuthAssertResult] operationList is empty, return.");
            return new HashMap<>();
        }

        AuthenticateAssertBatchOperationRequest assertRequest = new AuthenticateAssertBatchOperationRequest();
        assertRequest.setObjectCode(CANTEEN_OBJECT_CODE);
        assertRequest.setTenantId(WM_TENANT_ID);
        assertRequest.setUid(uid);
        // 操作类型code列表
        assertRequest.setOperations(operationList);
        // 业务对象ID列表
        Set<String> canteenPrimaryIdList = new HashSet<>();
        canteenPrimaryIdList.add(canteenPrimaryId.toString());
        assertRequest.setObjectIds(canteenPrimaryIdList);

        AuthenticateAssertBatchOperationResponse assertResponse = authenticateServiceAdapter.getAuthAssertResultByBatch(assertRequest);
        if (assertResponse == null
                || assertResponse.getCode() != 0
                || CollectionUtils.isEmpty(assertResponse.getAssertResults())) {
            log.error("[WmScCanteenAuthService.batchGetOperationAuthAssertResult] error. assertRequest = {}, assertResponse = {}",
                    JSONObject.toJSONString(assertRequest), JSONObject.toJSONString(assertResponse));
            throw new WmSchCantException(WmScCodeConstants.SERVER_ERROR, "权限获取异常, 请稍后刷新后再试");
        }

        List<AssertResult> assertResultList = assertResponse.getAssertResults();
        return assertResultList.stream()
                .collect(Collectors.toMap(AssertResult::getOperation, AssertResult::getResult));
    }


    /**
     * 档口绑定列表操作鉴权查询
     * @param canteenPrimaryId 食堂主键ID
     * @param uid 用户ID
     * @return 鉴权结果列表
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    public List<AssertResult> getBindListOperationAssertResult(Integer canteenPrimaryId, Integer uid) throws WmSchCantException {
        if (canteenPrimaryId == null || canteenPrimaryId <= 0 || uid == null || uid <= 0) {
            return new ArrayList<>();
        }

        AuthenticateAssertBatchOperationRequest assertRequest = new AuthenticateAssertBatchOperationRequest();
        assertRequest.setUid(uid);
        assertRequest.setTenantId(WM_TENANT_ID);
        assertRequest.setObjectCode(CANTEEN_OBJECT_CODE);
        // 操作类型code列表
        Set<String> operationList = new HashSet<>();
        operationList.add(CANTEEN_STALL_BIND_UNBIND_BUTTON);
        operationList.add(CANTEEN_STALL_BIND_PLACE_ORDER_BUTTON);
        operationList.add(CANTEEN_STALL_BIND_NORMAL_BUTTON);
        operationList.add(CANTEEN_STALL_BIND_ABNORMAL_BUTTON);
        operationList.add(CANTEEN_STALL_BIND_AUDITING_BUTTON);
        operationList.add(CANTEEN_STALL_BIND_LOG_BUTTON);
        assertRequest.setOperations(operationList);
        // 业务对象ID列表
        Set<String> canteenPrimaryIdSet = new HashSet<>();
        canteenPrimaryIdSet.add(String.valueOf(canteenPrimaryId));
        assertRequest.setObjectIds(canteenPrimaryIdSet);

        AuthenticateAssertBatchOperationResponse assertResponse = authenticateServiceAdapter.getAuthAssertResultByBatch(assertRequest);
        if (assertResponse == null || assertResponse.getCode() != 0) {
            log.error("[WmScCanteenAuthService.getManageListOperationAssertResult] error. canteenPrimaryId = {}, uid = {}, assertResponse = {}",
                    canteenPrimaryId, uid, JSONObject.toJSONString(assertResponse));
            throw new WmSchCantException(WmScCodeConstants.SERVER_ERROR, "权限获取异常, 请稍后刷新后再试");
        }

        return assertResponse.getAssertResults();
    }

    public void setCanteenStallBindDTOAssertResult(WmCanteenStallBindDTO bindDTO, AssertResult assertResult) {
        switch (assertResult.getOperation()) {
            case CANTEEN_STALL_BIND_UNBIND_BUTTON:
                bindDTO.setUnbindButtonAuth(assertResult.getResult() ? ScGrayEnum.CAN_EDIT.getCode() : ScGrayEnum.NOT_DISPLAY.getCode());
                break;
            case CANTEEN_STALL_BIND_PLACE_ORDER_BUTTON:
                bindDTO.setPlaceOrderButtonAuth(assertResult.getResult() ? ScGrayEnum.CAN_EDIT.getCode() : ScGrayEnum.NOT_DISPLAY.getCode());
                break;
            case CANTEEN_STALL_BIND_NORMAL_BUTTON:
                bindDTO.setNormalButtonAuth(assertResult.getResult() ? ScGrayEnum.CAN_EDIT.getCode() : ScGrayEnum.NOT_DISPLAY.getCode());
                break;
            case CANTEEN_STALL_BIND_ABNORMAL_BUTTON:
                bindDTO.setAbnormalButtonAuth(assertResult.getResult() ? ScGrayEnum.CAN_EDIT.getCode() : ScGrayEnum.NOT_DISPLAY.getCode());
                break;
            case CANTEEN_STALL_BIND_AUDITING_BUTTON:
                bindDTO.setAuditingButtonAuth(assertResult.getResult() ? ScGrayEnum.CAN_EDIT.getCode() : ScGrayEnum.NOT_DISPLAY.getCode());
                break;
            case CANTEEN_STALL_BIND_LOG_BUTTON:
                bindDTO.setLogButtonAuth(assertResult.getResult() ? ScGrayEnum.CAN_EDIT.getCode() : ScGrayEnum.NOT_DISPLAY.getCode());
                break;
            default:
                break;
        }
    }


    /**
     * 食堂信息审批鉴权-蜂鸟系统操作权限
     * @param auditDTO auditDTO
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     * @throws TException org.apache.thrift.TException
     */
    public void checkCanteenAuditAuthByHummingBird(WmCanteenAuditDTO auditDTO) throws WmSchCantException, TException {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.service.sc.auth.WmScCanteenAuthService.checkCanteenAuditAuthByHummingBird(com.sankuai.meituan.waimai.thrift.customer.domain.sc.WmCanteenAuditDTO)");
        log.info("[WmScCanteenAuthService.checkCanteenAuditAuthByHummingBird] input param: auditDTO = {}", JSONObject.toJSONString(auditDTO));
        WmAuditTask auditTask = auditServiceAdaptor.getTaskById(Integer.valueOf(auditDTO.getAuditSystemId()));
        if (auditTask == null) {
            log.error("[WmScCanteenAuthService.checkCanteenAuditAuthByHummingBird] auditTask is null. auditDTO = {}", JSONObject.toJSONString(auditDTO));
            throw new WmSchCantException(SERVER_ERROR, "蜂鸟审批任务查询为空");
        }

        if (!auditDTO.getUserId().equals(auditTask.getLastReceiveUid())) {
            log.warn("[WmScCanteenAuthService.checkCanteenAuditAuthByHummingBird] no auth. lastReceiveUid = {}, uid = {}",
                    auditTask.getLastReceiveUid(), auditDTO.getUserId());
            throw new WmSchCantException(BIZ_AUTH_ERROR, "抱歉，您暂无权限");
        }
    }



    public Boolean checkStallUnbindOperationAuth(Integer canteenPrimaryId, Integer uid) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.meituan.waimai.customer.service.sc.auth.WmScCanteenAuthService.checkStallUnbindOperationAuth(java.lang.Integer,java.lang.Integer)");
        // 统一走鉴权
        HashMap<Integer, CheckResult> resultMap = new HashMap<>();
        Boolean operationAuth;
        try {
            operationAuth = getOperationAuthAssertResult(uid, CANTEEN_STALL_BIND_UNBIND_BUTTON, canteenPrimaryId);
        } catch (Exception e) {
            log.error("食堂解绑鉴权失败", e);
            operationAuth = false;
        }
        return operationAuth;
    }

    /**
     * 校验解绑权限
     */
    public Map<Integer, CheckResult> checkStallUnbindAuth(Integer canteenPrimaryId, Integer uid, List<WmCanteenStallBindDTO> bindDTOList) {
        //统一走鉴权
        HashMap<Integer, CheckResult> resultMap = new HashMap<>();
        Boolean operationAuth;
        try {
            operationAuth = getOperationAuthAssertResult(uid, CANTEEN_STALL_BIND_UNBIND_BUTTON, canteenPrimaryId);
        } catch (Exception e) {
            log.error("食堂解绑鉴权失败", e);
            operationAuth = false;
        }
        List<Long> wmPoiIds = bindDTOList.stream().map(WmCanteenStallBindDTO::getWmPoiId).collect(Collectors.toList());
        Map<Long, Integer> remainingTransferCountMap = wmCanteenPoiCheckService.getRemainingTransferCount(wmPoiIds);

        for (WmCanteenStallBindDTO bindDTO : bindDTOList) {
            CheckResult checkResult = new CheckResult();
            try {
                if (!operationAuth) {
                    checkResult.setScGrayEnum(ScGrayEnum.NOT_DISPLAY);
                    checkResult.setReason("操作人无解绑按钮权限");
                    resultMap.put(bindDTO.getId(), checkResult);
                    continue;
                }
                //解绑门店已在绑定/解绑审批流程中，置灰
                if (Objects.equals(bindDTO.getWmPoiBindStatus().byteValue(), CanteenStallWmPoiBindStatusEnum.UNBINDING.getType())
                        || Objects.equals(bindDTO.getWmPoiBindStatus().byteValue(), CanteenStallWmPoiBindStatusEnum.REBINDING.getType())
                ) {
                    checkResult.setScGrayEnum(ScGrayEnum.READ_ONLY);
                    checkResult.setReason("该门店已在解绑/换绑审批流程中，不支持再次操作");
                    resultMap.put(bindDTO.getId(), checkResult);
                    continue;
                }
                //未绑定时，不展示按钮
                if (!bindDTO.getClueBindStatus().equals((int) CanteenStallClueBindStatusEnum.BIND_SUCCESS.getType())
                        && !bindDTO.getClueBindStatus().equals((int) CanteenStallClueBindStatusEnum.BINDING.getType())
                        && !bindDTO.getWmPoiBindStatus().equals((int) CanteenStallWmPoiBindStatusEnum.BIND_SUCCESS.getType())
                ) {
                    checkResult.setScGrayEnum(ScGrayEnum.NOT_DISPLAY);
                    checkResult.setReason("当前档口绑定任务未绑定");
                    resultMap.put(bindDTO.getId(), checkResult);
                    continue;
                }

                //解绑门店【解换绑操作次数】>=3，置灰
                if (remainingTransferCountMap.get(bindDTO.getWmPoiId()) >= MccConfig.getMaxUnbindRebind0perations()) {
                    checkResult.setScGrayEnum(ScGrayEnum.READ_ONLY);
                    checkResult.setReason("该门店解换绑操作次数已达最大次数，不支持再次操作");
                    resultMap.put(bindDTO.getId(), checkResult);
                    continue;
                }
                checkResult.setScGrayEnum(ScGrayEnum.CAN_EDIT);
                resultMap.put(bindDTO.getId(), checkResult);

            } catch (Exception e) {
                log.error("bindId:{}， 校验换绑权限异常", bindDTO.getId(), e);
                checkResult.setScGrayEnum(ScGrayEnum.NOT_DISPLAY);
                checkResult.setReason("系统权限判断异常");
                resultMap.put(bindDTO.getId(), checkResult);
            }

        }
        return resultMap;
    }


    public Boolean checkStallChangebindOperaAuth(Integer cateenPrimaryId, Integer uid) {
        // 统一走鉴权
        HashMap<Integer, CheckResult> resultMap = new HashMap<>();
        Boolean operationAuth;
        try {
            operationAuth = getOperationAuthAssertResult(uid, STALL_BIND_LIST_CHANGEBIND_BUTTON, cateenPrimaryId);
        } catch (Exception e) {
            log.error("校验食堂换绑权限失败", e);
            operationAuth = false;
        }
        return operationAuth;
    }

    /**
     * 食堂列表删除按钮鉴权
     *
     * @param canteenPrimaryId 食堂主键ID
     * @param uid              用户ID
     * @return
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    public boolean checkCanteenListDeleteButtonAuth(Integer canteenPrimaryId, Integer uid) throws WmSchCantException, TException {
        log.info("[WmScAuthService.checkCanteenListDeleteButtonAuth] canteenPrimaryId = {}, uid = {}", canteenPrimaryId, uid);
        return canteenThriftService.getOperationAuthAssertResult(uid, CANTEEN_DELETE_BUTTON_OPERATION_CODE, canteenPrimaryId);
    }

    

    /**
     * 校验换绑权限
     */
    public Map<Integer, CheckResult> checkStallChangebindAuth(Integer cateenPrimaryId, Integer uid, List<WmCanteenStallBindDTO> bindDTOList) {
        //统一走鉴权
        HashMap<Integer, CheckResult> resultMap = new HashMap<>();
        Boolean operationAuth;
        try {
            operationAuth = getOperationAuthAssertResult(uid, STALL_BIND_LIST_CHANGEBIND_BUTTON, cateenPrimaryId);
        } catch (Exception e) {
            log.error("校验食堂换绑权限失败", e);
            operationAuth = false;
        }

        List<Long> wmPoiIds = bindDTOList.stream().map(WmCanteenStallBindDTO::getWmPoiId).collect(Collectors.toList());
        Map<Long, Integer> remainingTransferCountMap = wmCanteenPoiCheckService.getRemainingTransferCount(wmPoiIds);

        for (WmCanteenStallBindDTO bindDTO : bindDTOList) {
            CheckResult checkResult = new CheckResult();
            try {
                if (!MccConfig.getIsNewFLowSwitch()) {
                    checkResult.setScGrayEnum(ScGrayEnum.NOT_DISPLAY);
                    checkResult.setReason("降级隐藏换绑按钮");
                    resultMap.put(bindDTO.getId(), checkResult);
                    continue;
                }

                if (!operationAuth) {
                    checkResult.setScGrayEnum(ScGrayEnum.NOT_DISPLAY);
                    checkResult.setReason("操作人无换绑按钮权限");
                    resultMap.put(bindDTO.getId(), checkResult);
                    continue;
                }
                //解绑门店已在绑定/解绑审批流程中，置灰
                if (Objects.equals(bindDTO.getWmPoiBindStatus().byteValue(), CanteenStallWmPoiBindStatusEnum.UNBINDING.getType())
                        || Objects.equals(bindDTO.getWmPoiBindStatus().byteValue(), CanteenStallWmPoiBindStatusEnum.REBINDING.getType())
                ) {
                    checkResult.setScGrayEnum(ScGrayEnum.READ_ONLY);
                    checkResult.setReason("该门店已在解绑/换绑审批流程中，不支持再次操作");
                    resultMap.put(bindDTO.getId(), checkResult);
                    continue;
                }
                //未绑定时，不展示按钮
                if (!bindDTO.getWmPoiBindStatus().equals((int) CanteenStallWmPoiBindStatusEnum.BIND_SUCCESS.getType())) {
                    checkResult.setScGrayEnum(ScGrayEnum.NOT_DISPLAY);
                    checkResult.setReason("当前档口绑定任务未绑定");
                    resultMap.put(bindDTO.getId(), checkResult);
                    continue;
                }
                //解绑门店【解换绑操作次数】>=3，置灰
                if (remainingTransferCountMap.get(bindDTO.getWmPoiId()) >= MccConfig.getMaxUnbindRebind0perations()) {
                    checkResult.setScGrayEnum(ScGrayEnum.READ_ONLY);
                    checkResult.setReason("该门店解换绑操作次数已达最大次数，不支持再次操作");
                    resultMap.put(bindDTO.getId(), checkResult);
                    continue;
                }
                checkResult.setScGrayEnum(ScGrayEnum.CAN_EDIT);
                resultMap.put(bindDTO.getId(), checkResult);

            } catch (Exception e) {
                log.error("bindId:{}， 校验换绑权限异常", bindDTO.getId(), e);
                checkResult.setScGrayEnum(ScGrayEnum.NOT_DISPLAY);
                checkResult.setReason("系统权限判断异常");
                resultMap.put(bindDTO.getId(), checkResult);
            }

        }
        return resultMap;
    }
}
