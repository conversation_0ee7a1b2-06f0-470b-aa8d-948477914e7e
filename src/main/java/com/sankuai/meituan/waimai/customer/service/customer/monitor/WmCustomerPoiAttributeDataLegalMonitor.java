package com.sankuai.meituan.waimai.customer.service.customer.monitor;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Sets;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerPoiAttributeDO;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerPoiAttributeService;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerConstants;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.monitor.MonitorCustomerPoiAttributeDataLegalDTO;
import com.sankuai.meituan.waimai.thrift.customer.domain.kp.WmCustomerKp;
import com.sankuai.meituan.waimai.thrift.domain.WmPoiAggre;
import joptsimple.internal.Strings;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 客户门店属性表数据合法性监控（MS模型迁移过程中保证数据一致性）
 */
@Slf4j
@Service
public class WmCustomerPoiAttributeDataLegalMonitor {

    @Autowired
    private WmCustomerPoiAttributeService wmCustomerPoiAttributeService;


    private final String MSG_PREFIX = "客户门店属性表数据合法性监控:";

    public String check(MonitorCustomerPoiAttributeDataLegalDTO param) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_2", "com.sankuai.meituan.waimai.customer.service.customer.monitor.WmCustomerPoiAttributeDataLegalMonitor.check(MonitorCustomerPoiAttributeDataLegalDTO)");
        try {
            Long wmPoiId = param.getWmPoiId();
            if (wmPoiId == null || wmPoiId <= 0) {
                return Strings.EMPTY;
            }
            List<WmCustomerPoiAttributeDO> wmCustomerPoiAttributeDoList = wmCustomerPoiAttributeService.searchAttributeByWmPoiId(wmPoiId);
            if (CollectionUtils.isEmpty(wmCustomerPoiAttributeDoList)) {
                //门店无客户属性记录（门店已经从客户解绑）
                return Strings.EMPTY;
            }
            int size = wmCustomerPoiAttributeDoList.size();
            if (size > 1) {
                return String.format("%s门店ID=%s存在多条客户属性记录", MSG_PREFIX, wmPoiId);
            }

        } catch (Exception e) {
            log.error("check::param = {}", JSON.toJSONString(param), e);
        }
        return Strings.EMPTY;
    }

    private String diffMsg(String pre, Map<String, Object> mapA, Map<String, Object> mapB, Set<String> keySets) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.service.customer.monitor.WmCustomerPoiAttributeDataLegalMonitor.diffMsg(java.lang.String,java.util.Map,java.util.Map,java.util.Set)");
        if (CollectionUtils.isEmpty(keySets)) {
            return Strings.EMPTY;
        }
        StringBuffer sb = new StringBuffer();
        sb.append(pre);
        for (String key : keySets) {
            sb.append(String.format("字段key为:%s，字段值value为:%s!=%s", key, mapA.get(key), mapB.get(key)));
        }
        return sb.toString();
    }

    private Map<String, Object> getAttributeMapForKp(WmCustomerPoiAttributeDO wmCustomerPoiAttributeDO) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_1", "com.sankuai.meituan.waimai.customer.service.customer.monitor.WmCustomerPoiAttributeDataLegalMonitor.getAttributeMapForKp(com.sankuai.meituan.waimai.customer.domain.WmCustomerPoiAttributeDO)");
        Map<String, Object> map = new HashMap<>();
        map.put("compellation", wmCustomerPoiAttributeDO.getKpCompellation());
        map.put("phoneNumEncryption", wmCustomerPoiAttributeDO.getPhoneNumEncryption());
        map.put("phoneNumToken", wmCustomerPoiAttributeDO.getPhoneNumToken());
        return map;
    }

    private Map<String, Object> getKpMap(WmCustomerKp WmCustomerKp) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.customer.monitor.WmCustomerPoiAttributeDataLegalMonitor.getKpMap(com.sankuai.meituan.waimai.thrift.customer.domain.kp.WmCustomerKp)");
        Map<String, Object> map = new HashMap<>();
        map.put("compellation", WmCustomerKp.getCompellation());
        map.put("phoneNumEncryption", WmCustomerKp.getPhoneNumEncryption());
        map.put("phoneNumToken", WmCustomerKp.getPhoneNumToken());
        return map;
    }

    private Map<String, Object> getAttributeMapForAggree(WmCustomerPoiAttributeDO wmCustomerPoiAttributeDO) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.customer.monitor.WmCustomerPoiAttributeDataLegalMonitor.getAttributeMapForAggree(com.sankuai.meituan.waimai.customer.domain.WmCustomerPoiAttributeDO)");
        Map<String, Object> map = new HashMap<>();
        map.put("mtCustomerId", wmCustomerPoiAttributeDO.getMtCustomerId());
        map.put("compellation", wmCustomerPoiAttributeDO.getKpCompellation());
        map.put("phoneNumEncryption", wmCustomerPoiAttributeDO.getPhoneNumEncryption());
        map.put("phoneNumToken", wmCustomerPoiAttributeDO.getPhoneNumToken());
        return map;
    }

    private Map<String, Object> getAgreeMap(WmPoiAggre wmPoiAggre) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.service.customer.monitor.WmCustomerPoiAttributeDataLegalMonitor.getAgreeMap(com.sankuai.meituan.waimai.thrift.domain.WmPoiAggre)");
        Map<String, Object> map = new HashMap<>();
        map.put("mtCustomerId", wmPoiAggre.getMt_customer_id());
        map.put("compellation", wmPoiAggre.getKp_compellation() == null ? "" : wmPoiAggre.getKp_compellation());
        map.put("phoneNumEncryption", wmPoiAggre.getPhone_num_encryption() == null ? "" : wmPoiAggre.getPhone_num_encryption());
        map.put("phoneNumToken", wmPoiAggre.getPhone_num_token() == null ? "" : wmPoiAggre.getPhone_num_token());
        return map;
    }

    /**
     * diffKey
     *
     * @param mapA
     * @param mapB
     * @return
     */
    private Set<String> diffMap(Map<String, Object> mapA, Map<String, Object> mapB) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.customer.monitor.WmCustomerPoiAttributeDataLegalMonitor.diffMap(java.util.Map,java.util.Map)");
        Set<String> diffSet = Sets.newHashSet();
        for (String key : mapA.keySet()) {
            if (!mapA.get(key).equals(mapB.get(key))) {
                diffSet.add(key);
            }
        }
        return diffSet;
    }
}
