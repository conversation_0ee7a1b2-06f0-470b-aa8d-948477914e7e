package com.sankuai.meituan.waimai.customer.service.customer.proxy;

import com.sankuai.meituan.poros.client.PorosHighLevelClientBuilder;
import com.sankuai.meituan.waimai.customer.config.KmsCustomerConfig;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.action.bulk.BulkRequest;
import org.elasticsearch.action.bulk.BulkResponse;
import org.elasticsearch.action.delete.DeleteRequest;
import org.elasticsearch.action.delete.DeleteResponse;
import org.elasticsearch.action.index.IndexRequest;
import org.elasticsearch.action.index.IndexResponse;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.action.update.UpdateRequest;
import org.elasticsearch.action.update.UpdateResponse;
import org.elasticsearch.client.RestHighLevelClient;
import org.springframework.stereotype.Service;

import java.io.IOException;

@Slf4j
@Service
public class RestHighLevelClientProxy {

    private RestHighLevelClient restHighLevelClient;

    public RestHighLevelClientProxy() {
        restHighLevelClient = PorosHighLevelClientBuilder.builder()
                .appKey("com.sankuai.waimai.e.customer")
                .clusterName(KmsCustomerConfig.getCustomerESClusterName())
                .accessKey(KmsCustomerConfig.getCustomerESAccessKey())
                .callESDirectly(true)
                .tcpKeepAlive(true)
                .build();
    }

    public SearchResponse search(SearchRequest searchRequest) throws IOException {
        return restHighLevelClient.search(searchRequest);
    }

    public IndexResponse index(IndexRequest indexRequest) throws IOException {
        return restHighLevelClient.index(indexRequest);
    }

    public BulkResponse bulk(BulkRequest bulkRequest) throws IOException {
        return restHighLevelClient.bulk(bulkRequest);
    }

    public UpdateResponse update(UpdateRequest updateRequest) throws IOException {
        return restHighLevelClient.update(updateRequest);
    }


    public DeleteResponse delete(DeleteRequest request) throws IOException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.customer.proxy.RestHighLevelClientProxy.delete(org.elasticsearch.action.delete.DeleteRequest)");
        return restHighLevelClient.delete(request);
    }

}
