package com.sankuai.meituan.waimai.customer.service.customer;

import com.alibaba.fastjson.JSON;
import com.sankuai.meituan.waimai.customer.ddd.adapter.MtCustomerKpAdapter;
import com.sankuai.meituan.waimai.customer.ddd.adapter.MtCustomerThriftServiceAdapter;
import com.sankuai.meituan.waimai.customer.domain.DcCustomerKp;
import com.sankuai.meituan.waimai.customer.service.customer.dto.DcCustomerKpQueryDto;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerConstants;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import com.sankuai.nibcus.inf.customer.client.dto.SignerDTO;
import com.sankuai.nibcus.inf.customer.client.enums.BusinessLineEnum;
import com.sankuai.nibcus.inf.customer.client.request.QualificationNumTypeGetCustomerIdRequest;
import com.sankuai.nibcus.inf.customer.client.request.SignerGetRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * 到餐客户签约人服务
 */
@Service
@Slf4j
public class DcCustomerKpService {

    @Autowired
    private MtCustomerThriftServiceAdapter mtCustomerThriftServiceAdapter;

    @Autowired
    private MtCustomerKpAdapter mtCustomerKpAdapter;


    /**
     * 根据资质编码或平台客户ID查询到餐业务线KP列表
     */
    public List<DcCustomerKp> getDcEffectiveCustomerKp(DcCustomerKpQueryDto dcCustomerKpQueryDto) throws WmCustomerException, TException {
        log.info("#getDcEffectiveCustomerKp-查询到餐客户生效的签约人信息-req:{}", JSON.toJSONString(dcCustomerKpQueryDto));
        checkGetDcEffectiveCustomerKpParams(dcCustomerKpQueryDto);
        //如果客户ID不为空，则优先使用客户ID查询
        Map<Long, List<SignerDTO>> customerKpMap;
        if (dcCustomerKpQueryDto.getMtCustomerId() != null){
            customerKpMap = mtCustomerKpAdapter.batchGetSigners(buildSignerGetRequest(dcCustomerKpQueryDto));
        }else {
            //使用资质编码查询
            //1、先根据资质编码查客户平台ID 列表，因为历史原因，同一个资质可能有多个平台客户存在
            List<Long> customerList = mtCustomerThriftServiceAdapter.getCustomerIdByQualificationNum(buildQualificationNumRequest(dcCustomerKpQueryDto));
            if (CollectionUtils.isEmpty(customerList)){
                log.warn("#getDcEffectiveCustomerKp-查询到餐客户生效的签约人信息-资质无复用的到餐客户:{}",dcCustomerKpQueryDto.getCustomerNumber());
                throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR,"资质无复用的到餐客户");
            }
            //过滤出来到餐业务线下的客户
            customerKpMap = mtCustomerKpAdapter.batchGetSigners(buildSignerGetRequest(dcCustomerKpQueryDto, customerList));

        }
        List<DcCustomerKp> resultList = new ArrayList<>();
        // 转换
        // kp如果重复，前端页面会以“客户-kp”的形式展示，供用户选择
        customerKpMap.forEach((key, signerDTOS) -> signerDTOS.forEach(signerDTO -> {
            DcCustomerKp dcCustomerKp = convertSignerDTOtoDcCustomerKp(signerDTO);
            dcCustomerKp.setDcPlatformId(key);
            resultList.add(dcCustomerKp);
        }));
        log.info("#getDcEffectiveCustomerKp-查询到餐客户生效的签约人信息-req:{}，size:{}", JSON.toJSONString(dcCustomerKpQueryDto),resultList.size());
        return resultList;
    }

    private void checkGetDcEffectiveCustomerKpParams(DcCustomerKpQueryDto dcCustomerKpQueryDto) throws WmCustomerException {
        if (StringUtils.isBlank(dcCustomerKpQueryDto.getCustomerNumber()) && dcCustomerKpQueryDto.getMtCustomerId() == null){
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR,"参数非法");
        }
    }

    private QualificationNumTypeGetCustomerIdRequest buildQualificationNumRequest(DcCustomerKpQueryDto dto){
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.service.customer.DcCustomerKpService.buildQualificationNumRequest(com.sankuai.meituan.waimai.customer.service.customer.dto.DcCustomerKpQueryDto)");
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.service.customer.DcCustomerKpService.buildQualificationNumRequest(com.sankuai.meituan.waimai.customer.service.customer.dto.DcCustomerKpQueryDto)");
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.service.customer.DcCustomerKpService.buildQualificationNumRequest(com.sankuai.meituan.waimai.customer.service.customer.dto.DcCustomerKpQueryDto)");
        QualificationNumTypeGetCustomerIdRequest request = new QualificationNumTypeGetCustomerIdRequest();
        request.setType(dto.getCustomerQuaPlatformType());
        request.setQualificationNum(dto.getCustomerNumber());
        request.setBusinessLineId(BusinessLineEnum.NIB_FOOD.getCode());
        return request;
    }

    private DcCustomerKp convertSignerDTOtoDcCustomerKp(SignerDTO signerDTO){
        DcCustomerKp dcCustomerKp = new DcCustomerKp();
        dcCustomerKp.setId(signerDTO.getId());
        dcCustomerKp.setName(signerDTO.getName());
        dcCustomerKp.setType(signerDTO.getType());
        dcCustomerKp.setIdentityCardNumber(signerDTO.getIdentityCardNumber());
        dcCustomerKp.setCertificateType(signerDTO.getCertificateType());
        dcCustomerKp.setMobileNo(signerDTO.getMobileNo());
        dcCustomerKp.setStatus(signerDTO.getStatus());
        return dcCustomerKp;

    }

    private SignerGetRequest buildSignerGetRequest(DcCustomerKpQueryDto dto){
        SignerGetRequest signerGetRequest = new SignerGetRequest();
        signerGetRequest.setCustomerIds(Collections.singletonList(dto.getMtCustomerId()));
        //默认查生效状态的签约人信息
        signerGetRequest.setStatus(1);
        signerGetRequest.setBusinessLineId(BusinessLineEnum.NIB_FOOD.getCode());
        return signerGetRequest;
    }

    private SignerGetRequest buildSignerGetRequest(DcCustomerKpQueryDto dto,List<Long> customerList){
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.service.customer.DcCustomerKpService.buildSignerGetRequest(com.sankuai.meituan.waimai.customer.service.customer.dto.DcCustomerKpQueryDto,java.util.List)");
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.service.customer.DcCustomerKpService.buildSignerGetRequest(com.sankuai.meituan.waimai.customer.service.customer.dto.DcCustomerKpQueryDto,java.util.List)");
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.service.customer.DcCustomerKpService.buildSignerGetRequest(com.sankuai.meituan.waimai.customer.service.customer.dto.DcCustomerKpQueryDto,java.util.List)");
        SignerGetRequest signerGetRequest = new SignerGetRequest();
        signerGetRequest.setCustomerIds(customerList);
        //默认查生效状态的签约人信息
        signerGetRequest.setStatus(1);
        signerGetRequest.setBusinessLineId(BusinessLineEnum.NIB_FOOD.getCode());
        return signerGetRequest;
    }

}
