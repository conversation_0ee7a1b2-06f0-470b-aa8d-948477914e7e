package com.sankuai.meituan.waimai.customer.service.dbus.customerPoiList;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.meituan.dbus.common.DbusUtils;
import com.meituan.dbus.thriftV2.utils.ThriftV2Utils;
import com.sankuai.meituan.waimai.customer.constant.WmCustomerConstant;
import com.sankuai.meituan.waimai.customer.constant.WmCustomerPoiListESFields;
import com.sankuai.meituan.waimai.customer.constant.customer.WmCustomerRelTableDbusEnum;
import com.sankuai.meituan.waimai.customer.dao.WmCustomerKpDBMapper;
import com.sankuai.meituan.waimai.customer.dao.WmCustomerPoiDBMapper;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerKpPoiDB;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerPoiDB;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerPoiQueryCondtionVo;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerPoiListEsService;
import com.sankuai.meituan.waimai.customer.service.kp.WmCustomerKpPoiService;
import com.sankuai.meituan.waimai.thrift.customer.constant.KpTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.ValidEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.kp.WmCustomerKp;
import com.sankuai.meituan.waimai.thrift.customer.domain.kp.WmCustomerKpPoi;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.action.bulk.BulkResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@Slf4j
public class WmCustomerKpPoiRelTableEventImpl implements ICustomerRelTableEvent {

    private static final String KP_ID = "kp_id";

    private static final String VALID = "valid";

    @Autowired
    private WmCustomerPoiDBMapper wmCustomerPoiDBMapper;

    @Autowired
    private WmCustomerKpDBMapper wmCustomerKpDBMapper;

    @Autowired
    private WmCustomerPoiListEsService esService;

    @Override
    public WmCustomerRelTableDbusEnum getTable() {
        return WmCustomerRelTableDbusEnum.TABLE_WM_CUSTOMER_KP_POI_REL;
    }

    @Override
    public String handleUpdate(Map<String, String> metaJsonData, String dataMapJson, String diffJson) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.dbus.customerPoiList.WmCustomerKpPoiRelTableEventImpl.handleUpdate(java.util.Map,java.lang.String,java.lang.String)");
        DbusUtils utils = ThriftV2Utils.newThriftV2InstanceForUpdate(metaJsonData, dataMapJson, diffJson);
        WmCustomerKpPoiDB bean = transToBo(utils.getAftMap());

        if (bean == null) {
            return null;
        }

        boolean isUpdate = checkUpdate(utils.getDiffMap());
        if (!isUpdate) {
            return null;
        }

        if (bean.getValid() != null && bean.getValid() == ValidEnum.VALID_YES.getValue()) {
            Long wmPoiId = bean.getWmPoiId();
            WmCustomerKp kp = getWmCustomerKp(bean.getKpId());
            int opManagerId = 0;
            String opManagerName = "";
            if (kp != null) {
                opManagerId = kp.getId();
                opManagerName = kp.getCompellation();
            }

            if (utils.getDiffMap().containsKey(WmCustomerPoiListESFields.WM_POI_ID.getDbField())) {
                Long oldWmPoiId = (Long) utils.getDiffMap().get(WmCustomerPoiListESFields.WM_POI_ID.getDbField());
                List<WmCustomerPoiDB> oldlist = getWmCustomerPoi(oldWmPoiId);
                String result = update(oldlist, 0, "");
                if (StringUtils.isNotBlank(result)) {
                    return result;
                }
            }
            List<WmCustomerPoiDB> newlist = getWmCustomerPoi(wmPoiId);
            return update(newlist, opManagerId, opManagerName);
        } else {
            Long wmPoiId = bean.getWmPoiId();
            List<WmCustomerPoiDB> oldlist = getWmCustomerPoi(wmPoiId);
            return update(oldlist, 0, "");
        }
    }

    @Override
    public String handleInsert(Map<String, String> metaJsonData, String dataMapJson) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.service.dbus.customerPoiList.WmCustomerKpPoiRelTableEventImpl.handleInsert(java.util.Map,java.lang.String)");
        DbusUtils utils = ThriftV2Utils.newThriftV2InstanceForInsert(metaJsonData, dataMapJson);
        WmCustomerKpPoiDB bean = transToBo(utils.getAftMap());
        if (bean == null) {
            return null;
        }
        WmCustomerKp kp = getWmCustomerKp(bean.getKpId());
        int opManagerId = 0;
        String opManagerName = "";
        if (kp != null) {
            opManagerId = kp.getId();
            opManagerName = kp.getCompellation();
        }
        List<WmCustomerPoiDB> list = getWmCustomerPoi(bean.getWmPoiId());
        return update(list, opManagerId, opManagerName);
    }

    @Override
    public String handleDelete(Map<String, String> metaJsonData, String dataMapJson) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.dbus.customerPoiList.WmCustomerKpPoiRelTableEventImpl.handleDelete(java.util.Map,java.lang.String)");
        DbusUtils utils = ThriftV2Utils.newThriftV2InstanceForDelete(metaJsonData, dataMapJson);
        WmCustomerKpPoiDB bean = transToBo(utils.getPreMap());
        if (bean == null) {
            return null;
        }
        List<WmCustomerPoiDB> list = getWmCustomerPoi(bean.getWmPoiId());
        return update(list, 0, "");
    }

    private WmCustomerKpPoiDB transToBo(Map<String, Object> aftMap) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.dbus.customerPoiList.WmCustomerKpPoiRelTableEventImpl.transToBo(java.util.Map)");
        String jsonString = JSON.toJSONString(aftMap);
        WmCustomerKpPoiDB wmCustomerKpPoiDB = JSON.parseObject(jsonString, WmCustomerKpPoiDB.class);
        return wmCustomerKpPoiDB;
    }

    private boolean checkUpdate(Map<String, Object> diffMap) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.dbus.customerPoiList.WmCustomerKpPoiRelTableEventImpl.checkUpdate(java.util.Map)");
        if (MapUtils.isEmpty(diffMap) || !(diffMap.containsKey(KP_ID) || diffMap.containsKey(WmCustomerPoiListESFields.WM_POI_ID.getDbField()) || diffMap.containsKey(VALID))) {
            //修改，判断范围是否发生了变化，如果未发生变化则不更新范围的rtree
            return false;
        }
        return true;
    }


    private WmCustomerKp getWmCustomerKp(Integer kpId) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_1", "com.sankuai.meituan.waimai.customer.service.dbus.customerPoiList.WmCustomerKpPoiRelTableEventImpl.getWmCustomerKp(java.lang.Integer)");
        if (kpId == null) {
            return null;
        }
        return wmCustomerKpDBMapper.selectByPrimaryKey(kpId);
    }


    private List<WmCustomerPoiDB> getWmCustomerPoi(Long wmPoiId) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.service.dbus.customerPoiList.WmCustomerKpPoiRelTableEventImpl.getWmCustomerPoi(java.lang.Long)");
        WmCustomerPoiQueryCondtionVo vo = new WmCustomerPoiQueryCondtionVo();
        vo.setWmPoiId(wmPoiId);
        return wmCustomerPoiDBMapper.selectCustomerPoiRelByCondition(vo);
    }


    private String update(List<WmCustomerPoiDB> list, Integer opManagerId, String opManagerName) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_2", "com.sankuai.meituan.waimai.customer.service.dbus.customerPoiList.WmCustomerKpPoiRelTableEventImpl.update(java.util.List,java.lang.Integer,java.lang.String)");
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        Map<Object, Map<String, Object>> map = Maps.newHashMap();
        for (WmCustomerPoiDB db : list) {
            log.info("db={},kpId={},compellation={}", opManagerId, opManagerName);
            map.put(db.getId(), esService.makeMap(new String[]{WmCustomerPoiListESFields.OP_MANAGER_ID.getField(), WmCustomerPoiListESFields.OP_MANAGER_NAME.getField()},
                    new Object[]{opManagerId, opManagerName}));
        }
        if (map == null || map.isEmpty()) {
            return null;
        }
        BulkResponse response = esService.bulkUpdate(map);
        if (response == null) {
            return "客户门店关系新增失败";
        } else {
            return null;
        }
    }
}
