package com.sankuai.meituan.waimai.customer.service.sc.thrift;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.sankuai.djdata.readata.openapi.QueryContext;
import com.sankuai.meituan.waimai.customer.adapter.*;
import com.sankuai.meituan.waimai.customer.config.MccConfig;
import com.sankuai.meituan.waimai.customer.dao.sc.WmScEnumDictMapper;
import com.sankuai.meituan.waimai.customer.domain.sc.WmScEnumDictDO;
import com.sankuai.meituan.waimai.customer.domain.sc.WmSchoolDB;
import com.sankuai.meituan.waimai.customer.service.sc.thirdworkplace.*;
import com.sankuai.meituan.waimai.customer.service.sc.thirdworkplace.config.OneServiceQueryMappingConfig;
import com.sankuai.meituan.waimai.customer.service.sc.thirdworkplace.wrapper.*;
import com.sankuai.meituan.waimai.gis.client.thrift.dto.city.WmOpenCity;
import com.sankuai.meituan.waimai.infra.constants.WmVirtualOrgRecursiveTypeEnum;
import com.sankuai.meituan.waimai.infra.constants.WmVirtualOrgSourceEnum;
import com.sankuai.meituan.waimai.infra.domain.WmEmploy;
import com.sankuai.meituan.waimai.infra.domain.WmUniAor;
import com.sankuai.meituan.waimai.infra.domain.WmVirtualOrg;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.*;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.thirdworkplace.*;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.thirdworkplace.DeliveryStatusEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.sc.thirdworkplace.*;
import com.sankuai.meituan.waimai.thrift.exception.WmSchCantException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

import static com.sankuai.meituan.waimai.thrift.customer.constant.sc.WmScCodeConstants.BIZ_PARA_ERROR;

@Slf4j
@Component
public class WmScThirdWorkplaceThriftAssemble {

    @Autowired
    private OneServiceDataConverter oneServiceDataConverter;

    @Autowired
    private WmAorServiceAdapter wmAorServiceAdapter;

    @Autowired
    private WmOpenCityServiceAdapter wmOpenCityServiceAdapter;

    @Autowired
    private QueryContextFactory queryContextFactory;

    @Autowired
    private ThirdWorkplaceOuterService thirdWorkplaceOuterService;

    @Autowired
    private WmScEnumDictMapper wmScEnumDictMapper;


    public QueryContext wmScThirdWorkplaceQueryBO2ListQueryContext(WmScThirdWorkplaceQueryBo queryBo)throws WmSchCantException {
        return queryContextFactory.buildListQueryContext(queryBo);
    }

    public QueryContext wmScThirdWorkplaceQueryBO2CountQueryContext(WmScThirdWorkplaceQueryBo queryBo) throws WmSchCantException {
        return queryContextFactory.buildCountQueryContext(queryBo);
    }

    public QueryContext wmScThirdWorkplaceQueryBO2MetricsQueryContext(WmScThirdWorkplaceQueryBo queryBo) throws WmSchCantException {
        return queryContextFactory.buildMetricsQueryContext(queryBo);
    }

    public WmScThirdWorkplaceQueryListDTO toQueryListDTO(OneServiceQueryResultBo oneServiceQueryResultBo, Long totalCount) {
        WmScThirdWorkplaceQueryListDTO result = new WmScThirdWorkplaceQueryListDTO();

        try {
            // 使用 oneServiceDataConverter 转换数据
            List<WmScThirdWorkplaceQueryListItem> businessItems =
                    oneServiceDataConverter.convertToWorkplaceListItems(
                            getOneServiceQueryMappingConfig(),
                            oneServiceQueryResultBo);

            List<WmScThirdWorkplaceQueryListItemDTO> dtoItems = new ArrayList<>();
            if (MccConfig.thirdWorkplaceConcurrentSwitch()) {
                // 并发转化
                dtoItems = concurrentConvertToWorkplaceListItemDTOs(businessItems);
            }else{
                dtoItems = convertToWorkplaceListItemDTOs(businessItems);
            }

            // 设置返回结果
            result.setTotal(totalCount);
            result.setList(dtoItems);
            result.setCode(200);
            result.setMessage("success");

        } catch (Exception e) {
            log.error("转换查询结果失败", e);
            result.setCode(500);
            result.setMessage("数据转换失败: " + e.getMessage());
            result.setList(Collections.emptyList());
            result.setTotal(0L);
        }

        return result;
    }

    public WmScThirdWorkplaceQueryMetricsDTO toQueryMetricsDTO(OneServiceQueryResultBo oneServiceQueryResultBo){
        WmScThirdWorkplaceQueryMetricsDTO metricsDTO = null;
        try {
            // 使用 oneServiceDataConverter 转换数据
            WmScThirdWorkplaceQueryMetrics businessItem =
                    oneServiceDataConverter.convertToWorkplaceMetrics(
                            getOneServiceQueryMappingConfig(),
                            oneServiceQueryResultBo);

            // 将业务对象转换为DTO对象
            metricsDTO =
                    convertToWorkplaceMetricsDTO(businessItem);
        } catch (Exception e) {
            log.error("转换查询结果失败", e);
        }
        return metricsDTO;
    }


    private List<WmScThirdWorkplaceQueryListItemDTO> convertToWorkplaceListItemDTOs(
            List<WmScThirdWorkplaceQueryListItem> businessItems) {

        if (businessItems == null || businessItems.isEmpty()) {
            return Collections.emptyList();
        }

        return businessItems.stream()
                .map(this::convertToWorkplaceListItemDTO)
                .collect(Collectors.toList());
    }

    private List<WmScThirdWorkplaceQueryListItemDTO> concurrentConvertToWorkplaceListItemDTOs(
            List<WmScThirdWorkplaceQueryListItem> businessItems) {

        if (businessItems == null || businessItems.isEmpty()) {
            return Collections.emptyList();
        }

        // 预加载所有需要的数据，避免在并发转换中重复查询
        PreloadedData preloadedData = preloadAllRequiredData(businessItems);

        // 使用并发流进行转换
        return businessItems.parallelStream()
                .map(item -> convertToWorkplaceListItemDTOWithPreloadedData(item, preloadedData))
                .collect(Collectors.toList());
    }

     /**
     * 预加载所有需要的数据
     */
    private PreloadedData preloadAllRequiredData(List<WmScThirdWorkplaceQueryListItem> businessItems) {
        // 收集所有需要查询的ID
        Set<Integer> aorIds = new HashSet<>();
        Set<Integer> cityIds = new HashSet<>();
        Set<Integer> employIds = new HashSet<>();
        Set<String> employMisIds = new HashSet<>();
        Set<Integer> schoolIds = new HashSet<>();

        for (WmScThirdWorkplaceQueryListItem item : businessItems) {
            if (item.getAor() != null) aorIds.add(item.getAor());
            if (item.getFirstPhysicalCity() != null) cityIds.add(item.getFirstPhysicalCity());
            if (item.getSecondPhysicalCity() != null) cityIds.add(item.getSecondPhysicalCity());

            // 城市经理和渠道经理使用数字ID
            if (item.getResponsiblePerson() != null) employIds.add(item.getResponsiblePerson());
            if (item.getChannelOwnerMis() != null) employIds.add(item.getChannelOwnerMis());

            // 校企经理使用字符串MisId
            if (item.getSchoolResponsiblePerson() != null) {
                employMisIds.add(item.getSchoolResponsiblePerson());
            }

            if (item.getSchoolId() != null) schoolIds.add(item.getSchoolId().intValue());
        }

        CompletableFuture<Map<Integer, WmUniAor>> aorFuture = CompletableFuture.supplyAsync(() ->
                thirdWorkplaceOuterService.batchGetAorInfo(aorIds));

        CompletableFuture<Map<Integer, WmOpenCity>> cityFuture = CompletableFuture.supplyAsync(() ->
                thirdWorkplaceOuterService.batchGetCityInfo(cityIds));

        CompletableFuture<Map<Integer, WmEmploy>> employByUidFuture = CompletableFuture.supplyAsync(() ->
                thirdWorkplaceOuterService.batchGetEmployInfo(employIds));

        // 城市蜂窝负责人，通过蜂窝id获取
        CompletableFuture<Map<Integer, WmEmploy>> employByAorIdFuture = CompletableFuture.supplyAsync(() ->
                thirdWorkplaceOuterService.batchGetEmployByAorId(aorIds));

        CompletableFuture<Map<String, WmEmploy>> employByMisFuture = CompletableFuture.supplyAsync(() ->
                thirdWorkplaceOuterService.batchGetEmployInfoByMis(employMisIds));

        CompletableFuture<Map<Integer, WmSchoolDB>> schoolFuture = CompletableFuture.supplyAsync(() ->
                thirdWorkplaceOuterService.batchGetSchoolInfo(schoolIds));

        CompletableFuture<Map<String, String>> pltFuture = CompletableFuture.supplyAsync(() ->
                buildPltNameMap());


        // 城市组织结构：使用蜂窝ID查询
        CompletableFuture<Map<Integer, List<WmVirtualOrg>>> cityOrgFuture = CompletableFuture.supplyAsync(() ->
                thirdWorkplaceOuterService.batchGetOrgInfoByAorId(aorIds, WmVirtualOrgSourceEnum.WAIMAI));

        try {
            CompletableFuture.allOf(aorFuture, cityFuture, employByUidFuture, employByMisFuture, employByAorIdFuture, schoolFuture, cityOrgFuture).join();

            Map<Integer, WmEmploy> employByUidMap = employByUidFuture.get();
            Map<String, WmEmploy> employByMisMap = employByMisFuture.get();

            // 校企组织结构：使用员工MIS查询
            CompletableFuture<Map<String, List<WmVirtualOrg>>> schoolEnterpriseOrgFuture = CompletableFuture.supplyAsync(() ->
                    thirdWorkplaceOuterService.batchGetOrgInfoByMisAndSource(employByMisMap, WmVirtualOrgSourceEnum.WAIMAI));

            // 校园送组织结构：使用员工ID查询
            CompletableFuture<Map<Integer, List<WmVirtualOrg>>> campusDeliveryOrgFuture = CompletableFuture.supplyAsync(() ->
                    thirdWorkplaceOuterService.batchGetOrgInfoByUid(employByUidMap, WmVirtualOrgSourceEnum.XIAO_YUAN_JU_HE_PEI_SONG));

            CompletableFuture.allOf(schoolEnterpriseOrgFuture, campusDeliveryOrgFuture).join();

            return new PreloadedData(
                    aorFuture.get(),
                    cityFuture.get(),
                    employByUidMap,
                    employByMisMap,
                    employByAorIdFuture.get(),
                    schoolFuture.get(),
                    cityOrgFuture.get(),
                    schoolEnterpriseOrgFuture.get(),
                    campusDeliveryOrgFuture.get(),
                    pltFuture.get()
            );
        } catch (Exception e) {
            log.error("预加载数据失败", e);
            return new PreloadedData(
                    Collections.emptyMap(),
                    Collections.emptyMap(),
                    Collections.emptyMap(),
                    Collections.emptyMap(),
                    Collections.emptyMap(),
                    Collections.emptyMap(),
                    Collections.emptyMap(),
                    Collections.emptyMap(),
                    Collections.emptyMap(),
                    Collections.emptyMap()
            );
        }
    }

    private Map<String, String> buildPltNameMap() {

        Map<String, String> result = new HashMap<>();

        List<WmScEnumDictDO> wmScEnumDictDOS = wmScEnumDictMapper.selectByEnumType((int) WmScEnumTypeEnum.COOPERATION_PLATFORM_V2.getType());
        if (CollectionUtil.isEmpty(wmScEnumDictDOS)){
            return result;
        }

        for (WmScEnumDictDO wmScEnumDictDO : wmScEnumDictDOS) {
            result.put(String.valueOf(wmScEnumDictDO.getEnumCode()), wmScEnumDictDO.getEnumDesc());
        }
        return result;
    }

    private WmScThirdWorkplaceQueryListItemDTO convertToWorkplaceListItemDTOWithPreloadedData(
            WmScThirdWorkplaceQueryListItem businessItem, PreloadedData preloadedData) {

        WmScThirdWorkplaceQueryListItemDTO dto = new WmScThirdWorkplaceQueryListItemDTO();

        // 获取线索ID
        Integer clueId = getClueIdFromPreloadedData(businessItem.getSchoolId().intValue(), preloadedData);

        // 基础信息字段
        dto.setSchoolId(businessItem.getSchoolId());
        dto.setSchoolName(businessItem.getSchoolName());

        // 蜂窝信息
        if (businessItem.getAor() != null) {
            dto.setHoneycombId(Long.valueOf(businessItem.getAor()));
            WmUniAor wmUniAor = preloadedData.getAorMap().get(businessItem.getAor());
            if (wmUniAor != null) {
                dto.setHoneycombName(wmUniAor.getName());
            }
        }

        // 城市信息
        String provinceCity = getCityNameFromPreloadedData(businessItem.getFirstPhysicalCity(), preloadedData) + "-" +
                getCityNameFromPreloadedData(businessItem.getSecondPhysicalCity(), preloadedData);
        dto.setProvinceCity(provinceCity);

        // 蜂窝类型-学校业务类型
        if (Objects.nonNull(businessItem.getAorType()) && Objects.nonNull(SchoolAorTypeEnum.getByType(businessItem.getAorType()))){
            SchoolAorTypeEnum schoolAorTypeEnum = SchoolAorTypeEnum.getByType(businessItem.getAorType());
            dto.setSchoolBusinessType(schoolAorTypeEnum.getName());
        }

        // 学校类型
        if (Objects.nonNull(businessItem.getScSchoolType()) && Objects.nonNull(SchoolTypeEnum.getByType(businessItem.getScSchoolType()))){
            SchoolTypeEnum schoolTypeEnum = SchoolTypeEnum.getByType(businessItem.getScSchoolType());
            dto.setSchoolType(schoolTypeEnum.getName());
        }

        // 学校分类
        if (Objects.nonNull(businessItem.getSchoolCategory()) && Objects.nonNull(SchoolCategoryEnum.getByType(businessItem.getSchoolCategory()))){
            SchoolCategoryEnum schoolCategoryEnum = SchoolCategoryEnum.getByType(businessItem.getSchoolCategory());
            dto.setSchoolCategory(schoolCategoryEnum.getName());
        }
        dto.setTeacherStudentCount(businessItem.getTeaStuNum());

        // 构造学校楼宇超链接字段
        if (Objects.nonNull(businessItem.getSchoolBuildingNum()) && businessItem.getSchoolBuildingNum() > 0){
            WmScThirdWorkplaceHyperlinkDTO schoolBuildingNumLink = new WmScThirdWorkplaceHyperlinkDTO();
            schoolBuildingNumLink.setDisplayKey(String.valueOf(businessItem.getSchoolBuildingNum()));
            schoolBuildingNumLink.setId(String.valueOf(clueId));
            dto.setBuildingCount(schoolBuildingNumLink);
        }

        dto.setRealOneToOne(Objects.nonNull(businessItem.getIsExclusivePlatformCooperation())
                && businessItem.getIsExclusivePlatformCooperation() == 1
                ? "是" : "否");

        // 其他平台相关字段
        dto.setOtherPlatformCount(businessItem.getSchoolOtherCoopPltfmCnt());
        dto.setOtherPlatformHyperlinks(convertToOtherPlatHyperlinksByPreloadData(businessItem.getSchoolOtherCoopPltfmInfo(),
                clueId,
                preloadedData.getPltNameMap()));

        dto.setOtherPlatformInnerOrders(businessItem.getOtherPlatformSchoolInPoiOrderCount());
        dto.setOtherPlatformOuterOrders(businessItem.getOtherPlatformSchoolOutPoiOrderCount());

        // 承包商相关字段
        dto.setContractorCount(businessItem.getSchoolContractorCnt());

        dto.setCanteenCount(businessItem.getScCanteenNum());
        dto.setDirectCanteenCount(businessItem.getDirectlyOperatedScCanteenNum());
        dto.setContractorCanteenCount(businessItem.getContractedScCanteenNum());

        // 档口相关字段
        dto.setStallCount(businessItem.getStallNum());
        dto.setOfflineOperatingStallCount(businessItem.getCafeteriaOfflineOpenStallNum());
        dto.setAvailableOnlineStallCount(businessItem.getCanteenPreOnlineStallNum());
        dto.setDirectStallCount(businessItem.getDirectlyOperatedCafeteriaOfflineOpenStallNum());
        dto.setDirectStallRatio(formatPenetrationValue(businessItem.getDirectlyOperatedCafeteriaOfflineOpenStallRate()));
        dto.setContractorStallCount(businessItem.getContractedCafeteriaOfflineOpenStallNum());
        dto.setContractorStallRatio(formatPenetrationValue(businessItem.getContractedCafeteriaOfflineOpenStallRate()));
        dto.setSingleCanteenStallCount(businessItem.getSingleCafeteriaOfflineOpenStallNum());
        dto.setSingleCanteenStallRatio(formatPenetrationValue(businessItem.getSingleCafeteriaOfflineOpenStallRate()));

        // 合伙人相关字段
        dto.setLeadPartnerCount(businessItem.getCluePartnerNum());
        dto.setLeadPartnerHyperlinks(convertToPartnerHyperlinks(businessItem.getLeadsSchoolPartnerInfos()));
        dto.setIntentionPartnerCount(businessItem.getIntentionPartnerNum());
        dto.setIntentionPartnerHyperlinks(convertToPartnerHyperlinks(businessItem.getIntentionSchoolPartnerInfos()));
        dto.setEntrustPartnerHyperlinks(convertToPartnerHyperlinks(businessItem.getMandateSchoolPartnerInfos()));
        dto.setSignedPartnerHyperlinks(convertToPartnerHyperlinks(businessItem.getSigningSchoolPartnerInfos()));

        // 合作状态相关字段
        dto.setCooperationStatus(CooperationStatusEnum.getNameByCode(businessItem.getSchoolCoStatus()));

        // 交付状态
        String deliveryStatus = DeliveryStatusEnum.getNameByCode(businessItem.getContractDeliverStatus());
        if (StringUtils.isNotBlank(deliveryStatus)){
            WmScThirdWorkplaceHyperlinkDTO deliveryStatusHyperlinkDTO = new WmScThirdWorkplaceHyperlinkDTO();
            deliveryStatusHyperlinkDTO.setDisplayKey(deliveryStatus);
            deliveryStatusHyperlinkDTO.setId(String.valueOf(businessItem.getContractDeliver()));
            dto.setDeliveryStatus(deliveryStatusHyperlinkDTO);
        }

        // 流失状态
        String churnStatus = ChurnStatusEnum.getNameByCode(businessItem.getContractLossStatus());
        if (StringUtils.isNotBlank(churnStatus)){
            WmScThirdWorkplaceHyperlinkDTO churnStatusHyperlinkDTO = new WmScThirdWorkplaceHyperlinkDTO();
            churnStatusHyperlinkDTO.setDisplayKey(churnStatus);
            churnStatusHyperlinkDTO.setId(String.valueOf(businessItem.getContractLoss()));
            dto.setChurnStatus(churnStatusHyperlinkDTO);
            dto.setChurnApprovalDate(businessItem.getContractLossApprovalDate());
        }

        // 签约类型
        dto.setContractType(SigningTypeEnum.getNameByCode(businessItem.getSchoolSigningType()));
        dto.setContractMethod(SignMethodEnum.getNameByCode(businessItem.getSchoolSigningMethod()));
        dto.setContractStartTime(businessItem.getContractBeginTime());
        dto.setContractEndTime(businessItem.getContractEndTime());
        dto.setContractOneToOne(Objects.nonNull(businessItem.getSchoolIsExclusiveCooperation())
                && businessItem.getSchoolIsExclusiveCooperation() == 1
                ? "是" : "否");
        dto.setIsBidding(
                Objects.nonNull(businessItem.getIsTenderExisted())
                        && businessItem.getIsTenderExisted() == 1
                        ? "是" : "否");
        DeliveryMethodEnum deliveryMethodEnum = DeliveryMethodEnum.getByCode(businessItem.getSchoolDeliveryMethod());
        if (Objects.nonNull(deliveryMethodEnum)){
            dto.setDeliveryMethod(deliveryMethodEnum.getName());
        }

        // 经营数据
        dto.setOnlineStallCount(convertToSimpleMetricDTO(
                businessItem.getOnlinePoiNum(), businessItem.getOnlinePoiNumMom()));
        dto.setOnlineStallPenetration(convertToPenetrationMetricDTO(
                businessItem.getCafeteriaOnlineStallInfiltrationRate(), businessItem.getCafeteriaOnlineStallInfiltrationRateMom()));
        dto.setOperatingStallCount(convertToSimpleMetricDTO(
                businessItem.getOpenPoiNum(), businessItem.getOpenPoiNumMom()));
        dto.setOperatingStallPenetration(convertToPenetrationMetricDTO(
                businessItem.getCafeteriaOpenStallInfiltrationRate(), businessItem.getCafeteriaOpenStallInfiltrationRateMom()));
        dto.setTradingStallCount(convertToSimpleMetricDTO(
                businessItem.getTxnPoiNum(), businessItem.getTxnPoiNumMom()));
        dto.setTradingStallPenetration(convertToPenetrationMetricDTO(
                businessItem.getCafeteriaTxnStallInfiltrationRate(), businessItem.getCafeteriaTxnStallInfiltrationRateMom()));
        dto.setStallSalesRate(convertToPenetrationMetricDTO(
                businessItem.getTxnrate(), businessItem.getTxnrateMom()));
        dto.setNewOnlineStallCount(convertToSimpleMetricDTO(
                businessItem.getNewPoiNum(), businessItem.getNewPoiNumMom()));
        dto.setActualPaymentAmount(convertToSimpleMetricDTO(
                businessItem.getFinActualAmt(), businessItem.getFinActualAmtMom()));
        dto.setOrderCount(convertToSimpleMetricDTO(
                businessItem.getFinOrdNum(), businessItem.getFinOrdNumMom()));
        dto.setAvgOrderValue(convertToSimpleMetricDTO(
                businessItem.getOrdAvgFinActualAmt(), businessItem.getOrdAvgFinActualAmtMom()));
        dto.setSingleOutput(convertToSimpleMetricDTO(
                businessItem.getPoiAvgSettleOrdNum(), businessItem.getPoiAvgSettleOrdNumMom()));
        dto.setTradingUserCount(convertToSimpleMetricDTO(
                businessItem.getFinUsrNum(), businessItem.getFinUsrNumMom()));
        dto.setNewCanteenUserCount(convertToSimpleMetricDTO(
                businessItem.getCafeteriaNewUserNum(), businessItem.getCafeteriaNewUserNumMom()));
        dto.setAvgTradingFrequency(convertToSimpleMetricDTO(
                businessItem.getUsrAvgFinOrdNum(), businessItem.getUsrAvgFinOrdNumMom()));

        if (businessItem.getAor() != null){
            // 城市经理
            WmEmploy cityManager = preloadedData.getEmployAorIdMap().get(businessItem.getAor());
            if (cityManager != null) {
                dto.setCityManager(buildStaffDTO(cityManager));
            }
            // 使用蜂窝ID查询城市组织架构
            List<WmVirtualOrg> orgList = preloadedData.getCityOrgMap().get(businessItem.getAor());
            if (orgList != null) {
                dto.setCityOrgStructure(orgList.stream()
                        .map(WmVirtualOrg::getName)
                        .collect(Collectors.joining("/")));
            }
        }

        // 校企经理
        if (businessItem.getSchoolResponsiblePerson() != null) {
            String misId = businessItem.getSchoolResponsiblePerson();
            WmEmploy schoolManager = preloadedData.getEmployMisMap().get(misId);
            if (schoolManager != null) {
                dto.setSchoolEnterpriseManager(buildStaffDTO(schoolManager));
                List<WmVirtualOrg> orgList = preloadedData.getSchoolEnterpriseOrgMap().get(misId);
                if (orgList != null) {
                    dto.setSchoolEnterpriseOrgStructure(orgList.stream()
                            .map(WmVirtualOrg::getName)
                            .collect(Collectors.joining("/")));
                }
                dto.setSchoolFollowUp(buildStaffDTO(schoolManager));
            }
        }

        // 渠道经理
        if (businessItem.getChannelOwnerMis() != null) {
            WmEmploy channelManager = preloadedData.getEmployMap().get(businessItem.getChannelOwnerMis());
            if (channelManager != null) {
                dto.setChannelManager(buildStaffDTO(channelManager));
                List<WmVirtualOrg> orgList = preloadedData.getCampusDeliveryOrgMap().get(businessItem.getChannelOwnerMis());
                if (orgList != null) {
                    dto.setCampusDeliveryOrgStructure(orgList.stream()
                            .map(WmVirtualOrg::getName)
                            .collect(Collectors.joining("/")));
                }
            }
        }

        return dto;
    }

    private String getCityNameFromPreloadedData(Integer cityId, PreloadedData preloadedData) {
        if (cityId == null) return "";

        WmOpenCity city = preloadedData.getCityMap().get(cityId);
        return city != null ? city.getCityName() : "";
    }

    private Integer getClueIdFromPreloadedData(Integer schoolId, PreloadedData preloadedData) {
        if (schoolId == null) return 0;

        WmSchoolDB school = preloadedData.getSchoolMap().get(schoolId);
        if (school != null && school.getWdcClueId() != null) {
            return school.getWdcClueId().intValue();
        }
        return 0;
    }

    /**
     * 将单个业务对象转换为DTO对象-串行化执行
     */
    private WmScThirdWorkplaceQueryListItemDTO convertToWorkplaceListItemDTO(
            WmScThirdWorkplaceQueryListItem businessItem) {

        WmScThirdWorkplaceQueryListItemDTO dto = new WmScThirdWorkplaceQueryListItemDTO();

        Integer clueId = thirdWorkplaceOuterService.getClueIdBySchoolId(businessItem.getSchoolId().intValue());
        // 基础信息字段
        dto.setSchoolId(businessItem.getSchoolId());
        dto.setSchoolName(businessItem.getSchoolName());
        // 构造蜂窝信息
        dto.setHoneycombId(Long.valueOf(businessItem.getAor()));
        WmUniAor wmUniAor = wmAorServiceAdapter.getAorInfoById(businessItem.getAor());
        if (Objects.nonNull(wmUniAor)) {
            dto.setHoneycombName(wmUniAor.getName());
        }

        // 拼接一级城市名称
        String provinceCity = getCityNameById(businessItem.getFirstPhysicalCity()) + "-" +
                // 拼接二级城市名称
                getCityNameById(businessItem.getSecondPhysicalCity());
        dto.setProvinceCity(provinceCity);

        // 蜂窝类型-学校业务类型
        if (Objects.nonNull(businessItem.getAorType()) && Objects.nonNull(SchoolAorTypeEnum.getByType(businessItem.getAorType()))){
            SchoolAorTypeEnum schoolAorTypeEnum = SchoolAorTypeEnum.getByType(businessItem.getAorType());
            dto.setSchoolBusinessType(schoolAorTypeEnum.getName());
        }

        // 学校类型
        if (Objects.nonNull(businessItem.getScSchoolType()) && Objects.nonNull(SchoolTypeEnum.getByType(businessItem.getScSchoolType()))){
            SchoolTypeEnum schoolTypeEnum = SchoolTypeEnum.getByType(businessItem.getScSchoolType());
            dto.setSchoolType(schoolTypeEnum.getName());
        }

        // 学校分类
        if (Objects.nonNull(businessItem.getSchoolCategory()) && Objects.nonNull(SchoolCategoryEnum.getByType(businessItem.getSchoolCategory()))){
            SchoolCategoryEnum schoolCategoryEnum = SchoolCategoryEnum.getByType(businessItem.getSchoolCategory());
            dto.setSchoolCategory(schoolCategoryEnum.getName());
        }
        dto.setTeacherStudentCount(businessItem.getTeaStuNum());

        // 构造学校楼宇超链接字段-跳转到学校楼宇信息页面
        if (Objects.nonNull(businessItem.getSchoolBuildingNum()) && businessItem.getSchoolBuildingNum() > 0){
            WmScThirdWorkplaceHyperlinkDTO schoolBuildingNumLink = new WmScThirdWorkplaceHyperlinkDTO();
            schoolBuildingNumLink.setDisplayKey(String.valueOf(businessItem.getSchoolBuildingNum()));
            schoolBuildingNumLink.setId(String.valueOf(clueId));
            dto.setBuildingCount(schoolBuildingNumLink);
        }

        dto.setRealOneToOne(Objects.nonNull(businessItem.getIsExclusivePlatformCooperation())
                && businessItem.getIsExclusivePlatformCooperation() == 1
                ? "是" : "否");

        // 其他平台相关字段
        dto.setOtherPlatformCount(businessItem.getSchoolOtherCoopPltfmCnt());
        dto.setOtherPlatformHyperlinks(convertToOtherPlatHyperlinks(businessItem.getSchoolOtherCoopPltfmInfo(), clueId));
        dto.setOtherPlatformInnerOrders(businessItem.getOtherPlatformSchoolInPoiOrderCount());
        dto.setOtherPlatformOuterOrders(businessItem.getOtherPlatformSchoolOutPoiOrderCount());

        // 承包商相关字段
        dto.setContractorCount(businessItem.getSchoolContractorCnt());

        dto.setCanteenCount(businessItem.getScCanteenNum());

        dto.setDirectCanteenCount(businessItem.getDirectlyOperatedScCanteenNum());
        dto.setContractorCanteenCount(businessItem.getContractedScCanteenNum());

        // 档口相关字段
        dto.setStallCount(businessItem.getStallNum());
        dto.setOfflineOperatingStallCount(businessItem.getCafeteriaOfflineOpenStallNum());
        dto.setAvailableOnlineStallCount(businessItem.getCanteenPreOnlineStallNum());
        dto.setDirectStallCount(businessItem.getDirectlyOperatedCafeteriaOfflineOpenStallNum());
        dto.setDirectStallRatio(formatPenetrationValue(businessItem.getDirectlyOperatedCafeteriaOfflineOpenStallRate()));
        dto.setContractorStallCount(businessItem.getContractedCafeteriaOfflineOpenStallNum());
        dto.setContractorStallRatio(formatPenetrationValue(businessItem.getContractedCafeteriaOfflineOpenStallRate()));
        dto.setSingleCanteenStallCount(businessItem.getSingleCafeteriaOfflineOpenStallNum());
        dto.setSingleCanteenStallRatio(formatPenetrationValue(businessItem.getSingleCafeteriaOfflineOpenStallRate()));

        // 合伙人相关字段
        dto.setLeadPartnerCount(businessItem.getCluePartnerNum());
        dto.setLeadPartnerHyperlinks(convertToPartnerHyperlinks(businessItem.getLeadsSchoolPartnerInfos()));
        dto.setIntentionPartnerCount(businessItem.getIntentionPartnerNum());
        dto.setIntentionPartnerHyperlinks(convertToPartnerHyperlinks(businessItem.getIntentionSchoolPartnerInfos()));
        dto.setEntrustPartnerHyperlinks(convertToPartnerHyperlinks(businessItem.getMandateSchoolPartnerInfos()));
        dto.setSignedPartnerHyperlinks(convertToPartnerHyperlinks(businessItem.getSigningSchoolPartnerInfos()));

        // 合作状态相关字段
        dto.setCooperationStatus(CooperationStatusEnum.getNameByCode(businessItem.getSchoolCoStatus()));

        // 交付状态，需要提供交付id前端构造超链接
        String deliveryStatus = DeliveryStatusEnum.getNameByCode(businessItem.getContractDeliverStatus());
        if (StringUtils.isNotBlank(deliveryStatus)){
            WmScThirdWorkplaceHyperlinkDTO deliveryStatusHyperlinkDTO = new WmScThirdWorkplaceHyperlinkDTO();
            deliveryStatusHyperlinkDTO.setDisplayKey(deliveryStatus);
            deliveryStatusHyperlinkDTO.setId(String.valueOf(businessItem.getContractDeliver()));
            dto.setDeliveryStatus(deliveryStatusHyperlinkDTO);
        }

        // 流失状态，需要提供流失id前端构造超链接
        String churnStatus = ChurnStatusEnum.getNameByCode(businessItem.getContractLossStatus());
        if (StringUtils.isNotBlank(churnStatus)){
            WmScThirdWorkplaceHyperlinkDTO churnStatusHyperlinkDTO = new WmScThirdWorkplaceHyperlinkDTO();
            churnStatusHyperlinkDTO.setDisplayKey(churnStatus);
            churnStatusHyperlinkDTO.setId(String.valueOf(businessItem.getContractLoss()));
            dto.setChurnStatus(churnStatusHyperlinkDTO);
            dto.setChurnApprovalDate(businessItem.getContractLossApprovalDate());
        }

        // 签约类型
        dto.setContractType(SigningTypeEnum.getNameByCode(businessItem.getSchoolSigningType()));
        // 签约方式
        dto.setContractMethod(SignMethodEnum.getNameByCode(businessItem.getSchoolSigningMethod()));
        dto.setContractStartTime(businessItem.getContractBeginTime());
        dto.setContractEndTime(businessItem.getContractEndTime());
        dto.setContractOneToOne(Objects.nonNull(businessItem.getSchoolIsExclusiveCooperation())
                && businessItem.getSchoolIsExclusiveCooperation() == 1
                ? "是" : "否");
        dto.setIsBidding(
                Objects.nonNull(businessItem.getIsTenderExisted())
                        && businessItem.getIsTenderExisted() == 1
                        ? "是" : "否");
        DeliveryMethodEnum deliveryMethodEnum = DeliveryMethodEnum.getByCode(businessItem.getSchoolDeliveryMethod());
        if (Objects.nonNull(deliveryMethodEnum)){
            dto.setDeliveryMethod(deliveryMethodEnum.getName());
        }

        // 经营数据
        dto.setOnlineStallCount(convertToSimpleMetricDTO(
                businessItem.getOnlinePoiNum(), businessItem.getOnlinePoiNumMom()));
        dto.setOnlineStallPenetration(convertToPenetrationMetricDTO(
                businessItem.getCafeteriaOnlineStallInfiltrationRate(), businessItem.getCafeteriaOnlineStallInfiltrationRateMom()));
        dto.setOperatingStallCount(convertToSimpleMetricDTO(
                businessItem.getOpenPoiNum(), businessItem.getOpenPoiNumMom()));
        dto.setOperatingStallPenetration(convertToPenetrationMetricDTO(
                businessItem.getCafeteriaOpenStallInfiltrationRate(), businessItem.getCafeteriaOpenStallInfiltrationRateMom()));
        dto.setTradingStallCount(convertToSimpleMetricDTO(
                businessItem.getTxnPoiNum(), businessItem.getTxnPoiNumMom()));
        dto.setTradingStallPenetration(convertToPenetrationMetricDTO(
                businessItem.getCafeteriaTxnStallInfiltrationRate(), businessItem.getCafeteriaTxnStallInfiltrationRateMom()));
        dto.setStallSalesRate(convertToPenetrationMetricDTO(
                businessItem.getTxnrate(), businessItem.getTxnrateMom()));
        dto.setNewOnlineStallCount(convertToSimpleMetricDTO(
                businessItem.getNewPoiNum(), businessItem.getNewPoiNumMom()));
        dto.setActualPaymentAmount(convertToSimpleMetricDTO(
                businessItem.getFinActualAmt(), businessItem.getFinActualAmtMom()));
        dto.setOrderCount(convertToSimpleMetricDTO(
                businessItem.getFinOrdNum(), businessItem.getFinOrdNumMom()));
        dto.setAvgOrderValue(convertToSimpleMetricDTO(
                businessItem.getOrdAvgFinActualAmt(), businessItem.getOrdAvgFinActualAmtMom()));
        dto.setSingleOutput(convertToSimpleMetricDTO(
                businessItem.getPoiAvgSettleOrdNum(), businessItem.getPoiAvgSettleOrdNumMom()));
        dto.setTradingUserCount(convertToSimpleMetricDTO(
                businessItem.getFinUsrNum(), businessItem.getFinUsrNumMom()));
        dto.setNewCanteenUserCount(convertToSimpleMetricDTO(
                businessItem.getCafeteriaNewUserNum(), businessItem.getCafeteriaNewUserNumMom()));
        dto.setAvgTradingFrequency(convertToSimpleMetricDTO(
                businessItem.getUsrAvgFinOrdNum(), businessItem.getUsrAvgFinOrdNumMom()));

        // 城市经理和城市组织节点信息 根据蜂窝Id获取
        WmEmploy cityManager = thirdWorkplaceOuterService.getEmployByAorId(businessItem.getAor());
        if (Objects.nonNull(cityManager)){
            dto.setCityManager(buildStaffDTO(cityManager));
        }
        // 使用蜂窝ID查询城市组织架构
        dto.setCityOrgStructure(thirdWorkplaceOuterService.buildOrgInfoByAorId(businessItem.getAor(), WmVirtualOrgSourceEnum.WAIMAI));

        // 校企经理和校企组织节点信息 根据数据侧学校负责任字段获取 mis
        WmEmploy schoolManager = thirdWorkplaceOuterService.getEmployInfoByMis(businessItem.getSchoolResponsiblePerson());
        if (Objects.nonNull(schoolManager)){
            dto.setSchoolEnterpriseManager(buildStaffDTO(schoolManager));
            dto.setSchoolEnterpriseOrgStructure(thirdWorkplaceOuterService.buildOrgInfo(schoolManager, WmVirtualOrgSourceEnum.WAIMAI, WmVirtualOrgRecursiveTypeEnum.BOTTOM_UP_NO_SELF));
        }

        // 渠道经理和渠道组织节点信息 根据数据侧渠道经理字段获取 uid
        WmEmploy channelManager = thirdWorkplaceOuterService.getEmployInfoByUid(businessItem.getChannelOwnerMis());
        if (Objects.nonNull(channelManager)){
            dto.setChannelManager(buildStaffDTO(channelManager));
            dto.setCampusDeliveryOrgStructure(thirdWorkplaceOuterService.buildOrgInfo(channelManager, WmVirtualOrgSourceEnum.XIAO_YUAN_JU_HE_PEI_SONG, WmVirtualOrgRecursiveTypeEnum.BOTTOM_UP));
        }

        // 学校跟进人 - 暂时取学校负责人-校企经理
        if (Objects.nonNull(schoolManager)){
            dto.setSchoolFollowUp(buildStaffDTO(schoolManager));
        }

        return dto;
    }

    /**
     * 将指标业务对象列表转换为DTO对象
     */
    private WmScThirdWorkplaceQueryMetricsDTO convertToWorkplaceMetricsDTO(
            WmScThirdWorkplaceQueryMetrics businessItem) {

        WmScThirdWorkplaceQueryMetricsDTO dto = new WmScThirdWorkplaceQueryMetricsDTO();

        // 基础统计字段
        dto.setSchoolCount(businessItem.getSchoolNum());
        dto.setCanteenCount(businessItem.getCanteenNum());
        dto.setTeacherStudentCount(businessItem.getTeaStuNum());
        dto.setLeadPartnerCount(businessItem.getCluePartnerNum());
        dto.setIntentionPartnerCount(businessItem.getIntentionPartnerNum());
        dto.setEntrustPartnerCount(businessItem.getEntrustPartnerNum());
        dto.setSignedPartnerCount(businessItem.getSignPartnerNum());

        // 指标相关字段
        dto.setStallCount(convertToSimpleMetricDTO(
                businessItem.getStallNum(), businessItem.getStallNumMom()));
        dto.setOfflineOperatingStallCount(convertToSimpleMetricDTO(
                businessItem.getCafeteriaOfflineOpenStallNum(), businessItem.getCafeteriaOfflineOpenStallNumMom()));
        dto.setAvailableOnlineStallCount(convertToSimpleMetricDTO(
                businessItem.getCanteenPreOnlineStallNum(), businessItem.getCanteenPreOnlineStallNumMom()));
        dto.setOnlineStallCount(convertToSimpleMetricDTO(
                businessItem.getOnlinePoiNum(), businessItem.getOnlinePoiNumMom()));
        dto.setOnlineStallPenetration(convertToPenetrationMetricDTO(
                businessItem.getCafeteriaOnlineStallInfiltrationRate(), businessItem.getCafeteriaOnlineStallInfiltrationRateMom()));
        dto.setOperatingStallCount(convertToSimpleMetricDTO(
                businessItem.getOpenPoiNum(), businessItem.getOpenPoiNumMom()));
        dto.setOperatingStallPenetration(convertToPenetrationMetricDTO(
                businessItem.getCafeteriaOpenStallInfiltrationRate(), businessItem.getCafeteriaOpenStallInfiltrationRateMom()));
        dto.setTradingStallCount(convertToSimpleMetricDTO(
                businessItem.getTxnPoiNum(), businessItem.getTxnPoiNumMom()));
        dto.setTradingStallPenetration(convertToPenetrationMetricDTO(
                businessItem.getCafeteriaTxnStallInfiltrationRate(), businessItem.getCafeteriaTxnStallInfiltrationRateMom()));
        dto.setStallSalesRate(convertToPenetrationMetricDTO(
                businessItem.getTxnrate(), businessItem.getTxnrateMom()));
        dto.setNewOnlineStallCount(convertToSimpleMetricDTO(
                businessItem.getNewPoiNum(), businessItem.getNewPoiNumMom()));
        dto.setActualPaymentAmount(convertToSimpleMetricDTO(
                businessItem.getFinActualAmt(), businessItem.getFinActualAmtMom()));
        dto.setOrderCount(convertToSimpleMetricDTO(
                businessItem.getFinOrdNum(), businessItem.getFinOrdNumMom()));
        dto.setAvgOrderValue(convertToSimpleMetricDTO(
                businessItem.getOrdAvgFinActualAmt(), businessItem.getOrdAvgFinActualAmtMom()));
        dto.setSingleOutput(convertToSimpleMetricDTO(
                businessItem.getPoiAvgSettleOrdNum(), businessItem.getPoiAvgSettleOrdNumMom()));
        dto.setTradingUserCount(convertToSimpleMetricDTO(
                businessItem.getFinUsrNum(), businessItem.getFinUsrNumMom()));
        dto.setNewCanteenUserCount(convertToSimpleMetricDTO(
                businessItem.getCafeteriaNewUserNum(), businessItem.getCafeteriaNewUserNumMom()));
        dto.setAvgTradingFrequency(convertToSimpleMetricDTO(
                businessItem.getUsrAvgFinOrdNum(), businessItem.getUsrAvgFinOrdNumMom()));

        return dto;
    }

    private WmScSimpleMetricDTO convertToPenetrationMetricDTO(String rate, String rateMom) {
        WmScSimpleMetricDTO dto = new WmScSimpleMetricDTO();

        // 处理当前值
        if (StringUtils.isBlank(rate) || "-".equals(rate)) {
            dto.setCurrentValue("0.00%");
        } else {
            try {
                double doubleValue = Double.parseDouble(rate);
                // 将小数转为百分比，乘以100并保留两位小数
                dto.setCurrentValue(String.format("%.2f%%", doubleValue * 100));
            } catch (NumberFormatException e) {
                dto.setCurrentValue(rate);
            }
        }

        // 处理环比值
        if (StringUtils.isBlank(rateMom) || "-".equals(rateMom)) {
            dto.setMomRate("0.00%");
        } else {
            try {
                double doubleValue = Double.parseDouble(rateMom);
                // 环比值乘以100并保留两位小数
                dto.setMomRate(String.format("%.2f%%", doubleValue * 100));
            } catch (NumberFormatException e) {
                dto.setMomRate(rateMom);
            }
        }

        return dto;
    }

    /**
     * 合伙人场景将ID列表转换为Hyperlink列表
     */
    private List<WmScThirdWorkplaceHyperlinkDTO> convertToPartnerHyperlinks(String infos) {
        if (StringUtils.isBlank(infos) || !infos.contains("partner")) {
            return Collections.emptyList();
        }

        List<WmScThirdWorkplaceHyperlinkDTO> hyperlinks = new ArrayList<>();

        try {
            // 先将infos转为List<Map<String, String>>
            List<Map<String, String>> mapList = parseJsonToMapList(infos);

            for (Map<String, String> map : mapList) {
                String id = map.get("partner");
                String name = map.get("partner_name");

                if (StringUtils.isNotBlank(id)) {
                    WmScThirdWorkplaceHyperlinkDTO hyperlink = new WmScThirdWorkplaceHyperlinkDTO();
                    hyperlink.setDisplayKey(StringUtils.isNotBlank(name) ? name : id);
                    try {
                        hyperlink.setId(id);
                        hyperlinks.add(hyperlink);
                    } catch (NumberFormatException e) {
                        log.warn("无法将ID转换为Integer: {}", id);
                    }
                }
            }
        } catch (Exception e) {
            log.warn("convertToPartnerHyperlinks转换超链接失败: {}", infos, e);
        }

        return hyperlinks;
    }

    /**
     * 其他平台场景将ID列表转换为Hyperlink列表-并发场景
     */
    private List<WmScThirdWorkplaceHyperlinkDTO> convertToOtherPlatHyperlinksByPreloadData(String infos, Integer clueId, Map<String, String> pltNameMap) {
        if (StringUtils.isBlank(infos) || !infos.contains("platform")) {
            return Collections.emptyList();
        }
        List<WmScThirdWorkplaceHyperlinkDTO> hyperlinks = new ArrayList<>();
        try {
            List<Map<String, String>> mapList = parseJsonToMapList(infos);
            for (Map<String, String> map : mapList) {
                String name = map.get("platform_name");
                String cooperationPlatform = map.get("cooperation_platform");

                String displayKey = "";
                if (StringUtils.isNotBlank(name)){
                    displayKey = name;
                }else{
                    displayKey = pltNameMap.getOrDefault(cooperationPlatform, "其他");
                }
                WmScThirdWorkplaceHyperlinkDTO hyperlink = new WmScThirdWorkplaceHyperlinkDTO();
                try {
                    hyperlink.setDisplayKey(displayKey);
                    hyperlink.setId(String.valueOf(clueId));
                    hyperlinks.add(hyperlink);
                } catch (ArithmeticException e) {
                    log.warn("数据转化失败,e:{}",e.getMessage(), e);
                }

            }
        } catch (Exception e) {
            log.warn("convertToOtherPlatHyperlinks转换超链接失败: {}", infos, e);
        }
        return hyperlinks;
    }

    /**
     * 其他平台场景将ID列表转换为Hyperlink列表-单线程
     */
    private List<WmScThirdWorkplaceHyperlinkDTO> convertToOtherPlatHyperlinks(String infos, Integer clueId) {
        if (StringUtils.isBlank(infos) || !infos.contains("platform")) {
            return Collections.emptyList();
        }
        List<WmScThirdWorkplaceHyperlinkDTO> hyperlinks = new ArrayList<>();
        try {
            List<Map<String, String>> mapList = parseJsonToMapList(infos);
            for (Map<String, String> map : mapList) {
                String name = map.get("platform_name");
                String cooperationPlatform = map.get("cooperation_platform");

                String displayKey = "";
                if (StringUtils.isNotBlank(name)){
                    displayKey = name;
                }else{
                    WmScEnumDictDO wmScEnumDictDO = wmScEnumDictMapper.selectByEnumTypeAndEnumCode((int) WmScEnumTypeEnum.COOPERATION_PLATFORM_V2.getType(), Integer.valueOf(cooperationPlatform));
                    displayKey = Objects.nonNull(wmScEnumDictDO) ? wmScEnumDictDO.getEnumDesc() : cooperationPlatform;
                }
                WmScThirdWorkplaceHyperlinkDTO hyperlink = new WmScThirdWorkplaceHyperlinkDTO();
                try {
                    hyperlink.setDisplayKey(displayKey);
                    hyperlink.setId(String.valueOf(clueId));
                    hyperlinks.add(hyperlink);
                } catch (ArithmeticException e) {
                    log.warn("数据转化失败,e:{}",e.getMessage(), e);
                }

            }
        } catch (Exception e) {
            log.warn("convertToOtherPlatHyperlinks转换超链接失败: {}", infos, e);
        }
        return hyperlinks;
    }

    /**
     * 将JSON字符串转换为List<Map<String, String>>
     */
    private List<Map<String, String>> parseJsonToMapList(String jsonStr) {
        if (StringUtils.isBlank(jsonStr)) {
            return Collections.emptyList();
        }

        try {
            // 先解析为JSONArray
            JSONArray jsonArray = JSON.parseArray(jsonStr);
            List<Map<String, String>> result = new ArrayList<>();
            for (int i = 0; i < jsonArray.size(); i++) {
                JSONObject jsonObject = jsonArray.getJSONObject(i);
                Map<String, String> map = new HashMap<>();

                // 将JSONObject转换为Map<String, String>
                for (String key : jsonObject.keySet()) {
                    Object value = jsonObject.get(key);
                    map.put(key, value != null ? value.toString() : null);
                }
                result.add(map);
            }
            return result;
        } catch (Exception e) {
            log.warn("解析JSON字符串失败: {}", jsonStr, e);
            return Collections.emptyList();
        }
    }

    /**
     * 指标数据构造
     */
    private WmScSimpleMetricDTO convertToSimpleMetricDTO(String value, String ratio) {
        WmScSimpleMetricDTO dto = new WmScSimpleMetricDTO();

        if (StringUtils.isBlank(value) || "-".equals(ratio)) {
            dto.setMomRate(value);
        }else{
            try {
                double doubleValue = Double.parseDouble(value);
                // 保留两位小数
                dto.setCurrentValue(String.format("%.2f", doubleValue));
            } catch (NumberFormatException e) {
                dto.setCurrentValue(value);
            }
        }

        // 处理环比值
        if (StringUtils.isBlank(ratio) || "-".equals(ratio)) {
            dto.setMomRate(ratio);
        } else {
            try {
                double doubleValue = Double.parseDouble(ratio);
                // 环比值乘以100并保留两位小数
                dto.setMomRate(String.format("%.2f%%", doubleValue * 100));
            } catch (NumberFormatException e) {
                dto.setMomRate(ratio);
            }
        }

        return dto;
    }

    private OneServiceQueryMappingConfig getOneServiceQueryMappingConfig() throws WmSchCantException {
        try {
            return MccConfig.getOneServiceQueryMappingConfig();
        } catch (IOException e) {
            throw new WmSchCantException(BIZ_PARA_ERROR, "配置解析错误");
        }
    }

    public OneServiceQueryMappingConfig getPublicOneServiceQueryMappingConfig() throws WmSchCantException {
        return getOneServiceQueryMappingConfig();
    }

    public OneServiceDataConverter getPublicOneServiceDataConverter() {
        return oneServiceDataConverter;
    }

    /**
     * 将业务对象列表转换为Excel模型列表
     */
    public List<WmScThirdWorkplaceQueryListItemExcelModel> convertToListItemExcelModels(List<WmScThirdWorkplaceQueryListItem> businessItems) {
        if (businessItems == null || businessItems.isEmpty()) {
            return Collections.emptyList();
        }

        return businessItems.stream()
                .map(this::convertToListItemExcelModel)
                .collect(Collectors.toList());
    }

    /**
     * 将单个业务对象转换为Excel模型
     */
    public WmScThirdWorkplaceQueryListItemExcelModel convertToListItemExcelModel(WmScThirdWorkplaceQueryListItem businessItem) {
        if (businessItem == null) {
            return null;
        }

        WmScThirdWorkplaceQueryListItemExcelModel listItemExcelModel = new WmScThirdWorkplaceQueryListItemExcelModel();

        // 基础信息字段
        listItemExcelModel.setSchoolId(handleStringValue(businessItem.getSchoolId()));
        listItemExcelModel.setSchoolName(handleStringValue(businessItem.getSchoolName()));
        listItemExcelModel.setHoneycombId(handleStringValue(businessItem.getAor()));

        // 构造蜂窝信息
        WmUniAor wmUniAor = wmAorServiceAdapter.getAorInfoById(businessItem.getAor());
        if (Objects.nonNull(wmUniAor)) {
            listItemExcelModel.setHoneycombName(handleStringValue(wmUniAor.getName()));
        }

        // 省市信息处理
        String provinceCity = getCityNameById(businessItem.getFirstPhysicalCity()) +
                getCityNameById(businessItem.getSecondPhysicalCity());
        listItemExcelModel.setProvinceCity(handleStringValue(provinceCity));

        // 枚举字段处理
        if (Objects.nonNull(businessItem.getAorType()) && Objects.nonNull(SchoolAorTypeEnum.getByType(businessItem.getAorType()))){
            SchoolAorTypeEnum schoolAorTypeEnum = SchoolAorTypeEnum.getByType(businessItem.getAorType());
            listItemExcelModel.setSchoolBusinessType(handleStringValue(schoolAorTypeEnum.getName()));
        }

        if (Objects.nonNull(businessItem.getScSchoolType()) && Objects.nonNull(SchoolTypeEnum.getByType(businessItem.getScSchoolType()))){
            SchoolTypeEnum schoolTypeEnum = SchoolTypeEnum.getByType(businessItem.getScSchoolType());
            listItemExcelModel.setSchoolType(handleStringValue(schoolTypeEnum.getName()));
        }

        if (Objects.nonNull(businessItem.getSchoolCategory()) && Objects.nonNull(SchoolCategoryEnum.getByType(businessItem.getSchoolCategory()))){
            SchoolCategoryEnum categoryEnum = SchoolCategoryEnum.getByType(businessItem.getSchoolCategory());
            listItemExcelModel.setSchoolCategory(handleStringValue(categoryEnum.getName()));
        }

        listItemExcelModel.setTeacherStudentCount(formatCountNumber(businessItem.getTeaStuNum()));
        listItemExcelModel.setBuildingCount(formatCountNumber(businessItem.getSchoolBuildingNum()));

        // 实时是否1-1
        listItemExcelModel.setRealOneToOne(Objects.nonNull(businessItem.getIsExclusivePlatformCooperation())
                && businessItem.getIsExclusivePlatformCooperation() == 1
                ? "是" : "否");

        // 其他平台相关字段
        listItemExcelModel.setOtherPlatformInfo(convertOtherPlatformInfoToExcelString(businessItem.getSchoolOtherCoopPltfmInfo()));
        listItemExcelModel.setOtherPlatformCount(formatCountNumber(businessItem.getSchoolOtherCoopPltfmCnt()));
        listItemExcelModel.setOtherPlatformInnerOrders(formatCountNumber(businessItem.getOtherPlatformSchoolInPoiOrderCount()));
        listItemExcelModel.setOtherPlatformOuterOrders(formatCountNumber(businessItem.getOtherPlatformSchoolOutPoiOrderCount()));

        // 承包商相关字段
        listItemExcelModel.setContractorCount(formatCountNumber(businessItem.getSchoolContractorCnt()));

        // 食堂-档口相关字段
        listItemExcelModel.setCanteenCount(formatCountNumber(businessItem.getScCanteenNum()));
        listItemExcelModel.setDirectCanteenCount(formatCountNumber(businessItem.getDirectlyOperatedScCanteenNum()));
        listItemExcelModel.setContractorCanteenCount(formatCountNumber(businessItem.getContractedScCanteenNum()));
        listItemExcelModel.setStallCount(formatCountNumber(businessItem.getStallNum()));
        listItemExcelModel.setOfflineOperatingStallCount(formatCountNumber(businessItem.getCafeteriaOfflineOpenStallNum()));
        listItemExcelModel.setAvailableOnlineStallCount(formatCountNumber(businessItem.getCanteenPreOnlineStallNum()));
        listItemExcelModel.setDirectStallCount(formatCountNumber(businessItem.getDirectlyOperatedCafeteriaOfflineOpenStallNum()));
        listItemExcelModel.setContractorStallCount(formatCountNumber(businessItem.getContractedCafeteriaOfflineOpenStallNum()));
        listItemExcelModel.setSingleCanteenStallCount(formatCountNumber(businessItem.getSingleCafeteriaOfflineOpenStallNum()));

        // 档口占比字段（百分比）
        listItemExcelModel.setDirectStallRatio(formatPenetrationValue(businessItem.getDirectlyOperatedCafeteriaOfflineOpenStallRate()));
        listItemExcelModel.setContractorStallRatio(formatPenetrationValue(businessItem.getContractedCafeteriaOfflineOpenStallRate()));
        listItemExcelModel.setSingleCanteenStallRatio(formatPenetrationValue(businessItem.getSingleCafeteriaOfflineOpenStallRate()));

        // 合伙人相关字段
        listItemExcelModel.setLeadPartnerCount(formatCountNumber(businessItem.getCluePartnerNum()));
        listItemExcelModel.setLeadPartnerInfo(convertPartnerInfoToExcelString(businessItem.getLeadsSchoolPartnerInfos()));
        listItemExcelModel.setIntentionPartnerCount(formatCountNumber(businessItem.getIntentionPartnerNum()));
        listItemExcelModel.setIntentionPartnerInfo(convertPartnerInfoToExcelString(businessItem.getIntentionSchoolPartnerInfos()));
        listItemExcelModel.setEntrustPartnerInfo(convertPartnerInfoToExcelString(businessItem.getMandateSchoolPartnerInfos()));
        listItemExcelModel.setSignedPartnerInfo(convertPartnerInfoToExcelString(businessItem.getSigningSchoolPartnerInfos()));

        // 合作状态相关字段
        listItemExcelModel.setCooperationStatus(handleStringValue(CooperationStatusEnum.getNameByCode(businessItem.getSchoolCoStatus())));
        listItemExcelModel.setDeliveryStatus(handleStringValue(DeliveryStatusEnum.getNameByCode(businessItem.getContractDeliverStatus())));
        listItemExcelModel.setLossStatus(handleStringValue(ChurnStatusEnum.getNameByCode(businessItem.getContractLossStatus())));
        listItemExcelModel.setChurnApprovalDate(handleStringValue(businessItem.getContractLossApprovalDate()));

        // 合同相关字段
        listItemExcelModel.setContractType(handleStringValue(SigningTypeEnum.getNameByCode(businessItem.getSchoolSigningType())));
        listItemExcelModel.setContractMethod(handleStringValue(SignMethodEnum.getNameByCode(businessItem.getSchoolSigningMethod())));
        listItemExcelModel.setContractStartTime(handleStringValue(businessItem.getContractBeginTime()));
        listItemExcelModel.setContractEndTime(handleStringValue(businessItem.getContractEndTime()));
        listItemExcelModel.setContractOneToOne(Objects.nonNull(businessItem.getSchoolIsExclusiveCooperation())
                && businessItem.getSchoolIsExclusiveCooperation() == 1
                ? "是" : "否");
        listItemExcelModel.setIsBidding(
                Objects.nonNull(businessItem.getIsTenderExisted())
                        && businessItem.getIsTenderExisted() == 1
                        ? "是" : "否");

        DeliveryMethodEnum deliveryMethodEnum = DeliveryMethodEnum.getByCode(businessItem.getSchoolDeliveryMethod());
        if (Objects.nonNull(deliveryMethodEnum)){
            listItemExcelModel.setDeliveryMethod(handleStringValue(deliveryMethodEnum.getName()));
        }

        // 指标相关字段（带环比）
        listItemExcelModel.setOnlineStallCount(formatMetricValue(businessItem.getOnlinePoiNum(), true));
        listItemExcelModel.setOnlineStallCountMom(formatMoMValue(businessItem.getOnlinePoiNumMom()));

        listItemExcelModel.setOnlineStallPenetration(formatPenetrationValue(businessItem.getCafeteriaOnlineStallInfiltrationRate()));
        listItemExcelModel.setOnlineStallPenetrationMom(formatMoMValue(businessItem.getCafeteriaOnlineStallInfiltrationRateMom()));

        listItemExcelModel.setOperatingStallCount(formatMetricValue(businessItem.getOpenPoiNum(), true));
        listItemExcelModel.setOperatingStallCountMom(formatMoMValue(businessItem.getOpenPoiNumMom()));

        listItemExcelModel.setOperatingStallPenetration(formatPenetrationValue(businessItem.getCafeteriaOpenStallInfiltrationRate()));
        listItemExcelModel.setOperatingStallPenetrationMom(formatMoMValue(businessItem.getCafeteriaOpenStallInfiltrationRateMom()));

        listItemExcelModel.setTradingStallCount(formatMetricValue(businessItem.getTxnPoiNum(), true));
        listItemExcelModel.setTradingStallCountMom(formatMoMValue(businessItem.getTxnPoiNumMom()));

        listItemExcelModel.setTradingStallPenetration(formatPenetrationValue(businessItem.getCafeteriaTxnStallInfiltrationRate()));
        listItemExcelModel.setTradingStallPenetrationMom(formatMoMValue(businessItem.getCafeteriaTxnStallInfiltrationRateMom()));

        listItemExcelModel.setStallSalesRate(formatPenetrationValue(businessItem.getTxnrate()));
        listItemExcelModel.setStallSalesRateMom(formatMoMValue(businessItem.getTxnrateMom()));

        listItemExcelModel.setNewOnlineStallCount(formatMetricValue(businessItem.getNewPoiNum(), true));
        listItemExcelModel.setNewOnlineStallCountMom(formatMoMValue(businessItem.getNewPoiNumMom()));

        listItemExcelModel.setActualPaymentAmount(formatMetricValue(businessItem.getFinActualAmt(), false));
        listItemExcelModel.setActualPaymentAmountMom(formatMoMValue(businessItem.getFinActualAmtMom()));

        listItemExcelModel.setOrderCount(formatMetricValue(businessItem.getFinOrdNum(), true));
        listItemExcelModel.setOrderCountMom(formatMoMValue(businessItem.getFinOrdNumMom()));

        listItemExcelModel.setAvgOrderValue(formatMetricValue(businessItem.getOrdAvgFinActualAmt(), false));
        listItemExcelModel.setAvgOrderValueMom(formatMoMValue(businessItem.getOrdAvgFinActualAmtMom()));

        listItemExcelModel.setSingleOutput(formatMetricValue(businessItem.getPoiAvgSettleOrdNum(), false));
        listItemExcelModel.setSingleOutputMom(formatMoMValue(businessItem.getPoiAvgSettleOrdNumMom()));

        listItemExcelModel.setTradingUserCount(formatMetricValue(businessItem.getFinUsrNum(), true));
        listItemExcelModel.setTradingUserCountMom(formatMoMValue(businessItem.getFinUsrNumMom()));

        listItemExcelModel.setNewCanteenUserCount(formatMetricValue(businessItem.getCafeteriaNewUserNum(), true));
        listItemExcelModel.setNewCanteenUserCountMom(formatMoMValue(businessItem.getCafeteriaNewUserNumMom()));

        listItemExcelModel.setAvgTradingFrequency(formatMetricValue(businessItem.getUsrAvgFinOrdNum(), false));
        listItemExcelModel.setAvgTradingFrequencyMom(formatMoMValue(businessItem.getUsrAvgFinOrdNumMom()));


        // 城市经理和城市组织节点信息 根据蜂窝id获取
        WmEmploy cityManager = thirdWorkplaceOuterService.getEmployByAorId(businessItem.getAor());
        if (Objects.nonNull(cityManager)){
            listItemExcelModel.setCityManager(buildStaffExcelInfo(cityManager));
        }

        // 使用蜂窝ID查询城市组织架构
        listItemExcelModel.setCityOrgStructure(thirdWorkplaceOuterService.buildOrgInfoByAorId(businessItem.getAor(), WmVirtualOrgSourceEnum.WAIMAI));

        // 校企经理和校企组织节点信息 通过mis获取
        WmEmploy schoolManager = thirdWorkplaceOuterService.getEmployInfoByMis(businessItem.getSchoolResponsiblePerson());
        if (Objects.nonNull(schoolManager)){
            listItemExcelModel.setSchoolEnterpriseManager(buildStaffExcelInfo(schoolManager));
            listItemExcelModel.setSchoolEnterpriseOrgStructure(thirdWorkplaceOuterService.buildOrgInfo(schoolManager, WmVirtualOrgSourceEnum.WAIMAI, WmVirtualOrgRecursiveTypeEnum.BOTTOM_UP_NO_SELF));
            // 学校跟进人 - 暂时取学校负责人-校企经理
            // listItemExcelModel.setSchoolFollowUp(buildStaffExcelInfo(schoolManager));
        }

        // 渠道经理和渠道组织节点信息 通过uid获取
        WmEmploy channelManager = thirdWorkplaceOuterService.getEmployInfoByUid(businessItem.getChannelOwnerMis());
        if (Objects.nonNull(channelManager)){
            listItemExcelModel.setChannelManager(buildStaffExcelInfo(channelManager));
            listItemExcelModel.setCampusDeliveryOrgStructure(thirdWorkplaceOuterService.buildOrgInfo(channelManager, WmVirtualOrgSourceEnum.XIAO_YUAN_JU_HE_PEI_SONG, WmVirtualOrgRecursiveTypeEnum.BOTTOM_UP));
        }

        return listItemExcelModel;
    }

    private String formatPenetrationValue(String rate) {
        if (StringUtils.isBlank(rate) || "-".equals(rate.trim())) {
            return "0.00%";
        }
        try {
            double doubleValue = Double.parseDouble(rate);
            // 将小数转为百分比，乘以100并保留两位小数
            return String.format("%.2f%%", doubleValue * 100);
        } catch (NumberFormatException e) {
            log.warn("无法解析渗透率数值: {}", rate);
            return "";
        }
    }

    private String formatPenetrationMoMValue(String penetrationMoM) {
        if (StringUtils.isBlank(penetrationMoM) || "-".equals(penetrationMoM.trim())) {
            return "";
        }

        String formattedValue;
        try {
            double doubleValue = Double.parseDouble(penetrationMoM);
            // 环比数据保留两位小数并添加百分号
            formattedValue = String.format("%.2f%%", doubleValue);
        } catch (NumberFormatException e) {
            // 如果无法解析为数字，直接使用原值
            formattedValue = penetrationMoM;
        }

        return formattedValue;

    }

    /**
     * 将合伙人信息转换为Excel显示字符串
     */
    private String convertPartnerInfoToExcelString(String infos) {
        if (StringUtils.isBlank(infos) || !infos.contains("partner")) {
            return "";
        }

        try {
            List<Map<String, String>> mapList = parseJsonToMapList(infos);
            List<String> partnerNames = new ArrayList<>();

            for (Map<String, String> map : mapList) {
                String name = map.get("partner_name");
                String id = map.get("partner");

                if (StringUtils.isNotBlank(name)) {
                    partnerNames.add(name);
                } else if (StringUtils.isNotBlank(id)) {
                    partnerNames.add(id);
                }
            }

            return String.join("; ", partnerNames);
        } catch (Exception e) {
            log.warn("转换合伙人信息失败: {}", infos, e);
            return "";
        }
    }

    private String formatCountNumber(Object value) {
        if (value == null) {
            return "";
        }

        String strValue = value.toString().trim();
        if ("-".equals(strValue) || StringUtils.isBlank(strValue)) {
            return "";
        }

        try {
            double doubleValue = Double.parseDouble(strValue);
            // 计数字段直接返回整数格式
            return String.valueOf((long) doubleValue);
        } catch (NumberFormatException e) {
            log.warn("无法解析计数数值: {}", value);
            return "";
        }
    }

    /**
     * 格式化主指标值
     */
    private String formatMetricValue(String value, boolean isCountField) {
        // 如果value为空或为"-"，直接返回空字符串
        if (StringUtils.isBlank(value) || "-".equals(value.trim())) {
            return "";
        }

        String formattedValue;
        try {
            double doubleValue = Double.parseDouble(value);
            if (isCountField) {
                // 计数字段不保留小数
                formattedValue = String.valueOf((long) doubleValue);
            } else {
                // 非计数字段保留两位小数
                formattedValue = String.format("%.2f", doubleValue);
            }
        } catch (NumberFormatException e) {
            // 如果无法解析为数字，直接使用原值
            formattedValue = value;
        }

        return formattedValue;
    }

    /**
     * 格式化环比数据，保留两位小数并添加百分号
     */
    private String formatMoMValue(String value) {
        // 如果value为空或为"-"，直接返回空字符串
        if (StringUtils.isBlank(value) || "-".equals(value.trim())) {
            return "0.00%";
        }

        String formattedValue;
        try {
            double doubleValue = Double.parseDouble(value);
            // 环比数据保留两位小数并添加百分号
            formattedValue = String.format("%.2f%%", doubleValue * 100);
        } catch (NumberFormatException e) {
            // 如果无法解析为数字，直接使用原值
            formattedValue = value;
        }

        return formattedValue;
    }

    /**
     * 将其他平台信息转换为Excel显示字符串
     */
    private String convertOtherPlatformInfoToExcelString(String infos) {
        if (StringUtils.isBlank(infos)) {
            return "";
        }

        try {
            List<Map<String, String>> mapList = parseJsonToMapList(infos);
            List<String> platformNames = new ArrayList<>();

            for (Map<String, String> map : mapList) {
                String name = map.get("platform_name");
                String cooperationPlatform = map.get("cooperation_platform");

                if (StringUtils.isNotBlank(name)) {
                    platformNames.add(name);
                } else if (StringUtils.isNotBlank(cooperationPlatform)) {
                    WmScEnumDictDO wmScEnumDictDO = wmScEnumDictMapper.selectByEnumTypeAndEnumCode((int) WmScEnumTypeEnum.COOPERATION_PLATFORM_V2.getType(), Integer.valueOf(cooperationPlatform));
                    platformNames.add(Objects.nonNull(wmScEnumDictDO) ? wmScEnumDictDO.getEnumDesc() : cooperationPlatform);
                }
            }

            return String.join("; ", platformNames);
        } catch (Exception e) {
            log.warn("转换其他平台信息失败: {}", infos, e);
            return "";
        }
    }

    private String getCityNameById(Integer cityId) {
        try {
            WmOpenCity city = wmOpenCityServiceAdapter.getCityByCityId(cityId);
            if (Objects.nonNull(city)) {
                return city.getCityName();
            }
        } catch (WmSchCantException e) {
            log.error("获取城市信息失败,id:{}", cityId);
        }
        return "";
    }

    /**
     * 将指标业务对象转换为Excel模型
     */
    public WmScThirdWorkplaceQueryMetricsExcelModel convertToMetricsExcelModel(WmScThirdWorkplaceQueryMetrics businessItem) {
        if (businessItem == null) {
            return null;
        }

        WmScThirdWorkplaceQueryMetricsExcelModel metricsExcelModel = new WmScThirdWorkplaceQueryMetricsExcelModel();

        metricsExcelModel.setSchoolNum(String.valueOf(businessItem.getSchoolNum()));
        metricsExcelModel.setCanteenNum(String.valueOf(businessItem.getCanteenNum()));
        metricsExcelModel.setStallNum(businessItem.getStallNum());
        metricsExcelModel.setStallNumMom(formatMomPercentage(businessItem.getStallNumMom()));
        metricsExcelModel.setCafeteriaOfflineOpenStallNum(businessItem.getCafeteriaOfflineOpenStallNum());
        metricsExcelModel.setCafeteriaOfflineOpenStallNumMom(formatMomPercentage(businessItem.getCafeteriaOfflineOpenStallNumMom()));
        metricsExcelModel.setCanteenPreOnlineStallNum(businessItem.getCanteenPreOnlineStallNum());
        metricsExcelModel.setCanteenPreOnlineStallNumMom(formatMomPercentage(businessItem.getCanteenPreOnlineStallNumMom()));
        metricsExcelModel.setOnlinePoiNum(businessItem.getOnlinePoiNum());
        metricsExcelModel.setOnlinePoiNumMom(formatMomPercentage(businessItem.getOnlinePoiNumMom()));
        metricsExcelModel.setOpenPoiNum(businessItem.getOpenPoiNum());
        metricsExcelModel.setOpenPoiNumMom(formatMomPercentage(businessItem.getOpenPoiNumMom()));
        metricsExcelModel.setNewPoiNum(businessItem.getNewPoiNum());
        metricsExcelModel.setNewPoiNumMom(formatMomPercentage(businessItem.getNewPoiNumMom()));
        metricsExcelModel.setCafeteriaOnlineStallInfiltrationRate(formatPenetrationValue(businessItem.getCafeteriaOnlineStallInfiltrationRate()));
        metricsExcelModel.setCafeteriaOnlineStallInfiltrationRateMom(formatMomPercentage(businessItem.getCafeteriaOnlineStallInfiltrationRateMom()));
        metricsExcelModel.setCafeteriaOpenStallInfiltrationRate(formatPenetrationValue(businessItem.getCafeteriaOpenStallInfiltrationRate()));
        metricsExcelModel.setCafeteriaOpenStallInfiltrationRateMom(formatMomPercentage(businessItem.getCafeteriaOpenStallInfiltrationRateMom()));
        metricsExcelModel.setCafeteriaTxnStallInfiltrationRate(formatPenetrationValue(businessItem.getCafeteriaTxnStallInfiltrationRate()));
        metricsExcelModel.setCafeteriaTxnStallInfiltrationRateMom(formatMomPercentage(businessItem.getCafeteriaTxnStallInfiltrationRateMom()));
        metricsExcelModel.setTxnrate(formatPenetrationValue(businessItem.getTxnrate()));
        metricsExcelModel.setTxnrateMom(formatMomPercentage(businessItem.getTxnrateMom()));
        metricsExcelModel.setFinActualAmt(formatMetricValue(businessItem.getFinActualAmt(), false));
        metricsExcelModel.setFinActualAmtMom(formatMomPercentage(businessItem.getFinActualAmtMom()));
        metricsExcelModel.setOrdAvgFinActualAmt(formatMetricValue(businessItem.getOrdAvgFinActualAmt(), false));
        metricsExcelModel.setOrdAvgFinActualAmtMom(formatMomPercentage(businessItem.getOrdAvgFinActualAmtMom()));
        metricsExcelModel.setFinOrdNum(businessItem.getFinOrdNum());
        metricsExcelModel.setFinOrdNumMom(formatMomPercentage(businessItem.getFinOrdNumMom()));
        metricsExcelModel.setFinUsrNum(businessItem.getFinUsrNum());
        metricsExcelModel.setFinUsrNumMom(formatMomPercentage(businessItem.getFinUsrNumMom()));
        metricsExcelModel.setUsrAvgFinOrdNum(formatMetricValue(businessItem.getUsrAvgFinOrdNum(), false));
        metricsExcelModel.setUsrAvgFinOrdNumMom(formatMomPercentage(businessItem.getUsrAvgFinOrdNumMom()));
        metricsExcelModel.setPoiAvgSettleOrdNum(formatMetricValue(businessItem.getPoiAvgSettleOrdNum(), false));
        metricsExcelModel.setPoiAvgSettleOrdNumMom(formatMomPercentage(businessItem.getPoiAvgSettleOrdNumMom()));
        metricsExcelModel.setCafeteriaNewUserNum(businessItem.getCafeteriaNewUserNum());
        metricsExcelModel.setCafeteriaNewUserNumMom(formatMomPercentage(businessItem.getCafeteriaNewUserNumMom()));
        metricsExcelModel.setTeaStuNum(businessItem.getTeaStuNum());
        metricsExcelModel.setTeaStuNumMom(formatMomPercentage(businessItem.getTeaStuNumMom()));
        metricsExcelModel.setTxnPoiNum(businessItem.getTxnPoiNum());
        metricsExcelModel.setTxnPoiNumMom(formatMomPercentage(businessItem.getTxnPoiNumMom()));

        return metricsExcelModel;
    }

    /**
     * 格式化环比数值百分比，保留两位小数
     */
    private String formatMomPercentage(String value) {
        // 如果value为空或为"-"，直接返回空字符串
        if (StringUtils.isBlank(value) || "-".equals(value.trim())) {
            return "0.00%";
        }

        try {
            double doubleValue = Double.parseDouble(value);
            return String.format("%.2f%%", doubleValue * 100);
        } catch (NumberFormatException e) {
            log.warn("无法解析百分比数值: {}", value);
            // 如果无法解析为数字，返回空字符串而不是原值
            return "";
        }
    }


    /**
     * 格式化渗透率与占比字段，保留两位小数
     */
    private String formatPercentage(String value) {
        // 如果value为空或为"-"，直接返回空字符串
        if (StringUtils.isBlank(value) || "-".equals(value.trim())) {
            return "";
        }

        try {
            double doubleValue = Double.parseDouble(value);
            return String.format("%.2f%%", doubleValue);
        } catch (NumberFormatException e) {
            log.warn("无法解析百分比数值: {}", value);
            // 如果无法解析为数字，返回空字符串而不是原值
            return "";
        }
    }

    /**
     * 处理字符串值，如果为"-"则返回空字符串
     */
    private String handleStringValue(Object value) {
        if (value == null) {
            return "";
        }

        String strValue = value.toString().trim();
        if ("-".equals(strValue)) {
            return "";
        }

        return strValue;
    }


    private WmScThirdWorkplaceStaffDTO buildStaffDTO(WmEmploy employ){
        if (Objects.isNull(employ)){
            return null;
        }
        WmScThirdWorkplaceStaffDTO wmScThirdWorkplaceStaffDTO = new WmScThirdWorkplaceStaffDTO();
        wmScThirdWorkplaceStaffDTO.setMisId(employ.getMisId());
        wmScThirdWorkplaceStaffDTO.setName(employ.getName());
        wmScThirdWorkplaceStaffDTO.setUid(employ.getUid());
        return wmScThirdWorkplaceStaffDTO;
    }

    private String buildStaffExcelInfo(WmEmploy employ){
        if (Objects.isNull(employ)){
            return "";
        }
        return String.format("%s(%s)", employ.getName(), employ.getMisId());
    }

    private static class PreloadedData {
        private final Map<Integer, WmUniAor> aorMap;
        private final Map<Integer, WmOpenCity> cityMap;
        private final Map<Integer, WmEmploy> employMap;
        private final Map<String, WmEmploy> employMisMap;
        private final Map<Integer, WmEmploy> employAorIdMap;
        private final Map<Integer, WmSchoolDB> schoolMap;
        private final Map<Integer, List<WmVirtualOrg>> cityOrgMap;           // 城市组织结构 - WAIMAI
        private final Map<String, List<WmVirtualOrg>> schoolEnterpriseOrgMap; // 校企组织结构 - WAIMAI
        private final Map<Integer, List<WmVirtualOrg>> campusDeliveryOrgMap;  // 校园送组织结构 - XIAO_YUAN_JU_HE_PEI_SONG
        private final Map<String, String> pltNameMap;

        public PreloadedData(Map<Integer, WmUniAor> aorMap,
                             Map<Integer, WmOpenCity> cityMap,
                             Map<Integer, WmEmploy> employMap,
                             Map<String, WmEmploy> employMisMap,
                             Map<Integer, WmEmploy> employAorIdMap,
                             Map<Integer, WmSchoolDB> schoolMap,
                             Map<Integer, List<WmVirtualOrg>> cityOrgMap,
                             Map<String, List<WmVirtualOrg>> schoolEnterpriseOrgMap,
                             Map<Integer, List<WmVirtualOrg>> campusDeliveryOrgMap,
                             Map<String, String> pltNameMap ) {
            this.aorMap = aorMap;
            this.cityMap = cityMap;
            this.employMap = employMap;
            this.employMisMap = employMisMap;
            this.employAorIdMap = employAorIdMap;
            this.schoolMap = schoolMap;
            this.cityOrgMap = cityOrgMap;
            this.schoolEnterpriseOrgMap = schoolEnterpriseOrgMap;
            this.campusDeliveryOrgMap = campusDeliveryOrgMap;
            this.pltNameMap = pltNameMap;
        }

        public Map<Integer, WmUniAor> getAorMap() { return aorMap; }
        public Map<Integer, WmOpenCity> getCityMap() { return cityMap; }
        public Map<Integer, WmEmploy> getEmployMap() { return employMap; }
        public Map<Integer, WmEmploy> getEmployAorIdMap() { return employAorIdMap; }
        public Map<String, WmEmploy> getEmployMisMap() { return employMisMap; }
        public Map<Integer, WmSchoolDB> getSchoolMap() { return schoolMap; }
        public Map<Integer, List<WmVirtualOrg>> getCityOrgMap() { return cityOrgMap; }
        public Map<String, List<WmVirtualOrg>> getSchoolEnterpriseOrgMap() { return schoolEnterpriseOrgMap; }
        public Map<Integer, List<WmVirtualOrg>> getCampusDeliveryOrgMap() { return campusDeliveryOrgMap; }
        public Map<String, String> getPltNameMap() { return pltNameMap; }
    }
}
