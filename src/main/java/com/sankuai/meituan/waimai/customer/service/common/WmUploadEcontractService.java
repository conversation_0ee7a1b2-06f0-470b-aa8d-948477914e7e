package com.sankuai.meituan.waimai.customer.service.common;

import com.meituan.sec.distributeplatform.thrift.ApiUploadFileParam;
import com.sankuai.meituan.auth.util.TimeUtil;
import com.sankuai.meituan.waimai.customer.adapter.ApiUploadFileServiceAdapter;
import com.sankuai.meituan.waimai.customer.util.SSOUtil;
import com.sankuai.meituan.waimai.s3cloud.util.MtS3CloudFileUtil;
import com.sankuai.meituan.waimai.scm.cloud.service.MtCloudService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;

/**
 * @description:
 * @author: liuyunjie05
 * @create: 2024/1/11 15:23
 */
@Service
@Slf4j
public class WmUploadEcontractService {

    @Resource
    private MtCloudService mtCloudService;

    @Resource
    private ApiUploadFileServiceAdapter apiUploadFileServiceAdapter;

    private static final String FILE_SUFFIX = ".xls";

    private static final String UPLOAD_APPKEY = "com.sankuai.waimai.e.customer";

    private static final String UPLOAD_FILE_SOURCE = "s3plus.sankuai.com";

    private static final String UPLOAD_FILETYPE_XLSX = "xlsx";

    private static final String UPLOAD_ENCRYPTION_LEVEL = "C3";

    private static final String UPLOAD_IS_INNER = "1";

    private static final String UPLOAD_WITH_WATERMARK = "1";

    public String uploadExcel(ByteArrayOutputStream os, SSOUtil.SsoUser user) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.service.common.WmUploadEcontractService.uploadExcel(java.io.ByteArrayOutputStream,com.sankuai.meituan.waimai.customer.util.SSOUtil$SsoUser)");
        byte[] bytes = os.toByteArray();

        String excelFileName = DigestUtils.md5Hex(bytes) + "_"  + TimeUtil.unixtime() + FILE_SUFFIX;
        log.info("WmUploadEcontractService#uploadExcel, excelFileName: {}", excelFileName);

        MtS3CloudFileUtil.uploadFileFromBytesForS3(bytes, excelFileName);
        // 获取美团云链接
        String s3Url = mtCloudService.getAbsoluteDownUrl(excelFileName, true, 0, (int) user.getId(), user.getName());
        log.info("WmUploadEcontractService#uploadExcel, s3Url: {}", s3Url);
        return uploadFile(excelFileName, s3Url, user);
    }

    /**
     * 上传文件到文枢
     *
     * @param fileName 文件名称
     * @param s3Url    s3链接
     * @param user     导出人
     * @return 文枢链接
     */
    private String uploadFile(String fileName, String s3Url, SSOUtil.SsoUser user) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.common.WmUploadEcontractService.uploadFile(java.lang.String,java.lang.String,com.sankuai.meituan.waimai.customer.util.SSOUtil$SsoUser)");
        ApiUploadFileParam apiUploadFileParam = getApiUploadFileParam(fileName, s3Url, user);
        return apiUploadFileServiceAdapter.uploadFile(apiUploadFileParam, UPLOAD_WITH_WATERMARK);
    }

    private ApiUploadFileParam getApiUploadFileParam(String fileName, String s3Url, SSOUtil.SsoUser user) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.common.WmUploadEcontractService.getApiUploadFileParam(java.lang.String,java.lang.String,com.sankuai.meituan.waimai.customer.util.SSOUtil$SsoUser)");
        ApiUploadFileParam apiUploadFileParam = new ApiUploadFileParam();
        apiUploadFileParam.setAppKey(UPLOAD_APPKEY);
        apiUploadFileParam.setFileName(fileName);
        apiUploadFileParam.setFileSource(UPLOAD_FILE_SOURCE);
        apiUploadFileParam.setFileType(UPLOAD_FILETYPE_XLSX);
        apiUploadFileParam.setIsInner(UPLOAD_IS_INNER);
        apiUploadFileParam.setMisId(user.getLogin());
        apiUploadFileParam.setS3Url(s3Url);
        apiUploadFileParam.setUserName(user.getName());
        apiUploadFileParam.setSecGrade(UPLOAD_ENCRYPTION_LEVEL);
        return apiUploadFileParam;
    }

}
