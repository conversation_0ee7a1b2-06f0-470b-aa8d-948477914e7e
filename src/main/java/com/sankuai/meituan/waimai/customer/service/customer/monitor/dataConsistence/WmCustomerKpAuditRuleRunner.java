package com.sankuai.meituan.waimai.customer.service.customer.monitor.dataConsistence;

import com.dianping.frog.sdk.alarm.DXUtil;
import com.dianping.frog.sdk.data.BinlogRawData;
import com.dianping.frog.sdk.data.DmlType;
import com.dianping.frog.sdk.data.RawData;
import com.dianping.frog.sdk.rpc.RpcService;
import com.dianping.frog.sdk.rpc.RpcServiceFactory;
import com.dianping.frog.sdk.runner.DefaultRuleRunner;
import com.dianping.frog.sdk.util.JsonUtils;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.lang3.StringUtils;

import java.util.Map;

public class WmCustomerKpAuditRuleRunner extends DefaultRuleRunner {

    @Override
    public boolean filter(RawData rawData, RawData... targetData) throws Exception {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.service.customer.monitor.dataConsistence.WmCustomerKpAuditRuleRunner.filter(com.dianping.frog.sdk.data.RawData,com.dianping.frog.sdk.data.RawData[])");
        BinlogRawData binlogRawData = (BinlogRawData) rawData;
        if (binlogRawData.getDmlType() == DmlType.DELETE) {
            return false;
        }
        String validStr = binlogRawData.getColumnInfoMap().get("valid").getNewValue().toString();
        if (StringUtils.isBlank(validStr)) {
            return false;
        }
        if (!validStr.equals("1")) {
            return false;
        }
        String tableName = binlogRawData.getRealTableName();
        if (tableName.equals("wm_customer_kp")) {
            String state = binlogRawData.getColumnInfoMap().get("state").getNewValue().toString();
            // 特批审核中、代理人审核中
            if (state.equals("30") || state.equals("50")) {
                return true;
            }
        } else if (tableName.equals("wm_customer_kp_temp")) {
            String state = binlogRawData.getColumnInfoMap().get("state").getNewValue().toString();
            // 特批审核中、代理人审核中
            if (state.equals("102") || state.equals("104")) {
                return true;
            }
        } else if (tableName.equals("wm_audit_task")) {
            String bizType = binlogRawData.getColumnInfoMap().get("biz_type").getNewValue().toString();
            // 特批认证、代理人授权
            if (!bizType.equals("43") && !bizType.equals("45")) {
                return false;
            }
            String state = binlogRawData.getColumnInfoMap().get("status").getNewValue().toString();
            // 审核通过、审核驳回
            if (state.equals("4") || state.equals("5")) {
                return true;
            }
        }
        return false;

    }

    @Override
    public String check(RawData rawData, RawData... rawData2) throws Exception {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.service.customer.monitor.dataConsistence.WmCustomerKpAuditRuleRunner.check(com.dianping.frog.sdk.data.RawData,com.dianping.frog.sdk.data.RawData[])");
        BinlogRawData binlogRawData = (BinlogRawData) rawData;
        String tableName = binlogRawData.getRealTableName();

        Map<String, Object> params = Maps.newHashMap();
        if (tableName.equals("wm_customer_kp")) {
            Integer kpId = Integer.valueOf(binlogRawData.getColumnInfoMap().get("id").getNewValue().toString());
            String customerIdStr = binlogRawData.getColumnInfoMap().get("customer_id").getNewValue().toString();
            if (StringUtils.isBlank(customerIdStr)) {
                return String.format("kpId:%s关联客户失败", kpId);
            }
            Integer val = Integer.parseInt(customerIdStr);
            if (val == null || val <= 0) {
                return String.format("kpId:%s关联客户失败", kpId);
            }
            String state = binlogRawData.getColumnInfoMap().get("state").getNewValue().toString();
            // 特批审核中、代理人审核中
            if (!state.equals("30") && !state.equals("50")) {
                return null;
            }
            params.put("kpId", kpId);
            // 审核中
            params.put("auditStatus", 1);

        } else if (tableName.equals("wm_customer_kp_temp")) {
            Integer kpId = Integer.valueOf(binlogRawData.getColumnInfoMap().get("kp_id").getNewValue().toString());
            String state = binlogRawData.getColumnInfoMap().get("state").getNewValue().toString();
            // 特批审核中、代理人审核中
            if (!state.equals("102") && !state.equals("104")) {
                return null;
            }
            params.put("kpId", kpId);
            // 审核中
            params.put("auditStatus", 1);
        } else if (tableName.equals("wm_audit_task")) {
            String bizType = binlogRawData.getColumnInfoMap().get("biz_type").getNewValue().toString();
            // 特批认证、代理人授权
            if (!bizType.equals("43") && !bizType.equals("45")) {
                return null;
            }
            String state = binlogRawData.getColumnInfoMap().get("status").getNewValue().toString();
            // 审核通过、审核驳回
            if (!state.equals("4") && !state.equals("5")) {
                return null;
            }
            Integer bizId = Integer.valueOf(binlogRawData.getColumnInfoMap().get("biz_id").getNewValue().toString());
            int auditStatus = state.equals("4") ? 2 : 3;
            params.put("bizId", bizId);
            params.put("auditStatus", auditStatus);
        }

        RpcService rpcService = RpcServiceFactory.getThriftInstance("com.sankuai.meituan.waimai.thrift.customer.service.WmCustomerKpThriftService",
                "com.sankuai.waimai.e.customer", 10000, null, "8430");
        String result = rpcService.invoke("monitorKpAudit",
                Lists.newArrayList("com.sankuai.meituan.waimai.thrift.customer.domain.customer.monitor.MonitorCustomerKpDTO"),
                Lists.newArrayList(JsonUtils.toJson(params)));
        if (!StringUtils.isBlank(result) && !"\"\"".equals(result)) {
            return result;
        }


        return null;

    }


    @Override
    public void alarm(String s, RawData rawData, RawData... rawData2) throws Exception {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_1", "com.sankuai.meituan.waimai.customer.service.customer.monitor.dataConsistence.WmCustomerKpAuditRuleRunner.alarm(String,RawData,RawData[])");
        //通过大象公众号发送告警消息, 收件人为告警配置中的告警接收人。
        DXUtil.sendAlarm(s);
    }

}
