package com.sankuai.meituan.waimai.customer.service.sc.thirdworkplace;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.sankuai.meituan.waimai.poi.annotation.ExcelExportField;
import lombok.Data;

@Data
@ExcelIgnoreUnannotated
public class WmScThirdWorkplaceQueryListItemExcelModel {

    @ExcelExportField(title = "日期范围", priority = 10)
    private String dateRange;

    @ExcelExportField(title = "学校ID", priority = 20)
    private String schoolId;

    @ExcelExportField(title = "学校名称", priority = 30)
    private String schoolName;

    @ExcelExportField(title = "蜂窝ID", priority = 40)
    private String honeycombId;

    @ExcelExportField(title = "蜂窝名称", priority = 50)
    private String honeycombName;

    @ExcelExportField(title = "省市", priority = 60)
    private String provinceCity;

    @ExcelExportField(title = "学校业务类型", priority = 70)
    private String schoolBusinessType;

    @ExcelExportField(title = "学校类型", priority = 80)
    private String schoolType;

    @ExcelExportField(title = "学校分类", priority = 90)
    private String schoolCategory;

    @ExcelExportField(title = "在校师生人数", priority = 100)
    private String teacherStudentCount;

    @ExcelExportField(title = "学校楼宇数量", priority = 110)
    private String buildingCount;

    @ExcelExportField(title = "实时是否1-1", priority = 120)
    private String realOneToOne;

    @ExcelExportField(title = "其他合作平台数量", priority = 130)
    private String otherPlatformCount;

    @ExcelExportField(title = "其他合作平台", priority = 140)
    private String otherPlatformInfo;

    @ExcelExportField(title = "其他合作平台校内单量", priority = 150)
    private String otherPlatformInnerOrders;

    @ExcelExportField(title = "其他合作平台校外单量", priority = 160)
    private String otherPlatformOuterOrders;

    @ExcelExportField(title = "承包商数量", priority = 170)
    private String contractorCount;

    @ExcelExportField(title = "食堂数量", priority = 180)
    private String canteenCount;

    @ExcelExportField(title = "直营食堂数", priority = 190)
    private String directCanteenCount;

    @ExcelExportField(title = "承包商食堂数", priority = 200)
    private String contractorCanteenCount;

    @ExcelExportField(title = "档口数量", priority = 210)
    private String stallCount;

    @ExcelExportField(title = "线下营业档口数", priority = 220)
    private String offlineOperatingStallCount;

    @ExcelExportField(title = "可上线档口数", priority = 230)
    private String availableOnlineStallCount;

    @ExcelExportField(title = "直营档口数量", priority = 240)
    private String directStallCount;

    @ExcelExportField(title = "直营档口占比", priority = 250)
    private String directStallRatio;

    @ExcelExportField(title = "承包商档口数量", priority = 260)
    private String contractorStallCount;

    @ExcelExportField(title = "承包商档口占比", priority = 270)
    private String contractorStallRatio;

    @ExcelExportField(title = "单店食堂档口数量", priority = 280)
    private String singleCanteenStallCount;

    @ExcelExportField(title = "单店食堂档口占比", priority = 290)
    private String singleCanteenStallRatio;

    @ExcelExportField(title = "线索合伙人数量", priority = 300)
    private String leadPartnerCount;

    @ExcelExportField(title = "线索合伙人信息", priority = 310)
    private String leadPartnerInfo;

    @ExcelExportField(title = "意向合伙人数量", priority = 320)
    private String intentionPartnerCount;

    @ExcelExportField(title = "意向合伙人信息", priority = 330)
    private String intentionPartnerInfo;

    @ExcelExportField(title = "委托合伙人信息", priority = 340)
    private String entrustPartnerInfo;

    @ExcelExportField(title = "签约合伙人信息", priority = 350)
    private String signedPartnerInfo;

    @ExcelExportField(title = "合作状态", priority = 360)
    private String cooperationStatus;

    @ExcelExportField(title = "交付状态", priority = 370)
    private String deliveryStatus;

    @ExcelExportField(title = "流失状态", priority = 380)
    private String lossStatus;

    @ExcelExportField(title = "流失审核通过日期", priority = 390)
    private String churnApprovalDate;

    @ExcelExportField(title = "签约类型", priority = 400)
    private String contractType;

    @ExcelExportField(title = "签约方式", priority = 410)
    private String contractMethod;

    @ExcelExportField(title = "合同/授权开始时间", priority = 420)
    private String contractStartTime;

    @ExcelExportField(title = "合同/授权结束时间", priority = 430)
    private String contractEndTime;

    @ExcelExportField(title = "是否1-1", priority = 440)
    private String contractOneToOne;

    @ExcelExportField(title = "是否招投标", priority = 450)
    private String isBidding;

    @ExcelExportField(title = "配送方式", priority = 460)
    private String deliveryMethod;

    @ExcelExportField(title = "在线档口数", priority = 470)
    private String onlineStallCount;

    @ExcelExportField(title = "在线档口数环比", priority = 480)
    private String onlineStallCountMom;

    @ExcelExportField(title = "在线档口渗透率", priority = 490)
    private String onlineStallPenetration;

    @ExcelExportField(title = "在线档口渗透率环比", priority = 500)
    private String onlineStallPenetrationMom;

    @ExcelExportField(title = "营业档口数", priority = 510)
    private String operatingStallCount;

    @ExcelExportField(title = "营业档口数环比", priority = 520)
    private String operatingStallCountMom;

    @ExcelExportField(title = "营业档口渗透率", priority = 530)
    private String operatingStallPenetration;

    @ExcelExportField(title = "营业档口渗透率环比", priority = 540)
    private String operatingStallPenetrationMom;

    @ExcelExportField(title = "交易档口数", priority = 550)
    private String tradingStallCount;

    @ExcelExportField(title = "交易档口数环比", priority = 560)
    private String tradingStallCountMom;

    @ExcelExportField(title = "交易档口渗透率", priority = 570)
    private String tradingStallPenetration;

    @ExcelExportField(title = "交易档口渗透率环比", priority = 580)
    private String tradingStallPenetrationMom;

    @ExcelExportField(title = "档口动销率", priority = 590)
    private String stallSalesRate;

    @ExcelExportField(title = "档口动销率环比", priority = 600)
    private String stallSalesRateMom;

    @ExcelExportField(title = "新增在线档口数", priority = 610)
    private String newOnlineStallCount;

    @ExcelExportField(title = "新增在线档口数环比", priority = 620)
    private String newOnlineStallCountMom;

    @ExcelExportField(title = "实付交易额", priority = 630)
    private String actualPaymentAmount;

    @ExcelExportField(title = "实付交易额环比", priority = 640)
    private String actualPaymentAmountMom;

    @ExcelExportField(title = "订单量", priority = 650)
    private String orderCount;

    @ExcelExportField(title = "订单量环比", priority = 660)
    private String orderCountMom;

    @ExcelExportField(title = "客单价", priority = 670)
    private String avgOrderValue;

    @ExcelExportField(title = "客单价环比", priority = 680)
    private String avgOrderValueMom;

    @ExcelExportField(title = "单产", priority = 690)
    private String singleOutput;

    @ExcelExportField(title = "单产环比", priority = 700)
    private String singleOutputMom;

    @ExcelExportField(title = "交易用户数", priority = 710)
    private String tradingUserCount;

    @ExcelExportField(title = "交易用户数环比", priority = 720)
    private String tradingUserCountMom;

    @ExcelExportField(title = "食堂新增用户数", priority = 730)
    private String newCanteenUserCount;

    @ExcelExportField(title = "食堂新增用户数环比", priority = 740)
    private String newCanteenUserCountMom;

    @ExcelExportField(title = "人均交易频次", priority = 750)
    private String avgTradingFrequency;

    @ExcelExportField(title = "人均交易频次环比", priority = 760)
    private String avgTradingFrequencyMom;

    @ExcelExportField(title = "城市组织结构", priority = 770)
    private String cityOrgStructure;

    @ExcelExportField(title = "城市责任人", priority = 780)
    private String cityManager;

    @ExcelExportField(title = "校企组织结构", priority = 790)
    private String schoolEnterpriseOrgStructure;

    @ExcelExportField(title = "校企经理", priority = 800)
    private String schoolEnterpriseManager;

    @ExcelExportField(title = "校园送组织结构", priority = 810)
    private String campusDeliveryOrgStructure;

    @ExcelExportField(title = "渠道经理", priority = 820)
    private String channelManager;
}