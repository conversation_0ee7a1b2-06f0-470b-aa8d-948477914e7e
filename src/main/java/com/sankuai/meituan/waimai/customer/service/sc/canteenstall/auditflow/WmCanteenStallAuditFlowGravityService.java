package com.sankuai.meituan.waimai.customer.service.sc.canteenstall.auditflow;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.sankuai.meituan.gravity.thrift.VariableUtils;
import com.sankuai.meituan.gravity.thrift.server.*;
import com.sankuai.meituan.waimai.customer.adapter.GravityThriftServiceAdapter;

import com.sankuai.meituan.waimai.customer.domain.sc.canteenstall.WmCanteenStallAuditTaskBO;
import com.sankuai.meituan.waimai.customer.domain.sc.canteenstall.WmCanteenStallAuditTaskDO;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.canteenstall.CanteenStallAuditNodeTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.canteenstall.CanteenStallAuditResultEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.canteenstall.CanteenStallAuditTaskTypeEnum;
import com.sankuai.meituan.waimai.thrift.exception.WmSchCantException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

import static com.sankuai.meituan.waimai.thrift.customer.constant.sc.WmScCodeConstants.BIZ_PARA_ERROR;
import static com.sankuai.meituan.waimai.thrift.customer.constant.sc.WmScCodeConstants.SERVER_ERROR;


/**
 * 食堂档口审批任务Gravity相关Service
 * <AUTHOR>
 * @date 2024/05/25
 * @email <EMAIL>
 */
@Slf4j
@Service
public class WmCanteenStallAuditFlowGravityService {

    @Autowired
    private GravityThriftServiceAdapter gravityThriftServiceAdapter;

    /**
     * 创建Gravity审批流程实例
     * @param taskDO taskDO
     * @return gravity流程实例ID
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    public String createAuditGravityInstance(WmCanteenStallAuditTaskDO taskDO) throws WmSchCantException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_2", "com.sankuai.meituan.waimai.customer.service.sc.canteenstall.auditflow.WmCanteenStallAuditFlowGravityService.createAuditGravityInstance(WmCanteenStallAuditTaskDO)");
        ProcessInstanceCreateRequest request = new ProcessInstanceCreateRequest();
        // 业务ID = 审批任务主键ID
        request.setBusinessKey(String.valueOf(taskDO.getId()));
        request.setProcessDefinitionKey(CanteenStallAuditTaskTypeEnum.getByType(taskDO.getAuditTaskType()).getFlowDefiniteKey());

        ProcessResultResponse resultResponse = gravityThriftServiceAdapter.createProcessInstanceWithReturn(request);
        if (!resultResponse.isSuccess() || resultResponse.getData() == null) {
            log.error("[WmCanteenStallAuditFlowGravityService.createDeliveryAuditGravityInstance] request = {}, resultResponse = {}",
                    JSON.toJSONString(request), JSON.toJSONString(resultResponse));
            throw new WmSchCantException(SERVER_ERROR, "Gravity流程实例创建失败");
        }
        return resultResponse.getData().getId();
    }

    /**
     * 根据GravityId查询流程实例获取当前审批节点
     * @param gravityId GravityId流程实例ID
     * @return 当前审批节点
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    public CanteenStallAuditNodeTypeEnum getGravityAuditNodeByGravityId(String gravityId) throws WmSchCantException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_2", "com.sankuai.meituan.waimai.customer.service.sc.canteenstall.auditflow.WmCanteenStallAuditFlowGravityService.getGravityAuditNodeByGravityId(java.lang.String)");
        List<TaskResponse> taskResponseList = gravityThriftServiceAdapter.getTasksByProcessInstanceIds(gravityId);
        log.info("[WmCanteenStallAuditFlowGravityService.getGravityAuditNodeByGravityId] taskResponseList = {}", taskResponseList);
        if (taskResponseList.size() > 1) {
            log.error("[WmCanteenStallAuditFlowGravityService.getGravityTaskResponseByGravityId] taskResponseList error. gravityId = {}", gravityId);
            throw new WmSchCantException(SERVER_ERROR, "查询活动任务失败");
        }

        if (CollectionUtils.isNotEmpty((taskResponseList))) {
            return CanteenStallAuditNodeTypeEnum.getByActivityId(taskResponseList.get(0).getTaskDefinitionKey());
        }

        ProcessInstanceResponse instanceResponse = gravityThriftServiceAdapter.getProcessInstanceById(gravityId);
        if (instanceResponse == null) {
            log.error("[WmCanteenStallAuditFlowGravityService.getGravityTaskResponseByGravityId] instanceResponse is null. gravityId = {}", gravityId);
            throw new WmSchCantException(SERVER_ERROR, "查询活动任务失败");
        }
        log.info("[WmCanteenStallAuditFlowGravityService.getGravityAuditNodeByGravityId] instanceResponse = {}", instanceResponse.toString());
        return CanteenStallAuditNodeTypeEnum.getByActivityId(instanceResponse.getActivityId());
    }

    /**
     * 驱动Gravity对流程驳回结束
     * @param auditTaskBO auditTaskBO
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    public void driveGravityProcessByAuditReject(WmCanteenStallAuditTaskBO auditTaskBO) throws WmSchCantException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.service.sc.canteenstall.auditflow.WmCanteenStallAuditFlowGravityService.driveGravityProcessByAuditReject(WmCanteenStallAuditTaskBO)");
        log.info("[WmCanteenStallAuditFlowGravityService.driveGravityProcessByAuditReject] input param: auditTaskBO = {}", JSONObject.toJSONString(auditTaskBO));
        WmCanteenStallAuditTaskDO taskDO = auditTaskBO.getTaskDO();
        // 1-查询Gravity流程的正在活动中任务
        TaskResponse taskResponse = gravityThriftServiceAdapter.getSingleTask(taskDO.getGravityId(), CanteenStallAuditNodeTypeEnum.getByType(taskDO.getAuditNode()).getAcitivityId());
        if (taskResponse == null) {
            log.error("[WmCanteenStallAuditFlowGravityService.driveGravityProcessByAuditReject] taskResponse is NULL. gravityId = {}, avtivityId = {}",
                    taskDO.getGravityId(), CanteenStallAuditNodeTypeEnum.getByType(taskDO.getAuditNode()).getAcitivityId());
            throw new WmSchCantException(BIZ_PARA_ERROR, "Gravity流程任务不存在");
        }

        // 2-驱动Gravity流程进行撤回结束
        TaskActionRequest taskActionRequest = new TaskActionRequest();
        taskActionRequest.setAssignee(String.valueOf(auditTaskBO.getOpUserUid()));
        taskActionRequest.addToVariables(VariableUtils.buildVariableStruct("auditResult", (int) CanteenStallAuditResultEnum.AUDIT_REJECT.getType()));
        ResultResponse resultResponse = gravityThriftServiceAdapter.completeTaskWithReturn(taskResponse.getId(), taskActionRequest);

        if (!resultResponse.isSuccess()) {
            log.error("[WmCanteenStallAuditFlowGravityService.driveGravityProcessByAuditReject] resultResponse not success. gravityId = {}, avtivityId = {}",
                    taskDO.getGravityId(), CanteenStallAuditNodeTypeEnum.getByType(taskDO.getAuditNode()).getAcitivityId());
            throw new WmSchCantException(BIZ_PARA_ERROR, "Gravity流程驱动失败");
        }
    }

    /**
     * 驱动Gravity对流程审批通过
     * @param auditTaskBO auditTaskBO
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    public void driveGravityProcessByAuditPass(WmCanteenStallAuditTaskBO auditTaskBO) throws WmSchCantException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.service.sc.canteenstall.auditflow.WmCanteenStallAuditFlowGravityService.driveGravityProcessByAuditPass(WmCanteenStallAuditTaskBO)");
        log.info("[WmCanteenStallAuditFlowGravityService.driveGravityProcessByAuditPass] input param: auditTaskBO = {}", JSONObject.toJSONString(auditTaskBO));
        WmCanteenStallAuditTaskDO taskDO = auditTaskBO.getTaskDO();
        // 1-查询Gravity流程的正在活动中任务
        TaskResponse taskResponse = gravityThriftServiceAdapter.getSingleTask(taskDO.getGravityId(), CanteenStallAuditNodeTypeEnum.getByType(taskDO.getAuditNode()).getAcitivityId());
        if (taskResponse == null) {
            log.error("[WmCanteenStallAuditFlowGravityService.driveGravityProcessByAuditPass] taskResponse is NULL. gravityId = {}, avtivityId = {}",
                    taskDO.getGravityId(), CanteenStallAuditNodeTypeEnum.getByType(taskDO.getAuditNode()).getAcitivityId());
            throw new WmSchCantException(BIZ_PARA_ERROR, "Gravity流程任务不存在");
        }

        // 2-驱动Gravity流程进行通过结束
        TaskActionRequest taskActionRequest = new TaskActionRequest();
        taskActionRequest.setAssignee(String.valueOf(auditTaskBO.getOpUserUid()));
        taskActionRequest.addToVariables(VariableUtils.buildVariableStruct("auditResult", (int) CanteenStallAuditResultEnum.AUDIT_PASS.getType()));
        ResultResponse resultResponse = gravityThriftServiceAdapter.completeTaskWithReturn(taskResponse.getId(), taskActionRequest);
        if (!resultResponse.isSuccess()) {
            log.error("[WmCanteenStallAuditFlowGravityService.driveGravityProcessByAuditPass] resultResponse not success. gravityId = {}, avtivityId = {}",
                    taskDO.getGravityId(), CanteenStallAuditNodeTypeEnum.getByType(taskDO.getAuditNode()).getAcitivityId());
            throw new WmSchCantException(BIZ_PARA_ERROR, "Gravity流程驱动失败");
        }
    }

}
