package com.sankuai.meituan.waimai.customer.service.kp.preverifier;

import com.sankuai.meituan.waimai.customer.config.MccConfig;
import com.sankuai.meituan.waimai.customer.constant.WmCustomerConstant;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerDB;
import com.sankuai.meituan.waimai.customer.service.gray.WmCustomerGrayService;
import com.sankuai.meituan.waimai.customer.service.kp.WmCustomerKpService;
import com.sankuai.meituan.waimai.customer.service.kp.operatorv2.preverifierv2.AgentAuthPreVerify;
import com.sankuai.meituan.waimai.customer.util.LuhnCheckUtil;
import com.sankuai.meituan.waimai.customer.util.ThrowUtil;
import com.sankuai.meituan.waimai.customer.util.jsonSchema.JsonSchemaValidatorResult;
import com.sankuai.meituan.waimai.customer.util.jsonSchema.JsonSchemaValidatorUtil;
import com.sankuai.meituan.waimai.thrift.customer.constant.*;
import com.sankuai.meituan.waimai.thrift.customer.domain.kp.WmCustomerKp;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import com.sankuai.meituan.waimai.thrift.exception.WmServerException;
import com.sankuai.meituan.waimai.thrift.util.StringUtil;
import com.sankuai.meituan.waimai.util.DateUtil;
import org.apache.commons.lang.StringUtils;
import org.joda.time.DateTime;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

/**
 * KP数据校验
 * <AUTHOR>
 */
@Component
public class KpDataPreVerify implements KpPreverifier {

    private static final Logger LOGGER = LoggerFactory.getLogger(KpDataPreVerify.class);

    @Autowired
    private WmCustomerKpService wmCustomerKpService;

    @Autowired
    private WmCustomerGrayService wmCustomerGrayService;

    @Override
    public void verify(WmCustomerDB wmCustomer, List<WmCustomerKp> oldWmCustomerKpList, List<WmCustomerKp> addKpList, List<WmCustomerKp> deleteKpList, List<WmCustomerKp> upgradeKpList) throws WmCustomerException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_2", "com.sankuai.meituan.waimai.customer.service.kp.preverifier.KpDataPreVerify.verify(com.sankuai.meituan.waimai.customer.domain.WmCustomerDB,java.util.List,java.util.List,java.util.List,java.util.List)");
        for (WmCustomerKp wmCustomerKp : addKpList) {
            verifyData(wmCustomerKp, wmCustomer);
        }
        for (WmCustomerKp wmCustomerKp : upgradeKpList) {
            verifyData(wmCustomerKp, wmCustomer);
        }
    }

    @Override
    public int order() {
        return 1;
    }

    /**
     * 是否是通过公私海线索新增拜访KP
     *
     * @param kp
     * @return
     */
    private boolean isContractorInsertVisitKpByWdcClue(WmCustomerKp kp) {
        return kp.getWdcClueId() != null && kp.getWdcClueId() > 0
                && KpTypeEnum.VISITKP.equals(KpTypeEnum.getByType(kp.getKpType()));
    }


    public void verifyData(WmCustomerKp kp, WmCustomerDB wmCustomerDB) throws WmCustomerException {
        byte kpType = kp.getKpType();
        KpTypeEnum kpTypeEnum = KpTypeEnum.getByType(kpType);
        if (kpTypeEnum == null) {
            ThrowUtil.throwClientError("KP类型不合法");
        }

        if (!isContractorInsertVisitKpByWdcClue(kp)) {
            if (StringUtil.isBlank(kp.getCompellation())) {
                ThrowUtil.throwClientError("姓名不能为空");
            }

            if (StringUtil.isBlank(kp.getPhoneNum())) {
                ThrowUtil.throwClientError("手机号不能为空");
            }
        }

        //校验法人KP属性
        if (KpTypeEnum.LEGAL.equals(kpTypeEnum)) {
            checkKpLegalData(wmCustomerDB, kp);
        }

        //校验签约人KP属性
        if (KpTypeEnum.SIGNER.equals(kpTypeEnum)) {
            checkSignerData(kp, wmCustomerDB);
        }

        //校验拜访KP属性
        if (KpTypeEnum.VISITKP.equals(kpTypeEnum)) {
            JsonSchemaValidatorResult valiResult = validateCustomerExtPro(kp.getVisitKPPro());
            if(!valiResult.isSuccess()){
                ThrowUtil.throwClientError(valiResult.getMessage());
            }
        }


        if (kp.getPhoneNum() != null && kp.getPhoneNum().length() > 15) {
            ThrowUtil.throwClientError("手机号码不正确");
        }

        if (StringUtil.isNotBlank(kp.getSpecialAttachment()) && kp.getSpecialAttachment().length() > 1024) {
            ThrowUtil.throwClientError("特批附件上传太多了");
        }

        if (StringUtil.isNotBlank(kp.getCreditCard()) && kp.getCreditCard().length() > 32) {
            ThrowUtil.throwClientError("银行卡号超长");
        }

        if (StringUtil.isNotBlank(kp.getCreditCard()) && !checkBankCardNum(kp.getCreditCard())) {
            ThrowUtil.throwClientError("银行卡号不正确");
        }

        if (StringUtil.isNotBlank(kp.getEmail()) && kp.getEmail().length() > 32) {
            ThrowUtil.throwClientError("邮箱超长");
        }

        if (StringUtil.isNotBlank(kp.getEmail()) && checkEmail(kp.getEmail())){
            ThrowUtil.throwClientError("邮箱格式错误");
        }

        if (StringUtil.isNotBlank(kp.getCertNumber()) && kp.getCertNumber().length() > 50) {
            ThrowUtil.throwClientError("证件号码超长");
        }

        if (StringUtil.isNotBlank(kp.getCompellation()) && kp.getCompellation().length() > 50) {
            ThrowUtil.throwClientError("姓名超长");
        }

    }

    /**
     * 校验签约人数据
     *
     * @param kp
     * @param wmCustomerDB
     * @throws WmCustomerException
     */
    private void checkSignerData(WmCustomerKp kp, WmCustomerDB wmCustomerDB) throws WmCustomerException {
        if (StringUtil.isBlank(kp.getCertNumber())) {
            ThrowUtil.throwClientError("证件编号不能为空");
        }
        KpSignerTypeEnum signerTypeEnum = KpSignerTypeEnum.getByType(kp.getSignerType());
        if (signerTypeEnum == null) {
            ThrowUtil.throwClientError("请选择签约类型");
        }
        if (KpSignerTypeEnum.AGENT.equals(signerTypeEnum)) {
            if (StringUtil.isBlank(kp.getAgentFrontIdcard())
                    && !wmCustomerKpService.checkAllowNoAgentAuthPic(wmCustomerDB, kp)) {
                ThrowUtil.throwClientError("代理人手持身份证不能为空");
            }
        }
        CertTypeEnum certTypeEnum = CertTypeEnum.getByType(kp.getCertType());
        if (certTypeEnum == null) {
            ThrowUtil.throwClientError("证件类型不能为空");
        }

        if (kp.getCertType() == CertTypeEnum.ID_CARD.getType()
                || kp.getCertType() == CertTypeEnum.ID_CARD_COPY.getType()
                || kp.getCertType() == CertTypeEnum.ID_CARD_TEMP.getType()) {

            if (kp.getCertNumber() == null || kp.getCertNumber().length() < 12) {
                ThrowUtil.throwClientError("证件号码填写错误");
            }

            String birthday = (kp.getCertNumber().length() == 18) ? kp.getCertNumber().substring(6, 14) : "19" + kp.getCertNumber().substring(6, 12);
            //获取出生日期
            Date yyyyMMdd = DateUtil.string2Date4Null(birthday, "yyyyMMdd");
            if (yyyyMMdd == null) {
                ThrowUtil.throwClientError("证件号码填写错误");
            }
            DateTime birthdayDate = new DateTime(yyyyMMdd.getTime());

            //若签约人为法人，则需校验是否满16周岁
            if (KpSignerTypeEnum.SIGNER.equals(signerTypeEnum)) {
                if (birthdayDate.plusYears(16).isAfterNow()) {
                    ThrowUtil.throwClientError("签约人为法人时需年满16周岁");
                }
            }

            //若签约人为个人/代理人，则需校验是否满18周岁
            if (KpSignerTypeEnum.AGENT.equals(signerTypeEnum) || KpSignerTypeEnum.LEGAL.equals(signerTypeEnum)) {
                if (birthdayDate.plusYears(18).isAfterNow()) {
                    ThrowUtil.throwClientError("签约人为个人/代理人时需年满18周岁");
                }
            }
        }

        // 校验KP银行流水
        checkKpBankStatement(kp, wmCustomerDB);

    }

    /**
     * 校验KP的银行流水必填
     * 
     * @param kp
     * @param wmCustomerDB
     * @throws WmCustomerException
     */
    public void checkKpBankStatement(WmCustomerKp kp, WmCustomerDB wmCustomerDB) throws WmCustomerException {

        // 身份证类型不执行校验
        if (CertTypeEnum.checkIdCardSet(kp.getCertType())) {
            return;
        }

        // 未命中允许不上传银行流水 && 非身份证类型 && 银行流水非空
        if (!wmCustomerGrayService.checkHitAllowNoBankStatementKpOperate(wmCustomerDB.getId().longValue())
                && StringUtils.isBlank(kp.getSpecialAttachment())) {
            ThrowUtil.throwClientError(WmCustomerConstant.KP_NO_BANK_STATEMENT_ERROR_MSG);
        }
    }

    /**
     * 对KP法人数据进行校验
     *
     * @param wmCustomer
     * @param kp
     */
    private void checkKpLegalData(WmCustomerDB wmCustomer, WmCustomerKp kp) throws WmCustomerException {
        if (StringUtil.isBlank(kp.getCertNumber())) {
            ThrowUtil.throwClientError("证件编号不能为空");
        }
        //新流程的法人KP只允许是身份证
        if (kp.getCertType() != CertTypeEnum.ID_CARD.getType()) {
            ThrowUtil.throwClientError("KP类型法人的证件类型只允许身份证");
        }
        //客户证件类型是个人证件不能新增KP法人数据
        if (wmCustomer.getCustomerType() == CustomerType.CUSTOMER_TYPE_IDCARD.getCode()) {
            ThrowUtil.throwClientError("客户资质为个人证件不能新增KP类型法人");
        }
        //KP名称必须与客户资质的法人信息一致
        if (StringUtils.isBlank(wmCustomer.getLegalPerson()) || !wmCustomer.getLegalPerson().equals(kp.getCompellation())) {
            ThrowUtil.throwClientError("KP名称与客户法人名称不一致");
        }
    }

    /**
     * 身份证号校验
     *
     * @throws WmServerException
     */
    public boolean checkBankCardNum(String bankCardNum) {
        if (!bankCardNum.matches("^\\d+$")) {
            return false;
        }
        return LuhnCheckUtil.luhnCheck(bankCardNum);
    }

    /**
     * 校验上级客户属性信息是否正确
     * @param visitKpPro
     * @return
     */
    private JsonSchemaValidatorResult validateCustomerExtPro(String visitKpPro){
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_1", "com.sankuai.meituan.waimai.customer.service.kp.preverifier.KpDataPreVerify.validateCustomerExtPro(java.lang.String)");
        JsonSchemaValidatorResult valiResult = new JsonSchemaValidatorResult();

        if(StringUtils.isEmpty(visitKpPro)){
            valiResult.setSuccess(true);
            return valiResult;
        }

        try{
            String proJsonSchemaStr = MccConfig.getVisitKpProJsonSchema();
            valiResult = JsonSchemaValidatorUtil.validateJsonByJsonNode(visitKpPro, proJsonSchemaStr);
        }catch (Exception e){
            LOGGER.error("校验KP属性异常，e");
            valiResult.setSuccess(false);
            valiResult.setMessage("校验拜访KP属性异常");
        }

        return valiResult;
    }

    /**
     * 邮箱格式校验
     *
     * @throws WmServerException
     */
    public boolean checkEmail(String email) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.kp.preverifier.KpDataPreVerify.checkEmail(java.lang.String)");
        return !email.matches("^\\w+([-+.]\\w+)*@\\w+([-.]\\w+)*\\.\\w+([-.]\\w+)*$");
    }

}
