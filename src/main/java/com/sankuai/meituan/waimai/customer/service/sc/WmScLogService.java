package com.sankuai.meituan.waimai.customer.service.sc;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.meituan.mtrace.thread.pool.ScheduledExecutorServiceTraceWrapper;
import com.sankuai.meituan.auth.util.CollectionUtils;
import com.sankuai.meituan.auth.util.TimeUtil;
import com.sankuai.meituan.waimai.customer.adapter.WmOpenCityServiceAdapter;
import com.sankuai.meituan.waimai.customer.adapter.WmPoiCategoryCacheServiceAdapter;
import com.sankuai.meituan.waimai.customer.config.MccScConfig;
import com.sankuai.meituan.waimai.customer.constant.KmsKeyNameEnum;
import com.sankuai.meituan.waimai.customer.constant.sc.*;
import com.sankuai.meituan.waimai.customer.dao.sc.*;
import com.sankuai.meituan.waimai.customer.domain.sc.*;
import com.sankuai.meituan.waimai.customer.domain.sc.canteenstall.WmCanteenStallBindDO;
import com.sankuai.meituan.waimai.customer.domain.sc.canteenstall.WmCanteenStallClueDO;
import com.sankuai.meituan.waimai.customer.domain.sc.canteenstall.WmCanteenStallLogBO;
import com.sankuai.meituan.waimai.customer.service.kp.sensitive.decrypt.KeyDecrypt;
import com.sankuai.meituan.waimai.customer.service.kp.sensitive.decrypt.KeyDecryptHandleService;
import com.sankuai.meituan.waimai.customer.service.sc.canteen.sensitive.WmScCanteenSensitiveWordsService;
import com.sankuai.meituan.waimai.customer.util.WmScTransUtil;
import com.sankuai.meituan.waimai.gis.client.thrift.dto.city.WmOpenCity;
import com.sankuai.meituan.waimai.oplog.thrift.domain.WmPoiOplog;
import com.sankuai.meituan.waimai.oplog.thrift.service.WmPoiOplogThriftService;
import com.sankuai.meituan.waimai.poi.domain.WmPoiOpLog;
import com.sankuai.meituan.waimai.poicategory.bo.WmPoiCateDic;
import com.sankuai.meituan.waimai.thrift.bo.WmCustomerDiffCellBo;
import com.sankuai.meituan.waimai.thrift.customer.constant.WmCoStatusEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.*;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.canteenstall.*;
import com.sankuai.meituan.waimai.thrift.customer.domain.sc.CanteenBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sc.SchoolBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sc.WmScCanIllegalPoi;
import com.sankuai.meituan.waimai.thrift.domain.WmPoiAggre;
import com.sankuai.meituan.waimai.thrift.service.WmPoiQueryThriftService;
import com.sankuai.meituan.waimai.thrift.util.DiffUtil;
import joptsimple.internal.Strings;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.concurrent.BasicThreadFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.lang.reflect.Field;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;

import static com.sankuai.meituan.waimai.customer.constant.ScFieldConstant.*;
import static com.sankuai.meituan.waimai.thrift.constants.WmPoiFieldQueryConstant.*;
import static com.sankuai.meituan.waimai.thrift.customer.constant.sc.ScConstants.*;

/**
 * @program: scm
 * @description: 学校食堂日志操作模块
 * @author: jianghuimin02
 * @create: 2020-05-18 19:25
 **/
@Slf4j
@Service
public class WmScLogService {

    @Autowired
    private WmScLogMapper wmScLogMapper;

    @Autowired
    private WmCanteenMapper wmCanteenMapper;

    @Autowired
    private WmPoiQueryThriftService.Iface wmPoiQueryThriftService;

    @Autowired
    private WmPoiOplogThriftService.Iface wmPoiOplogThriftService;

    @Autowired
    private WmScCanteenSensitiveWordsService wmScCanteenSensitiveWordsService;

    @Autowired
    private WmScCanteenPoiTaskDetailMapper wmScCanteenPoiTaskDetailMapper;

    @Autowired
    private WmScCanteenPoiTaskMapper wmScCanteenPoiTaskMapper;

    @Autowired
    private KeyDecryptHandleService keyDecryptHandleService;

    @Autowired
    private WmOpenCityServiceAdapter wmOpenCityServiceAdapter;

    @Autowired
    private WmPoiCategoryCacheServiceAdapter wmPoiCategoryCacheServiceAdapter;

    @Autowired
    private WmScLogSchoolInfoService wmScLogSchoolInfoService;


    private static int CPU_COUNT = Runtime.getRuntime().availableProcessors();

    private static final ScheduledExecutorService executorService = new ScheduledExecutorServiceTraceWrapper(new ScheduledThreadPoolExecutor(CPU_COUNT * 2 + 1,
            new BasicThreadFactory.Builder().namingPattern("sc-canteen-poi-log-%d").daemon(true).build()));

    public static final String nullShow = "空";

    /**
     * 日志插入处理的总控
     * moduleId物理主键的ID
     */
    public void handleLog(Byte opType, int moduleType, int moduleId, int userId, String userName, CanteenBo canteenBo, SchoolBo schoolBo) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_2", "com.sankuai.meituan.waimai.customer.service.sc.WmScLogService.handleLog(Byte,int,int,int,String,CanteenBo,SchoolBo)");
        if (opType == OptTypeEnum.INSERT.getType()) {
            if (moduleType == SC_SCHOOL_LOG) {
                String logInfo = composeSchoolInsertLog(schoolBo);
                insertScOptLog(opType, moduleType, moduleId, userId, userName, logInfo, "");
            }

            if (moduleType == SC_CANTEEN_LOG) {
                String logInfo = composeCanteenInsertLog(canteenBo);
                // 食堂新建操作日志
                insertScOptLog(opType, moduleType, moduleId, userId, userName, logInfo, "");
                WmCanteenDB wmCanteenDB = wmCanteenMapper.selectCanteenById(moduleId);
                wmScCanteenSensitiveWordsService.readWhenSelect(wmCanteenDB);
                // 学校绑定食堂操作日志
                String schooLog = "食堂:" + canteenBo.getCanteenName() + "(" +  wmCanteenDB.getCanteenId() + ")" + "绑定成功";
                insertScOptLog(OptTypeEnum.BIND.getType(), SC_SCHOOL_LOG, canteenBo.getSchoolId(), userId, userName, schooLog, "");
            }
        }
    }

    /**
     * 组装学校的插入日志
     * @param schoolBo schoolBo
     * @return 新建学校操作日志
     */
    public String composeSchoolInsertLog(SchoolBo schoolBo) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_1", "com.sankuai.meituan.waimai.customer.service.sc.WmScLogService.composeSchoolInsertLog(com.sankuai.meituan.waimai.thrift.customer.domain.sc.SchoolBo)");
        StringBuilder stringBuilder = new StringBuilder().append("操作：新建\\n");
        schoolField.forEach((key, value) -> {
            if (key.equals(SCHOOL_LIGHT_OFF_INFO)) {
                stringBuilder.append(composeSchoolLightOffInsertLog(schoolBo));
                return;
            }
            stringBuilder.append("[字段变更] ");
            stringBuilder.append(schoolField.get(key));
            stringBuilder.append(":空");
            stringBuilder.append("=> ");
            Object afterValue = transSaveSchoolToDesc(key, schoolBo);
            if (afterValue == null || "".equals(afterValue)) {
                stringBuilder.append("空");
            } else {
                stringBuilder.append(afterValue);
            }
            stringBuilder.append("\\n");
        });
        return stringBuilder.toString();
    }

    /**
     * 组装食堂的插入日志
     * @param canteenBo canteenBo
     */
    public String composeCanteenInsertLog(CanteenBo canteenBo) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_2", "com.sankuai.meituan.waimai.customer.service.sc.WmScLogService.composeCanteenInsertLog(com.sankuai.meituan.waimai.thrift.customer.domain.sc.CanteenBo)");
        StringBuilder stringBuilder = new StringBuilder().append("新建:\\n");
        canteenField.forEach((key, value) -> {
            stringBuilder.append("[字段变更]");
            stringBuilder.append(canteenField.get(key));
            stringBuilder.append(":空");
            stringBuilder.append("=>");
            Object afterValue = transSaveCanteenToDesc(key, canteenBo);
            if (afterValue == null || "".equals(afterValue)) {
                stringBuilder.append("空");
            } else {
                stringBuilder.append(afterValue);
            }
            stringBuilder.append("\\n");
        });
        return stringBuilder.toString();
    }

    /**
     * 组装学校的更新日志
     * @param schoolBo schoolBo
     * @param wmSchoolDB wmSchoolDB
     */
    public String composeSchoolUpdateLog(SchoolBo schoolBo, WmSchoolDB wmSchoolDB) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_2", "com.sankuai.meituan.waimai.customer.service.sc.WmScLogService.composeSchoolUpdateLog(SchoolBo,WmSchoolDB)");
        setSchoolBoDefaultValue(schoolBo);
        List<WmCustomerDiffCellBo> diffList = Lists.newArrayList();
        try {
            diffList = DiffUtil.compare(wmSchoolDB, schoolBo, schoolField);
        } catch (Exception e) {
            log.error("插入日志异常, schoolBo = {}, wmSchoolDB = {}", JSON.toJSONString(schoolBo), JSON.toJSONString(wmSchoolDB), e);
        }
        if (diffList.isEmpty()) {
            log.info("校园食堂项目:没有更新的字段:schoolBo:{}:wmSchoolDB:{}", JSON.toJSONString(schoolBo), JSON.toJSONString(wmSchoolDB));
            return "";
        }
        log.info("校园食堂临时日志:日志更新字段:schoolBo:{}:wmSchoolDB:{}", JSON.toJSONString(schoolBo), JSON.toJSONString(wmSchoolDB));
        StringBuilder stringBuilder = new StringBuilder().append("操作：修改\\n");
        for (WmCustomerDiffCellBo wmCustomerDiffCellBo : diffList) {
            // 学校熄灯信息, 单独处理日志
            if (wmCustomerDiffCellBo.getField().equals(SCHOOL_LIGHT_OFF_INFO)) {
                stringBuilder.append(composeSchoolLightOffUpdateLog(schoolBo, wmSchoolDB));
                continue;
            }
            stringBuilder.append("[字段变更] ");
            stringBuilder.append(schoolField.get(wmCustomerDiffCellBo.getField()));
            String preValue = wmCustomerDiffCellBo.getPre();
            if (StringUtils.isEmpty(preValue)) {
                preValue = nullShow;
            }
            preValue = transUpdateSchoolToDesc(wmCustomerDiffCellBo.getField(), preValue);
            stringBuilder.append(": ").append(preValue);
            stringBuilder.append("=>");
            String afterValue = transUpdateSchoolToDesc(wmCustomerDiffCellBo.getField(), wmCustomerDiffCellBo.getAft());
            if (StringUtils.isEmpty(afterValue)) {
                afterValue = nullShow;
            }
            stringBuilder.append(afterValue);
            stringBuilder.append("\\n");
        }
        return stringBuilder.toString();
    }

    /**
     * 获取学校熄灯时间信息更新日志
     * @param schoolBo schoolBo
     * @return 熄灯时间信息更新日志
     */
    public String composeSchoolLightOffUpdateLog(SchoolBo schoolBo, WmSchoolDB wmSchoolDB) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.service.sc.WmScLogService.composeSchoolLightOffUpdateLog(SchoolBo,WmSchoolDB)");
        if (StringUtils.isBlank(schoolBo.getSchoolLightOffInfo()) && StringUtils.isBlank(wmSchoolDB.getSchoolLightOffInfo())) {
            return Strings.EMPTY;
        }

        WmScSchoolLightOffBO wmScSchoolLightOffBOBefore = StringUtils.isBlank(wmSchoolDB.getSchoolLightOffInfo()) ?
                new WmScSchoolLightOffBO() : JSONObject.parseObject(wmSchoolDB.getSchoolLightOffInfo(), WmScSchoolLightOffBO.class);
        WmScSchoolLightOffBO wmScSchoolLightOffBOAfter = StringUtils.isBlank(schoolBo.getSchoolLightOffInfo()) ?
                new WmScSchoolLightOffBO() : JSONObject.parseObject(schoolBo.getSchoolLightOffInfo(), WmScSchoolLightOffBO.class);
        List<WmCustomerDiffCellBo> diffList = Lists.newArrayList();
        try {
            diffList = DiffUtil.compare(wmScSchoolLightOffBOBefore, wmScSchoolLightOffBOAfter, schoolLightOffField);
        } catch (Exception e) {
            log.error("[WmScLogService.composeSchoolLightOffUpdateLog] Exception. wmScSchoolLightOffBOBefore = {}, wmScSchoolLightOffBOAfter = {}",
                    JSON.toJSONString(wmScSchoolLightOffBOBefore), JSON.toJSONString(wmScSchoolLightOffBOAfter), e);
        }

        if (CollectionUtils.isEmpty(diffList)) {
            log.info("[WmScLogService.composeSchoolLightOffUpdateLog] diffList is empty, return.");
            return Strings.EMPTY;
        }

        StringBuilder stringBuilder = new StringBuilder();
        for (WmCustomerDiffCellBo wmCustomerDiffCellBo : diffList) {
            stringBuilder.append("[字段变更] ");
            stringBuilder.append(schoolLightOffField.get(wmCustomerDiffCellBo.getField()));
            String preValue = wmCustomerDiffCellBo.getPre();
            if (StringUtils.isEmpty(preValue)) {
                preValue = nullShow;
            }
            preValue = transUpdateSchoolLightOffInfoToDesc(wmCustomerDiffCellBo.getField(), preValue);
            stringBuilder.append(": ").append(preValue);
            stringBuilder.append("=>");
            String afterValue = transUpdateSchoolLightOffInfoToDesc(wmCustomerDiffCellBo.getField(), wmCustomerDiffCellBo.getAft());
            if (StringUtils.isEmpty(afterValue)) {
                afterValue = nullShow;
            }
            stringBuilder.append(afterValue);
            stringBuilder.append("\\n");
        }
        return stringBuilder.toString();
    }

    /**
     * 设置schoolBo默认值
     * @param schoolBo schoolBo
     */
    public void setSchoolBoDefaultValue(SchoolBo schoolBo) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.service.sc.WmScLogService.setSchoolBoDefaultValue(com.sankuai.meituan.waimai.thrift.customer.domain.sc.SchoolBo)");
        if (schoolBo.getAgreementTimeStart() == null) {
            schoolBo.setAgreementTimeStart(0);
        }

        if (schoolBo.getAgreementTimeEnd() == null) {
            schoolBo.setAgreementTimeEnd(0);
        }

        if (schoolBo.getOutDeliveryIn() == null) {
            schoolBo.setOutDeliveryIn(0);
        }
    }

    /**
     * 组装学校的更新日志
     * @param wmSchoolDbBefore wmSchoolDbBefore
     * @param wmSchoolDbAfter wmSchoolDbAfter
     */
    public String composeSchoolUpdateLog(WmSchoolDB wmSchoolDbBefore, WmSchoolDB wmSchoolDbAfter) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.service.sc.WmScLogService.composeSchoolUpdateLog(com.sankuai.meituan.waimai.customer.domain.sc.WmSchoolDB,com.sankuai.meituan.waimai.customer.domain.sc.WmSchoolDB)");
        List<WmCustomerDiffCellBo> diffList = Lists.newArrayList();
        try {
            diffList = DiffUtil.compare(wmSchoolDbBefore, wmSchoolDbAfter, schoolField);
        } catch (Exception e) {
            log.error("[WmScLogService.composeSchoolUpdateLog] Exception. wmSchoolDbBefore = {}, wmSchoolDbAfter = {}",
                    JSON.toJSONString(wmSchoolDbBefore), JSON.toJSONString(wmSchoolDbAfter), e);
        }

        if (diffList.isEmpty()) {
            log.info("[WmScLogService.composeSchoolUpdateLog] diffList is empty. wmSchoolDbBefore = {}, wmSchoolDbAfter = {}"
                    , JSON.toJSONString(wmSchoolDbBefore), JSON.toJSONString(wmSchoolDbAfter));
            return "";
        }

        log.info("[WmScLogService.composeSchoolUpdateLog] diffList = {}", JSON.toJSONString(diffList));
        StringBuilder stringBuilder = new StringBuilder().append("操作：修改\\n");
        for (WmCustomerDiffCellBo wmCustomerDiffCellBo : diffList) {
            stringBuilder.append("[字段变更] ");
            stringBuilder.append(schoolField.get(wmCustomerDiffCellBo.getField()));
            String preValue = wmCustomerDiffCellBo.getPre();
            if (StringUtils.isEmpty(preValue)) {
                preValue = nullShow;
            }
            preValue = transUpdateSchoolToDesc(wmCustomerDiffCellBo.getField(), preValue);
            stringBuilder.append(": ").append(preValue);
            stringBuilder.append("=>");
            String afterValue = transUpdateSchoolToDesc(wmCustomerDiffCellBo.getField(), wmCustomerDiffCellBo.getAft());
            if (StringUtils.isEmpty(afterValue)) {
                afterValue = nullShow;
            }
            stringBuilder.append(afterValue);
            stringBuilder.append("\\n");
        }
        return stringBuilder.toString();
    }

    /**
     * 组装食堂的更新日志
     */
    public String composeCanteenUpdateLog(CanteenBo canteenBo, WmCanteenDB wmCanteenDB, String userName, int userId) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.service.sc.WmScLogService.composeCanteenUpdateLog(CanteenBo,WmCanteenDB,String,int)");
        List<WmCustomerDiffCellBo> diffList = Lists.newArrayList();
        try {
            diffList = DiffUtil.compare(wmCanteenDB, canteenBo, canteenField);
        } catch (Exception e) {
            log.error("插入日志异常", e);
        }

        if (diffList.isEmpty()) {
            log.info("校园食堂项目:没有更新的字段:canteenBo:{}:wmCanteenDB:{}", JSON.toJSONString(canteenBo), JSON.toJSONString(wmCanteenDB));
            return "";
        }

        log.info("校园食堂临时日志:日志更新字段:canteenBo:{}:wmCanteenDB:{}", JSON.toJSONString(canteenBo), JSON.toJSONString(wmCanteenDB));
        canteenBo.setCanteenId(wmCanteenDB.getCanteenId());
        handleUpdateCanteenToSchool(diffList, canteenBo, userName, userId);
        StringBuilder stringBuilder = new StringBuilder().append("修改:\\n");
        for (WmCustomerDiffCellBo wmCustomerDiffCellBo : diffList) {
            stringBuilder.append("[字段变更]");
            stringBuilder.append(canteenField.get(wmCustomerDiffCellBo.getField()));
            String preValue = wmCustomerDiffCellBo.getPre();
            if (StringUtils.isEmpty(preValue)) {
                preValue = nullShow;
            }
            preValue = transUpdateCanteenToDesc(wmCustomerDiffCellBo.getField(), preValue);
            stringBuilder.append(":").append(preValue);
            stringBuilder.append("=>");
            String afterValue = wmCustomerDiffCellBo.getAft();
            if (StringUtils.isEmpty(afterValue)) {
                afterValue = nullShow;
            }
            afterValue = transUpdateCanteenToDesc(wmCustomerDiffCellBo.getField(), afterValue);
            stringBuilder.append(afterValue);
            stringBuilder.append("\\n");
        }

        stringBuilder.append(getCanteenStallChangeReasonLog(canteenBo));
        return stringBuilder.toString();
    }


    private String getCanteenStallChangeReasonLog(CanteenBo canteenBo) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.service.sc.WmScLogService.getCanteenStallChangeReasonLog(com.sankuai.meituan.waimai.thrift.customer.domain.sc.CanteenBo)");
        StringBuilder stringBuilder = new StringBuilder();
        CanteenStallNumChangeReasonEnum stallNumChangeReasonEnum = CanteenStallNumChangeReasonEnum.getByType(canteenBo.getStallNumChangeReason());
        if (stallNumChangeReasonEnum != null) {
            stringBuilder.append("[修改原因]修改档口数量原因:").append(stallNumChangeReasonEnum.getName()).append("\\n");
            stringBuilder.append("[修改原因]修改档口数量原因说明:").append(canteenBo.getStallNumChangeInfo()).append("\\n");
        }

        CanteenStallNumChangeReasonEnum offlineBizNumChangeReasonEnum = CanteenStallNumChangeReasonEnum.getByType(canteenBo.getOfflineBizStallNumChangeReason());
        if (offlineBizNumChangeReasonEnum != null) {
            stringBuilder.append("[修改原因]修改线下营业档口数量原因:").append(offlineBizNumChangeReasonEnum.getName()).append("\\n");
            stringBuilder.append("[修改原因]修改线下营业档口数量原因说明:").append(canteenBo.getOfflineBizStallNumChangeInfo()).append("\\n");
        }

        return stringBuilder.toString();
    }

    /**
     * 组装食堂的更新日志
     * @param wmCanteenDBBefore 更新前wmCanteenDB
     * @param wmCanteenDBAfter 更新后wmCanteenDB
     * @param userName 用户名称
     * @param userId 用户ID
     * @return 操作日志
     */
    public String composeCanteenUpdateLog(WmCanteenDB wmCanteenDBBefore, WmCanteenDB wmCanteenDBAfter, String userName, int userId) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_2", "com.sankuai.meituan.waimai.customer.service.sc.WmScLogService.composeCanteenUpdateLog(WmCanteenDB,WmCanteenDB,String,int)");
        List<WmCustomerDiffCellBo> diffList = Lists.newArrayList();
        try {
            diffList = DiffUtil.compare(wmCanteenDBBefore, wmCanteenDBAfter, canteenField);
        } catch (Exception e) {
            log.error("插入日志异常", e);
        }

        if (CollectionUtils.isEmpty(diffList)) {
            return Strings.EMPTY;
        }

        StringBuilder stringBuilder = new StringBuilder().append("修改:\\n");
        for (WmCustomerDiffCellBo wmCustomerDiffCellBo : diffList) {
            stringBuilder.append("[字段变更]");
            stringBuilder.append(canteenField.get(wmCustomerDiffCellBo.getField()));
            String preValue = wmCustomerDiffCellBo.getPre();
            if (StringUtils.isEmpty(preValue)) {
                preValue = nullShow;
            }
            preValue = transUpdateCanteenToDesc(wmCustomerDiffCellBo.getField(), preValue);
            stringBuilder.append(":").append(preValue);
            stringBuilder.append("=>");
            String afterValue = wmCustomerDiffCellBo.getAft();
            if (StringUtils.isEmpty(afterValue)) {
                afterValue = nullShow;
            }
            afterValue = transUpdateCanteenToDesc(wmCustomerDiffCellBo.getField(), afterValue);
            stringBuilder.append(afterValue);
            stringBuilder.append("\\n");
        }
        return stringBuilder.toString();
    }


    /**
     * 更新食堂时，是否更新了学校
     * todo 插入的时候要绑定成功
     */
    public void handleUpdateCanteenToSchool(List<WmCustomerDiffCellBo> diffList, CanteenBo bo, String userName, int userId) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.sc.WmScLogService.handleUpdateCanteenToSchool(java.util.List,com.sankuai.meituan.waimai.thrift.customer.domain.sc.CanteenBo,java.lang.String,int)");
        for (WmCustomerDiffCellBo wmCustomerDiffCellBo : diffList) {
            if (CANTEEN_FIELD_SCHOOLID.equals(wmCustomerDiffCellBo.getField())) {
                String canteenName = bo.getCanteenName();
                int canteenId = bo.getCanteenId();
                String beforeLog = canteenName + canteenId + "解绑成功";
                int beforeSchoolId = Integer.parseInt(wmCustomerDiffCellBo.getPre());
                int opTypeUnbind = OptTypeEnum.UNBAND.getType();
                insertScOptLog((byte) opTypeUnbind, SC_SCHOOL_LOG, beforeSchoolId, userId, userName, beforeLog, "");


                String afterLog = canteenName + canteenId + "绑定成功";
                int afterSchoolId = Integer.parseInt(wmCustomerDiffCellBo.getAft());
                int opTypeBind = OptTypeEnum.BIND.getType();
                insertScOptLog((byte) opTypeBind, SC_SCHOOL_LOG, afterSchoolId, userId, userName, afterLog, "");
            }
        }
    }

    /**
     * 插入操作日志
     * @param optType 操作类型。com.sankuai.meituan.waimai.thrift.customer.constant.sc.OptTypeEnum
     * @param moduleType 模块类型。学校1；食堂:2
     * @param moduleId 学校或着食堂的ID
     * @param userId 用户ID
     * @param userName 用户名称
     * @param logInfo 日志内容
     * @param remark 备注
     */
    public void insertScOptLog(Byte optType, int moduleType, int moduleId, Integer userId, String userName, String logInfo, String remark) {
        WmScLogDB wmScLogDB = new WmScLogDB();
        wmScLogDB.setCtime((int) System.currentTimeMillis() / 1000);
        wmScLogDB.setUtime((int) System.currentTimeMillis() / 1000);
        wmScLogDB.setLog(logInfo);
        wmScLogDB.setRemark(remark);
        wmScLogDB.setModuleId(moduleId);
        wmScLogDB.setModuleType((short) moduleType);
        wmScLogDB.setOpType(optType);
        wmScLogDB.setOpUid(userId);
        wmScLogDB.setOpUname(userName);
        int result = wmScLogMapper.insertScLog(wmScLogDB);
        log.info("校园食堂项目:插入日志插入参数:result:{}:wmScLogDB:{}", result, JSON.toJSONString(wmScLogDB));


        //执行更新食堂信息
        ArrayList<WmScLogDB> wmScLogDBArrayList = new ArrayList<>();
        wmScLogDBArrayList.add(wmScLogDB);
        int res = wmScLogSchoolInfoService.changeCanteenUTime(wmScLogDBArrayList);
        log.info("校园食堂项目:更新食堂信息: result:{}", res);
    }


    /**
     * 批量插入操作日志
     * @param optType 操作类型。com.sankuai.meituan.waimai.thrift.customer.constant.sc.OptTypeEnum
     * @param userId 用户ID
     * @param userName 用户名称
     * @param logInfoMap 日志Map
     * @param remark 备注
     */
    public void batchInsertSchoolOptLog(Byte optType, Integer userId, String userName, Map<Integer, String> logInfoMap, String remark) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.service.sc.WmScLogService.batchInsertSchoolOptLog(java.lang.Byte,java.lang.Integer,java.lang.String,java.util.Map,java.lang.String)");
        List<WmScLogDB> wmScLogDBList = new ArrayList<>();
        for (Map.Entry<Integer, String> entry : logInfoMap.entrySet()) {
            WmScLogDB wmScLogDB = new WmScLogDB();
            wmScLogDB.setLog(entry.getValue());
            wmScLogDB.setRemark(remark);
            wmScLogDB.setModuleId(entry.getKey());
            wmScLogDB.setModuleType((short) SC_SCHOOL_LOG);
            wmScLogDB.setOpType(optType);
            wmScLogDB.setOpUid(userId);
            wmScLogDB.setOpUname(userName);
            wmScLogDBList.add(wmScLogDB);
        }
        // 分批次批量插入
        List<List<WmScLogDB>> wmScLogDBListPartition = Lists.partition(wmScLogDBList, MccScConfig.getMaxListSizeOfBatchInsertScOpLog());
        for (List<WmScLogDB> list : wmScLogDBListPartition) {
            int result = wmScLogMapper.batchInsertScLog(list);
            log.info("[WmScLogService.batchInsertScOptLog] insert oplog success. list = {}, result = {}.", JSONObject.toJSONString(list), result);

            //执行更新食堂信息
            int res = wmScLogSchoolInfoService.changeCanteenUTime(list);
            log.info("校园食堂项目:更新食堂信息: result:{}", res);
        }
    }

    /**
     * 根据属性名获取属性值
     *
     * @param fieldName
     * @param object
     * @return
     */
    private Object getFieldValueByFieldName(String fieldName, Object object) {
        try {
            Field field = object.getClass().getDeclaredField(fieldName);
            field.setAccessible(true);
            return field.get(object);
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 组装学校熄灯信息新增日志
     * @param schoolBo schoolBo
     * @return 学校熄灯信息新增日志
     */
    public String composeSchoolLightOffInsertLog(SchoolBo schoolBo) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.service.sc.WmScLogService.composeSchoolLightOffInsertLog(com.sankuai.meituan.waimai.thrift.customer.domain.sc.SchoolBo)");
        if (StringUtils.isBlank(schoolBo.getSchoolLightOffInfo())) {
            return Strings.EMPTY;
        }
        StringBuilder stringBuilder = new StringBuilder();
        WmScSchoolLightOffBO wmScSchoolLightOffBO = JSONObject.parseObject(schoolBo.getSchoolLightOffInfo(), WmScSchoolLightOffBO.class);
        schoolLightOffField.forEach((key, value) -> {
            stringBuilder.append("[字段变更] ");
            stringBuilder.append(schoolLightOffField.get(key));
            stringBuilder.append(":空");
            stringBuilder.append("=> ");
            Object afterValue = transSaveSchoolLightOffToDesc(key, wmScSchoolLightOffBO);
            if (afterValue == null || "".equals(afterValue)) {
                stringBuilder.append("空");
            } else {
                stringBuilder.append(afterValue);
            }
            stringBuilder.append("\\n");
        });
        return stringBuilder.toString();
    }

    public Object transSaveSchoolLightOffToDesc(String key, WmScSchoolLightOffBO wmScSchoolLightOffBO) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.service.sc.WmScLogService.transSaveSchoolLightOffToDesc(java.lang.String,com.sankuai.meituan.waimai.customer.domain.sc.WmScSchoolLightOffBO)");
        Object trueValue = getFieldValueByFieldName(key, wmScSchoolLightOffBO);
        if (trueValue != null) {
            // 学校熄灯要求
            if (SCHOOL_LIGHT_OFF_REQUIREMENT.equals(key)) {
                SchoolLightOffRequirementEnum schoolLightOffRequirementEnum = SchoolLightOffRequirementEnum.getByType((int) trueValue);
                return schoolLightOffRequirementEnum == null ? nullShow : schoolLightOffRequirementEnum.getName();
            }
        }
        return trueValue;
    }

    public Object transSaveSchoolToDesc(String key, SchoolBo schoolBo) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_1", "com.sankuai.meituan.waimai.customer.service.sc.WmScLogService.transSaveSchoolToDesc(java.lang.String,com.sankuai.meituan.waimai.thrift.customer.domain.sc.SchoolBo)");
        Object trueValue = getFieldValueByFieldName(key, schoolBo);
        if (trueValue != null) {
            // 业务类型
            if (SCHOOL_FIELD_AORTYPE.equals(key)) {
                SchoolAorTypeEnum schoolAorTypeEnum = SchoolAorTypeEnum.getByType((int) trueValue);
                return schoolAorTypeEnum == null ? nullShow : schoolAorTypeEnum.getName();
            }

            // 学校类型
            if (SCHOOL_FIELD_SCHOOLTYPE.equals(key)) {
                SchoolTypeEnum schoolTypeEnum = SchoolTypeEnum.getByType((int) trueValue);
                return schoolTypeEnum == null ? nullShow : schoolTypeEnum.getName();
            }
            // 学校供给分级
            if (SCHOOL_FIELD_GRADE.equals(key)) {
                SchoolGradeEnum schoolGradeEnum = SchoolGradeEnum.getByType((int) trueValue);
                return schoolGradeEnum == null ? nullShow : schoolGradeEnum.getName();
            }
            // 合作状态
            if (SCHOOL_FIELD_WM_CO_STATUS.equals(key)) {
                return WmCoStatusEnum.getDescByCode((int) trueValue);
            }
            // 学校合作方式
            if (SCHOOL_COOPERATE_TYPE.equals(key)) {
                SchoolCooperateTypeEnum schoolCooperateTypeEnum = SchoolCooperateTypeEnum.getByType((int) trueValue);
                return schoolCooperateTypeEnum == null ? nullShow : schoolCooperateTypeEnum.getName();
            }
            // 学校生命周期
            if (SCHOOL_LIFECYCLE.equals(key)) {
                SchoolLifecycleEnum schoolLifecycleEnum = SchoolLifecycleEnum.getByType((int) trueValue);
                return schoolLifecycleEnum == null ? nullShow : schoolLifecycleEnum.getName();
            }
            // 学校分级
            if (SCHOOL_LEVEL.equals(key)) {
                SchoolLevelEnum schoolLevelEnum = SchoolLevelEnum.getByType((int) trueValue);
                return schoolLevelEnum == null ? nullShow : schoolLevelEnum.getName();
            }
            // 学校开发方式
            if (SCHOOL_DEV_TYPE.equals(key)) {
                SchoolDevTypeEnum schoolDevTypeEnum = SchoolDevTypeEnum.getByType((int) trueValue);
                return schoolDevTypeEnum == null ? nullShow : schoolDevTypeEnum.getName();
            }
            // 蜂窝内校外学生人数
            if (SCHOOL_OUTSIDE_STUDENT_NUM.equals(key) && "-1".equals(trueValue.toString())) {
                return nullShow;
            }
            // 在校学生人数
            if (SCHOOL_STUDENT_NUM.equals(key) && "-1".equals(trueValue.toString())) {
                return nullShow;
            }
            // 合同/授权编号类型
            if (SCHOOL_AGREEMENT_TYPE.equals(key)) {
                SchoolAgreementTypeEnum schoolAgreementTypeEnum = SchoolAgreementTypeEnum.getByType((int) trueValue);
                return schoolAgreementTypeEnum == null ? nullShow : schoolAgreementTypeEnum.getName();
            }
            return transSaveSchoolToDescExtension(key, trueValue);
        }
        return trueValue;
    }

    public Object transSaveSchoolToDescExtension(String key, Object trueValue) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.sc.WmScLogService.transSaveSchoolToDescExtension(java.lang.String,java.lang.Object)");
        // 合同/授权编号开始时间
        if (SCHOOL_AGREEMENT_TIME_START.equals(key) && "0".equals(trueValue.toString())) {
            return nullShow;
        }
        // 合同/授权编号结束时间
        if (SCHOOL_AGREEMENT_TIME_END.equals(key) && "0".equals(trueValue.toString())) {
            return nullShow;
        }
        // 是否有取餐柜
        if (SCHOOL_DINING_CABINET.equals(key)) {
            SchoolDiningCabinetEnum schoolDiningCabinetEnum = SchoolDiningCabinetEnum.getByType((int) trueValue);
            return schoolDiningCabinetEnum == null ? nullShow : schoolDiningCabinetEnum.getName();
        }
        // 聚合订单是否允许配送进校
        if (SCHOOL_AGGRE_ORDER_ALLOW_DELIVERY.equals(key)) {
            SchoolAggreOrderAllowDeliveryEnum schoolAggreOrderAllowDeliveryEnum = SchoolAggreOrderAllowDeliveryEnum.getByType((int) trueValue);
            return schoolAggreOrderAllowDeliveryEnum == null ? nullShow : schoolAggreOrderAllowDeliveryEnum.getName();
        }
        return trueValue;
    }


    public Object transSaveCanteenToDesc(String key, CanteenBo canteenBo) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.service.sc.WmScLogService.transSaveCanteenToDesc(java.lang.String,com.sankuai.meituan.waimai.thrift.customer.domain.sc.CanteenBo)");
        Object trueValue = getFieldValueByFieldName(key, canteenBo);
        if (trueValue != null) {
            // 食堂类型
            if (CANTEEN_FIELD_CANTEENTYPE.equals(key)) {
                CanteenTypeEnum canteenTypeEnum = CanteenTypeEnum.getByType((int) trueValue);
                return canteenTypeEnum == null ? nullShow : canteenTypeEnum.getName();
            }
            // 食堂属性
            if (CANTEEN_FIELD_CANTEENTATRI.equals(key)) {
                CanteenAttributeEnum canteenAttributeEnum = CanteenAttributeEnum.getByType((int) trueValue);
                return canteenAttributeEnum == null ? nullShow : canteenAttributeEnum.getName();
            }
            // 食堂供给分级
            if (CANTEEN_FIELD_GRADE.equals(key)) {
                if ((int) trueValue == 0) {
                    trueValue = 99;
                }
                CanteenGradeEnum canteenGradeEnum = CanteenGradeEnum.getByType((int) trueValue);
                return canteenGradeEnum == null ? nullShow : canteenGradeEnum.getName();
            }
            // 合作状态
            if (CANTEEN_CANTEEN_STATUS.equals(key)) {
                CanteenStatusEnum canteenStatusEnum = CanteenStatusEnum.getByType((int) trueValue);
                return canteenStatusEnum == null ? nullShow : canteenStatusEnum.getName();
            }
            // 线下营业档口数量
            if (CANTEEN_FIELD_OFFLINE_BIZ_STALL_NUM.equals(key) && "-1".equals(trueValue.toString())) {
                return nullShow;
            }
            // 可上线档口数量
            if (CANTEEN_FIELD_PRE_ONLINE_STALL_NUM.equals(key) && "-1".equals(trueValue.toString())) {
                return nullShow;
            }
        }
        return trueValue;
    }


    public String transUpdateCanteenToDesc(String key, String value) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_1", "com.sankuai.meituan.waimai.customer.service.sc.WmScLogService.transUpdateCanteenToDesc(java.lang.String,java.lang.String)");
        if (!nullShow.equals(value) && StringUtils.isNotEmpty(value)) {
            // 食堂类型
            if (CANTEEN_FIELD_CANTEENTYPE.equals(key)) {
                CanteenTypeEnum canteenTypeEnum = CanteenTypeEnum.getByType(Integer.parseInt(value));
                return canteenTypeEnum == null ? nullShow : canteenTypeEnum.getName();
            }
            // 食堂属性
            if (CANTEEN_FIELD_CANTEENTATRI.equals(key)) {
                CanteenAttributeEnum canteenAttributeEnum = CanteenAttributeEnum.getByType(Integer.parseInt(value));
                return canteenAttributeEnum == null ? nullShow : canteenAttributeEnum.getName();
            }
            // 食堂供给分级
            if (CANTEEN_FIELD_GRADE.equals(key)) {
                CanteenGradeEnum canteenGradeEnum = CanteenGradeEnum.getByType(Integer.parseInt(value));
                return canteenGradeEnum == null ? nullShow : canteenGradeEnum.getName();
            }
            // 合作状态
            if (CANTEEN_CANTEEN_STATUS.equals(key)) {
                CanteenStatusEnum canteenStatusEnum = CanteenStatusEnum.getByType(Integer.parseInt(value));
                return canteenStatusEnum == null ? nullShow : canteenStatusEnum.getName();
            }
            // 线下营业档口数量
            if (CANTEEN_FIELD_OFFLINE_BIZ_STALL_NUM.equals(key) && "-1".equals(value)) {
                return nullShow;
            }
            // 可上线档口数量
            if (CANTEEN_FIELD_PRE_ONLINE_STALL_NUM.equals(key) && "-1".equals(value)) {
                return nullShow;
            }
        }
        return value;
    }


    /**
     * 食堂删除门店日志
     *
     * @param deletePoiAggreList 删除门店列表
     * @return string
     */
    public String composeCanteenPoiBind(List<WmPoiAggre> deletePoiAggreList, String enter) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_1", "com.sankuai.meituan.waimai.customer.service.sc.WmScLogService.composeCanteenPoiBind(java.util.List,java.lang.String)");
        StringBuilder stringBuilder = new StringBuilder();
        for (WmPoiAggre wmPoiAggre : deletePoiAggreList) {
            stringBuilder.append(wmPoiAggre.getName()).append(" ").append(wmPoiAggre.getWm_poi_id()).append(".").append(enter);
        }
        return stringBuilder.toString();
    }

    /**
     * 将学校熄灯时间操作日志中的枚举值转换为描述
     * @param key key
     * @param value value
     * @return 枚举值描述
     */
    public String transUpdateSchoolLightOffInfoToDesc(String key, String value) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.sc.WmScLogService.transUpdateSchoolLightOffInfoToDesc(java.lang.String,java.lang.String)");
        if (!nullShow.equals(value) && StringUtils.isNotEmpty(value)) {
            // 学校熄灯要求
            if (SCHOOL_LIGHT_OFF_REQUIREMENT.equals(key)) {
                SchoolLightOffRequirementEnum schoolLightOffRequirementEnum = SchoolLightOffRequirementEnum.getByType(Integer.parseInt(value));
                return schoolLightOffRequirementEnum == null ? nullShow : schoolLightOffRequirementEnum.getName();
            }
        }
        return value;
    }
    /**
     * 将学校操作日志中的枚举值转换为描述
     * @param key key
     * @param value value
     * @return 枚举值描述
     */
    public String transUpdateSchoolToDesc(String key, String value) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_1", "com.sankuai.meituan.waimai.customer.service.sc.WmScLogService.transUpdateSchoolToDesc(java.lang.String,java.lang.String)");
        if (!nullShow.equals(value) && StringUtils.isNotEmpty(value)) {
            // 学校类型
            if (SCHOOL_FIELD_SCHOOLTYPE.equals(key)) {
                SchoolTypeEnum schoolTypeEnum = SchoolTypeEnum.getByType(Integer.parseInt(value));
                return schoolTypeEnum == null ? nullShow : schoolTypeEnum.getName();
            }
            // 学校供给分级
            if (SCHOOL_FIELD_GRADE.equals(key)) {
                SchoolGradeEnum schoolGradeEnum = SchoolGradeEnum.getByType(Integer.parseInt(value));
                return schoolGradeEnum == null ? nullShow : schoolGradeEnum.getName();
            }
            // 合作状态
            if (SCHOOL_FIELD_WM_CO_STATUS.equals(key)) {
                return WmCoStatusEnum.getDescByCode(Integer.parseInt(value));
            }
            // 学校合作方式
            if (SCHOOL_COOPERATE_TYPE.equals(key)) {
                SchoolCooperateTypeEnum schoolCooperateTypeEnum = SchoolCooperateTypeEnum.getByType(Integer.parseInt(value));
                return schoolCooperateTypeEnum == null ? nullShow : schoolCooperateTypeEnum.getName();
            }
            // 学校生命周期
            if (SCHOOL_LIFECYCLE.equals(key)) {
                SchoolLifecycleEnum schoolLifecycleEnum = SchoolLifecycleEnum.getByType(Integer.parseInt(value));
                return schoolLifecycleEnum == null ? nullShow : schoolLifecycleEnum.getName();
            }
            // 学校分级
            if (SCHOOL_LEVEL.equals(key)) {
                SchoolLevelEnum schoolLevelEnum = SchoolLevelEnum.getByType(Integer.parseInt(value));
                return schoolLevelEnum == null ? nullShow : schoolLevelEnum.getName();
            }
            // 学校开发方式
            if (SCHOOL_DEV_TYPE.equals(key)) {
                SchoolDevTypeEnum schoolDevTypeEnum = SchoolDevTypeEnum.getByType(Integer.parseInt(value));
                return schoolDevTypeEnum == null ? nullShow : schoolDevTypeEnum.getName();
            }
            // 蜂窝内校外学生人数
            if (SCHOOL_OUTSIDE_STUDENT_NUM.equals(key) && "-1".equals(value)) {
                return nullShow;
            }
            // 在校学生人数
            if (SCHOOL_STUDENT_NUM.equals(key) && "-1".equals(value)) {
                return nullShow;
            }
            // 合同/授权编号类型
            if (SCHOOL_AGREEMENT_TYPE.equals(key)) {
                SchoolAgreementTypeEnum schoolAgreementTypeEnum = SchoolAgreementTypeEnum.getByType(Integer.parseInt(value));
                return schoolAgreementTypeEnum == null ? nullShow : schoolAgreementTypeEnum.getName();
            }
            // 聚合订单是否允许配送进校
            if (SCHOOL_AGGRE_ORDER_ALLOW_DELIVERY.equals(key)) {
                SchoolAggreOrderAllowDeliveryEnum schoolAggreOrderAllowDeliveryEnum = SchoolAggreOrderAllowDeliveryEnum.getByType(Integer.parseInt(value));
                return schoolAggreOrderAllowDeliveryEnum == null ? nullShow : schoolAggreOrderAllowDeliveryEnum.getName();
            }
            // 蜂窝类型
            if (SCHOOL_FIELD_AORTYPE.equals(key)) {
                SchoolAorTypeEnum schoolAorTypeEnum = SchoolAorTypeEnum.getByType(Integer.parseInt(value));
                return schoolAorTypeEnum == null ? nullShow : schoolAorTypeEnum.getName();
            }

            return transUpdateSchoolToDescExtension(key, value);
        }
        return value;
    }

    public String transUpdateSchoolToDescExtension(String key, String value) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.service.sc.WmScLogService.transUpdateSchoolToDescExtension(java.lang.String,java.lang.String)");
        // 合同/授权编号开始时间
        if (SCHOOL_AGREEMENT_TIME_START.equals(key) && "0".equals(value)) {
            return nullShow;
        }
        // 合同/授权编号结束时间
        if (SCHOOL_AGREEMENT_TIME_END.equals(key) && "0".equals(value)) {
            return nullShow;
        }
        // 是否有取餐柜
        if (SCHOOL_DINING_CABINET.equals(key)) {
            SchoolDiningCabinetEnum schoolDiningCabinetEnum = SchoolDiningCabinetEnum.getByType(Integer.parseInt(value));
            return schoolDiningCabinetEnum == null ? nullShow : schoolDiningCabinetEnum.getName();
        }
        // 聚合订单是否允许配送进校
        if (SCHOOL_AGGRE_ORDER_ALLOW_DELIVERY.equals(key)) {
            SchoolAggreOrderAllowDeliveryEnum schoolAggreOrderAllowDeliveryEnum = SchoolAggreOrderAllowDeliveryEnum.getByType(Integer.parseInt(value));
            return schoolAggreOrderAllowDeliveryEnum == null ? nullShow : schoolAggreOrderAllowDeliveryEnum.getName();
        }
        return value;
    }

    /**
     * 食堂取消合作并强制解绑门店-操作日志
     * @param deletePoiIdList 强制解绑的门店ID列表
     * @param canteedId 食堂ID
     * @param opUid 用户ID
     * @param opUname 用户名
     */
    public void saveCancelCoPoiLog(List<Long> deletePoiIdList,
                                   Integer canteedId,
                                   Integer opUid,
                                   String opUname) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.sc.WmScLogService.saveCancelCoPoiLog(java.util.List,java.lang.Integer,java.lang.Integer,java.lang.String)");
        executorService.submit(() -> {
            try {
                List<WmPoiAggre> deletePoiList = wmPoiQueryThriftService.mgetWmPoiAggreByWmPoiIdWithSpecificField(
                        deletePoiIdList,
                        Sets.newHashSet(
                                WM_POI_FIELD_WM_POI_ID,
                                WM_POI_FIELD_NAME,
                                WM_POI_FIELD_IS_DELETE
                        ));
                // 保存日志
                String canteenLogInfo = WmCanteenPoiAuditFlowEnum.CANCEL_WM_CO_STATUS.getPre()
                        + WmCanteenLogPreConstant.CANTEEN_ENTER
                        + composeCanteenPoiBind(deletePoiList, WmCanteenLogPreConstant.CANTEEN_ENTER);
                insertScOptLog(OptTypeEnum.CANCEL_WM_CO_STATUS.getType(), SC_CANTEEN_LOG, canteedId, opUid, opUname, canteenLogInfo, "");
                // 门店系统中记录操作日志
                for (WmPoiAggre aggre : deletePoiList) {
                    String poiInfo = WmCanteenLogPreConstant.POI_LOG_PRE + WmCanteenLogPreConstant.POI_ENTER + WmCanteenPoiAuditFlowEnum.CANCEL_WM_CO_STATUS.getPre();
                    WmPoiOplog oplog = new WmPoiOplog().setWm_poi_id(aggre.getWm_poi_id())
                            .setOp_type(WmPoiOpLog.OpType.POI_CANTEEN_REL.getId())
                            .setOp_uid(opUid).setOp_uname(opUname)
                            .setCtime(TimeUtil.unixtime())
                            .setDiff(poiInfo);
                    wmPoiOplogThriftService.insertSendMQByAsync(oplog, null);
                }
            } catch (Exception e) {
                log.error("[食堂管理]保存日志异常", e);
            }
        });
    }

    /**
     * 强制解绑保存V2日志
     */
    public void saveForceUnbindPoiV2Log(Long poiId,
                                        Integer canteenId,
                                        Integer opUid,
                                        String opUname) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_2", "com.sankuai.meituan.waimai.customer.service.sc.WmScLogService.saveForceUnbindPoiV2Log(java.lang.Long,java.lang.Integer,java.lang.Integer,java.lang.String)");
        executorService.submit(() -> {
            try {
                WmPoiAggre aggre = wmPoiQueryThriftService.getWmPoiAggreByWmPoiIdWithSpecificField(poiId, Sets.newHashSet(WM_POI_FIELD_WM_POI_ID, WM_POI_FIELD_NAME));
                if (aggre == null) {
                    return;
                }
                String info = WmCanteenPoiAuditFlowV2Enum.Unbind.getPre() + WmCanteenLogPreConstant.CANTEEN_ENTER + aggre.getName() + " " + aggre.getWm_poi_id();
                insertScOptLog(OptTypeEnum.POI_BING.getType(), SC_CANTEEN_LOG, canteenId, opUid, opUname, info, "");

                String poiInfo = WmCanteenLogPreConstant.POI_LOG_PRE + WmCanteenLogPreConstant.POI_ENTER + WmCanteenPoiAuditFlowV2Enum.Unbind.getPre();
                WmPoiOplog oplog = new WmPoiOplog().setWm_poi_id(poiId)
                        .setOp_type(WmPoiOpLog.OpType.POI_CANTEEN_REL.getId())
                        .setOp_uid(opUid).setOp_uname(opUname)
                        .setCtime(TimeUtil.unixtime())
                        .setDiff(poiInfo);
                wmPoiOplogThriftService.insertSendMQByAsync(oplog, null);
            } catch (Exception e) {
                log.error("[食堂管理]强制解绑保存日志异常", e);
            }
        });
    }

    /**
     * 保存食堂添加日志V2: 从新表中获取数据
     * @param wmScCanteenPoiSaveLogBO wmScCanteenPoiSaveLogBO
     */
    public void saveCanteenPoiLogV2(WmScCanteenPoiSaveLogBO wmScCanteenPoiSaveLogBO) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.service.sc.WmScLogService.saveCanteenPoiLogV2(com.sankuai.meituan.waimai.customer.domain.sc.WmScCanteenPoiSaveLogBO)");
        log.info("[WmScLogService.saveCanteenPoiLogV2] input param: wmScCanteenPoiSaveLogBO = {}", JSONObject.toJSONString(wmScCanteenPoiSaveLogBO));
        if (wmScCanteenPoiSaveLogBO == null) {
            log.error("[WmScLogService.saveCanteenPoiLogV2] wmScCanteenPoiSaveLogBO is null");
            return;
        }

        executorService.submit(() -> {
            try {
                List<WmScCanteenPoiTaskDetailDO> taskDetailDOS = wmScCanteenPoiTaskDetailMapper.selectByCanteenPoiTaskId(wmScCanteenPoiSaveLogBO.getCanteenPoiTaskId());
                List<Long> poiIdList = taskDetailDOS.stream().map(WmScCanteenPoiTaskDetailDO::getWmPoiId).collect(Collectors.toList());
                List<WmPoiAggre> poiAggreList = wmPoiQueryThriftService.mgetWmPoiAggreByWmPoiIdWithSpecificField(
                        poiIdList,
                        Sets.newHashSet(WM_POI_FIELD_WM_POI_ID, WM_POI_FIELD_NAME)
                );
                // 保存日志
                WmScCanteenPoiLogComposeBO wmScCanteenPoiLogComposeBO1 = new WmScCanteenPoiLogComposeBO.Builder()
                        .effectPoiAggreList(poiAggreList)
                        .appendPoiInfo(true)
                        .enter(WmCanteenLogPreConstant.CANTEEN_ENTER)
                        .preAuditStatus(wmScCanteenPoiSaveLogBO.getPreAuditStatus())
                        .aftAuditStatus(wmScCanteenPoiSaveLogBO.getAftAuditStatus())
                        .values(wmScCanteenPoiSaveLogBO.getValuesMap())
                        .build();

                String canteenLogInfo = WmCanteenLogPreConstant.CANTEEN_LOG_PRE
                        + WmCanteenLogPreConstant.CANTEEN_ENTER
                        + composeCanteenPoiBindV2(wmScCanteenPoiLogComposeBO1);

                // 只有发起门店新增任务时 才会记录换绑信息
                if (wmScCanteenPoiSaveLogBO.getAftAuditStatus() == CanteenPoiAuditStatusV2Enum.FIRST_AUDITING
                        && wmScCanteenPoiSaveLogBO.getCanteenPoiTaskTypeEnum() == CanteenPoiTaskTypeEnum.TRANSFER_BIND) {
                    canteenLogInfo = canteenLogInfo
                            + WmCanteenLogPreConstant.CANTEEN_ENTER
                            + composeCanteenPoiChangeBind(wmScCanteenPoiSaveLogBO.getCanteenPoiTaskId(), wmScCanteenPoiSaveLogBO.getCanteenIdTo(), wmScCanteenPoiSaveLogBO.getCanteenIdFrom());
                }
                insertScOptLog(OptTypeEnum.POI_BING.getType(), SC_CANTEEN_LOG, wmScCanteenPoiSaveLogBO.getCanteenIdTo(), wmScCanteenPoiSaveLogBO.getOpUid(), wmScCanteenPoiSaveLogBO.getOpUname(), canteenLogInfo, "");

                // 异步发送消息
                WmScCanteenPoiLogComposeBO wmScCanteenPoiLogComposeBO = new WmScCanteenPoiLogComposeBO.Builder()
                        .effectPoiAggreList(poiAggreList)
                        .appendPoiInfo(false)
                        .enter(WmCanteenLogPreConstant.POI_ENTER)
                        .preAuditStatus(wmScCanteenPoiSaveLogBO.getPreAuditStatus())
                        .aftAuditStatus(wmScCanteenPoiSaveLogBO.getAftAuditStatus())
                        .values(wmScCanteenPoiSaveLogBO.getValuesMap())
                        .build();

                String poiLogInfo = WmCanteenLogPreConstant.POI_LOG_PRE
                        + WmCanteenLogPreConstant.POI_ENTER
                        + composeCanteenPoiBindV2(wmScCanteenPoiLogComposeBO)
                        + WmCanteenLogPreConstant.POI_ENTER;

                for (Long wmPoiId : poiIdList) {
                    WmPoiOplog oplog = new WmPoiOplog()
                            .setWm_poi_id(wmPoiId)
                            .setOp_type(WmPoiOpLog.OpType.POI_CANTEEN_REL.getId())
                            .setOp_uid(wmScCanteenPoiSaveLogBO.getOpUid())
                            .setOp_uname(wmScCanteenPoiSaveLogBO.getOpUname())
                            .setCtime(TimeUtil.unixtime()).setDiff(poiLogInfo);
                    wmPoiOplogThriftService.insertSendMQByAsync(oplog, null);
                }
            } catch (Exception e) {
                log.error("[WmScLogService.saveCanteenPoiLogV2] Exception. wmScCanteenPoiSaveLogBO = {}", JSONObject.toJSONString(wmScCanteenPoiSaveLogBO), e);
            }
        });
    }

    /**
     * 食堂绑定门店日志V2 ：新增加一级节点
     * @param wmScCanteenPoiLogComposeBO wmScCanteenPoiLogComposeBO
     * @return string
     */
    public String composeCanteenPoiBindV2(WmScCanteenPoiLogComposeBO wmScCanteenPoiLogComposeBO) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_1", "com.sankuai.meituan.waimai.customer.service.sc.WmScLogService.composeCanteenPoiBindV2(com.sankuai.meituan.waimai.customer.domain.sc.WmScCanteenPoiLogComposeBO)");
        log.info("[WmScLogService.composeCanteenPoiBindV2] input param: wmScCanteenPoiLogComposeBO = {}", JSONObject.toJSONString(wmScCanteenPoiLogComposeBO));
        if (wmScCanteenPoiLogComposeBO == null
                || wmScCanteenPoiLogComposeBO.getPreAuditStatus() == null
                || wmScCanteenPoiLogComposeBO.getAftAuditStatus() == null
                || wmScCanteenPoiLogComposeBO.getValues() == null) {
            log.error("[WmScLogService.composeCanteenPoiBindV2] wmScCanteenPoiLogComposeBO is null.");
            return Strings.EMPTY;
        }

        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append("当前状态:")
                .append(wmScCanteenPoiLogComposeBO.getPreAuditStatus().getName())
                .append("->")
                .append(wmScCanteenPoiLogComposeBO.getAftAuditStatus().getName())
                .append(wmScCanteenPoiLogComposeBO.getEnter());
        // 组成状态部分的文字 比如：生成任务（任务id：101866142)
        for (Byte key : wmScCanteenPoiLogComposeBO.getValues().keySet()) {
            stringBuilder.append(WmCanteenPoiAuditFlowV2Enum.getPre(key))
                    .append(wmScCanteenPoiLogComposeBO.getValues().get(key))
                    .append(WmCanteenPoiAuditFlowV2Enum.getAft(key))
                    .append(wmScCanteenPoiLogComposeBO.getEnter());
        }
        // 是否需要展示门店信息
        if (wmScCanteenPoiLogComposeBO.isAppendPoiInfo()) {
            for (WmPoiAggre wmPoiAggre : wmScCanteenPoiLogComposeBO.getEffectPoiAggreList()) {
                stringBuilder.append(wmPoiAggre.getName()).append(" ").append(wmPoiAggre.getWm_poi_id()).append(".");
            }
        }
        return stringBuilder.toString();
    }

    /**
     * 发起新增门店任务中的食堂换绑门店日志部分
     *
     * @param canteenPoiTaskId 食堂绑定门店任务ID（wmCanteenPoiTaskBO 的ID）
     * @param canteenIdTo      状态
     * @param canteenIdFrom    值
     * @return string
     */
    public String composeCanteenPoiChangeBind(Long canteenPoiTaskId,
                                              Integer canteenIdTo,
                                              Integer canteenIdFrom) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.service.sc.WmScLogService.composeCanteenPoiChangeBind(java.lang.Long,java.lang.Integer,java.lang.Integer)");
        WmScCanteenPoiTaskDO wmScCanteenPoiTaskDO = wmScCanteenPoiTaskMapper.selectByPrimaryKey(canteenPoiTaskId);
        WmCanteenDB wmCanteenDBTo = wmCanteenMapper.selectCanteenById(canteenIdTo);
        WmCanteenDB wmCanteenDBFrom = wmCanteenMapper.selectCanteenById(canteenIdFrom);
        StringBuilder msgBuilder = new StringBuilder();
        msgBuilder.append("换绑前食堂：").
                append(wmCanteenDBFrom.getCanteenName()).
                append("(").append(wmCanteenDBFrom.getId()).append(")").
                append(WmCanteenLogPreConstant.CANTEEN_ENTER);
        msgBuilder.append("换绑后食堂：").
                append(wmCanteenDBTo.getCanteenName()).
                append("(").append(wmCanteenDBTo.getId()).append(")").
                append(WmCanteenLogPreConstant.CANTEEN_ENTER);

        if (wmScCanteenPoiTaskDO.getTaskReasonType() != WmChangeBindCanPoiReasonTypeEnum.OTHER_REASON.getCode()) {
            msgBuilder.append("换绑原因：").append(WmChangeBindCanPoiReasonTypeEnum.of(wmScCanteenPoiTaskDO.getTaskReasonType()).getName());
        } else {
            msgBuilder.append("换绑原因：").append(wmScCanteenPoiTaskDO.getTaskReason());
        }
        return msgBuilder.toString();
    }

    /**
     * 在原食堂发起的日志：记录换绑的发起，成功，失败日志
     * @param wmScCanteenPoiChangeBindLogBO WmScCanteenPoiChangeBindLogBO
     */
    public void saveChangeBindPoiLog(WmScCanteenPoiChangeBindLogBO wmScCanteenPoiChangeBindLogBO) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_2", "com.sankuai.meituan.waimai.customer.service.sc.WmScLogService.saveChangeBindPoiLog(com.sankuai.meituan.waimai.customer.domain.sc.WmScCanteenPoiChangeBindLogBO)");
        log.info("[WmScLogService.saveChangeBindPoiLog] inut param: WmScCanteenPoiChangeBindLogBO = {}", JSONObject.toJSONString(wmScCanteenPoiChangeBindLogBO));
        if (wmScCanteenPoiChangeBindLogBO == null || wmScCanteenPoiChangeBindLogBO.getWmChangeBindPoiLogTypeEnum() == null) {
            log.error("[WmScLogService.saveChangeBindPoiLog] WmScCanteenPoiChangeBindLogBO is null.");
            return;
        }

        executorService.submit(() -> {
            try {
                String logInfo = WmCanteenLogPreConstant.CHANGE_BIND_POI_PRE
                        + wmScCanteenPoiChangeBindLogBO.getWmChangeBindPoiLogTypeEnum().getName()
                        + WmCanteenLogPreConstant.CANTEEN_ENTER
                        + composeCanteenPoiChangeBind(wmScCanteenPoiChangeBindLogBO.getCanteenPoiTaskId(), wmScCanteenPoiChangeBindLogBO.getCanteenIdTo(), wmScCanteenPoiChangeBindLogBO.getCanteenIdFrom())
                        + WmCanteenLogPreConstant.CANTEEN_ENTER;

                StringBuilder stringBuilder = new StringBuilder();
                stringBuilder.append("换绑门店：");
                for (WmPoiAggre wmPoiAggre : wmScCanteenPoiChangeBindLogBO.getChangePoiAggreList()) {
                    stringBuilder.append(wmPoiAggre.getName()).append(" ").append(wmPoiAggre.getWm_poi_id()).append(".");
                }
                logInfo = logInfo + stringBuilder.toString();
                insertScOptLog(OptTypeEnum.POI_BING.getType(),
                        SC_CANTEEN_LOG,
                        wmScCanteenPoiChangeBindLogBO.getCanteenIdFrom(),
                        wmScCanteenPoiChangeBindLogBO.getOpUid(),
                        wmScCanteenPoiChangeBindLogBO.getOpUname(),
                        logInfo, "");
            } catch (Exception e) {
                log.error("[WmScLogService.saveChangeBindPoiLog] Exception. wmScCanteenPoiChangeBindLogBO = {}",
                        JSONObject.toJSONString(wmScCanteenPoiChangeBindLogBO), e);
            }
        });
    }

    /**
     * 在任务流程中，门店下线或者门店迁移到校外，当任务流程走完后门店与食堂关系不生效
     *
     * @param canteenIdTo        将要绑上的食堂ID
     * @param wmScCanIllegalPois 日志信息
     */
    public void saveIllegalCanteenPoiLog(Integer canteenIdTo, List<WmScCanIllegalPoi> wmScCanIllegalPois) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.service.sc.WmScLogService.saveIllegalCanteenPoiLog(java.lang.Integer,java.util.List)");
        executorService.submit(() -> {
            try {
                for (WmScCanIllegalPoi wmScCanIllegalPoi : wmScCanIllegalPois) {
                    StringBuilder msgBuilder = new StringBuilder();
                    msgBuilder.append(WmCanteenLogPreConstant.POI_BIND_CANTEEN_FAIL).append(WmCanteenLogPreConstant.CANTEEN_ENTER);
                    msgBuilder.append(WmIllegalCanPoiTypeEnum.getByCode(wmScCanIllegalPoi.getIllegalPoiType()).getName()).append(":");
                    msgBuilder.append(wmScCanIllegalPoi.getPoiName()).append(wmScCanIllegalPoi.getWmPoiId());
                    insertScOptLog(OptTypeEnum.POI_BING.getType(), SC_CANTEEN_LOG, canteenIdTo, 0, "系统", msgBuilder.toString(), "");
                }
            } catch (Exception e) {
                log.error("[WmScLogService.saveIllegalCanteenPoiLog] Exception. canteenIdTo = {}, wmScCanIllegalPois = {}",
                        canteenIdTo, JSONObject.toJSONString(wmScCanIllegalPois), e);
            }
        });
    }

    /**
     * 保存，由于定时任务清洗门店下线或者门店处于学校外面这些原因导致学校解绑的日志。
     *
     * @param wmScCanIllegalPoi 需要清洗的门店
     * @param canteenId         门店所绑定的食堂的ID
     * @param opUid
     * @param opUname
     */
    public void saveUnBindCanteenPoiLog(WmScCanIllegalPoi wmScCanIllegalPoi, Integer canteenId, Integer opUid, String opUname) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.sc.WmScLogService.saveUnBindCanteenPoiLog(WmScCanIllegalPoi,Integer,Integer,String)");
        executorService.submit(() -> {
            try {
                StringBuilder msgBuilder = new StringBuilder();
                msgBuilder.append(WmCanteenLogPreConstant.FORCE_UNBIND_POI_PRE).append(WmCanteenLogPreConstant.CANTEEN_ENTER);
                WmIllegalCanPoiTypeEnum wmIllegalCanPoiTypeEnum = WmIllegalCanPoiTypeEnum.getByCode(wmScCanIllegalPoi.getIllegalPoiType());
                if (wmIllegalCanPoiTypeEnum == null) {
                    log.error("[食堂管理]保存门店解绑日志异常：无效的解绑原因");
                    return;
                }
                msgBuilder.append(wmIllegalCanPoiTypeEnum.getLogMsg()).append(WmCanteenLogPreConstant.CANTEEN_ENTER);
                msgBuilder.append(wmScCanIllegalPoi.getPoiName()).append(wmScCanIllegalPoi.getWmPoiId());
                insertScOptLog(OptTypeEnum.POI_BING.getType(), SC_CANTEEN_LOG, canteenId, opUid, opUname, msgBuilder.toString(), "");
            } catch (Exception e) {
                log.error("[食堂管理]保存由定时任务方式，清洗下线门店或者处于学校外面门店的日志错误", e);
            }
        });
    }

    /**
     * 保存学校标签变更日志
     * @param schoolId  学校ID
     * @param labelId   标签ID
     * @param opUid     操作人ID
     * @param opUname   操作人名称
     * @param type      操作类型
     */
    public void saveSchoolLabelChangeLog(Integer schoolId, Integer labelId, Integer opUid, String opUname, String type) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.sc.WmScLogService.saveSchoolLabelChangeLog(java.lang.Integer,java.lang.Integer,java.lang.Integer,java.lang.String,java.lang.String)");
        executorService.submit(() -> {
            try {
                StringBuilder msgBuilder = new StringBuilder();
                byte opType = 0;
                msgBuilder.append("[学校标签变更] 类型:").append("\n");
                if ("insert".equals(type)) {
                    opType = OptTypeEnum.INSERT.getType();
                    msgBuilder.append("打标");
                } else if ("delete".equals(type)) {
                    opType = OptTypeEnum.DELETE.getType();
                    msgBuilder.append("掉标");
                } else if ("update".equals(type)) {
                    opType = OptTypeEnum.UPDATE.getType();
                    msgBuilder.append("换标");
                }
                msgBuilder.append("标签[labelId:").append(labelId).append("] 学校[schoolId:").append(schoolId).append("]");
                insertScOptLog(opType, SC_SCHOOL_LOG, schoolId%10000, opUid, opUname, msgBuilder.toString(), "");
            } catch (Exception e) {
                log.error("[saveSchoolLabelChangeLog] 保存学校标签变更到操作日志表异常, schoolId = {}, labelId = {}, opUid = {}, opUname = {}, type = {}", schoolId, labelId, opUid, opUname, type, e);
            }
        });
    }

    /**
     * 获取批量修改学校责任人的操作日志LIST
     * @param wmSchoolDBList wmSchoolDBList
     * @param responsiblePersonMis 新的学校责任人MIS
     * @return Map<Integer, String> key->学校主键ID val->日志内容
     */
    public Map<Integer, String> getBatchModifySchoolRpLogInfoList(List<WmSchoolDB> wmSchoolDBList, String responsiblePersonMis) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.service.sc.WmScLogService.getBatchModifySchoolRpLogInfoList(java.util.List,java.lang.String)");
        Map<Integer, String> logInfoMap = new HashMap<>();
        if (CollectionUtils.isEmpty(wmSchoolDBList)) {
            return logInfoMap;
        }

        for (WmSchoolDB wmSchoolDB : wmSchoolDBList) {
            String logInfo = "操作：修改\\n" +
                    "提交来源：学校管理系统\\n" +
                    "[字段变更] 学校责任人: " + wmSchoolDB.getResponsiblePerson() + "=>" + responsiblePersonMis +
                    "\\n";
            logInfoMap.put(wmSchoolDB.getId(), logInfo);
        }
        return logInfoMap;
    }

    /**
     * 记录食堂档口线索跟进状态提审日志
     * @param logBO logBO
     */
    public void recordCanteenStallAuditTaskSubmitLog(WmCanteenStallLogBO logBO) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.service.sc.WmScLogService.recordCanteenStallAuditTaskSubmitLog(com.sankuai.meituan.waimai.customer.domain.sc.canteenstall.WmCanteenStallLogBO)");
        if (logBO == null || org.apache.commons.collections.CollectionUtils.isEmpty(logBO.getBindIdList())) {
            log.error("[WmScLogService.recordAuditTaskSubmitLog] input param invalid. logBO = {}", JSONObject.toJSONString(logBO));
            return;
        }

        String logInfo = "操作：线索跟进状态提交审批\\n" +
                "档口绑定任务ID：" + StringUtils.join(logBO.getBindIdList(), "、") + "\\n" +
                "审批任务：" + CanteenStallAuditTaskTypeEnum.getByType(logBO.getAuditTaskType()).getName() + "\\n" +
                getAbnormalReasonAndProofPictureText(logBO) +
                "[字段待变更] 档口绑定任务线索跟进状态：" + getClueFollowUpStatusText(logBO.getAuditTaskType()) + "\\n";

        // 档口绑定任务日志
        for (Integer bindId : logBO.getBindIdList()) {
            insertScOptLog(OptTypeEnum.INSERT.getType(), SC_CANTEEN_STALL_BIND_LOG, bindId,
                    logBO.getUserId(), logBO.getUserName(), logInfo, "");
        }

        // 食堂信息日志
        insertScOptLog(OptTypeEnum.INSERT.getType(), SC_CANTEEN_LOG, logBO.getCanteenPrimaryId(),
                logBO.getUserId(), logBO.getUserName(), logInfo, "");
    }

    public String getAbnormalReasonAndProofPictureText(WmCanteenStallLogBO logBO) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.service.sc.WmScLogService.getAbnormalReasonAndProofPictureText(com.sankuai.meituan.waimai.customer.domain.sc.canteenstall.WmCanteenStallLogBO)");
        if (logBO.getAuditTaskType().equals((int) CanteenStallAuditTaskTypeEnum.STALL_BIND_CLUE_NORMAL.getType())) {
            return Strings.EMPTY;
        }

        String proofPicture = StringUtils.isBlank(logBO.getProofPicture()) ? nullShow : logBO.getProofPicture();
        return "标记原因：" + CanteenStallClueAbnormalReasonEnum.getByType(logBO.getAbnormalReason()).getName() + "\\n" +
                "标记证明：" + proofPicture + "\\n";
    }

    private String getClueFollowUpStatusText(Integer auditTaskType) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_1", "com.sankuai.meituan.waimai.customer.service.sc.WmScLogService.getClueFollowUpStatusText(java.lang.Integer)");
        CanteenStallAuditTaskTypeEnum taskTypeEnum = CanteenStallAuditTaskTypeEnum.getByType(auditTaskType);
        if (taskTypeEnum.getType() == CanteenStallAuditTaskTypeEnum.STALL_BIND_CLUE_ABNORMAL.getType()) {
            return "正常->异常";
        } else {
            return "异常->正常";
        }
    }

    /**
     * 记录食堂档口线索跟进状态审批生效日志
     * @param logBO logBO
     */
    public void recordCanteenStallAuditEffectLog(WmCanteenStallLogBO logBO) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_2", "com.sankuai.meituan.waimai.customer.service.sc.WmScLogService.recordCanteenStallAuditEffectLog(com.sankuai.meituan.waimai.customer.domain.sc.canteenstall.WmCanteenStallLogBO)");
        if (logBO == null || org.apache.commons.collections.CollectionUtils.isEmpty(logBO.getBindIdList())) {
            log.error("[WmScLogService.recordCanteenStallAuditEffectLog] input param invalid. logBO = {}", JSONObject.toJSONString(logBO));
            return;
        }

        String logInfo = "操作：线索跟进状态审批生效\\n" +
                "档口绑定任务ID：" + StringUtils.join(logBO.getBindIdList(), "、") + "\\n" +
                "审批任务：" + CanteenStallAuditTaskTypeEnum.getByType(logBO.getAuditTaskType()).getName() + "\\n" +
                "审批任务ID：" + logBO.getAuditTaskId() + "\\n" +
                "[字段变更] 档口绑定任务线索跟进状态：" + getClueFollowUpStatusText(logBO.getAuditTaskType()) + "\\n";

        // 档口绑定任务日志
        for (Integer bindId : logBO.getBindIdList()) {
            insertScOptLog(OptTypeEnum.INSERT.getType(), SC_CANTEEN_STALL_BIND_LOG, bindId,
                    logBO.getUserId(), logBO.getUserName(), logInfo, "");
        }

        // 食堂信息日志
        insertScOptLog(OptTypeEnum.INSERT.getType(), SC_CANTEEN_LOG, logBO.getCanteenPrimaryId(),
                logBO.getUserId(), logBO.getUserName(), logInfo, "");
    }

    /**
     * 记录食堂档口线索跟进状态审批通过
     * @param logBO logBO
     */
    public void recordCanteenStallAuditPassLog(WmCanteenStallLogBO logBO) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.service.sc.WmScLogService.recordCanteenStallAuditPassLog(com.sankuai.meituan.waimai.customer.domain.sc.canteenstall.WmCanteenStallLogBO)");
        if (logBO == null || org.apache.commons.collections.CollectionUtils.isEmpty(logBO.getBindIdList())) {
            log.error("[WmScLogService.recordCanteenStallAuditPassLog] input param invalid. logBO = {}", JSONObject.toJSONString(logBO));
            return;
        }

        String logInfo = "操作：线索跟进状态审批通过\\n" +
                "档口绑定任务ID：" + StringUtils.join(logBO.getBindIdList(), "、") + "\\n" +
                "审批任务：" + CanteenStallAuditTaskTypeEnum.getByType(logBO.getAuditTaskType()).getName() + "\\n" +
                "审批任务ID：" + logBO.getAuditTaskId() + "\\n" +
                "审批人：" + logBO.getAuditorName() + "(" + logBO.getAuditorMis() + ")" + "\\n" +
                "下一审批节点：" + CanteenStallAuditNodeTypeEnum.getByType(logBO.getNextAuditNode()).getName() + "\n";

        // 档口绑定任务日志
        for (Integer bindId : logBO.getBindIdList()) {
            insertScOptLog(OptTypeEnum.INSERT.getType(), SC_CANTEEN_STALL_BIND_LOG, bindId,
                    logBO.getUserId(), logBO.getUserName(), logInfo, "");
        }

        // 食堂信息日志
        insertScOptLog(OptTypeEnum.INSERT.getType(), SC_CANTEEN_LOG, logBO.getCanteenPrimaryId(),
                logBO.getUserId(), logBO.getUserName(), logInfo, "");
    }

    /**
     * 记录食堂档口线索跟进状态审批终止
     * @param logBO logBO
     */
    public void recordCanteenStallAuditStopLog(WmCanteenStallLogBO logBO) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.service.sc.WmScLogService.recordCanteenStallAuditStopLog(com.sankuai.meituan.waimai.customer.domain.sc.canteenstall.WmCanteenStallLogBO)");
        if (logBO == null || org.apache.commons.collections.CollectionUtils.isEmpty(logBO.getBindIdList())) {
            log.error("[WmScLogService.recordCanteenStallAuditStopLog] input param invalid. logBO = {}", JSONObject.toJSONString(logBO));
            return;
        }

        String logInfo = "操作：线索跟进状态审批终结\\n" +
                "档口绑定任务ID：" + StringUtils.join(logBO.getBindIdList(), "、") + "\\n" +
                "审批任务：" + CanteenStallAuditTaskTypeEnum.getByType(logBO.getAuditTaskType()).getName() + "\\n" +
                "审批任务ID：" + logBO.getAuditTaskId() + "\\n" +
                "审批人：" + logBO.getAuditorName() + "(" + logBO.getAuditorMis() + ")" + "\\n";

        // 档口绑定任务日志
        for (Integer bindId : logBO.getBindIdList()) {
            insertScOptLog(OptTypeEnum.INSERT.getType(), SC_CANTEEN_STALL_BIND_LOG, bindId,
                    logBO.getUserId(), logBO.getUserName(), logInfo, "");
        }

        // 食堂信息日志
        insertScOptLog(OptTypeEnum.INSERT.getType(), SC_CANTEEN_LOG, logBO.getCanteenPrimaryId(),
                logBO.getUserId(), logBO.getUserName(), logInfo, "");
    }

    /**
     * 记录食堂档口线索跟进状态审批驳回
     * @param logBO logBO
     */
    public void recordCanteenStallAuditRejectLog(WmCanteenStallLogBO logBO) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.sc.WmScLogService.recordCanteenStallAuditRejectLog(com.sankuai.meituan.waimai.customer.domain.sc.canteenstall.WmCanteenStallLogBO)");
        if (logBO == null || org.apache.commons.collections.CollectionUtils.isEmpty(logBO.getBindIdList())) {
            log.error("[WmScLogService.recordCanteenStallAuditRejectLog] input param invalid. logBO = {}", JSONObject.toJSONString(logBO));
            return;
        }

        String logInfo = "操作：线索跟进状态审批驳回\\n" +
                "档口绑定任务ID：" + StringUtils.join(logBO.getBindIdList(), "、") + "\\n" +
                "审批任务：" + CanteenStallAuditTaskTypeEnum.getByType(logBO.getAuditTaskType()).getName() + "\\n" +
                "审批任务ID：" + logBO.getAuditTaskId() + "\\n" +
                "审批人：" + logBO.getAuditorName() + "(" + logBO.getAuditorMis() + ")" + "\\n";

        // 档口绑定任务日志
        for (Integer bindId : logBO.getBindIdList()) {
            insertScOptLog(OptTypeEnum.INSERT.getType(), SC_CANTEEN_STALL_BIND_LOG, bindId,
                    logBO.getUserId(), logBO.getUserName(), logInfo, "");
        }

        // 食堂信息日志
        insertScOptLog(OptTypeEnum.INSERT.getType(), SC_CANTEEN_LOG, logBO.getCanteenPrimaryId(),
                logBO.getUserId(), logBO.getUserName(), logInfo, "");
    }

    /**
     * 食堂档口批量创建线索操作日志
     * @param logBO logBO
     */
    public void recordCanteenStallBatchCreateClueLog(WmCanteenStallLogBO logBO) {
        if (logBO == null || logBO.getBindId() == null) {
            log.error("[WmScLogService.recordCanteenStallBatchCreateClueLog] input param invalid. logBO = {}", JSONObject.toJSONString(logBO));
            return;
        }

        String title = "操作：批量创建线索\\n" +
                "档口绑定任务ID：" + logBO.getBindId() + "\\n" +
                "档口管理任务ID：" + logBO.getManageId() + "\\n";
        // 线索信息日志
        String clueLogInfo = composeCanteenStallBatchCreateClueLogWithClue(logBO.getClueDO());
        // 档口绑定任务日志
        String bindLogInfo = composeCanteenStallBatchCreateClueLogWithBind(logBO.getBindDOAfter());

        // 档口绑定任务操作日志
        insertScOptLog(OptTypeEnum.INSERT.getType(), SC_CANTEEN_STALL_BIND_LOG, logBO.getBindId(),
                logBO.getUserId(), logBO.getUserName(), title + clueLogInfo + bindLogInfo, "");

        // 食堂详情操作日志
        insertScOptLog(OptTypeEnum.INSERT.getType(), SC_CANTEEN_LOG, logBO.getCanteenPrimaryId(),
                logBO.getUserId(), logBO.getUserName(), title + clueLogInfo + bindLogInfo, "");
    }

    /**
     * 创建线索绑定任务(公海绑定)操作日志
     * @param logBO logBO
     */
    public void recordCreateStallBindByWdcBindLog(WmCanteenStallLogBO logBO) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.sc.WmScLogService.recordCreateStallBindByWdcBindLog(com.sankuai.meituan.waimai.customer.domain.sc.canteenstall.WmCanteenStallLogBO)");
        String logInfo = "操作：新增档口绑定任务（公海绑定）\\n" +
                "档口绑定任务ID：" + logBO.getBindId() + "\\n" +
                composeCanteenStallBindInsertLog(logBO.getBindDOAfter());
        // 档口绑定任务操作日志
        insertScOptLog(OptTypeEnum.INSERT.getType(), SC_CANTEEN_STALL_BIND_LOG, logBO.getBindId(),
                logBO.getUserId(), logBO.getUserName(), logInfo, "");

        // 食堂详情操作日志
        insertScOptLog(OptTypeEnum.INSERT.getType(), SC_CANTEEN_LOG, logBO.getCanteenPrimaryId(),
                logBO.getUserId(), logBO.getUserName(), logInfo, "");
    }

    /**
     * 创建线索绑定任务(外卖门店绑定)操作日志
     * @param logBO logBO
     */
    public void recordCreateStallBindByWmPoiBindLog(WmCanteenStallLogBO logBO) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.sc.WmScLogService.recordCreateStallBindByWmPoiBindLog(com.sankuai.meituan.waimai.customer.domain.sc.canteenstall.WmCanteenStallLogBO)");
        String logInfo = "操作：新增档口绑定任务（外卖门店绑定）\\n" +
                "档口绑定任务ID：" + logBO.getBindId() + "\\n" +
                composeCanteenStallBindInsertLog(logBO.getBindDOAfter());
        // 档口绑定任务操作日志
        insertScOptLog(OptTypeEnum.INSERT.getType(), SC_CANTEEN_STALL_BIND_LOG, logBO.getBindId(),
                logBO.getUserId(), logBO.getUserName(), logInfo, "");

        // 食堂详情操作日志
        insertScOptLog(OptTypeEnum.INSERT.getType(), SC_CANTEEN_LOG, logBO.getCanteenPrimaryId(),
                logBO.getUserId(), logBO.getUserName(), logInfo, "");
    }

    /**
     * 创建线索绑定任务(系统初始化)操作日志
     * @param logBO logBO
     */
    public void recordCreateStallBindByInitLog(WmCanteenStallLogBO logBO) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.service.sc.WmScLogService.recordCreateStallBindByInitLog(com.sankuai.meituan.waimai.customer.domain.sc.canteenstall.WmCanteenStallLogBO)");
        String logInfo = "操作：新增档口绑定任务（系统初始化）\\n" +
                "档口绑定任务ID：" + logBO.getBindId() + "\\n" +
                composeCanteenStallBindInsertLog(logBO.getBindDOAfter());

        // 档口绑定任务操作日志
        insertScOptLog(OptTypeEnum.INSERT.getType(), SC_CANTEEN_STALL_BIND_LOG, logBO.getBindId(),
                logBO.getUserId(), logBO.getUserName(), logInfo, "");

        // 食堂详情操作日志
        insertScOptLog(OptTypeEnum.INSERT.getType(), SC_CANTEEN_LOG, logBO.getCanteenPrimaryId(),
                logBO.getUserId(), logBO.getUserName(), logInfo, "");
    }


    public void recordClueGenerateStatusToSuccessLog(WmCanteenStallLogBO logBO) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_1", "com.sankuai.meituan.waimai.customer.service.sc.WmScLogService.recordClueGenerateStatusToSuccessLog(com.sankuai.meituan.waimai.customer.domain.sc.canteenstall.WmCanteenStallLogBO)");
        String logInfo = "操作：线索生成成功（" + logBO.getOperationSource() + "）\\n" +
                "线索ID：" + logBO.getWdcClueId() + "\\n" +
                "档口绑定任务ID：" + logBO.getBindId() + "\\n" +
                composeCanteenStallBindUpdateLog(logBO.getBindDOBefore(), logBO.getBindDOAfter());

        // 档口绑定任务操作日志
        insertScOptLog(OptTypeEnum.INSERT.getType(), SC_CANTEEN_STALL_BIND_LOG, logBO.getBindId(),
                logBO.getUserId(), logBO.getUserName(), logInfo, "");

        // 食堂信息操作日志
        insertScOptLog(OptTypeEnum.INSERT.getType(), SC_CANTEEN_LOG, logBO.getCanteenPrimaryId(),
                logBO.getUserId(), logBO.getUserName(), logInfo, "");
    }


    /**
     *
     * */
    public void transferRecordUnbindCanteenPoiLog(WmCanteenStallLogBO logBO) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_2", "com.sankuai.meituan.waimai.customer.service.sc.WmScLogService.transferRecordUnbindCanteenPoiLog(com.sankuai.meituan.waimai.customer.domain.sc.canteenstall.WmCanteenStallLogBO)");
        String clueDiff = "";
        if(logBO.getIsClueBind() != null && logBO.getIsClueBind() != null  && logBO.getIsClueBind() == true){
            CanteenStallClueBindStatusEnum statusEnum = CanteenStallClueBindStatusEnum.getByType(logBO.getClueStatus());
            String statusName = statusEnum != null ? statusEnum.getName() : null;
            clueDiff = "[字段变更]线索绑定状态：" + statusName + "=>未绑定";
        }
        String logInfo = "操作：门店换绑申请通过\\n" +
                "门店ID：" + logBO.getWmPoiId() + "\\n" +
                "档口绑定任务ID：" + logBO.getBindId() + "\\n" +
                "[字段变更] 外卖门店绑定状态：换绑中=>未绑定\\n" + clueDiff;

        // 档口绑定任务操作日志
        insertScOptLog(OptTypeEnum.INSERT.getType(), SC_CANTEEN_STALL_BIND_LOG, logBO.getBindId(),
                logBO.getUserId(), logBO.getUserName(), logInfo, "");

        // 食堂信息操作日志
        insertScOptLog(OptTypeEnum.INSERT.getType(), SC_CANTEEN_LOG, logBO.getCanteenPrimaryId(),
                logBO.getUserId(), logBO.getUserName(), logInfo, "");
    }

    public void recordUnbindCanteenPoiLog(WmCanteenStallLogBO logBO) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.sc.WmScLogService.recordUnbindCanteenPoiLog(com.sankuai.meituan.waimai.customer.domain.sc.canteenstall.WmCanteenStallLogBO)");
        String logInfo;
        if (Objects.nonNull(logBO.getBeforeWmPoiBindStatus())) {
            CanteenStallWmPoiBindStatusEnum statusEnum = CanteenStallWmPoiBindStatusEnum.getByType(logBO.getBeforeWmPoiBindStatus());
            logInfo = "操作：门店解绑成功（" + logBO.getOperationSource() + "）\\n" +
                    "门店ID：" + logBO.getWmPoiId() + "\\n" +
                    "档口绑定任务ID：" + logBO.getBindId() + "\\n" +
                    "[字段变更] 外卖门店绑定状态：" + statusEnum.getName() + "=>未绑定\\n";
        } else {
            logInfo = "操作：门店解绑成功（" + logBO.getOperationSource() + "）\\n" +
                    "门店ID：" + logBO.getWmPoiId() + "\\n" +
                    "档口绑定任务ID：" + logBO.getBindId() + "\\n" +
                    "[字段变更] 外卖门店绑定状态：绑定成功=>未绑定\\n";
        }

        // 档口绑定任务操作日志
        insertScOptLog(OptTypeEnum.INSERT.getType(), SC_CANTEEN_STALL_BIND_LOG, logBO.getBindId(),
                logBO.getUserId(), logBO.getUserName(), logInfo, "");

        // 食堂信息操作日志
        insertScOptLog(OptTypeEnum.INSERT.getType(), SC_CANTEEN_LOG, logBO.getCanteenPrimaryId(),
                logBO.getUserId(), logBO.getUserName(), logInfo, "");
    }




    public void recordBindCanteenPoiLog(WmCanteenStallLogBO logBO) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.service.sc.WmScLogService.recordBindCanteenPoiLog(com.sankuai.meituan.waimai.customer.domain.sc.canteenstall.WmCanteenStallLogBO)");
        CanteenStallWmPoiBindStatusEnum statusEnum = CanteenStallWmPoiBindStatusEnum.getByType(logBO.getBindDOBefore().getWmPoiBindStatus());
        String logInfo = "操作：门店绑定成功（" + logBO.getOperationSource() + "）\\n" +
                "门店ID：" + logBO.getWmPoiId() + "\\n" +
                "档口绑定任务ID：" + logBO.getBindId() + "\\n" +
                "[字段变更] 外卖门店绑定状态：" + statusEnum.getName() + "=>绑定成功\\n";

        // 档口绑定任务操作日志
        insertScOptLog(OptTypeEnum.INSERT.getType(), SC_CANTEEN_STALL_BIND_LOG, logBO.getBindId(),
                logBO.getUserId(), logBO.getUserName(), logInfo, "");

        // 食堂信息操作日志
        insertScOptLog(OptTypeEnum.INSERT.getType(), SC_CANTEEN_LOG, logBO.getCanteenPrimaryId(),
                logBO.getUserId(), logBO.getUserName(), logInfo, "");
    }

    /***
     *    换绑审批通过
     * **/
    public void transfeRecordBindCanteenPoiLog(WmCanteenStallLogBO logBO) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_1", "com.sankuai.meituan.waimai.customer.service.sc.WmScLogService.transfeRecordBindCanteenPoiLog(com.sankuai.meituan.waimai.customer.domain.sc.canteenstall.WmCanteenStallLogBO)");
        CanteenStallWmPoiBindStatusEnum statusEnum = CanteenStallWmPoiBindStatusEnum.getByType(logBO.getBindDOBefore().getWmPoiBindStatus());
        String clueDiff = "";
        if(logBO.getWdcClueId() != null){
            clueDiff = "[字段变更] 线索绑定状态：" + "=>绑定成功\\n";
        }

        String logInfo = "操作：门店换绑成功" +"\\n" +
                "门店ID：" + logBO.getWmPoiId() + "\\n" +
                "档口绑定任务ID：" + logBO.getBindId() + "\\n" +
                "[字段变更] 外卖门店绑定状态：" + CanteenStallWmPoiBindStatusEnum.REBINDING.getName() + "=>绑定成功\\n" +
                clueDiff
               /* "[字段变更] 档口绑定任务：" + statusEnum.getName() + "=>绑定成功\\n" +*/
                ;

        // 档口绑定任务操作日志
        insertScOptLog(OptTypeEnum.INSERT.getType(), SC_CANTEEN_STALL_BIND_LOG, logBO.getBindId(),
                logBO.getUserId(), logBO.getUserName(), logInfo, "");

        // 食堂信息操作日志
        insertScOptLog(OptTypeEnum.INSERT.getType(), SC_CANTEEN_LOG, logBO.getCanteenPrimaryId(),
                logBO.getUserId(), logBO.getUserName(), logInfo, "");
    }

    /***
     * 解绑审批通过
     * */
    public void recordUnbindCanteenPoiLogAfterAudit(WmCanteenStallLogBO logBO) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.service.sc.WmScLogService.recordUnbindCanteenPoiLogAfterAudit(com.sankuai.meituan.waimai.customer.domain.sc.canteenstall.WmCanteenStallLogBO)");

        String diff = "";
        if(logBO.getIsClueBind()){
            CanteenStallClueBindStatusEnum statusEnum = CanteenStallClueBindStatusEnum.getByType(logBO.getClueStatus());
            if(statusEnum != null && statusEnum != CanteenStallClueBindStatusEnum.UNBIND){
                diff = "[字段变更] 线索绑定状态：" + statusEnum.getName() + "=>未绑定\\n";
            }
        }
        String logInfo = "门店解绑申请通过\\n" +
                "门店ID：" + logBO.getWmPoiId() + "\\n" +
                "档口绑定任务ID：" + logBO.getBindId() + "\\n" +
                "[字段变更] 外卖门店绑定状态：解绑中=>未绑定\\n" +
                diff;

        // 档口绑定任务操作日志
        insertScOptLog(OptTypeEnum.INSERT.getType(), SC_CANTEEN_STALL_BIND_LOG, logBO.getBindId(),
                logBO.getUserId(), logBO.getUserName(), logInfo, "");

        // 食堂信息操作日志
        insertScOptLog(OptTypeEnum.INSERT.getType(), SC_CANTEEN_LOG, logBO.getCanteenPrimaryId(),
                logBO.getUserId(), logBO.getUserName(), logInfo, "");
    }


    /**
     *   换绑失败后恢复状态
     *
     * **/
    public void transfeRecordBindCanteenPoiLogReject(WmCanteenStallLogBO logBO) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.service.sc.WmScLogService.transfeRecordBindCanteenPoiLogReject(com.sankuai.meituan.waimai.customer.domain.sc.canteenstall.WmCanteenStallLogBO)");
        /*CanteenStallWmPoiBindStatusEnum statusEnum = CanteenStallWmPoiBindStatusEnum.getByType(logBO.getBindDOBefore().getWmPoiBindStatus());
        String clueDiff = "";
        if(logBO.getWdcClueId() != null){
            clueDiff = "[字段变更] 线索绑定状态：" + "=>绑定成功\\n";
        }*/
        String logInfo = "操作：门店换绑"+ InvalidDataSourceEnum.getDescriptionByCode(logBO.getInvalidDataSourceCode())+"\\n" +
                "门店ID：" + logBO.getWmPoiId() + "\\n" +
                "档口绑定任务ID：" + logBO.getBindId() + "\\n" +
                "[字段变更] 外卖门店绑定状态："+ "换绑中=>绑定成功\\n"
                /* "[字段变更] 档口绑定任务：" + statusEnum.getName() + "=>绑定成功\\n" +*/
                ;

        // 档口绑定任务操作日志
        insertScOptLog(OptTypeEnum.INSERT.getType(), SC_CANTEEN_STALL_BIND_LOG, logBO.getBindId(),
                logBO.getUserId(), logBO.getUserName(), logInfo, "");

        // 食堂信息操作日志
        insertScOptLog(OptTypeEnum.INSERT.getType(), SC_CANTEEN_LOG, logBO.getCanteenPrimaryId(),
                logBO.getUserId(), logBO.getUserName(), logInfo, "");

        // 目标食堂信息操作日志
        insertScOptLog(OptTypeEnum.INSERT.getType(), SC_CANTEEN_LOG, Math.toIntExact(logBO.getCanteenToID()),
                logBO.getUserId(), logBO.getUserName(), logInfo, "");
    }

    /***
     *
     *  解绑失败后后续处理
     *
     * */

    public void unbindRecordBindCanteenPoiLogReject(WmCanteenStallLogBO logBO) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.service.sc.WmScLogService.unbindRecordBindCanteenPoiLogReject(com.sankuai.meituan.waimai.customer.domain.sc.canteenstall.WmCanteenStallLogBO)");
        /*CanteenStallWmPoiBindStatusEnum statusEnum = CanteenStallWmPoiBindStatusEnum.getByType(logBO.getBindDOBefore().getWmPoiBindStatus());
        String clueDiff = "";
        if(logBO.getWdcClueId() != null){
            clueDiff = "[字段变更] 线索绑定状态：" + "=>绑定成功\\n";
        }*/
        String logInfo = "操作：门店解绑"+InvalidDataSourceEnum.getDescriptionByCode(logBO.getInvalidDataSourceCode()) +"\\n" +
                "门店ID：" + logBO.getWmPoiId() + "\\n" +
                "档口绑定任务ID：" + logBO.getBindId() + "\\n" +
                "[字段变更] 外卖门店绑定状态："+ "解绑中=>绑定成功\\n"
                /* "[字段变更] 档口绑定任务：" + statusEnum.getName() + "=>绑定成功\\n" +*/
                ;

        // 档口绑定任务操作日志
        insertScOptLog(OptTypeEnum.INSERT.getType(), SC_CANTEEN_STALL_BIND_LOG, logBO.getBindId(),
                logBO.getUserId(), logBO.getUserName(), logInfo, "");

        // 食堂信息操作日志
        insertScOptLog(OptTypeEnum.INSERT.getType(), SC_CANTEEN_LOG, logBO.getCanteenPrimaryId(),
                logBO.getUserId(), logBO.getUserName(), logInfo, "");
    }

    /**
     * 食堂档口线索生成成功操作日志
     */
    public void recordClueGenerateSuccessLog(WmCanteenStallLogBO logBO) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.sc.WmScLogService.recordClueGenerateSuccessLog(com.sankuai.meituan.waimai.customer.domain.sc.canteenstall.WmCanteenStallLogBO)");
        if (logBO == null || logBO.getBindId() == null) {
            log.error("[WmScLogService.recordClueGenerateSuccessLog] input param invalid. logBO = {}", JSONObject.toJSONString(logBO));
            return;
        }

        String logInfo = "操作：线索生成成功（" + logBO.getOperationSource() + "）\\n" +
                "线索ID：" + logBO.getBindDOAfter().getWdcClueId() + "\\n" +
                "档口绑定任务ID：" + logBO.getBindId() + "\\n" +
                composeCanteenStallBindUpdateLog(logBO.getBindDOBefore(), logBO.getBindDOAfter());

        // 档口绑定任务操作日志
        insertScOptLog(OptTypeEnum.INSERT.getType(), SC_CANTEEN_STALL_BIND_LOG, logBO.getBindId(),
                logBO.getUserId(), logBO.getUserName(), logInfo, "");

        // 食堂信息操作日志
        insertScOptLog(OptTypeEnum.INSERT.getType(), SC_CANTEEN_LOG, logBO.getCanteenPrimaryId(),
                logBO.getUserId(), logBO.getUserName(), logInfo, "");
    }

    /**
     * 食堂档口线索生成失败操作日志
     */
    public void recordClueGenerateFailLog(WmCanteenStallLogBO logBO) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.service.sc.WmScLogService.recordClueGenerateFailLog(com.sankuai.meituan.waimai.customer.domain.sc.canteenstall.WmCanteenStallLogBO)");
        if (logBO == null || logBO.getBindId() == null) {
            log.error("[WmScLogService.recordClueGenerateFailLog] input param invalid. logBO = {}", JSONObject.toJSONString(logBO));
            return;
        }

        String logInfo = "操作：线索生成失败\\n" +
                "线索ID：" + logBO.getBindDOAfter().getWdcClueId() + "\\n" +
                "档口绑定任务ID：" + logBO.getBindId() + "\\n" +
                composeCanteenStallBindUpdateLog(logBO.getBindDOBefore(), logBO.getBindDOAfter());

        // 档口绑定任务操作日志
        insertScOptLog(OptTypeEnum.INSERT.getType(), SC_CANTEEN_STALL_BIND_LOG, logBO.getBindId(),
                logBO.getUserId(), logBO.getUserName(), logInfo, "");

        // 食堂信息操作日志
        insertScOptLog(OptTypeEnum.INSERT.getType(), SC_CANTEEN_LOG, logBO.getCanteenPrimaryId(),
                logBO.getUserId(), logBO.getUserName(), logInfo, "");
    }


    public void recordCanteenStallClueBindFailLog(WmCanteenStallLogBO logBO) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.service.sc.WmScLogService.recordCanteenStallClueBindFailLog(com.sankuai.meituan.waimai.customer.domain.sc.canteenstall.WmCanteenStallLogBO)");
        WmCanteenStallBindDO bindDO = logBO.getBindDOAfter();

        String logInfo = "操作：线索绑定失败\\n" +
                "线索ID：" + bindDO.getWdcClueId() + "\\n" +
                "档口绑定任务ID：" + logBO.getBindId() + "\\n" +
                "[字段变更] 线索绑定状态： " + "绑定中=>绑定失败\\n" +
                "[字段变更] 线索绑定失败原因：空=>" + logBO.getClueBindFailReason() + "\\n";

        // 档口绑定任务操作日志
        insertScOptLog(OptTypeEnum.INSERT.getType(), SC_CANTEEN_STALL_BIND_LOG, logBO.getBindId(),
                logBO.getUserId(), logBO.getUserName(), logInfo, "");

        // 食堂信息操作日志
        insertScOptLog(OptTypeEnum.INSERT.getType(), SC_CANTEEN_LOG, logBO.getCanteenPrimaryId(),
                logBO.getUserId(), logBO.getUserName(), logInfo, "");
    }

    public void recordCanteenStallClueBindSuccessLog(WmCanteenStallLogBO logBO) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_2", "com.sankuai.meituan.waimai.customer.service.sc.WmScLogService.recordCanteenStallClueBindSuccessLog(com.sankuai.meituan.waimai.customer.domain.sc.canteenstall.WmCanteenStallLogBO)");
        WmCanteenStallBindDO bindDO = logBO.getBindDOAfter();
        CanteenStallClueBindStatusEnum bindStatusEnum = CanteenStallClueBindStatusEnum.getByType(bindDO.getClueBindStatus());

        String logInfo = "操作：线索绑定成功（" + logBO.getOperationSource() + ")\\n" +
                "线索ID：" + bindDO.getWdcClueId() + "\\n" +
                "档口绑定任务ID：" + logBO.getBindId() + "\\n" +
                "[字段变更] 线索绑定状态：" + bindStatusEnum.getName() + "=>绑定成功\\n" +
                "[字段变更] 线索绑定失败原因：" + (StringUtils.isBlank(bindDO.getClueBindFailReason()) ? nullShow : bindDO.getClueBindFailReason()) + "=>空\\n";

        // 档口绑定任务操作日志
        insertScOptLog(OptTypeEnum.INSERT.getType(), SC_CANTEEN_STALL_BIND_LOG, logBO.getBindId(),
                logBO.getUserId(), logBO.getUserName(), logInfo, "");

        // 食堂信息操作日志
        insertScOptLog(OptTypeEnum.INSERT.getType(), SC_CANTEEN_LOG, logBO.getCanteenPrimaryId(),
                logBO.getUserId(), logBO.getUserName(), logInfo, "");
    }


    public void recordCanteenStallClueUnBindLog(WmCanteenStallLogBO logBO) {
        WmCanteenStallBindDO bindDO = logBO.getBindDOAfter();
        CanteenStallClueBindStatusEnum bindStatusEnum = CanteenStallClueBindStatusEnum.getByType(bindDO.getClueBindStatus());

        String logInfo = "操作：线索解绑成功（" + logBO.getOperationSource() + ")\\n" +
                "线索ID：" + bindDO.getWdcClueId() + "\\n" +
                "档口绑定任务ID：" + logBO.getBindId() + "\\n" +
                "[字段变更] 线索绑定状态： " + bindStatusEnum.getName() + "=>未绑定\\n";

        // 档口绑定任务操作日志
        insertScOptLog(OptTypeEnum.INSERT.getType(), SC_CANTEEN_STALL_BIND_LOG, logBO.getBindId(),
                logBO.getUserId(), logBO.getUserName(), logInfo, "");

        // 食堂信息操作日志
        insertScOptLog(OptTypeEnum.INSERT.getType(), SC_CANTEEN_LOG, logBO.getCanteenPrimaryId(),
                logBO.getUserId(), logBO.getUserName(), logInfo, "");
    }

    /**
     * 食堂档口绑定任务新增操作日志
     */
    public String composeCanteenStallBindInsertLog(WmCanteenStallBindDO bindDO) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.service.sc.WmScLogService.composeCanteenStallBindInsertLog(com.sankuai.meituan.waimai.customer.domain.sc.canteenstall.WmCanteenStallBindDO)");
        StringBuilder stringBuilder = new StringBuilder();
        canteenStallBindField.forEach((key, value) -> {
            Object trueValue = getFieldValueByFieldName(key, bindDO);
            Object afterValue = transCanteenStallBindSetValueToDesc(key, trueValue);
            if (afterValue == null || StringUtils.isBlank(afterValue.toString())) {
                return;
            }
            stringBuilder.append("[字段变更] ");
            stringBuilder.append(canteenStallBindField.get(key));
            stringBuilder.append(": 空=>");
            stringBuilder.append(afterValue);
            stringBuilder.append("\\n");
        });
        return stringBuilder.toString();
    }


    /**
     * 食堂档口绑定任务更新操作日志
     */
    public String composeCanteenStallBindUpdateLog(WmCanteenStallBindDO bindDOBefore, WmCanteenStallBindDO bindDOAfter) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.service.sc.WmScLogService.composeCanteenStallBindUpdateLog(WmCanteenStallBindDO,WmCanteenStallBindDO)");
        try {
            List<WmCustomerDiffCellBo> diffList = DiffUtil.compare(bindDOBefore, bindDOAfter, canteenStallBindField);
            StringBuilder stringBuilder = new StringBuilder();
            for (WmCustomerDiffCellBo wmCustomerDiffCellBo : diffList) {
                // 更新前的值
                Object preValue = transCanteenStallBindSetValueToDesc(wmCustomerDiffCellBo.getField(), wmCustomerDiffCellBo.getPre());
                if (StringUtils.isEmpty(preValue.toString())) {
                    preValue = nullShow;
                }
                // 更新后的值
                Object afterValue = transCanteenStallBindSetValueToDesc(wmCustomerDiffCellBo.getField(), wmCustomerDiffCellBo.getAft());
                if (StringUtils.isEmpty(afterValue.toString())) {
                    afterValue = nullShow;
                }
                if (preValue.equals(afterValue) && nullShow.equals(preValue)) {
                    continue;
                }
                stringBuilder.append("[字段变更] ");
                stringBuilder.append(canteenStallBindField.get(wmCustomerDiffCellBo.getField()));
                stringBuilder.append(": ").append(preValue);
                stringBuilder.append("=>");
                stringBuilder.append(afterValue);
                stringBuilder.append("\\n");
            }
            return stringBuilder.toString();
        } catch (Exception e) {
            log.error("[WmScLogSchoolInfoService.composeCanteenStallBindUpdateLog] Exception. bindDOBefore = {}, bindDOAfter = {} ",
                    JSONObject.toJSONString(bindDOBefore), JSONObject.toJSONString(bindDOAfter), e);
        }

        return Strings.EMPTY;
    }



    private String composeCanteenStallBatchCreateClueLogWithBind(WmCanteenStallBindDO bindDO) {
        StringBuilder stringBuilder = new StringBuilder();
        canteenStallBindField.forEach((key, value) -> {
            Object trueValue = getFieldValueByFieldName(key, bindDO);
            Object afterValue = transCanteenStallBindSetValueToDesc(key, trueValue);
            if (afterValue == null || StringUtils.isBlank(afterValue.toString())) {
                return;
            }
            stringBuilder.append("[字段变更] ");
            stringBuilder.append(canteenStallBindField.get(key));
            stringBuilder.append(": 空=>");
            stringBuilder.append(afterValue);
            stringBuilder.append("\\n");
        });
        return stringBuilder.toString();
    }

    private String composeCanteenStallBatchCreateClueLogWithClue(WmCanteenStallClueDO clueDO) {
        StringBuilder stringBuilder = new StringBuilder();
        canteenStallClueField.forEach((key, value) -> {
            Object trueValue = getFieldValueByFieldName(key, clueDO);
            Object afterValue = transCanteenStallClueSetValueToDesc(key, trueValue);
            if (afterValue == null || StringUtils.isBlank(afterValue.toString())) {
                return;
            }
            stringBuilder.append("[线索内容] ");
            stringBuilder.append(canteenStallClueField.get(key));
            stringBuilder.append(": ");
            stringBuilder.append(afterValue);
            stringBuilder.append("\\n");
        });
        return stringBuilder.toString();
    }



    private Object transCanteenStallClueSetValueToDesc(String key, Object trueValue) {
        if (trueValue == null) {
            return null;
        }

        try {
            // 外卖城市 / 外卖行政区
            if (CANTEEN_STALL_CLUE_SECOND_CITY_ID.equals(key) || CANTEEN_STALL_CLUE_THIRD_CITY_ID.equals(key)) {
                WmOpenCity wmOpenCity = wmOpenCityServiceAdapter.getCityByCityId(Integer.valueOf(trueValue.toString()));
                return wmOpenCity == null ? nullShow : wmOpenCity.getCityName();
            }

            // 门店电话
            if (CANTEEN_STALL_CLUE_POI_PHONE.equals(key)) {
                KeyDecrypt keyDecrypt = new KeyDecrypt();
                keyDecrypt.setKeyName(KmsKeyNameEnum.PHONE_NO);
                keyDecrypt.setValueForDecrypt(trueValue.toString());
                return keyDecryptHandleService.execute(keyDecrypt);
            }

            // 门店品类
            if (CANTEEN_STALL_CLUE_LEAF_CATE_ID.equals(key)) {
                List<WmPoiCateDic> cateDicList = wmPoiCategoryCacheServiceAdapter.getWmPoiCateDicTreeByCateId(Long.valueOf(trueValue.toString()));
                return WmScTransUtil.transPoiCateDicListToCate(cateDicList);
            }
        } catch (Exception e) {
            log.error("[WmScLogSchoolInfoService.transSchoolGoalSetAssignmentValueToDesc] WmSchCantException. key = {}, value = {}", key, trueValue, e);
        }
        return trueValue;
    }


    private Object transCanteenStallBindSetValueToDesc(String key, Object trueValue) {
        if (trueValue == null) {
            return null;
        }

        // 线索生成状态
        if (CANTEEN_STALL_BIND_CLUE_GENERATE_STATUS.equals(key)) {
            CanteenStallClueGenerateStatusEnum generateStatusEnum = CanteenStallClueGenerateStatusEnum.getByType(Integer.valueOf(trueValue.toString()));
            return generateStatusEnum == null ? nullShow : generateStatusEnum.getName();
        }

        // 线索生成失败原因
        if (CANTEEN_STALL_BIND_CLUE_GENERATE_FAIL_REASON.equals(key) && trueValue.toString().equals("")) {
            return nullShow;
        }

        // 线索绑定状态
        if (CANTEEN_STALL_BIND_CLUE_BIND_STATUS.equals(key)) {
            CanteenStallClueBindStatusEnum bindStatusEnum = CanteenStallClueBindStatusEnum.getByType(Integer.valueOf(trueValue.toString()));
            return bindStatusEnum == null ? nullShow : bindStatusEnum.getName();
        }

        // 线索绑定失败原因
        if (CANTEEN_STALL_BIND_CLUE_BIND_FAIL_REASON.equals(key) && trueValue.toString().equals("")) {
            return nullShow;
        }

        // 外卖门店绑定状态
        if (CANTEEN_STALL_BIND_WM_POI_BIND_STATUS.equals(key)) {
            CanteenStallWmPoiBindStatusEnum bindStatusEnum = CanteenStallWmPoiBindStatusEnum.getByType(Integer.valueOf(trueValue.toString()));
            return bindStatusEnum == null ? nullShow : bindStatusEnum.getName();
        }

        // 外卖门店绑定失败原因
        if (CANTEEN_STALL_BIND_WM_POI_BIND_FAIL_REASON.equals(key) && trueValue.toString().equals("")) {
            return nullShow;
        }

        // 线索跟进状态
        if (CANTEEN_STALL_BIND_CLUE_FOLLOW_UP_STATUS.equals(key)) {
            CanteenStallClueFollowUpStatusEnum followUpStatusEnum = CanteenStallClueFollowUpStatusEnum.getByType(Integer.valueOf(trueValue.toString()));
            return followUpStatusEnum == null ? nullShow : followUpStatusEnum.getName();
        }

        // 线索跟进审批状态
        if (CANTEEN_STALL_BIND_AUDIT_STATUS.equals(key)) {
            CanteenStallAuditStatusEnum auditStatusEnum = CanteenStallAuditStatusEnum.getByType(Integer.valueOf(trueValue.toString()));
            return auditStatusEnum == null ? nullShow : auditStatusEnum.getName();
        }
        return trueValue;
    }

}
