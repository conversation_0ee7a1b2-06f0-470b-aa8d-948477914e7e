package com.sankuai.meituan.waimai.customer.service.sc.thirdworkplace;

import java.util.Arrays;
import java.util.Objects;

public enum ThirdWorkplaceQueryRoleEnum {

    UNKNOWN(0, "未知"),
    CITY(1, "校园大区城市角色"),
    KA(2, "校园大区校企角色"),
    PS(3, "校园聚合配送角色"),
    ADMIN(4, "校园总部角色");



    private Integer code;
    private String desc;

    ThirdWorkplaceQueryRoleEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static ThirdWorkplaceQueryRoleEnum getByCode(Integer code){
        if (Objects.isNull(code)){
            return UNKNOWN;
        }
        return Arrays.stream(ThirdWorkplaceQueryRoleEnum.values()).filter(e -> e.getCode().equals(code)).findFirst().orElse(UNKNOWN);
    }
}
