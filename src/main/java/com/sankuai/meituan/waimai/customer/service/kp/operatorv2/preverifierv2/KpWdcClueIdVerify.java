package com.sankuai.meituan.waimai.customer.service.kp.operatorv2.preverifierv2;

import com.sankuai.meituan.auth.util.CollectionUtils;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerDB;
import com.sankuai.meituan.waimai.customer.service.kp.preverifier.KpDataPreVerify;
import com.sankuai.meituan.waimai.customer.service.kp.preverifier.KpWdcClueIdPreVerify;
import com.sankuai.meituan.waimai.customer.util.ThrowUtil;
import com.sankuai.meituan.waimai.thrift.customer.constant.KpTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.kp.WmCustomerKp;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * 公私海线索ID校验
 */
@Service
public class KpWdcClueIdVerify extends KpPreverify {

    @Autowired
    private KpWdcClueIdPreVerify kpWdcClueIdPreVerify;


    @Override
    public Object verify(WmCustomerDB wmCustomer, List<WmCustomerKp> oldCustomerKpList, WmCustomerKp insertKp, WmCustomerKp updateKp, WmCustomerKp deleteKp, int uid, String uname) throws WmCustomerException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_1", "com.sankuai.meituan.waimai.customer.service.kp.operatorv2.preverifierv2.KpWdcClueIdVerify.verify(WmCustomerDB,List,WmCustomerKp,WmCustomerKp,WmCustomerKp,int,String)");
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_1", "com.sankuai.meituan.waimai.customer.service.kp.operatorv2.preverifierv2.KpWdcClueIdVerify.verify(WmCustomerDB,List,WmCustomerKp,WmCustomerKp,WmCustomerKp,int,String)");
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_1", "com.sankuai.meituan.waimai.customer.service.kp.operatorv2.preverifierv2.KpWdcClueIdVerify.verify(WmCustomerDB,List,WmCustomerKp,WmCustomerKp,WmCustomerKp,int,String)");
        Map<String, List<WmCustomerKp>> transMap = parameterTrans(insertKp, updateKp, deleteKp);
        kpWdcClueIdPreVerify.verify(wmCustomer, oldCustomerKpList, transMap.get("insert"), transMap.get("delete"), transMap.get("update"));
        return new Object();// 暂且返回object，方便后续扩展
    }


}
