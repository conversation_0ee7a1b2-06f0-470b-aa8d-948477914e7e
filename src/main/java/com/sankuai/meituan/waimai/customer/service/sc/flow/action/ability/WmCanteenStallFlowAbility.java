package com.sankuai.meituan.waimai.customer.service.sc.flow.action.ability;

import com.alibaba.fastjson.JSONObject;
import com.sankuai.meituan.waimai.customer.domain.sc.canteenstall.WmCanteenStallAuditTaskBO;
import com.sankuai.meituan.waimai.customer.domain.sc.canteenstall.WmCanteenStallClueBindBO;
import com.sankuai.meituan.waimai.customer.service.sc.flow.constant.WmCanteenStallAuditStatusMachineEvent;
import com.sankuai.meituan.waimai.customer.service.sc.flow.constant.WmCanteenStallClueBindStatusMachineEvent;
import com.sankuai.meituan.waimai.customer.service.sc.flow.entity.WmCanteenStallAuditStatusMachineContext;
import com.sankuai.meituan.waimai.customer.service.sc.flow.entity.WmCanteenStallClueBindStatusMachineContext;
import com.sankuai.meituan.waimai.customer.service.sc.flow.trigger.WmCanteenStallAuditMachineTrigger;
import com.sankuai.meituan.waimai.customer.service.sc.flow.trigger.WmCanteenStallClueBindMachineTrigger;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class WmCanteenStallFlowAbility {

    @Autowired
    private WmCanteenStallAuditMachineTrigger wmCanteenStallAuditStatusMachineTrigger;

    @Autowired
    private WmCanteenStallClueBindMachineTrigger wmCanteenStallClueBindMachineTrigger;


    /**
     * 线索绑定成功
     * 状态流程： 未绑定(0)/绑定中(10)/绑定失败(20) -> 绑定成功(30)
     */
    public void bindClueSuccess(WmCanteenStallClueBindBO clueBindBO) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.service.sc.flow.action.ability.WmCanteenStallFlowAbility.bindClueSuccess(com.sankuai.meituan.waimai.customer.domain.sc.canteenstall.WmCanteenStallClueBindBO)");
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.service.sc.flow.action.ability.WmCanteenStallFlowAbility.bindClueSuccess(com.sankuai.meituan.waimai.customer.domain.sc.canteenstall.WmCanteenStallClueBindBO)");
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.service.sc.flow.action.ability.WmCanteenStallFlowAbility.bindClueSuccess(com.sankuai.meituan.waimai.customer.domain.sc.canteenstall.WmCanteenStallClueBindBO)");
        log.info("[WmCanteenStallFlowAbility.clueBindSuccess] clueBindBO = {}", JSONObject.toJSONString(clueBindBO));
        WmCanteenStallClueBindStatusMachineContext context = new WmCanteenStallClueBindStatusMachineContext();
        context.setClueBindBO(clueBindBO);
        wmCanteenStallClueBindMachineTrigger.triggerCanteenStallClueBindStateMachine(
                WmCanteenStallClueBindStatusMachineEvent.CLUE_BIND_SUCCESS,
                context
        );
    }

    /**
     * 线索绑定失败
     * 状态流程： 绑定中(10) -> 绑定失败(20)
     */
    public void bindClueFail(WmCanteenStallClueBindBO clueBindBO) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.sc.flow.action.ability.WmCanteenStallFlowAbility.bindClueFail(com.sankuai.meituan.waimai.customer.domain.sc.canteenstall.WmCanteenStallClueBindBO)");
        log.info("[WmCanteenStallFlowAbility.clueBindFail] clueBindBO = {}", JSONObject.toJSONString(clueBindBO));
        WmCanteenStallClueBindStatusMachineContext context = new WmCanteenStallClueBindStatusMachineContext();
        context.setClueBindBO(clueBindBO);
        wmCanteenStallClueBindMachineTrigger.triggerCanteenStallClueBindStateMachine(
                WmCanteenStallClueBindStatusMachineEvent.CLUE_BIND_FAIL,
                context
        );
    }

    /**
     * 线索解绑
     * 状态流程：绑定中(10)/绑定成功(30) -> 未绑定(0)
     */
    public void unBindClue(WmCanteenStallClueBindBO clueBindBO) {
        log.info("[WmCanteenStallFlowAbility.unBindClue] clueBindBO = {}", JSONObject.toJSONString(clueBindBO));
        WmCanteenStallClueBindStatusMachineContext context = new WmCanteenStallClueBindStatusMachineContext();
        context.setClueBindBO(clueBindBO);
        wmCanteenStallClueBindMachineTrigger.triggerCanteenStallClueBindStateMachine(
                WmCanteenStallClueBindStatusMachineEvent.CLUE_UNBIND,
                context
        );
    }


    /**
     * 食堂档口线索审批-提交审批
     * 状态流转: 待提审/审批通过/审批驳回 -> 审批中
     */
    public void submitAuditTask(WmCanteenStallAuditTaskBO auditTaskBO) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.service.sc.flow.action.ability.WmCanteenStallFlowAbility.submitAuditTask(com.sankuai.meituan.waimai.customer.domain.sc.canteenstall.WmCanteenStallAuditTaskBO)");
        log.info("[WmCanteenStallFlowAbility.submitAuditTask] auditTaskBO = {}", JSONObject.toJSONString(auditTaskBO));
        WmCanteenStallAuditStatusMachineContext context = new WmCanteenStallAuditStatusMachineContext();
        context.setAuditTaskBO(auditTaskBO);
        wmCanteenStallAuditStatusMachineTrigger.triggerCanteenStallAuditStateMachine(
                WmCanteenStallAuditStatusMachineEvent.SUBMIT,
                context
        );
    }

    /**
     * 食堂档口线索审批-审批通过
     */
    public void passAuditTask(WmCanteenStallAuditTaskBO auditTaskBO) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_2", "com.sankuai.meituan.waimai.customer.service.sc.flow.action.ability.WmCanteenStallFlowAbility.passAuditTask(com.sankuai.meituan.waimai.customer.domain.sc.canteenstall.WmCanteenStallAuditTaskBO)");
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_2", "com.sankuai.meituan.waimai.customer.service.sc.flow.action.ability.WmCanteenStallFlowAbility.passAuditTask(com.sankuai.meituan.waimai.customer.domain.sc.canteenstall.WmCanteenStallAuditTaskBO)");
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_2", "com.sankuai.meituan.waimai.customer.service.sc.flow.action.ability.WmCanteenStallFlowAbility.passAuditTask(com.sankuai.meituan.waimai.customer.domain.sc.canteenstall.WmCanteenStallAuditTaskBO)");
        log.info("[WmCanteenStallFlowAbility.passAuditTask] auditTaskBO = {}", JSONObject.toJSONString(auditTaskBO));
        WmCanteenStallAuditStatusMachineContext context = new WmCanteenStallAuditStatusMachineContext();
        context.setAuditTaskBO(auditTaskBO);
        wmCanteenStallAuditStatusMachineTrigger.triggerCanteenStallAuditStateMachine(
                WmCanteenStallAuditStatusMachineEvent.CRM_TICKET_PASS,
                context
        );
    }

    /**
     * 食堂档口线索审批-审批驳回
     */
    public void rejectAuditTask(WmCanteenStallAuditTaskBO auditTaskBO) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.sc.flow.action.ability.WmCanteenStallFlowAbility.rejectAuditTask(com.sankuai.meituan.waimai.customer.domain.sc.canteenstall.WmCanteenStallAuditTaskBO)");
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.sc.flow.action.ability.WmCanteenStallFlowAbility.rejectAuditTask(com.sankuai.meituan.waimai.customer.domain.sc.canteenstall.WmCanteenStallAuditTaskBO)");
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.sc.flow.action.ability.WmCanteenStallFlowAbility.rejectAuditTask(com.sankuai.meituan.waimai.customer.domain.sc.canteenstall.WmCanteenStallAuditTaskBO)");
        log.info("[WmCanteenStallFlowAbility.rejectAuditTask] auditTaskBO = {}", JSONObject.toJSONString(auditTaskBO));
        WmCanteenStallAuditStatusMachineContext context = new WmCanteenStallAuditStatusMachineContext();
        context.setAuditTaskBO(auditTaskBO);
        wmCanteenStallAuditStatusMachineTrigger.triggerCanteenStallAuditStateMachine(
                WmCanteenStallAuditStatusMachineEvent.CRM_TICKET_REJECT,
                context
        );
    }

    /**
     * 食堂档口线索审批-审批终止
     */
    public void stopAuditTask(WmCanteenStallAuditTaskBO auditTaskBO) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.sc.flow.action.ability.WmCanteenStallFlowAbility.stopAuditTask(com.sankuai.meituan.waimai.customer.domain.sc.canteenstall.WmCanteenStallAuditTaskBO)");
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.sc.flow.action.ability.WmCanteenStallFlowAbility.stopAuditTask(com.sankuai.meituan.waimai.customer.domain.sc.canteenstall.WmCanteenStallAuditTaskBO)");
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.sc.flow.action.ability.WmCanteenStallFlowAbility.stopAuditTask(com.sankuai.meituan.waimai.customer.domain.sc.canteenstall.WmCanteenStallAuditTaskBO)");
        log.info("[WmCanteenStallFlowAbility.stopAuditTask] auditTaskBO = {}", JSONObject.toJSONString(auditTaskBO));
        WmCanteenStallAuditStatusMachineContext context = new WmCanteenStallAuditStatusMachineContext();
        context.setAuditTaskBO(auditTaskBO);
        wmCanteenStallAuditStatusMachineTrigger.triggerCanteenStallAuditStateMachine(
                WmCanteenStallAuditStatusMachineEvent.CRM_TICKET_STOP,
                context
        );
    }

}
