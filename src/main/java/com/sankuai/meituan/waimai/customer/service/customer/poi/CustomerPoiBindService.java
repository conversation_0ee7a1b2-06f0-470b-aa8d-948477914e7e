package com.sankuai.meituan.waimai.customer.service.customer.poi;

import com.alibaba.fastjson.JSONObject;
import com.cip.crane.netty.utils.SleepUtils;
import com.dianping.cat.Cat;
import com.dianping.cat.util.MetricHelper;
import com.dianping.lion.client.util.CollectionUtils;
import com.google.common.base.Preconditions;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.sankuai.meituan.waimai.channel.beacon.lib.enums.CustomerModuleStateEnum;
import com.sankuai.meituan.waimai.customer.adapter.StateCenterAdapter;
import com.sankuai.meituan.waimai.customer.adapter.WmContractServiceAdaptor;
import com.sankuai.meituan.waimai.customer.adapter.WmPoiOplogThriftServiceAdaptor;
import com.sankuai.meituan.waimai.customer.constant.WmCustomerConstant;
import com.sankuai.meituan.waimai.customer.constant.customer.CustomerMetricEnum;
import com.sankuai.meituan.waimai.customer.constant.customer.CustomerPoiBindDecideResultEnum;
import com.sankuai.meituan.waimai.customer.constant.customer.CustomerPoiBindTypeEnum;
import com.sankuai.meituan.waimai.customer.constant.customer.WmCustomerPoiOplogSourceTypeEnum;
import com.sankuai.meituan.waimai.customer.contract.service.impl.WmContractPoiProduceService;
import com.sankuai.meituan.waimai.customer.dao.WmCustomerPoiSmsRecordMapper;
import com.sankuai.meituan.waimai.customer.ddd.adapter.WmPoiQueryAdapter;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerDB;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerPoiOplogDB;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerPoiSmsRecordDB;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerTaskQueryBO;
import com.sankuai.meituan.waimai.customer.domain.cuspoirel.bind.CustomerPoiBindFlowContext;
import com.sankuai.meituan.waimai.customer.domain.poi.*;
import com.sankuai.meituan.waimai.customer.service.customer.*;
import com.sankuai.meituan.waimai.customer.service.customer.check.ICustomerPoiValidator;
import com.sankuai.meituan.waimai.customer.service.customer.cuspoirel.rule.CusPoiRelBaseStrategy;
import com.sankuai.meituan.waimai.customer.service.customer.cuspoirel.strategy.check.IBindCheckStrategy;
import com.sankuai.meituan.waimai.customer.service.customer.task.CustomerTaskService;
import com.sankuai.meituan.waimai.customer.service.sign.WmEcontractSignBzService;
import com.sankuai.meituan.waimai.customer.util.AppContext;
import com.sankuai.meituan.waimai.thrift.constants.WmPoiFieldQueryConstant;
import com.sankuai.meituan.waimai.thrift.customer.constant.*;
import com.sankuai.meituan.waimai.thrift.customer.constant.customer.task.*;
import com.sankuai.meituan.waimai.thrift.customer.domain.WmCustomerOplogBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.base.LongResult;
import com.sankuai.meituan.waimai.thrift.customer.domain.mq.CustomerMQEventEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractFoodcityPoiTableBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractTaskApplyBo;
import com.sankuai.meituan.waimai.thrift.domain.WmPoiAggre;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import com.sankuai.meituan.waimai.thrift.util.ConfigUtilAdapter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
@Service
public class CustomerPoiBindService {

    @Autowired
    private List<ICustomerPoiValidator> customerPoiValidatorList;

    @Autowired
    private WmCustomerService wmCustomerService;

    @Autowired
    private CustomerTaskService customerTaskService;

    @Autowired
    private WmCustomerPoiRelService wmCustomerPoiRelService;

    @Autowired
    private WmEcontractSignBzService wmEcontractSignBzService;

    @Autowired
    private WmCustomerPoiSmsRecordMapper wmCustomerPoiSmsRecordMapper;

    @Autowired
    private WmPoiQueryAdapter wmPoiQueryAdapter;

    @Autowired
    private WmCustomerNewSettleService wmCustomerNewSettleService;

    @Autowired
    private WmCustomerPoiAttributeService wmCustomerPoiAttributeService;

    @Autowired
    private WmCustomerPoiOplogService wmCustomerPoiOplogService;

    @Autowired
    private WmContractPoiProduceService wmContractPoiProduceService;

    @Autowired
    private StateCenterAdapter stateCenterAdapter;

    @Autowired
    private WmCustomerMSCLabelService wmCustomerMSCLabelService;

    @Autowired
    private WmCustomerPoiListEsService wmCustomerPoiListEsService;

    @Autowired
    private WmPoiOplogThriftServiceAdaptor wmPoiOplogThriftServiceAdaptor;

    @Autowired
    private WmContractServiceAdaptor wmContractServiceAdaptor;

    private static final String CALL_INTERVAL_TIME = "call_interval_time";

    @Autowired
    private CusPoiRelBaseStrategy cusPoiRelBaseStrategy;


    /**
     * 客户门店绑定预校验
     *
     * @param customerId
     * @param wmPoiId
     * @param typeEnum
     * @param sourceTypeEnum
     * @throws WmCustomerException
     * @throws TException
     */
    public void checkBind(int customerId, long wmPoiId, CustomerPoiBindTypeEnum typeEnum, CustomerTaskSourceEnum sourceTypeEnum) throws WmCustomerException, TException {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.meituan.waimai.customer.service.customer.poi.CustomerPoiBindService.checkBind(int,long,CustomerPoiBindTypeEnum,CustomerTaskSourceEnum)");
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_2", "com.sankuai.meituan.waimai.customer.service.customer.poi.CustomerPoiBindService.checkBind(int,long,CustomerPoiBindTypeEnum,CustomerTaskSourceEnum)");
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_2", "com.sankuai.meituan.waimai.customer.service.customer.poi.CustomerPoiBindService.checkBind(int,long,CustomerPoiBindTypeEnum,CustomerTaskSourceEnum)");
        log.info("checkBind,customerId={},wmPoiId={},typeEnum={}", customerId, wmPoiId, JSONObject.toJSONString(typeEnum));
        // 获取客户信息
        WmCustomerDB wmCustomerDB = wmCustomerService.selectCustomerById(customerId);
        // 客户完备性校验
        checkCustomerInfo(wmCustomerDB);
        // 业务校验
        checkPoiBind(wmCustomerDB.getCustomerRealType(),WmCustomerPoiCheckBo.builder().
                wmCustomerDB(wmCustomerDB).
                wmPoiIdList(Lists.newArrayList(wmPoiId)).
                checkPoiVersion(false).
                typeEnum(typeEnum).
                sourceTypeEnum(sourceTypeEnum).
                build());
//        checkPoiBind(wmCustomerDB.getCustomerRealType(), new WmCustomerPoiCheckBo(wmCustomerDB, Lists.newArrayList(wmPoiId), false, typeEnum, sourceTypeEnum));
    }

    /**
     * 校验客户是否允许绑定门店，使用新流程策略
     *
     * @param customerId
     * @param wmPoiId
     * @throws WmCustomerException
     * @throws TException
     */
    public void checkBindByStrategy(int customerId, long wmPoiId) throws WmCustomerException, TException {
        // 获取客户信息
        WmCustomerDB wmCustomerDB = wmCustomerService.selectCustomerById(customerId);
        // 客户完备性校验
        checkCustomerInfo(wmCustomerDB);
        //根据客户类型获取绑定校验规则
        IBindCheckStrategy bindCheckStrategy = cusPoiRelBaseStrategy.getBindCheckStrategy(wmCustomerDB.getCustomerRealType());
        CustomerPoiBindFlowContext context = CustomerPoiBindFlowContext.builder()
                .wmCustomerDB(wmCustomerDB)
                .customerId(customerId)
                .wmPoiIdSet(Sets.newHashSet(wmPoiId))
                .opSource(CustomerTaskSourceEnum.POI_BASE_INFO.getCode())
                .opSourceDetail(CustomerTaskDetailSourceEnum.POI_BASE.getDesc())
                .opSysName(CustomerTaskOpSystemEnum.POI_BASE_SYS.getDesc())
                .checkPoiVersion(false)
                .opUid(0)
                .build();
        //校验
        bindCheckStrategy.checkBindByParams(context);
    }


    /**
     * 确认绑定
     *
     * @param customerId
     * @param wmPoiIdSet
     * @param smsId
     * @throws WmCustomerException
     */
    public void confirmBind(Integer customerId, Set<Long> wmPoiIdSet, Integer smsId) throws WmCustomerException {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.service.customer.poi.CustomerPoiBindService.confirmBind(java.lang.Integer,java.util.Set,java.lang.Integer)");
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.service.customer.poi.CustomerPoiBindService.confirmBind(java.lang.Integer,java.util.Set,java.lang.Integer)");
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.service.customer.poi.CustomerPoiBindService.confirmBind(java.lang.Integer,java.util.Set,java.lang.Integer)");
        log.info("confirmBind,customerId={},wmPoiIdSet={},smsId={}", customerId, JSONObject.toJSONString(wmPoiIdSet), smsId);
        // 获取客户信息
        WmCustomerDB wmCustomerDB = wmCustomerService.selectCustomerById(customerId);
        // 客户完备性校验
        checkCustomerInfo(wmCustomerDB);
        // 获取客户任务
        WmCustomerTaskQueryBO customerTaskQueryBo = new WmCustomerTaskQueryBO();
        customerTaskQueryBo.setTaskType(CustomerTaskTypeEnum.CUSTOMER_BIND_POI.getCode());
        customerTaskQueryBo.setCustomerId(customerId);
        customerTaskQueryBo.setWmPoiIds(Lists.newArrayList(wmPoiIdSet));
        customerTaskQueryBo.setSignTaskId(smsId);
        customerTaskQueryBo.setBizTaskId(0);
        customerTaskQueryBo.setStatus(CustomerTaskStatusEnum.DOING.getCode());
        Map<Long, Integer> poiAndTaskMaps = customerTaskService.getTaskWmPoiMapByCondition(customerTaskQueryBo);
        // 执行确认绑定
        bindCustomerPoi(new WmCustomerPoiBindBo(
                wmCustomerDB, wmPoiIdSet, "",
                0, "签约回调",
                wmPoiIdSet,
                poiAndTaskMaps,
                CustomerPoiBindTypeEnum.CONFIRM_BIND,
                WmCustomerPoiOplogSourceTypeEnum.PRE_BIND
        ));
        // 记录操作日志
        wmCustomerService.insertCustomerOpLog(customerId, 0, "商家处理", WmCustomerOplogBo.OpType.CHANGESTATUS,
                String.format(CustomerConstants.CUSTOMER_LOG_TEMPLATE_CONFIRM_PRE_BIND, StringUtils.join(wmPoiIdSet, CustomerConstants.SPLIT_SYMBOL)), "");

    }

    /**
     * 取消绑定
     *
     * @param customerId
     * @param wmPoiIdSet
     * @param taskStatus
     * @throws WmCustomerException
     */
    public void cancelBind(Integer customerId, Set<Long> wmPoiIdSet, CustomerTaskStatusEnum taskStatus) throws WmCustomerException {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.meituan.waimai.customer.service.customer.poi.CustomerPoiBindService.cancelBind(Integer,Set,CustomerTaskStatusEnum)");
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_2", "com.sankuai.meituan.waimai.customer.service.customer.poi.CustomerPoiBindService.cancelBind(Integer,Set,CustomerTaskStatusEnum)");
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_2", "com.sankuai.meituan.waimai.customer.service.customer.poi.CustomerPoiBindService.cancelBind(Integer,Set,CustomerTaskStatusEnum)");
        log.info("cancelBind,customerId={},wmPoiIdSet={},taskStatus={}", customerId, JSONObject.toJSONString(wmPoiIdSet), JSONObject.toJSONString(taskStatus));
        wmCustomerPoiRelService.cancelPreBind(wmPoiIdSet, 0L, customerId);
        String userName = CustomerConstants.CUSTOMER_LOG_OP_NAME_MERCHANT;
        String logMsg = CustomerConstants.CUSTOMER_LOG_TEMPLATE_CANCEL_PRE_BIND_BY_MERCHANT;
        if (taskStatus == CustomerTaskStatusEnum.CANCEL) {
            userName = CustomerConstants.CUSTOMER_LOG_OP_NAME_BD;
            logMsg = CustomerConstants.CUSTOMER_LOG_TEMPLATE_CANCEL_PRE_BIND;
        }
        // 发送MQ
        wmCustomerService.sendCustomerStatusNoticeMQ(customerId, CustomerMQEventEnum.CUSTOMER_PRE_BIND_POI_FAIL,
                wmPoiIdSet, 0, userName);
        //同步任务状态
        customerTaskService.updateStatusByCustomerIdAndPoiIdAndType(customerId, wmPoiIdSet, 0L,
                CustomerTaskTypeEnum.CUSTOMER_BIND_POI.getCode(), taskStatus.getCode());

        // 记录操作日志
        wmCustomerService.insertCustomerOpLog(customerId, 0, userName, WmCustomerOplogBo.OpType.CHANGESTATUS,
                String.format(logMsg, StringUtils.join(wmPoiIdSet, ",")));
    }

    /**
     * 客户门店绑定/重新发起绑定
     *
     * @param bindBo
     * @throws WmCustomerException
     * @throws TException
     */
    public boolean bindOrRestart(WmCustomerPoiBindParamBo bindBo) throws WmCustomerException, TException {
        log.info("bindOrRestart,bindBo={}", JSONObject.toJSONString(bindBo));
        // 基础参数校验
        checkBindParam(bindBo);
        // 获取客户信息
        WmCustomerDB wmCustomerDB = wmCustomerService.selectCustomerById(bindBo.getCustomerId());
        // 客户完备性校验
        checkCustomerInfo(wmCustomerDB);
        // 业务校验
        Map<CustomerPoiBindDecideResultEnum, List<Long>> checkMap = checkPoiBind(wmCustomerDB.getCustomerRealType(), WmCustomerPoiCheckBo.builder().
                wmCustomerDB(wmCustomerDB).
                wmPoiIdList(bindBo.getWmPoiIdSet().stream().collect(Collectors.toList())).
                checkPoiVersion(bindBo.isCheckPoiVersion()).
                typeEnum(bindBo.getTypeEnum()).
                sourceTypeEnum(bindBo.getSourceTypeEnum()).
                opUid(bindBo.getOpUid()).
                opSystem(bindBo.getCustomerOperateBO()!=null?bindBo.getCustomerOperateBO().getOpSystem():"").
                opDetailSource(bindBo.getCustomerOperateBO()!=null?bindBo.getCustomerOperateBO().getOpDetailSource():"").
                build()
        );


//        Map<CustomerPoiBindDecideResultEnum, List<Long>> checkMap = checkPoiBind(wmCustomerDB.getCustomerRealType(), new WmCustomerPoiCheckBo(
//                wmCustomerDB, Lists.newArrayList(bindBo.getWmPoiIdSet()), bindBo.isCheckPoiVersion(), bindBo.getTypeEnum(), bindBo.getSourceTypeEnum(),
//                bindBo.getOpUid()
//        ));
        if (checkMap == null || checkMap.isEmpty()) {
            log.error("bindOrRestart checkMap is Empty bindBo={}", JSONObject.toJSONString(bindBo));
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_BINDING_ERROR, "校验服务异常，请稍后重试");
        }
        //参数非空则创建客户任务
        Map<Long, Integer> poiAndTaskMaps = customerTaskService.batchAddCustomerBindTask(wmCustomerDB.getId(), bindBo.getWmPoiIdSet(), bindBo.getCustomerOperateBO());

        // 需要KP确认环节
        List<Long> needSignWmPoiIdList = checkMap.get(CustomerPoiBindDecideResultEnum.NEED_SIGN_TO_BIND);
        if (CollectionUtils.isNotEmpty(needSignWmPoiIdList)) {
            preBindCustomerPoiAndApplyConfirm(new WmCustomerPoiBindToSignBo(wmCustomerDB,
                    Sets.newHashSet(needSignWmPoiIdList), bindBo.getOpUid(), bindBo.getOpName(), poiAndTaskMaps, bindBo.getTypeEnum()));
        }

        // 直接绑定
        List<Long> bindWmPoiIdList = checkMap.get(CustomerPoiBindDecideResultEnum.CAN_BIND_DIRECT);
        List<Long> bindNoMscLabelWmPoiIdList = checkMap.get(CustomerPoiBindDecideResultEnum.CAN_BIND_NO_MSC_LABEL);
        if (CollectionUtils.isNotEmpty(bindWmPoiIdList) || CollectionUtils.isNotEmpty(bindNoMscLabelWmPoiIdList)) {
            Set<Long> wmPoiIdSet = Sets.newHashSet();
            Set<Long> needAddMscLableSet = Sets.newHashSet();

            if (CollectionUtils.isNotEmpty(bindWmPoiIdList)) {
                wmPoiIdSet.addAll(bindWmPoiIdList);
                needAddMscLableSet.addAll(bindWmPoiIdList);
            }
            if (CollectionUtils.isNotEmpty(bindNoMscLabelWmPoiIdList)) {
                wmPoiIdSet.addAll(bindNoMscLabelWmPoiIdList);
            }
            // 获取门店操作记录来源
            WmCustomerPoiOplogSourceTypeEnum oplogSourceTypeEnum = wmCustomerPoiOplogService.getOpSourceType(bindBo.getSourceTypeEnum(),bindBo.getCustomerOperateBO());
            bindCustomerPoi(new WmCustomerPoiBindBo(
                    wmCustomerDB, wmPoiIdSet, bindBo.getRemark(), bindBo.getOpUid(), bindBo.getOpName(),
                    needAddMscLableSet, poiAndTaskMaps, bindBo.getTypeEnum(),
                    oplogSourceTypeEnum
            ));
        }
        return true;
    }

    /**
     * 获取对应的校验器服务
     *
     * @param customerRealType
     * @return
     * @throws WmCustomerException
     */
    private ICustomerPoiValidator getBindCheckService(int customerRealType) throws WmCustomerException {
        for (ICustomerPoiValidator customerPoiValidator : customerPoiValidatorList) {
            if (customerPoiValidator.canCheck(customerRealType)) {
                return customerPoiValidator;
            }
        }
        throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "未找到客户门店绑定校验服务");
    }

    /**
     * 客户门店绑定校验
     *
     * @param customerRealType
     * @param wmCustomerPoiCheckBo
     * @throws WmCustomerException
     * @throws TException
     */
    private Map<CustomerPoiBindDecideResultEnum, List<Long>> checkPoiBind(int customerRealType, WmCustomerPoiCheckBo wmCustomerPoiCheckBo) throws WmCustomerException, TException {
        String status = WmCustomerConstant.SUCCESS;
        String reason = "";
        String source = wmCustomerPoiCheckBo.getTypeEnum().getDesc();
        try {
            // 业务校验
            ICustomerPoiValidator customerPoiValidator = getBindCheckService(customerRealType);
            Map<CustomerPoiBindDecideResultEnum, List<Long>> map = customerPoiValidator.validBind(wmCustomerPoiCheckBo);
            log.info("checkPoiBind customerRealType={},wmCustomerPoiCheckBo={},map={}", customerRealType, JSONObject.toJSONString(wmCustomerPoiCheckBo),
                    JSONObject.toJSONString(map));
            return map;
        } catch (WmCustomerException e) {
            log.warn("checkPoiBind unpass customerRealType={},wmCustomerPoiCheckBo={}", customerRealType, JSONObject.toJSONString(wmCustomerPoiCheckBo), e);
            status = WmCustomerConstant.BUSINESS_EXCEPTION;
            reason = e.getMsg();
            throw e;
        } catch (Exception e) {
            log.error("checkPoiBind error customerRealType={},wmCustomerPoiCheckBo={}", customerRealType, JSONObject.toJSONString(wmCustomerPoiCheckBo), e);
            Cat.logError(String.format("%s error", CustomerMetricEnum.CUSTOMER_POI_VALID.getName()), e);
            status = WmCustomerConstant.SYSTEM_EXCEPTION;
            reason = WmCustomerConstant.SYSTEM_EXCEPTION_MSG;
            throw e;
        } finally {
            Cat.logEvent(CustomerMetricEnum.CUSTOMER_POI_VALID.getName(), source, status, reason);
            MetricHelper.build().name(CustomerMetricEnum.CUSTOMER_POI_VALID.getName()).tag(CustomerMetricEnum.CUSTOMER_POI_VALID.getTag(), source)
                    .tag(CustomerMetricEnum.CUSTOMER_POI_VALID.getStatus(), status).count();
        }
    }

    /**
     * 门店绑定入参校验
     *
     * @param bindBo
     * @throws WmCustomerException
     */
    private void checkBindParam(WmCustomerPoiBindParamBo bindBo) throws WmCustomerException {
        try {
            Preconditions.checkNotNull(bindBo);
            Preconditions.checkArgument(bindBo.getCustomerId() > 0);
            Preconditions.checkArgument(CollectionUtils.isNotEmpty(bindBo.getWmPoiIdSet()));
            Preconditions.checkNotNull(bindBo.getSourceTypeEnum());
            Preconditions.checkNotNull(bindBo.getTypeEnum());
        } catch (Exception e) {
            log.error("门店绑定参数错误 bindBo={}", JSONObject.toJSONString(bindBo), e);
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "参数错误");
        }
    }

    /**
     * 客户信息完备性校验
     *
     * @param wmCustomerDB
     * @throws WmCustomerException
     */
    private void checkCustomerInfo(WmCustomerDB wmCustomerDB) throws WmCustomerException {
        if (wmCustomerDB == null) {
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "客户ID无法查到有效数据");
        }
        if (wmCustomerDB.getIsLeaf() == CustomerConstants.CUSTOMER_IS_LEAF_NO) {
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "不允许绑定到上级客户");
        }
    }

    /**
     * 门店预绑定客户-发起确认
     *
     * @param wmCustomerPoiBindToSignBo
     * @throws WmCustomerException
     */
    private void preBindCustomerPoiAndApplyConfirm(WmCustomerPoiBindToSignBo wmCustomerPoiBindToSignBo) throws WmCustomerException {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.meituan.waimai.customer.service.customer.poi.CustomerPoiBindService.preBindCustomerPoiAndApplyConfirm(com.sankuai.meituan.waimai.customer.domain.poi.WmCustomerPoiBindToSignBo)");
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_1", "com.sankuai.meituan.waimai.customer.service.customer.poi.CustomerPoiBindService.preBindCustomerPoiAndApplyConfirm(com.sankuai.meituan.waimai.customer.domain.poi.WmCustomerPoiBindToSignBo)");
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_1", "com.sankuai.meituan.waimai.customer.service.customer.poi.CustomerPoiBindService.preBindCustomerPoiAndApplyConfirm(com.sankuai.meituan.waimai.customer.domain.poi.WmCustomerPoiBindToSignBo)");
        WmCustomerDB wmCustomerDB = wmCustomerPoiBindToSignBo.getWmCustomerDB();
        Set<Long> wmPoiIdSet = wmCustomerPoiBindToSignBo.getWmPoiIdSet();
        Integer opUid = wmCustomerPoiBindToSignBo.getOpUid();
        String opName = wmCustomerPoiBindToSignBo.getOpName();
        Map<Long, Integer> poiAndTaskMaps = wmCustomerPoiBindToSignBo.getPoiAndTaskMaps();
        log.info("#preBindCustomerPoiAndApplyConfirm,wmCustomerPoiBindToSignBo={}", JSONObject.toJSONString(wmCustomerPoiBindToSignBo));
        Integer customerId = wmCustomerDB.getId();
        CustomerPoiBindTypeEnum customerPoiBindTypeEnum = wmCustomerPoiBindToSignBo.getCustomerPoiBindTypeEnum();
        // 初始化门店预绑定状态
        operateCustomerPoiRelForSigner(customerPoiBindTypeEnum, poiAndTaskMaps, wmPoiIdSet, customerId);
        Integer signTaskId = null;
        String status = WmCustomerConstant.SUCCESS;
        String reason = "";
        try {
            EcontractTaskApplyBo econtractTaskApplyBo = new EcontractTaskApplyBo();
            econtractTaskApplyBo.setBizId(customerId.toString());
            econtractTaskApplyBo.setBizTypeEnum(EcontractTaskApplyBizTypeEnum.CUSTOMER_ID);
            econtractTaskApplyBo.setApplyTypeEnum(EcontractTaskApplyTypeEnum.FOODCITY_POI_TABLE);
            EcontractFoodcityPoiTableBo econtractFoodcityPoiTableBo = new EcontractFoodcityPoiTableBo();
            econtractFoodcityPoiTableBo.setPartAName(wmCustomerDB.getCustomerName());
            Map<String, Long> wmPoiInfo = Maps.newHashMap();
            List<WmPoiAggre> wmPoiAggreList = wmPoiQueryAdapter.getWmPoiAggre(Lists.newArrayList(wmPoiIdSet),
                    Sets.newHashSet(WmPoiFieldQueryConstant.WM_POI_FIELD_WM_POI_ID, WmPoiFieldQueryConstant.WM_POI_FIELD_NAME));
            for (WmPoiAggre wmPoiAggre : wmPoiAggreList) {
                wmPoiInfo.put(wmPoiAggre.getName(), wmPoiAggre.getWm_poi_id());
            }
            econtractFoodcityPoiTableBo.setPoiTableName(wmPoiInfo);
            econtractFoodcityPoiTableBo.setPartAOfficialSeal(wmCustomerDB.getCustomerName());
            econtractFoodcityPoiTableBo.setPartBOfficialSeal("北京三快在线科技有限公司");
            econtractFoodcityPoiTableBo.setWmPoiIdList(Lists.newArrayList(wmPoiIdSet));
            econtractTaskApplyBo.setApplyInfoBo(JSONObject.toJSONString(econtractFoodcityPoiTableBo));
            econtractTaskApplyBo.setCommitUid(opUid);
            // 发送短信
            LongResult result = wmEcontractSignBzService.applyTask(econtractTaskApplyBo);
            // 记录短信记录
            WmCustomerPoiSmsRecordDB wmCustomerPoiSmsRecordDB = new WmCustomerPoiSmsRecordDB();
            wmCustomerPoiSmsRecordDB.setCustomerId(customerId);
            wmCustomerPoiSmsRecordDB.setTaskId(result.getValue());
            wmCustomerPoiSmsRecordDB.setWmPoiIds(StringUtils.join(wmPoiIdSet, CustomerConstants.SPLIT_SYMBOL));
            wmCustomerPoiSmsRecordDB.setTaskStatus(EcontractTaskStateEnum.TO_COMMIT.getType());
            wmCustomerPoiSmsRecordDB.setValid(CustomerConstants.VALID);
            wmCustomerPoiSmsRecordDB.setType((byte) 1);
            wmCustomerPoiSmsRecordMapper.insertSmsRecord(wmCustomerPoiSmsRecordDB);
            wmCustomerPoiRelService.applyPreBind(wmPoiIdSet, 0L, customerId, poiAndTaskMaps);
            signTaskId = wmCustomerPoiSmsRecordDB.getId();
            //更新任务-签约任务ID
            if (signTaskId != null && signTaskId > 0 && !CollectionUtils.isEmpty(poiAndTaskMaps)) {
                List<Integer> taskIds = Lists.newArrayList(poiAndTaskMaps.values());
                customerTaskService.updateTaskSignTaskId(customerId, taskIds, signTaskId);
            }
            wmCustomerService.insertCustomerOpLog(customerId, opUid, opName, WmCustomerOplogBo.OpType.CHANGESTATUS,
                    String.format(CustomerConstants.CUSTOMER_LOG_TEMPLATE_APPLY_PRE_BIND, StringUtils.join(wmPoiIdSet, CustomerConstants.SPLIT_SYMBOL)), "");

            // 发送MQ
            wmCustomerService.sendCustomerStatusNoticeMQ(customerId, CustomerMQEventEnum.CUSTOMER_PRE_BIND_POI_ING,
                    wmPoiIdSet, opUid, opName);
        } catch (Exception e) {
            log.error("发送客户门店预绑定确认短信异常 wmCustomerPoiBindToSignBo={}", JSONObject.toJSONString(wmCustomerPoiBindToSignBo), e);
            status = WmCustomerConstant.SYSTEM_EXCEPTION;
            reason = WmCustomerConstant.SYSTEM_EXCEPTION_MSG;
            Cat.logError(String.format("%s error", CustomerMetricEnum.CUSTOMER_POI_BIND_START_SIGN.getName()), e);
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "发送客户门店预绑定确认短信异常");
        } finally {
            String sourceDesc = customerPoiBindTypeEnum.getDesc();
            Cat.logEvent(CustomerMetricEnum.CUSTOMER_POI_BIND_START_SIGN.getName(), sourceDesc, status, reason);
            MetricHelper.build().name(CustomerMetricEnum.CUSTOMER_POI_BIND_START_SIGN.getName()).tag(CustomerMetricEnum.CUSTOMER_POI_BIND.getTag(), sourceDesc)
                    .tag(CustomerMetricEnum.CUSTOMER_POI_BIND_START_SIGN.getStatus(), status).count();
        }
    }

    /**
     * 执行客户门店绑定
     *
     * @param wmCustomerPoiBindBo
     * @throws WmCustomerException
     */
    private void bindCustomerPoi(WmCustomerPoiBindBo wmCustomerPoiBindBo) throws WmCustomerException {
        log.info("bindCustomerPoi wmCustomerPoiBindBo={}", JSONObject.toJSONString(wmCustomerPoiBindBo));
        WmCustomerDB wmCustomerDB = wmCustomerPoiBindBo.getWmCustomerDB();
        Set<Long> wmPoiIdSet = wmCustomerPoiBindBo.getWmPoiIdSet();
        Integer opUid = wmCustomerPoiBindBo.getOpUid();
        String opName = wmCustomerPoiBindBo.getOpName();
        WmCustomerPoiOplogSourceTypeEnum oplogSourceTypeEnum = wmCustomerPoiBindBo.getOplogSourceTypeEnum();
        String status = WmCustomerConstant.SUCCESS;
        String reason = "";
        try {
            int customerId = wmCustomerDB.getId();
            // 获取客户任务
            Map<Long, Integer> poiAndTaskMaps = wmCustomerPoiBindBo.getPoiAndTaskMaps();
            // 校验客户与门店的新结算版本是否一致
            boolean isNewSettle = wmCustomerNewSettleService.checkCustomerAndPoiVersion(wmCustomerDB.getMtCustomerId(), wmCustomerDB.getCustomerName(), wmPoiIdSet);
            // 批量绑定关系
            operateCustomerPoiRel(wmCustomerPoiBindBo.getTypeEnum(), poiAndTaskMaps, wmPoiIdSet, customerId);
            // 批量添加客户门店属性
            wmCustomerPoiAttributeService.upsertCustomerPoiAttribute(customerId, wmPoiIdSet);
            // 变更门店主体
            wmContractServiceAdaptor.batchChangePoiSubject(customerId, wmPoiIdSet, ContractSignSubjectOpTagEnum.CUSTOMER_BIND.getCode());
            // 新结算绑定，给门店打新结算标签
            if (isNewSettle) {
                wmCustomerNewSettleService.addNewSettlePoiTag(wmPoiIdSet, opUid, opName);
            }
            // 对商家进行打"已审核美食城"标签
            if (CollectionUtils.isNotEmpty(wmCustomerPoiBindBo.getNeedAddMscLableSet())) {
                boolean customerHasMSCLabel = wmCustomerMSCLabelService.checkCustomerHasMSCLabel(wmCustomerDB.getMtCustomerId());
                if (customerHasMSCLabel) {
                    wmCustomerMSCLabelService.addMSCPoiTag(wmCustomerPoiBindBo.getNeedAddMscLableSet(), opUid, opName);
                } else if (wmCustomerDB.getCustomerRealType() != null && wmCustomerDB.getCustomerRealType().intValue() == CustomerRealTypeEnum.MEISHICHENG.getValue()) {
                    log.error("bindCustomerPoi customerHasMSCLabel check unpass needAddMscLableSet={}", JSONObject.toJSONString(wmCustomerPoiBindBo.getNeedAddMscLableSet()));
                }
            }

            //客户类型为美食城+客户有资质共用标签
            if (wmCustomerDB.getCustomerRealType() == CustomerRealTypeEnum.MEISHICHENG.getValue()
                    && wmCustomerMSCLabelService.checkHasQuaComCustomerLabel(wmCustomerDB.getMtCustomerId())) {
                wmCustomerMSCLabelService.addMscQuaCommonPoiTag(Sets.newHashSet(wmPoiIdSet), opUid, opName);
            }

            // 更新任务状态-已完成
            if (!CollectionUtils.isEmpty(poiAndTaskMaps)) {
                customerTaskService.updateTaskStatus(customerId, wmPoiIdSet, Lists.newArrayList(poiAndTaskMaps.values()));
            }

            // 客户门店列表操作：批量刷新es
            if (oplogSourceTypeEnum == WmCustomerPoiOplogSourceTypeEnum.CUSTOMER_POI_LIST) {
                wmCustomerPoiListEsService.batchInsertEsSync(customerId, wmPoiIdSet);
            }
            // 批量插入客户门店绑定操作日志
            wmCustomerPoiOplogService.batchBindOplog(new WmCustomerPoiOplogDB(customerId, oplogSourceTypeEnum.getCode(), opUid, opName),
                    Lists.newArrayList(wmPoiIdSet));
            // 插入客户日志
            wmCustomerService.insertCustomerOpLog(customerId, opUid, opName, WmCustomerOplogBo.OpType.INSERT,
                    String.format(CustomerConstants.CUSTOMER_LOG_TEMPLATE_POI_BIND,
                            StringUtils.join(wmPoiIdSet, CustomerConstants.SPLIT_SYMBOL)),
                    wmCustomerPoiBindBo.getRemark());
            for (Long wmPoiId : wmPoiIdSet) {
                wmPoiOplogThriftServiceAdaptor.insertPoiOpLog(wmPoiId, String.format(CustomerConstants.POI_LOG_CHANGE_CUSTOMER, "", wmCustomerDB.getMtCustomerId() + wmCustomerDB.getCustomerName()),
                        opUid, opName, "", String.valueOf(wmCustomerDB.getMtCustomerId()));
            }
            // 插入上单状态机通知，门店维度日志
            wmContractPoiProduceService.logPoiProduce(customerId, Lists.newArrayList(wmPoiIdSet), opUid, opName);
            stateCenterAdapter.batchSyncCustomerState(wmPoiIdSet, CustomerModuleStateEnum.findByCode(wmCustomerDB.getAuditStatus()), opUid, opName);
            // 发送MQ
            if (AppContext.isLazyProcess()) {
                AppContext.offerLazyTask(new AppContext.LazyTask() {
                    @Override
                    public void lazyProcess() {
                        SleepUtils.sleep(ConfigUtilAdapter.getInt(CALL_INTERVAL_TIME, 500));
                        wmCustomerService.sendCustomerStatusNoticeMQ(customerId, CustomerMQEventEnum.CUSTOMER_BIND_POI,
                                wmPoiIdSet, opUid, opName);
                    }

                    @Override
                    public String taskDesc() {
                        return "自入驻同步门店至3.0，发送门店绑定至客户成功消息";
                    }
                });
            } else {
                // 发送MQ
                wmCustomerService.sendCustomerStatusNoticeMQ(customerId, CustomerMQEventEnum.CUSTOMER_BIND_POI,
                        wmPoiIdSet, opUid, opName);
            }
        } catch (WmCustomerException e) {
            status = WmCustomerConstant.BUSINESS_EXCEPTION;
            reason = e.getMsg();
            Cat.logError(String.format("%s error errMsg=%s", CustomerMetricEnum.CUSTOMER_POI_BIND.getName(), e.getMsg()), e);
            throw e;
        } catch (Exception e) {
            status = WmCustomerConstant.SYSTEM_EXCEPTION;
            reason = WmCustomerConstant.SYSTEM_EXCEPTION_MSG;
            Cat.logError(String.format("%s error", CustomerMetricEnum.CUSTOMER_POI_BIND.getName()), e);
            throw e;
        } finally {
            Cat.logEvent(CustomerMetricEnum.CUSTOMER_POI_BIND.getName(), oplogSourceTypeEnum.getDesc(), status, reason);
            MetricHelper.build().name(CustomerMetricEnum.CUSTOMER_POI_BIND.getName()).tag(CustomerMetricEnum.CUSTOMER_POI_BIND.getTag(), oplogSourceTypeEnum.getDesc())
                    .tag(CustomerMetricEnum.CUSTOMER_POI_BIND.getStatus(), status).count(wmPoiIdSet.size());
        }
    }

    /**
     * 处理客户门店关系
     *
     * @param typeEnum
     * @param poiAndTaskMaps
     * @param wmPoiIdSet
     * @param customerId
     */
    private void operateCustomerPoiRel(CustomerPoiBindTypeEnum typeEnum, Map<Long, Integer> poiAndTaskMaps, Set<Long> wmPoiIdSet, int customerId) throws WmCustomerException {
        switch (typeEnum) {
            case DIRECT_BIND:
            case RE_START_BIND:
                // 门店状态：已绑定
                if (CollectionUtils.isEmpty(poiAndTaskMaps)) {
                    wmCustomerPoiRelService.batchInsertCustomerPoi(customerId, wmPoiIdSet);
                } else {
                    wmCustomerPoiRelService.batchInsertCustomerPoiWithBizTaskId(customerId, wmPoiIdSet, poiAndTaskMaps);
                }
                break;
            case CONFIRM_BIND:
                // 门店状态：已绑定
                wmCustomerPoiRelService.confirmPreBind(wmPoiIdSet, 0L, customerId);
                break;
            default:
                log.error("operateCustomerPoiRel 未知的操作类型 typeEnum={},poiAndTaskMaps={},wmPoiIdSet={},customerId={}",
                        JSONObject.toJSONString(typeEnum), JSONObject.toJSONString(poiAndTaskMaps), JSONObject.toJSONString(wmPoiIdSet), customerId);
                throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "处理客户门店关系");
        }

    }


    /**
     * 处理客户门店关系
     *
     * @param typeEnum
     * @param poiAndTaskMaps
     * @param wmPoiIdSet
     * @param customerId
     */
    private void operateCustomerPoiRelForSigner(CustomerPoiBindTypeEnum typeEnum, Map<Long, Integer> poiAndTaskMaps, Set<Long> wmPoiIdSet, int customerId) throws WmCustomerException {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.service.customer.poi.CustomerPoiBindService.operateCustomerPoiRelForSigner(CustomerPoiBindTypeEnum,Map,Set,int)");
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.service.customer.poi.CustomerPoiBindService.operateCustomerPoiRelForSigner(CustomerPoiBindTypeEnum,Map,Set,int)");
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.service.customer.poi.CustomerPoiBindService.operateCustomerPoiRelForSigner(CustomerPoiBindTypeEnum,Map,Set,int)");
        switch (typeEnum) {
            case RE_START_BIND:
                break;
            case DIRECT_BIND:
                // 门店状态：已绑定-> 待发起确认
                if (CollectionUtils.isEmpty(poiAndTaskMaps)) {
                    wmCustomerPoiRelService.batchInsertCustomerPoiForPreBind(customerId, wmPoiIdSet, 0L);
                } else {
                    wmCustomerPoiRelService.batchInsertCustomerPoiForPreBindWithBizTaskId(customerId, wmPoiIdSet, 0L, poiAndTaskMaps);
                }
                break;
            default:
                log.error("operateCustomerPoiRel 未知的操作类型 typeEnum={},poiAndTaskMaps={},wmPoiIdSet={},customerId={}",
                        JSONObject.toJSONString(typeEnum), JSONObject.toJSONString(poiAndTaskMaps), JSONObject.toJSONString(wmPoiIdSet), customerId);
                throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "处理客户门店关系");
        }
    }
}
