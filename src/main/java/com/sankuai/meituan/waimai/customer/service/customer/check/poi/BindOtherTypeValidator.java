package com.sankuai.meituan.waimai.customer.service.customer.check.poi;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.sankuai.meituan.waimai.customer.constant.customer.CustomerPoiBindDecideResultEnum;
import com.sankuai.meituan.waimai.customer.constant.customer.CustomerPoiBindTypeEnum;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerDB;
import com.sankuai.meituan.waimai.customer.domain.poi.WmCustomerPoiCheckBo;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerNewSettleService;
import com.sankuai.meituan.waimai.customer.service.customer.check.CustomerPoiBaseValidator;
import com.sankuai.meituan.waimai.customer.service.customer.check.ICustomerPoiValidator;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerRealTypeEnum;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * 门店要绑定其他类型的客户校验
 */
@Slf4j
@Service
public class BindOtherTypeValidator extends CustomerPoiBaseValidator implements ICustomerPoiValidator {

    private static final List<Integer> canNotCustomerRealTypes = Lists.newArrayList(CustomerRealTypeEnum.AGGREGATE_DISTRIBUTOR.getValue(),
            CustomerRealTypeEnum.DANDIAN.getValue(),
            CustomerRealTypeEnum.DANDIAN_SG.getValue(),
            CustomerRealTypeEnum.DANDIAN_YAOPIN.getValue(),
            CustomerRealTypeEnum.MEISHICHENG.getValue(),
            CustomerRealTypeEnum.SHITANG.getValue());

    @Autowired
    private WmCustomerNewSettleService wmCustomerNewSettleService;

    @Override
    public boolean canCheck(int customerRealType) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_1", "com.sankuai.meituan.waimai.customer.service.customer.check.poi.BindOtherTypeValidator.canCheck(int)");
        if (!canNotCustomerRealTypes.contains(customerRealType)) {
            return true;
        }
        return false;
    }

    @Override
    public Map<CustomerPoiBindDecideResultEnum, List<Long>> validBind(WmCustomerPoiCheckBo wmCustomerPoiCheckBo) throws WmCustomerException, TException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.customer.check.poi.BindOtherTypeValidator.validBind(com.sankuai.meituan.waimai.customer.domain.poi.WmCustomerPoiCheckBo)");
        checkBind(wmCustomerPoiCheckBo);
        WmCustomerDB wmCustomerDB = wmCustomerPoiCheckBo.getWmCustomerDB();
        Integer customerRealType = wmCustomerDB.getCustomerRealType();
        Map<CustomerPoiBindDecideResultEnum, List<Long>> map = Maps.newHashMap();
        if (CustomerRealTypeEnum.getByValue(customerRealType) != CustomerRealTypeEnum.DEFAULT_TYPE) {
            if (wmCustomerPoiCheckBo.getTypeEnum() != CustomerPoiBindTypeEnum.HIGN_SEA_CHECK &&
                    CollectionUtils.isNotEmpty(wmCustomerPoiCheckBo.getRealWmPoiIdList())) {
                // 校验客户与门店的新结算版本是否一致
                wmCustomerNewSettleService.checkCustomerAndPoiVersion(wmCustomerDB.getMtCustomerId(), wmCustomerDB.getCustomerName(),
                        Sets.newHashSet(wmCustomerPoiCheckBo.getRealWmPoiIdList()));
            }
            map.put(CustomerPoiBindDecideResultEnum.CAN_BIND_DIRECT, wmCustomerPoiCheckBo.getRealWmPoiIdList());
        }
        return map;
    }
}
