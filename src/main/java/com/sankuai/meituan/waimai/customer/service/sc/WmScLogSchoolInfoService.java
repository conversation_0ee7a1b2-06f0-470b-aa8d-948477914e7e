package com.sankuai.meituan.waimai.customer.service.sc;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SimplePropertyPreFilter;
import com.google.common.collect.Lists;
import com.sankuai.meituan.auth.util.UserUtils;
import com.sankuai.meituan.auth.vo.User;
import com.sankuai.meituan.common.json.JSONUtil;
import com.sankuai.meituan.waimai.customer.adapter.WmCampusContactServiceAdapter;
import com.sankuai.meituan.waimai.customer.adapter.WmScEmployAdaptor;
import com.sankuai.meituan.waimai.customer.dao.sc.WmCanteenMapper;
import com.sankuai.meituan.waimai.customer.dao.sc.WmScEnumDictMapper;
import com.sankuai.meituan.waimai.customer.dao.sc.WmScLogMapper;
import com.sankuai.meituan.waimai.customer.domain.sc.*;
import com.sankuai.meituan.waimai.customer.domain.sc.canteenstall.WmCanteenStallBindDO;
import com.sankuai.meituan.waimai.customer.domain.sc.delivery.*;
import com.sankuai.meituan.waimai.infra.domain.WmEmploy;
import com.sankuai.meituan.waimai.mtauth.util.WmEmployUtils;
import com.sankuai.meituan.waimai.thrift.bo.WmCustomerDiffCellBo;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.*;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.delivery.*;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.time.SchoolTermBeginPlanEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.time.SchoolTermBeginSituationEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.time.SchoolTermEndPlanEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.time.SchoolTermEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.sc.WmScSchoolCoPlatformFeeTypeDTO;
import com.sankuai.meituan.waimai.thrift.customer.domain.sc.delivery.WmSchoolDeliveryAssignmentDTO;
import com.sankuai.meituan.waimai.thrift.customer.domain.sc.delivery.WmSchoolDeliveryFollowUpDTO;
import com.sankuai.meituan.waimai.thrift.customer.domain.sc.delivery.WmSchoolDeliveryGoalSetDTO;
import com.sankuai.meituan.waimai.thrift.customer.domain.sc.delivery.WmSchoolDepartmentIntensionDTO;
import com.sankuai.meituan.waimai.thrift.exception.WmSchCantException;
import com.sankuai.meituan.waimai.thrift.util.DateUtil;
import com.sankuai.meituan.waimai.thrift.util.DiffUtil;
import com.sankuai.waimai.crm.cd.saas.lead.campus.sdk.dto.contact.ContactDto;
import com.sankuai.waimai.crm.cd.saas.lead.campus.sdk.dto.partner.PartnerInfoDto;
import joptsimple.internal.Strings;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.lang.reflect.Field;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

import static com.sankuai.meituan.waimai.customer.constant.ScFieldConstant.*;
import static com.sankuai.meituan.waimai.thrift.customer.constant.sc.ScConstants.*;

/**
 * @description: 学校详情日志操作模块
 **/
@Slf4j
@Service
public class WmScLogSchoolInfoService {

    @Autowired
    private WmScLogMapper wmScLogMapper;

    @Autowired
    private WmCanteenMapper wmCanteenMapper;

    @Autowired
    private WmScEnumDictMapper wmScEnumDictMapper;

    @Autowired
    private WmCampusContactServiceAdapter wmCampusContactServiceAdapter;

    @Autowired
    private WmScEmployAdaptor wmScEmployAdaptor;

    public static final String nullShow = "空";

    /**
     * 组装学校范围的插入日志(手动新增)
     * @param wmScSchoolAreaDO wmScSchoolAreaDO
     * @return 学校范围的插入日志
     */
    public String composeSchoolAreaInsertingManual(WmScSchoolAreaDO wmScSchoolAreaDO) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.sc.WmScLogSchoolInfoService.composeSchoolAreaInsertingManual(com.sankuai.meituan.waimai.customer.domain.sc.WmScSchoolAreaDO)");
        StringBuilder stringBuilder = new StringBuilder().append("操作：新建\\n");
        stringBuilder.append("提交渠道：学校管理系统\\n");
        stringBuilder.append("AOI ID：0（手动添加范围）\\n");
        schoolAreaField.forEach((key,value) -> {
            if (SCHOOL_AREA_AOI_ID.equals(key) || SCHOOL_AREA_AOI_MODE.equals(key) || SCHOOL_AREA_AOI_NAME.equals(key)) {
                return;
            }
            stringBuilder.append("[学校范围] ");
            stringBuilder.append(schoolAreaField.get(key));
            stringBuilder.append("：");
            Object afterValue = transSaveSchoolAreaToDesc(key, wmScSchoolAreaDO);
            stringBuilder.append(afterValue);
            stringBuilder.append("\\n");
        });
        return stringBuilder.toString();
    }

    /**
     * 组装学校范围的插入日志(同步AOI信息)
     * @param wmScSchoolAreaDO wmScSchoolAreaDO
     * @return 学校范围的插入日志
     */
    public String composeSchoolAreaInserting(WmScSchoolAreaDO wmScSchoolAreaDO) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.service.sc.WmScLogSchoolInfoService.composeSchoolAreaInserting(com.sankuai.meituan.waimai.customer.domain.sc.WmScSchoolAreaDO)");
        StringBuilder stringBuilder = new StringBuilder().append("操作：新建\\n");
        stringBuilder.append("提交渠道：学校管理系统\\n");
        stringBuilder.append("AOI ID：").append(wmScSchoolAreaDO.getAoiId().toString()).append("\\n");
        schoolAreaField.forEach((key,value) -> {
            stringBuilder.append("[学校范围] ");
            stringBuilder.append(schoolAreaField.get(key));
            stringBuilder.append("：");
            Object afterValue = transSaveSchoolAreaToDesc(key, wmScSchoolAreaDO);
            stringBuilder.append(afterValue);
            stringBuilder.append("\\n");
        });
        return stringBuilder.toString();
    }

    /**
     * 记录学校范围变更操作日志
     * @param wmScSchoolAreaLogBO wmScSchoolAreaLogBO
     */
    public void recordSchoolAreaUpdateLog(WmScSchoolAreaLogBO wmScSchoolAreaLogBO) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_1", "com.sankuai.meituan.waimai.customer.service.sc.WmScLogSchoolInfoService.recordSchoolAreaUpdateLog(com.sankuai.meituan.waimai.customer.domain.sc.WmScSchoolAreaLogBO)");
        if (wmScSchoolAreaLogBO == null
                || wmScSchoolAreaLogBO.getWmScSchoolAreaDoAfter() == null
                || wmScSchoolAreaLogBO.getWmScSchoolAreaDoBefore() == null) {
            log.warn("[WmScLogSchoolInfoService.recordSchoolAreaUpdateLog] input param is null. wmScSchoolAreaLogBO = {}", JSONObject.toJSONString(wmScSchoolAreaLogBO));
            return;
        }
        String logInfo = composeSchoolAreaUpdateLog(wmScSchoolAreaLogBO.getWmScSchoolAreaDoBefore(), wmScSchoolAreaLogBO.getWmScSchoolAreaDoAfter());
        if (StringUtils.isNotBlank(logInfo)) {
            insertScOptLog(OptTypeEnum.UPDATE.getType(), SC_SCHOOLAREA_LOG, wmScSchoolAreaLogBO.getWmScSchoolAreaDoBefore().getSchoolPrimaryId(),
                    wmScSchoolAreaLogBO.getUserId(), wmScSchoolAreaLogBO.getUserName(), logInfo, "");
        }
    }

    /**
     * 组装学校范围的更新日志
     * @param wmScSchoolAreaDoBefore 变更前wmScSchoolAreaDoBefore
     * @param wmScSchoolAreaDoAfter 变更后wmScSchoolAreaDoAfter
     * @return 更新日志
     */
    public String composeSchoolAreaUpdateLog(WmScSchoolAreaDO wmScSchoolAreaDoBefore, WmScSchoolAreaDO wmScSchoolAreaDoAfter) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.service.sc.WmScLogSchoolInfoService.composeSchoolAreaUpdateLog(WmScSchoolAreaDO,WmScSchoolAreaDO)");
        List<WmCustomerDiffCellBo> diffList = Lists.newArrayList();
        try {
            diffList = DiffUtil.compare(wmScSchoolAreaDoBefore, wmScSchoolAreaDoAfter, schoolAreaField);
        } catch (Exception e) {
            log.error("[composeSchoolAreaUpdateLog] 插入学校范围日志异常, wmScSchoolAreaDoBefore = {}, wmScSchoolAreaDoAfter = {} ",
                    JSONObject.toJSONString(wmScSchoolAreaDoBefore), JSONObject.toJSONString(wmScSchoolAreaDoAfter), e);
        }

        StringBuilder stringBuilder = new StringBuilder().append("操作：修改\\n");
        stringBuilder.append("提交渠道：学校管理系统\\n");
        stringBuilder.append("AOI ID：").append(wmScSchoolAreaDoAfter.getAoiId().toString()).append("\\n");
        for (WmCustomerDiffCellBo wmCustomerDiffCellBo : diffList) {
            stringBuilder.append("[字段变更] ");
            stringBuilder.append(schoolAreaField.get(wmCustomerDiffCellBo.getField()));
            String preValue = wmCustomerDiffCellBo.getPre();
            if (StringUtils.isEmpty(preValue)) {
                preValue = nullShow;
            }
            preValue = transUpdateSchoolAreaToDesc(wmCustomerDiffCellBo.getField(), preValue);
            stringBuilder.append(":" + preValue);
            stringBuilder.append("=>");
            String afterValue = transUpdateSchoolAreaToDesc(wmCustomerDiffCellBo.getField(), wmCustomerDiffCellBo.getAft());
            if (StringUtils.isEmpty(afterValue)) {
                afterValue = nullShow;
            }
            stringBuilder.append(afterValue);
            stringBuilder.append("\\n");
        }
        return stringBuilder.toString();
    }

    /**
     * 组装学校楼宇信息的更新日志
     * @param wmScSchoolBuildingDoBefore 更新前DO
     * @param wmScSchoolBuildingDoAfter 更新后DO
     * @return 更新日志
     */
    public String composeSchoolBuildingUpdateLog(WmScSchoolBuildingDO wmScSchoolBuildingDoBefore,
                                                 WmScSchoolBuildingDO wmScSchoolBuildingDoAfter) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.service.sc.WmScLogSchoolInfoService.composeSchoolBuildingUpdateLog(WmScSchoolBuildingDO,WmScSchoolBuildingDO)");
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.service.sc.WmScLogSchoolInfoService.composeSchoolBuildingUpdateLog(WmScSchoolBuildingDO,WmScSchoolBuildingDO)");
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.service.sc.WmScLogSchoolInfoService.composeSchoolBuildingUpdateLog(WmScSchoolBuildingDO,WmScSchoolBuildingDO)");
        List<WmCustomerDiffCellBo> diffList = Lists.newArrayList();
        try {
            diffList = DiffUtil.compare(wmScSchoolBuildingDoBefore, wmScSchoolBuildingDoAfter, schoolBuildingField);
        } catch (Exception e) {
            log.error("[WmScLogSchoolInfoService.composeSchoolBuildingUpdateLog] 插入学校楼宇日志异常, wmScSchoolBuildingDoBefore = {}, wmScSchoolBuildingDoAfter = {} ",
                    JSONObject.toJSONString(wmScSchoolBuildingDoBefore), JSONObject.toJSONString(wmScSchoolBuildingDoAfter), e);
        }

        log.info("[composeSchoolBuildingUpdateLog] diffList = {}", JSONObject.toJSONString(diffList));
        StringBuilder stringBuilder = new StringBuilder().append("操作：修改\\n");
        stringBuilder.append("提交渠道：学校管理系统\\n");
        stringBuilder.append("楼宇ID：").append(wmScSchoolBuildingDoBefore.getId().toString()).append("\\n");
        for (WmCustomerDiffCellBo wmCustomerDiffCellBo : diffList) {
            String preValue = wmCustomerDiffCellBo.getPre();
            if (StringUtils.isEmpty(preValue)) {
                preValue = nullShow;
            }
            preValue = transUpdateSchoolBuildingToDesc(wmCustomerDiffCellBo.getField(), preValue);

            String afterValue = transUpdateSchoolBuildingToDesc(wmCustomerDiffCellBo.getField(), wmCustomerDiffCellBo.getAft());
            if (StringUtils.isEmpty(afterValue)) {
                afterValue = nullShow;
            }
            if (preValue.equals(afterValue) && nullShow.equals(preValue)) {
                continue;
            }
            stringBuilder.append("[字段变更] ");
            stringBuilder.append(schoolBuildingField.get(wmCustomerDiffCellBo.getField()));
            stringBuilder.append(": ").append(preValue);
            stringBuilder.append("=>");
            stringBuilder.append(afterValue);
            stringBuilder.append("\\n");
        }
        log.info("[composeSchoolBuildingUpdateLog] info = {}", stringBuilder.toString());
        return stringBuilder.toString();
    }

    /**
     * 记录学校楼宇变更操作日志
     * @param wmScSchoolBuildingLogBO wmScSchoolBuildingLogBO
     */
    public void recordSchoolBuildingUpdateLog(WmScSchoolBuildingLogBO wmScSchoolBuildingLogBO) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_1", "com.sankuai.meituan.waimai.customer.service.sc.WmScLogSchoolInfoService.recordSchoolBuildingUpdateLog(com.sankuai.meituan.waimai.customer.domain.sc.WmScSchoolBuildingLogBO)");
        if (wmScSchoolBuildingLogBO == null
                || wmScSchoolBuildingLogBO.getWmScSchoolBuildingDoBefore() == null
                || wmScSchoolBuildingLogBO.getWmScSchoolBuildingDoAfter() == null) {
            log.warn("[WmScLogSchoolInfoService.recordSchoolBuildingUpdateLog] input param is null. wmScSchoolBuildingLogBO = {}", JSONObject.toJSONString(wmScSchoolBuildingLogBO));
            return;
        }

        String logInfo = composeSchoolBuildingUpdateLog(wmScSchoolBuildingLogBO.getWmScSchoolBuildingDoBefore(), wmScSchoolBuildingLogBO.getWmScSchoolBuildingDoAfter());
        if (StringUtils.isNotBlank(logInfo)) {
            insertScOptLog(OptTypeEnum.UPDATE.getType(), SC_SCHOOL_BUILDING_LOG, wmScSchoolBuildingLogBO.getWmScSchoolBuildingDoBefore().getSchoolPrimaryId(),
                    wmScSchoolBuildingLogBO.getUserId(), wmScSchoolBuildingLogBO.getUserName(), logInfo, "");
        }
    }

    /**
     * 组装学校楼宇插入日志
     * @param wmScSchoolBuildingDoInsert wmScSchoolBuildingDoInsert
     * @return 学校楼宇插入日志
     */
    public String composeSchoolBuildingInsertLog(WmScSchoolBuildingDO wmScSchoolBuildingDoInsert) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_1", "com.sankuai.meituan.waimai.customer.service.sc.WmScLogSchoolInfoService.composeSchoolBuildingInsertLog(com.sankuai.meituan.waimai.customer.domain.sc.WmScSchoolBuildingDO)");
        StringBuilder stringBuilder = new StringBuilder().append("操作：新建\\n");
        stringBuilder.append("提交渠道：学校管理系统\\n");
        stringBuilder.append("楼宇ID：").append(wmScSchoolBuildingDoInsert.getId().toString()).append("\\n");
        schoolBuildingField.forEach((key, value) -> {
            Object afterValue = transInsertSchoolBuildingToDesc(key, wmScSchoolBuildingDoInsert);
            if (afterValue == null
                    || StringUtils.isBlank(afterValue.toString())
                    || "[]".equals(afterValue.toString())
                    || nullShow.equals(afterValue.toString())) {
                return;
            }
            stringBuilder.append("[字段变更] ");
            stringBuilder.append(schoolBuildingField.get(key));
            stringBuilder.append(": ");
            stringBuilder.append(afterValue);
            stringBuilder.append("\\n");
        });
        return stringBuilder.toString();
    }

    /**
     * 记录学校楼宇信息新增记录
     * @param wmScSchoolBuildingLogBO wmScSchoolBuildingLogBO
     */
    public void recordSchoolBuildingInsertLog(WmScSchoolBuildingLogBO wmScSchoolBuildingLogBO) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_2", "com.sankuai.meituan.waimai.customer.service.sc.WmScLogSchoolInfoService.recordSchoolBuildingInsertLog(com.sankuai.meituan.waimai.customer.domain.sc.WmScSchoolBuildingLogBO)");
        if (wmScSchoolBuildingLogBO == null
                || wmScSchoolBuildingLogBO.getWmScSchoolBuildingDoInsert() == null) {
            log.warn("[WmScLogSchoolInfoService.recordSchoolBuildingInsertLog] input param is null. wmScSchoolBuildingLogBO = {}", JSONObject.toJSONString(wmScSchoolBuildingLogBO));
            return;
        }
        String logInfo = composeSchoolBuildingInsertLog(wmScSchoolBuildingLogBO.getWmScSchoolBuildingDoInsert());
        if (StringUtils.isNotBlank(logInfo)) {
            insertScOptLog(OptTypeEnum.INSERT.getType(), SC_SCHOOL_BUILDING_LOG, wmScSchoolBuildingLogBO.getWmScSchoolBuildingDoInsert().getSchoolPrimaryId(),
                    wmScSchoolBuildingLogBO.getUserId(), wmScSchoolBuildingLogBO.getUserName(), logInfo, "");
        }
    }

    public Object transInsertSchoolBuildingToDesc(String key, WmScSchoolBuildingDO wmScSchoolBuildingDoInsert) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.service.sc.WmScLogSchoolInfoService.transInsertSchoolBuildingToDesc(java.lang.String,com.sankuai.meituan.waimai.customer.domain.sc.WmScSchoolBuildingDO)");
        Object trueValue = getFieldValueByFieldName(key, wmScSchoolBuildingDoInsert);
        if (trueValue != null) {
            // 楼宇电梯
            if (BUILDING_ELEVATOR.equals(key) && "0".equals(trueValue.toString())) {
                return nullShow;
            }

            if (BUILDING_ELEVATOR.equals(key)) {
                SchoolBuildingElevatorEnum schoolBuildingElevatorEnum = SchoolBuildingElevatorEnum.getByType((int) trueValue);
                return schoolBuildingElevatorEnum == null ? nullShow : schoolBuildingElevatorEnum.getName();
            }
            // 楼宇类型
            if (BUILDING_TYPE.equals(key)) {
                SchoolBuildingTypeEnum schoolBuildingTypeEnum = SchoolBuildingTypeEnum.getByType((int) trueValue);
                return schoolBuildingTypeEnum == null ? nullShow : schoolBuildingTypeEnum.getName();
            }
            // 楼宇位置
            if (BUILDING_LOCATION.equals(key)) {
                SchoolBuildingLocationEnum schoolBuildingLocationEnum = SchoolBuildingLocationEnum.getByType((int) trueValue);
                return schoolBuildingLocationEnum == null ? nullShow : schoolBuildingLocationEnum.getName();
            }
            // 楼宇楼层
            if (BUILDING_FLOOR.equals(key) && "-1".equals(trueValue.toString())) {
                return nullShow;
            }
            // 楼宇人数
            if (BUILDING_PERSON_NUM.equals(key) && "-1".equals(trueValue.toString())) {
                return nullShow;
            }
            // POI ID
            if (BUILDING_POI_ID.equals(key) && "0".equals(trueValue.toString())) {
                return nullShow;
            }
            // 楼宇范围
            if (BUILDING_AREA.equals(key) && ("[]".equals(trueValue.toString()) || StringUtils.isBlank(trueValue.toString()))) {
                return nullShow;
            }
            // 楼宇扎点
            if (BUILDING_COORDINATE.equals(key) && ("[]".equals(trueValue.toString()) || StringUtils.isBlank(trueValue.toString()))) {
                return nullShow;
            }
            // 楼宇状态
            if (BUILDING_STATUS.equals(key)) {
                SchoolBuildingStatusEnum schoolBuildingStatusEnum = SchoolBuildingStatusEnum.getByType((int) trueValue);
                return schoolBuildingStatusEnum == null ? nullShow : schoolBuildingStatusEnum.getName();
            }
            // 楼宇别名
            if (BUILDING_NICKNAME.equals(key)) {
                return StringUtils.isBlank(trueValue.toString()) ? nullShow : trueValue.toString();
            }
        }
        return trueValue;
    }

    /**
     * 组装学校楼宇信息删除日志
     * @param wmScSchoolBuildingDO wmScSchoolBuildingDO
     * @return 楼宇信息删除日志
     */
    public String composeSchoolBuildingDeleteLog(WmScSchoolBuildingDO wmScSchoolBuildingDO) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.service.sc.WmScLogSchoolInfoService.composeSchoolBuildingDeleteLog(com.sankuai.meituan.waimai.customer.domain.sc.WmScSchoolBuildingDO)");
        StringBuilder stringBuilder = new StringBuilder().append("操作：删除\\n");
        stringBuilder.append("提交渠道：学校管理系统\\n");
        stringBuilder.append("楼宇ID：").append(wmScSchoolBuildingDO.getId().toString()).append("\\n");
        schoolBuildingField.forEach((key, value) -> {
            Object afterValue = transInsertSchoolBuildingToDesc(key, wmScSchoolBuildingDO);
            if (afterValue == null || StringUtils.isBlank(afterValue.toString()) || "[]".equals(afterValue.toString())) {
                return;
            }
            stringBuilder.append("[楼宇信息] ");
            stringBuilder.append(schoolBuildingField.get(key));
            stringBuilder.append("：");
            stringBuilder.append(afterValue);
            stringBuilder.append("\\n");
        });
        return stringBuilder.toString();
    }

    /**
     * 组装学校范围AOI信息删除日志
     * @param wmScSchoolAreaDO wmScSchoolAreaDO
     * @return 学校范围AOI删除日志
     */
    public String composeSchoolAreaDeleteLog(WmScSchoolAreaDO wmScSchoolAreaDO) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.service.sc.WmScLogSchoolInfoService.composeSchoolAreaDeleteLog(com.sankuai.meituan.waimai.customer.domain.sc.WmScSchoolAreaDO)");
        StringBuilder stringBuilder = new StringBuilder().append("操作：删除\\n");
        stringBuilder.append("提交渠道：学校管理系统\\n");
        stringBuilder.append("AOI ID：").append(wmScSchoolAreaDO.getAoiId().toString()).append("\\n");
        schoolAreaField.forEach((key,value) -> {
            stringBuilder.append("[学校范围]");
            stringBuilder.append(schoolAreaField.get(key));
            stringBuilder.append("：");
            Object afterValue = transSaveSchoolAreaToDesc(key, wmScSchoolAreaDO);
            stringBuilder.append(afterValue);
            stringBuilder.append("\\n");
        });
        return stringBuilder.toString();
    }

    /**
     * 转换学校操作记录日志中的枚举值
     * @param key key
     * @param wmScSchoolAreaDO wmScSchoolAreaDO
     * @return 枚举值name或原值
     */
    public Object transSaveSchoolAreaToDesc(String key, WmScSchoolAreaDO wmScSchoolAreaDO){
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.service.sc.WmScLogSchoolInfoService.transSaveSchoolAreaToDesc(java.lang.String,com.sankuai.meituan.waimai.customer.domain.sc.WmScSchoolAreaDO)");
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.service.sc.WmScLogSchoolInfoService.transSaveSchoolAreaToDesc(java.lang.String,com.sankuai.meituan.waimai.customer.domain.sc.WmScSchoolAreaDO)");
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.service.sc.WmScLogSchoolInfoService.transSaveSchoolAreaToDesc(java.lang.String,com.sankuai.meituan.waimai.customer.domain.sc.WmScSchoolAreaDO)");
        Object trueValue = getFieldValueByFieldName(key, wmScSchoolAreaDO);
        if (trueValue != null) {
            if (SCHOOL_AREA_AOI_MODE.equals(key)) {
                SchoolAoiModeEnum schoolAoiModeEnum = SchoolAoiModeEnum.getByType((int) trueValue);
                return schoolAoiModeEnum == null ? nullShow : schoolAoiModeEnum.getName();
            }

            if (SCHOOL_AREA_AUTO_SYNC.equals(key)) {
                SchoolAreaAutoSyncEnum schoolAreaAutoSyncEnum = SchoolAreaAutoSyncEnum.getByType((int) trueValue);
                return schoolAreaAutoSyncEnum == null ? nullShow : schoolAreaAutoSyncEnum.getName();
            }

            if (SCHOOL_FIELD_AREA.equals(key)) {
                if (StringUtils.isBlank(trueValue.toString()) || "[]".equals(trueValue.toString())) {
                    return nullShow;
                }
            }

        }
        return trueValue;
    }

    /**
     * 根据属性名获取属性值
     *
     * @param fieldName
     * @param object
     * @return
     */
    private Object getFieldValueByFieldName(String fieldName, Object object) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.service.sc.WmScLogSchoolInfoService.getFieldValueByFieldName(java.lang.String,java.lang.Object)");
        try {
            Field field = object.getClass().getDeclaredField(fieldName);
            field.setAccessible(true);
            return field.get(object);
        } catch (Exception e) {
            return null;
        }
    }

    public String transUpdateSchoolAreaToDesc(String key, String value) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.service.sc.WmScLogSchoolInfoService.transUpdateSchoolAreaToDesc(java.lang.String,java.lang.String)");
        if (!nullShow.equals(value) && StringUtils.isNotEmpty(value)) {
            if (SCHOOLAREA_FIELD_AUDITSTATUS.equals(key)) {
                SchoolAuditStatusEnum schoolAuditStatusEnum = SchoolAuditStatusEnum.getByType(Integer.parseInt(value));
                return schoolAuditStatusEnum == null ? nullShow : schoolAuditStatusEnum.getName();
            }

            if (SCHOOLAREA_FIELD_SOURCETYPE.equals(key)) {
                return "学校管理系统";
            }

            if (SCHOOL_AREA_AUTO_SYNC.equals(key)) {
                SchoolAreaAutoSyncEnum schoolAreaAutoSyncEnum = SchoolAreaAutoSyncEnum.getByType(Integer.parseInt(value));
                return schoolAreaAutoSyncEnum == null ? nullShow : schoolAreaAutoSyncEnum.getName();
            }

            if (SCHOOL_AREA_AOI_MODE.equals(key) && "-2".equals(value)) {
                return nullShow;
            }

            if (SCHOOL_AREA_AOI_MODE.equals(key)) {
                SchoolAoiModeEnum schoolAoiModeEnum = SchoolAoiModeEnum.getByType(Integer.parseInt(value));
                return schoolAoiModeEnum == null ? nullShow : schoolAoiModeEnum.getName();
            }
        }
        return value;
    }

    public String transUpdateSchoolBuildingToDesc(String key, String value) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_1", "com.sankuai.meituan.waimai.customer.service.sc.WmScLogSchoolInfoService.transUpdateSchoolBuildingToDesc(java.lang.String,java.lang.String)");
        if (!nullShow.equals(value) && StringUtils.isNotEmpty(value)) {
            // 楼宇类型
            if (BUILDING_TYPE.equals(key)) {
                SchoolBuildingTypeEnum schoolBuildingTypeEnum = SchoolBuildingTypeEnum.getByType(Integer.parseInt(value));
                return schoolBuildingTypeEnum == null ? nullShow : schoolBuildingTypeEnum.getName();
            }
            // 楼宇电梯
            if (BUILDING_ELEVATOR.equals(key) && "0".equals(value)) {
                return nullShow;
            }

            if (BUILDING_ELEVATOR.equals(key)) {
                SchoolBuildingElevatorEnum schoolBuildingElevatorEnum = SchoolBuildingElevatorEnum.getByType(Integer.parseInt(value));
                return schoolBuildingElevatorEnum == null ? nullShow : schoolBuildingElevatorEnum.getName();
            }
            // 楼宇位置
            if (BUILDING_LOCATION.equals(key)) {
                SchoolBuildingLocationEnum schoolBuildingLocationEnum = SchoolBuildingLocationEnum.getByType(Integer.parseInt(value));
                return schoolBuildingLocationEnum == null ? nullShow : schoolBuildingLocationEnum.getName();
            }
            // 楼宇楼层
            if (BUILDING_FLOOR.equals(key) && "-1".equals(value)) {
                return nullShow;
            }
            // 楼宇人数
            if (BUILDING_PERSON_NUM.equals(key) && "-1".equals(value)) {
                return nullShow;
            }
            // 楼宇范围
            if (BUILDING_AREA.equals(key) && ("[]".equals(value) || StringUtils.isBlank(value))) {
                return nullShow;
            }
            // 楼宇扎点
            if (BUILDING_COORDINATE.equals(key) && ("[]".equals(value) || StringUtils.isBlank(value))) {
                return nullShow;
            }
            // 楼宇状态
            if (BUILDING_STATUS.equals(key)) {
                SchoolBuildingStatusEnum schoolBuildingStatusEnum = SchoolBuildingStatusEnum.getByType(Integer.parseInt(value));
                return schoolBuildingStatusEnum == null ? nullShow : schoolBuildingStatusEnum.getName();
            }
        }
        return value;
    }

    /**
     * 插入操作日志
     */
    public void insertScOptLog(Byte optType, int moduleType, int moduleId, Integer userId, String userName, String logInfo, String remark) {
        WmScLogDB wmScLogDB = new WmScLogDB();
        wmScLogDB.setCtime((int)System.currentTimeMillis()/1000);
        wmScLogDB.setUtime((int)System.currentTimeMillis()/1000);
        wmScLogDB.setLog(logInfo);
        wmScLogDB.setRemark(remark);
        wmScLogDB.setModuleId(moduleId);
        wmScLogDB.setModuleType((short)moduleType);
        wmScLogDB.setOpType(optType);
        wmScLogDB.setOpUid(userId);
        wmScLogDB.setOpUname(userName);
        int result = wmScLogMapper.insertScLog(wmScLogDB);
        log.info("校园食堂项目:插入日志插入参数:result:{}:wmScLogDB:{}",result ,JSON.toJSONString(wmScLogDB));

        //执行更新食堂信息
        ArrayList<WmScLogDB> wmScLogDBArrayList = new ArrayList<>();
        wmScLogDBArrayList.add(wmScLogDB);
        int res = changeCanteenUTime(wmScLogDBArrayList);
        log.info("校园食堂项目:更新食堂信息: result:{}", res);
    }

    /**
     * 组装学校时间信息的插入日志
     *
     * @param wmScTimeInfoBo
     * @return
     */
    public String composeSchooTimeInfoInserting(WmScTimeInfoBo wmScTimeInfoBo) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.service.sc.WmScLogSchoolInfoService.composeSchooTimeInfoInserting(com.sankuai.meituan.waimai.customer.domain.sc.WmScTimeInfoBo)");
        StringBuilder stringBuilder = new StringBuilder().append("操作：新建\\n");
        stringBuilder.append("提交渠道：学校管理系统\\n");
        schoolTimeInfoField.forEach((key, value) -> {
            stringBuilder.append("[字段变更] ");
            stringBuilder.append(schoolTimeInfoField.get(key));
            stringBuilder.append(": ");

            Object afterValue = transSaveSchoolTimeInfoToDesc(key, wmScTimeInfoBo);
            if (afterValue == null || "".equals(afterValue)) {
                afterValue = nullShow;
            }
            stringBuilder.append(afterValue);
            stringBuilder.append("\\n");
        });
        // 开学信息
        schoolTimeTermBeginField.forEach((key, value) -> {
            stringBuilder.append("[字段变更]");
            stringBuilder.append(schoolTimeTermBeginField.get(key));
            stringBuilder.append("：");
            Object afterValue = transSaveSchoolTimeTermBeginToDesc(key, wmScTimeInfoBo);
            if (afterValue == null || "".equals(afterValue)) {
                afterValue = nullShow;
            }
            stringBuilder.append(afterValue);
            stringBuilder.append("\\n");
        });
        // 放假信息
        schoolTimeTermEndField.forEach((key, value) -> {
            stringBuilder.append("[字段变更]");
            stringBuilder.append(schoolTimeTermEndField.get(key));
            stringBuilder.append("：");
            Object afterValue = transSaveSchoolTimeTermEndToDesc(key, wmScTimeInfoBo);
            if (afterValue == null || "".equals(afterValue)) {
                afterValue = nullShow;
            }
            stringBuilder.append(afterValue);
            stringBuilder.append("\\n");
        });
        return stringBuilder.toString();
    }

    public Object transSaveSchoolTimeInfoToDesc(String key, WmScTimeInfoBo wmScTimeInfoBo) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.sc.WmScLogSchoolInfoService.transSaveSchoolTimeInfoToDesc(java.lang.String,com.sankuai.meituan.waimai.customer.domain.sc.WmScTimeInfoBo)");
        Object trueValue = getFieldValueByFieldName(key, wmScTimeInfoBo);
        if (trueValue != null) {
            // 学期
            if (TIME_FIELD_TERM.equals(key)) {
                SchoolTermEnum schoolTermEnum = SchoolTermEnum.of((int) trueValue);
                return schoolTermEnum == null ? nullShow : schoolTermEnum.getDesc();
            }
            // 开学情况
            if (TIME_FIELD_TERM_BEGIN_SITUATION.equals(key)) {
                SchoolTermBeginSituationEnum schoolTermBeginSituationEnum = SchoolTermBeginSituationEnum.of((int) trueValue);
                return schoolTermBeginSituationEnum == null ? nullShow : schoolTermBeginSituationEnum.getDesc();
            }
            // 未开学资料来源
            if (TIME_FIELD_TERM_OPEN_NOT_INFO_SOURCE.equals(key)) {
                SchoolTimeInfoSourceEnum schoolTimeInfoSourceEnum = SchoolTimeInfoSourceEnum.getByType((int) trueValue);
                return schoolTimeInfoSourceEnum == null ? nullShow : schoolTimeInfoSourceEnum.getName();
            }
        }
        return trueValue;
    }

    public Object transSaveSchoolTimeTermBeginToDesc(String key, WmScTimeInfoBo wmScTimeInfoBo) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_1", "com.sankuai.meituan.waimai.customer.service.sc.WmScLogSchoolInfoService.transSaveSchoolTimeTermBeginToDesc(java.lang.String,com.sankuai.meituan.waimai.customer.domain.sc.WmScTimeInfoBo)");
        Object trueValue = getFieldValueByFieldName(key, wmScTimeInfoBo);
        if (trueValue != null) {
            // 开学安排
            if (TIME_FIELD_TERM_BEGIN_PLAN.equals(key)) {
                SchoolTermBeginPlanEnum schoolTermBeginPlanEnum = SchoolTermBeginPlanEnum.of((int) trueValue);
                return schoolTermBeginPlanEnum == null ? nullShow : schoolTermBeginPlanEnum.getDesc();
            }
            // 开学资料来源
            if (TIME_FIELD_TERM_BEGIN_INFO_SOURCE.equals(key)) {
                SchoolTimeInfoSourceEnum schoolTimeInfoSourceEnum = SchoolTimeInfoSourceEnum.getByType((int) trueValue);
                return schoolTimeInfoSourceEnum == null ? nullShow : schoolTimeInfoSourceEnum.getName();
            }
        }
        return trueValue;
    }

    public Object transSaveSchoolTimeTermEndToDesc(String key, WmScTimeInfoBo wmScTimeInfoBo) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_2", "com.sankuai.meituan.waimai.customer.service.sc.WmScLogSchoolInfoService.transSaveSchoolTimeTermEndToDesc(java.lang.String,com.sankuai.meituan.waimai.customer.domain.sc.WmScTimeInfoBo)");
        Object trueValue = getFieldValueByFieldName(key, wmScTimeInfoBo);
        if (trueValue != null) {
            // 放假安排
            if (TIME_FIELD_TERM_END_PLAN.equals(key)) {
                SchoolTermEndPlanEnum schoolTermEndPlanEnum = SchoolTermEndPlanEnum.of((int) trueValue);
                return schoolTermEndPlanEnum == null ? nullShow : schoolTermEndPlanEnum.getDesc();
            }
            // 放假资料来源
            if (TIME_FIELD_TERM_END_INFO_SOURCE.equals(key)) {
                SchoolTimeInfoSourceEnum schoolTimeInfoSourceEnum = SchoolTimeInfoSourceEnum.getByType((int) trueValue);
                return schoolTimeInfoSourceEnum == null ? nullShow : schoolTimeInfoSourceEnum.getName();
            }
        }
        return trueValue;
    }

    /**
     * 组装学校时间信息的更新日志
     * @param after
     * @param pre
     */
    public String composeSchoolTimeInfoUpdateLog(WmScTimeInfoBo after, WmScTimeInfoBo pre) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.service.sc.WmScLogSchoolInfoService.composeSchoolTimeInfoUpdateLog(WmScTimeInfoBo,WmScTimeInfoBo)");
        List<WmCustomerDiffCellBo> diffList = Lists.newArrayList();
        List<WmCustomerDiffCellBo> termBeginDiffList = Lists.newArrayList();
        List<WmCustomerDiffCellBo> termEndDiffList = Lists.newArrayList();

        try {
            diffList = DiffUtil.compare(pre, after, schoolTimeInfoField);
            if (after.getTermBeginPlan() != null) {
                termBeginDiffList = DiffUtil.compare(pre, after, schoolTimeTermBeginField);
            }
            if (after.getTermEndPlan() != null) {
                termEndDiffList = DiffUtil.compare(pre, after, schoolTimeTermEndField);
            }
        } catch (Exception e) {
            log.error("插入日志异常", e);
        }

        if (diffList.isEmpty() && termBeginDiffList.isEmpty() && termEndDiffList.isEmpty()) {
            log.info("校园食堂项目:没有更新的字段:after:{}:pre:{}", JSON.toJSONString(after), JSON.toJSONString(pre));
            return "";
        }

        log.info("校园食堂临时日志:日志更新字段:after:{}:pre:{}", JSON.toJSONString(after), JSON.toJSONString(pre));
        StringBuilder stringBuilder = new StringBuilder().append("操作：修改\\n");
        stringBuilder.append("提交渠道：学校管理系统\\n");
        stringBuilder.append(String.format("%s~%s学年 %s", after.getYearBegin(), after.getYearEnd(), SchoolTermEnum.of(after.getTerm()).getDesc()));
        stringBuilder.append("\\n");
        for (WmCustomerDiffCellBo wmCustomerDiffCellBo : diffList) {
            stringBuilder.append("[字段变更] ");
            stringBuilder.append(schoolTimeInfoField.get(wmCustomerDiffCellBo.getField()));
            String preValue = wmCustomerDiffCellBo.getPre();
            if (StringUtils.isEmpty(preValue)) {
                preValue = nullShow;
            }
            preValue = transUpdateSchoolTimeInfoToDesc(wmCustomerDiffCellBo.getField(), preValue);
            stringBuilder.append(": ").append(preValue);
            stringBuilder.append("=>");
            String afterValue = transUpdateSchoolTimeInfoToDesc(wmCustomerDiffCellBo.getField(), wmCustomerDiffCellBo.getAft());
            if (StringUtils.isEmpty(afterValue)) {
                afterValue = nullShow;
            }
            stringBuilder.append(afterValue);
            stringBuilder.append("\\n");
        }
        // 开学信息
        if (CollectionUtils.isNotEmpty(termBeginDiffList)) {
            for (WmCustomerDiffCellBo wmCustomerDiffCellBo : termBeginDiffList) {
                stringBuilder.append("[字段变更] ");
                String preValue = wmCustomerDiffCellBo.getPre();
                stringBuilder.append(schoolTimeTermBeginField.get(wmCustomerDiffCellBo.getField()));
                if (StringUtils.isEmpty(preValue)) {
                    preValue = nullShow;
                }
                preValue = updateSchoolTimeTermBeginToDesc(wmCustomerDiffCellBo.getField(), preValue);
                String afterValue = updateSchoolTimeTermBeginToDesc(wmCustomerDiffCellBo.getField(), wmCustomerDiffCellBo.getAft());
                if (StringUtils.isEmpty(afterValue)) {
                    afterValue = nullShow;
                }
                stringBuilder.append(": ").append(preValue);
                stringBuilder.append("=>");
                stringBuilder.append(afterValue);
                stringBuilder.append("\\n");
            }
        }
        // 放假信息
        if (CollectionUtils.isNotEmpty(termEndDiffList)) {
            for (WmCustomerDiffCellBo wmCustomerDiffCellBo : termEndDiffList) {
                stringBuilder.append("[字段变更] ");
                String preValue = wmCustomerDiffCellBo.getPre();
                stringBuilder.append(schoolTimeTermEndField.get(wmCustomerDiffCellBo.getField()));
                if (StringUtils.isEmpty(preValue)) {
                    preValue = nullShow;
                }
                preValue = updateSchoolTimeTermEndToDesc(wmCustomerDiffCellBo.getField(), preValue);
                String afterValue = updateSchoolTimeTermEndToDesc(wmCustomerDiffCellBo.getField(), wmCustomerDiffCellBo.getAft());
                if (StringUtils.isEmpty(afterValue)) {
                    afterValue = nullShow;
                }
                stringBuilder.append(": ").append(preValue);
                stringBuilder.append("=>");
                stringBuilder.append(afterValue);
                stringBuilder.append("\\n");
            }
        }
        return stringBuilder.toString();
    }

    public String transUpdateSchoolTimeInfoToDesc(String key, String value) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.sc.WmScLogSchoolInfoService.transUpdateSchoolTimeInfoToDesc(java.lang.String,java.lang.String)");
        if (!nullShow.equals(value) && StringUtils.isNotEmpty(value)) {
            // 学期
            if (TIME_FIELD_TERM.equals(key)) {
                SchoolTermEnum schoolTermEnum = SchoolTermEnum.of(Integer.parseInt(value));
                return schoolTermEnum == null ? nullShow : schoolTermEnum.getDesc();
            }
            // 开学情况
            if (TIME_FIELD_TERM_BEGIN_SITUATION.equals(key)) {
                SchoolTermBeginSituationEnum schoolTermBeginSituationEnum = SchoolTermBeginSituationEnum.of(Integer.parseInt(value));
                return schoolTermBeginSituationEnum == null ? nullShow : schoolTermBeginSituationEnum.getDesc();
            }
            // 未开学资料来源
            if (TIME_FIELD_TERM_OPEN_NOT_INFO_SOURCE.equals(key)) {
                SchoolTimeInfoSourceEnum schoolTimeInfoSourceEnum = SchoolTimeInfoSourceEnum.getByType(Integer.parseInt(value));
                return schoolTimeInfoSourceEnum == null ? nullShow : schoolTimeInfoSourceEnum.getName();
            }
        }
        return value;
    }

    public String updateSchoolTimeTermBeginToDesc(String key, String value) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_1", "com.sankuai.meituan.waimai.customer.service.sc.WmScLogSchoolInfoService.updateSchoolTimeTermBeginToDesc(java.lang.String,java.lang.String)");
        if (!nullShow.equals(value) && StringUtils.isNotEmpty(value)) {
            // 开学安排
            if (TIME_FIELD_TERM_BEGIN_PLAN.equals(key)) {
                SchoolTermBeginPlanEnum schoolTermBeginPlanEnum = SchoolTermBeginPlanEnum.of(Integer.parseInt(value));
                return schoolTermBeginPlanEnum == null ? nullShow : schoolTermBeginPlanEnum.getDesc();
            }
            // 开学资料来源
            if (TIME_FIELD_TERM_BEGIN_INFO_SOURCE.equals(key)) {
                SchoolTimeInfoSourceEnum schoolTimeInfoSourceEnum = SchoolTimeInfoSourceEnum.getByType(Integer.parseInt(value));
                return schoolTimeInfoSourceEnum == null ? nullShow : schoolTimeInfoSourceEnum.getName();
            }
        }
        return value;
    }

    public String updateSchoolTimeTermEndToDesc(String key, String value) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.service.sc.WmScLogSchoolInfoService.updateSchoolTimeTermEndToDesc(java.lang.String,java.lang.String)");
        if (!nullShow.equals(value) && StringUtils.isNotEmpty(value)) {
            // 放假安排
            if (TIME_FIELD_TERM_END_PLAN.equals(key)) {
                SchoolTermEndPlanEnum schoolTermEndPlanEnum = SchoolTermEndPlanEnum.of(Integer.parseInt(value));
                return schoolTermEndPlanEnum == null ? nullShow : schoolTermEndPlanEnum.getDesc();
            }
            // 放假资料来源
            if (TIME_FIELD_TERM_END_INFO_SOURCE.equals(key)) {
                SchoolTimeInfoSourceEnum schoolTimeInfoSourceEnum = SchoolTimeInfoSourceEnum.getByType(Integer.parseInt(value));
                return schoolTimeInfoSourceEnum == null ? nullShow : schoolTimeInfoSourceEnum.getName();
            }
        }
        return value;
    }

    /**
     * 组装学校时间信息的删除日志
     * @param wmScTimeInfoBo
     */
    public String composeSchoolTimeInfoDeldateLog(WmScTimeInfoBo wmScTimeInfoBo) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.sc.WmScLogSchoolInfoService.composeSchoolTimeInfoDeldateLog(com.sankuai.meituan.waimai.customer.domain.sc.WmScTimeInfoBo)");
        StringBuilder stringBuilder = new StringBuilder().append("操作：删除\\n");
        stringBuilder.append("提交渠道：学校管理系统\\n");
        stringBuilder.append(String.format("%s~%s学年 %s", wmScTimeInfoBo.getYearBegin(), wmScTimeInfoBo.getYearEnd(), SchoolTermEnum.of(wmScTimeInfoBo.getTerm()).getDesc()));
        stringBuilder.append("\\n");
        schoolTimeInfoField.forEach((key, value) -> {
            stringBuilder.append("[字段变更] ");
            stringBuilder.append(schoolTimeInfoField.get(key));
            stringBuilder.append("：");
            Object afterValue = transSaveSchoolTimeInfoToDesc(key, wmScTimeInfoBo);
            if (afterValue == null || "".equals(afterValue)) {
                afterValue = nullShow;
            }
            stringBuilder.append(afterValue);
            stringBuilder.append("\\n");
        });
        // 开学信息
        schoolTimeTermBeginField.forEach((key, value) -> {
            stringBuilder.append("[字段变更] ");
            stringBuilder.append(schoolTimeTermBeginField.get(key));
            stringBuilder.append("：");
            Object afterValue = transSaveSchoolTimeTermBeginToDesc(key, wmScTimeInfoBo);
            if (afterValue == null || "".equals(afterValue)) {
                afterValue = nullShow;
            }
            stringBuilder.append(afterValue);
            stringBuilder.append("\\n");
        });
        // 放假信息
        schoolTimeTermEndField.forEach((key, value) -> {
            stringBuilder.append("[字段变更] ");
            stringBuilder.append(schoolTimeTermEndField.get(key));
            stringBuilder.append("：");
            Object afterValue = transSaveSchoolTimeTermEndToDesc(key, wmScTimeInfoBo);
            if (afterValue == null || "".equals(afterValue)) {
                afterValue = nullShow;
            }
            stringBuilder.append(afterValue);
            stringBuilder.append("\\n");
        });

        return stringBuilder.toString();
    }

    /**
     * 记录学校平台合作信息变更操作日志V2
     *
     * @param wmScSchoolCoPlatformLogBO wmScSchoolCoPlatformLogBO
     */
    public void recordSchoolCoPlatformUpdateLog(WmScSchoolCoPlatformLogBO wmScSchoolCoPlatformLogBO) {
        if (wmScSchoolCoPlatformLogBO == null
                || wmScSchoolCoPlatformLogBO.getWmScSchoolCoPlatformDOBefore() == null
                || wmScSchoolCoPlatformLogBO.getWmScSchoolCoPlatformDOAfter() == null) {
            log.warn("[WmScLogSchoolInfoService.wmScSchoolCoPlatformLogBO] input param is null. wmScSchoolCoPlatformLogBO = {}", JSONObject.toJSONString(wmScSchoolCoPlatformLogBO));
            return;
        }

        String logInfo = composeSchoolCoPlatformUpdateLog(wmScSchoolCoPlatformLogBO.getWmScSchoolCoPlatformDOBefore(), wmScSchoolCoPlatformLogBO.getWmScSchoolCoPlatformDOAfter());
        if (StringUtils.isNotBlank(logInfo)) {
            insertScOptLog(OptTypeEnum.UPDATE.getType(), SC_SCHOOL_PLATFORM_LOG, wmScSchoolCoPlatformLogBO.getWmScSchoolCoPlatformDOBefore().getSchoolPrimaryId(),
                    wmScSchoolCoPlatformLogBO.getUserId(), wmScSchoolCoPlatformLogBO.getUserName(), logInfo, "");
        }
    }

    /**
     * 记录学校履约管控信息变更操作日志V2
     *
     * @param wmScSchoolPerformanceLogBO wmScSchoolPerformanceLogBO
     */
    public void recordSchoolPerformanceUpdateLog(WmScSchoolPerformanceLogBO wmScSchoolPerformanceLogBO) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.sc.WmScLogSchoolInfoService.recordSchoolPerformanceUpdateLog(com.sankuai.meituan.waimai.customer.domain.sc.WmScSchoolPerformanceLogBO)");
        if (wmScSchoolPerformanceLogBO == null
                || wmScSchoolPerformanceLogBO.getWmScSchoolPerformanceDOBefore() == null
                || wmScSchoolPerformanceLogBO.getWmScSchoolPerformanceDOAfter() == null) {
            log.warn("[WmScLogSchoolInfoService.recordSchoolPerformanceUpdateLog] input param is null. wmScSchoolPerformanceLogBO = {}",
                    JSONObject.toJSONString(wmScSchoolPerformanceLogBO));
            return;
        }

        String logInfo = composeSchoolPerformanceUpdateLog(wmScSchoolPerformanceLogBO.getWmScSchoolPerformanceDOBefore(), wmScSchoolPerformanceLogBO.getWmScSchoolPerformanceDOAfter());
        if (StringUtils.isNotBlank(logInfo)) {
            insertScOptLog(OptTypeEnum.UPDATE.getType(), SC_SCHOOL_DELIVERY_LOG, wmScSchoolPerformanceLogBO.getWmScSchoolPerformanceDOBefore().getSchoolPrimaryId(),
                    wmScSchoolPerformanceLogBO.getUserId(), wmScSchoolPerformanceLogBO.getUserName(), logInfo, "");
        }
    }

    /**
     * 记录学校平台合作信息新增记录V2
     *
     * @param wmScSchoolCoPlatformLogBO wmScSchoolCoPlatformLogBO
     */
    public void recordSchoolCoPlatformInsertLog(WmScSchoolCoPlatformLogBO wmScSchoolCoPlatformLogBO) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_1", "com.sankuai.meituan.waimai.customer.service.sc.WmScLogSchoolInfoService.recordSchoolCoPlatformInsertLog(com.sankuai.meituan.waimai.customer.domain.sc.WmScSchoolCoPlatformLogBO)");
        if (wmScSchoolCoPlatformLogBO == null
                || wmScSchoolCoPlatformLogBO.getWmScSchoolCoPlatformDOInsert() == null) {
            log.warn("[WmScLogSchoolInfoService.recordSchoolCoPlatformInsertLog] input param is null. wmScSchoolPlatformLogBO = {}",
                    JSONObject.toJSONString(wmScSchoolCoPlatformLogBO));
            return;
        }

        String logInfo = composeSchoolCoPlatformInsertLog(wmScSchoolCoPlatformLogBO.getWmScSchoolCoPlatformDOInsert());
        if (StringUtils.isNotBlank(logInfo)) {
            insertScOptLog(OptTypeEnum.INSERT.getType(), SC_SCHOOL_PLATFORM_LOG, wmScSchoolCoPlatformLogBO.getWmScSchoolCoPlatformDOInsert().getSchoolPrimaryId(),
                    wmScSchoolCoPlatformLogBO.getUserId(), wmScSchoolCoPlatformLogBO.getUserName(), logInfo, "");
        }
    }

    /**
     * 记录学校履约管控信息新增记录V2
     *
     * @param wmScSchoolPerformanceLogBO wmScSchoolPerformanceLogBO
     */
    public void recordSchoolPerformanceInsertLog(WmScSchoolPerformanceLogBO wmScSchoolPerformanceLogBO) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_1", "com.sankuai.meituan.waimai.customer.service.sc.WmScLogSchoolInfoService.recordSchoolPerformanceInsertLog(com.sankuai.meituan.waimai.customer.domain.sc.WmScSchoolPerformanceLogBO)");
        if (wmScSchoolPerformanceLogBO == null
                || wmScSchoolPerformanceLogBO.getWmScSchoolPerformanceDOInsert() == null) {
            log.warn("[WmScLogSchoolInfoService.recordSchoolPerformanceInsertLog] input param is null. wmScSchoolPerformanceLogBO = {}",
                    JSONObject.toJSONString(wmScSchoolPerformanceLogBO));
            return;
        }

        String logInfo = composeSchoolPerformanceInsertLog(wmScSchoolPerformanceLogBO.getWmScSchoolPerformanceDOInsert());
        if (StringUtils.isNotBlank(logInfo)) {
            insertScOptLog(OptTypeEnum.INSERT.getType(), SC_SCHOOL_DELIVERY_LOG, wmScSchoolPerformanceLogBO.getWmScSchoolPerformanceDOInsert().getSchoolPrimaryId(),
                    wmScSchoolPerformanceLogBO.getUserId(), wmScSchoolPerformanceLogBO.getUserName(), logInfo, "");
        }
    }

    /**
     * 组装学校平台合作信息新增操作记录V2
     *
     * @param wmScSchoolCoPlatformDOInsert wmScSchoolCoPlatformDOInsert
     * @return String
     */
    public String composeSchoolCoPlatformInsertLog(WmScSchoolCoPlatformDO wmScSchoolCoPlatformDOInsert) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_1", "com.sankuai.meituan.waimai.customer.service.sc.WmScLogSchoolInfoService.composeSchoolCoPlatformInsertLog(com.sankuai.meituan.waimai.customer.domain.sc.WmScSchoolCoPlatformDO)");
        StringBuilder stringBuilder = new StringBuilder().append("操作：新建\\n");
        stringBuilder.append("提交渠道：学校管理系统\\n");
        stringBuilder.append("平台合作信息ID：").append(wmScSchoolCoPlatformDOInsert.getId()).append("\\n");
        schoolCoPlatformField.forEach((key, value) -> {
            Object afterValue = transInsertSchoolCoPlatformToDesc(key, wmScSchoolCoPlatformDOInsert);
            if (afterValue == null || StringUtils.isBlank(afterValue.toString())) {
                return;
            }
            stringBuilder.append("[字段变更] ");
            stringBuilder.append(schoolCoPlatformField.get(key));
            stringBuilder.append(": ");
            stringBuilder.append(afterValue);
            stringBuilder.append("\\n");
        });
        // 营销活动信息
        if (CollectionUtils.isEmpty(wmScSchoolCoPlatformDOInsert.getWmScSchoolCoPlatformMarketDOList())) {
            return stringBuilder.toString();
        }
        for (WmScSchoolCoPlatformMarketDO wmScSchoolCoPlatformMarketDo : wmScSchoolCoPlatformDOInsert.getWmScSchoolCoPlatformMarketDOList()) {
            stringBuilder.append("【营销活动信息】\\n");
            schoolCoPlatformMarketField.forEach((key, value) -> {
                Object afterValue = transInsertSchoolCoPlatformMarketToDesc(key, wmScSchoolCoPlatformMarketDo);
                if (afterValue == null || StringUtils.isBlank(afterValue.toString())) {
                    return;
                }
                stringBuilder.append("[字段变更] ");
                stringBuilder.append(schoolCoPlatformMarketField.get(key));
                stringBuilder.append(": ");
                stringBuilder.append(afterValue);
                stringBuilder.append("\\n");
            });
        }
        return stringBuilder.toString();
    }

    /**
     * 组装学校履约管控信息新增操作记录V2
     *
     * @param wmScSchoolPerformanceDOInsert wmScSchoolPerformanceDOInsert
     * @return 新增操作记录
     */
    public String composeSchoolPerformanceInsertLog(WmScSchoolPerformanceDO wmScSchoolPerformanceDOInsert) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.sc.WmScLogSchoolInfoService.composeSchoolPerformanceInsertLog(com.sankuai.meituan.waimai.customer.domain.sc.WmScSchoolPerformanceDO)");
        StringBuilder stringBuilder = new StringBuilder().append("操作：新建\\n");
        stringBuilder.append("提交渠道：学校管理系统\\n");
        // 校方态度信息
        schoolPerformanceFiled.forEach((key, value) -> {
            Object afterValue = transInsertSchoolPerformanceToDesc(key, wmScSchoolPerformanceDOInsert);
            if (afterValue == null || StringUtils.isBlank(afterValue.toString())) {
                return;
            }
            stringBuilder.append("[字段变更] ");
            stringBuilder.append(schoolPerformanceFiled.get(key));
            stringBuilder.append(": ");
            stringBuilder.append(afterValue);
            stringBuilder.append("\\n");
        });
        // 履约能力信息
        if (CollectionUtils.isEmpty(wmScSchoolPerformanceDOInsert.getWmScSchoolPerformanceUnitDOList())) {
            return stringBuilder.toString();
        }
        stringBuilder.append("【履约管控信息】\\n");
        for (WmScSchoolPerformanceUnitDO wmScSchoolPerformanceUnitDO : wmScSchoolPerformanceDOInsert.getWmScSchoolPerformanceUnitDOList()) {
            schoolPerformanceUnitFiled.forEach((key, value) -> {
                Object afterValue = transInsertSchoolPerformanceUnitToDesc(key, wmScSchoolPerformanceUnitDO);
                if (afterValue == null || StringUtils.isBlank(afterValue.toString())) {
                    return;
                }
                stringBuilder.append("[字段变更] ");
                stringBuilder.append(schoolPerformanceUnitFiled.get(key));
                stringBuilder.append(": ");
                stringBuilder.append(afterValue);
                stringBuilder.append("\\n");
            });
        }

        return stringBuilder.toString();
    }

    public Object transInsertSchoolPerformanceToDesc(String key, WmScSchoolPerformanceDO wmScSchoolPerformanceDOInsert) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.service.sc.WmScLogSchoolInfoService.transInsertSchoolPerformanceToDesc(java.lang.String,com.sankuai.meituan.waimai.customer.domain.sc.WmScSchoolPerformanceDO)");
        Object trueValue = getFieldValueByFieldName(key, wmScSchoolPerformanceDOInsert);
        if (trueValue != null) {
            // 校方是否允许配送进校
            if (SCHOOL_ALLOW_DELIVERY_V3.equals(key)) {
                SchoolAllowDeliveryV2Enum deliveryV2Enum = SchoolAllowDeliveryV2Enum.getByType((int) trueValue);
                return deliveryV2Enum == null ? nullShow : deliveryV2Enum.getName();
            }

        }
        return trueValue;
    }

    public Object transInsertSchoolPerformanceUnitToDesc(String key, WmScSchoolPerformanceUnitDO wmScSchoolPerformanceUnitDOInsert) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.sc.WmScLogSchoolInfoService.transInsertSchoolPerformanceUnitToDesc(String,WmScSchoolPerformanceUnitDO)");
        Object trueValue = getFieldValueByFieldName(key, wmScSchoolPerformanceUnitDOInsert);
        if (trueValue != null) {
            // 校外送校内的配送方式
            if (SCHOOL_IN_DELIVERY_TYPE_V3.equals(key)) {
                SchoolInDeliveryTypeEnum schoolInDeliveryTypeEnum = SchoolInDeliveryTypeEnum.getByType((int) trueValue);
                return schoolInDeliveryTypeEnum == null ? nullShow : schoolInDeliveryTypeEnum.getName();
            }
            // 具体使用的自配方式
            if (POI_SELF_DELIVERY_TYPE_V3.equals(key)) {
                SchoolPoiSelfDeliveryTypeEnum selfDeliveryTypeEnum = SchoolPoiSelfDeliveryTypeEnum.getByType((int) trueValue);
                return selfDeliveryTypeEnum == null ? nullShow : selfDeliveryTypeEnum.getName();
            }
            // 该配送方式可将餐品送到的具体位置
            if (DELIVERY_SPECIFIC_LOCATION_V3.equals(key)) {
                SchoolDeliverySpecificLocationEnum specificLocationEnum = SchoolDeliverySpecificLocationEnum.getByType((int) trueValue);
                return specificLocationEnum == null ? nullShow : specificLocationEnum.getName();
            }
            //  不能送到校门口原因
            if (DELIVERY_NOT_GATE_REASON_V3.equals(key)) {
                SchoolDeliveryNotGateReasonEnum notGateReasonEnum = SchoolDeliveryNotGateReasonEnum.getByType((int) trueValue);
                return notGateReasonEnum == null ? nullShow : notGateReasonEnum.getName();
            }
            // 不能进校原因
            if (DELIVERY_NOT_ENTER_REASON_V3.equals(key)) {
                SchoolDeliveryNotEnterReasonEnum notEnterReasonEnum = SchoolDeliveryNotEnterReasonEnum.getByType((int) trueValue);
                return notEnterReasonEnum == null ? nullShow : notEnterReasonEnum.getName();
            }
            // 可以进校的原因
            if (DELIVERY_ENTER_REASON_V3.equals(key)) {
                SchoolDeliveryEnterReasonEnum enterReasonEnum = SchoolDeliveryEnterReasonEnum.getByType((int) trueValue);
                return enterReasonEnum == null ? nullShow : enterReasonEnum.getName();
            }
            // 可送餐上楼原因
            if (DELIVERY_UPSTAIRS_REASON_V3.equals(key)) {
                SchoolDeliveryUpstairsReasonEnum upstairsReasonEnum = SchoolDeliveryUpstairsReasonEnum.getByType((int) trueValue);
                return upstairsReasonEnum == null ? nullShow : upstairsReasonEnum.getName();
            }

        }
        return trueValue;
    }

    public Object transInsertSchoolCoPlatformToDesc(String key, WmScSchoolCoPlatformDO wmScSchoolCoPlatformDOInsert) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_1", "com.sankuai.meituan.waimai.customer.service.sc.WmScLogSchoolInfoService.transInsertSchoolCoPlatformToDesc(java.lang.String,com.sankuai.meituan.waimai.customer.domain.sc.WmScSchoolCoPlatformDO)");
        Object trueValue = getFieldValueByFieldName(key, wmScSchoolCoPlatformDOInsert);
        if (trueValue != null) {
            // 合作平台
            if (COOPERATION_PLATFORM_V2.equals(key) && !"0".equals(trueValue.toString())) {
                WmScEnumDictDO wmScEnumDictDO = wmScEnumDictMapper.selectByEnumTypeAndEnumCode((int) WmScEnumTypeEnum.COOPERATION_PLATFORM_V2.getType(), (int) trueValue);
                return wmScEnumDictDO == null ? nullShow : wmScEnumDictDO.getEnumDesc();
            }
            // 平台名称
            if (PLATFORM_NAME_V2.equals(key) && StringUtils.isBlank(trueValue.toString())) {
                return nullShow;
            }

            // 美团收费和合作平台对比
            if (COMPARE_COOPERATION_PLATFORM.equals(key)) {
                SchoolCompareCooperationPlatformEnum compareCooperationPlatformEnum = SchoolCompareCooperationPlatformEnum.getByType((int) trueValue);
                return compareCooperationPlatformEnum == null ? nullShow : compareCooperationPlatformEnum.getName();
            }

            // 合作平台是否可进校
            if (PLATFORM_ALLOW_TO_SCHOOL.equals(key)) {
                SchoolPlatformAllowToSchoolEnum allowToSchoolEnum = SchoolPlatformAllowToSchoolEnum.getByType((int) trueValue);
                return allowToSchoolEnum == null ? nullShow : allowToSchoolEnum.getName();
            }
            // 合作平台支持送餐上楼
            if (SUPPORT_FOOD_UPSTAIRS_V2.equals(key)) {
                SchoolPlatformSupportFoodUpstairsV2Enum supportFoodUpstairsEnum = SchoolPlatformSupportFoodUpstairsV2Enum.getByType((int) trueValue);
                return supportFoodUpstairsEnum == null ? nullShow : supportFoodUpstairsEnum.getName();
            }

            // 送餐上楼费用
            if (FOOD_UPSTAIRS_FEE_V2.equals(key) && StringUtils.isBlank(trueValue.toString())) {
                return nullShow;
            }

            // 合作平台与学校达成合作时间
            if (PLATFORM_ESTABLISH_TIME_V2.equals(key) && StringUtils.isBlank(trueValue.toString())) {
                return nullShow;
            }
            // 供给分布
            if (SUPPLY_DISTRIBUTION.equals(key)) {
                SchoolPlatformSupplyDistributionEnum supplyDistributionEnum = SchoolPlatformSupplyDistributionEnum.getByType((int) trueValue);
                return supplyDistributionEnum == null ? nullShow : supplyDistributionEnum.getName();
            }
            // 垄断形式
            if (MONOPOLY_FORM.equals(key)) {
                SchoolPlatformMonopolyFormEnum monopolyFormEnum = SchoolPlatformMonopolyFormEnum.getByType((int) trueValue);
                return monopolyFormEnum == null ? nullShow : monopolyFormEnum.getName();
            }

            // 合作平台的配送收费方式
            if (DELIVERY_FEE_TYPE_V2.equals(key)) {
                return transInsertDeliveryFeeTypeToDesc(key, wmScSchoolCoPlatformDOInsert);
            }

            // 送餐上楼原因（多选）
            if (FOOD_UPSTAIRS_REASON_V2.equals(key)) {
                return transInsertFoodUpstairsReasonToDesc(key, wmScSchoolCoPlatformDOInsert);
            }

            // 合作平台的优势（多选）
            if (PLATFORM_ESTABLISH_ADVANTAGE_V2.equals(key)) {
                return transInsertPlatformEstablishAdvantageToDesc(key, wmScSchoolCoPlatformDOInsert);
            }

            // 相关字段其他信息
            return transInsertExtraInfoToDesc(key, wmScSchoolCoPlatformDOInsert);

        }
        return trueValue;
    }

    private Object transInsertExtraInfoToDesc(String key, WmScSchoolCoPlatformDO wmScSchoolCoPlatformDOInsert) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.sc.WmScLogSchoolInfoService.transInsertExtraInfoToDesc(java.lang.String,com.sankuai.meituan.waimai.customer.domain.sc.WmScSchoolCoPlatformDO)");
        Object trueValue = getFieldValueByFieldName(key, wmScSchoolCoPlatformDOInsert);
        // 校内商家单量
        if ((SCHOOL_IN_POI_ORDER_COUNT.equals(key)
                || SCHOOL_OUT_POI_ORDER_COUNT.equals(key)
                || SCHOOL_IN_ONLINE_POI_COUNT.equals(key)
                || SCHOOL_OUT_ONLINE_POI_COUNT.equals(key))
                && StringUtils.isBlank(trueValue.toString())) {
            return nullShow;
        }
        // 当合作平台的收费方式选择为“其他”时填写的内容
        if (DELIVERY_FEE_TYPE_INFO.equals(key) && StringUtils.isBlank(trueValue.toString())) {
            return nullShow;
        }
        // 当美团收费和合作平台对比选择了“无法比较”时填写的内容
        if (COMPARE_COOPERATION_PLATFORM_INFO.equals(key) && StringUtils.isBlank(trueValue.toString())) {
            return nullShow;
        }
        // 当合作平台支持送餐上楼选择了“其他”时填写的内容
        if (SUPPORT_FOOD_UPSTAIRS_INFO.equals(key) && StringUtils.isBlank(trueValue.toString())) {
            return nullShow;
        }
        // 当送餐上楼原因选择了其他时填写的内容
        if (FOOD_UPSTAIRS_REASON_INFO.equals(key) && StringUtils.isBlank(trueValue.toString())) {
            return nullShow;
        }
        // 当合作平台的优势选择了“有用户增值服务（如代取快递）”填写的内容
        if ((ADVANTAGE_ADDED_SERVICE_INFO.equals(key)
                || ADVANTAGE_GOOD_EXPERIENCE_INFO.equals(key)
                || ADVANTAGE_ATTRACTION_INFO.equals(key)
                || ADVANTAGE_PROPAGANDA_INFO.equals(key)
                || ADVANTAGE_EXTRA_INFO.equals(key)) && StringUtils.isBlank(trueValue.toString())) {
            return nullShow;
        }

        // 其他信息
        if (EXTRA_INFO_V2.equals(key) && StringUtils.isBlank(trueValue.toString())) {
            return nullShow;
        }

        return trueValue;
    }

    private Object transInsertDeliveryFeeTypeToDesc(String key, WmScSchoolCoPlatformDO wmScSchoolCoPlatformDOInsert) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.sc.WmScLogSchoolInfoService.transInsertDeliveryFeeTypeToDesc(java.lang.String,com.sankuai.meituan.waimai.customer.domain.sc.WmScSchoolCoPlatformDO)");
        Object trueValue = getFieldValueByFieldName(key, wmScSchoolCoPlatformDOInsert);
        if (StringUtils.isBlank(trueValue.toString())) {
            return nullShow;
        } else {
            List<WmScSchoolCoPlatformFeeTypeDTO> feeTypeDTOList = JSONObject.parseArray(trueValue.toString(), WmScSchoolCoPlatformFeeTypeDTO.class);
            if (CollectionUtils.isEmpty(feeTypeDTOList)) {
                return nullShow;
            }
            List<String> feeType = new ArrayList<>();

            for (WmScSchoolCoPlatformFeeTypeDTO wmScSchoolCoPlatformFeeTypeDTO : feeTypeDTOList) {
                if (wmScSchoolCoPlatformFeeTypeDTO.getOption() == (int) SchoolPlatformDeliveryFeeTypeV2Enum.FREE.getType()
                        || wmScSchoolCoPlatformFeeTypeDTO.getOption() == (int) SchoolPlatformDeliveryFeeTypeV2Enum.OTHERS.getType()) {
                    feeType.add(SchoolPlatformDeliveryFeeTypeV2Enum.getByType(wmScSchoolCoPlatformFeeTypeDTO.getOption()).getName());
                } else {
                    feeType.add(SchoolPlatformDeliveryFeeTypeV2Enum.getByType(wmScSchoolCoPlatformFeeTypeDTO.getOption()).getName() + "-"
                            + SchoolPlatformDeliveryFeeTypeModeEnum.getByType(wmScSchoolCoPlatformFeeTypeDTO.getMode()).getName() + "-"
                            + wmScSchoolCoPlatformFeeTypeDTO.getValue());
                }
            }
            return StringUtils.join(feeType, "、");
        }
    }

    private Object transInsertFoodUpstairsReasonToDesc(String key, WmScSchoolCoPlatformDO wmScSchoolCoPlatformDOInsert) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_2", "com.sankuai.meituan.waimai.customer.service.sc.WmScLogSchoolInfoService.transInsertFoodUpstairsReasonToDesc(java.lang.String,com.sankuai.meituan.waimai.customer.domain.sc.WmScSchoolCoPlatformDO)");
        Object trueValue = getFieldValueByFieldName(key, wmScSchoolCoPlatformDOInsert);
        if (StringUtils.isBlank((String) trueValue)) {
            return nullShow;
        } else {
            List<Integer> reasonList = Arrays.stream(((String) trueValue).split(","))
                    .map(Integer::parseInt)
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(reasonList)) {
                return nullShow;
            }
            List<String> reasonDesc = new ArrayList<>();
            for (Integer reason : reasonList) {
                reasonDesc.add(SchoolPlatformFoodUpstairsReasonEnum.getByType(reason).getName());
            }
            return StringUtils.join(reasonDesc, "、");
        }
    }

    private Object transInsertPlatformEstablishAdvantageToDesc(String key, WmScSchoolCoPlatformDO wmScSchoolCoPlatformDOInsert) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.service.sc.WmScLogSchoolInfoService.transInsertPlatformEstablishAdvantageToDesc(String,WmScSchoolCoPlatformDO)");
        Object trueValue = getFieldValueByFieldName(key, wmScSchoolCoPlatformDOInsert);
        if (StringUtils.isBlank((String) trueValue)) {
            return nullShow;
        } else {
            List<Integer> reasonList = Arrays.stream(((String) trueValue).split(","))
                    .map(Integer::parseInt)
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(reasonList)) {
                return nullShow;
            }
            List<String> reasonDesc = new ArrayList<>();
            for (Integer reason : reasonList) {
                reasonDesc.add(SchoolPlatformEstablishAdvantageEnum.getByType(reason).getName());
            }
            return StringUtils.join(reasonDesc, "、");
        }
    }

    private Object transInsertSchoolCoPlatformMarketToDesc(String key, WmScSchoolCoPlatformMarketDO wmScSchoolCoPlatformMarketDOInsert) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.sc.WmScLogSchoolInfoService.transInsertSchoolCoPlatformMarketToDesc(String,WmScSchoolCoPlatformMarketDO)");
        Object trueValue = getFieldValueByFieldName(key, wmScSchoolCoPlatformMarketDOInsert);
        if (trueValue != null) {
            // 合作平台营销活动
            if (PLATFORM_MARKETING_ACTIVITY.equals(key)) {
                SchoolPlatformMarketingActivityEnum marketingActivityEnum = SchoolPlatformMarketingActivityEnum.getByType((int) trueValue);
                return marketingActivityEnum == null ? nullShow : marketingActivityEnum.getName();
            }
            // 合作平台营销活动选择了其他时填写的内容
            if (PLATFORM_MARKETING_ACTIVITY_INFO.equals(key) && StringUtils.isBlank(trueValue.toString())) {
                return nullShow;
            }
            // 活动规则描述
            if (ACTIVITY_RULE_DESCRIPTION.equals(key) && StringUtils.isBlank(trueValue.toString())) {
                return nullShow;
            }
            // 活动截图
            if (ACTIVITY_PIC.equals(key) && StringUtils.isBlank(trueValue.toString())) {
                return nullShow;
            }
            // 活动成本分摊类型
            if (ACTIVITY_COST_SHARING_TYPE.equals(key)) {
                SchoolPlatformActivityCostSharingTypeEnum activityCostSharingTypeEnum = SchoolPlatformActivityCostSharingTypeEnum.getByType((int) trueValue);
                return activityCostSharingTypeEnum == null ? nullShow : activityCostSharingTypeEnum.getName();
            }
            // 活动成本分摊范围，商家-最小值
            if (ACTIVITY_COST_SHARING_POI_MIN.equals(key) && StringUtils.isBlank(trueValue.toString())) {
                return nullShow;
            }
            // 活动成本分摊范围，商家-最大值
            if (ACTIVITY_COST_SHARING_POI_MAX.equals(key) && StringUtils.isBlank(trueValue.toString())) {
                return nullShow;
            }
            // 活动成本分摊范围，平台-最小值
            if (ACTIVITY_COST_SHARING_PLATFORM_MIN.equals(key) && StringUtils.isBlank(trueValue.toString())) {
                return nullShow;
            }
            // 活动成本分摊范围，平台-最大值
            if (ACTIVITY_COST_SHARING_PLATFORM_MAX.equals(key) && StringUtils.isBlank(trueValue.toString())) {
                return nullShow;
            }
        }
        return trueValue;
    }

    public String composeSchoolPerformanceUpdateLog(WmScSchoolPerformanceDO wmScSchoolPerformanceDOBefore,
                                                    WmScSchoolPerformanceDO wmScSchoolPerformanceDOAfter) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_1", "com.sankuai.meituan.waimai.customer.service.sc.WmScLogSchoolInfoService.composeSchoolPerformanceUpdateLog(WmScSchoolPerformanceDO,WmScSchoolPerformanceDO)");
        List<WmCustomerDiffCellBo> diffList = Lists.newArrayList();
        StringBuilder stringBuilder = new StringBuilder().append("操作：修改\\n");
        stringBuilder.append("提交渠道：学校管理系统\\n");
        // 校方态度信息
        try {
            diffList = DiffUtil.compare(wmScSchoolPerformanceDOBefore, wmScSchoolPerformanceDOAfter, schoolPerformanceFiled);
        } catch (Exception e) {
            log.error("[WmScLogSchoolInfoService.composeSchoolPerformanceUpdateLog] 插入学校平台信息日志异常, wmScSchoolPerformanceDOBefore = {}, wmScSchoolPerformanceDOAfter = {} ",
                    JSONObject.toJSONString(wmScSchoolPerformanceDOBefore), JSONObject.toJSONString(wmScSchoolPerformanceDOAfter), e);
        }

        log.info("[WmScLogSchoolInfoService.composeSchoolPerformanceUpdateLog] diffList = {}", JSONObject.toJSONString(diffList));
        for (WmCustomerDiffCellBo wmCustomerDiffCellBo : diffList) {
            // 更新前的值
            String preValue = wmCustomerDiffCellBo.getPre();
            if (StringUtils.isEmpty(preValue)) {
                preValue = nullShow;
            }
            preValue = transUpdateSchoolPerformanceToDesc(wmCustomerDiffCellBo.getField(), preValue);
            // 更新后的值
            String afterValue = transUpdateSchoolPerformanceToDesc(wmCustomerDiffCellBo.getField(), wmCustomerDiffCellBo.getAft());
            if (StringUtils.isEmpty(afterValue)) {
                afterValue = nullShow;
            }
            if (preValue.equals(afterValue) && nullShow.equals(preValue)) {
                continue;
            }
            stringBuilder.append("[字段变更] ");
            stringBuilder.append(schoolDeliveryFiled.get(wmCustomerDiffCellBo.getField()));
            stringBuilder.append(": ").append(preValue);
            stringBuilder.append("=>");
            stringBuilder.append(afterValue);
            stringBuilder.append("\\n");
        }
        // 履约能力信息
        try {
            int unitDOBeforeListSize = wmScSchoolPerformanceDOBefore.getWmScSchoolPerformanceUnitDOList().size();
            int unitDOAfterListSize = wmScSchoolPerformanceDOAfter.getWmScSchoolPerformanceUnitDOList().size();
            if (unitDOBeforeListSize != unitDOAfterListSize) {
                log.error("[WmScLogSchoolInfoService.composeSchoolPerformanceUpdateLog] list size is not equal. " +
                                "wmScSchoolPerformanceDOBefore = {}, wmScSchoolPerformanceDOAfter = {}",
                        JSONObject.toJSONString(wmScSchoolPerformanceDOBefore), JSONObject.toJSONString(wmScSchoolPerformanceDOAfter));
                throw new WmSchCantException(WmScCodeConstants.BIZ_PARA_ERROR, "记录学校履约管控信息变更操作日志V2异常");
            }
            for (int i = 0; i < unitDOBeforeListSize; i++) {
                diffList = DiffUtil.compare(wmScSchoolPerformanceDOBefore.getWmScSchoolPerformanceUnitDOList().get(i)
                        , wmScSchoolPerformanceDOAfter.getWmScSchoolPerformanceUnitDOList().get(i), schoolPerformanceUnitFiled);
                log.info("[WmScLogSchoolInfoService.composeSchoolPerformanceUpdateLog] diffList = {}", JSONObject.toJSONString(diffList));
                if (CollectionUtils.isEmpty(diffList)) {
                    continue;
                }
                stringBuilder.append("【履约管控信息】\\n");
                for (WmCustomerDiffCellBo wmCustomerDiffCellBo : diffList) {
                    // 更新前的值
                    String preValue = wmCustomerDiffCellBo.getPre();
                    if (StringUtils.isEmpty(preValue)) {
                        preValue = nullShow;
                    }
                    preValue = transUpdateSchoolPerformanceUnitToDesc(wmCustomerDiffCellBo.getField(), preValue);
                    // 更新后的值
                    String afterValue = transUpdateSchoolPerformanceUnitToDesc(wmCustomerDiffCellBo.getField(), wmCustomerDiffCellBo.getAft());
                    if (StringUtils.isEmpty(afterValue)) {
                        afterValue = nullShow;
                    }
                    if (preValue.equals(afterValue) && nullShow.equals(preValue)) {
                        continue;
                    }
                    stringBuilder.append("[字段变更] ");
                    stringBuilder.append(schoolDeliveryFiled.get(wmCustomerDiffCellBo.getField()));
                    stringBuilder.append(": ").append(preValue);
                    stringBuilder.append("=>");
                    stringBuilder.append(afterValue);
                    stringBuilder.append("\\n");
                }
            }
        } catch (Exception e) {
            log.error("[WmScLogSchoolInfoService.composeSchoolPerformanceUpdateLog] 插入学校平台信息日志异常, wmScSchoolPerformanceDOBefore = {}, wmScSchoolPerformanceDOAfter = {} ",
                    JSONObject.toJSONString(wmScSchoolPerformanceDOBefore), JSONObject.toJSONString(wmScSchoolPerformanceDOAfter), e);
        }

        log.info("[WmScLogSchoolInfoService.composeSchoolDeliveryUpdateLog] info = {}", stringBuilder);
        return stringBuilder.toString();

    }

    /**
     * 组装学校平台合作信息的更新日志V2
     *
     * @param wmScSchoolCoPlatformDoBefore 更新前DO
     * @param wmScSchoolCoPlatformDoAfter  更新后DO
     * @return 更新日志
     */
    public String composeSchoolCoPlatformUpdateLog(WmScSchoolCoPlatformDO wmScSchoolCoPlatformDoBefore,
                                                 WmScSchoolCoPlatformDO wmScSchoolCoPlatformDoAfter) {
        List<WmCustomerDiffCellBo> diffList = Lists.newArrayList();
        StringBuilder stringBuilder = new StringBuilder().append("操作：修改\\n");
        stringBuilder.append("平台合作信息ID：").append(wmScSchoolCoPlatformDoBefore.getId()).append("\\n");
        // 学校合作平台信息
        try {
            diffList = DiffUtil.compare(wmScSchoolCoPlatformDoBefore, wmScSchoolCoPlatformDoAfter, schoolCoPlatformField);
        } catch (Exception e) {
            log.error("[WmScLogSchoolInfoService.composeSchoolCoPlatformUpdateLog] 插入学校平台信息日志异常, wmScSchoolCoPlatformDoBefore = {}, wmScSchoolCoPlatformDoAfter = {} ",
                    JSONObject.toJSONString(wmScSchoolCoPlatformDoBefore), JSONObject.toJSONString(wmScSchoolCoPlatformDoAfter), e);
        }

        log.info("[WmScLogSchoolInfoService.composeSchoolCoPlatformUpdateLog] diffList = {}", JSONObject.toJSONString(diffList));

        for (WmCustomerDiffCellBo wmCustomerDiffCellBo : diffList) {
            // 更新前的值
            String preValue = wmCustomerDiffCellBo.getPre();
            if (StringUtils.isEmpty(preValue)) {
                preValue = nullShow;
            }
            preValue = transUpdateSchoolCoPlatformToDesc(wmCustomerDiffCellBo.getField(), preValue, wmScSchoolCoPlatformDoBefore.getSchoolPrimaryId());
            // 更新后的值
            String afterValue = transUpdateSchoolCoPlatformToDesc(wmCustomerDiffCellBo.getField(), wmCustomerDiffCellBo.getAft(), wmScSchoolCoPlatformDoBefore.getSchoolPrimaryId());
            if (StringUtils.isEmpty(afterValue)) {
                afterValue = nullShow;
            }
            if (preValue.equals(afterValue) && nullShow.equals(preValue)) {
                continue;
            }
            stringBuilder.append("[字段变更] ");
            stringBuilder.append(schoolCoPlatformField.get(wmCustomerDiffCellBo.getField()));
            stringBuilder.append(": ").append(preValue);
            stringBuilder.append("=>");
            stringBuilder.append(afterValue);
            stringBuilder.append("\\n");
        }
        // 学校合作平台营销活动信息
        try {
            int marketDoBeforeListSize = wmScSchoolCoPlatformDoBefore.getWmScSchoolCoPlatformMarketDOList().size();
            int marketDoAfterListSize = wmScSchoolCoPlatformDoAfter.getWmScSchoolCoPlatformMarketDOList().size();
            if (marketDoBeforeListSize != marketDoAfterListSize) {
                log.error("[WmScLogSchoolInfoService.composeSchoolCoPlatformUpdateLog] list size is not equal. " +
                                "wmScSchoolCoPlatformDoBefore = {}, wmScSchoolCoPlatformDoAfter = {}",
                        JSONObject.toJSONString(wmScSchoolCoPlatformDoBefore), JSONObject.toJSONString(wmScSchoolCoPlatformDoAfter));
                throw new WmSchCantException(WmScCodeConstants.BIZ_PARA_ERROR, "记录学校合作平台信息变更操作日志V2异常");
            }
            for (int i = 0; i < marketDoBeforeListSize; i++) {
                diffList = DiffUtil.compare(wmScSchoolCoPlatformDoBefore.getWmScSchoolCoPlatformMarketDOList().get(i)
                        , wmScSchoolCoPlatformDoAfter.getWmScSchoolCoPlatformMarketDOList().get(i), schoolCoPlatformMarketField);
                log.info("[WmScLogSchoolInfoService.composeSchoolCoPlatformUpdateLog] diffList = {}", JSONObject.toJSONString(diffList));
                if (CollectionUtils.isEmpty(diffList)) {
                    continue;
                }
                stringBuilder.append("【营销活动信息】\\n");
                for (WmCustomerDiffCellBo wmCustomerDiffCellBo : diffList) {
                    // 更新前的值
                    String preValue = wmCustomerDiffCellBo.getPre();
                    if (StringUtils.isEmpty(preValue)) {
                        preValue = nullShow;
                    }
                    preValue = transUpdateSchoolCoPlatformMarketToDesc(wmCustomerDiffCellBo.getField(), preValue, wmScSchoolCoPlatformDoBefore.getSchoolPrimaryId());
                    // 更新后的值
                    String afterValue = transUpdateSchoolCoPlatformMarketToDesc(wmCustomerDiffCellBo.getField(), wmCustomerDiffCellBo.getAft(), wmScSchoolCoPlatformDoBefore.getSchoolPrimaryId());
                    if (StringUtils.isEmpty(afterValue)) {
                        afterValue = nullShow;
                    }
                    if (preValue.equals(afterValue) && nullShow.equals(preValue)) {
                        continue;
                    }
                    stringBuilder.append("[字段变更] ");
                    stringBuilder.append(schoolCoPlatformMarketField.get(wmCustomerDiffCellBo.getField()));
                    stringBuilder.append(": ").append(preValue);
                    stringBuilder.append("=>");
                    stringBuilder.append(afterValue);
                    stringBuilder.append("\\n");
                }
            }
        } catch (Exception e) {
            log.error("[WmScLogSchoolInfoService.composeSchoolCoPlatformUpdateLog] 插入学校平台信息日志异常, wmScSchoolCoPlatformDoBefore = {}, wmScSchoolCoPlatformDoAfter = {} ",
                    JSONObject.toJSONString(wmScSchoolCoPlatformDoBefore), JSONObject.toJSONString(wmScSchoolCoPlatformDoAfter), e);
        }

        log.info("[WmScLogSchoolInfoService.composeSchoolCoPlatformUpdateLog] info = {}", stringBuilder);
        return stringBuilder.toString();
    }

    public String transUpdateSchoolPerformanceToDesc(String key, String value) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.service.sc.WmScLogSchoolInfoService.transUpdateSchoolPerformanceToDesc(java.lang.String,java.lang.String)");
        if (!nullShow.equals(value) && StringUtils.isNotEmpty(value)) {
            // 校方是否允许配送进校
            if (SCHOOL_ALLOW_DELIVERY_V3.equals(key)) {
                SchoolAllowDeliveryV2Enum deliveryV2Enum = SchoolAllowDeliveryV2Enum.getByType(Integer.parseInt(value));
                return deliveryV2Enum == null ? nullShow : deliveryV2Enum.getName();
            }
        }
        return value;
    }

    public String transUpdateSchoolPerformanceUnitToDesc(String key, String value) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.service.sc.WmScLogSchoolInfoService.transUpdateSchoolPerformanceUnitToDesc(java.lang.String,java.lang.String)");
        if (!nullShow.equals(value) && StringUtils.isNotEmpty(value)) {
            // 校外送校内的配送方式
            if (SCHOOL_IN_DELIVERY_TYPE_V3.equals(key)) {
                SchoolInDeliveryTypeEnum schoolInDeliveryTypeEnum = SchoolInDeliveryTypeEnum.getByType((int) Integer.parseInt(value));
                return schoolInDeliveryTypeEnum == null ? nullShow : schoolInDeliveryTypeEnum.getName();
            }
            // 具体使用的自配方式
            if (POI_SELF_DELIVERY_TYPE_V3.equals(key)) {
                SchoolPoiSelfDeliveryTypeEnum selfDeliveryTypeEnum = SchoolPoiSelfDeliveryTypeEnum.getByType((int) Integer.parseInt(value));
                return selfDeliveryTypeEnum == null ? nullShow : selfDeliveryTypeEnum.getName();
            }
            // 该配送方式可将餐品送到的具体位置
            if (DELIVERY_SPECIFIC_LOCATION_V3.equals(key)) {
                SchoolDeliverySpecificLocationEnum specificLocationEnum = SchoolDeliverySpecificLocationEnum.getByType((int) Integer.parseInt(value));
                return specificLocationEnum == null ? nullShow : specificLocationEnum.getName();
            }
            //  不能送到校门口原因
            if (DELIVERY_NOT_GATE_REASON_V3.equals(key)) {
                SchoolDeliveryNotGateReasonEnum notGateReasonEnum = SchoolDeliveryNotGateReasonEnum.getByType((int) Integer.parseInt(value));
                return notGateReasonEnum == null ? nullShow : notGateReasonEnum.getName();
            }
            // 不能进校原因
            if (DELIVERY_NOT_ENTER_REASON_V3.equals(key)) {
                SchoolDeliveryNotEnterReasonEnum notEnterReasonEnum = SchoolDeliveryNotEnterReasonEnum.getByType((int) Integer.parseInt(value));
                return notEnterReasonEnum == null ? nullShow : notEnterReasonEnum.getName();
            }
            // 可以进校的原因
            if (DELIVERY_ENTER_REASON_V3.equals(key)) {
                SchoolDeliveryEnterReasonEnum enterReasonEnum = SchoolDeliveryEnterReasonEnum.getByType((int) Integer.parseInt(value));
                return enterReasonEnum == null ? nullShow : enterReasonEnum.getName();
            }
            // 可送餐上楼原因
            if (DELIVERY_UPSTAIRS_REASON_V3.equals(key)) {
                SchoolDeliveryUpstairsReasonEnum upstairsReasonEnum = SchoolDeliveryUpstairsReasonEnum.getByType((int) Integer.parseInt(value));
                return upstairsReasonEnum == null ? nullShow : upstairsReasonEnum.getName();
            }
        }
        return value;
    }

    public String transUpdateSchoolCoPlatformToDesc(String key, String value, Integer schoolPrimaryId) {
        if (!nullShow.equals(value) && StringUtils.isNotEmpty(value)) {
            // 平台名称
            if (PLATFORM_NAME_V2.equals(key) && StringUtils.isBlank(value)) {
                return nullShow;
            }

            // 美团收费和合作平台对比
            if (COMPARE_COOPERATION_PLATFORM.equals(key)) {
                SchoolCompareCooperationPlatformEnum compareCooperationPlatformEnum = SchoolCompareCooperationPlatformEnum.getByType(Integer.parseInt(value));
                return compareCooperationPlatformEnum == null ? nullShow : compareCooperationPlatformEnum.getName();
            }

            // 合作平台是否可进校
            if (PLATFORM_ALLOW_TO_SCHOOL.equals(key)) {
                SchoolPlatformAllowToSchoolEnum allowToSchoolEnum = SchoolPlatformAllowToSchoolEnum.getByType(Integer.parseInt(value));
                return allowToSchoolEnum == null ? nullShow : allowToSchoolEnum.getName();
            }
            // 合作平台支持送餐上楼
            if (SUPPORT_FOOD_UPSTAIRS_V2.equals(key)) {
                SchoolPlatformSupportFoodUpstairsV2Enum supportFoodUpstairsEnum = SchoolPlatformSupportFoodUpstairsV2Enum.getByType(Integer.parseInt(value));
                return supportFoodUpstairsEnum == null ? nullShow : supportFoodUpstairsEnum.getName();
            }

            // 送餐上楼费用
            if (FOOD_UPSTAIRS_FEE_V2.equals(key) && StringUtils.isBlank(value)) {
                return nullShow;
            }

            // 合作平台与学校达成合作时间
            if (PLATFORM_ESTABLISH_TIME_V2.equals(key) && StringUtils.isBlank(value)) {
                return nullShow;
            }

            // 供给分布
            if (SUPPLY_DISTRIBUTION.equals(key)) {
                SchoolPlatformSupplyDistributionEnum supplyDistributionEnum = SchoolPlatformSupplyDistributionEnum.getByType(Integer.parseInt(value));
                return supplyDistributionEnum == null ? nullShow : supplyDistributionEnum.getName();
            }
            // 垄断形式
            if (MONOPOLY_FORM.equals(key)) {
                SchoolPlatformMonopolyFormEnum monopolyFormEnum = SchoolPlatformMonopolyFormEnum.getByType(Integer.parseInt(value));
                return monopolyFormEnum == null ? nullShow : monopolyFormEnum.getName();
            }

            // 合作平台的配送收费方式
            if (DELIVERY_FEE_TYPE_V2.equals(key)) {
                return transUpdateDeliveryFeeTypeToDesc(key, value, schoolPrimaryId);
            }

            // 送餐上楼原因（多选）
            if (FOOD_UPSTAIRS_REASON_V2.equals(key)) {
                return transUpdateFoodUpstairsReasonToDesc(key, value, schoolPrimaryId);
            }

            // 合作平台的优势（多选）
            if (PLATFORM_ESTABLISH_ADVANTAGE_V2.equals(key)) {
                return transUpdatePlatformEstablishAdvantageToDesc(key, value, schoolPrimaryId);
            }

            // 相关字段其他信息
            return transUpdateExtraInfoToDesc(key, value, schoolPrimaryId);

        }
        return value;
    }

    private String transUpdateExtraInfoToDesc(String key, String value, Integer schoolPrimaryId) {
        // 校内商家单量
        if (SCHOOL_IN_POI_ORDER_COUNT.equals(key) && StringUtils.isBlank(value)) {
            return nullShow;
        }
        // 校外商家单量
        if (SCHOOL_OUT_POI_ORDER_COUNT.equals(key) && StringUtils.isBlank(value)) {
            return nullShow;
        }
        // 校内在线商家数
        if (SCHOOL_IN_ONLINE_POI_COUNT.equals(key) && StringUtils.isBlank(value)) {
            return nullShow;
        }
        // 校外在线商家数
        if (SCHOOL_OUT_ONLINE_POI_COUNT.equals(key) && StringUtils.isBlank(value)) {
            return nullShow;
        }
        // 当合作平台的收费方式选择为“其他”时填写的内容
        if (DELIVERY_FEE_TYPE_INFO.equals(key) && StringUtils.isBlank(value)) {
            return nullShow;
        }
        // 当美团收费和合作平台对比选择了“无法比较”时填写的内容
        if (COMPARE_COOPERATION_PLATFORM_INFO.equals(key) && StringUtils.isBlank(value)) {
            return nullShow;
        }
        // 当合作平台支持送餐上楼选择了“其他”时填写的内容
        if (SUPPORT_FOOD_UPSTAIRS_INFO.equals(key) && StringUtils.isBlank(value)) {
            return nullShow;
        }
        // 当送餐上楼原因选择了其他时填写的内容
        if (FOOD_UPSTAIRS_REASON_INFO.equals(key) && StringUtils.isBlank(value)) {
            return nullShow;
        }
        // 当合作平台的优势选择了“有用户增值服务（如代取快递）”填写的内容
        if (ADVANTAGE_ADDED_SERVICE_INFO.equals(key) && StringUtils.isBlank(value)) {
            return nullShow;
        }
        // 当合作平台的优势选择了“配送体验好（时效等）”填写的内容
        if (ADVANTAGE_GOOD_EXPERIENCE_INFO.equals(key) && StringUtils.isBlank(value)) {
            return nullShow;
        }
        // 当合作平台的优势选择了“有吸引力的C端活动（如发券）”填写的内容
        if (ADVANTAGE_ATTRACTION_INFO.equals(key) && StringUtils.isBlank(value)) {
            return nullShow;
        }
        // 当合作平台的优势选择了“宣传力度大（如私域流量，扫楼等）”填写的内容
        if (ADVANTAGE_PROPAGANDA_INFO.equals(key) && StringUtils.isBlank(value)) {
            return nullShow;
        }
        // 当合作平台的优势选择了“其他”填写的内容
        if (ADVANTAGE_EXTRA_INFO.equals(key) && StringUtils.isBlank(value)) {
            return nullShow;
        }
        // 其他信息
        if (EXTRA_INFO_V2.equals(key) && StringUtils.isBlank(value)) {
            return nullShow;
        }
        return value;
    }

    private String transUpdateDeliveryFeeTypeToDesc(String key, String value, Integer schoolPrimaryId) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.sc.WmScLogSchoolInfoService.transUpdateDeliveryFeeTypeToDesc(java.lang.String,java.lang.String,java.lang.Integer)");
        if (StringUtils.isBlank(value)) {
            return nullShow;
        } else {
            List<WmScSchoolCoPlatformFeeTypeDTO> feeTypeDTOList = JSONObject.parseArray(value, WmScSchoolCoPlatformFeeTypeDTO.class);
            if (CollectionUtils.isEmpty(feeTypeDTOList)) {
                return nullShow;
            }
            List<String> feeType = new ArrayList<>();
            for (WmScSchoolCoPlatformFeeTypeDTO wmScSchoolCoPlatformFeeTypeDTO : feeTypeDTOList) {
                if (wmScSchoolCoPlatformFeeTypeDTO.getOption() == (int) SchoolPlatformDeliveryFeeTypeV2Enum.FREE.getType()
                        || wmScSchoolCoPlatformFeeTypeDTO.getOption() == (int) SchoolPlatformDeliveryFeeTypeV2Enum.OTHERS.getType()) {
                    feeType.add(SchoolPlatformDeliveryFeeTypeV2Enum.getByType(wmScSchoolCoPlatformFeeTypeDTO.getOption()).getName());
                } else {
                    feeType.add(SchoolPlatformDeliveryFeeTypeV2Enum.getByType(wmScSchoolCoPlatformFeeTypeDTO.getOption()).getName() + "-"
                            + SchoolPlatformDeliveryFeeTypeModeEnum.getByType(wmScSchoolCoPlatformFeeTypeDTO.getMode()).getName() + "-"
                            + wmScSchoolCoPlatformFeeTypeDTO.getValue());
                }

            }
            return StringUtils.join(feeType, "、");
        }
    }

    private String transUpdateFoodUpstairsReasonToDesc(String key, String value, Integer schoolPrimaryId) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.sc.WmScLogSchoolInfoService.transUpdateFoodUpstairsReasonToDesc(java.lang.String,java.lang.String,java.lang.Integer)");
        if (StringUtils.isBlank(value)) {
            return nullShow;
        } else {
            List<Integer> reasonList = Arrays.stream(value.split(","))
                    .map(Integer::parseInt)
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(reasonList)) {
                return nullShow;
            }
            List<String> reasonDesc = new ArrayList<>();
            for (Integer reason : reasonList) {
                reasonDesc.add(SchoolPlatformFoodUpstairsReasonEnum.getByType(reason).getName());
            }
            return StringUtils.join(reasonDesc, "、");
        }
    }

    private String transUpdatePlatformEstablishAdvantageToDesc(String key, String value, Integer schoolPrimaryId) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_2", "com.sankuai.meituan.waimai.customer.service.sc.WmScLogSchoolInfoService.transUpdatePlatformEstablishAdvantageToDesc(java.lang.String,java.lang.String,java.lang.Integer)");
        if (StringUtils.isBlank(value)) {
            return nullShow;
        } else {
            List<Integer> reasonList = Arrays.stream(value.split(","))
                    .map(Integer::parseInt)
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(reasonList)) {
                return nullShow;
            }
            List<String> reasonDesc = new ArrayList<>();
            for (Integer reason : reasonList) {
                reasonDesc.add(SchoolPlatformEstablishAdvantageEnum.getByType(reason).getName());
            }
            return StringUtils.join(reasonDesc, "、");
        }
    }

    public String transUpdateSchoolCoPlatformMarketToDesc(String key, String value, Integer schoolPrimaryId) {
        if (!nullShow.equals(value) && StringUtils.isNotEmpty(value)) {
            // 合作平台营销活动
            if (PLATFORM_MARKETING_ACTIVITY.equals(key)) {
                SchoolPlatformMarketingActivityEnum marketingActivityEnum = SchoolPlatformMarketingActivityEnum.getByType(Integer.parseInt(value));
                return marketingActivityEnum == null ? nullShow : marketingActivityEnum.getName();
            }
            // 合作平台营销活动选择了其他时填写的内容
            if (PLATFORM_MARKETING_ACTIVITY_INFO.equals(key) && StringUtils.isBlank(value)) {
                return nullShow;
            }
            // 活动规则描述
            if (ACTIVITY_RULE_DESCRIPTION.equals(key) && StringUtils.isBlank(value)) {
                return nullShow;
            }
            // 活动截图
            if (ACTIVITY_PIC.equals(key) && StringUtils.isBlank(value)) {
                return nullShow;
            }
            // 活动成本分摊类型
            if (ACTIVITY_COST_SHARING_TYPE.equals(key)) {
                SchoolPlatformActivityCostSharingTypeEnum activityCostSharingTypeEnum = SchoolPlatformActivityCostSharingTypeEnum.getByType(Integer.parseInt(value));
                return activityCostSharingTypeEnum == null ? nullShow : activityCostSharingTypeEnum.getName();
            }
            // 活动成本分摊范围，商家-最小值
            if (ACTIVITY_COST_SHARING_POI_MIN.equals(key) && StringUtils.isBlank(value)) {
                return nullShow;
            }
            // 活动成本分摊范围，商家-最大值
            if (ACTIVITY_COST_SHARING_POI_MAX.equals(key) && StringUtils.isBlank(value)) {
                return nullShow;
            }
            // 活动成本分摊范围，平台-最小值
            if (ACTIVITY_COST_SHARING_PLATFORM_MIN.equals(key) && StringUtils.isBlank(value)) {
                return nullShow;
            }
            // 活动成本分摊范围，平台-最大值
            if (ACTIVITY_COST_SHARING_PLATFORM_MAX.equals(key) && StringUtils.isBlank(value)) {
                return nullShow;
            }
        }
        return value;
    }

    /**
     * 组装学校平台合作信息删除日志V2
     *
     * @param wmScSchoolCoPlatformDO wmScSchoolCoPlatformDO
     * @return 平台合作信息删除日志
     */
    public String composeSchoolCoPlatformDeleteLog(WmScSchoolCoPlatformDO wmScSchoolCoPlatformDO) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.sc.WmScLogSchoolInfoService.composeSchoolCoPlatformDeleteLog(com.sankuai.meituan.waimai.customer.domain.sc.WmScSchoolCoPlatformDO)");
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.sc.WmScLogSchoolInfoService.composeSchoolCoPlatformDeleteLog(com.sankuai.meituan.waimai.customer.domain.sc.WmScSchoolCoPlatformDO)");
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.sc.WmScLogSchoolInfoService.composeSchoolCoPlatformDeleteLog(com.sankuai.meituan.waimai.customer.domain.sc.WmScSchoolCoPlatformDO)");
        StringBuilder stringBuilder = new StringBuilder().append("操作：删除\\n");
        stringBuilder.append("提交渠道：学校管理系统\\n");
        stringBuilder.append("平台合作信息ID：").append(wmScSchoolCoPlatformDO.getId()).append("\\n");
        schoolCoPlatformField.forEach((key, value) -> {
            Object afterValue = transInsertSchoolCoPlatformToDesc(key, wmScSchoolCoPlatformDO);
            if (afterValue == null || StringUtils.isBlank(afterValue.toString())) {
                return;
            }
            stringBuilder.append("[字段变更] ");
            stringBuilder.append(schoolCoPlatformField.get(key));
            stringBuilder.append("：");
            stringBuilder.append(afterValue);
            stringBuilder.append("\\n");
        });
        // 营销活动信息
        if (CollectionUtils.isEmpty(wmScSchoolCoPlatformDO.getWmScSchoolCoPlatformMarketDOList())) {
            return stringBuilder.toString();
        }
        for (WmScSchoolCoPlatformMarketDO wmScSchoolCoPlatformMarketDo: wmScSchoolCoPlatformDO.getWmScSchoolCoPlatformMarketDOList()) {
            stringBuilder.append("【营销活动信息】\\n");
            schoolCoPlatformMarketField.forEach((key, value) -> {
                Object afterValue = transInsertSchoolCoPlatformMarketToDesc(key, wmScSchoolCoPlatformMarketDo);
                if (afterValue == null || StringUtils.isBlank(afterValue.toString())) {
                    return;
                }
                stringBuilder.append("[字段变更] ");
                stringBuilder.append(schoolCoPlatformMarketField.get(key));
                stringBuilder.append(": ");
                stringBuilder.append(afterValue);
                stringBuilder.append("\\n");
            });
        }
        return stringBuilder.toString();
    }

    /**
     * 记录学校交付任务生成日志
     * @param streamDO streamDO
     * @param userId 用户ID
     * @param userName 用户名称
     */
    public void recordSchoolDeliveryInitiationLog(WmSchoolDeliveryStreamDO streamDO, Integer userId, String userName, String opSource) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.service.sc.WmScLogSchoolInfoService.recordSchoolDeliveryInitiationLog(WmSchoolDeliveryStreamDO,Integer,String,String)");
        if (streamDO == null || userId == null || StringUtils.isBlank(userName)) {
            log.warn("[WmScLogSchoolInfoService.recordSchoolDeliveryInitiationLog] input param invalid. streamDO = {}, userId = {}, userName = {}",
                    JSONObject.toJSONString(streamDO), userId, userName);
            return;
        }

        String logInfo = composeSchoolDeliveryInitiationLog(streamDO, opSource);
        if (StringUtils.isNotBlank(logInfo)) {
            insertScOptLog(OptTypeEnum.INSERT.getType(), SC_SCHOOL_DELIVERY_ASSIGNMENT_LOG, streamDO.getSchoolPrimaryId(),
                    userId, userName, logInfo, "");
        }
    }

    public void recordSchoolDeliveryEndLog(WmSchoolDeliveryStreamDO streamDO, Integer userId, String userName, String opSource) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.service.sc.WmScLogSchoolInfoService.recordSchoolDeliveryEndLog(WmSchoolDeliveryStreamDO,Integer,String,String)");
        String logInfo = composeSchoolDeliveryEndLog(streamDO, opSource);
        if (StringUtils.isNotBlank(logInfo)) {
            insertScOptLog(OptTypeEnum.INSERT.getType(), SC_SCHOOL_DELIVERY_ASSIGNMENT_LOG, streamDO.getSchoolPrimaryId(),
                    userId, userName, logInfo, "");
        }
    }

    public String composeSchoolDeliveryEndLog(WmSchoolDeliveryStreamDO streamDO, String opSource) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_2", "com.sankuai.meituan.waimai.customer.service.sc.WmScLogSchoolInfoService.composeSchoolDeliveryEndLog(com.sankuai.meituan.waimai.customer.domain.sc.delivery.WmSchoolDeliveryStreamDO,java.lang.String)");
        return "操作：交付任务终结\\n" +
                "交付编号: " + streamDO.getId() + "\\n" +
                "终结操作来源：" + opSource + "\\n";
    }

    /**
     * 交付流生成日志
     * @param streamDO 交付流信息
     * @return 交付流生成日志
     */
    public String composeSchoolDeliveryInitiationLog(WmSchoolDeliveryStreamDO streamDO, String opSource) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.service.sc.WmScLogSchoolInfoService.composeSchoolDeliveryInitiationLog(WmSchoolDeliveryStreamDO,String)");
        return "操作：交付任务生成\\n" +
                "交付编号: " + streamDO.getId() + "\\n" +
                "操作来源：" + opSource + "\\n";
    }

    public void recordSchoolDeliveryAssignmentTempSaveLog(WmSchoolDeliveryLogBO logBO) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.sc.WmScLogSchoolInfoService.recordSchoolDeliveryAssignmentTempSaveLog(com.sankuai.meituan.waimai.customer.domain.sc.delivery.WmSchoolDeliveryLogBO)");
        String title = "操作：暂存\\n" +
                "交付编号：" + logBO.getDeliveryId() + "\\n";
        String prefix = "[字段变更] ";
        String logInfo = logBO.getAssignmentDTOBefore() == null ? composeSchoolDeliveryAssignmentInsertLog(logBO.getAssignmentDTOAfter(), prefix)
                : composeSchoolDeliveryAssignmentUpdateLog(logBO.getAssignmentDTOBefore(), logBO.getAssignmentDTOAfter(), prefix);
        log.info("[WmScLogSchoolInfoService.recordSchoolDeliveryAssignmentTempSaveLog] logInfo = {}", logInfo);
        if (StringUtils.isNotBlank(logInfo)) {
            insertScOptLog(OptTypeEnum.INSERT.getType(), SC_SCHOOL_DELIVERY_ASSIGNMENT_TEMP_LOG, logBO.getSchoolPrimaryId(),
                    logBO.getUserId(), logBO.getUserName(), title + logInfo, "");
        }
    }

    public void recordSchoolDeliveryAssignmentSubmitLog(WmSchoolDeliveryLogBO logBO, Integer auditTaskId) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.service.sc.WmScLogSchoolInfoService.recordSchoolDeliveryAssignmentSubmitLog(WmSchoolDeliveryLogBO,Integer)");
        String title = "操作：提交审批\\n" +
                "交付编号：" + logBO.getAssignmentDTOAfter().getDeliveryId() + "\\n" +
                "审批任务ID：" + auditTaskId + "\\n";
        String prefix = "[字段待变更] ";
        String logInfo = logBO.getAssignmentDTOBefore() == null ? composeSchoolDeliveryAssignmentInsertLog(logBO.getAssignmentDTOAfter(), prefix)
                : composeSchoolDeliveryAssignmentUpdateLog(logBO.getAssignmentDTOBefore(), logBO.getAssignmentDTOAfter(), prefix);
        if (StringUtils.isNotBlank(logInfo)) {
            insertScOptLog(OptTypeEnum.INSERT.getType(), SC_SCHOOL_DELIVERY_ASSIGNMENT_LOG, logBO.getSchoolPrimaryId(),
                    logBO.getUserId(), logBO.getUserName(), title + logInfo, "");
        }
    }

    public void recordSchoolDeliveryAssignmentTaskEffectLog(WmSchoolDeliveryLogBO logBO, Integer auditTaskId) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.sc.WmScLogSchoolInfoService.recordSchoolDeliveryAssignmentTaskEffectLog(WmSchoolDeliveryLogBO,Integer)");
        String title = "操作：审批通过\\n" +
                "交付编号：" + logBO.getAssignmentDTOAfter().getDeliveryId() + "\\n" +
                "审批任务ID：" + auditTaskId + "\\n";
        String prefix = "[字段变更] ";
        String logInfo = logBO.getAssignmentDTOBefore() == null ? composeSchoolDeliveryAssignmentInsertLog(logBO.getAssignmentDTOAfter(), prefix)
                : composeSchoolDeliveryAssignmentUpdateLog(logBO.getAssignmentDTOBefore(), logBO.getAssignmentDTOAfter(), prefix);
        if (StringUtils.isNotBlank(logInfo)) {
            insertScOptLog(OptTypeEnum.INSERT.getType(), SC_SCHOOL_DELIVERY_ASSIGNMENT_LOG, logBO.getSchoolPrimaryId(),
                    logBO.getUserId(), logBO.getUserName(), title + logInfo, "");
        }
    }

    public String composeSchoolDeliveryAssignmentInsertLog(WmSchoolDeliveryAssignmentDTO assignmentDTOAfter, String prefix) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_1", "com.sankuai.meituan.waimai.customer.service.sc.WmScLogSchoolInfoService.composeSchoolDeliveryAssignmentInsertLog(WmSchoolDeliveryAssignmentDTO,String)");
        StringBuilder stringBuilder = new StringBuilder();
        // 除了校方部门摸排的内容
        schoolDeliveryAssignmentField.forEach((key, value) -> {
            Object trueValue = getFieldValueByFieldName(key, assignmentDTOAfter);
            Object afterValue = null;
            try {
                afterValue = transSchoolDeliveryAssignmentValueToDesc(key, trueValue);
            } catch (WmSchCantException e) {
                log.error("[WmScLogSchoolInfoService.composeSchoolDeliveryAssignmentCommitInsertLog] WmSchCantException. assignmentDOAfter = {}", JSONObject.toJSONString(assignmentDTOAfter), e);
            }

            if (afterValue == null || StringUtils.isBlank(afterValue.toString())) {
                return;
            }
            stringBuilder.append(prefix);
            stringBuilder.append(schoolDeliveryAssignmentField.get(key));
            stringBuilder.append(": 空=>");
            stringBuilder.append(afterValue);
            stringBuilder.append("\\n");
        });

        // 校方部门摸排
        List<WmSchoolDepartmentIntensionDTO> departmentIntensionDTOList = assignmentDTOAfter.getDepartmentIntensionDTOList();
        stringBuilder.append(getSchoolDeliveryDepartmentIntensionLog(departmentIntensionDTOList, prefix));
        return stringBuilder.toString();
    }

    private String getSchoolDeliveryDepartmentIntensionLog(List<WmSchoolDepartmentIntensionDTO> departmentIntensionDTOList, String prefix) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_2", "com.sankuai.meituan.waimai.customer.service.sc.WmScLogSchoolInfoService.getSchoolDeliveryDepartmentIntensionLog(java.util.List,java.lang.String)");
        if (CollectionUtils.isEmpty(departmentIntensionDTOList)) {
            return Strings.EMPTY;
        }

        List<String> afterLog = new ArrayList<>();
        for (WmSchoolDepartmentIntensionDTO intensionDTO : departmentIntensionDTOList) {
            SchoolDeliveryDepartmentIntensionEnum intensionEnum = SchoolDeliveryDepartmentIntensionEnum.getByType(intensionDTO.getDepartmentIntension());
            String intensionDesc = intensionEnum == null ? nullShow : intensionEnum.getName();
            afterLog.add(intensionDTO.getDepartmentName() + "-" + intensionDesc);
        }

        return prefix + DEPARTMENT_INTENTION_DTO_LIST_DESC + ": 空" + "=>" + StringUtils.join(afterLog, " / ")  + "\\n";
    }

    private Object transSchoolDeliveryAssignmentValueToDesc(String key, Object trueValue) throws WmSchCantException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_2", "com.sankuai.meituan.waimai.customer.service.sc.WmScLogSchoolInfoService.transSchoolDeliveryAssignmentValueToDesc(java.lang.String,java.lang.Object)");
        if (trueValue == null) {
            return null;
        }

        // 公海档口数
        if (ASSIGNMENT_PUBLIC_STALL_NUM.equals(key) && trueValue.toString().equals("-1")) {
            return nullShow;
        }
        // 可交付档口数
        if (ASSIGNMENT_DELIVERABLE_STALL_NUM.equals(key) && trueValue.toString().equals("-1")) {
            return nullShow;
        }
        // 取餐柜信息
        if (ASSIGNMENT_NEED_FOOD_CABINET.equals(key)) {
            SchoolDeliveryDiningCabinetEnum diningCabinetEnum = SchoolDeliveryDiningCabinetEnum.getByType(Integer.parseInt(trueValue.toString()));
            return diningCabinetEnum == null ? nullShow : diningCabinetEnum.getName();
        }

        // 物料需求(多选)
        if (ASSIGNMENT_MATIRIAL_DEMAND.equals(key)) {
            log.info("[transSchoolDeliveryAssignmentValueToDesc] demandList = {}", JSONObject.toJSONString(trueValue));
            if (StringUtils.isBlank(trueValue.toString()) || "[]".equals(JSONObject.toJSONString(trueValue))) {
                return nullShow;
            }

            List<Integer> reasonList = new ArrayList<>();
            String[] items = trueValue.toString().split(",");
            for (String item : items) {
                reasonList.add(Integer.parseInt(item));
            }
            List<String> reasonDesc = reasonList.stream()
                    .map(reason -> SchoolDeliveryMaterialDemandEnum.getByType(reason).getName())
                    .collect(Collectors.toList());
            return StringUtils.join(reasonDesc, "、");
        }

        // 交付发起时间
        if (ASSIGNMENT_INITIATION_TIME.equals(key)) {
            if (trueValue.toString().equals("0") || StringUtils.isBlank(trueValue.toString())) {
                return nullShow;
            }
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            Date date = new Date(Long.parseLong(trueValue.toString()) * 1000L);
            return sdf.format(date);
        }

        // 关键决策人信息
        if (ASSIGNMENT_KEY_DECISION_USER_ID.equals(key)) {
            if (StringUtils.isBlank(trueValue.toString())) {
                return nullShow;
            }
            ContactDto contactDto = wmCampusContactServiceAdapter.getSchoolContactByUserId(trueValue.toString());
            if (contactDto == null) {
                return nullShow;
            }
            return contactDto.getName() + "(" + contactDto.getId() + ")";
        }

        // 签约合伙人信息
        if (ASSIGNMENT_SIGN_PARTNER_USER_ID.equals(key)) {
            if (StringUtils.isBlank(trueValue.toString())) {
                return nullShow;
            }
            PartnerInfoDto partnerInfoDto = wmCampusContactServiceAdapter.getSchoolPartnerByUserId(trueValue.toString());
            if (partnerInfoDto == null) {
                return nullShow;
            }
            return partnerInfoDto.getPartnerName() + "(" + partnerInfoDto.getPartnerId() + ")";
        }

        // 取餐柜数量
        if (ASSIGNMENT_NEED_FOOD_CABINET_NUM.equals(key) && trueValue.toString().equals("-1")) {
            return nullShow;
        }
        // 需要的打印机数量
        if (ASSIGNMENT_NEED_PRINTER_NUM.equals(key) && trueValue.toString().equals("-1")) {
            return nullShow;
        }
        // 需要的展架数量
        if (ASSIGNMENT_NEED_DISPLAY_RACK_NUM.equals(key) && trueValue.toString().equals("-1")) {
            return nullShow;
        }
        // 需要的遮阳伞数量
        if (ASSIGNMENT_NEED_SUNSHADE_NUM.equals(key) && trueValue.toString().equals("-1")) {
            return nullShow;
        }
        // 需要的垃圾桶数量
        if (ASSIGNMENT_NEED_TRASH_CAN_NUM.equals(key) && trueValue.toString().equals("-1")) {
            return nullShow;
        }
        // 交付发起人/客户成功经理/学校对应蜂窝负责人/聚合渠道经理
        if (ASSIGNMENT_INITIATOR_UID.equals(key)
                || ASSIGNMENT_CSM_UID.equals(key)
                || ASSIGNMENT_AORM_UID.equals(key)
                || ASSIGNMENT_ACM_UID.equals(key)) {
            if (trueValue.toString().equals("0")) {
                return nullShow;
            }
            return getUserNameAndMisByUid(trueValue.toString());
        }

        if (StringUtils.isBlank(trueValue.toString())) {
            return nullShow;
        }
        return trueValue;
    }

    public String composeSchoolDeliveryAssignmentUpdateLog(WmSchoolDeliveryAssignmentDTO assignmentDTOBefore, WmSchoolDeliveryAssignmentDTO assignmentDTOAfter, String prefix) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_1", "com.sankuai.meituan.waimai.customer.service.sc.WmScLogSchoolInfoService.composeSchoolDeliveryAssignmentUpdateLog(WmSchoolDeliveryAssignmentDTO,WmSchoolDeliveryAssignmentDTO,String)");
        try {
            List<WmCustomerDiffCellBo> diffList = DiffUtil.compare(assignmentDTOBefore, assignmentDTOAfter, schoolDeliveryAssignmentField);
            log.info("[WmScLogSchoolInfoService.composeSchoolDeliveryAssignmentUpdateLog] diffList = {}", JSONObject.toJSONString(diffList));
            StringBuilder stringBuilder = new StringBuilder();
            for (WmCustomerDiffCellBo wmCustomerDiffCellBo : diffList) {
                // 更新前的值
                Object preValue = transSchoolDeliveryAssignmentValueToDesc(wmCustomerDiffCellBo.getField(), wmCustomerDiffCellBo.getPre());
                if (StringUtils.isEmpty(preValue.toString())) {
                    preValue = nullShow;
                }
                // 更新后的值
                Object afterValue = transSchoolDeliveryAssignmentValueToDesc(wmCustomerDiffCellBo.getField(), wmCustomerDiffCellBo.getAft());
                if (StringUtils.isEmpty(afterValue.toString())) {
                    afterValue = nullShow;
                }
                if (preValue.equals(afterValue) && nullShow.equals(preValue)) {
                    continue;
                }
                stringBuilder.append(prefix);
                stringBuilder.append(schoolDeliveryAssignmentField.get(wmCustomerDiffCellBo.getField()));
                stringBuilder.append(": ").append(preValue);
                stringBuilder.append("=>");
                stringBuilder.append(afterValue);
                stringBuilder.append("\\n");
            }
            // 校方部门摸排特殊处理
            stringBuilder.append(getSchoolDeliveryDepartmentIntensionLog(assignmentDTOBefore, assignmentDTOAfter, prefix));
            return stringBuilder.toString();
        } catch (Exception e) {
            log.error("[WmScLogSchoolInfoService.composeSchoolDeliveryAssignmentCommitUpdateLog] Exception. assignmentDOBefore = {}, assignmentDOAfter = {} ",
                    JSONObject.toJSONString(assignmentDTOBefore), JSONObject.toJSONString(assignmentDTOAfter), e);
        }
        return Strings.EMPTY;
    }

    private String getSchoolDeliveryDepartmentIntensionLog(WmSchoolDeliveryAssignmentDTO assignmentDTOBefore, WmSchoolDeliveryAssignmentDTO assignmentDTOAfter, String prefix) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_1", "com.sankuai.meituan.waimai.customer.service.sc.WmScLogSchoolInfoService.getSchoolDeliveryDepartmentIntensionLog(WmSchoolDeliveryAssignmentDTO,WmSchoolDeliveryAssignmentDTO,String)");
        List<WmSchoolDepartmentIntensionDTO> beforeList = assignmentDTOBefore.getDepartmentIntensionDTOList();
        List<WmSchoolDepartmentIntensionDTO> afterList = assignmentDTOAfter.getDepartmentIntensionDTOList();
        SimplePropertyPreFilter filter = new SimplePropertyPreFilter(WmSchoolDepartmentIntensionDTO.class, "departmentName", "departmentIntension");
        log.info("[WmScLogSchoolInfoService.getSchoolDeliveryDepartmentIntensionLog] beforeList = {}, afterList = {}",
                JSONObject.toJSONString(beforeList, filter), JSONObject.toJSONString(afterList, filter));
        if (beforeList.equals(afterList) || JSONObject.toJSONString(beforeList, filter).equals(JSONObject.toJSONString(afterList, filter))) {
            return Strings.EMPTY;
        }

        List<String> beforeLog = new ArrayList<>();
        for (WmSchoolDepartmentIntensionDTO intensionDTO : beforeList) {
            SchoolDeliveryDepartmentIntensionEnum intensionEnum = SchoolDeliveryDepartmentIntensionEnum.getByType(intensionDTO.getDepartmentIntension());
            String intensionDesc = intensionEnum == null ? nullShow : intensionEnum.getName();
            beforeLog.add(intensionDTO.getDepartmentName() + "-" + intensionDesc);
        }

        List<String> afterLog = new ArrayList<>();
        for (WmSchoolDepartmentIntensionDTO intensionDTO : afterList) {
            SchoolDeliveryDepartmentIntensionEnum intensionEnum = SchoolDeliveryDepartmentIntensionEnum.getByType(intensionDTO.getDepartmentIntension());
            String intensionDesc = intensionEnum == null ? nullShow : intensionEnum.getName();
            afterLog.add(intensionDTO.getDepartmentName() + "-" + intensionDesc);
        }

        return prefix + DEPARTMENT_INTENTION_DTO_LIST_DESC + ": " + StringUtils.join(beforeLog, " / ")
                + "=>" + StringUtils.join(afterLog, " / ")  + "\\n";
    }

    public void recordSchoolDeliveryGoalSetTempSaveLog(WmSchoolDeliveryLogBO logBO) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_2", "com.sankuai.meituan.waimai.customer.service.sc.WmScLogSchoolInfoService.recordSchoolDeliveryGoalSetTempSaveLog(com.sankuai.meituan.waimai.customer.domain.sc.delivery.WmSchoolDeliveryLogBO)");
        String title = "操作：暂存\\n" +
                "交付编号：" + logBO.getDeliveryId() + "\\n";
        String prefix = "[字段变更] ";
        String logInfo = logBO.getGoalSetDTOBefore() == null ? composeSchoolDeliveryGoalSetInsertLog(logBO.getGoalSetDTOAfter(), prefix)
                : composeSchoolDeliveryGoalSetUpdateLog(logBO.getGoalSetDTOBefore(), logBO.getGoalSetDTOAfter(), prefix);
        log.info("[WmScLogSchoolInfoService.recordSchoolDeliveryGoalSetTempSaveLog] logInfo = {}", JSONObject.toJSONString(logInfo));
        if (StringUtils.isNotBlank(logInfo)) {
            insertScOptLog(OptTypeEnum.INSERT.getType(), SC_SCHOOL_DELIVERY_GOALSET_TEMP_LOG, logBO.getSchoolPrimaryId(),
                    logBO.getUserId(), logBO.getUserName(), title + logInfo, "");
        }
    }

    public void recordSchoolDeliveryGoalSetSubmitLog(WmSchoolDeliveryLogBO logBO, Integer auditTaskId) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.sc.WmScLogSchoolInfoService.recordSchoolDeliveryGoalSetSubmitLog(WmSchoolDeliveryLogBO,Integer)");
        String title = "操作：提交审批\\n" +
                "交付编号：" + logBO.getGoalSetDTOAfter().getDeliveryId() + "\\n" +
                "审批任务ID：" + auditTaskId + "\\n";
        String prefix = "[字段待变更] ";
        String logInfo = logBO.getGoalSetDTOBefore() == null ? composeSchoolDeliveryGoalSetInsertLog(logBO.getGoalSetDTOAfter(), prefix)
                : composeSchoolDeliveryGoalSetUpdateLog(logBO.getGoalSetDTOBefore(), logBO.getGoalSetDTOAfter(), prefix);
        if (StringUtils.isNotBlank(logInfo)) {
            insertScOptLog(OptTypeEnum.INSERT.getType(), SC_SCHOOL_DELIVERY_GOALSET_LOG, logBO.getSchoolPrimaryId(),
                    logBO.getUserId(), logBO.getUserName(), title + logInfo, "");
        }
    }

    public void recordSchoolDeliveryGoalSetTaskEffectLog(WmSchoolDeliveryLogBO logBO, Integer auditTaskId) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_2", "com.sankuai.meituan.waimai.customer.service.sc.WmScLogSchoolInfoService.recordSchoolDeliveryGoalSetTaskEffectLog(WmSchoolDeliveryLogBO,Integer)");
        String title = "操作：审批通过\\n" +
                "交付编号：" + logBO.getGoalSetDTOAfter().getDeliveryId() + "\\n" +
                "审批任务I：" +auditTaskId + "\\n";
        String prefix = "[字段变更] ";
        String logInfo = logBO.getGoalSetDTOBefore() == null ? composeSchoolDeliveryGoalSetInsertLog(logBO.getGoalSetDTOAfter(), prefix)
                : composeSchoolDeliveryGoalSetUpdateLog(logBO.getGoalSetDTOBefore(), logBO.getGoalSetDTOAfter(), prefix);
        if (StringUtils.isNotBlank(logInfo)) {
            insertScOptLog(OptTypeEnum.INSERT.getType(), SC_SCHOOL_DELIVERY_GOALSET_LOG, logBO.getSchoolPrimaryId(),
                    logBO.getUserId(), logBO.getUserName(), title + logInfo, "");
        }
    }

    public String composeSchoolDeliveryGoalSetInsertLog(WmSchoolDeliveryGoalSetDTO goalSetDTOAfter, String prefix) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_1", "com.sankuai.meituan.waimai.customer.service.sc.WmScLogSchoolInfoService.composeSchoolDeliveryGoalSetInsertLog(WmSchoolDeliveryGoalSetDTO,String)");
        StringBuilder stringBuilder = new StringBuilder();
        schoolDeliveryGoalSetField.forEach((key, value) -> {
            Object trueValue = getFieldValueByFieldName(key, goalSetDTOAfter);
            Object afterValue = transSchoolDeliveryGoalSetValueToDesc(key, trueValue);
            if (afterValue == null || StringUtils.isBlank(afterValue.toString())) {
                return;
            }
            stringBuilder.append(prefix);
            stringBuilder.append(schoolDeliveryGoalSetField.get(key));
            stringBuilder.append(": 空=>");
            stringBuilder.append(afterValue);
            stringBuilder.append("\\n");
        });
        return stringBuilder.toString();
    }

    public String composeSchoolDeliveryGoalSetUpdateLog(WmSchoolDeliveryGoalSetDTO goalSetDTOBefore, WmSchoolDeliveryGoalSetDTO goalSetDTOAfter, String prefix) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_2", "com.sankuai.meituan.waimai.customer.service.sc.WmScLogSchoolInfoService.composeSchoolDeliveryGoalSetUpdateLog(WmSchoolDeliveryGoalSetDTO,WmSchoolDeliveryGoalSetDTO,String)");
        try {
            List<WmCustomerDiffCellBo> diffList = DiffUtil.compare(goalSetDTOBefore, goalSetDTOAfter, schoolDeliveryGoalSetField);
            StringBuilder stringBuilder = new StringBuilder();
            for (WmCustomerDiffCellBo wmCustomerDiffCellBo : diffList) {
                // 更新前的值
                Object preValue = transSchoolDeliveryGoalSetValueToDesc(wmCustomerDiffCellBo.getField(), wmCustomerDiffCellBo.getPre());
                if (StringUtils.isEmpty(preValue.toString())) {
                    preValue = nullShow;
                }
                // 更新后的值
                Object afterValue = transSchoolDeliveryGoalSetValueToDesc(wmCustomerDiffCellBo.getField(), wmCustomerDiffCellBo.getAft());
                if (StringUtils.isEmpty(afterValue.toString())) {
                    afterValue = nullShow;
                }
                if (preValue.equals(afterValue) && nullShow.equals(preValue)) {
                    continue;
                }
                stringBuilder.append(prefix);
                stringBuilder.append(schoolDeliveryGoalSetField.get(wmCustomerDiffCellBo.getField()));
                stringBuilder.append(": ").append(preValue);
                stringBuilder.append("=>");
                stringBuilder.append(afterValue);
                stringBuilder.append("\\n");
            }
            return stringBuilder.toString();
        } catch (Exception e) {
            log.error("[WmScLogSchoolInfoService.composeSchoolDeliveryGoalSetCommitUpdateLog] Exception. goalSetDOBefore = {}, goalSetDOAfter = {} ",
                    JSONObject.toJSONString(goalSetDTOBefore), JSONObject.toJSONString(goalSetDTOAfter), e);
        }
        return Strings.EMPTY;
    }

    public Object transSchoolDeliveryGoalSetValueToDesc(String key, Object trueValue) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_1", "com.sankuai.meituan.waimai.customer.service.sc.WmScLogSchoolInfoService.transSchoolDeliveryGoalSetValueToDesc(java.lang.String,java.lang.Object)");
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_1", "com.sankuai.meituan.waimai.customer.service.sc.WmScLogSchoolInfoService.transSchoolDeliveryGoalSetValueToDesc(java.lang.String,java.lang.Object)");
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_1", "com.sankuai.meituan.waimai.customer.service.sc.WmScLogSchoolInfoService.transSchoolDeliveryGoalSetValueToDesc(java.lang.String,java.lang.Object)");
        if (trueValue == null) {
            return null;
        }
        try {
            // 普遍客户关系梳理
            if (GOALSET_CONTACT_USER_IDS.equals(key)) {
                if (StringUtils.isBlank(trueValue.toString()) || trueValue.toString().equals("[]")) {
                    return nullShow;
                }
                List<String> userIds = Arrays.asList(trueValue.toString().split(","));
                if (CollectionUtils.isNotEmpty(userIds)) {
                    List<ContactDto> contactDtoList = wmCampusContactServiceAdapter.getSchoolContactsByUserIds(userIds);
                    List<String> userInfoList = new ArrayList<>();
                    for (ContactDto contactDto : contactDtoList) {
                        userInfoList.add(contactDto.getName() + "(" + contactDto.getId() + ")");
                    }
                    return StringUtils.join(userInfoList, " / ");
                }
            }
            // 校内站线上签约预计完成时间
            if (GOALSET_INSC_ONLINE_SIGN_EXPTIME.equals(key) && trueValue.toString().equals("")) {
                return nullShow;
            }
            // 校内站线下建站预计完成时间
            if (GOALSET_INSC_OFFLINE_BUILD_EXPTIME.equals(key) && trueValue.toString().equals("")) {
                return nullShow;
            }
            // 是否建立校外校外建站
            if (GOALSET_BUILD_OFF_CAMPUS_STATION.equals(key)) {
                SchoolDeliveryBuildOffCampusEnum buildOffCampusEnum = SchoolDeliveryBuildOffCampusEnum.getByType(Integer.valueOf(trueValue.toString()));
                return buildOffCampusEnum == null ? nullShow : buildOffCampusEnum.getName();
            }
            // 校外站线上签约预计完成时间
            if (GOALSET_OUTSC_ONLINE_SIGN_EXPTIME.equals(key) && trueValue.toString().equals("")) {
                return nullShow;
            }
            // 校外站线下建站预计完成时间
            if (GOALSET_OUTSC_OFFLINE_BUILD_EXPTIME.equals(key) && trueValue.toString().equals("")) {
                return nullShow;
            }
            // 相对准时率目标
            if (GOALSET_ONTIME_RATE_TARGET.equals(key) && trueValue.toString().equals("")) {
                return nullShow;
            }
            // 相对准时率目标预计完成时间
            if (GOALSET_ONTIME_RATE_TARGET_EXPTIME.equals(key) && trueValue.toString().equals("")) {
                return nullShow;
            }
            // 平均配送时长目标
            if (GOALSET_AVG_DELIVERY_TIME_TARGET.equals(key) && trueValue.toString().equals("")) {
                return nullShow;
            }
            // 平均配送时长目标预计完成时间
            if (GOALSET_AVG_DELIVERY_TIME_TARGET_EXPTIME.equals(key) && trueValue.toString().equals("")) {
                return nullShow;
            }
            // 首批档口建店计划时间
            if (GOALSET_FIRST_STALL_BUILD_EXPTIME.equals(key) && trueValue.toString().equals("")) {
                return nullShow;
            }
            // 首批档口上线计划时间
            if (GOALSET_FIRST_STALL_ONLINE_EXPTIME.equals(key) && trueValue.toString().equals("")) {
                return nullShow;
            }
            // 首批上线档口数
            if (GOALSET_FIRST_ONLINE_STALL_NUM.equals(key) && trueValue.toString().equals("-1")) {
                return nullShow;
            }
            // 在线渗透率目标
            if (GOALSET_ONLINE_PENERATE_TARGET.equals(key) && trueValue.toString().equals("")) {
                return nullShow;
            }
            // 在线渗透率目标预计完成时间
            if (GOALSET_ONLINE_PENERATE_TARGET_EXPTIME.equals(key) && trueValue.toString().equals("")) {
                return nullShow;
            }
            // 日均订单量目标
            if (GOALSET_DAILY_ORDER_TARGET.equals(key) && trueValue.toString().equals("")) {
                return nullShow;
            }
            // 日均订单量目标预计完成时间
            if (GOALSET_DAILY_ORDER_TARGET_EXPTIME.equals(key) && trueValue.toString().equals("")) {
                return nullShow;
            }
            // 人顿渗透率目标
            if (GOALSET_MEAL_PENERATE_TARGET.equals(key) && trueValue.toString().equals("")) {
                return nullShow;
            }
            // 人顿渗透率目标预计完成时间
            if (GOALSET_MEAL_PENERATE_TARGET_EXPTIME.equals(key) && trueValue.toString().equals("")) {
                return nullShow;
            }
            // 日均店单产目标
            if (GOALSET_DAILY_YIELD_TARGET.equals(key) && trueValue.toString().equals("")) {
                return nullShow;
            }
            // 日均店单产目标预计完成时间
            if (GOALSET_DAILY_YIELD_TARGET_EXPTIME.equals(key) && trueValue.toString().equals("")) {
                return nullShow;
            }
        } catch (WmSchCantException e) {
            log.error("[WmScLogSchoolInfoService.transSchoolGoalSetAssignmentValueToDesc] WmSchCantException. key = {}, value = {}", key, trueValue, e);
        }

        return trueValue;
    }

    public void recordSchoolDeliveryFollowUpTempSaveLog(WmSchoolDeliveryLogBO logBO) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_1", "com.sankuai.meituan.waimai.customer.service.sc.WmScLogSchoolInfoService.recordSchoolDeliveryFollowUpTempSaveLog(com.sankuai.meituan.waimai.customer.domain.sc.delivery.WmSchoolDeliveryLogBO)");
        String title = "操作：暂存\\n" +
                "交付编号：" + logBO.getDeliveryId() + "\\n";
        String prefix = "[字段变更] ";
        String logInfo = logBO.getFollowUpDTOBefore() == null ? composeSchoolDeliveryFollowUpInsertLog(logBO.getFollowUpDTOAfter(), prefix)
                : composeSchoolDeliveryFollowUpUpdateLog(logBO.getFollowUpDTOBefore(), logBO.getFollowUpDTOAfter(), prefix);
        if (StringUtils.isNotBlank(logInfo)) {
            insertScOptLog(OptTypeEnum.INSERT.getType(), SC_SCHOOL_DELIVERY_FOLLOWUP_TEMP_LOG, logBO.getSchoolPrimaryId(),
                    logBO.getUserId(), logBO.getUserName(), title + logInfo, "");
        }
    }


    public void recordSchoolDeliveryFollowUpSubmitLog(WmSchoolDeliveryLogBO logBO, Integer auditTaskId) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_2", "com.sankuai.meituan.waimai.customer.service.sc.WmScLogSchoolInfoService.recordSchoolDeliveryFollowUpSubmitLog(WmSchoolDeliveryLogBO,Integer)");
        String title = "操作：提交审批\\n" +
                "交付编号：" + logBO.getFollowUpDTOAfter().getDeliveryId() + "\\n" +
                "审批任务ID：" + auditTaskId + "\\n";
        String prefix = "[字段待变更] ";
        String logInfo = logBO.getFollowUpDTOBefore() == null ? composeSchoolDeliveryFollowUpInsertLog(logBO.getFollowUpDTOAfter(), prefix)
                : composeSchoolDeliveryFollowUpUpdateLog(logBO.getFollowUpDTOBefore(), logBO.getFollowUpDTOAfter(), prefix);
        insertScOptLog(OptTypeEnum.INSERT.getType(), SC_SCHOOL_DELIVERY_FOLLOWUP_LOG, logBO.getSchoolPrimaryId(),
                logBO.getUserId(), logBO.getUserName(), title + logInfo, "");
    }

    public void recordSchoolDeliveryFollowUpTaskEffectLog(WmSchoolDeliveryLogBO logBO, Integer auditTaskId) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_2", "com.sankuai.meituan.waimai.customer.service.sc.WmScLogSchoolInfoService.recordSchoolDeliveryFollowUpTaskEffectLog(WmSchoolDeliveryLogBO,Integer)");
        String title = "操作：审批通过\\n" +
                "交付编号：" + logBO.getFollowUpDTOAfter().getDeliveryId() + "\\n" +
                "审批任务ID：" + auditTaskId + "\\n";
        String prefix = "[字段变更] ";
        String logInfo = logBO.getFollowUpDTOBefore() == null ? composeSchoolDeliveryFollowUpInsertLog(logBO.getFollowUpDTOAfter(), prefix)
                : composeSchoolDeliveryFollowUpUpdateLog(logBO.getFollowUpDTOBefore(), logBO.getFollowUpDTOAfter(), prefix);
        if (StringUtils.isNotBlank(logInfo)) {
            insertScOptLog(OptTypeEnum.INSERT.getType(), SC_SCHOOL_DELIVERY_FOLLOWUP_LOG, logBO.getSchoolPrimaryId(),
                    logBO.getUserId(), logBO.getUserName(), title + logInfo, "");
        }
    }

    public String composeSchoolDeliveryFollowUpInsertLog(WmSchoolDeliveryFollowUpDTO followUpDTOAfter, String prefix) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_2", "com.sankuai.meituan.waimai.customer.service.sc.WmScLogSchoolInfoService.composeSchoolDeliveryFollowUpInsertLog(WmSchoolDeliveryFollowUpDTO,String)");
        StringBuilder stringBuilder = new StringBuilder();
        schoolDeliveryFollowUpField.forEach((key, value) -> {
            Object trueValue = getFieldValueByFieldName(key, followUpDTOAfter);
            Object afterValue = transSchoolDeliveryFollowUpValueToDesc(key, trueValue);
            if (afterValue == null || StringUtils.isBlank(afterValue.toString())) {
                return;
            }
            stringBuilder.append(prefix);
            stringBuilder.append(schoolDeliveryFollowUpField.get(key));
            stringBuilder.append(": 空=>");
            stringBuilder.append(afterValue);
            stringBuilder.append("\\n");
        });

        return stringBuilder.toString();
    }

    public String composeSchoolDeliveryFollowUpUpdateLog(WmSchoolDeliveryFollowUpDTO followUpDTOBefore, WmSchoolDeliveryFollowUpDTO followUpDTOAfter, String prefix) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.service.sc.WmScLogSchoolInfoService.composeSchoolDeliveryFollowUpUpdateLog(WmSchoolDeliveryFollowUpDTO,WmSchoolDeliveryFollowUpDTO,String)");
        try {
            List<WmCustomerDiffCellBo> diffList = DiffUtil.compare(followUpDTOBefore, followUpDTOAfter, schoolDeliveryFollowUpField);
            StringBuilder stringBuilder = new StringBuilder();
            for (WmCustomerDiffCellBo wmCustomerDiffCellBo : diffList) {
                // 更新前的值
                Object preValue = transSchoolDeliveryFollowUpValueToDesc(wmCustomerDiffCellBo.getField(), wmCustomerDiffCellBo.getPre());
                if (StringUtils.isEmpty(preValue.toString())) {
                    preValue = nullShow;
                }
                // 更新后的值
                Object afterValue = transSchoolDeliveryFollowUpValueToDesc(wmCustomerDiffCellBo.getField(), wmCustomerDiffCellBo.getAft());
                if (StringUtils.isEmpty(afterValue.toString())) {
                    afterValue = nullShow;
                }
                if (preValue.equals(afterValue) && nullShow.equals(preValue)) {
                    continue;
                }
                stringBuilder.append(prefix);
                stringBuilder.append(schoolDeliveryFollowUpField.get(wmCustomerDiffCellBo.getField()));
                stringBuilder.append(": ").append(preValue);
                stringBuilder.append("=>");
                stringBuilder.append(afterValue);
                stringBuilder.append("\\n");
            }
            return stringBuilder.toString();
        } catch (Exception e) {
            log.error("[WmScLogSchoolInfoService.composeSchoolDeliveryFollowUpCommitUpdateLog] Exception. followUpDOBefore = {}, followUpDOAfter = {} ",
                    JSONObject.toJSONString(followUpDTOBefore), JSONObject.toJSONString(followUpDTOAfter), e);
        }
        return Strings.EMPTY;
    }

    public Object transSchoolDeliveryFollowUpValueToDesc(String key, Object trueValue) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.service.sc.WmScLogSchoolInfoService.transSchoolDeliveryFollowUpValueToDesc(java.lang.String,java.lang.Object)");
        if (trueValue == null) {
            return null;
        }
        if (StringUtils.isBlank(trueValue.toString())) {
            return nullShow;
        }
        // 当前状态(校内站线上签约/校内站线下建站/校外站线上签约/校外站线下建站/首批档口上线)
        if (FOLLOWUP_INSC_ONLINE_SIGN_STATUS.equals(key)
                || FOLLOWUP_INSC_OFFLINE_BUILD_STATUS.equals(key)
                || FOLLOWUP_OUTSC_ONLINE_SIGN_STATUS.equals(key)
                || FOLLOWUP_OUTSC_OFFLINE_BUILD_STATUS.equals(key)
                || FOLLOWUP_FIRST_STALL_ONLINE_STATUS.equals(key)
                || FOLLOWUP_ONLINE_PENERATE_STATUS.equals(key)
                || FOLLOWUP_ONTIME_RATE_STATUS.equals(key)
                || FOLLOWUP_AVG_DELIVERY_TIME_STATUS.equals(key)
                || FOLLOWUP_DAILY_ORDER_STATUS.equals(key)
                || FOLLOWUP_MEAL_PENERATE_STATUS.equals(key)
                || FOLLOWUP_DAILY_YIELD_STATUS.equals(key)) {
            SchoolDeliveryGoalStatusEnum statusEnum = SchoolDeliveryGoalStatusEnum.getByType(Integer.parseInt(trueValue.toString()));
            return statusEnum == null ? nullShow : statusEnum.getName();
        }

        // 完成情况(校内站线上签约/校内站线下建站/校外站线上签约/校外站线下建站/首批档口上线)
        if (FOLLOWUP_INSC_ONLINE_SIGN_ACHIEVE.equals(key)
                || FOLLOWUP_INSC_OFFLINE_BUILD_ACHIEVE.equals(key)
                || FOLLOWUP_OUTSC_ONLINE_SIGN_ACHIEVE.equals(key)
                || FOLLOWUP_OUTSC_OFFLINE_BUILD_ACHIEVE.equals(key)
                || FOLLOWUP_FIRST_STALL_ONLINE_ACHIEVE.equals(key)) {
            SchoolDeliveryGoalAchieveEnum goalAchieveEnum = SchoolDeliveryGoalAchieveEnum.getByType(Integer.parseInt(trueValue.toString()));
            return goalAchieveEnum == null ? nullShow : goalAchieveEnum.getName();
        }

        // 完成情况(在线渗透率/相对准时率/平均配送时长/日均订单量/人顿渗透率/日均店单产)
        if (FOLLOWUP_ONLINE_PENERATE_ACHIEVE.equals(key)
                || FOLLOWUP_ONTIME_RATE_ACHIEVE.equals(key)
                || FOLLOWUP_AVG_DELIVERY_TIME_ACHIEVE.equals(key)
                || FOLLOWUP_DAILY_ORDER_ACHIEVE.equals(key)
                || FOLLOWUP_MEAL_PENERATE_ACHIEVE.equals(key)
                || FOLLOWUP_DAILY_YIELD_ACHIEVE.equals(key)) {
            SchoolDeliveryOperationMonitorGoalAchieveEnum goalAchieveEnum = SchoolDeliveryOperationMonitorGoalAchieveEnum.getByType(Integer.parseInt(trueValue.toString()));
            return goalAchieveEnum == null ? nullShow : goalAchieveEnum.getName();
        }

        // 生命周期
        if (FOLLOWUP_LIFE_CYCLE.equals(key)) {
            SchoolLifecycleEnum lifecycleEnum = SchoolLifecycleEnum.getByType(Integer.parseInt(trueValue.toString()));
            return lifecycleEnum == null ? nullShow : lifecycleEnum.getName();
        }
        return trueValue;
    }

    public void recordSchoolDeliveryAssignmentAuditCancelLog(WmSchoolDeliveryLogBO logBO) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_1", "com.sankuai.meituan.waimai.customer.service.sc.WmScLogSchoolInfoService.recordSchoolDeliveryAssignmentAuditCancelLog(com.sankuai.meituan.waimai.customer.domain.sc.delivery.WmSchoolDeliveryLogBO)");
        if (logBO == null || logBO.getSchoolPrimaryId() == null) {
            log.warn("[WmScLogSchoolInfoService.recordSchoolDeliveryAssignmentCancelTaskLog] input param invalid. logBO = {}",
                    JSONObject.toJSONString(logBO));
            return;
        }

        String logInfo = "操作：撤回审批\\n" +
                "交付编号：" + logBO.getDeliveryId() + "\\n" +
                "审批任务ID：" + logBO.getAuditTaskId() + "\\n";

        insertScOptLog(OptTypeEnum.INSERT.getType(), SC_SCHOOL_DELIVERY_ASSIGNMENT_LOG, logBO.getSchoolPrimaryId(),
                logBO.getUserId(), logBO.getUserName(), logInfo, "");
    }

    public void recordSchoolDeliveryAssignmentAuditRejectLog(WmSchoolDeliveryLogBO logBO) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.service.sc.WmScLogSchoolInfoService.recordSchoolDeliveryAssignmentAuditRejectLog(com.sankuai.meituan.waimai.customer.domain.sc.delivery.WmSchoolDeliveryLogBO)");
        if (logBO == null || logBO.getSchoolPrimaryId() == null) {
            log.warn("[WmScLogSchoolInfoService.recordSchoolDeliveryAssignmentAuditRejectLog] input param invalid. logBO = {}",
                    JSONObject.toJSONString(logBO));
            return;
        }

        String logInfo = "操作：审批驳回\\n" +
                "交付编号：" + logBO.getDeliveryId() + "\\n" +
                "审批任务ID：" + logBO.getAuditTaskId() + "\\n";

        insertScOptLog(OptTypeEnum.INSERT.getType(), SC_SCHOOL_DELIVERY_ASSIGNMENT_LOG, logBO.getSchoolPrimaryId(),
                logBO.getUserId(), logBO.getUserName(), logInfo, "");
    }

    public void recordSchoolDeliveryAssignmentAuditStopLog(WmSchoolDeliveryLogBO logBO) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_2", "com.sankuai.meituan.waimai.customer.service.sc.WmScLogSchoolInfoService.recordSchoolDeliveryAssignmentAuditStopLog(com.sankuai.meituan.waimai.customer.domain.sc.delivery.WmSchoolDeliveryLogBO)");
        if (logBO == null || logBO.getSchoolPrimaryId() == null) {
            log.warn("[WmScLogSchoolInfoService.recordSchoolDeliveryAssignmentAuditStopLog] input param invalid. logBO = {}",
                    JSONObject.toJSONString(logBO));
            return;
        }

        String logInfo = "操作：审批终结\\n" +
                "交付编号：" + logBO.getDeliveryId() + "\\n" +
                "审批任务ID：" + logBO.getAuditTaskId() + "\\n";

        insertScOptLog(OptTypeEnum.INSERT.getType(), SC_SCHOOL_DELIVERY_ASSIGNMENT_LOG, logBO.getSchoolPrimaryId(),
                logBO.getUserId(), logBO.getUserName(), logInfo, "");
    }

    public void recordSchoolDeliveryAssignmentAuditPassLog(WmSchoolDeliveryLogBO logBO) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_2", "com.sankuai.meituan.waimai.customer.service.sc.WmScLogSchoolInfoService.recordSchoolDeliveryAssignmentAuditPassLog(com.sankuai.meituan.waimai.customer.domain.sc.delivery.WmSchoolDeliveryLogBO)");
        if (logBO == null || logBO.getSchoolPrimaryId() == null) {
            log.warn("[WmScLogSchoolInfoService.recordSchoolDeliveryAssignmentAuditPassLog] input param invalid. logBO = {}",
                    JSONObject.toJSONString(logBO));
            return;
        }

        String logInfo = "操作：任务系统审批通过\\n" +
                "交付编号：" + logBO.getDeliveryId() + "\\n" +
                "审批任务ID：" + logBO.getAuditTaskId() + "\\n" +
                "下一审批节点：" + SchoolDeliveryAuditNodeTypeEnum.getByType(logBO.getNextAuditNode()).getName() + "\\n";

        insertScOptLog(OptTypeEnum.INSERT.getType(), SC_SCHOOL_DELIVERY_ASSIGNMENT_LOG, logBO.getSchoolPrimaryId(),
                logBO.getUserId(), logBO.getUserName(), logInfo, "");
    }

    public void recordSchoolDeliveryGoalSetAuditRejectLog(WmSchoolDeliveryLogBO logBO) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_1", "com.sankuai.meituan.waimai.customer.service.sc.WmScLogSchoolInfoService.recordSchoolDeliveryGoalSetAuditRejectLog(com.sankuai.meituan.waimai.customer.domain.sc.delivery.WmSchoolDeliveryLogBO)");
        if (logBO == null || logBO.getSchoolPrimaryId() == null) {
            log.warn("[WmScLogSchoolInfoService.recordSchoolDeliveryGoalSetAuditRejectLog] input param invalid. logBO = {}",
                    JSONObject.toJSONString(logBO));
            return;
        }

        String logInfo = "操作：审批驳回\\n" +
                "交付编号：" + logBO.getDeliveryId() + "\\n" +
                "审批任务ID：" + logBO.getAuditTaskId() + "\\n";

        insertScOptLog(OptTypeEnum.INSERT.getType(), SC_SCHOOL_DELIVERY_GOALSET_LOG, logBO.getSchoolPrimaryId(),
                logBO.getUserId(), logBO.getUserName(), logInfo, "");
    }

    public void recordSchoolDeliveryGoalSetAuditStopLog(WmSchoolDeliveryLogBO logBO) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.service.sc.WmScLogSchoolInfoService.recordSchoolDeliveryGoalSetAuditStopLog(com.sankuai.meituan.waimai.customer.domain.sc.delivery.WmSchoolDeliveryLogBO)");
        if (logBO == null || logBO.getSchoolPrimaryId() == null) {
            log.warn("[WmScLogSchoolInfoService.recordSchoolDeliveryGoalSetAuditStopLog] input param invalid. logBO = {}",
                    JSONObject.toJSONString(logBO));
            return;
        }

        String logInfo = "操作：审批终结\\n" +
                "交付编号：" + logBO.getDeliveryId() + "\\n" +
                "审批任务ID：" + logBO.getAuditTaskId() + "\\n";

        insertScOptLog(OptTypeEnum.INSERT.getType(), SC_SCHOOL_DELIVERY_GOALSET_LOG, logBO.getSchoolPrimaryId(),
                logBO.getUserId(), logBO.getUserName(), logInfo, "");
    }

    public void recordSchoolDeliveryGoalSetAuditPassLog(WmSchoolDeliveryLogBO logBO) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.sc.WmScLogSchoolInfoService.recordSchoolDeliveryGoalSetAuditPassLog(com.sankuai.meituan.waimai.customer.domain.sc.delivery.WmSchoolDeliveryLogBO)");
        if (logBO == null || logBO.getSchoolPrimaryId() == null) {
            log.warn("[WmScLogSchoolInfoService.recordSchoolDeliveryGoalSetAuditPassLog] input param invalid. logBO = {}",
                    JSONObject.toJSONString(logBO));
            return;
        }

        String logInfo = "操作：任务系统审批通过\\n" +
                "交付编号：" + logBO.getDeliveryId() + "\\n" +
                "审批任务ID：" + logBO.getAuditTaskId() + "\\n" +
                "下一审批节点：" + SchoolDeliveryAuditNodeTypeEnum.getByType(logBO.getNextAuditNode()).getName()+ "\\n";

        insertScOptLog(OptTypeEnum.INSERT.getType(), SC_SCHOOL_DELIVERY_GOALSET_LOG, logBO.getSchoolPrimaryId(),
                logBO.getUserId(), logBO.getUserName(), logInfo, "");
    }

    public void recordSchoolDeliveryFollowUpAuditRejectLog(WmSchoolDeliveryLogBO logBO) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.sc.WmScLogSchoolInfoService.recordSchoolDeliveryFollowUpAuditRejectLog(com.sankuai.meituan.waimai.customer.domain.sc.delivery.WmSchoolDeliveryLogBO)");
        if (logBO == null || logBO.getSchoolPrimaryId() == null) {
            log.warn("[WmScLogSchoolInfoService.recordSchoolDeliveryFollowUpAuditRejectLog] input param invalid. logBO = {}",
                    JSONObject.toJSONString(logBO));
            return;
        }

        String logInfo = "操作：审批驳回\\n" +
                "交付编号：" + logBO.getDeliveryId() + "\\n" +
                "审批任务ID：" + logBO.getAuditTaskId() + "\\n";

        insertScOptLog(OptTypeEnum.INSERT.getType(), SC_SCHOOL_DELIVERY_FOLLOWUP_LOG, logBO.getSchoolPrimaryId(),
                logBO.getUserId(), logBO.getUserName(), logInfo, "");
    }

    public void recordSchoolDeliveryFollowUpAuditStopLog(WmSchoolDeliveryLogBO logBO) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_2", "com.sankuai.meituan.waimai.customer.service.sc.WmScLogSchoolInfoService.recordSchoolDeliveryFollowUpAuditStopLog(com.sankuai.meituan.waimai.customer.domain.sc.delivery.WmSchoolDeliveryLogBO)");
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_2", "com.sankuai.meituan.waimai.customer.service.sc.WmScLogSchoolInfoService.recordSchoolDeliveryFollowUpAuditStopLog(com.sankuai.meituan.waimai.customer.domain.sc.delivery.WmSchoolDeliveryLogBO)");
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_2", "com.sankuai.meituan.waimai.customer.service.sc.WmScLogSchoolInfoService.recordSchoolDeliveryFollowUpAuditStopLog(com.sankuai.meituan.waimai.customer.domain.sc.delivery.WmSchoolDeliveryLogBO)");
        if (logBO == null || logBO.getSchoolPrimaryId() == null) {
            log.warn("[WmScLogSchoolInfoService.recordSchoolDeliveryFollowUpAuditStopLog] input param invalid. logBO = {}",
                    JSONObject.toJSONString(logBO));
            return;
        }

        String logInfo = "操作：审批终结\\n" +
                "交付编号：" + logBO.getDeliveryId() + "\\n" +
                "审批任务ID：" + logBO.getAuditTaskId() + "\\n";

        insertScOptLog(OptTypeEnum.INSERT.getType(), SC_SCHOOL_DELIVERY_FOLLOWUP_LOG, logBO.getSchoolPrimaryId(),
                logBO.getUserId(), logBO.getUserName(), logInfo, "");
    }

    public void recordSchoolDeliveryFollowUpAuditPassLog(WmSchoolDeliveryLogBO logBO) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_2", "com.sankuai.meituan.waimai.customer.service.sc.WmScLogSchoolInfoService.recordSchoolDeliveryFollowUpAuditPassLog(com.sankuai.meituan.waimai.customer.domain.sc.delivery.WmSchoolDeliveryLogBO)");
        if (logBO == null || logBO.getSchoolPrimaryId() == null) {
            log.warn("[WmScLogSchoolInfoService.recordSchoolDeliveryFollowUpAuditPassLog] input param invalid. logBO = {}",
                    JSONObject.toJSONString(logBO));
            return;
        }

        String logInfo = "操作：任务系统审批通过\\n" +
                "交付编号：" + logBO.getDeliveryId() + "\\n" +
                "审批任务ID：" + logBO.getAuditTaskId() + "\\n" +
                "下一审批节点：" + SchoolDeliveryAuditNodeTypeEnum.getByType(logBO.getNextAuditNode()).getName() + "\\n";

        insertScOptLog(OptTypeEnum.INSERT.getType(), SC_SCHOOL_DELIVERY_FOLLOWUP_LOG, logBO.getSchoolPrimaryId(),
                logBO.getUserId(), logBO.getUserName(), logInfo, "");
    }

    public void recordSchoolDeliveryGoalSetCancelTaskLog(WmSchoolDeliveryLogBO logBO) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.service.sc.WmScLogSchoolInfoService.recordSchoolDeliveryGoalSetCancelTaskLog(com.sankuai.meituan.waimai.customer.domain.sc.delivery.WmSchoolDeliveryLogBO)");
        if (logBO == null || logBO.getSchoolPrimaryId() == null) {
            log.warn("[WmScLogSchoolInfoService.recordSchoolDeliveryGoalSetCancelTaskLog] input param invalid. logBO = {}",
                    JSONObject.toJSONString(logBO));
            return;
        }

        String logInfo = "操作：撤回审批\\n" +
                "交付编号：" + logBO.getDeliveryId() + "\\n" +
                "审批任务ID：" + logBO.getAuditTaskId() + "\\n";

        insertScOptLog(OptTypeEnum.INSERT.getType(), SC_SCHOOL_DELIVERY_GOALSET_LOG, logBO.getSchoolPrimaryId(),
                logBO.getUserId(), logBO.getUserName(), logInfo, "");
    }

    public void recordSchoolDeliveryFollowUpCancelTaskLog(WmSchoolDeliveryLogBO logBO) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_1", "com.sankuai.meituan.waimai.customer.service.sc.WmScLogSchoolInfoService.recordSchoolDeliveryFollowUpCancelTaskLog(com.sankuai.meituan.waimai.customer.domain.sc.delivery.WmSchoolDeliveryLogBO)");
        if (logBO == null || logBO.getSchoolPrimaryId() == null) {
            log.warn("[WmScLogSchoolInfoService.recordSchoolDeliveryFollowUpCancelTaskLog] input param invalid. logBO = {}",
                    JSONObject.toJSONString(logBO));
            return;
        }

        String logInfo = "操作：撤回审批\\n" +
                "交付编号：" + logBO.getDeliveryId() + "\\n" +
                "审批任务ID：" + logBO.getAuditTaskId() + "\\n";

        insertScOptLog(OptTypeEnum.INSERT.getType(), SC_SCHOOL_DELIVERY_FOLLOWUP_LOG, logBO.getSchoolPrimaryId(),
                logBO.getUserId(), logBO.getUserName(), logInfo, "");
    }

    private String getUserNameAndMisByUid(String uid) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_2", "com.sankuai.meituan.waimai.customer.service.sc.WmScLogSchoolInfoService.getUserNameAndMisByUid(java.lang.String)");
        try {
            WmEmploy wmEmploy = wmScEmployAdaptor.getWmEmployByUid(Integer.valueOf(uid));
            return wmEmploy.getName() + "(" + wmEmploy.getMisId() + ")";
        } catch (Exception e) {
            log.error("[WmScLogSchoolInfoService.getUserNameAndMisByUid] Exception. uid = {}", uid, e);
        }
        return Strings.EMPTY;
    }


    /**
     * 由于范围变更引起档口绑定任务解绑操作日志
     * @param outOfScPoiList 不在学校范围内的门店列表
     * @param outOfScClueList 不在学校范围内的线索列表
     * @param bindDOList 去重后的档口绑定任务
     * @return
     */
    public String composeUnbindClueAndWmPoiBySchoolAreaLog(List<WmCanteenStallBindDO> outOfScPoiList,
                                                           List<WmCanteenStallBindDO> outOfScClueList,
                                                           List<WmCanteenStallBindDO> bindDOList) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.service.sc.WmScLogSchoolInfoService.composeUnbindClueAndWmPoiBySchoolAreaLog(java.util.List,java.util.List,java.util.List)");
        StringBuilder stringBuilder = new StringBuilder().append("操作：档口绑定任务解绑\\n");
        stringBuilder.append("提交渠道：学校管理系统\\n");
        if (CollectionUtils.isNotEmpty(outOfScPoiList)) {
            stringBuilder.append("范围外门店ID：");
            List<Long> wmPoiIdList = outOfScPoiList.stream()
                    .map(WmCanteenStallBindDO::getWmPoiId)
                    .collect(Collectors.toList());
            stringBuilder.append(StringUtils.join(wmPoiIdList, "、")).append("\\n");
        }

        if (CollectionUtils.isNotEmpty(outOfScClueList)) {
            stringBuilder.append("范围外线索ID：");
            List<Long> wdcClueIdList = outOfScClueList.stream()
                    .map(WmCanteenStallBindDO::getWdcClueId)
                    .collect(Collectors.toList());
            stringBuilder.append(StringUtils.join(wdcClueIdList, "、")).append("\\n");
        }

        List<Integer> bindIdList = bindDOList.stream()
                .map(WmCanteenStallBindDO::getId)
                .collect(Collectors.toList());
        stringBuilder.append("档口绑定任务ID：").append(StringUtils.join(bindIdList, "、")).append("\\n");

        List<Long> wdcClueIdList = bindDOList.stream()
                .map(WmCanteenStallBindDO::getWdcClueId)
                .collect(Collectors.toList());
        stringBuilder.append("[关联线索解绑] WDC线索ID：").append(StringUtils.join(wdcClueIdList, "、")).append("\\n");


        List<Long> wmPoiIdList = bindDOList.stream()
                .map(WmCanteenStallBindDO::getWmPoiId)
                .collect(Collectors.toList());
        stringBuilder.append("[关联门店解绑] 外卖门店ID：").append(StringUtils.join(wmPoiIdList, "、")).append("\\n");

        return stringBuilder.toString();
    }

    /**
     * 修改食堂信息(UTime)
     * @param wmScLogDBList wmScLogDBList
     * @return 更新食堂的更新时间
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    @Transactional
    public int changeCanteenUTime(List<WmScLogDB> wmScLogDBList) {
        log.info("[WmScLogSchoolInfoService.changeCanteenUTime] input param: wmScLogDBList = {}", JSONUtil.toJSONString(wmScLogDBList));
        //1.获取该食堂信息最新的操作记录
        if (wmScLogDBList == null || wmScLogDBList.size() == 0){
            return 0;
        }

        //2.处理数据
        ArrayList<WmCanteenDB> wmCanteenDBArrayList = new ArrayList<>();
        for (WmScLogDB wmScLogDB : wmScLogDBList) {
            Integer moduleType = Integer.valueOf(wmScLogDB.getModuleType());
            Integer moduleId = wmScLogDB.getModuleId();
            Integer utime = wmScLogDB.getUtime();
            String logInfo = wmScLogDB.getLog();
            //模块类型:学校1；食堂:2
            if(moduleType.equals(1)){
                continue;
            }
            //食堂信息审批驳回(蜂鸟系统)  食堂信息审批驳回(任务系统)
            //logInfo为审批驳回,审批拒绝的不记录
            if(logInfo.contains("操作：审批驳回")){
                continue;
            }
            WmCanteenDB updateWmCanteenDB = new WmCanteenDB();
            updateWmCanteenDB.setId(moduleId);

            updateWmCanteenDB.setUtime(utime);
            wmCanteenDBArrayList.add(updateWmCanteenDB);
        }

        //3.更新食堂信息
        try {
            if (CollectionUtils.isNotEmpty(wmCanteenDBArrayList)) {
                wmCanteenDBArrayList.forEach(updateWmCanteenDB-> wmCanteenMapper.updateCanteen(updateWmCanteenDB));
            }
        } catch (Exception e) {
            log.error("[changeCanteenUTime] 更新食堂范围日志异常, wmCanteenDBArrayList = {}",
                    JSONObject.toJSONString(wmCanteenDBArrayList), e);
        }
        return 1;
    }

    public void recordConfirmSchoolAreaLog(Integer schoolPrimaryId, Integer uid, String operatorMis) {
        String logInfo = String.format("[学校范围]确认学校范围信息,学校id:%d", schoolPrimaryId);
        if (StringUtils.isNotBlank(logInfo)) {
            insertScOptLog(OptTypeEnum.UPDATE.getType(), SC_SCHOOLAREA_LOG, schoolPrimaryId,
                    uid, operatorMis, logInfo, "");
        }
    }
}
