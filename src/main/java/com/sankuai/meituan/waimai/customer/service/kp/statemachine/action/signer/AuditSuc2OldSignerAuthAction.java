package com.sankuai.meituan.waimai.customer.service.kp.statemachine.action.signer;

import com.alibaba.fastjson.JSON;
import com.sankuai.meituan.waimai.customer.dao.WmCustomerKpAuditMapper;
import com.sankuai.meituan.waimai.customer.dao.WmCustomerKpTempDBMapper;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerKpAudit;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerSensitiveWordsService;
import com.sankuai.meituan.waimai.customer.service.kp.WmCustomerKpAuditService;
import com.sankuai.meituan.waimai.customer.service.kp.statemachine.action.KpSignerAbstractAction;
import com.sankuai.meituan.waimai.customer.service.kp.statemachine.constant.event.KpSignerEventEnum;
import com.sankuai.meituan.waimai.customer.service.kp.statemachine.context.KpSignerStatusMachineContext;
import com.sankuai.meituan.waimai.customer.service.kp.statemachine.machine.KpSignerBaseSM;
import com.sankuai.meituan.waimai.thrift.customer.constant.KpAuditConstants;
import com.sankuai.meituan.waimai.thrift.customer.constant.KpSignerStateMachine;
import com.sankuai.meituan.waimai.thrift.customer.domain.kp.WmCustomerKp;
import com.sankuai.meituan.waimai.thrift.customer.domain.kp.WmCustomerKpTemp;
import com.sankuai.meituan.waimai.thrift.exception.StatusMachineException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import static com.sankuai.meituan.waimai.thrift.customer.constant.KpConstants.UN_VALID;

/**
 * <AUTHOR>
 * @date 20240423
 * @desc 审核成功发起原签约人授权事件
 */
@Service
@Slf4j
public class AuditSuc2OldSignerAuthAction extends KpSignerAbstractAction {


    @Autowired
    private WmCustomerKpAuditService wmCustomerKpAuditService;

    @Autowired
    private WmCustomerSensitiveWordsService wmCustomerSensitiveWordsService;

    @Autowired
    private WmCustomerKpTempDBMapper wmCustomerKpTempDBMapper;

    @Autowired
    private WmCustomerKpAuditMapper wmCustomerKpAuditMapper;

    /**
     * KP签约人提审事件
     *
     * @param from
     * @param to
     * @param eventEnum
     * @param context
     * @param kpSignerBaseSM
     */
    @Override
    public void execute(KpSignerStateMachine from, KpSignerStateMachine to, KpSignerEventEnum eventEnum,
                        KpSignerStatusMachineContext context, KpSignerBaseSM kpSignerBaseSM) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_1", "c.s.m.w.customer.service.kp.statemachine.action.signer.AuditSuc2OldSignerAuthAction.execute(KpSignerStateMachine,KpSignerStateMachine,KpSignerEventEnum,KpSignerStatusMachineContext,KpSignerBaseSM)");
        log.info("AuditSuc2OldSignerAuthAction.execute,审核成功原签约人授权action,from={},to={},context={}", from, to, JSON.toJSONString(context));

        try {
            WmCustomerKpTemp kpTemp = context.getTempKp();
            wmCustomerSensitiveWordsService.readKpWhenSelect(kpTemp);
            WmCustomerKp singerKp = context.getWmCustomerKp();
            wmCustomerSensitiveWordsService.readKpWhenSelect(singerKp);
            //进行原签约人授权
            wmCustomerKpAuditService.commitOriginSignerAuth(singerKp, wmCustomerKpAuditService.transformWmCustomerKpTemp(kpTemp), context.getOpUid(), context.getOpUName());
            //更新临时数据状态
            kpTemp.setFailReason("");
            kpTemp.setState(KpSignerStateMachine.CHANGE_ORIGIN_SIGNER_AUTHING.getState());
            wmCustomerSensitiveWordsService.writeKpWhenInsertOrUpdate(kpTemp);
            wmCustomerKpTempDBMapper.updateByPrimaryKey(kpTemp);
            //更新KP审核记录状态
            WmCustomerKpAudit audit = context.getWmCustomerKpAudit();
            String result = KpAuditConstants.TYPE_SPECIAL == audit.getType() ? "特批审核通过" : "代理人审核通过";
            audit.setResult(result);
            audit.setValid(UN_VALID);
            wmCustomerKpAuditMapper.updateByPrimaryKey(audit);

        } catch (Exception e) {
            log.error("AuditSuc2OldSignerAuthAction.execute,KP签约人发原签约人授权操作异常,context={}", JSON.toJSONString(context), e);
            throw new StatusMachineException("KP签约人发原签约人授权操作异常");
        }

    }
}
