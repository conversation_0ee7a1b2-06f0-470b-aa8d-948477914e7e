package com.sankuai.meituan.waimai.customer.service.sc.dao;

import com.alibaba.fastjson.JSONObject;
import com.sankuai.meituan.waimai.customer.dao.sc.WmCanteenMapper;
import com.sankuai.meituan.waimai.customer.domain.sc.WmCanteenDB;
import com.sankuai.meituan.waimai.customer.service.sc.canteen.sensitive.WmScCanteenSensitiveWordsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
public class WmScCanteenDao {

    @Autowired
    private WmCanteenMapper wmCanteenMapper;

    @Autowired
    private WmScCanteenSensitiveWordsService wmScCanteenSensitiveWordsService;

    public WmCanteenDB getById(Integer canteenId) {
        WmCanteenDB wmCanteenDB = wmCanteenMapper.selectCanteenById(canteenId);
        wmScCanteenSensitiveWordsService.readWhenSelect(wmCanteenDB);
        return wmCanteenDB;
    }

    public List<WmCanteenDB> getCanteenListByPrimaryIdList(List<Integer> canteenIdList) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.sc.dao.WmScCanteenDao.getCanteenListByPrimaryIdList(java.util.List)");
        List<WmCanteenDB> wmCanteenDBList = wmCanteenMapper.selectCanteensByIdsALL(canteenIdList);
        for(WmCanteenDB wmCanteenDB : wmCanteenDBList){
            wmScCanteenSensitiveWordsService.readWhenSelect(wmCanteenDB);
        }
        return wmCanteenDBList;
    }

    public List<WmCanteenDB> getByContractId(int contractorId) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.service.sc.dao.WmScCanteenDao.getByContractId(int)");
        List<WmCanteenDB> list = wmCanteenMapper.getCanteenByContractorId(contractorId);
        wmScCanteenSensitiveWordsService.readWhenSelect(list);
        return list;
    }

    public void updateStoreNum(WmCanteenDB canteenDB) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.sc.dao.WmScCanteenDao.updateStoreNum(com.sankuai.meituan.waimai.customer.domain.sc.WmCanteenDB)");
        log.info("[WmScCanteenDao.updateStoreNum] input param: canteenDB = {}", JSONObject.toJSONString(canteenDB));
        wmScCanteenSensitiveWordsService.writeSourceWhenUpdate(canteenDB);
        wmCanteenMapper.updateCanteen(canteenDB);
    }
}
