package com.sankuai.meituan.waimai.customer.service.sc.area;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.sankuai.meituan.waimai.customer.domain.sc.area.WmScPoint;
import com.vividsolutions.jts.geom.*;
import com.vividsolutions.jts.geom.impl.CoordinateArraySequenceFactory;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Slf4j
public class AreaCalculatorUtils {

    private static final CoordinateArraySequenceFactory coordinateArraySequenceFactory = CoordinateArraySequenceFactory.instance();

    private static final PrecisionModel precisionModel = new PrecisionModel(1E+6);

    public static final GeometryFactory geometryFactory = new GeometryFactory(precisionModel);

    public static final double EARTH_RADIUS = 6378.137 * 1000;

    private static final double RAND_EARTH_RADIUS = EARTH_RADIUS * 10000;

    public static final double FACTOR = 1000000;

    public static double calculate(Geometry geometry) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.service.sc.area.AreaCalculatorUtils.calculate(com.vividsolutions.jts.geom.Geometry)");
        if (geometry == null || !(geometry instanceof Polygon || geometry instanceof MultiPolygon)) {
            return 0.0;
        }
        List<Coordinate> convertedCoordinates = new ArrayList<>(geometry.getNumPoints() + 1);
        double minX = Integer.MAX_VALUE;
        double minY = Integer.MAX_VALUE;
        for (Coordinate coordinate : geometry.getCoordinates()) {
            if (minX > coordinate.x) {
                minX = coordinate.x;
            }
            if (minY > coordinate.y) {
                minY = coordinate.y;
            }
        }

        for (Coordinate coordinate : geometry.getCoordinates()) {
            double distancey = getDistance(coordinate.x, minY, coordinate.x, coordinate.y);
            double distancex = getDistance(minX, coordinate.y, coordinate.x, coordinate.y);
            convertedCoordinates.add(new Coordinate(distancex, distancey));
        }

        if (convertedCoordinates.size() < 3) {
            log.warn("多边形点个数小于三个，无法求面积：" + geometry);
            return 0.0;
        }

        Coordinate fc = convertedCoordinates.get(0);
        Coordinate lc = convertedCoordinates.get(convertedCoordinates.size() - 1);
        if (!fc.equals2D(lc)) {
            convertedCoordinates.add(fc);
        }

        Polygon convertedPolygon = createRawPolygon(convertedCoordinates.toArray(new Coordinate[0]));
        return convertedPolygon.getArea();
    }

    public static Polygon createPolygon(List<WmScPoint> list) {
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        if (list.size() < 3) {
            return null;
        }
        List<Coordinate> points = Lists.newArrayList();
        for (WmScPoint wmScPoint:list) {
            double x = wmScPoint.getX();
            double y = wmScPoint.getY();
            points.add(new Coordinate(x, y));
        }

        if (!points.get(0).equals(points.get(points.size() - 1))) {
            points.add(points.get(0));
        }
        return createRawPolygon(points.toArray(new Coordinate[0]));
    }

    public static Point createPoint(Double latitude, Double longitude) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_1", "com.sankuai.meituan.waimai.customer.service.sc.area.AreaCalculatorUtils.createPoint(java.lang.Double,java.lang.Double)");
        return geometryFactory.createPoint(new Coordinate(latitude, longitude));
    }

    private static Polygon createRawPolygon(Coordinate... coordinates) {
        return new Polygon(createLinearRing(createCoordinateSequence(coordinates)), null, geometryFactory);
    }

    private static LinearRing createLinearRing(CoordinateSequence coordinateSequence) {
        if (coordinateSequence == null || coordinateSequence.size() < 4) {
            return null;
        }
        return new LinearRing(coordinateSequence, geometryFactory);
    }

    private static CoordinateSequence createCoordinateSequence(Coordinate... coordinates) {
        List<Coordinate> coordinateList = Lists.newArrayList(coordinates);
        return createCoordinateSequence2(coordinateList);
    }

    private static CoordinateSequence createCoordinateSequence2(final List<Coordinate> coordinates) {
        if (CollectionUtils.isEmpty(coordinates) || coordinates.size() < 4) {
            return null;
        }

        if (coordinates.get(0).equals(coordinates.get(coordinates.size() - 1))) {
            return coordinateArraySequenceFactory.create(coordinates.toArray(new Coordinate[0]));
        }

        List<Coordinate> newList = Lists.newArrayList(coordinates);
        newList.add(coordinates.get(0));
        return coordinateArraySequenceFactory.create(newList.toArray(new Coordinate[0]));
    }

    private static double rad(double d) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.service.sc.area.AreaCalculatorUtils.rad(double)");
        return d * Math.PI / 180.0;
    }

    public static double getDistance(double lat1, double lng1, double lat2, double lng2) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.sc.area.AreaCalculatorUtils.getDistance(double,double,double,double)");
        double radLat1 = rad(lat1);
        double radLat2 = rad(lat2);
        double a = radLat1 - radLat2;
        double b = rad(lng1) - rad(lng2);
        double s = 2 * Math.asin(Math.sqrt(Math.pow(Math.sin(a / 2), 2) +
                Math.cos(radLat1) * Math.cos(radLat2) * Math.pow(Math.sin(b / 2), 2)));
        s = Math.round(s * RAND_EARTH_RADIUS) / 10000;
        return s;
    }

//    /**
//     * 画圆
//     */
//    public static Polygon createCircle(double x, double y, double latRadius, double lngRadius) {
//        int sides = MccConfig.getCircleSides();
//        Coordinate coords[] = new Coordinate[sides + 1];
//        for (int i = 0; i < sides; i++) {
//            double angle = ((double) i / (double) sides) * Math.PI * 2.0;
//            double dx = Math.cos(angle) * latRadius;
//            double dy = Math.sin(angle) * lngRadius;
//            coords[i] = new Coordinate((double) x + dx, (double) y + dy);
//        }
//
//        coords[sides] = coords[0];
//        LinearRing ring = geometryFactory.createLinearRing(coords);
//        Polygon polygon = geometryFactory.createPolygon(ring, null);
//
//        return polygon;
//    }

    /**
     * 初始化范围，4个点
     *
     * @param longitude
     * @param latitude
     * @return
     */
    public static List<String> createInitPoint(long longitude, long latitude) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.service.sc.area.AreaCalculatorUtils.createInitPoint(long,long)");
        //偏差经纬度
        BigDecimal degree = BigDecimal.valueOf(0.01);
        BigDecimal lng = BigDecimal.valueOf(longitude).divide(BigDecimal.valueOf(FACTOR)), lat = BigDecimal.valueOf(latitude).divide(BigDecimal.valueOf(FACTOR));
        List<Map> list = Lists.newArrayListWithExpectedSize(4);
        list.add(ImmutableMap.of("x", lng.add(degree), "y", lat));
        list.add(ImmutableMap.of("x", lng, "y", lat.add(degree)));
        list.add(ImmutableMap.of("x", lng.subtract(degree), "y", lat));
        list.add(ImmutableMap.of("x", lng, "y", lat.subtract(degree)));
        return Lists.newArrayList(JSONObject.toJSONString(list));
    }

}
