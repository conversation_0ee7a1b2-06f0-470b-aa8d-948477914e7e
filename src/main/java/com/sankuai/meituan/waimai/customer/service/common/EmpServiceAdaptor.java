package com.sankuai.meituan.waimai.customer.service.common;

import com.sankuai.meituan.org.opensdk.model.domain.Emp;
import com.sankuai.meituan.org.opensdk.model.domain.items.EmpItems;
import com.sankuai.meituan.org.opensdk.service.EmpService;
import com.sankuai.meituan.org.queryservice.domain.base.Paging;
import com.sankuai.meituan.org.queryservice.exception.MDMThriftException;
import java.util.ArrayList;
import java.util.List;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class EmpServiceAdaptor {

    static Logger logger = LoggerFactory.getLogger(EmpServiceAdaptor.class);

    @Autowired
    EmpService empService;

    public String getPhone(String misId) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.common.EmpServiceAdaptor.getPhone(java.lang.String)");
        String mobile = null;
        try {
            Emp emp = empService.queryByMis(misId, null);
            mobile = emp == null ? "" : empService.empDataDecode(emp.getEmpId(), "mobile");
        } catch (MDMThriftException e) {
            logger.error("查询手机号失败", e);
        }
        return mobile;
    }

    public String getPhone(int uid) {
        String mobile = null;
        try {
            logger.info("#getPhone#uid={}", uid);
            mobile = empService.empDataDecode(String.valueOf(uid), "mobile");
            logger.info("#getPhone#mobile={}", mobile);
        } catch (MDMThriftException e) {
            logger.error("查询手机号失败", e);
        }
        if(mobile == null){
            logger.error("getPhone error,uid={}",uid);
        }
        return mobile;
    }

    public List<String> queryMisIdsByOrgId(String orgId) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.common.EmpServiceAdaptor.queryMisIdsByOrgId(java.lang.String)");
        List<String> misIds = new ArrayList<>();
        try {
            EmpItems empItems = empService.queryEmp(orgId, 0, null, new Paging(0, Paging.MAX_SIZE));
            if (empItems != null && CollectionUtils.isNotEmpty(empItems.getItems())) {
                for (Emp emp : empItems.getItems()) {
                    misIds.add(emp.getMis());
                }
            }
        } catch (Exception e) {
            logger.error("根据组织id查询组织下mis号列表失败 orgId:{}", orgId, e);
        }
        return misIds;
    }

}
