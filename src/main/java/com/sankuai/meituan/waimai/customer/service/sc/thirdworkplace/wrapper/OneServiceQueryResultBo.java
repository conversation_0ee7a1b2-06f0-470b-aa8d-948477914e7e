package com.sankuai.meituan.waimai.customer.service.sc.thirdworkplace.wrapper;

import com.sankuai.djdata.readata.protocol.openapi.entity.DataTitle;
import com.sankuai.djdata.readata.protocol.openapi.entity.Statistics;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class OneServiceQueryResultBo {

    /** title列表 **/
    private List<DataTitle> titleList;

    /** 数据 **/
    private List<Map<String, String>> dataList;

    /** queryId,只有异步缓存的时候会有值 */
    private String queryId;

    /** 查询语句 **/
    private List<String> plan;

    /** 统计信息 **/
    private Map<String, Statistics> stats;

    private Map<String, String> datasetMetadata;
}
