package com.sankuai.meituan.waimai.customer.service.sc.thirdworkplace.wrapper;

import com.sankuai.meituan.waimai.customer.service.sc.thirdworkplace.WmScThirdWorkplaceQueryListItem;
import com.sankuai.meituan.waimai.customer.service.sc.thirdworkplace.WmScThirdWorkplaceQueryMetrics;
import com.sankuai.meituan.waimai.customer.service.sc.thirdworkplace.config.OneServiceQueryMappingConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.lang.reflect.Field;
import java.util.*;

/**
 * 数据格式转换工具类
 */
@Component
@Slf4j
public class OneServiceDataConverter {


    /**
     * 列转行
     */
    public List<Map<String, String>> columnsToRows(Map<String, List<String>> columnData) {
        if (columnData == null || columnData.isEmpty()) {
            return Collections.emptyList();
        }

        // 预计算行数和列名
        String[] columnNames = columnData.keySet().toArray(new String[0]);
        int rowCount = columnData.values().iterator().next().size();

        // 预分配容量
        List<Map<String, String>> result = new ArrayList<>(rowCount);

        for (int i = 0; i < rowCount; i++) {
            Map<String, String> row = new HashMap<>(columnNames.length);
            for (String columnName : columnNames) {
                List<String> columnValues = columnData.get(columnName);
                row.put(columnName, i < columnValues.size() ? columnValues.get(i) : null);
            }
            result.add(row);
        }

        return result;
    }

    /**
     * 转换为WmScThirdWorkplaceQueryListItem列表
     *
     * @param resultBo OneService查询结果
     * @return 转换后的对象列表
     */
    public List<WmScThirdWorkplaceQueryListItem> convertToWorkplaceListItems(
            OneServiceQueryMappingConfig mappingConfig,
            OneServiceQueryResultBo resultBo) {

        if (resultBo == null || resultBo.getDataList() == null || mappingConfig == null) {
            return Collections.emptyList();
        }

        List<Map<String, String>> dataList = resultBo.getDataList();
        List<String> indicatorList = mappingConfig.getItemIndicatorList();
        Map<String, String> fieldMapping = mappingConfig.getItemMappingConfig();

        return convertToObjects(dataList, indicatorList, fieldMapping, WmScThirdWorkplaceQueryListItem.class);
    }

    /**
     * 转换为WmScThirdWorkplaceQueryMetrics对象（单个对象）
     *
     * @param resultBo OneService查询结果
     * @return 转换后的对象
     */
    public WmScThirdWorkplaceQueryMetrics convertToWorkplaceMetrics(
            OneServiceQueryMappingConfig mappingConfig,
            OneServiceQueryResultBo resultBo) {

        if (resultBo == null || resultBo.getDataList() == null || mappingConfig == null) {
            return null;
        }

        List<Map<String, String>> dataList = resultBo.getDataList();
        List<String> indicatorList = mappingConfig.getMetricIndicatorList();
        Map<String, String> fieldMapping = mappingConfig.getMetricMappingConfig();

        // 只处理第一行数据，因为指标数据通常只有一行汇总数据
        if (dataList.isEmpty()) {
            return null;
        }

        return convertToObject(dataList.get(0), indicatorList, fieldMapping, WmScThirdWorkplaceQueryMetrics.class);
    }

    /**
     * 通用转换方法 - 多个对象
     */
    private <T> List<T> convertToObjects(List<Map<String, String>> dataList,
                                         List<String> indicatorList,
                                         Map<String, String> fieldMapping,
                                         Class<T> targetClass) {
        if (dataList == null || indicatorList == null || fieldMapping == null) {
            return Collections.emptyList();
        }

        List<T> resultList = new ArrayList<>();

        for (Map<String, String> rowData : dataList) {
            T item = convertToObject(rowData, indicatorList, fieldMapping, targetClass);
            if (item != null) {
                resultList.add(item);
            }
        }

        return resultList;
    }

    /**
     * 通用转换方法 - 单个对象
     */
    private <T> T convertToObject(Map<String, String> rowData,
                                  List<String> indicatorList,
                                  Map<String, String> fieldMapping,
                                  Class<T> targetClass) {
        if (rowData == null || indicatorList == null || fieldMapping == null) {
            return null;
        }

        Map<String, Field> fieldMap = getFieldMap(targetClass);

        try {
            T item = targetClass.newInstance();

            // 遍历所有映射关系
            for (Map.Entry<String, String> entry : fieldMapping.entrySet()) {
                String sourceKey = entry.getKey();
                String targetFieldName = entry.getValue();

                // 检查数据中是否存在该字段
                if (!rowData.containsKey(sourceKey)) {
                    continue;
                }

                String value = rowData.get(sourceKey);
                setFieldValue(item, targetFieldName, value, fieldMap);
            }

            return item;
        } catch (Exception e) {
            log.error("创建对象失败: " + targetClass.getSimpleName(), e);
            return null;
        }
    }

    /**
     * 获取类的所有字段映射
     */
    private Map<String, Field> getFieldMap(Class<?> clazz) {
        Map<String, Field> fieldMap = new HashMap<>();
        Field[] fields = clazz.getDeclaredFields();

        for (Field field : fields) {
            field.setAccessible(true);
            fieldMap.put(field.getName(), field);
        }

        return fieldMap;
    }

    /**
     * 通过反射设置字段值
     */
    private void setFieldValue(Object target, String fieldName, String value, Map<String, Field> fieldMap) {
        try {
            Field field = fieldMap.get(fieldName);
            if (field == null || value == null || value.trim().isEmpty()) {
                return;
            }

            Class<?> fieldType = field.getType();
            Object convertedValue = convertValue(value, fieldType);

            if (convertedValue != null) {
                field.set(target, convertedValue);
            }
        } catch (Exception e) {
            System.err.println("设置字段值失败: fieldName=" + fieldName + ", value=" + value + ", error=" + e.getMessage());
        }
    }

    /**
     * 根据字段类型转换值
     */
    private Object convertValue(String value, Class<?> targetType) {
        try {
            if (targetType == String.class) {
                return value;
            } else if (targetType == Integer.class || targetType == int.class) {
                return Integer.valueOf(value);
            } else if (targetType == Long.class || targetType == long.class) {
                return Long.valueOf(value);
            } else if (targetType == Double.class || targetType == double.class) {
                return Double.valueOf(value);
            } else if (targetType == Float.class || targetType == float.class) {
                return Float.valueOf(value);
            } else if (targetType == Boolean.class || targetType == boolean.class) {
                return Boolean.valueOf(value);
            } else if (targetType == List.class) {
                // 对于List类型，假设是逗号分隔的字符串
                return parseListValue(value);
            }
        } catch (NumberFormatException e) {
            System.err.println("数值转换失败: value=" + value + ", targetType=" + targetType.getSimpleName());
        }

        return null;
    }

    /**
     * 解析List类型的值（假设是逗号分隔的整数）
     */
    private List<Integer> parseListValue(String value) {
        if (value == null || value.trim().isEmpty()) {
            return new ArrayList<>();
        }

        List<Integer> result = new ArrayList<>();
        String[] parts = value.split(",");

        for (String part : parts) {
            try {
                result.add(Integer.valueOf(part.trim()));
            } catch (NumberFormatException e) {
                System.err.println("List元素转换失败: " + part);
            }
        }

        return result;
    }
}