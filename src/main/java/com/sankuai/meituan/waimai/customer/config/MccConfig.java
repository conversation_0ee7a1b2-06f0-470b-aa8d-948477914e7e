package com.sankuai.meituan.waimai.customer.config;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.dianping.lion.client.util.JsonUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import com.github.pagehelper.util.StringUtil;
import com.google.common.base.Splitter;
import com.google.common.collect.Sets;
import com.sankuai.meituan.util.ConfigUtilAdapter;
import com.sankuai.meituan.waimai.customer.bo.brand.BizOrgCodeBrandTypeRelBO;
import com.sankuai.meituan.waimai.customer.bo.sign.BizOrdCodeSignLinkRelBO;
import com.sankuai.meituan.waimai.customer.contract.bo.ContractTypeBO;
import com.sankuai.meituan.waimai.customer.contract.bo.RecordTypeBO;

import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

import com.sankuai.meituan.waimai.customer.service.sc.thirdworkplace.config.OneServiceQueryMappingConfig;
import com.sankuai.meituan.waimai.thrift.customer.domain.sc.thirdworkplace.WmScThirdWorkplaceQueryEnumDTO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.ap.internal.util.Strings;

/**
 * <NAME_EMAIL>
 * on 2016/11/25.
 */
public class MccConfig {

    public static Splitter SPLITTER = Splitter.on(",").omitEmptyStrings().trimResults();

    /**
     * 获取批量操作客户关联品牌最大数
     *
     * @return
     */
    public static int getCustomerBrandBatchNum() {
        return ConfigUtilAdapter.getInt("customer_brand_batch_num", 30);
    }

    /**
     * 获取删除客户，关联门店最大数
     *
     * @return
     */
    public static int getCustomerDeletePoiBatchNum() {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.config.MccConfig.getCustomerDeletePoiBatchNum()");
        return ConfigUtilAdapter.getInt("customer_delete_poi_batch_num", 5);
    }

    /**
     * 客户重复编号校验
     * false:新老数据校验规则不一致，新数据新增全量校验，生效后则只与新增数据校验
     *
     * @return
     */
    public static Boolean getCustomerDuplicateNumberCheck() {
        return ConfigUtilAdapter.getBoolean("customer_duplicate_number_check", false);
    }

    /**
     * 客户解绑门店异常需要通知的邮箱
     *
     * @return
     */
    public static String getCustomerUnBindErrorNoticeEmail() {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_2", "com.sankuai.meituan.waimai.customer.config.MccConfig.getCustomerUnBindErrorNoticeEmail()");
        return ConfigUtilAdapter.getString("customer_unbind_error_notice_email", "<EMAIL>");
    }

    /**
     * 提供创建任务时间提示灰度
     *
     * @return
     */
    public static Integer getCreateTaskTimeHintGrayPercent() {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.config.MccConfig.getCreateTaskTimeHintGrayPercent()");
        return ConfigUtilAdapter.getInt("create_task_time_hint_gray_percent", 0);
    }

    /**
     * 获取解换绑的最大次数
     *
     * @return
     */
    public static Integer getMaxUnbindRebind0perations() {
        return ConfigUtilAdapter.getInt("max_unbind_rebind_operations", 3);
    }

    /**
     * 批量解换绑及换绑按钮降级开关
     * @return
     */
    public static Boolean getIsNewFLowSwitch() {
        return ConfigUtilAdapter.getBoolean("is_new_switch_flow_switch", true);
    }

    /**
     * 前端提示相关信息时需要的解换绑的最大次数
     *
     * @return
     */
    public static Integer getMaxUnbindRebind0perationsSuggest() {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_2", "com.sankuai.meituan.waimai.customer.config.MccConfig.getMaxUnbindRebind0perationsSuggest()");
        return ConfigUtilAdapter.getInt("max_unbind_rebind_operations_suggest", 2);
    }

    /**
     * 清理短信签约任务状态或客户门店关系状态开关
     *
     * @return
     */
    public static Boolean getOpenAlignSignStatus() {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_1", "com.sankuai.meituan.waimai.customer.config.MccConfig.getOpenAlignSignStatus()");
        return ConfigUtilAdapter.getBoolean("open_align_sign_status", false);
    }

    /**
     * 是否迁移结算接口开关
     *
     * @return
     */
    public static Boolean getSettleQuerySwitch() {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.config.MccConfig.getSettleQuerySwitch()");
        return ConfigUtilAdapter.getBoolean("open_settle_query_switch", false);
    }

    /**
     * 获取测试环境接收登月验卡失败短信手机号
     *
     * @return
     */
    public static String getTestSmsUser() {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.config.MccConfig.getTestSmsUser()");
        return ConfigUtilAdapter.getString("test.sms.user");
    }

    /**
     * WmCutomerAggre、WmCustomerKpAggre、WmCustomerOplogAggre、WmCustomerEsAggre、WmCustomerPoiAggre是否启用单利模式
     *
     * @return true启用单利模式；false：非单利模式
     */
    public static boolean isCustomerAggreSingletone() {
        return ConfigUtilAdapter.getBoolean("DDDGrayUtil_is_customerAggre_singleton", false);
    }

    /**
     * 客户门店信息中，资质公用证明最大的存储数量
     *
     * @return
     */
    public static int getCustomerCommonQuaUrlMaxNum() {
        return ConfigUtilAdapter.getInt("customer_common_qua_max_num", 400);
    }

    /**
     * 客户门店信息中，其他附件最大的存储数量
     *
     * @return
     */
    public static int getCustomerOtherQuaUrlMaxNum() {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_2", "com.sankuai.meituan.waimai.customer.config.MccConfig.getCustomerOtherQuaUrlMaxNum()");
        return ConfigUtilAdapter.getInt("customer_other_qua_max_num", 400);
    }

    /**
     * 是否展示美团云上的客户信息总开关
     *
     * @return
     */
    public static boolean isDispalyMtCustomer() {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_1", "com.sankuai.meituan.waimai.customer.config.MccConfig.isDispalyMtCustomer()");
        return ConfigUtilAdapter.getBoolean("accessMtCustomer_displayMtCustomer", false);
    }

    /**
     * 灰度展示从美团云获取的客户信息用到，开始展示的customerId
     * isDispalyMtCustomer 返回false时起作用
     *
     * @return
     */
    public static int getBeginIdOfDisplayMtCustomer() {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.config.MccConfig.getBeginIdOfDisplayMtCustomer()");
        return ConfigUtilAdapter.getInt("accessMtCustomer_displayMtCustomer_beginId", 0);
    }

    /**
     * 灰度展示从美团云获取的客户信息用到，结束展示的customerId
     * isDispalyMtCustomer 返回false时起作用
     *
     * @return
     */
    public static int getEndIdOfDisplayMtCustomer() {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.config.MccConfig.getEndIdOfDisplayMtCustomer()");
        return ConfigUtilAdapter.getInt("accessMtCustomer_displayMCustomer_EndId", 0);
    }

    /**
     * 界面上是否展示美团客户Id，而不是客户主键（原客户ID）
     *
     * @return
     */
    public static boolean isDisplayMtCustomerId() {
        return ConfigUtilAdapter.getBoolean("accessMtCustomer_is_mtCustomerId_display", false);
    }

    /**
     * 获得父客户属性校验的jsonSchema
     *
     * @return
     */
    public static String getCustomerExtProJsonSchema() {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_1", "com.sankuai.meituan.waimai.customer.config.MccConfig.getCustomerExtProJsonSchema()");
        return ConfigUtilAdapter.getString("customer_customerExtPro_jsonSchema", "{}");
    }

    /**
     * 获得父客户属性校验的jsonSchema
     *
     * @return
     */
    public static String getVisitKpProJsonSchema() {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_1", "com.sankuai.meituan.waimai.customer.config.MccConfig.getVisitKpProJsonSchema()");
        return ConfigUtilAdapter.getString("customer_VisitKpPro_jsonSchema", "{}");
    }

    /**
     * 清洗isLeaf字段到Es是否完毕
     *
     * @return
     */
    public static boolean isCleanIsLeafProCompleted() {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.config.MccConfig.isCleanIsLeafProCompleted()");
        return ConfigUtilAdapter.getBoolean("customer_clean_isLeafPro_toEs_completed", false);
    }

    public static int getBatchUpdateDueDateContractNum() {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.config.MccConfig.getBatchUpdateDueDateContractNum()");
        return ConfigUtilAdapter.getInt("batch_update_duedate_contract_num", 50);
    }

    public static String getShanGouCustomerRealType() {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.config.MccConfig.getShanGouCustomerRealType()");
        return ConfigUtilAdapter.getString("shangou_customer_real_type", "1,7,8,9,10,11,12,13,14");
    }

    /**
     * 是否启用客户状态机，默认为启用
     *
     * @return
     */
    public static boolean isCustomerStateMachineEnable() {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.config.MccConfig.isCustomerStateMachineEnable()");
        return ConfigUtilAdapter.getBoolean("is_customer_state_machine_enalbe", true);
    }

    /**
     * 商家端查询协议qps
     *
     * @return
     */
    public static int getSupplierQueryQps() {
        return ConfigUtilAdapter.getInt("supplier_getUnsignProtocolWmPoiIds_qps", 200);
    }

    /**
     * 限流开关
     *
     * @return
     */
    public static boolean isOpenRateLimit() {
        return ConfigUtilAdapter.getBoolean("is_open_supplier_getUnsignProtocolWmPoiIds_limiter", false);
    }

    /**
     * 缓存开关
     *
     * @return
     */
    public static boolean isCacheOpen() {
        return ConfigUtilAdapter.getBoolean("is_open_getUnsignProtocolWmPoiIds_cache", false);
    }

    /**
     * 获取 token 平台服务方公钥
     *
     * @param defaultPublicKey
     * @return
     */
    public static String getTokenProviderPublicKey(String defaultPublicKey) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.config.MccConfig.getTokenProviderPublicKey(java.lang.String)");
        return ConfigUtilAdapter.getString("token_access_provider_public_key", defaultPublicKey);
    }

    /**
     * 客户解绑门店消息 是否发送 wmPoiIdAndSettleId 字段
     * 为保证灰度期间消息消费无误。上线后可关闭或删掉。
     *
     * @return
     */
    public static boolean isSendCustomerUnbindPoiMsgField() {
        return ConfigUtilAdapter.getBoolean("is_send_customerUnbindPoi_msg_field", true);
    }

    /**
     * 合同待生效到生效异常发送大象misid
     *
     * @return
     */
    public static String getContractEffectNotifyMisidList() {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_1", "com.sankuai.meituan.waimai.customer.config.MccConfig.getContractEffectNotifyMisidList()");
        return ConfigUtilAdapter.getString("contract_effect_notify_misIds", "limingxuan");
    }

    /**
     * 支持企客合同新签场景撤销开关
     * @return
     */
    public static boolean supportBusinessContractRetractSwitch(){
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_1", "com.sankuai.meituan.waimai.customer.config.MccConfig.supportBusinessContractRetractSwitch()");
        return ConfigUtilAdapter.getBoolean("support_business_contract_retract_switch", true);
    }

    /**
     *  获取合同告警的MIS ID列表
     * @return
     */
    public static List<String> getContractAlarmMisIdList() {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.config.MccConfig.getContractAlarmMisIdList()");
        String misIdStrs = ConfigUtilAdapter.getString("contract_alarm_misIds", "limingxuan");
        List<String> misIds = Splitter.on(",").trimResults().splitToList(misIdStrs);
        return misIds;
    }

    /**
     * 签约合同列表
     *
     * @return
     */
    public static String getContractSignType() {
        return ConfigUtilAdapter.getString("contract_sign_type", "[{\"type\":1,\"name\":\"美团与客户合同\",\"key\":\"1\"," +
            "\"sort\":1},{\"type\":2,\"name\":\"合作商与客户合同\",\"key\":\"2\",\"sort\":2},{\"type\":100001," +
            "\"name\":\"配送协议\",\"key\":\"delivery_preferential_application\",\"sort\":3},{\"type\":100002," +
            "\"name\":\"优惠政策\",\"key\":\"delivery_preferential_policy\",\"sort\":4},{\"type\":100003," +
            "\"name\":\"履约服务费\",\"key\":\"delivery_performance_service\",\"sort\":5},{\"type\":100004," +
            "\"name\":\"全城送协议\",\"key\":\"delivery_whole_city\",\"sort\":6},{\"type\":100005,\"name\":\"聚合配送协议\"," +
            "\"key\":\"delivery_aggregation\",\"sort\":7}," +
            "{\"type\":4,\"name\":\"企客配送服务合同\",\"key\":\"4\",\"sort\":8}]" +
            "{\"type\":200001,\"name\":\"美团拼好饭服务费收费协议\",\"key\":\"200001\",\"sort\":9}]");
    }


    /**
     * 批量查询客户的门店数量
     *
     * @return
     */
    public static int getWmPoiIdNum_For_BatchQueryCustomerBoMap() {
        return ConfigUtilAdapter.getInt("WmPoiIdNum_For_BatchQueryCustomerBoMap", 100);
    }

    /**
     * 批量查询客户合同的门店数量
     *
     * @return
     */
    public static int getWmPoiIdNum_For_BatchQueryCustomerContractBoMap() {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.config.MccConfig.getWmPoiIdNum_For_BatchQueryCustomerContractBoMap()");
        return ConfigUtilAdapter.getInt("WmPoiIdNum_For_BatchQueryCustomerContractBoMap", 100);
    }

    /**
     * 是否开启新主子门店模型校验
     *
     * @return
     */
    public static boolean openNewModeDandianBindCheck() {
        return ConfigUtilAdapter.getBoolean("open_new_mode_dandian_bind_check", false);
    }

    /**
     * 食堂门店审核，任务系统的type
     *
     * @return
     */
    public static int getCanteenPoiAuditTicketType() {
        return ConfigUtilAdapter.getInt("canteen_poi_audit_ticket_type", 19);
    }

    /**
     * 食堂审核，任务系统的type
     *
     * @return
     */
    public static int getCanteenAuditTicketType() {
        return ConfigUtilAdapter.getInt("canteen_audit_ticket_type", 104);
    }

    /**
     * 食堂门店审核，任务系统的source
     *
     * @return
     */
    public static int getCanteenPoiAuditSource() {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.config.MccConfig.getCanteenPoiAuditSource()");
        return ConfigUtilAdapter.getInt("canteen_poi_audit_ticket_source", 26);
    }

    /**
     * 电子合同的模板
     *
     * @return
     */
    public static String getEcontractType() {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_1", "com.sankuai.meituan.waimai.customer.config.MccConfig.getEcontractType()");
        return ConfigUtilAdapter.getString("canteen_poi_audit_econtract_type", "school");
    }

    public static boolean isHQSwitch() {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_2", "com.sankuai.meituan.waimai.customer.config.MccConfig.isHQSwitch()");
        return ConfigUtilAdapter.getBoolean("cateen_poi_audit_ishq_switch", false);
    }

    public static boolean isMCCHQ() {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.config.MccConfig.isMCCHQ()");
        return ConfigUtilAdapter.getBoolean("cateen_poi_audit_ishq", false);
    }

    public static String getCanteenTagLabel() {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.config.MccConfig.getCanteenTagLabel()");
        return ConfigUtilAdapter.getString("canteen_poi_canteen_labels", "[\n" + "    {\"key\":\"A\",\"labelId\":87,\"name\":\"校园食堂商家\",\"labelType\":1,\"labelTypeName\":\"A类\"},\n" + "    {\"key\":\"SKR_DIRECT\",\"labelId\":185,\"name\":\"重点直营\",\"labelType\":2,\"labelTypeName\":\"B类\"},\n" + "    {\"key\":\"KR_DIRECT\",\"labelId\":186,\"name\":\"普通直营\",\"labelType\":2,\"labelTypeName\":\"B类\"},\n" + "    {\"key\":\"CANTEEN_DIRECT\",\"labelId\":187,\"name\":\"私立直营\",\"labelType\":2,\"labelTypeName\":\"B类\"},\n" + "    {\"key\":\"SKR_CONTRACT\",\"labelId\":188,\"name\":\"SKR承包\",\"labelType\":2,\"labelTypeName\":\"B类\"},\n" + "    {\"key\":\"KR_CONTRACT\",\"labelId\":189,\"name\":\"KR承包\",\"labelType\":2,\"labelTypeName\":\"B类\"},\n" + "    {\"key\":\"CANTEEN_CONTRACT\",\"labelId\":190,\"name\":\"食堂承包\",\"labelType\":2,\"labelTypeName\":\"B类\"}\n" + "]");
    }

    /*
     * 合同待生效到生效异常发送大象misid
     * @return
     */
    public static String getContractIds() {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.config.MccConfig.getContractIds()");
        // return ConfigUtilAdapter.getString("get_contractor_ids", "\n");
        return ConfigUtilAdapter.getString("get_contractor_ids", "13189088\n" + "13271236\n" + "13271268\n" + "13271327\n" + "13365595");
    }

    /**
     * 客户id是否在灰度中
     *
     * @param customerId
     * @return
     */
    public static boolean getXingHuoGrayCustomerIdsBoolean(Integer customerId) {
        if (ConfigUtilAdapter.getBoolean("xing_huo_open", false)) {
            return true;
        }
        String xingHuoGrayCustomerIds = ConfigUtilAdapter.getString("xing_huo_gray_customer_ids", "");
        if (StringUtils.isEmpty(xingHuoGrayCustomerIds)) {
            return false;
        }
        Set<String> xingHuoGrayCustomerIdsSet = Sets.newHashSet(Splitter.on(",").trimResults().splitToList(xingHuoGrayCustomerIds));
        return xingHuoGrayCustomerIdsSet.contains(String.valueOf(customerId));
    }

    /**
     * c1合同区分医药客户灰度过程
     *
     * @param customerId
     * @return
     */
    public static boolean getC1MedicineGaryBoolean(Integer customerId) {
        if (ConfigUtilAdapter.getBoolean("c1_medicine_open", false)) {
            return true;
        }
        String c1MedicineGaryCustoemrIds = ConfigUtilAdapter.getString("c1_medicine_gray_customer_ids", "");
        if (StringUtils.isEmpty(c1MedicineGaryCustoemrIds)) {
            return false;
        }
        Set<String> c1MedicineGaryCustomerIdsSet = Sets.newHashSet(Splitter.on(",").trimResults().splitToList(c1MedicineGaryCustoemrIds));
        return c1MedicineGaryCustomerIdsSet.contains(String.valueOf(customerId));
    }

    /**
     * 清洗数据权限人misId
     *
     * @param misId
     * @return
     */
    public static boolean getWashAuthorityMisIdsBoolean(String misId) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_2", "com.sankuai.meituan.waimai.customer.config.MccConfig.getWashAuthorityMisIdsBoolean(java.lang.String)");
        String washAuthorityMisids = ConfigUtilAdapter.getString("wash_authority_misids", "xuezhangang");
        if (StringUtils.isEmpty(washAuthorityMisids)) {
            return false;
        }
        Set<String> washAuthorityMisidsSet = Sets.newHashSet(Splitter.on(",").trimResults().splitToList(washAuthorityMisids));
        return washAuthorityMisidsSet.contains(misId);
    }

    /**
     * 未协议签署门店是否在配置中
     *
     * @param wmPoiId
     * @return
     */
    public static boolean getSubjectChangeWmPoiIdBoolean(Long wmPoiId) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_1", "com.sankuai.meituan.waimai.customer.config.MccConfig.getSubjectChangeWmPoiIdBoolean(java.lang.Long)");
        String subjectChangeIds = ConfigUtilAdapter.getString("agreement_subject_change_wmPoiIds", "");
        if (StringUtils.isEmpty(subjectChangeIds)) {
            return false;
        }
        Set<String> subjectChangeIdsSet = Sets.newHashSet(Splitter.on(",").trimResults().splitToList(subjectChangeIds));
        return subjectChangeIdsSet.contains(String.valueOf(wmPoiId));
    }

    /**
     * 签约主体白名单标签系统对应标签分类ID_A
     */
    public static int getLabelClassificationIdA() {
        return ConfigUtilAdapter.getInt("contract_label_classficate_num_a", 0);
    }

    /**
     * 签约主体白名单标签系统对应标签分类ID_B
     */
    public static int getLabelClassificationIdB() {
        return ConfigUtilAdapter.getInt("contract_label_classficate_num_b", 0);
    }

    /**
     * 机器灰度过程中写快照开关
     *
     * @return
     */
    public static boolean getRecordSubjectSwitchGray() {
        return ConfigUtilAdapter.getBoolean("record_poi_subject_switch", false);
    }

    /**
     * 非医药可以切换客户类型集合
     *
     * @return
     */
    public static String getCanSwitchCustomerTypeList() {
        return ConfigUtilAdapter.getString("customer_canswitch_customertypeList", "0,1,2,3,4,5,6,7,8,10,11,12,13,14");
    }

    /**
     * 医药可以切换客户类型集合
     *
     * @return
     */
    public static String getCanSwitchCustomerTypeYaoPinList() {
        return ConfigUtilAdapter.getString("customer_canswitch_med_customertypeList", "9,16");
    }

    /**
     * 互斥不可切换集合
     *
     * @return
     */
    public static String getCanNotSwitchCustomerTypeList() {
        return ConfigUtilAdapter.getString("customer_cannot_switch_customertypeList", "15,17,18");
    }

    /**
     * 获取用户能够查看的客户类型 "业务类型"区分
     *
     * @return
     */
    public static String getCustomerRealTypeForBizType() {
        String defaultVal = "[{\"type\":0,\"value\":[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17],\"defaultVal\":0}," + "{\"type\":1,\"value\":[1,2,3,16],\"defaultVal\":0}," + "{\"type\":4,\"value\":[4],\"defaultVal\":0}," + "{\"type\":5,\"value\":[1,2,3,5,6],\"defaultVal\":0}," + "{\"type\":6,\"value\":[1,2,3,16],\"defaultVal\":0}," + "{\"type\":10,\"value\":[17],\"defaultVal\":0}," + "{\"type\":12,\"value\":[0,1,2],\"defaultVal\":0}," + "{\"type\":13,\"value\":[0,1,2,8],\"defaultVal\":0}," + "{\"type\":14,\"value\":[0,1,2,7,8,9,10,11,12,13,14,16],\"defaultVal\":0}," + "{\"type\":19,\"value\":[1,16,2,3],\"defaultVal\":0}," + "{\"type\":22,\"value\":[0,1,7,8,9,10,11,12,13,14],\"defaultVal\":0}" + "]";
        return ConfigUtilAdapter.getString("CustomerRealType_For_BizType", defaultVal);
    }

    /**
     * 获取用户能够查看的客户类型 "团队类型"区分
     *
     * @return
     */
    public static String getCustomerRealTypeForTeamType() {
        String defaultVal = "[{\"type\":1,\"value\":[0,1,2,3,15,16],\"defaultVal\":0}]";
        return ConfigUtilAdapter.getString("CustomerRealType_For_TeamType", defaultVal);
    }

    /**
     * 校验切换客户类型开关
     *
     * @return
     */
    public static boolean batchPoiDataWrapperDeliveryUseNewData() {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.config.MccConfig.batchPoiDataWrapperDeliveryUseNewData()");
        return ConfigUtilAdapter.getBoolean("WmEcontractBatchPoiDataWrapperService_delivery_use_new_data_open", false);
    }

    public static boolean customerPoiGenPdfUseNewData() {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_1", "com.sankuai.meituan.waimai.customer.config.MccConfig.customerPoiGenPdfUseNewData()");
        return ConfigUtilAdapter.getBoolean("customerPoiGenPdfUseNewData_use_new_data_open", false);
    }

    public static boolean getAddPackageFeeSwitch() {
        return ConfigUtilAdapter.getBoolean("agent_add_package_fee_switch", false);
    }

    public static boolean getAddPackageFeeGrayAll() {
        return ConfigUtilAdapter.getBoolean("agent_add_package_fee_gray_all", false);
    }

    public static boolean getCustomerSubjectMasterSwitch() {
        return ConfigUtilAdapter.getBoolean("customer_poi_subject_master_switch", false);
    }

    /**
     * 《配送服务合同》和《配送站点合同》接入新的合同配置平台 开关
     *
     * @return
     */
    public static boolean getDeliveryAggregationEcontractTemplateConfigSwitch() {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.config.MccConfig.getDeliveryAggregationEcontractTemplateConfigSwitch()");
        return ConfigUtilAdapter.getBoolean("delivery_aggregation_econtract_template_config_switch", false);
    }

    /**
     * 二级城市是否在灰度范围内
     *
     * @param cityLocationId
     * @return
     */
    public static boolean subjectChangeGrey(int cityLocationId) {
        String greyCityLocationId = ConfigUtilAdapter.getString("subject_change_grey_city_location_id");
        if (StringUtil.isNotEmpty(greyCityLocationId)) {
            return SPLITTER.splitToList(greyCityLocationId).contains(cityLocationId + "");
        }
        return false;
    }

    /**
     * 关闭商家端配送协议签约提醒
     *
     * @return
     */
    public static boolean closeLogisticsSubject() {
        return ConfigUtilAdapter.getBoolean("logistics_subject_close", true);
    }

    public static boolean subjectChangeJudgeTotalOpen() {
        return ConfigUtilAdapter.getBoolean("subject_change_judge_total_open", false);
    }

    /**
     * 获取团餐补充协议大象推送群组orgId
     *
     * @return
     */
    public static String getGroupMealContractNotifyOrgId() {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.config.MccConfig.getGroupMealContractNotifyOrgId()");
        return ConfigUtilAdapter.getString("group_meal_contract_notify_org_id", "160045");
    }

    /**
     * 获取团餐补充协议大象推送misIds
     *
     * @return
     */
    public static String getGroupMealContractNotifyMisIds() {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.config.MccConfig.getGroupMealContractNotifyMisIds()");
        return ConfigUtilAdapter.getString("group_meal_contract_notify_mis_id", "");
    }

    /**
     * 客户校验资质分页大小
     *
     * @return
     */
    public static int checkQuaPageSize() {
        return ConfigUtilAdapter.getInt("customer_check_qua_page_size", 200);
    }

    /**
     * 结算信息页-敏感字段加密开关
     */
    public static boolean encryptSensitiveWords() {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.config.MccConfig.encryptSensitiveWords()");
        return ConfigUtilAdapter.getBoolean("encrypt_sensitive", true);
    }

    /**
     * 电子合同签章CA认证添加签约人信息百分比
     */
    public static int econtractSyncCASignerInfoPercent() {
        return ConfigUtilAdapter.getInt("econtract_sync_ca_signer_info_percent", 0);
    }

    /**
     * 电子合同签约页面添加签约人信息百分比
     */
    public static int econtractSyncH5CertifyInfoPercent() {
        return ConfigUtilAdapter.getInt("econtract_sync_H5_certify_info_percent", 0);
    }

    /**
     * 批量平台操作-获取各种类型任务线程池大小
     *
     * @return
     */
    public static int taskQueryThreadPoolSize() {
        return ConfigUtilAdapter.getInt("task_query_thread_pool_size", 3);
    }

    /**
     * 批量平台操作-获取各种类型任务线程池大小
     *
     * @return
     */
    public static int taskExecutorThreadPoolSize() {
        return ConfigUtilAdapter.getInt("task_executor_thread_pool_size", 10);
    }

    /**
     * 批量平台操作-分页大小
     *
     * @return
     */
    public static int batchOpQueryPageSize() {
        return ConfigUtilAdapter.getInt("batch_op_query_page_size", 100);
    }

    /**
     * 批量平台操作-实际操作总开关
     *
     * @return
     */
    public static boolean batchOpSwitch() {
        return ConfigUtilAdapter.getBoolean("batch_op_switch", true);
    }

    public static int rollPackSize() {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_2", "com.sankuai.meituan.waimai.customer.config.MccConfig.rollPackSize()");
        return ConfigUtilAdapter.getInt("roll_pack_size", 300);
    }

    /**
     * 存储加密-总开关(默认关)
     */
    public static boolean isEncryptionSwitch() {
        return ConfigUtilAdapter.getBoolean("is_encryption_switch", false);
    }

    /**
     * 子文本大小-默认1M
     */
    public static int subContextSize() {
        return ConfigUtilAdapter.getInt("sub_context_size", 1024 * 1024);
    }

    /**
     * 存储加密-线程池大小
     *
     * @return
     */
    public static int encryptionThreadPoolSize() {
        return ConfigUtilAdapter.getInt("encryption_thread_pool_size", 10);
    }

    /**
     * 存储加密-是否停写明文
     *
     * @return
     */
    public static boolean isFilterOriginValue() {
        return ConfigUtilAdapter.getBoolean("is_filter_origin_value", false);
    }

    /**
     * 存储加密-是否读密文
     *
     * @return
     */
    public static boolean isFillingByEncrypedValue() {
        return ConfigUtilAdapter.getBoolean("is_filling_by_encrypted_value", false);
    }

    /**
     * 存储加密-读密文灰度百分比
     *
     * @return
     */
    public static int fillingByEncrypedValuePercent() {
        return ConfigUtilAdapter.getInt("filling_by_encrypted_value_percent", 0);
    }

    /**
     * 存储加密-更新相关流程入参改对象
     *
     * @return
     */
    public static boolean isEncryptionUpdateByPOJO() {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_1", "com.sankuai.meituan.waimai.customer.config.MccConfig.isEncryptionUpdateByPOJO()");
        return ConfigUtilAdapter.getBoolean("is_encrytion_update_by_POJO", true);
    }

    /**
     * 存储加密-批量插入转串行插入
     *
     * @return
     */
    public static boolean isEncryptionInsertBySingle() {
        return ConfigUtilAdapter.getBoolean("is_encrytion_insert_by_single", true);
    }

    /**
     * 存储加密-指定mysql表
     *
     * @return
     */
    public static String encryptionTableNameList() {
        return ConfigUtilAdapter.getString("encrytion_table_name_list", "wm_templet_contract_extension,wm_templet_contract_sign,wm_templet_contract_sign_audited,wm_econtract_sign_base,wm_econtract_sign_batch,wm_econtract_sign_task,wm_econtract_sign_batch_context,wm_econtract_sign_pack");
    }

    /**
     * 存储加密-未获取到锁等待时间
     *
     * @return
     */
    public static int encryptionThreadSleepMillis() {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.config.MccConfig.encryptionThreadSleepMillis()");
        return ConfigUtilAdapter.getInt("encrytion_thread_sleep_mills", 50);
    }

    /**
     * 获取客户upm权限角色编码
     *
     * @param role
     * @return
     */
    public static Integer getCustomerAuthRoleCode(String role) {
        List<JSONObject> customerUpmConfigs = JSONObject.parseArray(ConfigUtilAdapter.getString("customer_upm_config", "[{\"name\":\"customerSignModeRole\",\"code\": 203288}," + "{\"name\":\"customerCertMainBodyRole\",\"code\": 226983}," + "{\"name\":\"customerCertEditRole\",\"code\": 207126}," + "{\"name\":\"addSuperiorCustomerRole\",\"code\": 229970}," + "{\"name\":\"customerTypeSwitchOfDrugRole\",\"code\": 235318}," + "{\"name\":\"customerOplogButtonRole\",\"code\": 234466}," + "{\"name\":\"paperSignModeUnBindPoiManager\",\"code\": 203841}," + "{\"name\":\"fromCustomerConfrimUnbindAutoRole\",\"code\":236835}," + "{\"name\":\"agentForceAuthRole\",\"code\":297046}," + "{\"name\":\"notUploadAgentAuthForInsertRole\",\"code\":297047}," + "{\"name\":\"notUploadAgentAuthForUpdateRole\",\"code\":297048}," + "{\"name\":\"xianfu_waimai_145d3ac4d8e28533a62787eba281244c\",\"code\":329389}," + "{\"name\":\"xianfu_waimai_cf3f341de17079cf37c5c1e7c5384b2e\",\"code\":333416}," + "{\"name\":\"supportMultipleBizOrgCreateRole\",\"code\":334483}" + "]"), JSONObject.class);
        if (CollectionUtils.isNotEmpty(customerUpmConfigs)) {
            for (JSONObject jsonObject : customerUpmConfigs) {
                if (jsonObject.getString("name").equals(role)) {
                    return jsonObject.getInteger("code");
                }
            }
        }
        return -1;
    }

    /**
     * 签约取消回调配送接口是否分批
     */
    public static boolean isCallBackLogisticsPartition() {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_1", "com.sankuai.meituan.waimai.customer.config.MccConfig.isCallBackLogisticsPartition()");
        return ConfigUtilAdapter.getBoolean("is_call_back_logistics_partition", true);
    }

    /**
     * 签约取消回调配送接口每批次门店数量
     */
    public static int callBackLogisticsPerWmPoiIdNum() {
        return ConfigUtilAdapter.getInt("call_back_logistics_per_wm_poi_id_num", 10);
    }

    /**
     * 历史数据加密-任务执行截止小时数(默认执行至早上8点)
     */
    public static int dataEncryptionLimitHour() {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_1", "com.sankuai.meituan.waimai.customer.config.MccConfig.dataEncryptionLimitHour()");
        return ConfigUtilAdapter.getInt("data_encryption_limit_hour", 8);
    }

    /**
     * 上线检查点-新逻辑
     */
    public static boolean isPoiSetupNew() {
        return ConfigUtilAdapter.getBoolean("is_poi_setup_new", true);
    }

    public static String getTokenServicePublicKey() {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.config.MccConfig.getTokenServicePublicKey()");
        return ConfigUtilAdapter.getString("token_service_public_key");
    }

    /**
     * 明文和密文不相等时是否重写密文
     *
     * @return
     */
    public static boolean isOverWriteEncryptionRecord() {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.config.MccConfig.isOverWriteEncryptionRecord()");
        return ConfigUtilAdapter.getBoolean("is_over_write_encryption_record", false);
    }

    /**
     * 是否需要打印分页查询结果
     * */
    public static boolean isPrintCanteenQueryResult() {
        return ConfigUtilAdapter.getBoolean("is_print_canteen_query_result", false);
    }

    /**
     * 是否删除历史明文
     */
    public static boolean isRemoveOriginalRecord() {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.config.MccConfig.isRemoveOriginalRecord()");
        return ConfigUtilAdapter.getBoolean("is_remove_original_record", false);
    }

    /**
     * 批量签约信息冷热数据分离总开关
     *
     * @return
     */
    public static boolean isSeperateHotAndColdDataSwitch() {
        return ConfigUtilAdapter.getBoolean("is_seperate_hot_cold_data_switch", false);
    }

    /**
     * 批量签约信息冷热数据分离-灰度客户百分比
     *
     * @return
     */
    public static int seperateHotAndColdDataCustomerPercent() {
        return ConfigUtilAdapter.getInt("seperate_hot_cold_data_customer_percent", 0);
    }

    /**
     * 分批写入主体快照每批数量
     *
     * @return
     */
    public static int getBatchSubjectPartNum() {
        return ConfigUtilAdapter.getInt("batch_save_subject_part_num", 200);
    }

    /**
     * APP/PC待签约页-顶部提示线程池大小
     *
     * @return
     */
    public static int toSignTipsThreadPoolSize() {
        return ConfigUtilAdapter.getInt("to_sign_tips__thread_pool_size", 10);
    }

    /**
     * APP/PC待签约页-顶部提示文案
     */
    public static String toSignTips() {
        return ConfigUtilAdapter.getString("to_sign_tips", "您的{X}还未生成，请联系业务经理发起签约");
    }

    /**
     * APP/PC待签约页-灰度门店百分比
     */
    public static Integer toSignGrayWmPoiIdPercent() {
        return ConfigUtilAdapter.getInt("to_sign_gray_wm_poi_id_percent", 0);
    }

    /**
     * APP/PC待签约页-PC跳转页
     */
    public static String toSignPCJumpLink() {
        return ConfigUtilAdapter.getString("to_sign_pc_jump_link", "https://waimaieapp.meituan.com/igate/customer/contract/pc/list.html?source=1&wmPoiId=");
    }

    /**
     * APP/PC待签约页-APP跳转页
     */
    public static String toSignAPPJumpLink() {
        return ConfigUtilAdapter.getString("to_sign_app_jump_link", "https://waimaieapp.meituan.com/igate/customer/contract/h5/list.html?source=1&wmPoiId=");
    }

    /**
     * 签约链接push等级
     *
     * @return
     */
    public static Integer getMessagePushLevel() {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_1", "com.sankuai.meituan.waimai.customer.config.MccConfig.getMessagePushLevel()");
        return ConfigUtilAdapter.getInt("contract_message_push_level", 5);
    }

    /**
     * 配送合同切换上海签章开关
     *
     * @return
     */
    public static boolean isDeliveryContractUseShStamp() {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.config.MccConfig.isDeliveryContractUseShStamp()");
        return ConfigUtilAdapter.getBoolean("is_delivery_service_use_sh_stamp", false);
    }

    /**
     * 打包模块-签约完成MQ通知上游task灰度万分比
     *
     * @return
     */
    public static int signSuccessPushByMQTaskGrayPercent() {
        return ConfigUtilAdapter.getInt("sign_success_push_by_mq_task_gray_percent", 0);
    }

    /**
     * 电子合同-打包模块链路优化-batch灰度万分比
     */
    public static int econtractChainOptBatchGrayPercent() {
        return ConfigUtilAdapter.getInt("econtract_chain_opt_by_batch_gray_percent", 0);
    }

    /**
     * 电子合同等待上游回调合同类型列表(哪个业务方接入就往这个MCC新增配置)
     */
    public static String econtractWaitForUpstreamTaskType() {
        return ConfigUtilAdapter.getString("econtract_wait_for_upstream_task_type", "batch_delivery,delivery");
    }

    /**
     * 电子合同等待上游回调百分比-task门店数量限制
     */
    public static int econtractWaitForUpstreamTaskPoiNum() {
        return ConfigUtilAdapter.getInt("econtract_wait_for_upstream_task_poi_num", 0);
    }

    /**
     * 电子合同反查打包模块签约状态DSL语句
     */
    public static String econtractCallBackStatusDsl() {
        return ConfigUtilAdapter.getString("econtract_call_back_status_dsl", "{\"tasks\":[{\"url\":\"<EMAIL>@8437\",\"alias\":\"d1\",\"taskType\":\"ThriftGeneric\",\"method\":\"queryAllTaskUpstreamStatus\",\"timeout\":10000,\"inputs\":{\"recordKey\":\"${recordKey}\"},\"inputsExtra\":{\"recordKey\":\"java.lang.String\"}}],\"name\":\"queryAllTaskUpstreamStatus\",\"description\":\"根据recordKey查询batch状态\",\"outputs\":{\"upstreamStatus\": \"$d1\"}}");
    }

    /**
     * 打包模块-是否反查上游状态
     */
    public static boolean isQueryUpstreamStatus() {
        return ConfigUtilAdapter.getBoolean("is_query_upstream_status", false);
    }

    /**
     * 打包模块-反查上游状态延迟时间
     *
     * @return
     */
    public static int queryUpstreamStatusDelayTime() {
        return ConfigUtilAdapter.getInt("query_upstream_status_delay_time", 10);
    }

    /**
     * 打包模块-反查上游状态最大延迟时间
     *
     * @return
     */
    public static int queryUpstreamStatusMaxDelayTime() {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_1", "com.sankuai.meituan.waimai.customer.config.MccConfig.queryUpstreamStatusMaxDelayTime()");
        return ConfigUtilAdapter.getInt("query_upstream_status_max_delay_time", 500);
    }

    /**
     * @return
     */
    public static boolean isRetryIfContextIsEmpty() {
        return ConfigUtilAdapter.getBoolean("is_retry_if_context_is_empty", true);
    }

    /**
     * @return
     */
    public static boolean isRetryIfTaskContextIsEmpty() {
        return ConfigUtilAdapter.getBoolean("is_retry_if_task_context_is_empty", true);
    }

    /**
     * 是否处理密文数据并发写入导致的脏数据
     */
    public static boolean isHandleConcurrentRecord() {
        return ConfigUtilAdapter.getBoolean("is_handle_concurrent_record", true);
    }

    public static String packSignGrayTemplateType() {
        return ConfigUtilAdapter.getString("pack_sign_gray_template_type", "1201,2202");
    }

    /**
     * 签约信息推送商家端，锁超时时间（秒）
     */
    public static int pushMsgTairLockExpireTime() {
        return ConfigUtilAdapter.getInt("push_msg_tair_lock_expire_time", 30);
    }

    /**
     * 配送拆分存储-客户ID灰度百分比
     */
    public static int areaSeperateSaveGrayCutomerIdPercent() {
        return ConfigUtilAdapter.getInt("area_seperate_save_gray_customer_id_percent", 0);
    }

    /**
     * 配送拆分存储-门店数量限制
     */
    public static int areaSeperateSaveGrayPoiNum() {
        return ConfigUtilAdapter.getInt("area_seperate_save_gray_poi_num", 500);
    }

    /**
     * task数据配送范围批量插入每批大小
     */
    public static int taskAreaParitionSize() {
        return ConfigUtilAdapter.getInt("task_area_partition_size", 20);
    }

    /**
     * 聚合配送PDF数据是否移除配送范围
     */
    public static boolean isRemoveAggregationAreaData() {
        return ConfigUtilAdapter.getBoolean("is_remove_aggregation_area_data", true);
    }

    /**
     * 跨境B2C药品客户乙方
     */
    public static String getB2CPartBName() {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.config.MccConfig.getB2CPartBName()");
        return ConfigUtilAdapter.getString("b2c_part_b_name", "KANGAROO DELIVERY LIMITED");
    }

    /**
     * 企餐门店标签id
     */
    public static long getGroupMealPoiTagId() {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.config.MccConfig.getGroupMealPoiTagId()");
        return ConfigUtilAdapter.getLong("group_meal_poi_tag_id", 216l);
    }

    /**
     * 新主子门店模式：主站门店标签id(默认线上环境值）
     */
    public static int getMainPoiTagId() {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_2", "com.sankuai.meituan.waimai.customer.config.MccConfig.getMainPoiTagId()");
        return ConfigUtilAdapter.getInt("main_poi_tag_id", 660);
    }

    /**
     * 商家端查看签约人信息，灰度开关
     *
     * @return
     */
    public static boolean getGrayPoiCitySwitch() {
        return ConfigUtilAdapter.getBoolean("customer_gray_poi_list_switch", false);
    }

    /**
     * 商家端查看签约人信息，灰度门店城市列表
     *
     * @return
     */
    public static List<Integer> getGrayPoiCityList() {
        return JSONObject.parseArray(ConfigUtilAdapter.getString("customer_gray_poi_list", "[]"), Integer.class);
    }

    /**
     * 发送Push消息时，消息模板
     *
     * @return
     */
    public static String getPushMessTemplate() {
        return ConfigUtilAdapter.getString("wm_customer_kp_update_push_template", "wm_customer_signer_kp_update_4_business");
    }

    public static Set<String> getMultiCustomerSignContractType() {
        return JSON.parseObject(ConfigUtilAdapter.getString("customer_multi_poi_contract_type", "[\"ad_annual_framework_contract\",\"ad_order\",\"added_service_discount\",\"bag_service\",\"batch_delivery\",\"batch_poi_generate_pdf\",\"brand_ad_contract\",\"business_customer_contract\",\"c1contract\",\"c2contract\",\"cancel_confirm\",\"delivery\",\"delivery_service_contract\",\"delivery_site_contract\",\"foodcity_poi_table\",\"foodcity_statement\",\"group_meal\",\"kp_confirm\",\"med_deposit\",\"operation_manager_kp_confirm\",\"phf_charge\",\"poi_promotion_service\",\"qua_real_letter\",\"settle\",\"shangou_rebate\",\"wm_poi_base_tag_sign\"]"), new TypeReference<Set<String>>() {}.getType());
    }

    /**
     * 页面异步刷新状态兜底超时:单位秒
     */
    public static int applySubmitStatusQueryTimeout() {
        return ConfigUtilAdapter.getInt("apply_base_query_status_timeout", 20 * 60);
    }

    /**
     * 提交数限制
     *
     * @return 返回
     */
    public static int getActualTaskPartitionNum() {
        int submitTaskLimit = ConfigUtilAdapter.getInt("actual_task_partition_num", 300);
        return submitTaskLimit;
    }

    /**
     * 灰度清洗 任务数据
     *
     * @return
     */
    public static List<Integer> getGrayCleanupCustomerList() {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.config.MccConfig.getGrayCleanupCustomerList()");
        return JSONObject.parseArray(ConfigUtilAdapter.getString("customer_task_cleanup_list", "[]"), Integer.class);
    }

    /**
     * 灰度清洗 任务数据 开始时间
     *
     * @return
     */
    public static String getGrayCleanupStartTime() {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_1", "com.sankuai.meituan.waimai.customer.config.MccConfig.getGrayCleanupStartTime()");
        return ConfigUtilAdapter.getString("customer_task_cleanup_start_time", "2022-12-04 00:00:00");
    }

    /**
     * 灰度清洗 任务数据 结束时间
     *
     * @return
     */
    public static String getGrayCleanupEndTime() {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.config.MccConfig.getGrayCleanupEndTime()");
        return ConfigUtilAdapter.getString("customer_task_cleanup_end_time", "2022-12-04 23:59:59");
    }

    /**
     * 灰度清洗 任务数据 结束时间
     *
     * @return
     */
    public static boolean cleanupC1StopTag() {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.config.MccConfig.cleanupC1StopTag()");
        return ConfigUtilAdapter.getBoolean("customer_C1_task_cleanup_stop_tag", false);
    }

    /**
     * 灰度清洗 C1任务数据 开始时间
     *
     * @return 返回
     */
    public static String getGrayC1CleanupStartTime() {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.config.MccConfig.getGrayC1CleanupStartTime()");
        return ConfigUtilAdapter.getString("customer_task_C1_cleanup_start_time", "2022-12-04 00:00:00");
    }

    /**
     * 灰度清洗 C1任务数据 结束时间
     *
     * @return 返回
     */
    public static String getGrayC1CleanupEndTime() {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.config.MccConfig.getGrayC1CleanupEndTime()");
        return ConfigUtilAdapter.getString("customer_task_C1_cleanup_end_time", "2022-12-04 23:59:59");
    }

    /**
     * 灰度清洗 任务数据 结束时间
     *
     * @return
     */
    public static Boolean cleanupStopTag() {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.config.MccConfig.cleanupStopTag()");
        return ConfigUtilAdapter.getBoolean("customer_task_cleanup_stop_tag", false);
    }

    /**
     * 灰度清洗 任务数据 结束时间
     */
    public static long cleanupSleepTime() {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_2", "com.sankuai.meituan.waimai.customer.config.MccConfig.cleanupSleepTime()");
        return ConfigUtilAdapter.getLong("customer_task_cleanup_sleep_time", 10);
    }

    /**
     * 处理的合同类别. 默认 c1
     */
    public static List<String> cleanupApplyTypeList() {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.config.MccConfig.cleanupApplyTypeList()");
        return JSONObject.parseArray(ConfigUtilAdapter.getString("customer_task_cleanup_apply_type_List", "[\"c1contract\"]"), String.class);
    }

    /**
     * 提交任务的时候，是否需要等待
     */
    public static boolean needToAwait() {
        return ConfigUtilAdapter.getBoolean("need_to_await", true);
    }

    /**
     * 页面异步刷新状态兜底超时:单位秒. 默认五秒
     */
    public static int applySubmitRequestKeyTimeout() {
        return ConfigUtilAdapter.getInt("apply_submit_request_key_timeout", 5);
    }

    /**
     * 已签约合同类型
     *
     * @return BO
     */
    public static List<ContractTypeBO> getSignedContractTypeList() {
        return JSONObject.parseArray(ConfigUtilAdapter.getString("multi_customer_contract_sign_type", "[{\"type\":1,\"name\":\"美团与客户合同\",\"key\":\"1\",\"sort\":1},{\"type\":2,\"name\":\"合作商与客户合同\",\"key\":\"2\",\"sort\":2},{\"type\":4,\"name\":\"企客配送服务合同\",\"key\":\"4\",\"sort\":8}]"), ContractTypeBO.class);
    }

    /**
     *
     */
    public static boolean repeatObjectSwitch() {
        return ConfigUtilAdapter.getBoolean("repeat_object_switch", true);
    }

    public static int repeatGrayProportion() {
        return ConfigUtilAdapter.getInt("repeat_gray_proportion", 0);
    }

    public static String repeatGrayCustomerList() {
        return ConfigUtilAdapter.getString("repeat_gray_customerlist", "");
    }

    /**
     * default loading time
     */
    public static long defaultManualTaskSubmitLoadingTime() {
        return ConfigUtilAdapter.getLong("default_manual_task_submit_loading_time", 1000L * 60L);
    }

    /**
     * 是否降级
     */
    public static boolean isSubmitStatusDegrade() {
        return ConfigUtilAdapter.getBoolean("submit_status_degrade", false);
    }

    /**
     * 批量处理数据操作-分页大小
     *
     * @return 返回
     */
    public static int batchQueryC1PageSize() {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.config.MccConfig.batchQueryC1PageSize()");
        return ConfigUtilAdapter.getInt("batch_query_C1_page_size", 50);
    }

    /**
     * 只处理7日内，待发起打包任务
     *
     * @return 返回
     */
    public static int manualTaskConvertStartTime() {
        return ConfigUtilAdapter.getInt("manual_task_convert_start_time", -7);
    }

    /**
     * 是否开启入口灰度
     *
     * @return 返回
     */
    public static boolean isQueryAccountSignTaskOpen() {
        return ConfigUtilAdapter.getBoolean("is_query_account_sign_task_open", true);
    }

    /**
     * 灰度放量百分比
     *
     * @return 返回
     */
    public static int isQueryAccountSignTaskOpenPercent() {
        return ConfigUtilAdapter.getInt("is_query_account_sign_task_open_percent", 0);
    }

    /**
     * 客户灰度，加白
     *
     * @return 返回
     */
    public static List<Integer> customerGrayWhiteList() {
        return JSONObject.parseArray(ConfigUtilAdapter.getString("customer-gray-white-list", "[********]"), Integer.class);
    }

    public static String unhandleEcontractType() {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.config.MccConfig.unhandleEcontractType()");
        return ConfigUtilAdapter.getString("callback_unhandle_econtractype_list", "type_sg_thridpart_deposit_qdb," + "type_sgwaima_purchase");
    }

    /**
     * pdf模版
     *
     * @return 返回
     */
    public static int getMedicOrderSplitPdfTemplateId() {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_2", "com.sankuai.meituan.waimai.customer.config.MccConfig.getMedicOrderSplitPdfTemplateId()");
        return ConfigUtilAdapter.getInt("medic_order_split_pdf_template_id", 277);
    }

    /**
     * pdf模版. 版本： 0 为最新版本
     *
     * @return 返回
     */
    public static int getMedicOrderSplitPdfTemplateVersion() {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_2", "com.sankuai.meituan.waimai.customer.config.MccConfig.getMedicOrderSplitPdfTemplateVersion()");
        return ConfigUtilAdapter.getInt("medic_order_split_pdf_template_version", 0);
    }

    public static int bizDataRetryTimes() {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.config.MccConfig.bizDataRetryTimes()");
        return ConfigUtilAdapter.getInt("biz_data_retry_time", 20);
    }

    /**
     * 禁止老结算写逻辑开关
     * true-开关开启
     * false-开关关闭
     */
    public static boolean oldSettleWriteForbiddenSwitch() {
        return ConfigUtilAdapter.getBoolean("old_settle_write_forbidden_switch", false);
    }

    /**
     * 发送Push消息时，消息模板
     *
     * @return 返回模版
     */
    public static String getContractSignPushMessageTemplate() {
        return ConfigUtilAdapter.getString("contract_sign_push_message_template", "wm_econtract_sign_common");
    }

    /**
     * 发送C1到期续签 Push消息时，消息模板1--non final
     *
     * @return 返回模版
     */
    public static List<String> getC1AutoRenewalNonFinalMessageTemplate() {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.config.MccConfig.getC1AutoRenewalNonFinalMessageTemplate()");
        String s= ConfigUtilAdapter.getString("c1_auto_renewal_non_final_message_template", "new_near_contract_update_30/7,old_near_contract_update_30/7");
        return Splitter.on(",").trimResults().splitToList(s);
    }

    /**
     * 发送C1到期续签 Push消息时，final renewal
     *
     * @return 返回模版
     */
    public static List<String> getC1AutoRenewalFinalMessageTemplate() {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.config.MccConfig.getC1AutoRenewalFinalMessageTemplate()");
        String s= ConfigUtilAdapter.getString("c1_auto_renewal_final_message_template", "new_near_contract_update_1,old_near_contract_update_1");
        return Splitter.on(",").trimResults().splitToList(s);
    }

    /**
     * 发送C1到期续签 Push消息时，消息模板1--Expired non final
     *
     * @return 返回模版
     */
    public static List<String> getC1AutoRenewalExpiredNonFinalMessageTemplate() {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_2", "com.sankuai.meituan.waimai.customer.config.MccConfig.getC1AutoRenewalExpiredNonFinalMessageTemplate()");
        String s= ConfigUtilAdapter.getString("c1_auto_renewal_expired_non_final_message_template", "new_due_contract_update_1/3,old_due_contract_update_1/3");
        return Splitter.on(",").trimResults().splitToList(s);
    }

    /**
     * 发送C1到期续签 Push消息时，expired final
     *
     * @return 返回模版
     */
    public static List<String> getC1AutoRenewalExpiredFinalMessageTemplate() {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.config.MccConfig.getC1AutoRenewalExpiredFinalMessageTemplate()");
        String s= ConfigUtilAdapter.getString("c1_auto_renewal_expired_final_message_template", "new_due_contract_update_7,old_due_contract_update_7");
        return Splitter.on(",").trimResults().splitToList(s);
    }

    /**
     * c1自动续签回调url
     * @return
     */
    public static String getC1AutoRenewalPushCallbackUrl(){
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.config.MccConfig.getC1AutoRenewalPushCallbackUrl()");
        return ConfigUtilAdapter.getString("c1_auto_renewal_push_callback_url", "/econtract/restful/api/v1/sign/confirm_sign_econtract_for_poi");
    }

    /**
     * econtract的域名地址
     * @return
     */
    public static String getEcontractHostKey(){
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.config.MccConfig.getEcontractHostKey()");
        return ConfigUtilAdapter.getString("econtract.host.key", "http://econtract.meituan.com");
    }

    /**
     * 发送Push消息,是否用新渠道
     *
     * @return 返回
     */
    public static boolean getContractSignPushNewChannel() {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.config.MccConfig.getContractSignPushNewChannel()");
        return ConfigUtilAdapter.getBoolean("contract_sign_push_new_channel", true);
    }

    /**
     * 支持消息推送的账号类别
     *
     * @return 账号类别list
     */
    public static List<Integer> getPushAccountSubTypeList() {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.config.MccConfig.getPushAccountSubTypeList()");
        return JSONObject.parseArray(ConfigUtilAdapter.getString("customer_account_sub_type_list", "[310,320]"), Integer.class);
    }

    public static Boolean agentTransferProp() {
        return (int) (Math.random() * 100) + 1 <= ConfigUtilAdapter.getInt("agent_transfer_prop", 0);
    }

    /**
     * 客户同步编号长度限制
     *
     * @return 长度
     */
    public static Integer getQueryBatchContractTaskLimit() {
        return ConfigUtilAdapter.getInt("query_batch_contract_task_limit", 5);
    }

    /**
     * 是否开启修复查询主从延时NPE
     * 在未取到从库数据的时候，backup方式，再读一下主库
     * 通过packId查询pack信息
     */
    public static boolean isQuerySignPackViaPackMaster() {
        return ConfigUtilAdapter.getBoolean("is_query_sign_pack_via_pack_master", true);
    }

    /**
     * 在未取到从库数据的时候，backup方式，再读一下主库
     * 通过packid获取signBatch list
     */
    public static boolean isQuerySignBatchListViaPackMaster() {
        return ConfigUtilAdapter.getBoolean("is_query_sign_batch_list_via_pack_master", true);
    }

    /**
     * 灰度表对应record_type
     *
     * @return record type list
     */
    public static List<RecordTypeBO> getGrayRecordTypeList() {
        return JSONObject.parseArray(ConfigUtilAdapter.getString("gray_record_type_list", "[{\"recordTypeId\":\"1\",\"tableName\":\"wm_templet_contract_extension\",\"grayTag\":\"false\"},{\"recordTypeId\":\"2\",\"tableName\":\"wm_templet_contract_sign\",\"grayTag\":\"false\"},{\"recordTypeId\":\"3\",\"tableName\":\"wm_templet_contract_sign_audited\",\"grayTag\":\"false\"},{\"recordTypeId\":\"4\",\"tableName\":\"wm_econtract_sign_base\",\"grayTag\":\"false\"},{\"recordTypeId\":\"5\",\"tableName\":\"wm_econtract_sign_batch\",\"grayTag\":\"false\"},{\"recordTypeId\":\"6\",\"tableName\":\"wm_econtract_sign_task\",\"grayTag\":\"false\"},{\"recordTypeId\":\"7\",\"tableName\":\"wm_econtract_sign_batch_context\",\"grayTag\":\"false\"},{\"recordTypeId\":\"8\",\"tableName\":\"wm_econtract_sign_pack\",\"grayTag\":\"false\"}]"), RecordTypeBO.class);
    }

    /**
     * 品牌分类：
     * https://km.sankuai.com/collabpage/1418492200#
     *
     * @return BizOrgCodeBrandTypeRel
     */
    public static List<BizOrgCodeBrandTypeRelBO> getBizOrgCodeBrandTypeRel() {
        return JSONObject.parseArray(ConfigUtilAdapter.getString("biz_org_code_brand_type_rel", "[{\"bizOrgCode\":14010,\"bizOrgCodeDesc\":\"外卖\",\"brandTypeList\":[3,7,14]},{\"bizOrgCode\":14060," + "\"bizOrgCodeDesc\":\"闪购\",\"brandTypeList\":[5,6,8,10,11,12,13,15]},{\"bizOrgCode\":14090,\"bizOrgCodeDesc\":\"医药\",\"brandTypeList\":[9]}]"), BizOrgCodeBrandTypeRelBO.class);
    }

    /**
     * 获取闪购配置
     */
    public static String getShanGouMsgContractAppKey() {
        return ConfigUtilAdapter.getString("shan_gou_msg_contract_app_key", "c4fffa20-162c-4225-b060-81755a4fb0b7");
    }

    /**
     * 账户类别消息模板id
     * 闪购-多店-PC
     *
     * @return 模板id
     */
    public static Integer getShanGouMsgAccountTemplateId() {
        return ConfigUtilAdapter.getInt("shan_gou_msg_account_template_id", 2656);
    }

    /**
     * 账户类别消息模板id
     * 医药-多店-PC
     *
     * @return 模板id
     */
    public static Integer getMedicMsgAccountTemplateId() {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.config.MccConfig.getMedicMsgAccountTemplateId()");
        return ConfigUtilAdapter.getInt("medic_msg_account_template_id", 2656);
    }

    /**
     * 门店类别消息模板id
     * 闪购-单店-PC
     *
     * @return 模板id
     */
    public static Integer getShanGouMsgPoiTemplateId() {
        return ConfigUtilAdapter.getInt("shan_gou_msg_poi_template_id", 2654);
    }

    /**
     * 门店类别消息模板id
     * 医药-单店-PC
     *
     * @return 模板id
     */
    public static Integer getMedicPoiTemplateId() {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.config.MccConfig.getMedicPoiTemplateId()");
        return ConfigUtilAdapter.getInt("medic_msg_poi_template_id", 2657);
    }

    /**
     * 门店APP类别消息模板id
     * 闪购-单店-APP
     *
     * @return 模板id
     */
    public static Integer getShanGouMsgPoiAppTemplateId() {
        return ConfigUtilAdapter.getInt("shan_gou_msg_poi_app_template_id", 2661);
    }

    /**
     * 门店APP类别消息模板id
     * 医药-单店-APP
     *
     * @return 模板id
     */
    public static Integer getMedicsgPoiAppTemplateId() {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.config.MccConfig.getMedicsgPoiAppTemplateId()");
        return ConfigUtilAdapter.getInt("medic_msg_poi_app_template_id", 2660);
    }

    /**
     * PC待签约页-PC跳转页
     */
    public static List<BizOrdCodeSignLinkRelBO> toSignBizOrgPCJumpLink() {
        return JSONObject.parseArray(ConfigUtilAdapter.getString("to_sign_biz_org_pc_jump_link", "[{\n" + "\t\t\"singleMultiPoiType\": 1,\n" + "\t\t\"bizOrgCode\": 14010,\n" + "\t\t\"bizOrgCodeDesc\": \"外卖\",\n" + "\t\t\"signHyperLink\": \"https://waimaieapp.meituan.com/igate/customer/contract/pc/list.html?source=1&wmPoiId=\"\n" + "\t}, {\n" + "\t\t\"singleMultiPoiType\": 1,\n" + "\t\t\"bizOrgCode\": 14060,\n" + "\t\t\"bizOrgCodeDesc\": \"闪购\",\n" + "\t\t\"signHyperLink\": \"https://waimaieapp.meituan.com/igate/customer/contract/pc/list.html?source=1&wmPoiId=\"\n" + "\t}, {\n" + "\t\t\"singleMultiPoiType\": 1,\n" + "\t\t\"bizOrgCode\": 14090,\n" + "\t\t\"bizOrgCodeDesc\": \"医药\",\n" + "\t\t\"signHyperLink\": \"https://waimaieapp.meituan.com/igate/customer/contract/pc/list.html?source=1&wmPoiId=\"\n" + "\t},\n" + "\t{\n" + "\t\t\"singleMultiPoiType\": 2,\n" + "\t\t\"bizOrgCode\": 14010,\n" + "\t\t\"bizOrgCodeDesc\": \"外卖\",\n" + "\t\t\"signHyperLink\": \"http://waimaieapp.meituan.com/?ignoreSetRouterProxy=true#/v2/shop/manage/signedProtocol\"\n" + "\t}, {\n" + "\t\t\"singleMultiPoiType\": 2,\n" + "\t\t\"bizOrgCode\": 14060,\n" + "\t\t\"bizOrgCodeDesc\": \"闪购\",\n" + "\t\t\"signHyperLink\": \"https://shangou-kd.meituan.com/#/signContract\"\n" + "\t}, {\n" + "\t\t\"singleMultiPoiType\": 2,\n" + "\t\t\"bizOrgCode\": 14090,\n" + "\t\t\"bizOrgCodeDesc\": \"医药\",\n" + "\t\t\"signHyperLink\": \"https://yiyao.meituan.com/main/frame#/page/home\"\n" + "\t}\n" + "]"), BizOrdCodeSignLinkRelBO.class);
    }

    /**
     * poi查询是否并行开关
     */
    public static boolean queryPoiQueryAgreeSwitch() {
        return ConfigUtilAdapter.getBoolean("query_poi_query_agree_switch", true);
    }

    /**
     * 查询操作日志走ES流量占比
     * getQueryCustomerOplogESPercent
     *
     * @return poiquery数量限制
     */
    public static int getPoiQueryPartitionLimit() {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.config.MccConfig.getPoiQueryPartitionLimit()");
        return ConfigUtilAdapter.getInt("poi_query_partition_limit", 300);
    }

    /**
     * 加密的record_id做灰度, 全量为： 10000
     */
    public static int readSalveRecordIdGrayPercent() {
        return ConfigUtilAdapter.getInt("read_salve_record_id_gray_percent", 0);
    }

    /**
     * 获取外部签约数据重试次数
     */
    public static int queryEcontractDataRetryTimes() {
        return ConfigUtilAdapter.getInt("query_econtract_data_retry_times", 3);
    }

    public static boolean poifeeTempletTransferMasterSwitch() {
        return ConfigUtilAdapter.getBoolean("poifee_templet_transfer_master_switch", false);
    }

    public static int poifeeTempletTransferWhiteLableid() {
        return ConfigUtilAdapter.getInt("poifee_templet_transfer_white_lableid", 659);
    }

    public static String poifeeTempletTransferGrayCustomerIds() {
        return ConfigUtilAdapter.getString("poifee_templet_transfer_gray_customerids");
    }

    public static int poifeeTempletTransferGrayCustomerProp() {
        return ConfigUtilAdapter.getInt("poifee_templet_transfer_gray_customerprop", 0);
    }

    /**
     * 子门店特殊标签，如企餐、极速达
     *
     * @return
     */
    public static List<String> getSpecialSubPoiTagIds() {
        List<String> tagIdList = new ArrayList<>();
        String tagIds = ConfigUtilAdapter.getString("special_sub_tag_ids", "394");
        String[] ss = tagIds.split(",");
        tagIdList.addAll(Arrays.asList(ss));
        return tagIdList;
    }

    public static int signBatchGrayCustomerIdPercent() {
        return ConfigUtilAdapter.getInt("sign_batch_gray_customer_id_percent", 0);
    }

    public static int signBatchLimitMaxNew() {
        return ConfigUtilAdapter.getInt("sign_batch_limit_max_new", 200);
    }

    public static int signBatchPackLimitMaxNew() {
        return ConfigUtilAdapter.getInt("sign_batch_pack_limit_max_new", 200);
    }

    /**
     * 需要过滤的：企餐门店、极速达子门店
     */
    public static List<Long> getFilteredPoiTagIds() {
        String filteredPoiTagIds = ConfigUtilAdapter.getString("filtered_poi_tag_ids", "216,378");
        String[] split = filteredPoiTagIds.split(",");
        return Arrays.stream(split).map(String::trim).map(Long::valueOf).collect(Collectors.toList());
    }

    /**
     * 闪购2.2全城送合同模板id
     */
    public static int getWholeCityTemplateIdInSg22() {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.config.MccConfig.getWholeCityTemplateIdInSg22()");
        return ConfigUtilAdapter.getInt("whole_city_template_id_in_sg22", 161);
    }

    /**
     * 是否将存量闪购2.2（专快混）全城送模板迁移到新平台
     */
    public static boolean isSg22WholeCityTemplateMigratedToNewPlatform() {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.config.MccConfig.isSg22WholeCityTemplateMigratedToNewPlatform()");
        return ConfigUtilAdapter.getBoolean("sg22_whole_city_template_migrate_to_new_platform", true);
    }

    /**
     * 是否将存量闪购2.2（专快混）全城送模板迁移到新平台
     */
    public static int getC2ContractIdFixGrayPercent() {
        return ConfigUtilAdapter.getInt("get_c2contractid_fix_gray_percent", 0);
    }

    /**
     * 四轮履约补充协议模板id
     */
    public static int getFourWheelPerfSupplementTemplateId() {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.config.MccConfig.getFourWheelPerfSupplementTemplateId()");
        return ConfigUtilAdapter.getInt("four_wheel_perf_supplement_template_id", 166);
    }

    /**
     * 四轮履约补充协议模板版本
     */
    public static int getFourWheelPerfSupplementTemplateVersion() {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_1", "com.sankuai.meituan.waimai.customer.config.MccConfig.getFourWheelPerfSupplementTemplateVersion()");
        return ConfigUtilAdapter.getInt("four_wheel_perf_supplement_template_version", 0);
    }

    /**
     * 极速达模式合作协议模板id
     */
    public static int getSpeedyDeliveryCooperationTemplateId() {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.config.MccConfig.getSpeedyDeliveryCooperationTemplateId()");
        return ConfigUtilAdapter.getInt("speedy_delivery_cooperation_template_id", 167);
    }

    /**
     * 极速达模式合作协议模板版本
     */
    public static int getSpeedyDeliveryCooperationTemplateVersion() {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.config.MccConfig.getSpeedyDeliveryCooperationTemplateVersion()");
        return ConfigUtilAdapter.getInt("speedy_delivery_cooperation_template_version", 0);
    }

    /**
     * 老结算功能下线
     * true-开关开启
     * false-开关关闭
     */
    public static boolean oldSettleFuncOffineSwitch() {
        return ConfigUtilAdapter.getBoolean("old_settle_func_Offine_switch", true);
    }

    /**
     * 老结算功能下线-第二批
     * true-开关开启
     * false-开关关闭
     * @return
     */
    public static boolean getOfflineSettleSwitch() {
        return ConfigUtilAdapter.getBoolean("old_settle_func_offline_switch_v2", false);
    }

    /**
     * 老结算功能下线-第三批
     * true-开关开启
     * false-开关关闭
     * @return
     */
    public static boolean getOfflineSettleSwitchV3() {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.config.MccConfig.getOfflineSettleSwitchV3()");
        return ConfigUtilAdapter.getBoolean("old_settle_func_offline_switch_v3", false);
    }

    /**
     * 大象异常信息告警人 taomengchun,lihaowei,zhongsihui,limingxuan,liuyunjie05,sunshuai07,liuyi60,chenyihao04,liuyu116,lixuepeng,zhaijiawen03,yinzhangfu,shenshuxin,wangzhenrong04,wangyongfang,wangyongchao04
     */
    public static List<String > getDaXiangAlarmMisList() {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.config.MccConfig.getDaXiangAlarmMisList()");
        String misIds = ConfigUtilAdapter.getString("da_xiang_alarm_mis", "limingxuan,liuyunjie05,wangyongfang,chiyuting04");
        return SPLITTER.splitToList(misIds);
    }

    /**
     * 全局合同默认分页大小
     */
    public static Integer getGlobalDefaultPageSize() {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_1", "com.sankuai.meituan.waimai.customer.config.MccConfig.getGlobalDefaultPageSize()");
        return ConfigUtilAdapter.getInt("global_default_page_size", 20);
    }

    /**
     * 导出功能数目上限
     */
    public static Integer getExportGlobalEcontractNumberUpper() {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_2", "com.sankuai.meituan.waimai.customer.config.MccConfig.getExportGlobalEcontractNumberUpper()");
        return ConfigUtilAdapter.getInt("export_global_econtract_number_upper", 10000);
    }

    /**
     * 全局合同信息数据角色超级管理员 UAC Id
     */
    public static Integer getSuperDataAdminUacRoleId() {
        return ConfigUtilAdapter.getInt("super_admin_uac_role_id", 10060815);
    }

    /**
     * 全局合同信息外卖业务线总部角色Uac Id
     */
    public static Integer getWaiMaiBizLineUacRoleId() {
        return ConfigUtilAdapter.getInt("wai_mai_biz_line_uac_role_id", 10053645);
    }

    /**
     * 全局合同信息闪购业务线总部角色Uac Id
     */
    public static Integer getShanGouBizLineUacRoleId() {
        return ConfigUtilAdapter.getInt("shan_gou_biz_line_uac_role_id", 10053646);
    }

    /**
     * 全局合同信息医药业务线总部角色Uac Id
     */
    public static Integer getYiYaoBizLineUacRoleId() {
        return ConfigUtilAdapter.getInt("yi_yao_biz_line_uac_role_id", 10053647);
    }

    /**
     * 生成短链申请的bizType
     */
    public static Integer getShortLinkBizType() {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.config.MccConfig.getShortLinkBizType()");
        return ConfigUtilAdapter.getInt("get_short_link_biz_type", 739);
    }

    /**
     * 短链接过期时间
     */
    public static int getShortUrlExpireDays() {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.config.MccConfig.getShortUrlExpireDays()");
        return ConfigUtilAdapter.getInt("short_url_expire_days", 3);
    }

    /**
     * 查询角色的最大查询次数
     */
    public static int getMaxQueryTimesForRoles() {
        return ConfigUtilAdapter.getInt("get_max_query_times_for_roles", 4);
    }

    /**
     * C2合同有效期置为长期有效灰度
     * @return 灰度
     */
    public static Integer getGrayToSetC2DueDateLongEffect() {
        return ConfigUtilAdapter.getInt("gray_to_set_c2_due_date_long_effective", 0);
    }

    /**
     * 配置化合同的内容模板版本号配置
     */
    public static String getConfigContractTemplateVersion() {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.config.MccConfig.getConfigContractTemplateVersion()");
        return ConfigUtilAdapter.getString("config_contract_template_version_map", "");
    }

    /**
     * 停止校验左右区间
     */
    public static boolean closeCheckLeftAndRightInterval() {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_2", "com.sankuai.meituan.waimai.customer.config.MccConfig.closeCheckLeftAndRightInterval()");
        return ConfigUtilAdapter.getBoolean("close_check_left_and_right_interval", false);
    }

    /**
     * 是否校验闪购低费率
     */
    public static Boolean resignSgLowFee() {
        return ConfigUtilAdapter.getBoolean("C1_sg_low_fee_resign_validator_switch", false);
    }

    /**
     * 冷链即时达配送服务收费标准确认函内容模板ID
     */
    public static Integer getPdfTemplateIdOfColdChain() {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_1", "com.sankuai.meituan.waimai.customer.config.MccConfig.getPdfTemplateIdOfColdChain()");
        return ConfigUtilAdapter.getInt("pdf_template_id_of_cold_chain", 233);
    }

    /**
     * 冷链即时达配送服务收费标准确认函内容模板版本号
     */
    public static Integer getPdfTemplateVersionOfColdChain() {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.config.MccConfig.getPdfTemplateVersionOfColdChain()");
        return ConfigUtilAdapter.getInt("pdf_template_version_of_cold_chain", 0);
    }

    /**
     * 查询配置化信息时的重试次数
     */
    public static int getRetryTimesToQueryConfigContract() {
        return ConfigUtilAdapter.getInt("get_retry_times_to_query_config_contract", 3);
    }

    /**
     * 到餐C2合同的PDF模板ID
     */
    public static Integer getDaoCanC2ContractTemplateId() {
        return ConfigUtilAdapter.getInt("dao_can_c2_contract_template_id", 241);
    }

    /**
     * 到餐C2合同的PDF模板版本
     */
    public static Integer getDaoCanC2ContractTemplateVersion() {
        return ConfigUtilAdapter.getInt("get_dao_can_c2_contract_template_version", 0);
    }

    /**
     * 到餐门店ID入参size限制
     */
    public static Integer getDaoCanMtPoiListSizeLimit() {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.config.MccConfig.getDaoCanMtPoiListSizeLimit()");
        return ConfigUtilAdapter.getInt("dao_can_mt_poi_list_size_limit", 100);
    }

    /**
     * 到餐侧合同降级开关
     */
    public static boolean isSupportDaoCanCustomerContract() {
        return ConfigUtilAdapter.getBoolean("is_support_dao_can_customer_contract", true);
    }

    /**
     * 不支持取消签约
     */
    public static boolean notSupportCancelCustomerContract() {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.config.MccConfig.notSupportCancelCustomerContract()");
        return ConfigUtilAdapter.getBoolean("not_support_cancel_customer_contract", true);
    }

    /**
     * 闪购异步推送消息开关
     */
    public static boolean isSupportShanGouAsyncPubMsg() {
        return ConfigUtilAdapter.getBoolean("is_support_shangou_async_pub_msg", true);
    }

    /**
     * 获取到餐uac角色id
     * @return
     */
    public static List<Long> getDcRoleIdList() {
        String dcRoleIdStr = ConfigUtilAdapter.getString("dao_can_role_id_set", "10088495, 10088499, 10079990, 10075642");
        String[] idArray = StringUtils.split(dcRoleIdStr, ',');

        return Arrays.stream(idArray).map(e -> Long.parseLong(e.trim())).collect(Collectors.toList());
    }

    public static long getKDBInternalMessageTemplateId() {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_2", "com.sankuai.meituan.waimai.customer.config.MccConfig.getKDBInternalMessageTemplateId()");
        return ConfigUtilAdapter.getLong("dao_can_KDB_internal_message_template_id", 57457L);
    }


    /**
     * 到餐业务线展示开关
     * @return
     */
    public static boolean isSupportShowDcBusinessGroupLine() {
        return ConfigUtilAdapter.getBoolean("is_support_show_dc_business_line", true);
    }

    /**
     * 到餐C1乙方展示信息
     * @return
     */
    public static String getDcC1SignInfo() {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.config.MccConfig.getDcC1SignInfo()");
        return ConfigUtilAdapter.getString("dc_c1_sign_info", "详见合同内容");
    }

    /**
     * 通知到餐C2合同生效重试次数
     */
    public static int getRetryTimesToNotifyDcC2SignSuccess() {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.config.MccConfig.getRetryTimesToNotifyDcC2SignSuccess()");
        return ConfigUtilAdapter.getInt("get_retry_times_to_notify_dc_c2_sign_success", 3);
    }

    /**
     * 查询外卖客户ID的重试次数
     */
    public static int getRetryTimesToGetWmCustomerByMtCustomerId() {
        return ConfigUtilAdapter.getInt("get_retry_times_to_get_wm_customer_by_mt_customer_id", 3);
    }

    /**
     * 到餐合同查看时候的跳转路径
     */
    public static String getDaoCanContractPdfUrlRoutePrefix() {
        return ConfigUtilAdapter.getString("dao_can_contract_pdf_url_route_prefix", "https://igate.waimai.meituan.com/igate/customer/daocan_contract_preview.html?type=image&pdfUrl=");
    }

    public static boolean repairWmBatchSaveDcC2() {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_1", "com.sankuai.meituan.waimai.customer.config.MccConfig.repairWmBatchSaveDcC2()");
        return ConfigUtilAdapter.getBoolean("repair_wm_batch_save_dc_c2", true);
    }

    public static boolean fixQueryHoldingSignTask() {
        return ConfigUtilAdapter.getBoolean("fix_query_holding_sign_task", true);
    }

    /**
     * 地图开平key
     */
    public static String getPoiProvideMapKey() {
        return ConfigUtilAdapter.getString("poi_provide_map_key", "mf7c18fa68544b5e85a748b0a8952d9t");
    }

    /**
     * 到餐合同失败时是否需要大象push消息
     */
    public static boolean isNeedAlertFailByDaXiang() {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_1", "com.sankuai.meituan.waimai.customer.config.MccConfig.isNeedAlertFailByDaXiang()");
        return ConfigUtilAdapter.getBoolean("is_need_alert_fail_by_da_xiang", true);
    }

    /**
     * 不需要大象push的异常文案
     */
    public static List<String> noNeedAlertDcContractFailReason() {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_1", "com.sankuai.meituan.waimai.customer.config.MccConfig.noNeedAlertDcContractFailReason()");
        String noNeedAlertMsg = ConfigUtilAdapter.getString("no_need_alert_dc_contract_fail_reason", "请勿重复添加");
        if (Strings.isEmpty(noNeedAlertMsg)) {
            return Collections.emptyList();
        } else {
            return SPLITTER.splitToList(noNeedAlertMsg);
        }
    }

    /**
     * 发送开店宝触达消息时，receiverIds的批次大小
     */
    public static int getReceiverIdsSizeToSendMessage() {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_2", "com.sankuai.meituan.waimai.customer.config.MccConfig.getReceiverIdsSizeToSendMessage()");
        return ConfigUtilAdapter.getInt("get_receiver_ids_size_to_send_message", 100);
    }

    /**
     * 发起到餐的打包签约时 是否需要校验到餐合同的写权限
     */
    public static boolean needCheckDcWriteAuthWithManualPack() {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_2", "com.sankuai.meituan.waimai.customer.config.MccConfig.needCheckDcWriteAuthWithManualPack()");
        return ConfigUtilAdapter.getBoolean("need_check_dc_write_auth_with_manual_pack", true);
    }

    /**
     * 闪购BD虚拟号
     * @return
     */
    public static String getShanGouBdVirtualNumber() {
        return ConfigUtilAdapter.getString("shangou_bd_virtual_number", "**********");
    }

    /**
     * 闪购虚拟号展示开关
     * @return
     */
    public static boolean showShanGouVirtualNumberSwitch() {
        return ConfigUtilAdapter.getBoolean("show_shangou_virtual_number_switch", true);
    }

    /**
     * 食光机一口价框架合同判断
     *
     * @return
     */
    public static int getFoodMachineContractTemplateId() {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_1", "com.sankuai.meituan.waimai.customer.config.MccConfig.getFoodMachineContractTemplateId()");
        return ConfigUtilAdapter.getInt("food_machine_contract_template_id", 1532153);
    }

    /**
     * 批量创建PDF，一个批次最大包含的合同数量
     * @return
     */
    public static int getBatchOnlyCreatePdfMaxSize() {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_1", "com.sankuai.meituan.waimai.customer.config.MccConfig.getBatchOnlyCreatePdfMaxSize()");
        return ConfigUtilAdapter.getInt("batch_only_create_pdf_max_size", 10);
    }

    /**
     * 多门店批量创建PDF，一个批次最大包含的合同数量
     * @return
     */
    public static int getBathOnlyCreatePdfWithMultiPoiMaxSize() {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.config.MccConfig.getBathOnlyCreatePdfWithMultiPoiMaxSize()");
        return ConfigUtilAdapter.getInt("batch_only_create_pdf_with_multi_poi_max_size", 1);
    }


    /**
     * 拼好饭数据比对开关
     * @return
     */
    public static boolean getPhfSignDataCompareSwitch() {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_1", "com.sankuai.meituan.waimai.customer.config.MccConfig.getPhfSignDataCompareSwitch()");
        return ConfigUtilAdapter.getBoolean("phf_sign_data_compare_switch", false);
    }

    /**
     * 拼好饭合同参数保存时间（仅在双写阶段进行比对时使用）,单位秒
     * @return
     */
    public static int getPhfPdfParamExpireTime() {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.config.MccConfig.getPhfPdfParamExpireTime()");
        return ConfigUtilAdapter.getInt("phf_pdf_param_expire_time", 60 * 60);
    }

    /**
     * 获取拼好饭电子合同签约的token
     * @return
     */
    public static String getPhfEcontractToken() {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.config.MccConfig.getPhfEcontractToken()");
        return ConfigUtilAdapter.getString("phf_econtract_token", "USR_phf_17ee8afd-b69f-4c");
    }

    /**
     * 拼好饭数据比对不一致告警接收人
     * @return
     */
    public static List<String > getPhfSignDataCompareAlarmMisList() {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.config.MccConfig.getPhfSignDataCompareAlarmMisList()");
        String misIds = ConfigUtilAdapter.getString("phf_data_compare_alarm_mis", "zhangyuanhao02");
        return SPLITTER.splitToList(misIds);
    }

    /**
     * 拼好饭批量创建PDF最大超时时间（单位：s）
     * @return
     */
    public static int getPhfBatchCreatePdfMaxDelayTime() {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_1", "com.sankuai.meituan.waimai.customer.config.MccConfig.getPhfBatchCreatePdfMaxDelayTime()");
        return ConfigUtilAdapter.getInt("phf_batch_create_pdf_max_delay_time", 5);
    }

    /**
     * 拼好饭查询技术和基础信息的最大超时时间（单位：s）
     * @return
     */
    public static int getQueryPhfLogisticsFeeDataMaxDelayTime() {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_1", "com.sankuai.meituan.waimai.customer.config.MccConfig.getQueryPhfLogisticsFeeDataMaxDelayTime()");
        return ConfigUtilAdapter.getInt("query_phf_logistics_fee_data_max_delay_time", 20);
    }

    /**
     * 获取任务系统审批进度链接
     *
     * @return 任务系统审批进度链接
     */
    public static String getApprovalProgressUrl(Long ticketId) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.config.MccConfig.getApprovalProgressUrl(java.lang.Long)");
        String format = ConfigUtilAdapter.getString("approval_progress_url", "https://wm.ocrm.meituan.com/page/ticket/taskDetail/taskDetail.html?id={ticketId}");
        return format.replace("{ticketId}", ticketId.toString());
    }

    public static String getFallbackApproverForRebinding() {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_2", "com.sankuai.meituan.waimai.customer.config.MccConfig.getFallbackApproverForRebinding()");
        return  ConfigUtilAdapter.getString("fallback_approver_for_rebinding", "lixinya02");
    }

    /**
     * 是否支持通过优先级排序
     *
     * @return
     */
    public static boolean isSupportSortByPriority() {
        return ConfigUtilAdapter.getBoolean("is_support_sort_by_priority", true);
    }


    /**
     * 获取phf数据比对stageName的白名单
     * @return
     */
    public static List<String> getPhfCompareStageNameWhiteList() {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_1", "com.sankuai.meituan.waimai.customer.config.MccConfig.getPhfCompareStageNameWhiteList()");
        String stageNameStr = ConfigUtilAdapter.getString("phf_compare_stage_name_white", "econtract_view_content,ca_certify_b,ca_certify_e");
        return SPLITTER.splitToList(stageNameStr);
    }

    /**
     * 获取phf数据比对key的白名单
     * @return
     */
    public static List<String> getPhfCompareKeyWhiteList() {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_2", "com.sankuai.meituan.waimai.customer.config.MccConfig.getPhfCompareKeyWhiteList()");
        String keyStr = ConfigUtilAdapter.getString("phf_compare_key_white", "computeFormula,_base_order,_top_order");
        return SPLITTER.splitToList(keyStr);
    }

    /**
     * 签约平台合同pdf前缀
     * @return
     */
    public static String getPdfUrlPrefix() {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.config.MccConfig.getPdfUrlPrefix()");
        return ConfigUtilAdapter.getString("pdf_url_prefix", "https://econtract.meituan.com");
    }

    /**
     * 拼好饭非正式版合同名称
     * @return
     */
    public static String getPhfContractNameInFormal() {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.config.MccConfig.getPhfContractNameInFormal()");
        return ConfigUtilAdapter.getString("phf_contract_name_informal", "美团拼好饭服务费收费协议");
    }

    /**
     * 灰度中的模版列表
     * key：模版的枚举名称
     * value：是否走新模版，true表示走新平台模版，false表示走旧平台模版
     *
     * @return
     */
    public static Map<String, Boolean> getGrayNewPlatformTemplateEnumMap() {
        return JSONObject.parseObject(ConfigUtilAdapter.getString("gray_new_platform_template_enum","{\"TECHNICAL_SERVICE_SG_20\":true,\"TECHNICAL_SERVICE_SG_22\":true,\"PERFORMANCE_SERVICE_SG_22\":true,\"PERFORMANCE_SERVICE_SG_22_NKS\":true}"),
                new com.alibaba.fastjson.TypeReference<Map<String, Boolean>>() {});
    }
    /**
     * 是否替换代理商相关接口
     */
    public static boolean replaceAgentService() {
        return ConfigUtilAdapter.getBoolean("replace_agent_service", true);
    }
    /**
     * 会员卡协议模板id
     */
    public static int getVipCardTemplateId() {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_1", "com.sankuai.meituan.waimai.customer.config.MccConfig.getVipCardTemplateId()");
        return ConfigUtilAdapter.getInt("vip_card_cooperation_template_id", 2329);
    }

    /**
     * 会员卡协议模板版本
     */
    public static int getVipCardTemplateVersion() {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.config.MccConfig.getVipCardTemplateVersion()");
        return ConfigUtilAdapter.getInt("vip_card_cooperation_template_version", 0);
    }

    /**
     * 是否支持客户切换场景下C2合同打包签约
     */
    public static boolean isSupportC2ContractPackSignInCustomerChange() {
        return ConfigUtilAdapter.getBoolean("support_c2_contract_pack_sign_in_customer_change", false);
    }

    /**
     * 门店推广协议新增三快网络章
     */
    public static boolean isAddSKWLStamp(Integer customerId) {
        int customerIdTail = customerId % 100;
        int rate = ConfigUtilAdapter.getInt("add_skwl_stamp_rate", -1);
        return customerIdTail <= rate;
    }

    /**
     * B2C问医寻药标签
     * 线下环境 1365
     * 线上环境 959
     * 标签ID
     */
    public static int getB2CXunYiWenYaoLabelId() {
        return ConfigUtilAdapter.getInt("b2c_xun_yi_wen_yao_label_id", 959);
    }

    /**
     * B2C问医寻药客户C1合同模板ID
     */
    public static int getB2CXunYiWenYaoC1ContractTemplateId() {
        return ConfigUtilAdapter.getInt("b2c_xun_yi_wen_yao_c1_contract_template_id", 336);
    }

    /**
     * B2C问医寻药客户C1合同版本ID
     */
    public static int getB2CXunYiWenYaoC2ContractTemplateVersionId() {
        return ConfigUtilAdapter.getInt("b2c_xun_yi_wen_yao_c2_contract_template_version_id", 0);
    }

    /**
     * 获取国补经销商门店标签id
     */
    public static int getNationalSubsidyDistributorPoiLabelId() {
        return ConfigUtilAdapter.getInt("national_subsidy_distributor_poi_label_id", 955);
    }

    /**
     * 获取国补总部门店标签id
     */
    public static int getNationalSubsidyHeadQuartersPoiLabelId() {
        return ConfigUtilAdapter.getInt("national_subsidy_head_quarters_poi_label_id", 954);
    }

    /**
     * 客户动态数据路由总开关
     */
    public static boolean isCustomerReadRouter2BrandDB() {
        return ConfigUtilAdapter.getBoolean("is_customer_read_router_to_brand_db", false);
    }

    /**
     * 客户读请求路由的方法名单
     * @return
     */
    public static String getCustomerReadRouter2BrandDbMethod() {
        return ConfigUtilAdapter.getString("customer_read_router_to_brand_db_method", "[]");
    }

    /**
     * 客户读请求路由比例
     * @return
     */
    public static Integer getCustomerReadRouter2BrandDbRatio() {
        return ConfigUtilAdapter.getInt("customer_read_router_to_brand_db_ratio", 0);
    }

    /**
     * 获取三方工作台查询oneService接口映射关系
     */
    public static OneServiceQueryMappingConfig getOneServiceQueryMappingConfig () throws IOException {
        String oneServiceQueryMappingConfigStr = ConfigUtilAdapter.getString("one_service_query_mapping_config", "{}");
        return JsonUtils.fromJson(oneServiceQueryMappingConfigStr, OneServiceQueryMappingConfig.class);
    }

    /**
     * 获取三方工作台查询枚举字段信息
     */
    public static WmScThirdWorkplaceQueryEnumDTO getThirdWorkplaceQueryEnum () throws IOException {
        String wmScThirdWorkplaceQueryEnumStr = ConfigUtilAdapter.getString("third_workplace_query_enum_config", "{}");
        return JsonUtils.fromJson(wmScThirdWorkplaceQueryEnumStr, WmScThirdWorkplaceQueryEnumDTO.class);
    }

    public static String getOneServiceToken() {
        return ConfigUtilAdapter.getString("one_service_token", "");
    }

    public static boolean oneServiceMock() {
        return ConfigUtilAdapter.getBoolean("one_service_mock", false);
    }


    public static Integer getAdminDefaultQueryOrgId() {
        return ConfigUtilAdapter.getInt("third_workplace_admin_query_default_org_id", 16071);
    }


    public static Map<String, Integer> getThirdWorkplaceOrgIdMapping() {
        return JSONObject.parseObject(ConfigUtilAdapter.getString("third_workplace_org_id_mapping","{}"),
                new com.alibaba.fastjson.TypeReference<Map<String, Integer>>() {});
    }

    public static boolean thirdWorkplaceConcurrentSwitch() {
        return ConfigUtilAdapter.getBoolean("third_workplace_concurrent_switch", true);
    }

    public static String getWorkplaceListSortFiled() {
        return ConfigUtilAdapter.getString("workplace_list_sort_filed", "school_id");
    }

    /**
     * 不再调用wmSettleExportThriftService.modifySettleSignFail, 该接口已废弃
     */
    public static boolean notCallSettleModifySettleSignFail() {
        return ConfigUtilAdapter.getBoolean("not_call_settle_modify_settle_sign_fail", true);
    }

    /**
     * 打包签约分组任务数量限制
     */
    public static int getManualPackGroupSize() {
        return ConfigUtilAdapter.getInt("manual.pack.task.group.size", 300);
    }
}
