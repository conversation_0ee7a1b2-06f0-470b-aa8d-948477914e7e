package com.sankuai.meituan.waimai.customer.contract.partner.ability.sms.impl;

import com.sankuai.meituan.waimai.customer.contract.partner.ability.sms.WmPartnerCustomerContractSmsAbilityService;
import com.sankuai.meituan.waimai.customer.domain.WmEcontractSignPackDB;
import com.sankuai.meituan.waimai.customer.service.sign.batch.wrap.sms.WmEcontractSmsDataService;
import com.sankuai.meituan.waimai.customer.service.sign.signpack.WmEcontractSignPackService;
import com.sankuai.meituan.waimai.thrift.customer.constant.BusinessGroupLineEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.partnercontract.PushMsgSendRequestDTO;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.partnercontract.PushMsgSendResponseDTO;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * @description:
 * @author: liuyunjie05
 * @create: 2024/8/31 11:54
 */
@Service
@Slf4j
public class WmPartnerCustomerContractSmsAbilityServiceImpl implements WmPartnerCustomerContractSmsAbilityService {

    @Resource
    private WmEcontractSignPackService wmEcontractSignPackService;

    @Resource
    private WmEcontractSmsDataService wmEcontractSmsDataService;

    @Override
    public PushMsgSendResponseDTO sendBatchTaskPushMessage(PushMsgSendRequestDTO requestDTO) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_1", "com.sankuai.meituan.waimai.customer.contract.partner.ability.sms.impl.WmPartnerCustomerContractSmsAbilityServiceImpl.sendBatchTaskPushMessage(PushMsgSendRequestDTO)");
        try {
            String checkResult = checkSendParam(requestDTO);
            if (Strings.isNotEmpty(checkResult)) {
                return PushMsgSendResponseDTO.fail(checkResult);
            }
            WmEcontractSignPackDB signPackDB = wmEcontractSignPackService.selectByRecordBatchId(requestDTO.getRecordBatchId());
            wmEcontractSmsDataService.sendPushMsgForDaoCanPackTask(signPackDB, requestDTO.getSmsShortLink());
        } catch (WmCustomerException e) {
            log.warn("WmPartnerCustomerContractSmsAbilityServiceImpl#sendBatchTaskPushMessage, warn", e);
            return PushMsgSendResponseDTO.fail(e.getMsg());
        } catch (Exception e) {
            log.warn("WmPartnerCustomerContractSmsAbilityServiceImpl#sendBatchTaskPushMessage, error", e);
            return PushMsgSendResponseDTO.fail("消息发送失败");
        }
        return PushMsgSendResponseDTO.success();
    }


    private String checkSendParam(PushMsgSendRequestDTO requestDTO) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.contract.partner.ability.sms.impl.WmPartnerCustomerContractSmsAbilityServiceImpl.checkSendParam(PushMsgSendRequestDTO)");
        if (requestDTO == null) {
            return "参数异常";
        }
        if (requestDTO.getRecordBatchId() == null) {
            return "打包批次ID异常";
        }
        if (requestDTO.getGroupLineEnum() == null) {
            return "没有指定业务线";
        }
        if (requestDTO.getGroupLineEnum() != BusinessGroupLineEnum.DAOCAN_SERVICE) {
            return "暂时只支持到餐业务线";
        }
        return null;
    }


}
