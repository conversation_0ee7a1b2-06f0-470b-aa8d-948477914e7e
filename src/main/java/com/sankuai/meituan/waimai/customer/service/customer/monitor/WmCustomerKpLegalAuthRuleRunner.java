package com.sankuai.meituan.waimai.customer.service.customer.monitor;

import com.dianping.frog.sdk.alarm.DXUtil;
import com.dianping.frog.sdk.data.BinlogRawData;
import com.dianping.frog.sdk.data.ColumnInfo;
import com.dianping.frog.sdk.data.RawData;
import com.dianping.frog.sdk.rpc.RpcService;
import com.dianping.frog.sdk.rpc.RpcServiceFactory;
import com.dianping.frog.sdk.runner.DefaultRuleRunner;
import com.dianping.lion.client.util.JsonUtils;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.lang3.StringUtils;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 20221222
 * @desc KP法人授权添加监控
 */
public class WmCustomerKpLegalAuthRuleRunner extends DefaultRuleRunner {

    /**
     * 检测规则
     *
     * @param triggerData 触发数据：触发数据源的数据，即填写的消息1，但如果是双向校验，则两个流都会触发校验，因此triggerData可能是其中任一个
     * @param targetData  目标数据：根据触发数据源的设置的key对应的其他源数据源的数据；单流校验为空，多流校验则为所有数据源数据除去触发数据
     *                    单流时targetData为length为0的数组；多流时如果匹配阶段没有匹配上，targetData为length为0的数组
     * @return 为null则不进行告警，非null则调用alarm，并把该方法的返回结果作为参数checkResult传入alarm方法
     * 注意：代码内部不要修改RawData及其内部引用所指向的数据，如需要修改，请自行copy RawData对象修改
     */
    @Override
    public String check(RawData triggerData, RawData... targetData) throws Exception {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_1", "com.sankuai.meituan.waimai.customer.service.customer.monitor.WmCustomerKpLegalAuthRuleRunner.check(com.dianping.frog.sdk.data.RawData,com.dianping.frog.sdk.data.RawData[])");
        BinlogRawData binlogRawData = (BinlogRawData) triggerData;
        String tableName = binlogRawData.getRealTableName();
        Map<String, ColumnInfo> columnInfoMap = binlogRawData.getColumnInfoMap();
        RpcService rpcService = RpcServiceFactory.getThriftInstance("com.sankuai.meituan.waimai.thrift.customer.service.WmCustomerKpThriftService",
                "com.sankuai.waimai.e.customer", 3000, "10.171.117.221", "8430");
        Map<String, Object> params = Maps.newHashMap();
        params.put("tableName", tableName);
        params.put("kpType", columnInfoMap.get("kp_type").getNewValue());
        params.put("signerType", columnInfoMap.get("signer_type").getNewValue());
        params.put("legalAuthType", columnInfoMap.get("legal_auth_type").getNewValue());
        params.put("certType", columnInfoMap.get("cert_type").getNewValue());
        params.put("compellation", columnInfoMap.get("compellation").getNewValue());
        if (tableName.equals("wm_customer_kp_temp")) {
            params.put("kpId", columnInfoMap.get("kp_id").getNewValue());
        } else {
            //temp表无version字段
            params.put("version", columnInfoMap.get("version").getNewValue());
            params.put("customerId", columnInfoMap.get("customer_id").getNewValue());
        }
        params.put("state", columnInfoMap.get("state").getNewValue());
        params.put("id", columnInfoMap.get("id").getNewValue());

        params.put("operateType", binlogRawData.getDmlType());

        String result = rpcService.invoke("checkKpLegalAuthInfo",
                Lists.newArrayList("com.sankuai.meituan.waimai.thrift.customer.domain.kp.KpLegalAuthMonitorReq"),
                Lists.newArrayList(JsonUtils.toJson(params)));

        if (!StringUtils.isBlank(result) && !result.equals("null") && !result.equals("\"\"")) {
            return result;
        }

        return null;
    }

    /**
     * 默认情况下alarm方法会自动发送大象告警(不要覆盖该方法)，如需通过mafka或者泛化调用等方式请复写alarm方法
     *
     * @param checkResult 是check方法的返回结果，传入alarm方便进行告警
     * @param triggerData 触发数据：触发数据源的数据
     * @param targetData  目标数据：根据触发数据源的设置的key对应的其他源数据源的数据；单流校验为空，多流校验则为所有数据源数据除去触发数据
     */
    @Override
    public void alarm(String checkResult, RawData triggerData, RawData... targetData) throws Exception {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.service.customer.monitor.WmCustomerKpLegalAuthRuleRunner.alarm(java.lang.String,com.dianping.frog.sdk.data.RawData,com.dianping.frog.sdk.data.RawData[])");
        DXUtil.sendAlarm(checkResult);
    }
}
