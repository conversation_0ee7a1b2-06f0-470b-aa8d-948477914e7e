package com.sankuai.meituan.waimai.customer.service.sign.signpack;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.dianping.cat.Cat;
import com.dianping.cat.util.MetricHelper;
import com.google.common.collect.Lists;
import com.sankuai.meituan.waimai.customer.bo.sign.SignPackSmsInfoBo;
import com.sankuai.meituan.waimai.customer.constant.SignPackStatusConstant;
import com.sankuai.meituan.waimai.customer.constant.SignPackTypeConstant;
import com.sankuai.meituan.waimai.customer.constant.WmCustomerConstant;
import com.sankuai.meituan.waimai.customer.constant.contract.CommonTaskConstant;
import com.sankuai.meituan.waimai.customer.constant.customer.MetricConstant;
import com.sankuai.meituan.waimai.customer.dao.WmEcontractSignPackDBMapper;
import com.sankuai.meituan.waimai.customer.domain.WmEcontractSignBatchDB;
import com.sankuai.meituan.waimai.customer.domain.WmEcontractSignPackDB;
import com.sankuai.meituan.waimai.customer.domain.WmEcontractSignTaskDB;
import com.sankuai.meituan.waimai.customer.service.sign.applytask.context.ManualPackNoticeContext;
import com.sankuai.meituan.waimai.customer.service.sign.biz.WmEcontractBigBatchParseService;
import com.sankuai.meituan.waimai.customer.service.sign.biz.WmEcontractBigTaskParseService;
import com.sankuai.meituan.waimai.customer.service.sign.biz.WmEcontractManualBatchBizService;
import com.sankuai.meituan.waimai.customer.service.sign.mafka.producer.WmEContractUpdatePackStatusProducer;
import com.sankuai.meituan.waimai.customer.service.sign.pack.WmEcontractCustomerPackService;
import com.sankuai.meituan.waimai.customer.util.WmEcontractSignBatchDBUtil;
import com.sankuai.meituan.waimai.thrift.constant.WmContractErrorCodeConstant;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractBatchStateEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractBatchContextBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractBatchDeliveryInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractNotifyBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.partner.DaoCanContractContext;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * Author: 窦旭蒙
 * MIS: douxumeng
 * Date: 2022-03-14 15:09
 * Email: <EMAIL>
 * Desc:
 */
@Slf4j
@Service
public class WmEcontractSignPackService {

    @Autowired
    private WmEcontractSignPackDBMapper wmEcontractSignPackDBMapper;

    @Autowired
    private WmEcontractBigBatchParseService wmEcontractBigBatchParseService;

    @Autowired
    private WmEcontractCustomerPackService wmEcontractCustomerPackService;

    @Autowired
    private WmEcontractManualBatchBizService wmEcontractManualBatchBizService;

    @Autowired
    private WmEcontractBigTaskParseService wmEcontractBigTaskParseService;

    @Autowired
    private WmEContractUpdatePackStatusProducer wmEContractUpdatePackStatusProducer;

    public static List<Integer> PACK_END_STATUS =
            Lists.newArrayList(SignPackStatusConstant.SUCCESS, SignPackStatusConstant.FAIL, SignPackStatusConstant.CANCEL);

    public static List<Integer> PACK_CAN_PUSH_MSG_STATUS =
            Lists.newArrayList(SignPackStatusConstant.ALL_APPLY, SignPackStatusConstant.SUCCESS, SignPackStatusConstant.FAIL, SignPackStatusConstant.CANCEL);


    public Long applySignPack(ManualPackNoticeContext context) {
        WmEcontractSignPackDB signPack = new WmEcontractSignPackDB();
        signPack.setStatus(SignPackStatusConstant.INIT);
        signPack.setType(SignPackTypeConstant.MANUAL);
        signPack.setCustomerId(context.getCustomerId());
        signPack.setTaskNum(context.getAllTaskInfo().size());
        wmEcontractSignPackDBMapper.insertSelective(signPack);
        return signPack.getId();
    }

    public void updateRecordBatchById(Long signPackId, Long recordBatchId) {
        wmEcontractSignPackDBMapper.updateRecordBatchById(signPackId, recordBatchId);
    }

    public WmEcontractSignPackDB selectByRecordBatchId(Long recordBatchId) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.meituan.waimai.customer.service.sign.signpack.WmEcontractSignPackService.selectByRecordBatchId(java.lang.Long)");
        return wmEcontractSignPackDBMapper.selectByRecordBatchId(recordBatchId);
    }

    public void updateSignPackStatusById(Long signPackId, Integer status) {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.service.sign.signpack.WmEcontractSignPackService.updateSignPackStatusById(java.lang.Long,java.lang.Integer)");
        wmEcontractSignPackDBMapper.updateStatusByRecordId(signPackId, status);
    }

    public void updateSignPackToAllApplyStatus(Long signPackId, List<EcontractBatchContextBo> batchContextBoList,
                                               Integer wmCustomerId) throws WmCustomerException, TException {
        log.info("updateSignPackToAllApplyStatus#batchContextBoList:{}，wmCustomerId:{}", JSON.toJSONString(batchContextBoList), wmCustomerId);
        if (signPackId > 0 && wmEcontractCustomerPackService.manualSignUpdateGray(wmCustomerId)) {
            List<Long> batchIdList = Lists.newArrayList();
            for(EcontractBatchContextBo batchContextBo : batchContextBoList){
                batchIdList.add(batchContextBo.getBatchId());
            }
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("signPackId", signPackId);
            jsonObject.put("batchIdList", batchIdList);
            jsonObject.put("wmCustomerId", wmCustomerId);
            jsonObject.put("tryStatus", "allapply");
            log.info("updateSignPackToAllApplyStatus sendMsg:{}", jsonObject.toJSONString());
            Long packId = batchContextBoList.get(0).getSignPackId();
            wmEContractUpdatePackStatusProducer.sendMsg(jsonObject, packId);
        }
    }

    public boolean trySignPackStatusToAllApply(Long signPackId, Long batchId) throws WmCustomerException, TException {
        boolean isAllApply = false;
        try {
            //试图更新状态为all_apply状态
            WmEcontractSignPackDB signPackDB = wmEcontractSignPackDBMapper.selectByPrimaryKey(signPackId);
            log.info("trySignPackStatusToAllApply#signPackDB:{}", JSON.toJSONString(signPackDB));
            List<WmEcontractSignBatchDB> signBatchDBList = wmEcontractBigBatchParseService.querySignBatchListByPackIdAndStatusMaster(signPackId,
                    EcontractBatchStateEnum.IN_PROCESSING.getName());
            log.info("trySignPackStatusToAllApply#signBatchDBList:{}", JSON.toJSONString(signBatchDBList));
            Integer packTaskNum = packTaskNumCalculate(signPackDB);
            if (packTaskNum != signBatchDBList.size()) {
                wmEcontractSignPackDBMapper.updateStatusByRecordId(signPackId, SignPackStatusConstant.PART_APPLY);
            } else {
                wmEcontractSignPackDBMapper.updateStatusByRecordId(signPackId, SignPackStatusConstant.ALL_APPLY);
                isAllApply = true;
            }
        } catch (Exception e) {
            log.error("trySignPackStatusToAllApply尝试更新为【全部提交】状态失败，signPackId:{}，batchId:{}", signPackId, batchId, e);
            throw new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR, "发起打包签约失败");
        }
        return isAllApply;
    }

    public void updateSignPackToEndStatusDeploying(WmEcontractSignBatchDB batchDB,
                                                   EcontractBatchContextBo batchContextBo,
                                                   EcontractNotifyBo notifyBo) throws WmCustomerException{
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.service.sign.signpack.WmEcontractSignPackService.updateSignPackToEndStatusDeploying(WmEcontractSignBatchDB,EcontractBatchContextBo,EcontractNotifyBo)");
        Long signPackId = batchDB.getPackId();
        if (signPackId == 0) {
            return;
        }
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("batchDB", batchDB);
        jsonObject.put("batchContextBo", batchContextBo);
        jsonObject.put("notifyBo", notifyBo);
        jsonObject.put("tryStatus", "end");
        log.info("updateSignPackToEndStatus sendMsg:{}", jsonObject.toJSONString());
        wmEContractUpdatePackStatusProducer.sendMsg(jsonObject, batchDB.getPackId());
    }

    public void updateSignPackToEndStatus(WmEcontractSignBatchDB batchDB,
                                          EcontractNotifyBo notifyBo) throws WmCustomerException {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.meituan.waimai.customer.service.sign.signpack.WmEcontractSignPackService.updateSignPackToEndStatus(WmEcontractSignBatchDB,EcontractNotifyBo)");
        Long signPackId = batchDB.getPackId();
        if (signPackId == 0) {
            return;
        }
        log.info("updateSignPackToEndStatus#batchContextBo:{}",JSON.toJSONString(batchDB));
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("batchId", batchDB.getId());
        jsonObject.put("notifyBo", notifyBo);
        jsonObject.put("tryStatus", "end");
        log.info("updateSignPackToEndStatus sendMsg:{}", jsonObject.toJSONString());
        wmEContractUpdatePackStatusProducer.sendMsg(jsonObject, batchDB.getPackId());
    }

    public boolean isAllBatchSuccess(List<WmEcontractSignBatchDB> batchDBList) {
        //batch全部成功视为成功
        List<WmEcontractSignBatchDB> unSuccessBatchList = batchDBList.stream().filter(signBatch ->
                !signBatch.getBatchState().equals(EcontractBatchStateEnum.SUCCESS.getName())).collect(Collectors.toList());
        return unSuccessBatchList.size() == 0;
    }

    public boolean hasBatchFail(List<WmEcontractSignBatchDB> batchDBList) {
        List<WmEcontractSignBatchDB> failBatchList = batchDBList.stream().filter(signBatch ->
                signBatch.getBatchState().equals(EcontractBatchStateEnum.FAIL.getName())).collect(Collectors.toList());
        return failBatchList.size() > 0;
    }

    public boolean hasBatchCancel(List<WmEcontractSignBatchDB> batchDBList) {
        List<WmEcontractSignBatchDB> cancelBatchList = batchDBList.stream().filter(signBatch ->
                signBatch.getBatchState().equals(EcontractBatchStateEnum.CANCEL.getName())).collect(Collectors.toList());
        return cancelBatchList.size() > 0;
    }

    public boolean judgePackCallback(Long signPackId) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.service.sign.signpack.WmEcontractSignPackService.judgePackCallback(java.lang.Long)");
        WmEcontractSignPackDB signPackDB = wmEcontractSignPackDBMapper.selectByPrimaryKeyMaster(signPackId);
        return PACK_END_STATUS.contains(signPackDB.getStatus());
    }

    public WmEcontractSignPackDB querySignPackById(Long signPackId) {
        return wmEcontractSignPackDBMapper.selectByPrimaryKey(signPackId);
    }

    public List<WmEcontractSignPackDB> querySignPackByIdList(List<Long> signPackIdList){
        if(CollectionUtils.isEmpty(signPackIdList)){
            return Lists.newArrayList();
        }
        return wmEcontractSignPackDBMapper.selectByPrimaryKeyBatch(signPackIdList);
    }

    public void updateSmsParamInfoById(Long packId, String info) {
        WmEcontractSignPackDB signPackDB = querySignPackById(packId);
        if (null != signPackDB) {
            signPackDB.setSmsParamInfo(info);
            if (Objects.nonNull(signPackDB.getSmsParamInfo())) {
                Cat.logEvent(MetricConstant.METRIC_SIGN_PACK_UPDATE,
                    "update",
                    WmCustomerConstant.SUCCESS, "");

                MetricHelper.build()
                    .name(MetricConstant.METRIC_SIGN_PACK_UPDATE)
                    .count();
            }
            wmEcontractSignPackDBMapper.updateByPrimaryKeySelective(signPackDB);
        }
    }

    public List<String> queryRecordKeyBySignPackId(Long packId) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.service.sign.signpack.WmEcontractSignPackService.queryRecordKeyBySignPackId(java.lang.Long)");
        List<WmEcontractSignBatchDB> signBatchDBList = wmEcontractBigBatchParseService.querySignBatchListByPackId(packId);
        List<String> recordKeyList = signBatchDBList.stream().map(WmEcontractSignBatchDB::getRecordKey).collect(Collectors.toList());
        log.info("queryRecordKeyBySignPackId#result:{}", JSON.toJSONString(recordKeyList));
        return recordKeyList;
    }

    /**
     * 查询recordKey，并通过priotiry对recordKey排序
     * @param packId
     * @return
     */
    public List<String> queryRecordKeyBySignPackIdOrderByPriority(Long packId) throws WmCustomerException {
        log.info("WmEcontractSignPackService#queryRecordKeyBySignPackIdOrderByPriority packId:{}", packId);
        List<WmEcontractSignBatchDB> signBatchDBList = wmEcontractBigBatchParseService.querySignBatchListByPackId(packId);

        if(CollectionUtils.isEmpty(signBatchDBList)){
            return Lists.newArrayList();
        }

        // 按照priority降序排列
        List<WmEcontractSignBatchDB> signBatchDBListSortByPriority = WmEcontractSignBatchDBUtil.sortByPriority(signBatchDBList);

        List<String> recordKeyList = signBatchDBListSortByPriority.stream()
                .map(WmEcontractSignBatchDB::getRecordKey)
                .collect(Collectors.toList());

        log.info("WmEcontractSignPackService#queryRecordKeyBySignPackIdOrderByPriority recordKeyList:{}", JSONObject.toJSONString(recordKeyList));
        return recordKeyList;
    }

    public List<DaoCanContractContext> queryDcContractInfo(List<String> recordKeyList) {
        return wmEcontractBigBatchParseService.queryByRecordKeys(recordKeyList).stream()
                .map(WmEcontractSignBatchDB::getBatchContext)
                .filter(StringUtils::isNotEmpty)
                .map(this::getDcContractContext)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    private DaoCanContractContext getDcContractContext(String signBatchContext) {
        try {
            EcontractBatchContextBo contextBo = JSON.parseObject(signBatchContext, EcontractBatchContextBo.class);
            return contextBo.getDaoCanContractContext();
        } catch (Exception e) {
            log.error("WmEcontractSignPackService#parseEcontractBatchContextBo, error", e);
            return null;
        }
    }

    public String querySmsParamInfoByRecordKey(String recordKey) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.meituan.waimai.customer.service.sign.signpack.WmEcontractSignPackService.querySmsParamInfoByRecordKey(java.lang.String)");
        WmEcontractSignBatchDB batchDB = wmEcontractBigBatchParseService.queryByRecordKey(recordKey);
        WmEcontractSignPackDB packDB = wmEcontractSignPackDBMapper.selectByPrimaryKey(batchDB.getPackId());
        return packDB.getSmsParamInfo();
    }

    public WmEcontractSignPackDB querySignPackByIdMaster(Long signPackId) {
        return wmEcontractSignPackDBMapper.selectByPrimaryKeyMaster(signPackId);
    }

    public String querySmsParamByPackId(Long packId) {
        WmEcontractSignPackDB signPackDB = wmEcontractSignPackDBMapper.selectByPrimaryKey(packId);
        String smsParamInfo = signPackDB.getSmsParamInfo();
        SignPackSmsInfoBo smsInfoBo = JSONObject.parseObject(smsParamInfo, SignPackSmsInfoBo.class);
        if (null == smsInfoBo) {
            return StringUtils.EMPTY;
        }
        return smsInfoBo.getDetail();
    }

    private Integer packTaskNumCalculate(WmEcontractSignPackDB signPackDB) throws WmCustomerException{
        Integer packTaskNum = signPackDB.getTaskNum();
        Long manualBatchId = wmEcontractManualBatchBizService.queryManualBatchIdBySignPackId(signPackDB.getCustomerId(), signPackDB.getId());
        log.info("packTaskNumCalculate#manualBatchId:{}", manualBatchId);
        List<WmEcontractSignTaskDB> wmEcontractSignTaskDBList = wmEcontractBigTaskParseService.getValidTaskByManualBatchId(manualBatchId);
        List<WmEcontractSignTaskDB> batchDeliveryTask = wmEcontractSignTaskDBList.stream().filter(signTask
                -> CommonTaskConstant.BATCH_APPLY_NAME_LIST.contains(signTask.getApplyType())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(batchDeliveryTask)) {
            //获取该批次配送manual_task的任务数量
            for (WmEcontractSignTaskDB batchPoiFeeTask : batchDeliveryTask) {
                EcontractBatchDeliveryInfoBo batchDeliveryInfoBo = JSON.parseObject(batchPoiFeeTask.getApplyContext(), EcontractBatchDeliveryInfoBo.class);
                int deliveryManualTaskNum = batchDeliveryInfoBo.getWmPoiIdList().size();
                log.info("packTaskNumCalculate#调整前,packTaskNum:{},deliveryManualTaskNum:{}", packTaskNum, deliveryManualTaskNum);
                if (packTaskNum > 1) {
                    if (deliveryManualTaskNum > 1) {
                        packTaskNum = packTaskNum - deliveryManualTaskNum + 1;
                        log.info("packTaskNumCalculate#有批量配送任务，调整后，packTaskNum:{},deliveryManualTaskNum:{}", packTaskNum, deliveryManualTaskNum);
                    } else {
                        log.info("packTaskNumCalculate#批量配送任务门店数为1，不重新计算，packTaskNum:{},deliveryManualTaskNum:{}", packTaskNum, deliveryManualTaskNum);
                    }
                } else {
                    log.info("packTaskNumCalculate#打包子任务数为1，不重新计算，packTaskNum:{},deliveryManualTaskNum:{}", packTaskNum, deliveryManualTaskNum);
                }
            }
        }
        log.info("packTaskNumCalculate#result，packTaskNum:{}", packTaskNum);
        return packTaskNum;
    }




}
