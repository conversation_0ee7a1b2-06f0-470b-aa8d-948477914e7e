package com.sankuai.meituan.waimai.customer.service.sc;

import com.alibaba.fastjson.JSONObject;
import com.sankuai.meituan.waimai.customer.dao.sc.WmSchoolMapper;
import com.sankuai.meituan.waimai.customer.domain.sc.WmSchoolDB;
import com.sankuai.meituan.waimai.heron.fee.api.domain.dto.FeeSettingDTO;
import com.sankuai.meituan.waimai.heron.fee.api.domain.dto.request.FeeSettingRequestDTO;
import com.sankuai.meituan.waimai.heron.fee.api.domain.dto.result.FeeSettingPagerResultDTO;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.WmScCodeConstants;
import com.sankuai.meituan.waimai.thrift.customer.domain.sc.*;
import com.sankuai.meituan.waimai.thrift.exception.WmSchCantException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.sankuai.meituan.waimai.customer.adapter.WmHeronFeeServiceAdapter;

import java.util.ArrayList;
import java.util.List;

/**
 * 学校基础费率信息处理逻辑
 * <AUTHOR>
 * @date 2023/09/26
 * @email <EMAIL>
 */
@Slf4j
@Service
public class WmScSchoolBasicFeeService {

    @Autowired
    private WmHeronFeeServiceAdapter wmHeronFeeServiceAdapter;

    @Autowired
    private WmSchoolMapper wmSchoolMapper;

    /**
     * 查询学校基础费率信息列表
     * @param wmScSchoolBasicFeeQueryDTO wmScSchoolBasicFeeQueryDTO
     * @return 基础费率信息列表
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     * @throws TException org.apache.thrift.TException
     */
    public WmScSchoolBasicFeeResultDTO getSchoolBasicFeeList(WmScSchoolBasicFeeQueryDTO wmScSchoolBasicFeeQueryDTO) throws WmSchCantException, TException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_1", "com.sankuai.meituan.waimai.customer.service.sc.WmScSchoolBasicFeeService.getSchoolBasicFeeList(com.sankuai.meituan.waimai.thrift.customer.domain.sc.WmScSchoolBasicFeeQueryDTO)");
        log.info("[WmScSchoolBasicFeeService.getSchoolBasicFeeList] input param: wmScSchoolBasicFeeQueryDTO = {}",
                JSONObject.toJSONString(wmScSchoolBasicFeeQueryDTO));
        WmScSchoolBasicFeeResultDTO resultDTO = new WmScSchoolBasicFeeResultDTO();
        if (wmScSchoolBasicFeeQueryDTO == null
                || wmScSchoolBasicFeeQueryDTO.getSchoolPrimaryId() == null
                || wmScSchoolBasicFeeQueryDTO.getSchoolPrimaryId() <= 0
                || wmScSchoolBasicFeeQueryDTO.getFirstCategory() == null
                || wmScSchoolBasicFeeQueryDTO.getPageNo() == null
                || wmScSchoolBasicFeeQueryDTO.getPageSize() == null) {
            log.error("[WmScSchoolBasicFeeService.getSchoolBasicFeeList] wmScSchoolBasicFeeQueryDTO is null, return.");
            throw new WmSchCantException(WmScCodeConstants.BIZ_PARA_ERROR, "入参校验不通过");
        }

        WmSchoolDB wmSchoolDB = wmSchoolMapper.selectSchoolById(wmScSchoolBasicFeeQueryDTO.getSchoolPrimaryId());
        if (wmSchoolDB == null) {
            log.error("[WmScSchoolBasicFeeService.getSchoolBasicFeeList] wmSchoolDB is null.");
            throw new WmSchCantException(WmScCodeConstants.BIZ_PARA_ERROR, "查询学校不存在");
        }

        FeeSettingRequestDTO feeSettingRequestDTO = new FeeSettingRequestDTO();
        // 配送方为商家自配
        feeSettingRequestDTO.setDeliveryType(2);
        feeSettingRequestDTO.setFirstCategory(wmScSchoolBasicFeeQueryDTO.getFirstCategory());
        // 二级物理城市ID
        feeSettingRequestDTO.setCityLocationId(wmSchoolDB.getCityId());
        feeSettingRequestDTO.setPageSize(wmScSchoolBasicFeeQueryDTO.getPageSize());
        feeSettingRequestDTO.setPageNum(wmScSchoolBasicFeeQueryDTO.getPageNo());
        // 查询学校基础费率信息
        FeeSettingPagerResultDTO feeSettingPagerResultDTO = wmHeronFeeServiceAdapter.getFeeSettingPagerResult(feeSettingRequestDTO);
        if (feeSettingPagerResultDTO == null
                || feeSettingPagerResultDTO.getData() == null
                || CollectionUtils.isEmpty(feeSettingPagerResultDTO.getData().getResultList())) {
            return resultDTO;
        }
        // 学校基础费率信息
        resultDTO.setSchoolBasicFeeDTOList(transFeeSettingToDTOList(feeSettingPagerResultDTO.getData().getResultList()));
        // 页数信息
        WmScPageInfoDTO pageInfoDTO = new WmScPageInfoDTO();
        pageInfoDTO.setPageNum(feeSettingPagerResultDTO.getData().getPageNum());
        pageInfoDTO.setPageSize(feeSettingPagerResultDTO.getData().getPageSize());
        pageInfoDTO.setTotal(feeSettingPagerResultDTO.getData().getTotal());
        resultDTO.setPageInfo(pageInfoDTO);

        return resultDTO;
    }

    /**
     * 费率信息DTO转化
     * @param feeSettingDTOList feeSettingDTOList
     * @return List<WmScSchoolBasicFeeDTO>
     */
    public List<WmScSchoolBasicFeeDTO> transFeeSettingToDTOList(List<FeeSettingDTO> feeSettingDTOList) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_2", "com.sankuai.meituan.waimai.customer.service.sc.WmScSchoolBasicFeeService.transFeeSettingToDTOList(java.util.List)");
        List<WmScSchoolBasicFeeDTO> wmScSchoolBasicFeeDTOList = new ArrayList<>();
        if (CollectionUtils.isEmpty(feeSettingDTOList)) {
            return wmScSchoolBasicFeeDTOList;
        }

        for (FeeSettingDTO feeSettingDTO : feeSettingDTOList) {
            WmScSchoolBasicFeeDTO dto = new WmScSchoolBasicFeeDTO();
            dto.setCityDesc(feeSettingDTO.getCityLocationLabel());
            dto.setLocationDesc(feeSettingDTO.getLocationLabel());
            dto.setDeliveryTypeDesc(feeSettingDTO.getDeliveryTypeLabel());
            dto.setLogisticsCodeDesc(feeSettingDTO.getLogisticsCodeLabel());
            dto.setFirstCategoryDesc(feeSettingDTO.getFirstCategoryLabel());
            dto.setSecondCategoryDesc(feeSettingDTO.getSecondCategoryLabel());
            dto.setServiceFee(feeSettingDTO.getSetting().get("tallTechFee"));
            wmScSchoolBasicFeeDTOList.add(dto);
        }
        return wmScSchoolBasicFeeDTOList;
    }


}
