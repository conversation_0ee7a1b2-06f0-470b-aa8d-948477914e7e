package com.sankuai.meituan.waimai.customer.service.kp.operatorv2.dboperatorv2;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.ImmutableSet;
import com.google.common.collect.Lists;
import com.sankuai.meituan.waimai.customer.bo.WmPoiDomain;
import com.sankuai.meituan.waimai.customer.constant.KmsKeyNameEnum;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerDB;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerKpPoiDB;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerSensitiveWordsService;
import com.sankuai.meituan.waimai.customer.util.ThrowUtil;
import com.sankuai.meituan.waimai.thrift.bo.WmCustomerDiffCellBo;
import com.sankuai.meituan.waimai.thrift.customer.constant.KpConstants;
import com.sankuai.meituan.waimai.thrift.customer.constant.KpSignerStateMachine;
import com.sankuai.meituan.waimai.thrift.customer.domain.kp.WmCustomerKp;
import com.sankuai.meituan.waimai.thrift.customer.domain.kp.WmCustomerKpTemp;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import com.sankuai.meituan.waimai.thrift.exception.WmServerException;
import com.sankuai.meituan.waimai.thrift.util.DiffUtil;
import org.apache.thrift.TException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Author: 窦旭蒙
 * MIS: douxumeng
 * Date: 2020-07-06 17:19
 * Email: <EMAIL>
 * Desc:
 */
@Service
public class OpmanagerDBOperator extends KpDBOperator {

    private static final Logger LOGGER = LoggerFactory.getLogger(OpmanagerDBOperator.class);

    @Autowired
    private WmCustomerSensitiveWordsService wmCustomerSensitiveWordsService;

    /**
     * 列表页可操作性更新状态
     */
    private static final Set<Byte> KPLIST_OPMANAGER_UPDATE_STATES =
            ImmutableSet.of(KpSignerStateMachine.NO_AUTHORIZE.getState(),
                    KpSignerStateMachine.EFFECT.getState(),
                    KpSignerStateMachine.AUTHORIZE_CANCEL.getState(),
                    KpSignerStateMachine.AUTHORIZE_FAIL.getState(),
                    KpSignerStateMachine.CHANGE_NO_AUTHORIZE.getState()
            );

    @Override
    public Object insert(WmCustomerDB wmCustomer, List<WmCustomerKp> oldCustomerKpList, List<WmCustomerKp> insertKpList, int uid, String uname) throws WmCustomerException, TException, WmServerException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.service.kp.operatorv2.dboperatorv2.OpmanagerDBOperator.insert(WmCustomerDB,List,List,int,String)");
        WmCustomerKp insertKp = getOperateKp(insertKpList);
        if (null == insertKp) {
            ThrowUtil.throwClientError("待新增的kp对象为空");
        }
        insertKp.setState(KpSignerStateMachine.NO_AUTHORIZE.getState());
        wmCustomerSensitiveWordsService.writeKpWhenInsertOrUpdate(insertKp);
        wmCustomerKpDBMapper.insertSelective(insertKp);
        int kpId = insertKp.getId();
        if (!StringUtils.isEmpty(insertKp.getRelPoiInfo())) {
            List<WmCustomerKpPoiDB> kpPoiDBList = getPoiDBList(insertKp, kpId);
            wmCustomerKpPoiTempDBMapper.batchInsert(kpPoiDBList);
        }
        wmCustomerKpLogService.insertKpLog(insertKp, uid, uname);
        LOGGER.info("insertKp customerId:{},kpId={},kpInfo={}", wmCustomer.getId(), insertKp.getId(), JSON.toJSONString(insertKp));
        return null;
    }

    @Override
    public Object update(WmCustomerDB wmCustomer, List<WmCustomerKp> oldCustomerKpList, List<WmCustomerKp> updateKpList, int uid, String uname) throws WmCustomerException, TException, WmServerException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.service.kp.operatorv2.dboperatorv2.OpmanagerDBOperator.update(WmCustomerDB,List,List,int,String)");
        WmCustomerKp updateKp = getOperateKp(updateKpList);
        if (null == updateKp) {
            ThrowUtil.throwClientError("待更新的kp对象为空");
        }
        WmCustomerKp oldCustomerKp = getOldKp(oldCustomerKpList, updateKp.getId());
        if (oldCustomerKp == null) {
            ThrowUtil.throwClientError("无法获取KP信息");
        }
        LOGGER.info("updateKp customerId:{},kpId={},kpInfo={}", wmCustomer.getId(), updateKp.getId(), JSON.toJSONString(updateKp));
        if (KPLIST_OPMANAGER_UPDATE_STATES.contains(updateKp.getState())) {
            if (updateKp.getState() == KpSignerStateMachine.NO_AUTHORIZE.getState()
                    || updateKp.getState() == KpSignerStateMachine.AUTHORIZE_CANCEL.getState()
                    || updateKp.getState() == KpSignerStateMachine.AUTHORIZE_FAIL.getState()) {
                updateKp.setState(KpSignerStateMachine.NO_AUTHORIZE.getState());
                // 获取更新前的门店状态用于操作日志封装
                List<WmCustomerKpPoiDB> oldKpList;
                if (updateKp.getState() == KpSignerStateMachine.EFFECT.getState()) {
                    // 线上表记录
                    oldKpList = wmCustomerKpPoiDBMapper.selectByKpId(updateKp.getId());
                } else {
                    // 线下表记录
                    oldKpList = wmCustomerKpPoiTempDBMapper.selectByKpId(updateKp.getId());
                }
                // 门店处理
                kpPoiDBHandler(updateKp);
                // 正式表
                updateKp.setValid(KpConstants.VALID);
                wmCustomerSensitiveWordsService.writeKpWhenInsertOrUpdate(updateKp);
                wmCustomerKpDBMapper.updateByPrimaryKeySelective(updateKp);
                // 日志处理
                List<WmCustomerDiffCellBo> diffCellBos = DiffUtil.compare(oldCustomerKp, updateKp,
                        differentCustomerKpService.getKpDiffFieldsMap());
                logFieldHandler(updateKp, oldKpList, diffCellBos);
                LOGGER.info("update#diffCellBos:{}", diffCellBos);
                wmCustomerKpLogService.updateKpLog(updateKp, diffCellBos, uid, uname);
            } else if (updateKp.getState() == KpSignerStateMachine.EFFECT.getState()) {
                // 获取更新前的门店状态用于操作日志封装
                List<WmCustomerKpPoiDB> oldKpList;
                if (updateKp.getState() == KpSignerStateMachine.EFFECT.getState()) {
                    // 线上表记录
                    oldKpList = wmCustomerKpPoiDBMapper.selectByKpId(updateKp.getId());
                } else {
                    // 线下表记录
                    oldKpList = wmCustomerKpPoiTempDBMapper.selectByKpId(updateKp.getId());
                }
                updateKp.setState(KpSignerStateMachine.CHANGE_NO_AUTHORIZE.getState());
                // 门店处理
                kpPoiDBHandler(updateKp);
                // 临时表
                upsertKpTemp(updateKp);
                // 日志处理
                List<WmCustomerDiffCellBo> diffCellBos = DiffUtil.compare(oldCustomerKp, updateKp,
                        differentCustomerKpService.getKpDiffFieldsMap());
                logFieldHandler(updateKp, oldKpList, diffCellBos);
                wmCustomerKpLogService.updateKpLog(updateKp, diffCellBos, uid, uname);
            } else {
                // 门店处理
                kpPoiDBHandler(updateKp);
                // 临时表
                upsertKpTemp(updateKp);
            }
        } else {
            ThrowUtil.throwClientError("当前KP状态无法进行更新操作");
        }
        return null;
    }

    /**
     * update重载，处理生效状态下更新指定字段的update的操作
     */
    public Object update(List<WmCustomerKp> oldCustomerKpList, List<WmCustomerKp> updateKpList, int uid, String uname) throws WmCustomerException, TException, WmServerException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.service.kp.operatorv2.dboperatorv2.OpmanagerDBOperator.update(java.util.List,java.util.List,int,java.lang.String)");
        // 更新kp线上表，状态为生效
        WmCustomerKp updateKp = getOperateKp(updateKpList);
        if (null == updateKp) {
            ThrowUtil.throwClientError("待更新的kp对象为空");
        }
        WmCustomerKp oldCustomerKp = getOldKp(oldCustomerKpList, updateKp.getId());
        if (oldCustomerKp == null) {
            ThrowUtil.throwClientError("无法获取KP信息");
        }
        // 更新正式表
        updateKp.setState(KpSignerStateMachine.EFFECT.getState());
        updateKp.setValid(KpConstants.VALID);
        wmCustomerSensitiveWordsService.writeKpWhenInsertOrUpdate(updateKp);
        wmCustomerKpDBMapper.updateByPrimaryKeySelective(updateKp);
        // 日志处理
        List<WmCustomerDiffCellBo> diffCellBos = DiffUtil.compare(oldCustomerKp, updateKp,
                differentCustomerKpService.getKpDiffFieldsMap());
        wmCustomerKpLogService.updateKpLog(updateKp, diffCellBos, uid, uname);
        return null;
    }

    @Override
    public Object delete(WmCustomerDB wmCustomer, List<WmCustomerKp> oldCustomerKpList, List<WmCustomerKp> deleteKpList, int uid, String uname) throws WmCustomerException, TException, WmServerException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_1", "com.sankuai.meituan.waimai.customer.service.kp.operatorv2.dboperatorv2.OpmanagerDBOperator.delete(WmCustomerDB,List,List,int,String)");
        WmCustomerKp deleteKp = getOperateKp(deleteKpList);
        if (null == deleteKp) {
            ThrowUtil.throwClientError("待删除的kp对象为空");
        }
        if (deleteKp.getState() == KpSignerStateMachine.NO_AUTHORIZE.getState()
                || deleteKp.getState() == KpSignerStateMachine.AUTHORIZE_CANCEL.getState()
                || deleteKp.getState() == KpSignerStateMachine.AUTHORIZE_FAIL.getState()) {
            // kp删除：正式表
            wmCustomerKpDBMapper.deleteByPrimaryKey(deleteKp.getId());
            // 门店解绑：临时表
            List<Long> poiInfoList = wmCustomerKpPoiService.getPoiList(deleteKp.getRelPoiInfo());
            if (!CollectionUtils.isEmpty(poiInfoList)) {
                wmCustomerKpPoiTempDBMapper.deleteByKpIdAndWmPoiIdList(deleteKp.getId(), poiInfoList);
            }
        } else if (deleteKp.getState() == KpSignerStateMachine.CHANGE_NO_AUTHORIZE.getState()) {
            // kp删除：临时表
            wmCustomerKpTempDBMapper.deleteByPrimaryKey(deleteKp.getId());
        } else if (deleteKp.getState() == KpSignerStateMachine.EFFECT.getState()) {
            // kp删除：正式表
            wmCustomerKpDBMapper.deleteByPrimaryKey(deleteKp.getId());
        } else {
            ThrowUtil.throwClientError("当前KP状态无法进行删除操作");
        }
        return null;
    }

    private void upsertKpTemp(WmCustomerKp updateKp) throws WmCustomerException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.service.kp.operatorv2.dboperatorv2.OpmanagerDBOperator.upsertKpTemp(com.sankuai.meituan.waimai.thrift.customer.domain.kp.WmCustomerKp)");
        WmCustomerKpTemp kpTempDB = new WmCustomerKpTemp();
        BeanUtils.copyProperties(updateKp, kpTempDB);
        // 幂等处理
        WmCustomerKpTemp tempKp = wmCustomerKpTempDBMapper.selectByKpIdMaster(updateKp.getId());
        wmCustomerSensitiveWordsService.readKpWhenSelect(tempKp);
        if (tempKp != null) {
            LOGGER.info("当前KP已存在临时表数据，更新临时数据");
            // 临时表
            kpTempDB.setId(tempKp.getId());
            kpTempDB.setKpId(updateKp.getId());
            kpTempDB.setValid(KpConstants.VALID);
            wmCustomerSensitiveWordsService.writeKpWhenInsertOrUpdate(kpTempDB);
            wmCustomerKpTempDBMapper.updateByPrimaryKeySelective(kpTempDB);
        } else {
            // 临时表
            kpTempDB.setKpId(updateKp.getId());
            kpTempDB.setValid(KpConstants.VALID);
            wmCustomerSensitiveWordsService.writeKpWhenInsertOrUpdate(kpTempDB);
            wmCustomerKpTempDBMapper.insertSelective(kpTempDB);
        }
    }

    private List<WmCustomerKpPoiDB> getPoiDBList(WmCustomerKp kp, Integer kpId) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.service.kp.operatorv2.dboperatorv2.OpmanagerDBOperator.getPoiDBList(com.sankuai.meituan.waimai.thrift.customer.domain.kp.WmCustomerKp,java.lang.Integer)");
        List<WmCustomerKpPoiDB> KpPoiDBList = new ArrayList<>();
        String[] pois = kp.getRelPoiInfo().split(",");
        List<String> poiList = Arrays.stream(pois).distinct().collect(Collectors.toList());
        for (String poi : poiList) {
            WmCustomerKpPoiDB kpPoiDB = new WmCustomerKpPoiDB();
            kpPoiDB.setKpId(kpId);
            kpPoiDB.setValid(1);//有效
            kpPoiDB.setWmPoiId(Long.valueOf(poi));
            KpPoiDBList.add(kpPoiDB);
        }
        return KpPoiDBList;
    }

    private void kpPoiDBHandler(WmCustomerKp kpInfo) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.service.kp.operatorv2.dboperatorv2.OpmanagerDBOperator.kpPoiDBHandler(com.sankuai.meituan.waimai.thrift.customer.domain.kp.WmCustomerKp)");
        // 查询DB中现有数据
        List<WmCustomerKpPoiDB> oldKpList;
        if (kpInfo.getState() == KpSignerStateMachine.EFFECT.getState()) {
            // 线上表记录
            oldKpList = wmCustomerKpPoiDBMapper.selectByKpId(kpInfo.getId());
        } else {
            // 线下表记录
            oldKpList = wmCustomerKpPoiTempDBMapper.selectByKpId(kpInfo.getId());
        }
        List<Long> newKpRelPoiList = getNewWmPoiList(kpInfo);
        List<Long> oldKpRelPoiList = getOldWmPoiList(oldKpList);
        // old有，new没有的——>update无效
        deleteKpRelPoi(newKpRelPoiList, oldKpRelPoiList, kpInfo);
        // old没有，new有的——>insert
        insertKpRelPoi(newKpRelPoiList, oldKpRelPoiList, kpInfo);
    }

    private List<Long> getNewWmPoiList(WmCustomerKp kpInfo) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.service.kp.operatorv2.dboperatorv2.OpmanagerDBOperator.getNewWmPoiList(com.sankuai.meituan.waimai.thrift.customer.domain.kp.WmCustomerKp)");
        List<Long> wmPoiList = new ArrayList<>();
        if (StringUtils.isEmpty(kpInfo.getRelPoiInfo())) {
            return wmPoiList;
        }
        String[] wmPois = kpInfo.getRelPoiInfo().split(",");
        for (String wmPoi : wmPois) {
            wmPoiList.add(Long.valueOf(wmPoi));
        }
        return wmPoiList;
    }

    private List<Long> getOldWmPoiList(List<WmCustomerKpPoiDB> kpList) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.service.kp.operatorv2.dboperatorv2.OpmanagerDBOperator.getOldWmPoiList(java.util.List)");
        List<Long> wmPoiList = new ArrayList<>();
        for (WmCustomerKpPoiDB wmPoi : kpList) {
            wmPoiList.add(wmPoi.getWmPoiId());
        }
        return wmPoiList;
    }

    private void deleteKpRelPoi(List<Long> newKpRelPoiList, List<Long> oldKpRelPoiList, WmCustomerKp kpInfo) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_2", "com.sankuai.meituan.waimai.customer.service.kp.operatorv2.dboperatorv2.OpmanagerDBOperator.deleteKpRelPoi(List,List,WmCustomerKp)");
        List<Long> newList = deepCopyKpRelPoiList(newKpRelPoiList);
        List<Long> oldList = deepCopyKpRelPoiList(oldKpRelPoiList);
        // old有，new没有
        oldList.removeAll(newList);
        if (!CollectionUtils.isEmpty(oldList)) {
            wmCustomerKpPoiTempDBMapper.deleteByKpIdAndWmPoiIdList(kpInfo.getId(), oldList);
        }
    }

    private void insertKpRelPoi(List<Long> newKpRelPoiList, List<Long> oldKpRelPoiList, WmCustomerKp kpInfo) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_1", "com.sankuai.meituan.waimai.customer.service.kp.operatorv2.dboperatorv2.OpmanagerDBOperator.insertKpRelPoi(List,List,WmCustomerKp)");
        List<Long> newList = deepCopyKpRelPoiList(newKpRelPoiList);
        List<Long> oldList = deepCopyKpRelPoiList(oldKpRelPoiList);
        List<WmCustomerKpPoiDB> insertList = new ArrayList<>();
        // new有，old没有
        newList.removeAll(oldList);
        if (!CollectionUtils.isEmpty(newList)) {
            for (Long wmPoiId : newList) {
                WmCustomerKpPoiDB wmCustomerKpPoiDB = new WmCustomerKpPoiDB();
                wmCustomerKpPoiDB.setKpId(kpInfo.getId());
                wmCustomerKpPoiDB.setWmPoiId(wmPoiId);
                wmCustomerKpPoiDB.setValid(1);
                insertList.add(wmCustomerKpPoiDB);
            }
            wmCustomerKpPoiTempDBMapper.batchInsert(insertList);
        }
    }

    private List<Long> deepCopyKpRelPoiList(List<Long> source) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.service.kp.operatorv2.dboperatorv2.OpmanagerDBOperator.deepCopyKpRelPoiList(java.util.List)");
        List<Long> target = Lists.newArrayList();
        if (CollectionUtils.isEmpty(source)) {
            return target;
        }
        for (Long temp : source) {
            target.add(new Long(temp));
        }
        return target;
    }

    private void logFieldHandler(WmCustomerKp updateKp, List<WmCustomerKpPoiDB> oldKpList, List<WmCustomerDiffCellBo> diffCellBos) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.kp.operatorv2.dboperatorv2.OpmanagerDBOperator.logFieldHandler(WmCustomerKp,List,List)");
        for (WmCustomerDiffCellBo wmCustomerDiffCellBo : diffCellBos) {
            if (wmCustomerDiffCellBo.getField().equals(KpConstants.KpFields.SIGN_TASK_TYPE)) {
                // 签约任务处理
                String beforeVal = logSignTaskHandler(wmCustomerDiffCellBo.getPre());
                String afterVal = logSignTaskHandler(wmCustomerDiffCellBo.getAft());
                wmCustomerDiffCellBo.setPre(beforeVal);
                wmCustomerDiffCellBo.setAft(afterVal);
            }
        }
        List<Long> newKpRelPoiList = getNewWmPoiList(updateKp);
        List<Long> oldKpRelPoiList = getOldWmPoiList(oldKpList);
        // old有new没有——>删除
        List<Long> deleteNewList = deepCopyKpRelPoiList(newKpRelPoiList);
        List<Long> deleteOldList = deepCopyKpRelPoiList(oldKpRelPoiList);
        LOGGER.info("logFieldHandler#deleteNewList:{},deleteOldList:{}", deleteNewList, deleteOldList);
        deleteOldList.removeAll(deleteNewList);
        String deletePoiLog = logPoiHandler(deleteOldList, 1);
        LOGGER.info("logFieldHandler#deletePoiLog:{}", deletePoiLog);
        // new有old没有——>新增
        List<Long> insertNewList = deepCopyKpRelPoiList(newKpRelPoiList);
        List<Long> insertOldList = deepCopyKpRelPoiList(oldKpRelPoiList);
        LOGGER.info("logFieldHandler#insertNewList:{},insertOldList:{}", insertNewList, insertOldList);
        insertNewList.removeAll(insertOldList);
        String insertPoiLog = logPoiHandler(insertNewList, 2);
        LOGGER.info("logFieldHandler#insertPoiLog:{}", insertPoiLog);
        if (org.apache.commons.lang3.StringUtils.isNotEmpty(insertPoiLog) || org.apache.commons.lang3.StringUtils.isNotEmpty(deletePoiLog)) {
            String preValue = "";
            String aftValue = "";
            if (org.apache.commons.lang3.StringUtils.isNotEmpty(insertPoiLog)) {
                preValue = insertPoiLog;
            }
            if (org.apache.commons.lang3.StringUtils.isNotEmpty(deletePoiLog)) {
                aftValue = deletePoiLog;
            }
            WmCustomerDiffCellBo poiDiffInfo = new WmCustomerDiffCellBo(WmCustomerKp.class, KpConstants.KpFields.REL_POI_INFO, KpConstants.KpFields.REL_POI_INFO_DESC, preValue, aftValue);
            LOGGER.info("logFieldHandler#poiDiffInfo:{}", poiDiffInfo);
            diffCellBos.add(poiDiffInfo);
        }
    }

    private String logSignTaskHandler(String signTaskVal) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.kp.operatorv2.dboperatorv2.OpmanagerDBOperator.logSignTaskHandler(java.lang.String)");
        if (StringUtils.isEmpty(signTaskVal)) {
            return null;
        }
        StringBuffer sb = new StringBuffer();
        String[] signTaskArray = signTaskVal.split(",");
        for (String signTask : signTaskArray) {
            if (signTask.equals("delivery")) {
                sb.append("配送信息");
                sb.append("、");
            } else if (signTask.equals("customer_poi_unbind")) {
                sb.append("客户解绑门店");
                sb.append("、");
            }
        }
        if (!StringUtils.isEmpty(sb)) {
            sb.deleteCharAt(sb.length() - 1);
        }
        return sb.toString();
    }

    /**
     * @param wmPoiIdList
     * @param operateType 1删除，2新增
     * @return
     */
    private String logPoiHandler(List<Long> wmPoiIdList, Integer operateType) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_2", "com.sankuai.meituan.waimai.customer.service.kp.operatorv2.dboperatorv2.OpmanagerDBOperator.logPoiHandler(java.util.List,java.lang.Integer)");
        if (CollectionUtils.isEmpty(wmPoiIdList)) {
            return null;
        }
        // 封装名称
        StringBuffer sb = new StringBuffer();
        if (operateType == 1) {
            sb.append("删除关联商家：");
        } else if (operateType == 2) {
            sb.append("新增关联商家：");
        }
        List<WmPoiDomain> poiList = null;
        try {
            poiList = wmPoiClient.pageGetWmPoiByWmPoiIdList(wmPoiIdList);
        } catch (WmCustomerException e) {
            LOGGER.error("logPoiHandler#获取门店名称执行失败");
        }
        for (WmPoiDomain wmPoiDomain : poiList) {
            sb.append(wmPoiDomain.getName());
            sb.append("(");
            sb.append(wmPoiDomain.getWmPoiId());
            sb.append(")");
            sb.append(",");
        }
        sb.deleteCharAt(sb.length() - 1);
        return sb.toString();
    }
}
