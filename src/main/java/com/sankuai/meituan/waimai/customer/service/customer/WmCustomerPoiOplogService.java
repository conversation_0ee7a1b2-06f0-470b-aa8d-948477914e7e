package com.sankuai.meituan.waimai.customer.service.customer;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.sankuai.meituan.waimai.customer.constant.customer.WmCustomerPoiOplogOperateTypeEnum;
import com.sankuai.meituan.waimai.customer.constant.customer.WmCustomerPoiOplogSourceTypeEnum;
import com.sankuai.meituan.waimai.customer.dao.WmCustomerPoiOplogMapper;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerPoiOplogDB;
import com.sankuai.meituan.waimai.customer.domain.task.CustomerOperateBO;
import com.sankuai.meituan.waimai.oplog.thrift.domain.WmPoiOplog;
import com.sankuai.meituan.waimai.poi.domain.WmPoiOpLog;
import com.sankuai.meituan.waimai.thrift.customer.constant.customer.task.CustomerTaskDetailSourceEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.customer.task.CustomerTaskSourceEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;

@Slf4j
@Service
public class WmCustomerPoiOplogService {

    public static final String CUSTOMER_LOG_TEMPLATE_POI_BIND = "客户ID:%s关联门店ID:%s";
    public static final String CUSTOMER_LOG_TEMPLATE_POI_UNBIND = "客户ID:%s解绑门店ID:%s";

    @Autowired
    private WmCustomerPoiOplogMapper wmCustomerPoiOplogMapper;

    public void bindOplog(WmCustomerPoiOplogDB record) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.service.customer.WmCustomerPoiOplogService.bindOplog(com.sankuai.meituan.waimai.customer.domain.WmCustomerPoiOplogDB)");
        try {
            record.setOperateType(WmCustomerPoiOplogOperateTypeEnum.BIND.getCode());
            record.setLog(String.format(CUSTOMER_LOG_TEMPLATE_POI_BIND, record.getCustomerId(), record.getWmPoiId()));
            wmCustomerPoiOplogMapper.insert(record);
        } catch (Exception e) {
            log.error("bindOplog失败 record={}", JSONObject.toJSONString(record), e);
        }
    }

    public void unbindOplog(WmCustomerPoiOplogDB record) {
        try {
            record.setOperateType(WmCustomerPoiOplogOperateTypeEnum.UNBIND.getCode());
            record.setLog(String.format(CUSTOMER_LOG_TEMPLATE_POI_UNBIND, record.getCustomerId(), record.getWmPoiId()));
            wmCustomerPoiOplogMapper.insert(record);
        } catch (Exception e) {
            log.error("unbindOplog record={}", JSONObject.toJSONString(record), e);
        }
    }


    public void batchBindOplog(WmCustomerPoiOplogDB record, List<Long> wmPoiIdList) {
        if (CollectionUtils.isEmpty(wmPoiIdList)) {
            return;
        }
        try {
            List<WmCustomerPoiOplogDB> list = Lists.newArrayList();
            for (Long wmPoiId : wmPoiIdList) {
                WmCustomerPoiOplogDB temp = new WmCustomerPoiOplogDB(record.getCustomerId(), record.getSourceType(), record.getOpUid(), record.getOpUname());
                temp.setWmPoiId(wmPoiId);
                temp.setOperateType(WmCustomerPoiOplogOperateTypeEnum.BIND.getCode());
                temp.setLog(String.format(CUSTOMER_LOG_TEMPLATE_POI_BIND, record.getCustomerId(), wmPoiId));
                list.add(temp);
            }
            wmCustomerPoiOplogMapper.batchInsert(list);
        } catch (Exception e) {
            log.error("batchBindOplog record={},wmPoiIdList={}", JSONObject.toJSONString(record), JSONObject.toJSONString(wmPoiIdList), e);
        }
    }

    public void batchUnbindOplog(WmCustomerPoiOplogDB record, List<Long> wmPoiIdList) {
        if (CollectionUtils.isEmpty(wmPoiIdList)) {
            return;
        }
        try {
            List<WmCustomerPoiOplogDB> list = Lists.newArrayList();
            for (Long wmPoiId : wmPoiIdList) {
                WmCustomerPoiOplogDB temp = new WmCustomerPoiOplogDB(record.getCustomerId(), record.getSourceType(), record.getOpUid(), record.getOpUname());
                temp.setWmPoiId(wmPoiId);
                temp.setOperateType(WmCustomerPoiOplogOperateTypeEnum.UNBIND.getCode());
                temp.setLog(String.format(CUSTOMER_LOG_TEMPLATE_POI_UNBIND, record.getCustomerId(), wmPoiId));
                list.add(temp);
            }
            wmCustomerPoiOplogMapper.batchInsert(list);
        } catch (Exception e) {
            log.error("batchUnbindOplog record={},wmPoiIdList={}", JSONObject.toJSONString(record), JSONObject.toJSONString(wmPoiIdList), e);
        }
    }


    /**
     * 获取操作记录来源
     *
     * @param sourceTypeEnum
     * @param customerOperateBO
     * @return
     */
    public WmCustomerPoiOplogSourceTypeEnum getOpSourceType(CustomerTaskSourceEnum sourceTypeEnum, CustomerOperateBO customerOperateBO) {
        if (sourceTypeEnum == null) {
            return WmCustomerPoiOplogSourceTypeEnum.UNKNOW;
        }
        if (sourceTypeEnum != CustomerTaskSourceEnum.BD_SETTLE) {
            return WmCustomerPoiOplogSourceTypeEnum.findByOpSource(sourceTypeEnum.getCode());
        }
        if (customerOperateBO == null) {
            return WmCustomerPoiOplogSourceTypeEnum.UNKNOW;
        }
        CustomerTaskDetailSourceEnum detailSourceEnum = CustomerTaskDetailSourceEnum.getByDesc(customerOperateBO.getOpDetailSource());
        List<Integer> xianfuSourceList = Lists.newArrayList(CustomerTaskDetailSourceEnum.XIAN_FU_BATCH_BIND.getCode(),
                CustomerTaskDetailSourceEnum.XIAN_FU_SINGLE_BIND.getCode(), CustomerTaskDetailSourceEnum.XIAN_FU_BATCH_UNBIND.getCode(),
                CustomerTaskDetailSourceEnum.XIAN_FU_SINGLE_UNBIND.getCode(), CustomerTaskDetailSourceEnum.RE_START_BIND.getCode());
        if (xianfuSourceList.contains(detailSourceEnum.getCode())) {
            return WmCustomerPoiOplogSourceTypeEnum.CUSTOMER_POI_LIST;
        }
        return WmCustomerPoiOplogSourceTypeEnum.UNKNOW;
    }

    /**
     * 获取操作记录来源
     *
     * @param taskOpSource
     * @param opSourceDetail
     * @return
     */
    public WmCustomerPoiOplogSourceTypeEnum getOpSourceTypeByTaskSource(Integer taskOpSource, String opSourceDetail) {
        if (taskOpSource == null || StringUtils.isBlank(opSourceDetail)) {
            return WmCustomerPoiOplogSourceTypeEnum.UNKNOW;
        }
        if (taskOpSource != CustomerTaskSourceEnum.BD_SETTLE.getCode()) {
            return WmCustomerPoiOplogSourceTypeEnum.findByOpSource(taskOpSource);
        }

        CustomerTaskDetailSourceEnum detailSourceEnum = CustomerTaskDetailSourceEnum.getByDesc(opSourceDetail);
        List<Integer> xianfuSourceList = Lists.newArrayList(CustomerTaskDetailSourceEnum.XIAN_FU_BATCH_BIND.getCode(),
                CustomerTaskDetailSourceEnum.XIAN_FU_SINGLE_BIND.getCode(), CustomerTaskDetailSourceEnum.XIAN_FU_BATCH_UNBIND.getCode(),
                CustomerTaskDetailSourceEnum.XIAN_FU_SINGLE_UNBIND.getCode(), CustomerTaskDetailSourceEnum.RE_START_BIND.getCode());
        if (xianfuSourceList.contains(detailSourceEnum.getCode())) {
            return WmCustomerPoiOplogSourceTypeEnum.CUSTOMER_POI_LIST;
        }
        return WmCustomerPoiOplogSourceTypeEnum.UNKNOW;
    }
}
