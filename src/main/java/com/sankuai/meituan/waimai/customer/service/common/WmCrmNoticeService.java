package com.sankuai.meituan.waimai.customer.service.common;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.meituan.util.ConfigUtilAdapter;
import com.sankuai.meituan.waimai.bizme.thrift.enums.WmAppTypeEnum;
import com.sankuai.meituan.waimai.bizme.thrift.service.WmBizNoticeThriftService;
import com.sankuai.meituan.waimai.customer.config.MccConfig;
import com.sankuai.meituan.waimai.thrift.constants.WmNoticeMainBodyTypeEnum;
import com.sankuai.meituan.waimai.thrift.constants.WmNoticeMsgFormatEnum;
import com.sankuai.meituan.waimai.thrift.constants.WmNoticeMustReadEnum;
import com.sankuai.meituan.waimai.thrift.constants.WmNoticePushMediaEnum;
import com.sankuai.meituan.waimai.thrift.dto.PublishNoticeDto;
import com.sankuai.meituan.waimai.thrift.exception.WmServerException;
import com.sankuai.meituan.waimai.thrift.service.WmNoticePublishThriftService;
import java.util.List;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 消息通知
 */
@Service
public class WmCrmNoticeService {

    private static final Logger LOGGER = LoggerFactory.getLogger(WmCrmNoticeService.class);

    @Autowired
    private WmNoticePublishThriftService.Iface wmNoticePublishThriftService;

    @Autowired
    private WmBizNoticeThriftService.Iface wmBizNoticeThriftService;

    /**
     * 修改卡失败
     * 
     * @param receiveUids
     * @param content
     * @return
     */
    public void modifyCardFailNotify(String receiveUids, Integer customerId, String content) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.service.common.WmCrmNoticeService.modifyCardFailNotify(java.lang.String,java.lang.Integer,java.lang.String)");
        try {

            PublishNoticeDto publishNoticeDto = new PublishNoticeDto();

            publishNoticeDto.setUid(0);
            publishNoticeDto.setReceiveUids(receiveUids);
            publishNoticeDto.setMsgFormat(WmNoticeMsgFormatEnum.MSG_FORMAT_PLAIN.getValue());
            JSONArray jsonArray = new JSONArray();
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("title", "【修改卡失败】");
            jsonObject.put("content", content);
            jsonArray.add(jsonObject);
            String message=jsonArray.toJSONString();
            publishNoticeDto.setMsg(message);
            publishNoticeDto.setWmNoticeTypeId(ConfigUtilAdapter.getInt("settle_info_modify_fail_notify_type", 0));
            publishNoticeDto.setMustRead(WmNoticeMustReadEnum.NO.getValue());
            publishNoticeDto
                    .setPushMedia(ConfigUtilAdapter.getString("settle_info_modify_fail_notify_type_receive_terminal",
                            String.valueOf(WmNoticePushMediaEnum.PUSH_MEDIA_DAXIANG.getValue())));
            Map<String, String> map = Maps.newHashMap();
            map.put(WmNoticeMainBodyTypeEnum.CUSTOMER_ID.getMainBodyType(), String.valueOf(customerId));
            publishNoticeDto.setBusinessMainBody(map);

            LOGGER.info("modifyCardFailNotify={}", JSON.toJSONString(publishNoticeDto));
            wmNoticePublishThriftService.publishNotice(publishNoticeDto);
        } catch (Exception e) {
            LOGGER.error("发送【修改卡失败】失败", e);
        }

    }

    /**
     * 客户信息批量操作通知
     * 
     * @param receiveUids
     * @param customerId
     * @param content
     */
    public void customerOperationNotify(String receiveUids, String title, Integer customerId, String content) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_2", "com.sankuai.meituan.waimai.customer.service.common.WmCrmNoticeService.customerOperationNotify(java.lang.String,java.lang.String,java.lang.Integer,java.lang.String)");
        try {

            PublishNoticeDto publishNoticeDto = new PublishNoticeDto();

            publishNoticeDto.setUid(0);
            publishNoticeDto.setReceiveUids(receiveUids);
            publishNoticeDto.setMsgFormat(WmNoticeMsgFormatEnum.MSG_FORMAT_PLAIN.getValue());
            JSONArray jsonArray = new JSONArray();
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("title", title);
            jsonObject.put("content", content);
            jsonArray.add(jsonObject);
            String message = jsonArray.toJSONString();
            publishNoticeDto.setMsg(message);
            publishNoticeDto.setWmNoticeTypeId(ConfigUtilAdapter.getInt("customer_operation_notify_type", 0));
            publishNoticeDto.setMustRead(WmNoticeMustReadEnum.NO.getValue());
            publishNoticeDto.setPushMedia(ConfigUtilAdapter.getString("customer_operation_notify_type_receive_terminal",
                    String.valueOf(WmNoticePushMediaEnum.PUSH_MEDIA_DAXIANG.getValue())));

            Map<String, String> map = Maps.newHashMap();
            map.put(WmNoticeMainBodyTypeEnum.CUSTOMER_ID.getMainBodyType(), String.valueOf(customerId));
            publishNoticeDto.setBusinessMainBody(map);

            LOGGER.info("customerOperationNotify={}", JSON.toJSONString(publishNoticeDto));
            wmNoticePublishThriftService.publishNotice(publishNoticeDto);
        } catch (Exception e) {
            LOGGER.error("发送" + title + "失败", e);
        }

    }

    /**
     * 结算信息批量操作
     * 
     * @param receiveUids
     * @param customerId
     * @param content
     */
    public void settleBatchCreateNotify(String receiveUids, Integer customerId, String content) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.service.common.WmCrmNoticeService.settleBatchCreateNotify(java.lang.String,java.lang.Integer,java.lang.String)");
        try {

            PublishNoticeDto publishNoticeDto = new PublishNoticeDto();

            publishNoticeDto.setUid(0);
            publishNoticeDto.setReceiveUids(receiveUids);
            publishNoticeDto.setMsgFormat(WmNoticeMsgFormatEnum.MSG_FORMAT_PLAIN.getValue());


            JSONArray jsonArray = new JSONArray();
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("title", "【结算信息批量操作】");
            jsonObject.put("content", content);
            jsonArray.add(jsonObject);
            String message = jsonArray.toJSONString();
            publishNoticeDto.setMsg(message);
            publishNoticeDto.setWmNoticeTypeId(ConfigUtilAdapter.getInt("settle_batch_tool_notify_type", 0));
            publishNoticeDto.setMustRead(WmNoticeMustReadEnum.NO.getValue());
            publishNoticeDto.setPushMedia(ConfigUtilAdapter.getString("settle_batch_tool_notify_type_receive_terminal",
                    String.valueOf(WmNoticePushMediaEnum.PUSH_MEDIA_DAXIANG.getValue())));
            Map<String, String> map = Maps.newHashMap();
            map.put(WmNoticeMainBodyTypeEnum.CUSTOMER_ID.getMainBodyType(), String.valueOf(customerId));
            publishNoticeDto.setBusinessMainBody(map);

            LOGGER.info("settleBatchCreateNotify={}", JSON.toJSONString(publishNoticeDto));
            wmNoticePublishThriftService.publishNotice(publishNoticeDto);
        } catch (Exception e) {
            LOGGER.error("发送【结算信息批量操作】失败", e);
        }

    }

    /**
     * 银行卡信息错误
     * 
     * @param receiveUids
     * @param customerId
     * @param content
     */
    public void bankInfoError(String receiveUids, Integer customerId, Integer wmPoiId, String content) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_1", "com.sankuai.meituan.waimai.customer.service.common.WmCrmNoticeService.bankInfoError(java.lang.String,java.lang.Integer,java.lang.Integer,java.lang.String)");
        try {

            PublishNoticeDto publishNoticeDto = new PublishNoticeDto();

            publishNoticeDto.setUid(0);
            publishNoticeDto.setReceiveUids(receiveUids);
            publishNoticeDto.setMsgFormat(WmNoticeMsgFormatEnum.MSG_FORMAT_PLAIN.getValue());

            JSONArray jsonArray = new JSONArray();
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("title", "【银行卡信息错误】");
            jsonObject.put("content", content);
            jsonArray.add(jsonObject);
            String message = jsonArray.toJSONString();
            publishNoticeDto.setMsg(message);
            publishNoticeDto.setWmNoticeTypeId(ConfigUtilAdapter.getInt("bank_info_error_type", 0));
            publishNoticeDto.setMustRead(WmNoticeMustReadEnum.NO.getValue());
            publishNoticeDto.setPushMedia(ConfigUtilAdapter.getString("bank_info_error_type_receive_terminal",
                    String.valueOf(WmNoticePushMediaEnum.PUSH_MEDIA_DAXIANG.getValue())));
            Map<String, String> map = Maps.newHashMap();
            if (customerId != null) {
                map.put(WmNoticeMainBodyTypeEnum.CUSTOMER_ID.getMainBodyType(), String.valueOf(customerId));
            } else if (wmPoiId != null) {
                map.put(WmNoticeMainBodyTypeEnum.WM_POI_ID.getMainBodyType(), String.valueOf(wmPoiId));
            }

            publishNoticeDto.setBusinessMainBody(map);

            LOGGER.info("bankInfoError={}", JSON.toJSONString(publishNoticeDto));
            wmNoticePublishThriftService.publishNotice(publishNoticeDto);
        } catch (Exception e) {
            LOGGER.error("发送【银行卡信息错误】失败", e);
        }

    }

    /**
     * 发布免审接口 商家推送消息
     * 
     * @param title 消息标题
     * @param content 消息文本，支持HTML样式标签
     * @param endTime 消息展示截止时间 10位时间戳
     * @param wmPoiIdList 门店id列表 size<=100
     */
    public void pubWmENoticeNoAudit(String title, String content, long endTime, List<Long> wmPoiIdList) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.service.common.WmCrmNoticeService.pubWmENoticeNoAudit(java.lang.String,java.lang.String,long,java.util.List)");
        LOGGER.info("pubWmENoticeNoAudit title={},content={},endTime={},wmPoiIdList={}", title, content, endTime, wmPoiIdList);
        try {
           boolean result= wmBizNoticeThriftService.pubWmENoticeNoAudit(title, content, endTime, wmPoiIdList,
                    Lists.newArrayList(WmAppTypeEnum.IOS, WmAppTypeEnum.PC, WmAppTypeEnum.ANDROID, WmAppTypeEnum.HARMONY));
            LOGGER.info("pubWmENoticeNoAudit result:[{}]",result);
        } catch (Exception e) {
            LOGGER.error("#pubWmENoticeNoAudit#发送【商家推送消息】失败", e);
        }
    }

    public void pubWmENoticeNoAuditWithAppType(String title, String pcContent, String appContent, long endTime, List<Long> wmPoiIdList) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.service.common.WmCrmNoticeService.pubWmENoticeNoAuditWithAppType(java.lang.String,java.lang.String,java.lang.String,long,java.util.List)");
        LOGGER.info("pubWmENoticeNoAuditWithAppType title:{} pcContent:{} appContent:{} endTime={} wmPoiIdList={}", title, pcContent, appContent, endTime, wmPoiIdList);
        try {
            int pushLevel = MccConfig.getMessagePushLevel();
            // 推送PC端消息
            boolean pcResult = wmBizNoticeThriftService.sendBusinessNotice(title, pcContent, endTime, wmPoiIdList, Lists.newArrayList(WmAppTypeEnum.PC), pushLevel);
            // 推送APP端消息
            boolean appResult = wmBizNoticeThriftService.sendBusinessNotice(title, appContent, endTime, wmPoiIdList, Lists.newArrayList(WmAppTypeEnum.IOS, WmAppTypeEnum.ANDROID), pushLevel);

            LOGGER.info("pubWmENoticeNoAudit pcResult:{}, appResult:{}", pcResult, appResult);
        } catch (Exception e) {
            LOGGER.error("#pubWmENoticeNoAuditWithAppType#发送【商家推送消息】失败", e);
        }
    }

    /**
     * 参考 https://km.sankuai.com/page/127886567
     *
     * @param receiveUids
     * @param msgFormatEnum
     * @param title
     * @param content
     * @param noticeTypeId  参考 https://whale.crm.waimai.sankuai.com/whale/notice/type/r/typeManage
     * @param mustReadEnum
     * @param publishMedia
     * @param mainBodyTypeEnum
     * @param mainBodyValue
     */
    public void publishNotice(String receiveUids, WmNoticeMsgFormatEnum msgFormatEnum, String title, String content, int noticeTypeId,
                              WmNoticeMustReadEnum mustReadEnum, String publishMedia, WmNoticeMainBodyTypeEnum mainBodyTypeEnum,
                              String mainBodyValue) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.common.WmCrmNoticeService.publishNotice(String,WmNoticeMsgFormatEnum,String,String,int,WmNoticeMustReadEnum,String,WmNoticeMainBodyTypeEnum,String)");
        try {
            PublishNoticeDto publishNoticeDto = new PublishNoticeDto();

            publishNoticeDto.setUid(0);
            publishNoticeDto.setReceiveUids(receiveUids);
            publishNoticeDto.setMsgFormat(msgFormatEnum.getValue());

            JSONArray msgJSONArray = new JSONArray();
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("title", title);
            jsonObject.put("content", content);
            msgJSONArray.add(jsonObject);
            String message = msgJSONArray.toJSONString();
            publishNoticeDto.setMsg(message);

            publishNoticeDto.setWmNoticeTypeId(noticeTypeId);
            publishNoticeDto.setMustRead(mustReadEnum.getValue());
            publishNoticeDto.setPushMedia(publishMedia);
            Map<String, String> map = Maps.newHashMap();
            map.put(mainBodyTypeEnum.getMainBodyType(), mainBodyValue);
            publishNoticeDto.setBusinessMainBody(map);

            LOGGER.info("publishNoticeDto = {}", JSON.toJSONString(publishNoticeDto));
            wmNoticePublishThriftService.publishNotice(publishNoticeDto);
        } catch (WmServerException e) {
            LOGGER.error("WmCrmNoticeService#notice 发送消息失败失败 code = {}, msg = {}", e.getCode(), e.getMsg(), e);
        } catch (Exception e) {
            LOGGER.error("WmCrmNoticeService#notice 发送消息失败失败", e);
        }
    }
}
