package com.sankuai.meituan.waimai.customer.service.sc.canteenstall.auditflow;

import com.alibaba.fastjson.JSONObject;
import com.sankuai.meituan.waimai.crm.ticket.thrift.domain.WmTicketDto;
import com.sankuai.meituan.waimai.customer.adapter.WmScEmployAdaptor;
import com.sankuai.meituan.waimai.customer.dao.sc.canteenstall.WmCanteenStallAuditTaskMapper;
import com.sankuai.meituan.waimai.customer.dao.sc.canteenstall.WmCanteenStallAuditTaskNodeMapper;
import com.sankuai.meituan.waimai.customer.dao.sc.canteenstall.WmCanteenStallBindAuditStreamMapper;
import com.sankuai.meituan.waimai.customer.dao.sc.canteenstall.WmCanteenStallBindMapper;
import com.sankuai.meituan.waimai.customer.domain.sc.canteenstall.*;
import com.sankuai.meituan.waimai.infra.domain.WmEmploy;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.canteenstall.*;
import com.sankuai.meituan.waimai.thrift.exception.WmSchCantException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

import static com.sankuai.meituan.waimai.thrift.customer.constant.sc.WmScCodeConstants.SERVER_ERROR;

/**
 * 食堂档口审批任务食堂系统相关Service
 * <AUTHOR>
 * @date 2024/05/25
 * @email <EMAIL>
 */
@Slf4j
@Service
public class WmCanteenStallAuditFlowCanteenService {

    @Autowired
    private WmScEmployAdaptor wmScEmployAdaptor;

    @Autowired
    private WmCanteenStallAuditTaskMapper wmCanteenStallAuditTaskMapper;

    @Autowired
    private WmCanteenStallAuditTaskNodeMapper wmCanteenStallAuditTaskNodeMapper;

    @Autowired
    private WmCanteenStallAuditFlowGravityService wmCanteenStallAuditFlowGravityService;

    @Autowired
    private WmCanteenStallBindMapper wmCanteenStallBindMapper;

    @Autowired
    private WmCanteenStallBindAuditStreamMapper wmCanteenStallBindAuditStreamMapper;


    /**
     * 创建档口审批流信息
     * @param taskDO taskDO
     * @param auditTaskBO auditTaskBO
     */
    public void createAuditStreamBySubmit(WmCanteenStallAuditTaskDO taskDO, WmCanteenStallAuditTaskBO auditTaskBO) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_2", "com.sankuai.meituan.waimai.customer.service.sc.canteenstall.auditflow.WmCanteenStallAuditFlowCanteenService.createAuditStreamBySubmit(WmCanteenStallAuditTaskDO,WmCanteenStallAuditTaskBO)");
        List<Integer> bindIdList = auditTaskBO.getBindIdList();
        for (Integer bindId : bindIdList) {
            WmCanteenStallBindAuditStreamDO auditStreamDO = new WmCanteenStallBindAuditStreamDO();
            auditStreamDO.setAuditTaskId(taskDO.getId());
            auditStreamDO.setBindId(bindId);
            auditStreamDO.setAuditStatus((int) CanteenStallAuditStatusEnum.AUDITING.getType());
            auditStreamDO.setCuid(auditTaskBO.getOpUserUid());
            auditStreamDO.setMuid(auditTaskBO.getOpUserUid());
            wmCanteenStallBindAuditStreamMapper.insertSelective(auditStreamDO);
        }
    }

    /**
     * 新增审批流信息(审批驳回)
     * @param auditTaskBO auditTaskBO
     */
    public void createAuditStreamByAuditReject(WmCanteenStallAuditTaskBO auditTaskBO) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_1", "com.sankuai.meituan.waimai.customer.service.sc.canteenstall.auditflow.WmCanteenStallAuditFlowCanteenService.createAuditStreamByAuditReject(WmCanteenStallAuditTaskBO)");
        WmCanteenStallAuditTaskDO taskDO = auditTaskBO.getTaskDO();
        List<WmCanteenStallBindAuditStreamDO> streamDOList = wmCanteenStallBindAuditStreamMapper.selectByAuditTaskIdWithAuditing(taskDO.getId());
        List<Integer> bindIdList = streamDOList.stream()
                .map(WmCanteenStallBindAuditStreamDO::getBindId)
                .collect(Collectors.toList());

        for (Integer bindId : bindIdList) {
            WmCanteenStallBindAuditStreamDO auditStreamDO = new WmCanteenStallBindAuditStreamDO();
            auditStreamDO.setAuditTaskId(taskDO.getId());
            auditStreamDO.setBindId(bindId);
            auditStreamDO.setAuditStatus((int) CanteenStallAuditStatusEnum.REJECT.getType());
            auditStreamDO.setCuid(auditTaskBO.getOpUserUid());
            auditStreamDO.setMuid(auditTaskBO.getOpUserUid());
            wmCanteenStallBindAuditStreamMapper.insertSelective(auditStreamDO);
        }
    }

    /**
     * 新增审批流信息(审批通过)
     * @param auditTaskBO auditTaskBO
     */
    public void createAuditStreamByAuditEffect(WmCanteenStallAuditTaskBO auditTaskBO) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.sc.canteenstall.auditflow.WmCanteenStallAuditFlowCanteenService.createAuditStreamByAuditEffect(WmCanteenStallAuditTaskBO)");
        WmCanteenStallAuditTaskDO taskDO = auditTaskBO.getTaskDO();
        List<WmCanteenStallBindAuditStreamDO> streamDOList = wmCanteenStallBindAuditStreamMapper.selectByAuditTaskIdWithAuditing(taskDO.getId());
        List<Integer> bindIdList = streamDOList.stream()
                .map(WmCanteenStallBindAuditStreamDO::getBindId)
                .collect(Collectors.toList());

        for (Integer bindId : bindIdList) {
            WmCanteenStallBindAuditStreamDO auditStreamDO = new WmCanteenStallBindAuditStreamDO();
            auditStreamDO.setAuditTaskId(taskDO.getId());
            auditStreamDO.setBindId(bindId);
            auditStreamDO.setAuditStatus((int) CanteenStallAuditStatusEnum.PASS.getType());
            auditStreamDO.setCuid(auditTaskBO.getOpUserUid());
            auditStreamDO.setMuid(auditTaskBO.getOpUserUid());
            wmCanteenStallBindAuditStreamMapper.insertSelective(auditStreamDO);
        }
    }

    /**
     * 审批任务主表创建主任务
     * @param auditTaskBO auditTaskBO
     * @return WmCanteenStallAuditTaskDO
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    public WmCanteenStallAuditTaskDO createAuditTaskBySubmit(WmCanteenStallAuditTaskBO auditTaskBO) throws WmSchCantException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_2", "com.sankuai.meituan.waimai.customer.service.sc.canteenstall.auditflow.WmCanteenStallAuditFlowCanteenService.createAuditTaskBySubmit(WmCanteenStallAuditTaskBO)");
        log.info("[WmCanteenStallAuditFlowCanteenService.createAuditTaskBySubmit] input param: auditTaskBO = {}", JSONObject.toJSONString(auditTaskBO));

        WmCanteenStallAuditTaskDO taskDO = new WmCanteenStallAuditTaskDO();
        taskDO.setCanteenPrimaryId(auditTaskBO.getCanteenPrimaryId());
        taskDO.setAuditStatus((int) CanteenStallAuditStatusEnum.AUDITING.getType());
        taskDO.setCuid(auditTaskBO.getAuditCreateUid().longValue());
        taskDO.setAuditTaskType(auditTaskBO.getAuditTaskType());
        taskDO.setAbnormalReason(auditTaskBO.getAbnormalReason());
        taskDO.setProofPicture(auditTaskBO.getProofPicture());

        WmEmploy employ = wmScEmployAdaptor.getWmEmployByUid(auditTaskBO.getAuditCreateUid());
        taskDO.setCmis(employ.getMisId());

        int result = wmCanteenStallAuditTaskMapper.insertSelective(taskDO);
        if (result <= 0 || taskDO.getId() == null) {
            log.error("[WmCanteenStallAuditFlowCanteenService.createAuditTaskBySubmit] insertSelective error. taskDO = {}", JSONObject.toJSONString(taskDO));
            throw new WmSchCantException(SERVER_ERROR, "新增审批任务失败");
        }
        log.info("[WmCanteenStallAuditFlowCanteenService.createAuditTaskBySubmit] taskDO = {}", JSONObject.toJSONString(taskDO));
        return taskDO;
    }

    /**
     * 更新审批任务主表中GravityId和审批节点
     * @param gravityId gravityId
     * @param taskDO taskDO
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    public void updateAuditTaskGravityIdAndAuditNode(String gravityId, WmCanteenStallAuditTaskDO taskDO) throws WmSchCantException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.service.sc.canteenstall.auditflow.WmCanteenStallAuditFlowCanteenService.updateAuditTaskGravityIdAndAuditNode(String,WmCanteenStallAuditTaskDO)");
        log.info("[WmCanteenStallAuditFlowCanteenService.updateAuditTaskGravityIdAndAuditNode] gravityId = {}, taskDO = {}", gravityId, JSONObject.toJSONString(taskDO));
        // 1-根据GravityId查询流程实例获取当前审批节点
        CanteenStallAuditNodeTypeEnum nodeTypeEnum = wmCanteenStallAuditFlowGravityService.getGravityAuditNodeByGravityId(gravityId);

        // 2-更新审批任务主表
        taskDO.setGravityId(gravityId);
        taskDO.setAuditNode(nodeTypeEnum.getType());
        wmCanteenStallAuditTaskMapper.updateByPrimaryKeySelective(taskDO);
        log.info("[WmCanteenStallAuditFlowCanteenService.updateAuditTaskGravityIdAndAuditNode] taskDO = {}", JSONObject.toJSONString(taskDO));
    }

    /**
     * 审批任务子表创建子任务
     * @param taskDO taskDO
     * @param auditorBO auditorBO
     * @return WmCanteenStallAuditTaskNodeDO
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    public WmCanteenStallAuditTaskNodeDO createAuditTaskNodeBySubmit(WmCanteenStallAuditTaskDO taskDO, WmCanteenStallAuditorBO auditorBO)
            throws WmSchCantException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_2", "com.sankuai.meituan.waimai.customer.service.sc.canteenstall.auditflow.WmCanteenStallAuditFlowCanteenService.createAuditTaskNodeBySubmit(WmCanteenStallAuditTaskDO,WmCanteenStallAuditorBO)");
        WmCanteenStallAuditTaskNodeDO taskNodeDO = new WmCanteenStallAuditTaskNodeDO();
        taskNodeDO.setAuditTaskId(taskDO.getId());
        taskNodeDO.setAuditNode(taskDO.getAuditNode());
        taskNodeDO.setAuditSystemType((int) CanteenStallAuditSystemTypeEnum.CRM_TICKET_SYSTEM.getType());
        taskNodeDO.setAuditorUid(auditorBO.getAuditorUid());
        taskNodeDO.setAuditorMis(auditorBO.getAuditorMis());
        taskNodeDO.setAuditorName(auditorBO.getAuditorName());
        taskNodeDO.setCuid(taskDO.getCuid());

        int result = wmCanteenStallAuditTaskNodeMapper.insertSelective(taskNodeDO);
        if (result == 0 || taskNodeDO.getId() == null) {
            log.error("[WmCanteenStallAuditFlowCanteenService.createAuditTaskNodeBySubmit] insertSelective error. taskNodeDO = {}", JSONObject.toJSONString(taskNodeDO));
            throw new WmSchCantException(SERVER_ERROR, "新增审批子任务失败");
        }
        log.info("[WmCanteenStallAuditFlowCanteenService.createAuditTaskNodeBySubmit] taskNodeDO = {}", JSONObject.toJSONString(taskNodeDO));
        return taskNodeDO;
    }

    /**
     * 更新审批任务子表中的任务系统ID
     * @param ticketId 任务系统ID(主任务ID)
     * @param taskNodeDO taskNodeDO
     */
    public void updateAuditTaskNodeAuditSystemId(Integer ticketId, WmCanteenStallAuditTaskNodeDO taskNodeDO) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.sc.canteenstall.auditflow.WmCanteenStallAuditFlowCanteenService.updateAuditTaskNodeAuditSystemId(Integer,WmCanteenStallAuditTaskNodeDO)");
        log.info("[WmCanteenStallAuditFlowCanteenService.updateAuditTaskNodeAuditSystemId] ticketId = {}, taskNodeDO = {}", ticketId, JSONObject.toJSONString(taskNodeDO));
        taskNodeDO.setAuditSystemId(String.valueOf(ticketId));
        wmCanteenStallAuditTaskNodeMapper.updateByPrimaryKeySelective(taskNodeDO);
        log.info("[WmCanteenStallAuditFlowCanteenService.updateAuditTaskNodeAuditSystemId] update success. taskNodeDO = {}", JSONObject.toJSONString(taskNodeDO));
    }

    /**
     * 更新食堂档口绑定任务审批状态(审批中)
     * @param bindIdList 档口绑定任务ID列表
     */
    public void updateCanteenStallBindAuditStatusBySubmit(List<Integer> bindIdList) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.service.sc.canteenstall.auditflow.WmCanteenStallAuditFlowCanteenService.updateCanteenStallBindAuditStatusBySubmit(java.util.List)");
        log.info("[WmCanteenStallAuditFlowCanteenService.updateCanteenStallBindAuditStatusBySubmit] bindIdList = {}", JSONObject.toJSONString(bindIdList));
        if (CollectionUtils.isEmpty(bindIdList)) {
            log.error("[WmCanteenStallAuditFlowCanteenService.updateCanteenStallBindAuditStatusBySubmit] bindIdList is empty, return");
            return;
        }
        wmCanteenStallBindMapper.updateAuditStatusByPrimaryIdList((int) CanteenStallAuditStatusEnum.AUDITING.getType(), bindIdList);
    }


    /**
     * 更新食堂档口绑定任务审批状态(审批驳回)
     * @param auditTaskBO auditTaskBO
     */
    public void updateCanteenStallBindAuditStatusByAuditReject(WmCanteenStallAuditTaskBO auditTaskBO) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.service.sc.canteenstall.auditflow.WmCanteenStallAuditFlowCanteenService.updateCanteenStallBindAuditStatusByAuditReject(WmCanteenStallAuditTaskBO)");
        WmCanteenStallAuditTaskDO taskDO = auditTaskBO.getTaskDO();
        List<WmCanteenStallBindAuditStreamDO> auditStreamDOList = wmCanteenStallBindAuditStreamMapper.selectByAuditTaskIdWithAuditing(taskDO.getId());
        List<Integer> bindIdList = auditStreamDOList.stream()
                .map(WmCanteenStallBindAuditStreamDO::getBindId)
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(bindIdList)) {
            log.error("[WmCanteenStallAuditFlowCanteenService.updateCanteenStallBindAuditStatusByAuditReject] bindIdList is empty. auditTaskBO = {}",
                    JSONObject.toJSONString(auditTaskBO));
            return;
        }
        log.info("[WmCanteenStallAuditFlowCanteenService.updateCanteenStallBindAuditStatusByAuditReject] bindIdList = {}", JSONObject.toJSONString(bindIdList));
        wmCanteenStallBindMapper.updateAuditStatusByPrimaryIdList((int) CanteenStallAuditStatusEnum.REJECT.getType(), bindIdList);
    }

    /**
     * 更新食堂档口绑定任务审批状态(审批通过)
     * @param auditTaskBO auditTaskBO
     */
    public void updateCanteenStallBindAuditStatusByAuditEffect(WmCanteenStallAuditTaskBO auditTaskBO) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.service.sc.canteenstall.auditflow.WmCanteenStallAuditFlowCanteenService.updateCanteenStallBindAuditStatusByAuditEffect(WmCanteenStallAuditTaskBO)");
        WmCanteenStallAuditTaskDO taskDO = auditTaskBO.getTaskDO();
        // 1-查询审批的档口绑定任务ID
        List<WmCanteenStallBindAuditStreamDO> auditStreamDOList = wmCanteenStallBindAuditStreamMapper.selectByAuditTaskIdWithAuditing(taskDO.getId());
        List<Integer> bindIdList = auditStreamDOList.stream()
                .map(WmCanteenStallBindAuditStreamDO::getBindId)
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(bindIdList)) {
            log.error("[WmCanteenStallAuditFlowCanteenService.updateCanteenStallBindAuditStatusByAuditEffect] bindIdList is empty. auditTaskBO = {}",
                    JSONObject.toJSONString(auditTaskBO));
            return;
        }

        // 2-将审批状态更新为"审批通过"
        log.info("[WmCanteenStallAuditFlowCanteenService.updateCanteenStallBindAuditStatusByAuditEffect] bindIdList = {}", JSONObject.toJSONString(bindIdList));
        wmCanteenStallBindMapper.updateAuditStatusByPrimaryIdList((int) CanteenStallAuditStatusEnum.PASS.getType(), bindIdList);
    }

    /**
     * 更新档口绑定任务线索跟进状态
     * @param auditTaskBO auditTaskBO
     */
    public void updateCanteenStallBindClueFollowUpStatus(WmCanteenStallAuditTaskBO auditTaskBO) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_1", "com.sankuai.meituan.waimai.customer.service.sc.canteenstall.auditflow.WmCanteenStallAuditFlowCanteenService.updateCanteenStallBindClueFollowUpStatus(WmCanteenStallAuditTaskBO)");
        WmCanteenStallAuditTaskDO taskDO = auditTaskBO.getTaskDO();
        // 1-查询审批的档口绑定任务ID
        List<WmCanteenStallBindAuditStreamDO> auditStreamDOList = wmCanteenStallBindAuditStreamMapper.selectByAuditTaskIdWithAuditing(taskDO.getId());
        List<Integer> bindIdList = auditStreamDOList.stream()
                .map(WmCanteenStallBindAuditStreamDO::getBindId)
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(bindIdList)) {
            log.error("[WmCanteenStallAuditFlowCanteenService.updateCanteenStallBindClueFollowUpStatus] bindIdList is empty. auditTaskBO = {}",
                    JSONObject.toJSONString(auditTaskBO));
            return;
        }

        // 2-更新档口绑定任务线索跟进状态
        Integer followUpStatus = CanteenStallAuditTaskTypeEnum.getByType(taskDO.getAuditTaskType()).getTargetStatus();
        log.info("[WmCanteenStallAuditFlowCanteenService.updateCanteenStallBindClueFollowUpStatus] bindIdList = {}, followUpStatus = {}",
                JSONObject.toJSONString(bindIdList), followUpStatus);
        wmCanteenStallBindMapper.updateClueFollowUpStatusByPrimaryIdList(followUpStatus, bindIdList);
    }

    /**
     * 审批任务主表更新任务状态为已驳回
     * @param auditTaskBO auditTaskBO
     */
    public void updateAuditTaskAuditStatusByAuditReject(WmCanteenStallAuditTaskBO auditTaskBO) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.service.sc.canteenstall.auditflow.WmCanteenStallAuditFlowCanteenService.updateAuditTaskAuditStatusByAuditReject(WmCanteenStallAuditTaskBO)");
        WmCanteenStallAuditTaskDO taskDO = auditTaskBO.getTaskDO();
        taskDO.setAuditStatus((int) CanteenStallAuditStatusEnum.REJECT.getType());
        wmCanteenStallAuditTaskMapper.updateByPrimaryKeySelective(taskDO);
        log.info("[WmCanteenStallAuditFlowCanteenService.updateAuditTaskAuditResultByAuditReject] taskDO = {}", JSONObject.toJSONString(taskDO));
    }

    /**
     * 审批任务主表更新任务状态为已终止
     * @param auditTaskBO auditTaskBO
     */
    public void updateAuditTaskAuditStatusByAuditStop(WmCanteenStallAuditTaskBO auditTaskBO) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.sc.canteenstall.auditflow.WmCanteenStallAuditFlowCanteenService.updateAuditTaskAuditStatusByAuditStop(WmCanteenStallAuditTaskBO)");
        WmCanteenStallAuditTaskDO taskDO = auditTaskBO.getTaskDO();
        taskDO.setAuditStatus((int) CanteenStallAuditStatusEnum.REJECT.getType());
        wmCanteenStallAuditTaskMapper.updateByPrimaryKeySelective(taskDO);
        log.info("[WmCanteenStallAuditFlowCanteenService.updateAuditTaskAuditStatusByAuditStop] taskDO = {}", JSONObject.toJSONString(taskDO));
    }

    /**
     * 审批任务主表更新任务状态为已通过
     * @param auditTaskBO auditTaskBO
     */
    public void updateAuditTaskAuditStatusByAuditEffect(WmCanteenStallAuditTaskBO auditTaskBO) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.service.sc.canteenstall.auditflow.WmCanteenStallAuditFlowCanteenService.updateAuditTaskAuditStatusByAuditEffect(WmCanteenStallAuditTaskBO)");
        WmCanteenStallAuditTaskDO taskDO = auditTaskBO.getTaskDO();
        taskDO.setAuditStatus((int) CanteenStallAuditStatusEnum.PASS.getType());
        wmCanteenStallAuditTaskMapper.updateByPrimaryKeySelective(taskDO);
        log.info("[WmCanteenStallAuditFlowCanteenService.updateAuditTaskAuditResultByAuditEffect] taskDO = {}", JSONObject.toJSONString(taskDO));
    }

    /**
     * 审批任务子表更新任务状态为已驳回
     * @param auditTaskBO auditTaskBO
     * @param ticketDto ticketDto 任务系统DTO
     */
    public void updateAuditTaskNodeByAuditReject(WmCanteenStallAuditTaskBO auditTaskBO, WmTicketDto ticketDto) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_1", "com.sankuai.meituan.waimai.customer.service.sc.canteenstall.auditflow.WmCanteenStallAuditFlowCanteenService.updateAuditTaskNodeByAuditReject(WmCanteenStallAuditTaskBO,WmTicketDto)");
        WmCanteenStallAuditTaskNodeDO taskNodeDO = auditTaskBO.getTaskNodeDO();
        taskNodeDO.setAuditResult((int) CanteenStallAuditResultEnum.AUDIT_REJECT.getType());
        taskNodeDO.setAuditRemark(ticketDto.getRemark());
        taskNodeDO.setMuid((long) ticketDto.getOpUid());
        taskNodeDO.setAuditTime(ticketDto.getUnixUtime());
        wmCanteenStallAuditTaskNodeMapper.updateByPrimaryKeySelective(taskNodeDO);
        log.info("[WmCanteenStallAuditFlowCanteenService.updateAuditTaskNodeByAuditReject] taskNodeDO = {}", JSONObject.toJSONString(taskNodeDO));
    }

    /**
     * 审批任务子表更新任务状态为已终止
     * @param auditTaskBO auditTaskBO
     * @param ticketDto ticketDto 任务系统DTO
     */
    public void updateAuditTaskNodeByAuditStop(WmCanteenStallAuditTaskBO auditTaskBO, WmTicketDto ticketDto) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.service.sc.canteenstall.auditflow.WmCanteenStallAuditFlowCanteenService.updateAuditTaskNodeByAuditStop(WmCanteenStallAuditTaskBO,WmTicketDto)");
        WmCanteenStallAuditTaskNodeDO taskNodeDO = auditTaskBO.getTaskNodeDO();
        taskNodeDO.setAuditResult((int) CanteenStallAuditResultEnum.AUDIT_END.getType());
        taskNodeDO.setAuditRemark(ticketDto.getRemark());
        taskNodeDO.setMuid((long) ticketDto.getOpUid());
        taskNodeDO.setAuditTime(ticketDto.getUnixUtime());
        wmCanteenStallAuditTaskNodeMapper.updateByPrimaryKeySelective(taskNodeDO);
        log.info("[WmCanteenStallAuditFlowCanteenService.updateAuditTaskNodeByAuditStop] taskNodeDO = {}", JSONObject.toJSONString(taskNodeDO));
    }

    /**
     * 审批任务子表更新任务状态为已通过
     * @param auditTaskBO auditTaskBO
     * @param ticketDto ticketDto 任务系统DTO
     */
    public void updateAuditTaskNodeByAuditPass(WmCanteenStallAuditTaskBO auditTaskBO, WmTicketDto ticketDto) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.sc.canteenstall.auditflow.WmCanteenStallAuditFlowCanteenService.updateAuditTaskNodeByAuditPass(WmCanteenStallAuditTaskBO,WmTicketDto)");
        WmCanteenStallAuditTaskNodeDO taskNodeDO = auditTaskBO.getTaskNodeDO();
        taskNodeDO.setAuditResult((int) CanteenStallAuditResultEnum.AUDIT_PASS.getType());
        taskNodeDO.setAuditRemark(ticketDto.getRemark());
        taskNodeDO.setMuid((long) ticketDto.getOpUid());
        taskNodeDO.setAuditTime(ticketDto.getUnixUtime());
        wmCanteenStallAuditTaskNodeMapper.updateByPrimaryKeySelective(taskNodeDO);
        log.info("[WmCanteenStallAuditFlowCanteenService.updateAuditTaskNodeByAuditPass] taskNodeDO = {}", JSONObject.toJSONString(taskNodeDO));
    }

    /**
     * 审批任务主表更新审批节点
     * @param auditTaskBO auditTaskBO
     * @param currentAuditNode 当前审批节点
     */
    public void updateAuditTaskAuditNode(WmCanteenStallAuditTaskBO auditTaskBO, Integer currentAuditNode) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.service.sc.canteenstall.auditflow.WmCanteenStallAuditFlowCanteenService.updateAuditTaskAuditNode(WmCanteenStallAuditTaskBO,Integer)");
        WmCanteenStallAuditTaskDO taskDO = auditTaskBO.getTaskDO();
        taskDO.setAuditNode(currentAuditNode);
        wmCanteenStallAuditTaskMapper.updateByPrimaryKeySelective(taskDO);
        log.info("[WmCanteenStallAuditFlowCanteenService.updateAuditTaskAuditNode] taskDO = {}", JSONObject.toJSONString(taskDO));
    }


}
