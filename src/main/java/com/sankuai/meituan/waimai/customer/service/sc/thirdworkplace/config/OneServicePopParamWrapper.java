package com.sankuai.meituan.waimai.customer.service.sc.thirdworkplace.config;


import com.sankuai.djdata.readata.openapi.enums.PopRateType;
import com.sankuai.djdata.readata.openapi.model.PopParam;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

@AllArgsConstructor
@NoArgsConstructor
@Data
public class OneServicePopParamWrapper {
    private CustomerPopParam diffRateInfo;

    private CustomerPopParam radioRateInfo;

    private List<String> noNeedJoinDims;

    public PopParam buildDiffPopParam(){
        if (diffRateInfo == null) {
            return null;
        }
        PopParam.PopParamBuilder popParamBuilder = PopParam.newBuilder();
        popParamBuilder.setPopRateType(PopRateType.RISE_PP_VALUE);
        popParamBuilder.dateDimensionKey(diffRateInfo.getDim());
        popParamBuilder.offset(diffRateInfo.getOffset());
        if (CollectionUtils.isEmpty(radioRateInfo.getReteIndexInfo())) {
            return null;
        }
        for (CustomerPopParam.ReteIndexInfo reteIndexInfo : diffRateInfo.getReteIndexInfo()) {
            popParamBuilder.addPopIndexInfo(reteIndexInfo.getIndex(), reteIndexInfo.getDerivedIndexName(), reteIndexInfo.getDerivedIndexId());
        }
        return popParamBuilder.build();
    }

    public PopParam buildRadioPopParam(){
        if (radioRateInfo == null) {
            return null;
        }
        PopParam.PopParamBuilder popParamBuilder = PopParam.newBuilder();
        popParamBuilder.setPopRateType(PopRateType.RISE_RATIO);
        popParamBuilder.dateDimensionKey(radioRateInfo.getDim());
        popParamBuilder.offset(radioRateInfo.getOffset());
        if (CollectionUtils.isEmpty(radioRateInfo.getReteIndexInfo())) {
            return null;
        }
        for (CustomerPopParam.ReteIndexInfo reteIndexInfo : radioRateInfo.getReteIndexInfo()) {
            popParamBuilder.addPopIndexInfo(reteIndexInfo.getIndex(), reteIndexInfo.getDerivedIndexName(), reteIndexInfo.getDerivedIndexId());
        }
        return popParamBuilder.build();
    }
}
