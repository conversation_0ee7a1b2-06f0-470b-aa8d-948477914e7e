package com.sankuai.meituan.waimai.customer.service.customer;

import com.google.common.collect.Lists;
import com.sankuai.meituan.waimai.customer.constant.customer.CustomerPoiUnBindTypeEnum;
import com.sankuai.meituan.waimai.customer.constant.customer.WmCustomerPoiOplogSourceTypeEnum;
import com.sankuai.meituan.waimai.customer.constant.customer.WmCustomerPoiSwitchOperateTypeEnum;
import com.sankuai.meituan.waimai.customer.dao.WmCustomerPoiSmsRecordMapper;
import com.sankuai.meituan.waimai.customer.ddd.domain.customer.aggre.WmCustomerPoiAggre;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerPoiSmsRecordDB;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerPoiSwitchBO;
import com.sankuai.meituan.waimai.customer.domain.poi.WmCustomerPoiUnbindConfirmBo;
import com.sankuai.meituan.waimai.customer.service.customer.poi.CustomerPoiBindService;
import com.sankuai.meituan.waimai.customer.service.customer.poi.CustomerPoiUnBindService;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerConstants;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractTaskStateEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.customer.WmCustomerSmsTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.customer.task.CustomerTaskStatusEnum;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import lombok.extern.slf4j.Slf4j;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Set;

@Service
@Slf4j
public class WmCustomerRecoveryToolService {

    @Autowired
    private WmCustomerPoiSmsRecordMapper wmCustomerPoiSmsRecordMapper;

    @Autowired
    private WmCustomerSwitchService wmCustomerSwitchService;

    @Autowired
    private CustomerPoiBindService customerPoiBindService;

    @Autowired
    private CustomerPoiUnBindService customerPoiUnBindService;

    /**
     * 恢复客户门店确认绑定(调用前提：电子签约系统处理完成，但客户系统门店绑定短信取消/确认失败，客户任务列表找不到短信任务)
     *
     * @param taskId     短信任务ID：wm_customer_poi_sms_record的taskId
     * @param taskStatus 短信任务处理状态：com.sankuai.meituan.waimai.thrift.customer.constant.EcontractTaskStateEnum
     * @throws TException
     * @throws WmCustomerException
     */
    public void recoverConfirmBind(long taskId, int taskStatus) throws TException, WmCustomerException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.customer.WmCustomerRecoveryToolService.recoverConfirmBind(long,int)");
        log.info("recoverConfirmBind taskId={},taskStatus={}", taskId, taskStatus);
        // 获取指定短信任务
        WmCustomerPoiSmsRecordDB wmCustomerPoiSmsRecordDB = wmCustomerPoiSmsRecordMapper.selectSmsRecordByTaskId(taskId);
        if (wmCustomerPoiSmsRecordDB == null || wmCustomerPoiSmsRecordDB.getType() != WmCustomerSmsTypeEnum.NEW_CUSTOMER_BIND.getCode()) {
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "未找到对应的门店绑定短信任务");
        }
        if (taskStatus != EcontractTaskStateEnum.SUCCESS.getType() && taskStatus != EcontractTaskStateEnum.CANCEL.getType()) {
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "门店绑定短信任务状态错误");
        }
        int status = wmCustomerPoiSmsRecordDB.getTaskStatus();
        int customerId = wmCustomerPoiSmsRecordDB.getCustomerId();
        List<Integer> doingStatusList = Lists.newArrayList(EcontractTaskStateEnum.TO_COMMIT.getType(),
                EcontractTaskStateEnum.HOLDING.getType(),
                EcontractTaskStateEnum.IN_PROCESSING.getType());
        Set<Long> wmPoiIdSet = WmCustomerPoiAggre.Factory.make().convertWmPoiIds(wmCustomerPoiSmsRecordDB.getWmPoiIds());
        // 如果短信未达到终态更新短信
        if (doingStatusList.contains(status)) {
            wmCustomerPoiSmsRecordMapper.updateSmsRecordByTaskStatus(taskStatus, taskId);
        }
        // 基于场景分析短信签约任务来源
        Long switchTaskId = wmCustomerSwitchService.checkFromSwitchBind(wmCustomerPoiSmsRecordDB);
        if (switchTaskId != null) {
            recoverConfirmBindForSwitch(switchTaskId, taskStatus, customerId, wmPoiIdSet);
        } else {
            recoverConfirmBindForPoi(wmCustomerPoiSmsRecordDB.getId(), taskStatus, customerId, wmPoiIdSet);
        }
    }

    /**
     * 恢复客户门店确认解绑（调用前提：电子签约系统处理完成，但门店解绑商家短信取消/确认失败，客户任务列表找不到短信任务）
     *
     * @param taskId     短信任务ID：wm_customer_poi_sms_record的taskId
     * @param taskStatus 短信任务处理状态：com.sankuai.meituan.waimai.thrift.customer.constant.EcontractTaskStateEnum
     * @throws TException
     * @throws WmCustomerException
     */
    public void recoverConfirmUnBind(long taskId, int taskStatus) throws TException, WmCustomerException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.service.customer.WmCustomerRecoveryToolService.recoverConfirmUnBind(long,int)");
        log.info("recoverConfirmUnBind taskId={},taskStatus={}", taskId, taskStatus);
        // 获取指定短信任务
        WmCustomerPoiSmsRecordDB wmCustomerPoiSmsRecordDB = wmCustomerPoiSmsRecordMapper.selectSmsRecordByTaskId(taskId);
        if (wmCustomerPoiSmsRecordDB == null || wmCustomerPoiSmsRecordDB.getType() != WmCustomerSmsTypeEnum.OLD_CUSTOMER_UNBIND.getCode()) {
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "未找到对应的门店解绑短信任务");
        }
        if (taskStatus != EcontractTaskStateEnum.SUCCESS.getType() && taskStatus != EcontractTaskStateEnum.CANCEL.getType()) {
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "门店解绑短信任务状态错误");
        }
        int status = wmCustomerPoiSmsRecordDB.getTaskStatus();
        int customerId = wmCustomerPoiSmsRecordDB.getCustomerId();
        List<Integer> doingStatusList = Lists.newArrayList(EcontractTaskStateEnum.TO_COMMIT.getType(),
                EcontractTaskStateEnum.HOLDING.getType(),
                EcontractTaskStateEnum.IN_PROCESSING.getType());
        Set<Long> wmPoiIdSet = WmCustomerPoiAggre.Factory.make().convertWmPoiIds(wmCustomerPoiSmsRecordDB.getWmPoiIds());
        // 如果短信未达到终态更新短信
        if (doingStatusList.contains(status)) {
            wmCustomerPoiSmsRecordMapper.updateSmsRecordByTaskStatus(taskStatus, taskId);
        }
        // 基于场景分析短信签约任务来源
        Long switchTaskId = wmCustomerSwitchService.checkFromSwitchUnBind(wmCustomerPoiSmsRecordDB);
        if (switchTaskId != null) {
            recoverConfirmUnBindForSwitch(switchTaskId, taskStatus, customerId, wmPoiIdSet);
        } else {
            recoverConfirmUnBindForPoi(wmCustomerPoiSmsRecordDB.getId(), taskStatus, customerId, wmPoiIdSet);
        }
    }

    /**
     * 恢复客户切换门店绑定短信确认结果
     * @param switchTaskId
     * @param taskStatus
     * @param customerId
     * @param wmPoiIdSet
     * @throws WmCustomerException
     */
    private void recoverConfirmBindForSwitch(long switchTaskId, int taskStatus, int customerId, Set<Long> wmPoiIdSet) throws WmCustomerException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.customer.WmCustomerRecoveryToolService.recoverConfirmBindForSwitch(long,int,int,java.util.Set)");
        if (taskStatus == EcontractTaskStateEnum.SUCCESS.getType()) {
            // 处理客户门店关系
            wmCustomerSwitchService.bind(new WmCustomerPoiSwitchBO(switchTaskId, customerId, wmPoiIdSet, 0, "系统",
                    WmCustomerPoiSwitchOperateTypeEnum.BIND_SMS_CONFIRM));
        } else {
            wmCustomerSwitchService.bind(new WmCustomerPoiSwitchBO(switchTaskId, customerId, wmPoiIdSet, 0, "系统",
                    WmCustomerPoiSwitchOperateTypeEnum.BIND_TASK_CANCEL));
        }
    }

    /**
     * 恢复门店绑定短信确认结果
     * @param smsId
     * @param taskStatus
     * @param customerId
     * @param wmPoiIdSet
     * @throws WmCustomerException
     */
    private void recoverConfirmBindForPoi(int smsId, int taskStatus, int customerId, Set<Long> wmPoiIdSet) throws WmCustomerException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.customer.WmCustomerRecoveryToolService.recoverConfirmBindForPoi(int,int,int,java.util.Set)");
        if (taskStatus == EcontractTaskStateEnum.SUCCESS.getType()) {
            customerPoiBindService.confirmBind(customerId, wmPoiIdSet, smsId);
        } else {
            customerPoiBindService.cancelBind(customerId, wmPoiIdSet, CustomerTaskStatusEnum.CANCEL);
        }
    }

    /**
     * 恢复客户切换门店解绑短信确认结果
     * @param switchTaskId
     * @param taskStatus
     * @param customerId
     * @param wmPoiIdSet
     * @throws WmCustomerException
     */
    private void recoverConfirmUnBindForSwitch(long switchTaskId, int taskStatus, int customerId, Set<Long> wmPoiIdSet) throws WmCustomerException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.service.customer.WmCustomerRecoveryToolService.recoverConfirmUnBindForSwitch(long,int,int,java.util.Set)");
        if (taskStatus == EcontractTaskStateEnum.SUCCESS.getType()) {
            wmCustomerSwitchService.unBind(new WmCustomerPoiSwitchBO(switchTaskId, customerId, wmPoiIdSet, 0, "系统",
                    WmCustomerPoiSwitchOperateTypeEnum.UNBIND_TASK_FORCE));
        } else {
            wmCustomerSwitchService.unBind(new WmCustomerPoiSwitchBO(switchTaskId, customerId, wmPoiIdSet, 0, "系统",
                    WmCustomerPoiSwitchOperateTypeEnum.UNBIND_TASK_CANCEL));
        }
    }

    /**
     * 恢复客户切换门店解绑短信确认结果
     * @param smsId
     * @param taskStatus
     * @param customerId
     * @param wmPoiIdSet
     * @throws TException
     * @throws WmCustomerException
     */
    private void recoverConfirmUnBindForPoi(int smsId, int taskStatus, int customerId, Set<Long> wmPoiIdSet) throws TException, WmCustomerException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.customer.WmCustomerRecoveryToolService.recoverConfirmUnBindForPoi(int,int,int,java.util.Set)");
        if (taskStatus == EcontractTaskStateEnum.SUCCESS.getType()) {
            customerPoiUnBindService.confirmUnBind(new WmCustomerPoiUnbindConfirmBo(customerId, wmPoiIdSet,
                    0, "系统", CustomerPoiUnBindTypeEnum.BD_FORCE_UNBIND,
                    WmCustomerPoiOplogSourceTypeEnum.FORCE_UNBIND, smsId));
        } else {
            customerPoiUnBindService.cancelUnBind(new WmCustomerPoiUnbindConfirmBo(customerId, wmPoiIdSet,
                    0, "系统", CustomerPoiUnBindTypeEnum.BD_CANCEL_UNBIND,
                    null, smsId));
        }
    }
}
