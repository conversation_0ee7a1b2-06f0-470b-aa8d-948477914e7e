package com.sankuai.meituan.waimai.customer.service.sc.thirdworkplace;

import lombok.Data;

@Data
public class WmScThirdWorkplaceQueryMetrics {

    /**
     * 学校数量
     */
    private String schoolNum;

    /**
     * 食堂数量
     */
    private String canteenNum;

    /**
     * 线索合伙人数量
     */
    private String cluePartnerNum;

    /**
     * 线索合伙人环比
     */
    private String cluePartnerNumMom;

    /**
     * 意向合伙人数量
     */
    private String intentionPartnerNum;

    /**
     * 意向合伙人环比
     */
    private String intentionPartnerNumMom;

    /**
     * 委托合伙人数量
     */
    private String entrustPartnerNum;

    /**
     * 委托合伙人环比
     */
    private String entrustPartnerNumMom;

    /**
     * 签约合伙人数量
     */
    private String signPartnerNum;

    /**
     * 签约合伙人环比
     */
    private String signPartnerNumMom;

    /**
     * 档口数量
     */
    private String stallNum;

    /**
     * 档口数量环比
     */
    private String stallNumMom;

    /**
     * 食堂线下营业档口数
     */
    private String cafeteriaOfflineOpenStallNum;

    /**
     * 食堂线下营业档口数环比
     */
    private String cafeteriaOfflineOpenStallNumMom;

    /**
     * 食堂可上线营业档口数
     */
    private String canteenPreOnlineStallNum;

    /**
     * 食堂可上线营业档口数环比
     */
    private String canteenPreOnlineStallNumMom;

    /**
     * 在线档口数
     */
    private String onlinePoiNum;

    /**
     * 在线档口数环比
     */
    private String onlinePoiNumMom;

    /**
     * 营业档口数
     */
    private String openPoiNum;

    /**
     * 营业档口数环比
     */
    private String openPoiNumMom;

    /**
     * 新增档口数
     */
    private String newPoiNum;

    /**
     * 新增档口数环比
     */
    private String newPoiNumMom;

    /**
     * 在线档口渗透率
     */
    private String cafeteriaOnlineStallInfiltrationRate;

    /**
     * 在线档口渗透率环比
     */
    private String cafeteriaOnlineStallInfiltrationRateMom;

    /**
     * 营业档口渗透率
     */
    private String cafeteriaOpenStallInfiltrationRate;

    /**
     * 营业档口渗透率环比
     */
    private String cafeteriaOpenStallInfiltrationRateMom;

    /**
     * 交易档口渗透率
     */
    private String cafeteriaTxnStallInfiltrationRate;

    /**
     * 交易档口渗透率环比
     */
    private String cafeteriaTxnStallInfiltrationRateMom;

    /**
     * 档口动销率
     */
    private String txnrate;

    /**
     * 交易档口数
     */
    private String txnPoiNum;

    /**
     * 交易档口数环比
     */
    private String txnPoiNumMom;

    /**
     * 档口动销率环比
     */
    private String txnrateMom;

    /**
     * 实付交易额
     */
    private String finActualAmt;

    /**
     * 实付交易额环比
     */
    private String finActualAmtMom;

    /**
     * 实付ARPU
     */
    private String actualArpu;

    /**
     * 实付ARPU环比
     */
    private String actualArpuMom;

    /**
     * 消费单均价-客单价
     */
    private String ordAvgFinActualAmt;

    /**
     * 消费单均价环比
     */
    private String ordAvgFinActualAmtMom;

    /**
     * 消费订单量
     */
    private String finOrdNum;

    /**
     * 消费订单量环比
     */
    private String finOrdNumMom;

    /**
     * 交易用户数
     */
    private String finUsrNum;

    /**
     * 交易用户数环比
     */
    private String finUsrNumMom;

    /**
     * 人均交易频次
     */
    private String usrAvgFinOrdNum;

    /**
     * 人均交易频次环比
     */
    private String usrAvgFinOrdNumMom;

    /**
     * 档口平均结算订单数-单产
     */
    private String poiAvgSettleOrdNum;

    /**
     * 档口平均结算订单数环比
     */
    private String poiAvgSettleOrdNumMom;

    /**
     * 食堂新增用户数
     */
    private String cafeteriaNewUserNum;

    /**
     * 食堂新增用户数环比
     */
    private String cafeteriaNewUserNumMom;

    /**
     * 在校师生数
     */
    private String teaStuNum;

    /**
     * 在校师生数环比
     */
    private String teaStuNumMom;
}