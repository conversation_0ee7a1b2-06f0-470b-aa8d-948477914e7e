package com.sankuai.meituan.waimai.customer.service.customer;

import com.sankuai.meituan.waimai.customer.dao.WmCustomerAuditDBMapper;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerAuditDB;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerDB;
import com.sankuai.meituan.waimai.customer.util.trans.WmCustomerTransUtil;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerConstants;
import com.sankuai.meituan.waimai.thrift.customer.constant.customer.CertificateOverdueEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.customer.CertificateStatusEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.customer.CertificateTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.customer.LegalPersonChangeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.WmCustomerBasicBo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;


/**
 * <AUTHOR>
 * @date 20240229
 * @desc 客户审核服务
 */
@Slf4j
@Service
public class WmCustomerAuditService {

    @Autowired
    private WmCustomerAuditDBMapper wmCustomerAuditDBMapper;


    /**
     * 创建客户提审记录表
     *
     * @param wmCustomerBasicBo
     * @param opUid
     * @param auditStatus
     * @return
     */
    public int commitCustomerAudit(WmCustomerBasicBo wmCustomerBasicBo, Integer opUid, Integer auditStatus) {
        WmCustomerAuditDB wmCustomerAuditDB = WmCustomerTransUtil.customerBoToAuditDB(wmCustomerBasicBo);
        wmCustomerAuditDB.setOwnerUid(wmCustomerBasicBo.getOwnerUid());
        wmCustomerAuditDB.setAuditStatus(auditStatus);
        wmCustomerAuditDB.setEffective(wmCustomerBasicBo.getEffective());
        wmCustomerAuditDB.setValid(CustomerConstants.VALID);
        wmCustomerAuditDB.setBatchSubmit(CustomerConstants.BATCH_SUBMIT_NO);
        wmCustomerAuditDB.setOpUid(opUid);
        wmCustomerAuditDBMapper.insertCustomerAudit(wmCustomerAuditDB);

        return wmCustomerAuditDB.getId();

    }

    /**
     * 将提审记录更新为特批中并同步特批任务ID
     *
     * @param id
     * @param applicationId
     */
    public void update2SpecialAuditingById(Integer id, Long applicationId) {
        log.info("update2SpecialAuditingById,将提审记录修改为特批中，id={},applicationId={}", id, applicationId);
        wmCustomerAuditDBMapper.update2SpecialAuditingById(applicationId, id);
    }

    /**
     * 根据关联任务ID查询特批中有效提审记录
     *
     * @param bizTaskId
     * @return
     */
    public List<WmCustomerAuditDB> getSpecialAuditingByBizTaskId(Integer bizTaskId) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.service.customer.WmCustomerAuditService.getSpecialAuditingByBizTaskId(java.lang.Integer)");
        return wmCustomerAuditDBMapper.getSpecialAuditingByBizTaskId(bizTaskId);
    }

    /**
     * 将客户下有效驳回记录更新为无效
     *
     * @param customerId
     */
    public void updateCustomerAudit2InValid(Integer customerId) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.customer.WmCustomerAuditService.updateCustomerAudit2InValid(java.lang.Integer)");
        wmCustomerAuditDBMapper.invalidCustomerAudit(customerId);
    }
}
