package com.sankuai.meituan.waimai.customer.service.customer.monitor.customerList;

import com.dianping.frog.sdk.alarm.DXUtil;
import com.dianping.frog.sdk.data.BinlogRawData;
import com.dianping.frog.sdk.data.RawData;
import com.dianping.frog.sdk.rpc.RpcService;
import com.dianping.frog.sdk.rpc.RpcServiceFactory;
import com.dianping.frog.sdk.runner.DefaultRuleRunner;
import com.dianping.frog.sdk.util.BcpLoggerUtils;
import com.dianping.lion.client.util.JsonUtils;
import com.dianping.lion.client.util.StringUtils;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

import java.util.Arrays;
import java.util.Map;

/**
 * bcp一致性校验脚本-校验「客户关联门店数量」
 *
 * <AUTHOR> hou
 * @since 2023.03.03
 * @email <EMAIL>
 */
public class WmCustomerListRuleRunner extends DefaultRuleRunner {
    @Override
    public String check(RawData rawData, RawData... rawData2) throws Exception {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_1", "com.sankuai.meituan.waimai.customer.service.customer.monitor.customerList.WmCustomerListRuleRunner.check(com.dianping.frog.sdk.data.RawData,com.dianping.frog.sdk.data.RawData[])");
        BinlogRawData binlogRawData = (BinlogRawData) rawData;
        try {
            RpcService rpcService = RpcServiceFactory.getThriftInstance("com.sankuai.meituan.waimai.thrift.customer.service.WmCustomerThriftService",
                    "com.sankuai.waimai.e.customer", 10000, null, "8433");
            Map<String, Object> params = Maps.newHashMap();
            Integer customerId = Integer.valueOf(binlogRawData.getColumnInfoMap().get("customer_id").getNewValue().toString());
            if (customerId <= 0) {
                return String.format("[bcp校验客户关联门店数量] 客户ID获取失败, 客户ID=%s", customerId);
            }
            params.put("customerId", customerId);

            String result = rpcService.invoke("monitorPoiCountInCustomerListEs",
                    Lists.newArrayList("com.sankuai.meituan.waimai.thrift.customer.domain.customer.monitor.MonitorCustomerPoiCountDTO"),
                    Lists.newArrayList(JsonUtils.toJson(params)));

            if (!StringUtils.isBlank(result) && !"\"\"".equals(result)) {
                return result;
            }
        } catch (Exception e) {
            BcpLoggerUtils.info("[bcp校验客户关联门店数量] 校验失败:msg=" + binlogRawData.toString() + "; 异常日志:" + Arrays.toString(e.getStackTrace()));
        }
        return null;
    }

    @Override
    public void alarm(String s, RawData rawData, RawData... rawData2) throws Exception {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.customer.monitor.customerList.WmCustomerListRuleRunner.alarm(java.lang.String,com.dianping.frog.sdk.data.RawData,com.dianping.frog.sdk.data.RawData[])");
        //通过大象公众号发送告警消息, 收件人为告警配置中的告警接收人。
        DXUtil.sendAlarm(s);
    }
}
