package com.sankuai.meituan.waimai.customer.service.kp.operatorv2;

import com.sankuai.meituan.waimai.customer.domain.WmCustomerDB;
import com.sankuai.meituan.waimai.customer.service.kp.operatorv2.preverifierv2.*;
import com.sankuai.meituan.waimai.thrift.customer.domain.kp.WmCustomerKp;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import com.sankuai.meituan.waimai.thrift.exception.WmServerException;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;

/**
 * Author: 窦旭蒙
 * MIS: douxumeng
 * Date: 2020-07-01 18:16
 * Email: <EMAIL>
 * Desc:
 */
@Service
public class OtherOperate extends WmCustomerKpOperate {

    @Override
    public void insert(WmCustomerDB wmCustomer, List<WmCustomerKp> oldCustomerKpList, WmCustomerKp operateKp, int uid, String uname) throws WmCustomerException, TException, WmServerException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_1", "com.sankuai.meituan.waimai.customer.service.kp.operatorv2.OtherOperate.insert(WmCustomerDB,List,WmCustomerKp,int,String)");
        // 数据校验
        List<KpPreverify> verifyList = Arrays.asList(kpDataVerify, kpTypeNumVerify);
        for (KpPreverify kpPreverify : verifyList) {
            kpPreverify.verify(wmCustomer, oldCustomerKpList, operateKp, null, null, uid, uname);
        }
        // DB操作
        otherDBOperator.insert(wmCustomer, oldCustomerKpList, kpTransList(operateKp), uid, uname);
        sendEffectiveMq(wmCustomer);
    }

    @Override
    public void update(WmCustomerDB wmCustomer, List<WmCustomerKp> oldCustomerKpList, WmCustomerKp operateKp, int uid, String uname) throws WmCustomerException, TException, WmServerException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_2", "com.sankuai.meituan.waimai.customer.service.kp.operatorv2.OtherOperate.update(WmCustomerDB,List,WmCustomerKp,int,String)");
        // 数据校验
        List<KpPreverify> verifyList = Arrays.asList(kpDataVerify);
        for (KpPreverify kpPreverify : verifyList) {
            kpPreverify.verify(wmCustomer, oldCustomerKpList, null, operateKp, null, uid, uname);
        }
        // DB操作
        otherDBOperator.update(wmCustomer, oldCustomerKpList, kpTransList(operateKp), uid, uname);
        sendEffectiveMq(wmCustomer);
    }

    @Override
    public void delete(WmCustomerDB wmCustomer, List<WmCustomerKp> oldCustomerKpList, WmCustomerKp operateKp, int uid, String uname) throws WmCustomerException, TException, WmServerException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.kp.operatorv2.OtherOperate.delete(WmCustomerDB,List,WmCustomerKp,int,String)");
        // 数据校验暂无
        // DB操作
        otherDBOperator.delete(wmCustomer, oldCustomerKpList, kpTransList(operateKp), uid, uname);
        sendEffectiveMq(wmCustomer);
    }
}
