package com.sankuai.meituan.waimai.customer.service.sign;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.sankuai.meituan.waimai.customer.config.MccConfig;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerService;
import com.sankuai.meituan.waimai.thrift.customer.domain.OpCustomerResultBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.WmCustomerBasicBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.BatchApplyManualPackResultOutPutDTO;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import org.apache.commons.collections.CollectionUtils;
import org.apache.thrift.TException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class WmEcontractSignBzExternalService {

    private static final Logger      LOGGER   = LoggerFactory.getLogger(WmEcontractSignBzExternalService.class);

    @Autowired
    private WmEcontractSignBzService wmEcontractSignBzService;

    @Autowired
    private WmCustomerService        wmCustomerService;

    private static Splitter          SPLITTER = Splitter.on(",").omitEmptyStrings().trimResults();

    /**
     * 批量触发待签约任务
     *
     * @param customerIdList
     * @param commitUid
     * @return
     * @throws TException
     */
    public OpCustomerResultBo batchApplyManualPackForShanGou(List<Long> customerIdList, int commitUid) throws TException, WmCustomerException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_1", "com.sankuai.meituan.waimai.customer.service.sign.WmEcontractSignBzExternalService.batchApplyManualPackForShanGou(java.util.List,int)");
        LOGGER.info("batchApplyManualPackForShanGou before: customerIdList = {}, commitUid = {}", JSON.toJSONString(customerIdList), commitUid);
        OpCustomerResultBo opCustomerResultBo = new OpCustomerResultBo();

        List<WmCustomerBasicBo> wmCustomerBasicBoList = wmCustomerService.getCustomerListByIdOrMtCustomerId(Sets.newHashSet(customerIdList));
        filterNotSgCustomer(wmCustomerBasicBoList, customerIdList, opCustomerResultBo);

        LOGGER.info("batchApplyManualPackForShanGou after: customerIdList = {}, commitUid = {}", JSON.toJSONString(customerIdList), commitUid);
        if (CollectionUtils.isEmpty(customerIdList)) {
            return opCustomerResultBo;
        }
        return wmEcontractSignBzService.batchApplyManualPack(customerIdList, commitUid, opCustomerResultBo);
    }

    /**
     * 批量触发待签约任务
     *
     * @param customerIdList
     * @param commitUid
     * @return
     * @throws TException
     */
    public List<BatchApplyManualPackResultOutPutDTO> batchApplyManualPack(List<Long> customerIdList, int commitUid)
            throws TException, WmCustomerException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_1", "com.sankuai.meituan.waimai.customer.service.sign.WmEcontractSignBzExternalService.batchApplyManualPack(java.util.List,int)");
        LOGGER.info("batchApplyManualPack customerIdList = {}, commitUid = {}", JSON.toJSONString(customerIdList), commitUid);
        return wmEcontractSignBzService.batchApplyManualPack(customerIdList, commitUid);
    }

    /**
     * 校验闪购客户的客户类型
     *
     * @param wmCustomerBasicBoList
     * @param customerIdList
     * @param opCustomerResultBo
     * @return
     */
    private void filterNotSgCustomer(List<WmCustomerBasicBo> wmCustomerBasicBoList, List<Long> customerIdList,
                                     OpCustomerResultBo opCustomerResultBo) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.sign.WmEcontractSignBzExternalService.filterNotSgCustomer(List,List,OpCustomerResultBo)");
        String sgCustomerRealType = MccConfig.getShanGouCustomerRealType();
        List<Integer> sgCustomerRealTypeList = SPLITTER.splitToList(sgCustomerRealType).stream().map(Integer::parseInt).collect(Collectors.toList());

        List<Long> sgCustomerIdList = Lists.newArrayList();
        for (WmCustomerBasicBo wmCustomerBasicBo : wmCustomerBasicBoList) {
            if (sgCustomerRealTypeList.contains(wmCustomerBasicBo.getCustomerRealType())) {
                sgCustomerIdList.add(Long.valueOf(wmCustomerBasicBo.getId()));
                sgCustomerIdList.add(wmCustomerBasicBo.getMtCustomerId());
            }
        }

        List<Long> notSgCustomerIdList = Lists.newArrayList(customerIdList);
        notSgCustomerIdList.removeAll(sgCustomerIdList);
        handleErrorCustomer(notSgCustomerIdList, "非闪购客户", opCustomerResultBo);

        customerIdList.retainAll(sgCustomerIdList);
    }

    /**
     * 处理异常的客户ID
     *
     * @param customerIdList
     * @param errMsg
     * @param opCustomerResultBo
     */
    private void handleErrorCustomer(List<Long> customerIdList, String errMsg, OpCustomerResultBo opCustomerResultBo) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.sign.WmEcontractSignBzExternalService.handleErrorCustomer(List,String,OpCustomerResultBo)");
        if (CollectionUtils.isEmpty(customerIdList)) {
            return;
        }

        Map<Long, String> resultMap = opCustomerResultBo.getResultMap();
        for (Long customerId : customerIdList) {
            resultMap.put(customerId, errMsg);
        }
    }
}
