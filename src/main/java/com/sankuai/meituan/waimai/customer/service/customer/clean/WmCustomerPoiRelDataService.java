package com.sankuai.meituan.waimai.customer.service.customer.clean;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 20230919
 * @desc 客户门店关系数据清洗
 */
@Service
@Slf4j
public class WmCustomerPoiRelDataService {


    /**
     * 将客户下关联所有门店的是否子门店字段更新到ES
     *
     * @param customerId
     */
    public void updateIsChildTag2PoiRelEs(Integer customerId) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_1", "com.sankuai.meituan.waimai.customer.service.customer.clean.WmCustomerPoiRelDataService.updateIsChildTag2PoiRelEs(java.lang.Integer)");


    }
}
