package com.sankuai.meituan.waimai.customer.adapter;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.meituan.mtrace.thread.pool.ExecutorServiceTraceWrapper;
import com.sankuai.meituan.waimai.customer.config.MccConfig;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.exception.HeronContractGatewayThriftException;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.param.flow.sign.*;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerErrorCodeConstants;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import org.apache.thrift.TException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.service.HeronContractFlowProcessGatewayThriftService;

import java.util.List;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.Future;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeoutException;
import java.util.ArrayList;

/**
 * 配送新合同服务
 *
 * Created by lixuepeng on 2023/5/31
 */
@Service
public class WmLogisticsGatewayThriftServiceAdapter {

    // 线程池

    private static final ExecutorService DELIVERY_MANUAL_PACK_APPLY_EXECUTOR = new ExecutorServiceTraceWrapper(
            new ThreadPoolExecutor(16,
                    32,
                    60L,
                    TimeUnit.SECONDS,
                    new ArrayBlockingQueue<>(1024),
                    new ThreadFactoryBuilder().setNameFormat("delivery_manual-pack-apply-thread-%d").build(),
                    new ThreadPoolExecutor.DiscardPolicy())
    );

    private static Logger log = LoggerFactory.getLogger(WmLogisticsGatewayThriftServiceAdapter.class);

    @Autowired
    private HeronContractFlowProcessGatewayThriftService heronContractFlowProcessGatewayThriftService;

    /**
     * 是否使用新的签约相关能力（发起签约、取消签约、取消待签约）
     * @param param
     * @return
     * @throws WmCustomerException
     */
    public boolean isDeliverySignOperateUseNewIface(HeronContractSignGrayParam param) throws WmCustomerException {
        try {
            log.info("#getGatewaySignOperateGrayResult param:{}", JSON.toJSONString(param));
            boolean result = heronContractFlowProcessGatewayThriftService.getGatewaySignOperateGrayResult(param);
            log.info("#getGatewaySignOperateGrayResult result:{}", result);
            return result;
        } catch (HeronContractGatewayThriftException e) {
            log.error("#isDeliveryApplySignUseNewIface 判断配送合同批量打包签约是否使用售卖网关接口业务异常 param:{}", JSON.toJSONString(param), e);
            throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, e.getMessage());
        } catch (TException ex) {
            log.error("#isDeliveryApplySignUseNewIface 判断配送合同批量打包签约是否使用售卖网关接口系统异常 param:{}", JSON.toJSONString(param), ex);
            throw new WmCustomerException(CustomerErrorCodeConstants.SYSTEM_ERROR, "判断配送合同批量打包签约是否使用售卖网关接口系统异常");
        } catch (Exception e) {
            log.error("WmLogisticsGatewayThriftServiceAdapter#isDeliverySignOperateUseNewIface, error", e);
            throw new WmCustomerException(CustomerErrorCodeConstants.SYSTEM_ERROR, "判断配送合同批量打包签约是否使用售卖网关接口系统异常");
        }
    }

    /**
     * 通过售卖网关发起打包签约
     * @param param
     * @throws WmCustomerException
     */
    public void deliveryBatchApplySignUseNewIface(HeronContractManualBatchSignParam param) throws WmCustomerException {
        List<HeronContractManualSignItem> signItemList = param.getSignItemList();
        List<List<HeronContractManualSignItem>> partition = Lists.partition(signItemList, MccConfig.getManualPackGroupSize());
        for (List<HeronContractManualSignItem> signItems : partition) {
            HeronContractManualBatchSignParam batchSignParam = new HeronContractManualBatchSignParam();
            batchSignParam.setSignItemList(signItems);
            batchSignParam.setBatchManualConfirmId(param.getBatchManualConfirmId());
            batchSignParam.setOperator(param.getOperator());
            batchSignParam.setApplyType(param.getApplyType());
            DELIVERY_MANUAL_PACK_APPLY_EXECUTOR.submit(() -> applyBatchDeliverySign(batchSignParam));
        }
    }

    private void applyBatchDeliverySign(HeronContractManualBatchSignParam param) throws WmCustomerException {
        try {
            log.info("WmLogisticsGatewayThriftServiceAdapter#applyBatchDeliverySign, param:{}", JSON.toJSONString(param));
            heronContractFlowProcessGatewayThriftService.manualBatchApplySign(param);
        } catch (HeronContractGatewayThriftException e) {
            log.warn("WmLogisticsGatewayThriftServiceAdapter#applyBatchDeliverySign, warn param:{}", JSON.toJSONString(param), e);
            throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, e.getMessage());
        } catch (TException ex) {
            log.error("WmLogisticsGatewayThriftServiceAdapter#applyBatchDeliverySign, TException, param:{}", JSON.toJSONString(param), ex);
            throw new WmCustomerException(CustomerErrorCodeConstants.SYSTEM_ERROR, "使用售卖网关接口发起配送合同批量签约系统异常");
        } catch (Exception e) {
            log.error("WmLogisticsGatewayThriftServiceAdapter#applyBatchDeliverySign, error", e);
            throw new WmCustomerException(CustomerErrorCodeConstants.SYSTEM_ERROR, "使用售卖网关接口发起配送合同批量签约系统异常");
        }
    }

    /**
     * 通过售卖网关取消签约任务
     * @param param
     * @throws WmCustomerException
     */
    public void deliveryCancelSignUseNewIface(HeronContractEcontractCancelSignParam param) throws WmCustomerException {
        try {
            log.info("#econtractCancelSign param:{}", JSON.toJSONString(param));
            heronContractFlowProcessGatewayThriftService.econtractCancelSign(param);
        } catch (HeronContractGatewayThriftException e) {
            log.error("#deliveryCancelSignUseNewIface 使用售卖网关接口取消配送合同签约中任务业务异常 param:{}", JSON.toJSONString(param), e);
            throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, e.getMessage());
        } catch (TException ex) {
            log.error("#deliveryCancelSignUseNewIface 使用售卖网关接口取消配送合同签约中任务系统异常 param:{}", JSON.toJSONString(param), ex);
            throw new WmCustomerException(CustomerErrorCodeConstants.SYSTEM_ERROR, "使用售卖网关接口取消配送合同签约中任务系统异常");
        } catch (Exception e) {
            log.error("WmLogisticsGatewayThriftServiceAdapter#deliveryCancelSignUseNewIface, error", e);
            throw new WmCustomerException(CustomerErrorCodeConstants.SYSTEM_ERROR, "使用售卖网关接口取消配送合同签约中任务系统异常");
        }
    }

    /**
     * 通过售卖网关取消待打包签约任务
     * @param param
     * @throws WmCustomerException
     */
    public void deliveryCancelManualSignUseNewIface(HeronContractEcontractCancelManualSignParam param) throws WmCustomerException {
        try {
            log.info("#econtractCancelManualSign param:{}", JSON.toJSONString(param));
            heronContractFlowProcessGatewayThriftService.econtractCancelManualSign(param);
        } catch (HeronContractGatewayThriftException e) {
            log.error("#deliveryCancelManualSignUseNewIface 使用售卖网关接口取消待打包签约任务业务异常 param:{}", JSON.toJSONString(param), e);
            throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, e.getMessage());
        } catch (TException ex) {
            log.error("#deliveryCancelManualSignUseNewIface 使用售卖网关接口取消待打包签约任务系统异常 param:{}", JSON.toJSONString(param), ex);
            throw new WmCustomerException(CustomerErrorCodeConstants.SYSTEM_ERROR, "使用售卖网关接口取消待打包签约任务系统异常");
        } catch (Exception e) {
            log.error("WmLogisticsGatewayThriftServiceAdapter#deliveryCancelManualSignUseNewIface, error", e);
            throw new WmCustomerException(CustomerErrorCodeConstants.SYSTEM_ERROR, "使用售卖网关接口取消待打包签约任务系统异常");
        }
    }
}
