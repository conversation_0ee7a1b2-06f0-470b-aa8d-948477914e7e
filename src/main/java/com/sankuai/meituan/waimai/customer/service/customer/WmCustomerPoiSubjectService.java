package com.sankuai.meituan.waimai.customer.service.customer;

import com.alibaba.fastjson.JSON;
import com.sankuai.meituan.waimai.customer.dao.WmCustomerPoiLogisticsSubjectDBMapper;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerPoiLogisticsSubjectDB;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class WmCustomerPoiSubjectService {

    private static Logger logger = LoggerFactory.getLogger(WmCustomerPoiSubjectService.class);

    @Autowired
    private WmCustomerPoiLogisticsSubjectDBMapper wmCustomerPoiLogisticsSubjectDBMapper;

    public void batchSavePoiSubject(List<WmCustomerPoiLogisticsSubjectDB> list){
        logger.info("批量保存门店主体快照：list={}", JSON.toJSON(list));
        wmCustomerPoiLogisticsSubjectDBMapper.batchInsertCustomerPoiSubject(list);
    }

    public void savePoiSubject(WmCustomerPoiLogisticsSubjectDB record){
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.service.customer.WmCustomerPoiSubjectService.savePoiSubject(com.sankuai.meituan.waimai.customer.domain.WmCustomerPoiLogisticsSubjectDB)");
        logger.info("保存门店主体快照：record={}", JSON.toJSON(record));
        wmCustomerPoiLogisticsSubjectDBMapper.insertCustomerPoiSubject(record);
    }

    public void updatePoiSubject(WmCustomerPoiLogisticsSubjectDB record){
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.service.customer.WmCustomerPoiSubjectService.updatePoiSubject(com.sankuai.meituan.waimai.customer.domain.WmCustomerPoiLogisticsSubjectDB)");
        logger.info("更新门店主体快照：record={}", JSON.toJSON(record));
        wmCustomerPoiLogisticsSubjectDBMapper.updateCustomerPoiSubject(record);
    }

    public WmCustomerPoiLogisticsSubjectDB getLatestPoiSubject(Integer customerId,Long wmPoiId) {
        logger.info("查询最新一条主体记录：customerId={},wmPoiId={}", customerId, wmPoiId);
        return wmCustomerPoiLogisticsSubjectDBMapper.getLatestPoiSubject(customerId, wmPoiId);
    }
}
