package com.sankuai.meituan.waimai.customer.service.customer.check.customer;

import com.alibaba.fastjson.JSONObject;
import com.sankuai.meituan.waimai.customer.config.MccCustomerConfig;
import com.sankuai.meituan.waimai.customer.service.customer.check.IWmCustomerValidator;
import com.sankuai.meituan.waimai.customer.util.ValidateUtil;
import com.sankuai.meituan.waimai.thrift.customer.constant.CertTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerConstants;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerRealTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerType;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.ValidateResultBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.WmCustomerBasicBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * 字段校验
 */
@Service
public class WmCustomerInputValueValidator implements IWmCustomerValidator {

    @Override
    public ValidateResultBo valid(ValidateResultBo validateResultBo, WmCustomerBasicBo wmCustomerBasicBo, Boolean force, Boolean isAudit, Integer opUid) throws WmCustomerException {
        if (wmCustomerBasicBo.getCustomerType() == CustomerType.CUSTOMER_TYPE_BUSINESS.getCode()) {
            //营业执照
            return validateBusinessLicense(wmCustomerBasicBo, validateResultBo);
        } else if (wmCustomerBasicBo.getCustomerType() == CustomerType.CUSTOMER_TYPE_IDCARD.getCode()) {
            //身份证
            return validateIDCard(wmCustomerBasicBo, validateResultBo);
        } else if (wmCustomerBasicBo.getCustomerType() == CustomerType.CUSTOMER_TYPE_BUSINESS_ABROAD.getCode()) {
            //身份证
            return validateBusinessAbroadLicense(wmCustomerBasicBo, validateResultBo);
        } else {
            return checkFail(validateResultBo, CustomerConstants.RESULT_CODE_ERROR, "不支持该客户类型字段校验");
        }
    }

    /**
     * 校验营业执照输入字段
     *
     * @param wmCustomerBasicBo
     * @param validateResultBo
     * @throws WmCustomerException
     */
    private ValidateResultBo validateBusinessLicense(WmCustomerBasicBo wmCustomerBasicBo, ValidateResultBo validateResultBo)
            throws WmCustomerException {
        //个人证件类型错误
        if (CertTypeEnum.getByType(wmCustomerBasicBo.getCustomerSecondType()) != null) {
            return checkFail(validateResultBo, CustomerConstants.RESULT_CODE_VERIFY_INPUT_ERROR, "个人证件类型错误");
        }
        //必填校验
        int customerRealType = wmCustomerBasicBo.getCustomerRealType();
        if (customerRealType != CustomerRealTypeEnum.CONTRACTOR.getValue()) {
            if (StringUtils.isBlank(wmCustomerBasicBo.getPicUrl()) || StringUtils.isBlank(wmCustomerBasicBo.getCustomerNumber())
                    || StringUtils.isBlank(wmCustomerBasicBo.getCustomerName()) || StringUtils.isBlank(wmCustomerBasicBo.getLegalPerson())
                    || StringUtils.isBlank(wmCustomerBasicBo.getAddress())) {
                return checkFail(validateResultBo, CustomerConstants.RESULT_CODE_VERIFY_INPUT_ERROR, "必填项缺失");
            }
        } else {
            if (StringUtils.isBlank(wmCustomerBasicBo.getCustomerName()) || wmCustomerBasicBo.getCustomerRealType() == 0
                    || wmCustomerBasicBo.getCustomerType() == 0) {
                return checkFail(validateResultBo, CustomerConstants.RESULT_CODE_VERIFY_INPUT_ERROR, "必填项缺失");
            }
        }

        if (wmCustomerBasicBo.getPicUrl().indexOf(CustomerConstants.SPLIT_SYMBOL) > 0) {
            return checkFail(validateResultBo, CustomerConstants.RESULT_CODE_VERIFY_INPUT_ERROR, "资质附件上传错误");
        }
        if (wmCustomerBasicBo.getCustomerNumber().length() > 255) {
            return checkFail(validateResultBo, CustomerConstants.RESULT_CODE_VERIFY_INPUT_ERROR, "注册号长度超过255");
        }
        if (wmCustomerBasicBo.getCustomerName().length() > 256) {
            return checkFail(validateResultBo, CustomerConstants.RESULT_CODE_VERIFY_INPUT_ERROR, "执照名称长度超过256");
        }
        if (wmCustomerBasicBo.getLegalPerson().length() > 128) {
            return checkFail(validateResultBo, CustomerConstants.RESULT_CODE_VERIFY_INPUT_ERROR, "法定代表人超过128");
        }
        if (wmCustomerBasicBo.getAddress().length() > 255) {
            return checkFail(validateResultBo, CustomerConstants.RESULT_CODE_VERIFY_INPUT_ERROR, "地址超过255");
        }
        if (StringUtils.isNotEmpty(wmCustomerBasicBo.getBusinessScope()) && wmCustomerBasicBo.getBusinessScope().length() > 1024) {
            return checkFail(validateResultBo, CustomerConstants.RESULT_CODE_VERIFY_INPUT_ERROR, "经营范围超过1024");
        }
        return checkPass(validateResultBo);
    }

    /**
     * 校验身份证输入字段
     *
     * @param wmCustomerBasicBo
     * @param validateResultBo
     * @throws WmCustomerException
     */
    private ValidateResultBo validateIDCard(WmCustomerBasicBo wmCustomerBasicBo, ValidateResultBo validateResultBo) throws WmCustomerException {
        //必填校验
        if (CertTypeEnum.getByType(wmCustomerBasicBo.getCustomerSecondType()) == null || StringUtils.isBlank(wmCustomerBasicBo.getPicUrl())
                || StringUtils.isBlank(wmCustomerBasicBo.getCustomerName()) || StringUtils.isBlank(wmCustomerBasicBo.getCustomerNumber())) {
            return checkFail(validateResultBo, CustomerConstants.RESULT_CODE_VERIFY_INPUT_ERROR, "必填项缺失");
        }
        //个人证件等于身份证，则上传图片要是正反两张图片
        if (CertTypeEnum.getByType(wmCustomerBasicBo.getCustomerSecondType()) == CertTypeEnum.ID_CARD
                && wmCustomerBasicBo.getPicUrl().split(CustomerConstants.SPLIT_SYMBOL).length != 2) {
            return checkFail(validateResultBo, CustomerConstants.RESULT_CODE_VERIFY_INPUT_ERROR, "身份证上传图片错误");
        }
        //如果非身份证，则上传图片只能一张
        if (CertTypeEnum.getByType(wmCustomerBasicBo.getCustomerSecondType()) != CertTypeEnum.ID_CARD
                && wmCustomerBasicBo.getPicUrl().indexOf(CustomerConstants.SPLIT_SYMBOL) > 0) {
            return checkFail(validateResultBo, CustomerConstants.RESULT_CODE_VERIFY_INPUT_ERROR, "非身份证上传图片错误");
        }
        //当身份证/临时身份证/身份证复印件时校验身份证
        if (CertTypeEnum.getByType(wmCustomerBasicBo.getCustomerSecondType()) == CertTypeEnum.ID_CARD
                || CertTypeEnum.getByType(wmCustomerBasicBo.getCustomerSecondType()) == CertTypeEnum.ID_CARD_COPY
                || CertTypeEnum.getByType(wmCustomerBasicBo.getCustomerSecondType()) == CertTypeEnum.ID_CARD_TEMP) {
            if (!ValidateUtil.isIDCardNumber(wmCustomerBasicBo.getCustomerNumber()) || !ValidateUtil.isAdult(wmCustomerBasicBo.getCustomerNumber())) {
                return checkFail(validateResultBo, CustomerConstants.RESULT_CODE_VERIFY_INPUT_ERROR, "身份证号码错误或者未满18周岁");
            }
        }
        //校验姓名长度
        if (wmCustomerBasicBo.getCustomerName().length() > 256) {
            return checkFail(validateResultBo, CustomerConstants.RESULT_CODE_VERIFY_INPUT_ERROR, "姓名长度大于256");
        }
        return checkPass(validateResultBo);
    }


    /**
     * 校验海外营业执照输入字段
     *
     * @param wmCustomerBasicBo
     * @param validateResultBo
     * @throws WmCustomerException
     */
    private ValidateResultBo validateBusinessAbroadLicense(WmCustomerBasicBo wmCustomerBasicBo, ValidateResultBo validateResultBo)
            throws WmCustomerException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.service.customer.check.customer.WmCustomerInputValueValidator.validateBusinessAbroadLicense(WmCustomerBasicBo,ValidateResultBo)");
        //个人证件类型错误
        if (CertTypeEnum.getByType(wmCustomerBasicBo.getCustomerSecondType()) != null) {
            return checkFail(validateResultBo, CustomerConstants.RESULT_CODE_VERIFY_INPUT_ERROR, "个人证件类型错误");
        }
        //必填校验
        if (StringUtils.isBlank(wmCustomerBasicBo.getPicUrl()) || StringUtils.isBlank(wmCustomerBasicBo.getAddress()) || StringUtils.isBlank(wmCustomerBasicBo.getLegalPerson())
                || StringUtils.isBlank(wmCustomerBasicBo.getBusinessScope()) || wmCustomerBasicBo.getRegistryState() == null ||
                StringUtils.isBlank(wmCustomerBasicBo.getCustomerName()) || StringUtils.isBlank(wmCustomerBasicBo.getCustomerNumber())) {
            return checkFail(validateResultBo, CustomerConstants.RESULT_CODE_VERIFY_INPUT_ERROR, "必填项缺失");
        }

        String[] picUrls = wmCustomerBasicBo.getPicUrl().split(CustomerConstants.SPLIT_SYMBOL);
        if (picUrls.length == 0 || picUrls.length > 10) {
            return checkFail(validateResultBo, CustomerConstants.RESULT_CODE_VERIFY_INPUT_ERROR, "资质附件必传且最多支持10张");
        }
        if (wmCustomerBasicBo.getCustomerNumber().length() > 255) {
            return checkFail(validateResultBo, CustomerConstants.RESULT_CODE_VERIFY_INPUT_ERROR, "注册号长度超过255");
        }
        if (wmCustomerBasicBo.getCustomerName().length() > 256) {
            return checkFail(validateResultBo, CustomerConstants.RESULT_CODE_VERIFY_INPUT_ERROR, "执照名称长度超过256");
        }
        if (wmCustomerBasicBo.getLegalPerson().length() > 128) {
            return checkFail(validateResultBo, CustomerConstants.RESULT_CODE_VERIFY_INPUT_ERROR, "法定代表人超过128");
        }
        if (wmCustomerBasicBo.getAddress().length() > 255) {
            return checkFail(validateResultBo, CustomerConstants.RESULT_CODE_VERIFY_INPUT_ERROR, "地址超过255");
        }
        if (StringUtils.isNotEmpty(wmCustomerBasicBo.getBusinessScope()) && wmCustomerBasicBo.getBusinessScope().length() > 1024) {
            return checkFail(validateResultBo, CustomerConstants.RESULT_CODE_VERIFY_INPUT_ERROR, "经营范围超过1024");
        }
        List<Integer> registryStateList = JSONObject.parseArray(MccCustomerConfig.getB2CRegistryState(), Integer.class);
        if (!CollectionUtils.isEmpty(registryStateList) && !registryStateList.contains(wmCustomerBasicBo.getRegistryState())) {
            return checkFail(validateResultBo, CustomerConstants.RESULT_CODE_VERIFY_INPUT_ERROR, "注册国家/地区不合法");
        }
        return checkPass(validateResultBo);
    }
}
