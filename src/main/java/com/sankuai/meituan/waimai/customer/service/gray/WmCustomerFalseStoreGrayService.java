/*
 * Copyright (c) 2022 meituan.com. All Rights Reserved.
 */
package com.sankuai.meituan.waimai.customer.service.gray;

import com.alibaba.fastjson.JSON;
import com.sankuai.meituan.waimai.customer.adapter.WmOrgClient;
import com.sankuai.meituan.waimai.customer.adapter.WmVirtualOrgServiceAdaptor;
import com.sankuai.meituan.waimai.customer.config.MccCustomerConfig;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerDB;
import com.sankuai.meituan.waimai.infra.constants.WmVirtualOrgTeamTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerType;
import com.sankuai.meituan.waimai.thrift.customer.constant.customer.CertificateAuthTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.customer.CertificateTypeEnum;
import com.sankuai.meituan.waimai.thrift.exception.WmServerException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * 虚假门店灰度逻辑
 */
@Service
@Slf4j
public class WmCustomerFalseStoreGrayService {

    @Autowired
    private WmVirtualOrgServiceAdaptor wmVirtualOrgServiceAdaptor;

    @Autowired
    private WmOrgClient wmOrgClient;

    /**
     * 是否需要认证
     *
     * @param userId       用户ID
     * @param wmCustomerDB 客户对象
     * @param type         认证类型
     * @param isUserGray   是否判断按照用户灰度
     * @return
     */
    public boolean isNeedAuthGray(Integer userId, WmCustomerDB wmCustomerDB, CertificateAuthTypeEnum type, boolean isUserGray) {
        log.info("isNeedAuth userId={},wmCustomerDB={},type={}", userId, JSON.toJSONString(wmCustomerDB), JSON.toJSONString(type));
        if (userId == null || wmCustomerDB == null || type == null) {
            return false;
        }
        switch (type) {
            case ENTERPRISE_THREE_ELEMENT_AUTH:
                return isNeedThreeAuth(userId, wmCustomerDB, isUserGray);
            case EXACT_ENTERPRISE_FOUR_ELEMENT_AUTH:
                return isNeedFourAuth(userId, wmCustomerDB, isUserGray);
            default:
                return false;
        }
    }

    /**
     * 是否满足企业四要素认证的灰度策略
     *
     * @param userId       用户ID
     * @param wmCustomerDB 客户对象
     * @param isUserGray   是否判断按照用户灰度规则
     * @return
     */
    private boolean isNeedFourAuth(int userId, WmCustomerDB wmCustomerDB, boolean isUserGray) {
        if (!MccCustomerConfig.customerBusinessLicenseFourAuthSwitch()) {
            //企业四要素认证总开关未开启，不做四要素认证
            return false;
        }
        // 客户未生效不做企业四要素校验
        if (wmCustomerDB.isUnEffectived()) {
            return false;
        }
        // 非营业执照不做企业四要素校验
        if (!isBusiness(wmCustomerDB)) {
            return false;
        }
        //电子执照不做企业四要素校验
        if (isElectronic(wmCustomerDB)) {
            return false;
        }
        if (isWhiteList(wmCustomerDB.getCustomerNumber())) {
            //校验统一社会信用编码处于三四要素白名单中,不做四要素认证
            return false;
        }
        //营业执照名字于法人名字相等不做企业精准四要素认证
        if (wmCustomerDB.getCustomerName() != null
                && wmCustomerDB.getCustomerName().equals(wmCustomerDB.getLegalPerson())) {
            log.info("营业执照名字与法人名字相等不做企业精准四要素认证 customerId={}", wmCustomerDB.getId());
            return false;
        }
        if (isUserGray) {
            if (MccCustomerConfig.customerBusinessLicenseFourAuthCitySwitch()) {
                if (!isBaiLing(userId)) {
                    //用户不属于白领团队，不做四要素认证
                    return false;
                }
                //四要素按照城市灰度开关开启，进行用户灰度城市校验
                List<Integer> userCityIdList = wmVirtualOrgServiceAdaptor.getWmVirtualOrgCityIdsByUserid(userId);
                List<Integer> userCityIdGrayList = MccCustomerConfig.customerBusinessLicenseFourAuthCity();
                if (disjoint(userCityIdList, userCityIdGrayList)) {
                    return false;
                }
            }
        }
        return true;
    }

    /**
     * 是否营业执照
     *
     * @param wmCustomerDB
     * @return
     */
    private boolean isBusiness(WmCustomerDB wmCustomerDB) {
        return (wmCustomerDB != null
                && wmCustomerDB.getCustomerType() != null
                && wmCustomerDB.getCustomerType() == CustomerType.CUSTOMER_TYPE_BUSINESS.getCode());
    }

    /**
     * 是否纸质
     *
     * @param wmCustomerDB
     * @return
     */
    private boolean isPaper(WmCustomerDB wmCustomerDB) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_1", "com.sankuai.meituan.waimai.customer.service.gray.WmCustomerFalseStoreGrayService.isPaper(com.sankuai.meituan.waimai.customer.domain.WmCustomerDB)");
        return wmCustomerDB.getCertificateType() != null
                && wmCustomerDB.getCertificateType() == CertificateTypeEnum.PAPER.getType();
    }

    /**
     * 是否电子
     *
     * @param wmCustomerDB
     * @return
     */
    private boolean isElectronic(WmCustomerDB wmCustomerDB) {
        return wmCustomerDB.getCertificateType() != null
                && wmCustomerDB.getCertificateType() == CertificateTypeEnum.ELECTRONIC.getType();
    }

    /**
     * 是否满足企业三要素认证的灰度策略
     *
     * @param userId       用户ID
     * @param wmCustomerDB 客户对象
     * @param isUserGray   是否判断按照用户灰度规则
     * @return
     */
    private boolean isNeedThreeAuth(int userId, WmCustomerDB wmCustomerDB, boolean isUserGray) {
        if (!MccCustomerConfig.customerBusinessLicenseThreeAuthSwitch()) {
            //企业三要素认证总开关未开启，不做三要素认证
            return false;
        }
        if (!isBusiness(wmCustomerDB)) {
            // 非营业执照不需要三要素认证
            return false;
        }
        if (isWhiteList(wmCustomerDB.getCustomerNumber())) {
            //校验统一社会信用编码处于三四要素白名单中,不做三要素认证
            return false;
        }
        if (isUserGray) {
            if (MccCustomerConfig.customerBusinessLicenseThreeAuthCitySwitch()) {
                if (!isBaiLing(userId)) {
                    //用户不属于白领团队，不做三要素认证
                    return false;
                }
                //三要素按照城市灰度开关开启，进行用户灰度城市校验
                List<Integer> userCityIdList = wmVirtualOrgServiceAdaptor.getWmVirtualOrgCityIdsByUserid(userId);
                List<Integer> userCityIdGrayList = MccCustomerConfig.customerBusinessLicenseThreeAuthCity();
                if (disjoint(userCityIdList, userCityIdGrayList)) {
                    return false;
                }
            }
        }
        return true;
    }

    /**
     * 两个集合是否不相交
     *
     * @param ListA
     * @param ListB
     * @return
     */
    private boolean disjoint(List<Integer> ListA, List<Integer> ListB) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_1", "com.sankuai.meituan.waimai.customer.service.gray.WmCustomerFalseStoreGrayService.disjoint(java.util.List,java.util.List)");
        if (CollectionUtils.isEmpty(ListA) || CollectionUtils.isEmpty(ListB)) {
            return true;
        }
        return Collections.disjoint(ListA, ListB);
    }

    /**
     * 判断用户是否属于白领团队
     *
     * @param userId 用户ID
     * @return
     */
    private boolean isBaiLing(int userId) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.service.gray.WmCustomerFalseStoreGrayService.isBaiLing(int)");
        try {
            if (userId <= 0) {
                return false;
            }
            return wmOrgClient.isUserInWmOrgTeam(userId, WmVirtualOrgTeamTypeEnum.BAI_LING);
        } catch (WmServerException e) {
            log.error("isBaiLing userId={}", userId, e);
        } catch (Exception e) {
            log.error("isBaiLing userId={}", userId, e);
        }
        return false;
    }

    /**
     * 校验统一社会信用编码是否处于三四要素白名单中
     *
     * @param customerNumber 统一社会信用编码
     * @return
     */
    public boolean isWhiteList(String customerNumber) {
        if (StringUtils.isBlank(customerNumber)) {
            return false;
        }
        List<String> list = MccCustomerConfig.customerBusinessLicenseNumberAuthWhiteList();
        if (CollectionUtils.isEmpty(list)) {
            return false;
        }
        return list.contains(customerNumber);
    }

}
