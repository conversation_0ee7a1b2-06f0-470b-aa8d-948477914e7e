package com.sankuai.meituan.waimai.customer.service.sc.thirdworkplace.wrapper;

import cn.hutool.core.collection.CollectionUtil;
import com.dianping.frog.sdk.util.CollectionUtils;
import com.sankuai.djdata.readata.openapi.QueryContext;
import com.sankuai.djdata.readata.openapi.enums.QueryMode;
import com.sankuai.djdata.readata.openapi.enums.QueryType;
import com.sankuai.djdata.readata.openapi.model.PopParam;
import com.sankuai.djdata.readata.openapi.model.QueryFeatures;
import com.sankuai.djdata.readata.openapi.model.QueryFilter;
import com.sankuai.djdata.readata.openapi.model.SortParam;
import com.sankuai.djdata.readata.protocol.openapi.entity.RateInfo;
import com.sankuai.meituan.waimai.customer.adapter.*;
import com.sankuai.meituan.waimai.customer.config.MccConfig;
import com.sankuai.meituan.waimai.customer.service.sc.thirdworkplace.ThirdWorkplaceQueryRoleEnum;
import com.sankuai.meituan.waimai.customer.service.sc.thirdworkplace.config.OneServicePopParamWrapper;
import com.sankuai.meituan.waimai.customer.service.sc.thirdworkplace.config.OneServiceQueryMappingConfig;
import com.sankuai.meituan.waimai.customer.util.SSOUtil;
import com.sankuai.meituan.waimai.gis.client.thrift.dto.city.WmOpenCity;
import com.sankuai.meituan.waimai.infra.constants.WmVirtualOrgSourceEnum;
import com.sankuai.meituan.waimai.infra.domain.WmVirtualOrg;
import com.sankuai.meituan.waimai.thrift.customer.domain.sc.thirdworkplace.WmScThirdWorkplaceQueryBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import com.sankuai.meituan.waimai.thrift.exception.WmSchCantException;
import com.sankuai.octo.doclet.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.lang.reflect.Field;
import java.util.*;

import static com.sankuai.meituan.waimai.thrift.customer.constant.sc.WmScCodeConstants.BIZ_PARA_ERROR;

@Slf4j
@Component
public class QueryContextFactory {

    @Autowired
    private OneServiceWrapper oneServiceWrapper;


    @Autowired
    private WmOrgClient wmOrgClient;

    @Autowired
    private WmOpenCityServiceAdapter wmOpenCityServiceAdapter;



    public QueryContext buildListQueryContext(WmScThirdWorkplaceQueryBo queryBo)throws WmSchCantException {

        checkParam(queryBo);

        QueryContext queryContext = oneServiceWrapper.initQueryContext();

        OneServiceQueryMappingConfig mappingConfig = getOneServiceQueryMappingConfig();

        // 构造分页信息
        queryContext.setPageInfo(queryBo.getPageNo(), queryBo.getPageSize());

        List<String> demensionList = getDimensionList(queryBo);
        // 列表查询专属维度、配置化 部分维度必须带上否则在返回值中取不到
        demensionList.addAll(mappingConfig.getItemQueryDimensionList());

        buildQueryContext(queryContext,
                mappingConfig.getItemIndicatorList(),
                demensionList,
                queryBo);

        // 构造数据集参数
        buildOriginDs(queryContext, mappingConfig.getOriginDsMappingConfig(), queryBo.getStatisticalDimension());

        // 构造环比查询参数
        buildRateInfo(queryContext,
                mappingConfig.getItemMoMDataConfig(),
                queryBo.getStatisticalDimension(),
                MccConfig.getWorkplaceListSortFiled(),
                true);

        return queryContext;
    }

    public QueryContext buildCountQueryContext(WmScThirdWorkplaceQueryBo queryBo) throws WmSchCantException {
        // 构造查询总数的请求上下文
        QueryContext queryContext = oneServiceWrapper.initQueryContext();
        OneServiceQueryMappingConfig mappingConfig = getOneServiceQueryMappingConfig();

        // 设置查询类型为COUNT查询
        queryContext.setQueryMode(QueryMode.AGGREGATION);
        queryContext.setQueryType(QueryType.QUERY_COUNT);

        // count查询设置为1条即可
        queryContext.setPageInfo(1, 1);

        queryContext.addIndex(mappingConfig.getItemIndicatorList().toArray(new String[0]));
        List<String> dimensionList = getDimensionList(queryBo);
        dimensionList.addAll(mappingConfig.getItemQueryDimensionList());
        queryContext.addDimension(dimensionList.toArray(new String[0]));

        // 构造过滤条件（保持与列表查询相同的过滤条件）
        queryContext.setFilter(buildQueryFilter(queryBo));

        // 构造数据集参数
        buildOriginDs(queryContext, mappingConfig.getOriginDsMappingConfig(), queryBo.getStatisticalDimension());

        return queryContext;
    }

    public QueryContext buildMetricsQueryContext(WmScThirdWorkplaceQueryBo queryBo) throws WmSchCantException {

        checkParam(queryBo);

        QueryContext queryContext = oneServiceWrapper.initQueryContext();

        // 构造分页信息-概览统计场景仅查询一条数据
        queryContext.setPageInfo(1, 1);

        OneServiceQueryMappingConfig mappingConfig = getOneServiceQueryMappingConfig();

        buildQueryContext(queryContext,
                mappingConfig.getMetricIndicatorList(),
                getDimensionList(queryBo),
                queryBo);

        // 构造数据集参数
        buildOriginDs(queryContext, mappingConfig.getOriginDsMappingConfig(), queryBo.getStatisticalDimension());

        // 构造环比查询参数
        buildRateInfo(queryContext,
                mappingConfig.getMetricMoMDataConfig(),
                queryBo.getStatisticalDimension(),
                queryBo.getStatisticalDimension(),
                false);

        return queryContext;
    }

    private void buildOriginDs(QueryContext queryContext, Map<String, String> originDsMappingConfig, String statisticalDimension) {
        if (!originDsMappingConfig.containsKey(statisticalDimension)){
            throw new IllegalArgumentException("维度信息不存在数据集");
        }
        queryContext.setOriginDsIdList(Arrays.asList(originDsMappingConfig.get(statisticalDimension)));
    }

    private void buildRateInfo(QueryContext queryContext,
                               Map<String, OneServicePopParamWrapper> metricMoMDataConfig,
                               String dataDimension,
                               String sortFiled,
                               boolean isListQuery) {
        if (!metricMoMDataConfig.containsKey(dataDimension)){
            throw new IllegalArgumentException("维度信息有误");
        }
        QueryFeatures.QueryFeaturesBuilder queryFeaturesBuilder = QueryFeatures.newBuilder();

        OneServicePopParamWrapper oneServicePopParamWrapper = metricMoMDataConfig.get(dataDimension);
        // 构造差值环比查询对象
        PopParam diffPopParam = oneServicePopParamWrapper.buildDiffPopParam();
        if (Objects.nonNull(diffPopParam)){
            queryFeaturesBuilder.pop(diffPopParam);
            if (isListQuery){
                setPopParamNoNeedJoinDims(oneServicePopParamWrapper, diffPopParam);
            }
        }

        // 构造比率环比查询对象
        PopParam radioPopParam = oneServicePopParamWrapper.buildRadioPopParam();
        if (Objects.nonNull(radioPopParam)){
            queryFeaturesBuilder.pop(radioPopParam);
            if (isListQuery){
                setPopParamNoNeedJoinDims(oneServicePopParamWrapper, radioPopParam);
            }
        }

        // 构造排序信息，分页场景必传
        queryFeaturesBuilder.setSortInfo(SortParam.newBuilder().sortDesc(sortFiled)
                .build());

        queryContext.setQueryFeatures(queryFeaturesBuilder.build());
    }

    private void setPopParamNoNeedJoinDims(OneServicePopParamWrapper oneServicePopParamWrapper, PopParam diffPopParam) {
        // 获取 PopParam 类的 Class 对象
        Class<?> popParamClass = diffPopParam.getClass();

        // 获取 PopParam 中的私有字段 rateInfo
        Field rateInfoField = null;
        try {
            rateInfoField = popParamClass.getDeclaredField("rateInfo");
            if (Objects.nonNull(rateInfoField)){
                // 设置 rateInfo 字段可访问
                rateInfoField.setAccessible(true);
                // 获取 rateInfo 实例
                RateInfo rateInfoInstance = (RateInfo) rateInfoField.get(diffPopParam);
                rateInfoInstance.setNoNeedJoinDims(oneServicePopParamWrapper.getNoNeedJoinDims());
            }
        } catch (NoSuchFieldException e) {
            log.error("反射注入失败",e);
            e.printStackTrace();
        } catch (IllegalAccessException e) {
            log.error("反射注入失败",e);
            e.printStackTrace();
        }
    }

    private void checkParam(WmScThirdWorkplaceQueryBo queryBo) {
        if (StringUtil.isBlank(queryBo.getPageNo()) ||
                StringUtil.isBlank(queryBo.getPageSize())){
            throw new IllegalArgumentException("分页参数必填");
        }
        if (StringUtil.isBlank(queryBo.getStatisticalDataStr()) ||
                StringUtil.isBlank(queryBo.getStatisticalDimension())){
            throw new IllegalArgumentException("日期维度参数必填");
        }
        if (Objects.isNull(queryBo.getSchoolTrilateralOrgType())){
            throw new IllegalArgumentException("学校归属组织架构类型参数必填");
        }
    }

    private static List<String> getDimensionList(WmScThirdWorkplaceQueryBo queryBo) {
        List<String> demensionList = new ArrayList<>();
        demensionList.add(queryBo.getStatisticalDimension()); // 日期维度、所有场景必传
        return demensionList;
    }

    private void buildQueryContext(QueryContext queryContext, List<String> indexList, List<String> demensionList, WmScThirdWorkplaceQueryBo queryBo){

        queryContext.setQueryMode(QueryMode.AGGREGATION);
        queryContext.setQueryType(QueryType.QUERY);
        // 构造需要查询的字段列表
        queryContext.addIndex(indexList.toArray(new String[0]));
        // 构造维度列表
        queryContext.addDimension(demensionList.toArray(new String[0]));
        // 构造过滤条件
        queryContext.setFilter(buildQueryFilter(queryBo));
    }

    private List<QueryFilter> buildQueryFilter(WmScThirdWorkplaceQueryBo queryBo){
        List<QueryFilter> queryFilters = new ArrayList<>();
        // 构造过滤条件
        queryFilters.add(QueryFilter.between(queryBo.getStatisticalDimension()
                , queryBo.getStatisticalDataStr()
                , queryBo.getStatisticalDataStr()));

        // 学校id数组、列表查询时如果带有则表示仅查询选择的学校信息
        if (CollectionUtil.isNotEmpty(queryBo.getSchools())) {
            String[] values = queryBo.getSchools().stream()
                    .map(String::valueOf)
                    .toArray(String[]::new);
            queryFilters.add(QueryFilter.in("school_id", values));
        }

        // 校企负责人MIS数组
        if (queryBo.getSchoolEnterpriseManagers() != null && !queryBo.getSchoolEnterpriseManagers().isEmpty()) {
            String[] values = queryBo.getSchoolEnterpriseManagers().toArray(new String[0]);
            queryFilters.add(QueryFilter.in("school_responsible_person_id", values));
        }

        List<Integer> orgList = getQueryOrgList(queryBo);
        // 组织节点数组
        if (CollectionUtil.isNotEmpty(orgList) && !orgList.isEmpty()) {
            String[] values = orgList.stream()
                    .map(String::valueOf)
                    .toArray(String[]::new);
            queryFilters.add(QueryFilter.in("org_id", values));
        }

        // 学校归属组织架构类型
        if (queryBo.getSchoolTrilateralOrgType() != null) {
            queryFilters.add(QueryFilter.equal("school_trilateral_org_type_id", String.valueOf(queryBo.getSchoolTrilateralOrgType())));
        }

        // 学校业务类型
        if (queryBo.getSchoolBusinessType() != null) {
            queryFilters.add(QueryFilter.equal("zy_agent_aor_type_id", String.valueOf(queryBo.getSchoolBusinessType())));
        }

        // 学校分类
        if (queryBo.getSchoolCategory() != null && !queryBo.getSchoolCategory().isEmpty()) {
            String[] values = queryBo.getSchoolCategory().stream()
                    .map(String::valueOf)
                    .toArray(String[]::new);
            queryFilters.add(QueryFilter.in("school_category_id", values));
        }

        // 合作状态
        if (queryBo.getCooperationStatus() != null && !queryBo.getCooperationStatus().isEmpty()) {
            String[] values = queryBo.getCooperationStatus().stream()
                    .map(String::valueOf)
                    .toArray(String[]::new);
            queryFilters.add(QueryFilter.in("school_co_status_id", values));
        }

        // 签约类型
        if (queryBo.getContractTypes() != null && !queryBo.getContractTypes().isEmpty()) {
            String[] values = queryBo.getContractTypes().stream()
                    .map(String::valueOf)
                    .toArray(String[]::new);
            queryFilters.add(QueryFilter.in("school_signing_type_id", values));
        }

        Map<Integer, List<Integer>> cityGroup = groupByCityLevel(queryBo.getCities());

        if (!cityGroup.isEmpty()){
            // 1级物理城市
            if (cityGroup.containsKey(1)){
                String[] values = cityGroup.get(1).stream()
                        .map(String::valueOf)
                        .toArray(String[]::new);
                queryFilters.add(QueryFilter.in("first_physical_city_id", values));
            }

            // 2级物理城市
            if (cityGroup.containsKey(2)){
                String[] values = cityGroup.get(2).stream()
                        .map(String::valueOf)
                        .toArray(String[]::new);
                queryFilters.add(QueryFilter.in("second_physical_city_id", values));
            }
        }

        // 签约方式
        if (queryBo.getContractMethods() != null && !queryBo.getContractMethods().isEmpty()) {
            String[] values = queryBo.getContractMethods().stream()
                    .map(String::valueOf)
                    .toArray(String[]::new);
            queryFilters.add(QueryFilter.in("school_signing_method_id", values));
        }

        // 合同是否1-1
        if (queryBo.getContractOneToOne() != null) {
            queryFilters.add(QueryFilter.equal("school_is_exclusive_cooperation_id", String.valueOf(queryBo.getContractOneToOne())));
        }

        // 实时是否1-1
        if (queryBo.getRealOneToOne() != null) {
            queryFilters.add(QueryFilter.equal("is_exclusive_platform_cooperation_id", String.valueOf(queryBo.getRealOneToOne())));
        }

        // 合伙人阶段
        if (queryBo.getPartnerStage() != null && !queryBo.getPartnerStage().isEmpty()) {
            String[] values = queryBo.getPartnerStage().stream()
                    .map(String::valueOf)
                    .toArray(String[]::new);
            queryFilters.add(QueryFilter.in("school_partner_stage_id", values));
        }

        // 交付状态
        if (queryBo.getDeliveryStatus() != null && !queryBo.getDeliveryStatus().isEmpty()) {
            String[] values = queryBo.getDeliveryStatus().stream()
                    .map(String::valueOf)
                    .toArray(String[]::new);
            queryFilters.add(QueryFilter.in("contract_deliver_status_id", values));
        }

        // 流失状态
        if (queryBo.getChurnStatus() != null && !queryBo.getChurnStatus().isEmpty()) {
            String[] values = queryBo.getChurnStatus().stream()
                    .map(String::valueOf)
                    .toArray(String[]::new);
            queryFilters.add(QueryFilter.in("contract_loss_status_id", values));
        }

        queryFilters.add(QueryFilter.in("school_valid_id", "1"));

        return queryFilters;
    }

    private List<Integer> getQueryOrgList(WmScThirdWorkplaceQueryBo queryBo) {
        ThirdWorkplaceQueryRoleEnum queryRoleType = ThirdWorkplaceQueryRoleEnum.getByCode(queryBo.getSchoolTrilateralOrgType());
        // 获取当前用户
        SSOUtil.SsoUser user = Optional.ofNullable(SSOUtil.getUser())
                .orElseThrow(() -> new RuntimeException("获取用户信息失败"));

        List<Integer> orgList = new ArrayList<>();

        // 角色类型在ThirdWorkplaceService.getUserAssertResult判断
        switch (queryRoleType){
            case KA:
                // 用户未选择组织架构id的场景->根据用户uid查询组织架构所在节点id
                if (CollectionUtil.isEmpty(queryBo.getSchoolEnterpriseOrgStructure())){
                    orgList.add(getUserOrgId(user.getId(), WmVirtualOrgSourceEnum.WAIMAI.getSource()));
                }else{
                    // 用户已选择组织架构id->前端选择器会对权限进行控制
                    orgList.addAll(queryBo.getSchoolEnterpriseOrgStructure());
                }
                break;
            case PS:
                if (CollectionUtil.isEmpty(queryBo.getCampusDeliveryOrgStructure())){
                    orgList.add(getUserOrgId(user.getId(), WmVirtualOrgSourceEnum.XIAO_YUAN_JU_HE_PEI_SONG.getSource()));
                }else{
                    orgList.addAll(queryBo.getCampusDeliveryOrgStructure());
                }
                break;
            case CITY:
                if (CollectionUtil.isEmpty(queryBo.getCityOrgStructure())){
                    orgList.add(getUserOrgId(user.getId(), WmVirtualOrgSourceEnum.WAIMAI.getSource()));
                }else{
                    orgList.addAll(queryBo.getCityOrgStructure());
                }
                break;
            case ADMIN:
                // 总部场景默认查询城市，oneService侧无总部角色枚举
                queryBo.setSchoolTrilateralOrgType(ThirdWorkplaceQueryRoleEnum.CITY.getCode());

                // 总部优先查询城市节点->校企->配送渠道
                if (CollectionUtil.isNotEmpty(queryBo.getCityOrgStructure())){
                    orgList.addAll(queryBo.getCityOrgStructure());
                }else if (CollectionUtil.isNotEmpty(queryBo.getSchoolEnterpriseOrgStructure())){
                    queryBo.setSchoolTrilateralOrgType(ThirdWorkplaceQueryRoleEnum.KA.getCode());
                    orgList.addAll(queryBo.getSchoolEnterpriseOrgStructure());
                }else if (CollectionUtil.isNotEmpty(queryBo.getCampusDeliveryOrgStructure())){
                    queryBo.setSchoolTrilateralOrgType(ThirdWorkplaceQueryRoleEnum.PS.getCode());
                    orgList.addAll(queryBo.getCampusDeliveryOrgStructure());
                }else{
                    final Integer CITY_ROOT_ORG_ID = MccConfig.getAdminDefaultQueryOrgId();
                    // 如果都未选择则注入配置兜底节点id-城市跟节点
                    orgList.add(CITY_ROOT_ORG_ID);
                }
                break;
            case UNKNOWN:
                throw new IllegalArgumentException("未知查询角色类型");
        }

        if (CollectionUtil.isEmpty(orgList)){
            return Collections.emptyList();
        }
        // TODO 组织节点映射配置 线下测试需要
//        Map<String, Integer> orgIdMapping = MccConfig.getThirdWorkplaceOrgIdMapping();
//        List<Integer> result = new ArrayList<>();
//        orgList.forEach(orgId -> {
//            result.add(orgIdMapping.getOrDefault(String.valueOf(orgId), orgId));
//            log.info("getQueryOrgList 命中orgId映射规则,src:{},desc:{}", orgId, orgIdMapping.getOrDefault(String.valueOf(orgId), orgId));
//        });

        return orgList;
    }

    private Integer getUserOrgId(long id, byte source) {
        try {
            WmVirtualOrg org = wmOrgClient.getUidOrg((int) id, source);
            if (Objects.isNull(org)){
                return null;
            }
            return org.getId();
        }catch (WmCustomerException e){
            log.error("获取用户组织节点失败", e);
        }
        return null;
    }

    private OneServiceQueryMappingConfig getOneServiceQueryMappingConfig() throws WmSchCantException {
        try {
            return MccConfig.getOneServiceQueryMappingConfig();
        } catch (IOException e) {
            throw new WmSchCantException(BIZ_PARA_ERROR, "配置解析错误");
        }
    }

    private Map<Integer, List<Integer>> groupByCityLevel(List<Integer> cityIds) {
        if (CollectionUtils.isEmpty(cityIds)){
            return Collections.emptyMap();
        }
        try {
            Map<Integer, WmOpenCity> cityMap = wmOpenCityServiceAdapter.getCityMapByCityIdList(cityIds);
            Map<Integer, List<Integer>> levelGroupMap = new HashMap<>();

            for (Map.Entry<Integer, WmOpenCity> entry : cityMap.entrySet()) {
                Integer cityId = entry.getKey();
                WmOpenCity city = entry.getValue();

                if (Objects.isNull(city) || Objects.isNull(city.getCityAdLevel())) {
                    continue;
                }

                Integer cityAdLevel = city.getCityAdLevel();
                levelGroupMap.computeIfAbsent(cityAdLevel, k -> new ArrayList<>()).add(cityId);
            }
            return levelGroupMap;
        } catch (WmSchCantException e) {
            log.error("获取城市信息失败,cityIds:{}", cityIds, e);
            return Collections.emptyMap();
        }
    }
}
