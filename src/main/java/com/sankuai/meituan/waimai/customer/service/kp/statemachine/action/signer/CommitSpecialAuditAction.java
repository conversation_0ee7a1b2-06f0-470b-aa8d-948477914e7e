package com.sankuai.meituan.waimai.customer.service.kp.statemachine.action.signer;

import com.alibaba.fastjson.JSON;
import com.sankuai.meituan.waimai.customer.dao.WmCustomerKpDBMapper;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerSensitiveWordsService;
import com.sankuai.meituan.waimai.customer.service.kp.CustomerKpBusinessService;
import com.sankuai.meituan.waimai.customer.service.kp.WmCustomerKpLogService;
import com.sankuai.meituan.waimai.customer.service.kp.statemachine.action.KpSignerAbstractAction;
import com.sankuai.meituan.waimai.customer.service.kp.statemachine.constant.event.KpSignerEventEnum;
import com.sankuai.meituan.waimai.customer.service.kp.statemachine.context.KpSignerStatusMachineContext;
import com.sankuai.meituan.waimai.customer.service.kp.statemachine.machine.KpSignerBaseSM;
import com.sankuai.meituan.waimai.thrift.customer.constant.KpAuditConstants;
import com.sankuai.meituan.waimai.thrift.customer.constant.KpSignerStateMachine;
import com.sankuai.meituan.waimai.thrift.customer.domain.kp.WmCustomerKp;
import com.sankuai.meituan.waimai.thrift.exception.StatusMachineException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 20230828
 * @desc 提交特批审核事件
 */
@Service
@Slf4j
public class CommitSpecialAuditAction extends KpSignerAbstractAction {

    @Autowired
    private CustomerKpBusinessService customerKpBusinessService;

    @Autowired
    private WmCustomerKpLogService wmCustomerKpLogService;

    @Autowired
    private WmCustomerSensitiveWordsService wmCustomerSensitiveWordsService;

    @Autowired
    private WmCustomerKpDBMapper wmCustomerKpDBMapper;

    /**
     * KP签约人特批提审事件
     *
     * @param from
     * @param to
     * @param eventEnum
     * @param context
     * @param kpSignerBaseSM
     */
    @Override
    public void execute(KpSignerStateMachine from, KpSignerStateMachine to, KpSignerEventEnum eventEnum,
                        KpSignerStatusMachineContext context, KpSignerBaseSM kpSignerBaseSM) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_2", "c.s.m.waimai.customer.service.kp.statemachine.action.signer.CommitSpecialAuditAction.execute(KpSignerStateMachine,KpSignerStateMachine,KpSignerEventEnum,KpSignerStatusMachineContext,KpSignerBaseSM)");

        try {
            WmCustomerKp signerKp = context.getWmCustomerKp();
            log.info("CommitSpecialAuditAction.execute,开始提交特批审核，context={}", JSON.toJSONString(context));
            //设置上下文的提审类型为代理人审核
            context.setAuditType((int) KpAuditConstants.TYPE_SPECIAL);
            //是否生效标识
            boolean haveEffectFlag = context.getExistEffectiveFlag();
            //如果未生效
            if (!haveEffectFlag) {
                context.getWmCustomerKp().setState(KpSignerStateMachine.SPECILA_AUDIT_ING.getState());
                context.getWmCustomerKp().setFailReason("");
                //创建提审记录&提审
                customerKpBusinessService.commitKpAudit(context, KpAuditConstants.TYPE_SPECIAL);
                //更新KP数据
                wmCustomerSensitiveWordsService.writeKpWhenInsertOrUpdate(context.getWmCustomerKp());
                wmCustomerKpDBMapper.updateByPrimaryKeySelective(context.getWmCustomerKp());
                //记录状态变更
                wmCustomerKpLogService.changeState(signerKp.getCustomerId(), signerKp, "特批认证提交审核", context.getOpUid(), context.getOpUName());
            } else {
                //已生效KP
                context.getWmCustomerKp().setState(KpSignerStateMachine.CHANGE_SPECILA_AUDIT_ING.getState());
                context.getWmCustomerKp().setFailReason("");
                customerKpBusinessService.effectKpCommitAudit(context, KpAuditConstants.TYPE_SPECIAL);
            }

        } catch (Exception e) {
            log.error("CommitSpecialAuditAction.execute,提交特批审核发生异常,context={}", JSON.toJSONString(context), e);
            throw new StatusMachineException("提交特批审核发生异常");
        }

    }
}
