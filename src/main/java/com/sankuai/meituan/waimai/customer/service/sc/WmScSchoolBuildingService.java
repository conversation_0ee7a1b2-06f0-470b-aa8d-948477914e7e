package com.sankuai.meituan.waimai.customer.service.sc;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSONObject;
import com.sankuai.meituan.waimai.customer.adapter.BmLbsAoiExtThriftServiceAdapter;
import com.sankuai.meituan.waimai.customer.adapter.WmEmployClient;
import com.sankuai.meituan.waimai.customer.dao.sc.WmScSchoolAreaMapper;
import com.sankuai.meituan.waimai.customer.dao.sc.WmScSchoolBuildingMapper;
import com.sankuai.meituan.waimai.customer.dao.sc.WmSchoolMapper;
import com.sankuai.meituan.waimai.customer.domain.sc.*;
import com.sankuai.meituan.waimai.customer.domain.sc.area.WmScPoint;
import com.sankuai.meituan.waimai.customer.service.sc.area.WmRtreeUtil;
import com.sankuai.meituan.waimai.customer.util.WmScTransUtil;
import com.sankuai.meituan.waimai.infra.domain.WmEmploy;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.*;
import com.sankuai.meituan.waimai.thrift.customer.domain.sc.*;
import com.sankuai.meituan.waimai.thrift.customer.domain.sc.dto.WmScSchoolAoiDTO;
import com.sankuai.meituan.waimai.thrift.exception.WmSchCantException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.thrift.TException;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

import static com.sankuai.meituan.waimai.thrift.customer.constant.sc.ScConstants.SC_SCHOOL_BUILDING_LOG;
import static com.sankuai.meituan.waimai.thrift.customer.constant.sc.WmScCodeConstants.BIZ_PARA_ERROR;

/**
 * 学校楼宇信息处理逻辑
 * <AUTHOR>
 * @date 2023/06/04
 * @email <EMAIL>
 */
@Slf4j
@Service
public class WmScSchoolBuildingService {

    @Autowired
    private WmSchoolServerService wmSchoolServerService;

    @Autowired
    private WmScSchoolAreaMapper wmScSchoolAreaMapper;

    @Autowired
    private WmScSchoolAreaService wmScSchoolAreaService;

    @Autowired
    private WmScSchoolBuildingMapper wmScSchoolBuildingMapper;

    @Autowired
    private WmScLogSchoolInfoService wmScLogSchoolInfoService;

    @Autowired
    private CheckAreaInfoService checkAreaInfoService;

    @Autowired
    private BmLbsAoiExtThriftServiceAdapter bmLbsAoiExtThriftServiceAdapter;

    @Autowired
    private WmSchoolMapper wmSchoolMapper;

    @Autowired
    private WmEmployClient wmEmployClient;

    /**
     * 查询学校楼宇信息列表
     * @param wmScSchoolBuildingQueryDTO wmScSchoolBuildingQueryDTO
     * @return WmScSchoolBuildingListDTO 学校楼宇列表信息
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     * @throws Exception java.lang.Exception
     */
    public WmScSchoolBuildingListDTO getSchoolBuildingList(WmScSchoolBuildingQueryDTO wmScSchoolBuildingQueryDTO) throws WmSchCantException, Exception {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_1", "com.sankuai.meituan.waimai.customer.service.sc.WmScSchoolBuildingService.getSchoolBuildingList(com.sankuai.meituan.waimai.thrift.customer.domain.sc.WmScSchoolBuildingQueryDTO)");
        log.info("[WmScSchoolBuildingService.getSchoolBuildingList] input param: wmScSchoolBuildingQueryDTO = {}", JSONObject.toJSONString(wmScSchoolBuildingQueryDTO));
        if (wmScSchoolBuildingQueryDTO == null || wmScSchoolBuildingQueryDTO.getSchoolPrimaryId() == null
                || wmScSchoolBuildingQueryDTO.getSchoolPrimaryId() <= 0) {
            throw new WmSchCantException(BIZ_PARA_ERROR, "查询学校楼宇信息参数错误");
        }
        WmScSchoolBuildingListDTO wmScSchoolBuildingListDTO = new WmScSchoolBuildingListDTO();

        // 查询学校地址信息
        SchoolBo schoolBo = wmSchoolServerService.selectSchoolById(wmScSchoolBuildingQueryDTO.getSchoolPrimaryId());
        wmScSchoolBuildingListDTO.setSchoolAddress(schoolBo.getSchoolAddress());
        wmScSchoolBuildingListDTO.setSchoolPrimaryId(schoolBo.getId());
        wmScSchoolBuildingListDTO.setAuditStatus((int) SchoolAuditStatusEnum.FORENTRY.getType());
        wmScSchoolBuildingListDTO.setAuditStatusDesc(SchoolAuditStatusEnum.FORENTRY.getName());

        // 查询学校范围信息
        List<WmScSchoolAreaDO> wmScSchoolAreaDOList = wmScSchoolAreaMapper.selectBySchoolId(wmScSchoolBuildingQueryDTO.getSchoolPrimaryId());
        if (CollectionUtils.isEmpty(wmScSchoolAreaDOList)) {
            wmScSchoolBuildingListDTO.setWmScSchoolAoiDTOList(new ArrayList<>());
            wmScSchoolBuildingListDTO.setSchoolBuildingInfo(new ArrayList<>());
            log.info("[WmScSchoolBuildingService.getSchoolBuildingList] wmScSchoolAreaDOList is empty. schoolPrimaryId = {}", wmScSchoolBuildingQueryDTO.getSchoolPrimaryId());
            return wmScSchoolBuildingListDTO;
        }
        List<WmScSchoolAoiBO> wmScSchoolAoiBOList = wmScSchoolAreaService.getWmScSchoolAoiBOList(wmScSchoolAreaDOList);
        wmScSchoolBuildingListDTO.setWmScSchoolAoiDTOList(WmScSchoolAoiBO.transWmScSchoolAoiBoToDto(wmScSchoolAoiBOList));

        // 查询学校楼宇信息
        List<WmScSchoolBuildingDO> wmScSchoolBuildingDOList = getSchoolBuildingDOList(wmScSchoolBuildingQueryDTO);
        if (CollectionUtils.isEmpty(wmScSchoolBuildingDOList)) {
            wmScSchoolBuildingListDTO.setSchoolBuildingInfo(new ArrayList<>());
            log.info("[WmScSchoolBuildingService.getSchoolBuildingList] wmScSchoolBuildingDOList is empty. schoolPrimaryId = {}", wmScSchoolBuildingQueryDTO.getSchoolPrimaryId());
            return wmScSchoolBuildingListDTO;
        }

        List<WmScSchoolBuildingDetailBO> wmScSchoolBuildingDetailBOList = WmScTransUtil.transWmScSchoolBuildingDetailDOListToBOList(wmScSchoolBuildingDOList);
        wmScSchoolBuildingListDTO.setSchoolBuildingInfo(WmScTransUtil.transSchoolBuildingDetailBoToDto(wmScSchoolBuildingDetailBOList));
        wmScSchoolBuildingListDTO.setAuditStatus((int) SchoolAuditStatusEnum.PASS.getType());
        wmScSchoolBuildingListDTO.setAuditStatusDesc(SchoolAuditStatusEnum.PASS.getName());

        log.info("[WmScSchoolBuildingService.getSchoolBuildingList] output result: wmScSchoolBuildingListDTO = {}", JSONObject.toJSONString(wmScSchoolBuildingListDTO));
        return wmScSchoolBuildingListDTO;
    }

    /**
     * 查询学校楼宇信息列表
     * @param wmScSchoolBuildingQueryDTO wmScSchoolBuildingQueryDTO
     * @return 楼宇信息列表
     */
    public List<WmScSchoolBuildingDO> getSchoolBuildingDOList(WmScSchoolBuildingQueryDTO wmScSchoolBuildingQueryDTO) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_1", "com.sankuai.meituan.waimai.customer.service.sc.WmScSchoolBuildingService.getSchoolBuildingDOList(com.sankuai.meituan.waimai.thrift.customer.domain.sc.WmScSchoolBuildingQueryDTO)");
        WmScSchoolBuildingSearchCondition condition = new WmScSchoolBuildingSearchCondition();
        condition.setId(wmScSchoolBuildingQueryDTO.getBuildingId());
        condition.setSchoolPrimaryId(wmScSchoolBuildingQueryDTO.getSchoolPrimaryId());
        condition.setBuildingName(wmScSchoolBuildingQueryDTO.getBuildingName());
        condition.setBuildingType(wmScSchoolBuildingQueryDTO.getBuildingType());
        condition.setBuildingLocation(wmScSchoolBuildingQueryDTO.getBuildingLocation());
        condition.setBuildingStatus(wmScSchoolBuildingQueryDTO.getBuildingStatus());
        condition.setBuildingElevator(wmScSchoolBuildingQueryDTO.getBuildingElevator());
        return wmScSchoolBuildingMapper.selectByCondition(condition);
    }

    /**
     * 学校楼宇信息页面-同步学校楼宇POI信息
     * @param wmScSchoolBuildingPoiInfoSyncDTO 学校楼宇POI信息同步DTO对象
     * @throws TException org.apache.thrift.TException
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    public void syncSchoolBuildingPoiInfo(WmScSchoolBuildingPoiInfoSyncDTO wmScSchoolBuildingPoiInfoSyncDTO) throws TException, WmSchCantException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.service.sc.WmScSchoolBuildingService.syncSchoolBuildingPoiInfo(com.sankuai.meituan.waimai.thrift.customer.domain.sc.WmScSchoolBuildingPoiInfoSyncDTO)");
        log.info("[syncSchoolBuildingPoiInfo] input param: wmScSchoolBuildingPoiInfoSyncDTO = {}", wmScSchoolBuildingPoiInfoSyncDTO.toString());
        Integer schoolPrimaryId = wmScSchoolBuildingPoiInfoSyncDTO.getSchoolPrimaryId();
        Integer userId = wmScSchoolBuildingPoiInfoSyncDTO.getUserId();
        String userName = wmScSchoolBuildingPoiInfoSyncDTO.getUserName();

        // 查询学校的所有AOI ID
        List<Long> schoolAoiIdList = wmScSchoolAreaService.getSchoolAoiIdListBySchoolPrimaryId(schoolPrimaryId);
        if (CollectionUtils.isEmpty(schoolAoiIdList)) {
            log.info("[syncSchoolBuildingPoiInfo] schoolAoiIdList is empty. schoolPrimaryId = {}", schoolPrimaryId);
            throw new WmSchCantException(BIZ_PARA_ERROR, "学校AOI ID信息未填写");
        }

        // 根据AOI ID列表从履约侧查询POI信息列表
        List<WmScSchoolBuildingPoiBO> schoolBuildingPoiBoList = bmLbsAoiExtThriftServiceAdapter.getSchoolBuildingPoiListByAoiIdList(schoolAoiIdList);
        if (CollectionUtils.isEmpty(schoolBuildingPoiBoList)) {
            log.info("[syncSchoolBuildingPoiInfo] schoolBuildingPoiBoList is empty. schoolPrimaryId = {}, schoolAoiIdList = {}",
                    schoolPrimaryId, JSONObject.toJSONString(schoolAoiIdList));
            return;
        }

        // 查询学校楼宇的所有POI ID
        Map<Long, WmScSchoolBuildingDO> wmScSchoolBuildingDoMap = getSchoolBuildingPoiMap(schoolPrimaryId);
        log.info("[syncSchoolBuildingPoiInfo] schoolPrimaryId = {}, schoolBuildingPoiBoList = {}, wmScSchoolBuildingDoMap = {}",
                schoolPrimaryId, JSONObject.toJSONString(schoolBuildingPoiBoList), JSONObject.toJSONString(wmScSchoolBuildingDoMap));
        for (WmScSchoolBuildingPoiBO wmScSchoolBuildingPoiBO : schoolBuildingPoiBoList) {
            WmScSchoolBuildingDO wmScSchoolBuildingDO = wmScSchoolBuildingDoMap.get(wmScSchoolBuildingPoiBO.getPoiId());
            if (wmScSchoolBuildingDO != null) {
                // POI信息已存在, 执行更新
                updateSchoolBuildingPoiInfo(wmScSchoolBuildingPoiBO, wmScSchoolBuildingDO, userId, userName);
            } else {
                // POI信息不存在, 执行新增
                insertSchoolBuildingPoiInfo(wmScSchoolBuildingPoiBO, schoolPrimaryId, userId, userName);
            }
        }
    }

    /**
     * 定时任务-同步更新已有的学校POI信息(POI名称和坐标)
     * @param schoolPrimaryId 学校主键ID
     * @param userId 用户ID
     * @param userName 用户名称
     * @throws TException org.apache.thrift.TException
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    public void syncSchoolBuildingExistingPoiInfo(Integer schoolPrimaryId, Integer userId, String userName) throws TException, WmSchCantException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.sc.WmScSchoolBuildingService.syncSchoolBuildingExistingPoiInfo(java.lang.Integer,java.lang.Integer,java.lang.String)");
        log.info("[syncSchoolBuildingExistingPoiInfo] input param: schoolPrimaryId = {}, userId = {}, userName = {}", schoolPrimaryId, userId, userName);
        // 查询学校的所有AOI ID信息
        List<Long> schoolAoiIdList = wmScSchoolAreaService.getSchoolAoiIdListBySchoolPrimaryId(schoolPrimaryId);
        if (CollectionUtils.isEmpty(schoolAoiIdList)) {
            log.info("[syncSchoolBuildingExistingPoiInfo] schoolAoiIdList is empty. schoolPrimaryId = {}", schoolPrimaryId);
            return;
        }

        // 查询该学校下的所有楼宇POI信息
        List<WmScSchoolBuildingDO> wmScSchoolBuildingDOList = wmScSchoolBuildingMapper.selectBySchoolPrimaryId(schoolPrimaryId);
        if (CollectionUtils.isEmpty(wmScSchoolBuildingDOList)) {
            log.info("[syncSchoolBuildingExistingPoiInfo] wmScSchoolBuildingDOList is empty. schoolPrimaryId = {}", schoolPrimaryId);
            return;
        }

        // 从履约侧查询该学校下的所有楼宇POI信息
        List<WmScSchoolBuildingPoiBO> schoolBuildingPoiBoList = bmLbsAoiExtThriftServiceAdapter.getSchoolBuildingPoiListByAoiIdList(schoolAoiIdList);
        if (CollectionUtils.isEmpty(schoolBuildingPoiBoList)) {
            log.info("[syncSchoolBuildingExistingPoiInfo] schoolBuildingPoiBoList is empty. schoolAoiIdList = {}", JSONObject.toJSONString(schoolAoiIdList));
            return;
        }
        // 将履约侧查询的所有楼宇POI信息组装为map
        Map<Long, WmScSchoolBuildingPoiBO> map = new HashMap<>();
        for (WmScSchoolBuildingPoiBO wmScSchoolBuildingPoiBO : schoolBuildingPoiBoList) {
            if (wmScSchoolBuildingPoiBO.getPoiId() != null && wmScSchoolBuildingPoiBO.getPoiId() > 0) {
                map.put(wmScSchoolBuildingPoiBO.getPoiId(), wmScSchoolBuildingPoiBO);
            }
        }

        for (WmScSchoolBuildingDO wmScSchoolBuildingDO : wmScSchoolBuildingDOList) {
            // 手动添加的楼宇, 无需同步更新
            if (wmScSchoolBuildingDO.getBuildingPoiId() == null || wmScSchoolBuildingDO.getBuildingPoiId() == 0) {
                continue;
            }
            // 履约侧不存在该POI信息
            if (map.get(wmScSchoolBuildingDO.getBuildingPoiId()) == null) {
                log.warn("[syncSchoolBuildingExistingPoiInfo] wmScSchoolBuildingDO is not exist from LBS. wmScSchoolBuildingDO = {}", JSONObject.toJSONString(wmScSchoolBuildingDO));
                continue;
            }
            // 更新楼宇的POI信息
            updateSchoolBuildingPoiInfo(map.get(wmScSchoolBuildingDO.getBuildingPoiId()), wmScSchoolBuildingDO, userId, userName);
        }
    }

    /**
     * 根据学校主键ID查询学校楼宇POI ID MAP
     * @param schoolPrimaryId 学校主键ID
     * @return Map<Integer, WmScSchoolBuildingDO> key=poi id
     */
    public Map<Long, WmScSchoolBuildingDO> getSchoolBuildingPoiMap(Integer schoolPrimaryId) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.service.sc.WmScSchoolBuildingService.getSchoolBuildingPoiMap(java.lang.Integer)");
        log.info("[WmScSchoolBuildingService.getSchoolBuildingPoiIdList] input param: schoolPrimaryId = {}", schoolPrimaryId);
        Map<Long, WmScSchoolBuildingDO> map = new HashMap<>();
        WmScSchoolBuildingSearchCondition condition = new WmScSchoolBuildingSearchCondition();
        condition.setSchoolPrimaryId(schoolPrimaryId);
        List<WmScSchoolBuildingDO> wmScSchoolBuildingDOList = wmScSchoolBuildingMapper.selectByCondition(condition);
        if (CollectionUtils.isEmpty(wmScSchoolBuildingDOList)) {
            return map;
        }

        for (WmScSchoolBuildingDO wmScSchoolBuildingDO : wmScSchoolBuildingDOList) {
            if (wmScSchoolBuildingDO.getBuildingPoiId() != null && wmScSchoolBuildingDO.getBuildingPoiId() > 0) {
                map.put(wmScSchoolBuildingDO.getBuildingPoiId(), wmScSchoolBuildingDO);
            }
        }
        return map;
    }

    /**
     * 更新学校楼宇POI信息
     * @param wmScSchoolBuildingPoiBO 从履约侧查询得到的楼宇POI BO
     * @param wmScSchoolBuildingDO 从数据库查询得到的POI DO
     * @param userId 用户ID
     * @param userName 用户名称
     */
    public void updateSchoolBuildingPoiInfo(WmScSchoolBuildingPoiBO wmScSchoolBuildingPoiBO, WmScSchoolBuildingDO wmScSchoolBuildingDO,
                                            Integer userId, String userName) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.service.sc.WmScSchoolBuildingService.updateSchoolBuildingPoiInfo(WmScSchoolBuildingPoiBO,WmScSchoolBuildingDO,Integer,String)");
        log.info("[WmScSchoolBuildingService.updateSchoolBuildingPoiInfo] input param: wmScSchoolBuildingPoiBO = {}, wmScSchoolBuildingDO = {}, userId = {}, userName = {}",
                JSONObject.toJSONString(wmScSchoolBuildingPoiBO), JSONObject.toJSONString(wmScSchoolBuildingDO), userId, userName);
        // POI相关信息无变化, 则无需更新
        if (wmScSchoolBuildingPoiBO.getPoiName().equals(wmScSchoolBuildingDO.getBuildingName())
                && wmScSchoolBuildingPoiBO.getPoiCoordinate().equals(wmScSchoolBuildingDO.getBuildingCoordinate())) {
            log.info("[updateSchoolBuildingPoiInfo] poiName and poiCoordinate are not changed. wmScSchoolBuildingPoiBO = {}", JSONObject.toJSONString(wmScSchoolBuildingPoiBO));
            return;
        }

        WmScSchoolBuildingDO wmScSchoolBuildingDoUpdate = new WmScSchoolBuildingDO();
        wmScSchoolBuildingDoUpdate.setId(wmScSchoolBuildingDO.getId());
        // 设置变更的学校楼宇POI字段
        wmScSchoolBuildingDoUpdate = setUpdateSchoolBuildingPoiInfo(wmScSchoolBuildingDO, wmScSchoolBuildingDoUpdate, wmScSchoolBuildingPoiBO);
        wmScSchoolBuildingDoUpdate.setMuid(userId.longValue());
        // 计算楼宇状态
        WmScSchoolBuildingDO wmScSchoolBuildingDOTemp = new WmScSchoolBuildingDO();
        BeanUtils.copyProperties(wmScSchoolBuildingDO, wmScSchoolBuildingDOTemp);
        wmScSchoolBuildingDOTemp = setUpdateSchoolBuildingPoiInfo(wmScSchoolBuildingDO, wmScSchoolBuildingDOTemp, wmScSchoolBuildingPoiBO);
        Integer buildingStatus = getSchoolBuildingStatus(wmScSchoolBuildingDOTemp);
        if (!wmScSchoolBuildingDO.getBuildingStatus().equals(buildingStatus)) {
            wmScSchoolBuildingDoUpdate.setBuildingStatus(buildingStatus);
        }

        int result = wmScSchoolBuildingMapper.updateByPrimaryKeySelective(wmScSchoolBuildingDoUpdate);
        if (result > 0) {
            // 记录到操作日志
            BeanUtils.copyProperties(wmScSchoolBuildingDO, wmScSchoolBuildingDoUpdate);
            wmScSchoolBuildingDoUpdate = setUpdateSchoolBuildingPoiInfo(wmScSchoolBuildingDO, wmScSchoolBuildingDoUpdate, wmScSchoolBuildingPoiBO);
            if (!wmScSchoolBuildingDO.getBuildingStatus().equals(buildingStatus)) {
                wmScSchoolBuildingDoUpdate.setBuildingStatus(buildingStatus);
            }

            WmScSchoolBuildingLogBO wmScSchoolBuildingLogBO = new WmScSchoolBuildingLogBO();
            wmScSchoolBuildingLogBO.setUserId(userId);
            wmScSchoolBuildingLogBO.setUserName(userName);
            wmScSchoolBuildingLogBO.setWmScSchoolBuildingDoBefore(wmScSchoolBuildingDO);
            wmScSchoolBuildingLogBO.setWmScSchoolBuildingDoAfter(wmScSchoolBuildingDoUpdate);
            wmScLogSchoolInfoService.recordSchoolBuildingUpdateLog(wmScSchoolBuildingLogBO);
        }
        log.info("[WmScSchoolBuildingService.updateSchoolBuildingPoiInfo] update school building success. schoolPrimaryId = {}, wmScSchoolBuildingDoUpdate = {}",
                wmScSchoolBuildingDO.getSchoolPrimaryId(), JSONObject.toJSONString(wmScSchoolBuildingDoUpdate));
    }

    /**
     * 更改楼宇位置信息: 校外->校内，校内->校外
     * @param wmScSchoolBuildingDOListNotMatch 楼宇信息列表
     * @param userId 操作者ID
     * @param userName 操作者名称
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    public void updateSchoolBuildingLocation(List<WmScSchoolBuildingDO> wmScSchoolBuildingDOListNotMatch, Integer userId, String userName)
            throws WmSchCantException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_1", "com.sankuai.meituan.waimai.customer.service.sc.WmScSchoolBuildingService.updateSchoolBuildingLocation(java.util.List,java.lang.Integer,java.lang.String)");
        log.info("[WmScSchoolBuildingService.updateSchoolBuildingLocation] input param: wmScSchoolBuildingDOListNotMatch = {}",
                JSONObject.toJSONString(wmScSchoolBuildingDOListNotMatch));
        if (CollectionUtils.isEmpty(wmScSchoolBuildingDOListNotMatch)) {
            log.info("[WmScSchoolBuildingService.updateSchoolBuildingLocation] wmScSchoolBuildingDOListNotMatch is empty, return.");
            return;
        }

        for (WmScSchoolBuildingDO wmScSchoolBuildingDOBefore : wmScSchoolBuildingDOListNotMatch) {
            WmScSchoolBuildingDO wmScSchoolBuildingDOUpdate = new WmScSchoolBuildingDO();
            wmScSchoolBuildingDOUpdate.setId(wmScSchoolBuildingDOBefore.getId());
            if (wmScSchoolBuildingDOBefore.getBuildingLocation().equals((int) SchoolBuildingLocationEnum.INSCHOOL.getType())) {
                // 楼宇位置调整: 校内->校外
                wmScSchoolBuildingDOUpdate.setBuildingLocation((int) SchoolBuildingLocationEnum.OUTOFSCHOOL.getType());
            } else if (wmScSchoolBuildingDOBefore.getBuildingLocation().equals((int) SchoolBuildingLocationEnum.OUTOFSCHOOL.getType())) {
                // 楼宇位置调整: 校外->校内
                wmScSchoolBuildingDOUpdate.setBuildingLocation((int) SchoolBuildingLocationEnum.INSCHOOL.getType());
            }
            wmScSchoolBuildingDOUpdate.setMuid(userId.longValue());

            int result = wmScSchoolBuildingMapper.updateByPrimaryKeySelective(wmScSchoolBuildingDOUpdate);
            if (result > 0) {
                // 记录到操作日志
                BeanUtils.copyProperties(wmScSchoolBuildingDOBefore, wmScSchoolBuildingDOUpdate);
                if (wmScSchoolBuildingDOBefore.getBuildingLocation().equals((int) SchoolBuildingLocationEnum.INSCHOOL.getType())) {
                    // 楼宇位置调整: 校内->校外
                    wmScSchoolBuildingDOUpdate.setBuildingLocation((int) SchoolBuildingLocationEnum.OUTOFSCHOOL.getType());
                } else if (wmScSchoolBuildingDOBefore.getBuildingLocation().equals((int) SchoolBuildingLocationEnum.OUTOFSCHOOL.getType())) {
                    // 楼宇位置调整: 校外->校内
                    wmScSchoolBuildingDOUpdate.setBuildingLocation((int) SchoolBuildingLocationEnum.INSCHOOL.getType());
                }

                WmScSchoolBuildingLogBO wmScSchoolBuildingLogBO = new WmScSchoolBuildingLogBO();
                wmScSchoolBuildingLogBO.setUserId(userId);
                wmScSchoolBuildingLogBO.setUserName(userName);
                wmScSchoolBuildingLogBO.setWmScSchoolBuildingDoBefore(wmScSchoolBuildingDOBefore);
                wmScSchoolBuildingLogBO.setWmScSchoolBuildingDoAfter(wmScSchoolBuildingDOUpdate);
                wmScLogSchoolInfoService.recordSchoolBuildingUpdateLog(wmScSchoolBuildingLogBO);
            }
            log.info("[WmScSchoolBuildingService.updateSchoolBuildingLocation] update school building success. schoolPrimaryId = {}, wmScSchoolBuildingDoUpdate = {}",
                    wmScSchoolBuildingDOUpdate.getSchoolPrimaryId(), JSONObject.toJSONString(wmScSchoolBuildingDOUpdate));
        }
    }

    /**
     * 设置变更的学校楼宇POI字段
     * @param wmScSchoolBuildingDoBefore 变更前DO
     * @param wmScSchoolBuildingDoUpdate 变更后DO
     * @param wmScSchoolBuildingPoiBO 学校楼宇POI BO
     */
    public WmScSchoolBuildingDO setUpdateSchoolBuildingPoiInfo(WmScSchoolBuildingDO wmScSchoolBuildingDoBefore,
                                               WmScSchoolBuildingDO wmScSchoolBuildingDoUpdate,
                                               WmScSchoolBuildingPoiBO wmScSchoolBuildingPoiBO) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_1", "com.sankuai.meituan.waimai.customer.service.sc.WmScSchoolBuildingService.setUpdateSchoolBuildingPoiInfo(WmScSchoolBuildingDO,WmScSchoolBuildingDO,WmScSchoolBuildingPoiBO)");
        // 楼宇名称
        if (!wmScSchoolBuildingDoBefore.getBuildingName().equals(wmScSchoolBuildingPoiBO.getPoiName())) {
            wmScSchoolBuildingDoUpdate.setBuildingName(wmScSchoolBuildingPoiBO.getPoiName());
        }
        // 楼宇坐标
        if (!wmScSchoolBuildingDoBefore.getBuildingCoordinate().equals(wmScSchoolBuildingPoiBO.getPoiCoordinate())) {
            wmScSchoolBuildingDoUpdate.setBuildingCoordinate(wmScSchoolBuildingPoiBO.getPoiCoordinate());
        }
        return wmScSchoolBuildingDoUpdate;
    }

    /**
     * 新增学校楼宇POI信息
     * @param wmScSchoolBuildingPoiBO wmScSchoolBuildingPoiBO
     * @param userId 用户ID
     * @param userName 用户名称
     */
    public void insertSchoolBuildingPoiInfo(WmScSchoolBuildingPoiBO wmScSchoolBuildingPoiBO, Integer schoolPrimaryId, Integer userId, String userName) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.sc.WmScSchoolBuildingService.insertSchoolBuildingPoiInfo(WmScSchoolBuildingPoiBO,Integer,Integer,String)");
        log.info("[WmScSchoolBuildingService.insertSchoolBuildingPoiInfo] input param: wmScSchoolBuildingPoiBO = {}, schoolPrimaryId = {}, userId = {}, userName = {}",
                    JSONObject.toJSONString(wmScSchoolBuildingPoiBO), schoolPrimaryId, userId, userName);
        WmScSchoolBuildingDO wmScSchoolBuildingDoInsert = new WmScSchoolBuildingDO();
        wmScSchoolBuildingDoInsert.setSchoolPrimaryId(schoolPrimaryId);
        wmScSchoolBuildingDoInsert.setBuildingPoiId(wmScSchoolBuildingPoiBO.getPoiId());
        wmScSchoolBuildingDoInsert.setBuildingName(wmScSchoolBuildingPoiBO.getPoiName());
        wmScSchoolBuildingDoInsert.setBuildingCoordinate(wmScSchoolBuildingPoiBO.getPoiCoordinate());
        // 楼宇位置默认为"校内"
        wmScSchoolBuildingDoInsert.setBuildingLocation((int) SchoolBuildingLocationEnum.INSCHOOL.getType());
        wmScSchoolBuildingDoInsert.setCuid(userId.longValue());
        wmScSchoolBuildingDoInsert.setMuid(userId.longValue());
        // 学校楼宇范围字段设置默认值
        if (StringUtils.isEmpty(wmScSchoolBuildingDoInsert.getBuildingArea())) {
            wmScSchoolBuildingDoInsert.setBuildingArea("[]");
        }

        int result = wmScSchoolBuildingMapper.insertSelective(wmScSchoolBuildingDoInsert);
        if (result > 0) {
            // 记录到操作日志
            WmScSchoolBuildingLogBO wmScSchoolBuildingLogBO = new WmScSchoolBuildingLogBO();
            wmScSchoolBuildingLogBO.setUserId(userId);
            wmScSchoolBuildingLogBO.setUserName(userName);
            wmScSchoolBuildingLogBO.setWmScSchoolBuildingDoInsert(wmScSchoolBuildingDoInsert);
            wmScLogSchoolInfoService.recordSchoolBuildingInsertLog(wmScSchoolBuildingLogBO);
        }
        log.info("[WmScSchoolBuildingService.insertSchoolBuildingPoiInfo] insert school building success. schoolPrimaryId = {}, wmScSchoolBuildingDoInsert = {}",
                schoolPrimaryId, JSONObject.toJSONString(wmScSchoolBuildingDoInsert));
    }

    /**
     * 删除学校楼宇信息
     * @param wmScSchoolBuildingDO 楼宇信息
     * @param userId 用户ID
     * @param userName 用户名称
     */
    public void deleteSchoolBuilding(WmScSchoolBuildingDO wmScSchoolBuildingDO, Integer userId, String userName) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_2", "com.sankuai.meituan.waimai.customer.service.sc.WmScSchoolBuildingService.deleteSchoolBuilding(com.sankuai.meituan.waimai.customer.domain.sc.WmScSchoolBuildingDO,java.lang.Integer,java.lang.String)");
        log.info("[WmScSchoolBuildingService.deleteSchoolBuilding] input param: wmScSchoolBuildingDO = {}, userId = {}, userName = {}",
                JSONObject.toJSONString(wmScSchoolBuildingDO), userId, userName);

        WmScSchoolBuildingDO wmScSchoolBuildingDoDelete = new WmScSchoolBuildingDO();
        wmScSchoolBuildingDoDelete.setId(wmScSchoolBuildingDO.getId());
        wmScSchoolBuildingDoDelete.setValid(ScConstants.SC_IS_DELETE);
        wmScSchoolBuildingDoDelete.setMuid(userId.longValue());

        int result = wmScSchoolBuildingMapper.updateByPrimaryKeySelective(wmScSchoolBuildingDoDelete);
        if (result > 0) {
            String logInfo = wmScLogSchoolInfoService.composeSchoolBuildingDeleteLog(wmScSchoolBuildingDO);
            if (StringUtils.isNotBlank(logInfo)) {
                wmScLogSchoolInfoService.insertScOptLog(OptTypeEnum.UNBAND.getType(), SC_SCHOOL_BUILDING_LOG, wmScSchoolBuildingDO.getSchoolPrimaryId(),
                        userId, userName, logInfo, "");
            }
        }
        log.info("[WmScSchoolBuildingService.deleteSchoolBuilding] delete school building success. wmScSchoolBuildingDoDelete = {}",
                JSONObject.toJSONString(wmScSchoolBuildingDoDelete));
    }

    /**
     * 删除学校楼宇信息(单条)
     * @param buildingId 楼宇主键ID
     * @param userId 用户ID
     * @param userName 用户名称
     */
    public void deleteSchoolBuildingById(Integer buildingId, Integer userId, String userName) throws WmSchCantException, TException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.service.sc.WmScSchoolBuildingService.deleteSchoolBuildingById(java.lang.Integer,java.lang.Integer,java.lang.String)");
        log.info("[WmScSchoolBuildingService.deleteSchoolBuildingById] input param: buildingId = {}, userId = {}, userName = {}",
                buildingId, userId, userName);

        WmScSchoolBuildingDO wmScSchoolBuildingDO = wmScSchoolBuildingMapper.selectByPrimaryId(buildingId);
        if (wmScSchoolBuildingDO == null) {
            log.error("[WmScSchoolBuildingService.deleteSchoolBuildingById] wmScSchoolBuildingDO is null. buildingId = {}", buildingId);
            throw new WmSchCantException(BIZ_PARA_ERROR, "被删除的楼宇不存在");
        }

        // 执行逻辑删除
        int result = wmScSchoolBuildingMapper.invalidByPrimaryKey(buildingId.longValue(), userId.longValue());
        if (result > 0) {
            String logInfo = wmScLogSchoolInfoService.composeSchoolBuildingDeleteLog(wmScSchoolBuildingDO);
            if (StringUtils.isNotBlank(logInfo)) {
                wmScLogSchoolInfoService.insertScOptLog(OptTypeEnum.DELETE.getType(), SC_SCHOOL_BUILDING_LOG, wmScSchoolBuildingDO.getSchoolPrimaryId(),
                        userId, userName, logInfo, "");
            }
        }
        log.info("[WmScSchoolBuildingService.deleteSchoolBuildingById] delete school building success. buildingId = {}", buildingId);
    }

    /**
     * 删除学校下的所有楼宇信息
     * @param schoolPrimaryId 学校主键ID
     * @param userId 用户ID
     * @param userName 用户名称
     */
    public void deleteAllSchoolBuilding(Integer schoolPrimaryId, Integer userId, String userName) throws WmSchCantException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.service.sc.WmScSchoolBuildingService.deleteAllSchoolBuilding(java.lang.Integer,java.lang.Integer,java.lang.String)");
        log.info("[deleteAllSchoolBuilding] input param: schoolPrimaryId = {}, userId = {}, userName = {}", schoolPrimaryId, userId, userName);
        if (schoolPrimaryId == null || schoolPrimaryId <= 0) {
            throw new WmSchCantException(BIZ_PARA_ERROR, "学校ID为空");
        }
        // 查询该学校下的所有楼宇信息
        List<WmScSchoolBuildingDO> wmScSchoolBuildingDOList = wmScSchoolBuildingMapper.selectBySchoolPrimaryId(schoolPrimaryId);
        if (CollectionUtils.isEmpty(wmScSchoolBuildingDOList)) {
            log.info("[deleteAllSchoolBuilding] wmScSchoolBuildingDOList is empty. schoolPrimaryId = {}", schoolPrimaryId);
            return;
        }

        for (WmScSchoolBuildingDO wmScSchoolBuildingDO : wmScSchoolBuildingDOList) {
            deleteSchoolBuilding(wmScSchoolBuildingDO, userId, userName);
        }
    }

    /**
     * 计算学校楼宇状态
     * @param wmScSchoolBuildingDO wmScSchoolBuildingDO
     * @return 楼宇状态
     */
    public Integer getSchoolBuildingStatus(WmScSchoolBuildingDO wmScSchoolBuildingDO) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.service.sc.WmScSchoolBuildingService.getSchoolBuildingStatus(com.sankuai.meituan.waimai.customer.domain.sc.WmScSchoolBuildingDO)");
        if (wmScSchoolBuildingDO == null) {
            return (int) SchoolBuildingStatusEnum.INVALID.getType();
        }
        // 1-楼宇扎点或楼宇范围信息未填写:楼宇状态=失效
        if (StringUtils.isBlank(wmScSchoolBuildingDO.getBuildingCoordinate())
            || StringUtils.isBlank(wmScSchoolBuildingDO.getBuildingArea())
            || "[]".equals(wmScSchoolBuildingDO.getBuildingArea())
            || "[]".equals(wmScSchoolBuildingDO.getBuildingCoordinate())) {
            return (int) SchoolBuildingStatusEnum.INVALID.getType();
        }
        // 学校范围列表
        List<WmScSchoolAreaDO> wmScSchoolAreaDOList = wmScSchoolAreaMapper.selectBySchoolId(wmScSchoolBuildingDO.getSchoolPrimaryId());
        if (CollectionUtils.isEmpty(wmScSchoolAreaDOList)) {
            return (int) SchoolBuildingStatusEnum.INVALID.getType();
        }
        List<String> schoolAreaList = wmScSchoolAreaDOList.stream()
                .map(WmScSchoolAreaDO::getArea)
                .filter(area -> StringUtils.isNotBlank(area) && !"[]".equals(area))
                .collect(Collectors.toList());
        // 楼宇坐标经纬度
        List<WmScPoint> wmScPoints = JSONObject.parseArray(wmScSchoolBuildingDO.getBuildingCoordinate(), WmScPoint.class);
        int buildingLatitude = wmScPoints.get(0).getX();
        int buidlingLongitude = wmScPoints.get(0).getY();

        // 2-校验楼宇坐标与学校范围的关系不通过:楼宇状态=失效
        if (wmScSchoolBuildingDO.getBuildingLocation().equals((int) SchoolBuildingLocationEnum.INSCHOOL.getType())
                && !WmRtreeUtil.withinAreaList(buildingLatitude, buidlingLongitude, schoolAreaList)) {
            return (int) SchoolBuildingStatusEnum.INVALID.getType();
        }

        if (wmScSchoolBuildingDO.getBuildingLocation().equals((int) SchoolBuildingLocationEnum.OUTOFSCHOOL.getType())
                && WmRtreeUtil.withinAreaList(buildingLatitude, buidlingLongitude, schoolAreaList)) {
            return (int) SchoolBuildingStatusEnum.INVALID.getType();
        }

        // 3-校验楼宇扎点与楼宇范围的关系不通过:楼宇状态=失效
        if (!WmRtreeUtil.withinArea(buildingLatitude, buidlingLongitude, wmScSchoolBuildingDO.getBuildingArea())) {
            return (int) SchoolBuildingStatusEnum.INVALID.getType();
        }

        // 4-校验楼宇范围与学校范围的关系不通过:楼宇状态=失效
        if (!checkAreaInfoService.checkAreaIsIntersect(wmScSchoolBuildingDO.getBuildingArea())) {
            return (int) SchoolBuildingStatusEnum.INVALID.getType();
        }
        // 校验楼宇范围是否位于校内
        if (wmScSchoolBuildingDO.getBuildingLocation().equals((int) SchoolBuildingLocationEnum.INSCHOOL.getType())
                && !checkAreaInfoService.areaContains(schoolAreaList, wmScSchoolBuildingDO.getBuildingArea())) {
            return (int) SchoolBuildingStatusEnum.INVALID.getType();
        }
        // 校验楼宇范围是否位于校外
        List<String> checkBuildingOutSchoolList = new ArrayList<>(schoolAreaList);
        checkBuildingOutSchoolList.add(wmScSchoolBuildingDO.getBuildingArea());
        if (wmScSchoolBuildingDO.getBuildingLocation().equals((int) SchoolBuildingLocationEnum.OUTOFSCHOOL.getType())
                && checkAreaInfoService.checkAreaListIntersect(checkBuildingOutSchoolList)) {
            return (int) SchoolBuildingStatusEnum.INVALID.getType();
        }
        // 楼宇范围之间不存在重叠
        List<WmScSchoolBuildingDO> wmScSchoolBuildingDOList = wmScSchoolBuildingMapper.selectBySchoolPrimaryId(wmScSchoolBuildingDO.getSchoolPrimaryId());
        for (WmScSchoolBuildingDO buildingDO : wmScSchoolBuildingDOList) {
            if (StringUtils.isBlank(buildingDO.getBuildingArea()) || "[]".equals(buildingDO.getBuildingArea())) {
                continue;
            }
            if (buildingDO.getId().equals(wmScSchoolBuildingDO.getId())) {
                continue;
            }
            if (checkAreaInfoService.intersects(wmScSchoolBuildingDO.getBuildingArea(), buildingDO.getBuildingArea())) {
                return (int) SchoolBuildingStatusEnum.INVALID.getType();
            }
        }
        return (int) SchoolBuildingStatusEnum.VALID.getType();
    }

    /**
     * 通过已知的学校范围计算楼宇状态
     * @param wmScSchoolBuildingDO 学校楼宇DO
     * @param schoolAreaList 学校范围列表
     * @return 楼宇状态
     */
    public Integer getSchoolBuildingStatusWithSchoolArea(WmScSchoolBuildingDO wmScSchoolBuildingDO, List<String> schoolAreaList) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.service.sc.WmScSchoolBuildingService.getSchoolBuildingStatusWithSchoolArea(com.sankuai.meituan.waimai.customer.domain.sc.WmScSchoolBuildingDO,java.util.List)");
        if (wmScSchoolBuildingDO == null) {
            return (int) SchoolBuildingStatusEnum.INVALID.getType();
        }
        // 1-楼宇扎点或楼宇范围信息未填写:楼宇状态=失效
        if (StringUtils.isBlank(wmScSchoolBuildingDO.getBuildingCoordinate())
                || StringUtils.isBlank(wmScSchoolBuildingDO.getBuildingArea())
                || "[]".equals(wmScSchoolBuildingDO.getBuildingArea())
                || "[]".equals(wmScSchoolBuildingDO.getBuildingCoordinate())) {
            return (int) SchoolBuildingStatusEnum.INVALID.getType();
        }

        // 楼宇坐标经纬度
        List<WmScPoint> wmScPoints = JSONObject.parseArray(wmScSchoolBuildingDO.getBuildingCoordinate(), WmScPoint.class);
        int buildingLatitude = wmScPoints.get(0).getX();
        int buidlingLongitude = wmScPoints.get(0).getY();

        // 2-校验楼宇坐标与学校范围的关系不通过:楼宇状态=失效
        if (wmScSchoolBuildingDO.getBuildingLocation().equals((int) SchoolBuildingLocationEnum.INSCHOOL.getType())
                && !WmRtreeUtil.withinAreaList(buildingLatitude, buidlingLongitude, schoolAreaList)) {
            return (int) SchoolBuildingStatusEnum.INVALID.getType();
        }

        if (wmScSchoolBuildingDO.getBuildingLocation().equals((int) SchoolBuildingLocationEnum.OUTOFSCHOOL.getType())
                && WmRtreeUtil.withinAreaList(buildingLatitude, buidlingLongitude, schoolAreaList)) {
            return (int) SchoolBuildingStatusEnum.INVALID.getType();
        }

        // 3-校验楼宇扎点与楼宇范围的关系不通过:楼宇状态=失效
        if (!WmRtreeUtil.withinArea(buildingLatitude, buidlingLongitude, wmScSchoolBuildingDO.getBuildingArea())) {
            return (int) SchoolBuildingStatusEnum.INVALID.getType();
        }

        // 4-校验楼宇范围与学校范围的关系不通过:楼宇状态=失效
        if (!checkAreaInfoService.checkAreaIsIntersect(wmScSchoolBuildingDO.getBuildingArea())) {
            return (int) SchoolBuildingStatusEnum.INVALID.getType();
        }
        // 校验楼宇范围是否位于校内
        if (wmScSchoolBuildingDO.getBuildingLocation().equals((int) SchoolBuildingLocationEnum.INSCHOOL.getType())
                && !checkAreaInfoService.areaContains(schoolAreaList, wmScSchoolBuildingDO.getBuildingArea())) {
            return (int) SchoolBuildingStatusEnum.INVALID.getType();
        }
        // 校验楼宇范围是否位于校外
        List<String> checkBuildingOutSchoolList = new ArrayList<>(schoolAreaList);
        checkBuildingOutSchoolList.add(wmScSchoolBuildingDO.getBuildingArea());
        if (wmScSchoolBuildingDO.getBuildingLocation().equals((int) SchoolBuildingLocationEnum.OUTOFSCHOOL.getType())
                && checkAreaInfoService.checkAreaListIntersect(checkBuildingOutSchoolList)) {
            return (int) SchoolBuildingStatusEnum.INVALID.getType();
        }
        // 楼宇范围之间不存在重叠
        List<WmScSchoolBuildingDO> wmScSchoolBuildingDOList = wmScSchoolBuildingMapper.selectBySchoolPrimaryId(wmScSchoolBuildingDO.getSchoolPrimaryId());
        for (WmScSchoolBuildingDO buildingDO : wmScSchoolBuildingDOList) {
            if (StringUtils.isBlank(buildingDO.getBuildingArea()) || "[]".equals(buildingDO.getBuildingArea())) {
                continue;
            }
            if (buildingDO.getId().equals(wmScSchoolBuildingDO.getId())) {
                continue;
            }
            if (checkAreaInfoService.intersects(wmScSchoolBuildingDO.getBuildingArea(), buildingDO.getBuildingArea())) {
                return (int) SchoolBuildingStatusEnum.INVALID.getType();
            }
        }
        return (int) SchoolBuildingStatusEnum.VALID.getType();
    }

    /**
     * 保存单条学校楼宇信息
     * @param wmScSchoolBuildingSaveDTO wmScSchoolBuildingSaveDTO
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     * @throws TException org.apache.thrift.TException
     */
    public void saveSingleSchoolBuilding(WmScSchoolBuildingSaveDTO wmScSchoolBuildingSaveDTO) throws WmSchCantException, TException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.sc.WmScSchoolBuildingService.saveSingleSchoolBuilding(com.sankuai.meituan.waimai.thrift.customer.domain.sc.WmScSchoolBuildingSaveDTO)");
        log.info("[WmScSchoolBuildingService.saveSingleSchoolBuilding] input param: wmScSchoolBuildingSaveDTO = {}",
                JSONObject.toJSONString(wmScSchoolBuildingSaveDTO));
        // 校验基本信息
        checkSingleSchoolBuildingSave(wmScSchoolBuildingSaveDTO);
        // 组装学校楼宇信息列表
        List<WmScSchoolBuildingDetailDTO> wmScSchoolBuildingDetailDTOList = getSchoolBuildingDetailDTOListForSave(wmScSchoolBuildingSaveDTO);
        wmScSchoolBuildingSaveDTO.setWmScSchoolBuildingDetailDTOList(wmScSchoolBuildingDetailDTOList);

        if (wmScSchoolBuildingSaveDTO.getOpType().equals((int) SchoolBuildingOpTypeEnum.BUILDING_INFO.getType())) {
            // 楼宇基本信息编辑保存
            checkSchoolBuildingBasicInfo(wmScSchoolBuildingSaveDTO);
            saveSchoolBuildingBasicInfo(wmScSchoolBuildingSaveDTO);
        } else if (wmScSchoolBuildingSaveDTO.getOpType().equals((int) SchoolBuildingOpTypeEnum.BUILDING_COORDINATE.getType())) {
            // 楼宇扎点信息编辑保存
            checkSchoolBuildingCoordinateInfo(wmScSchoolBuildingSaveDTO);
            saveSchoolBuildingCoordinateInfo(wmScSchoolBuildingSaveDTO);
        } else if (wmScSchoolBuildingSaveDTO.getOpType().equals((int) SchoolBuildingOpTypeEnum.BUILDING_AREA.getType())) {
            // 楼宇范围信息编辑保存
            checkSchoolBuildingAreaInfo(wmScSchoolBuildingSaveDTO);
            saveSchoolBuildingAreaInfo(wmScSchoolBuildingSaveDTO);
        }
    }

    /**
     * 组装学校楼宇信息列表(保存楼宇信息时)
     * @param wmScSchoolBuildingSaveDTO wmScSchoolBuildingSaveDTO
     * @return List<WmScSchoolBuildingDetailDTO>
     */
    public List<WmScSchoolBuildingDetailDTO> getSchoolBuildingDetailDTOListForSave(WmScSchoolBuildingSaveDTO wmScSchoolBuildingSaveDTO) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_1", "com.sankuai.meituan.waimai.customer.service.sc.WmScSchoolBuildingService.getSchoolBuildingDetailDTOListForSave(com.sankuai.meituan.waimai.thrift.customer.domain.sc.WmScSchoolBuildingSaveDTO)");
        List<WmScSchoolBuildingDetailDTO> dtoList = new ArrayList<>();
        List<WmScSchoolBuildingDO> wmScSchoolBuildingDOList = wmScSchoolBuildingMapper.selectBySchoolPrimaryId(wmScSchoolBuildingSaveDTO.getSchoolPrimaryId());
        if (CollectionUtils.isEmpty(wmScSchoolBuildingDOList)) {
            dtoList.add(wmScSchoolBuildingSaveDTO.getEditBuildingDetailDTO());
            return dtoList;
        }

        WmScSchoolBuildingDetailDTO editBuildingDetailDTO = wmScSchoolBuildingSaveDTO.getEditBuildingDetailDTO();
        if (editBuildingDetailDTO.getBuildingId() == null) {
            // 楼宇ID为空->新增楼宇信息
            for (WmScSchoolBuildingDO wmScSchoolBuildingDO : wmScSchoolBuildingDOList) {
                WmScSchoolBuildingDetailDTO dto = WmScTransUtil.transSchoolBuildingDOToDetailDTO(wmScSchoolBuildingDO);
                dtoList.add(dto);
            }
            dtoList.add(editBuildingDetailDTO);
        } else {
            // 楼宇ID不为空->修改楼宇信息
            for (WmScSchoolBuildingDO wmScSchoolBuildingDO : wmScSchoolBuildingDOList) {
                if (wmScSchoolBuildingDO.getId().equals(editBuildingDetailDTO.getBuildingId())) {
                    dtoList.add(editBuildingDetailDTO);
                    continue;
                }
                WmScSchoolBuildingDetailDTO dto = WmScTransUtil.transSchoolBuildingDOToDetailDTO(wmScSchoolBuildingDO);
                dtoList.add(dto);
            }
        }
        return dtoList;
    }


    /**
     * 保存单条学校楼宇-基本信息
     * @param wmScSchoolBuildingSaveDTO wmScSchoolBuildingSaveDTO
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     * @throws TException org.apache.thrift.TException
     */
    public void saveSchoolBuildingBasicInfo(WmScSchoolBuildingSaveDTO wmScSchoolBuildingSaveDTO) throws WmSchCantException, TException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.sc.WmScSchoolBuildingService.saveSchoolBuildingBasicInfo(com.sankuai.meituan.waimai.thrift.customer.domain.sc.WmScSchoolBuildingSaveDTO)");
        if (wmScSchoolBuildingSaveDTO == null
                || wmScSchoolBuildingSaveDTO.getEditBuildingDetailDTO() == null) {
            log.error("[WmScSchoolBuildingService.saveSchoolBuildingBasicInfo] wmScSchoolBuildingSaveDTO is null. wmScSchoolBuildingSaveDTO = {}",
                    JSONObject.toJSONString(wmScSchoolBuildingSaveDTO));
            throw new WmSchCantException(BIZ_PARA_ERROR, "楼宇基本信息为空");
        }

        WmScSchoolBuildingDetailDTO editBuildingDetailDTO = wmScSchoolBuildingSaveDTO.getEditBuildingDetailDTO();
        if (editBuildingDetailDTO.getBuildingId() == null
                || editBuildingDetailDTO.getBuildingId() <= 0) {
            // 楼宇ID为空->新增楼宇基本信息
            insertSchoolBuildingBasicInfo(wmScSchoolBuildingSaveDTO);
        } else {
            // 楼宇ID不为空->更新楼宇基本信息
            updateSchoolBuildingBasicInfo(wmScSchoolBuildingSaveDTO);
        }
    }

    /**
     * 插入单条学校楼宇-基本信息
     * @param wmScSchoolBuildingSaveDTO wmScSchoolBuildingSaveDTO
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     * @throws TException org.apache.thrift.TException
     */
    public void insertSchoolBuildingBasicInfo(WmScSchoolBuildingSaveDTO wmScSchoolBuildingSaveDTO) throws WmSchCantException, TException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.service.sc.WmScSchoolBuildingService.insertSchoolBuildingBasicInfo(com.sankuai.meituan.waimai.thrift.customer.domain.sc.WmScSchoolBuildingSaveDTO)");
        if (wmScSchoolBuildingSaveDTO == null || wmScSchoolBuildingSaveDTO.getEditBuildingDetailDTO() == null) {
            log.error("[WmScSchoolBuildingService.insertSchoolBuildingBasicInfo] wmScSchoolBuildingDetailDTO is null. wmScSchoolBuildingDetailDTO = {}",
                    JSONObject.toJSONString(wmScSchoolBuildingSaveDTO));
            throw new WmSchCantException(BIZ_PARA_ERROR, "楼宇基本信息为空");
        }

        WmScSchoolBuildingDetailDTO editBuildingDetailDTO = wmScSchoolBuildingSaveDTO.getEditBuildingDetailDTO();
        WmScSchoolBuildingDO wmScSchoolBuildingDOInsert = new WmScSchoolBuildingDO();
        wmScSchoolBuildingDOInsert.setBuildingName(editBuildingDetailDTO.getBuildingName());
        wmScSchoolBuildingDOInsert.setBuildingNickname(editBuildingDetailDTO.getBuildingNickname());
        wmScSchoolBuildingDOInsert.setBuildingElevator(editBuildingDetailDTO.getBuildingElevator());
        wmScSchoolBuildingDOInsert.setBuildingPersonNum(editBuildingDetailDTO.getBuildingPersonNum());
        wmScSchoolBuildingDOInsert.setBuildingType(editBuildingDetailDTO.getBuildingType());
        wmScSchoolBuildingDOInsert.setBuildingFloor(editBuildingDetailDTO.getBuildingFloor());
        wmScSchoolBuildingDOInsert.setCuid(wmScSchoolBuildingSaveDTO.getUserId().longValue());
        wmScSchoolBuildingDOInsert.setMuid(wmScSchoolBuildingSaveDTO.getUserId().longValue());
        wmScSchoolBuildingDOInsert.setBuildingLocation(editBuildingDetailDTO.getBuildingLocation());
        wmScSchoolBuildingDOInsert.setSchoolPrimaryId(wmScSchoolBuildingSaveDTO.getSchoolPrimaryId());
        wmScSchoolBuildingDOInsert.setBuildingStatus((int) SchoolBuildingStatusEnum.INVALID.getType());
        wmScSchoolBuildingDOInsert.setBuildingArea("[]");
        wmScSchoolBuildingDOInsert.setBuildingCoordinate("[]");

        int result = wmScSchoolBuildingMapper.insertSelective(wmScSchoolBuildingDOInsert);
        if (result > 0) {
            // 记录到操作日志
            WmScSchoolBuildingLogBO wmScSchoolBuildingLogBO = new WmScSchoolBuildingLogBO();
            wmScSchoolBuildingLogBO.setUserId(wmScSchoolBuildingSaveDTO.getUserId());
            wmScSchoolBuildingLogBO.setUserName(wmScSchoolBuildingSaveDTO.getUserName());
            wmScSchoolBuildingLogBO.setWmScSchoolBuildingDoInsert(wmScSchoolBuildingDOInsert);
            wmScLogSchoolInfoService.recordSchoolBuildingInsertLog(wmScSchoolBuildingLogBO);
        }
        log.info("[WmScSchoolBuildingService.insertSchoolBuildingBasicInfo] insert success. wmScSchoolBuildingDOInsert = {}",
                JSONObject.toJSONString(wmScSchoolBuildingDOInsert));
    }

    /**
     * 更新单条学校楼宇-基本信息
     * @param wmScSchoolBuildingSaveDTO wmScSchoolBuildingSaveDTO
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     * @throws TException org.apache.thrift.TException
     */
    public void updateSchoolBuildingBasicInfo(WmScSchoolBuildingSaveDTO wmScSchoolBuildingSaveDTO) throws WmSchCantException, TException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.service.sc.WmScSchoolBuildingService.updateSchoolBuildingBasicInfo(com.sankuai.meituan.waimai.thrift.customer.domain.sc.WmScSchoolBuildingSaveDTO)");
        if (wmScSchoolBuildingSaveDTO == null
                || wmScSchoolBuildingSaveDTO.getEditBuildingDetailDTO() == null
                || wmScSchoolBuildingSaveDTO.getEditBuildingDetailDTO().getBuildingId() == null) {
            log.error("[WmScSchoolBuildingService.updateSchoolBuildingBasicInfo] wmScSchoolBuildingDetailDTO is null. wmScSchoolBuildingSaveDTO = {}",
                    JSONObject.toJSONString(wmScSchoolBuildingSaveDTO));
            throw new WmSchCantException(BIZ_PARA_ERROR, "楼宇基本信息为空");
        }

        WmScSchoolBuildingDetailDTO editBuildingDetailDTO = wmScSchoolBuildingSaveDTO.getEditBuildingDetailDTO();
        WmScSchoolBuildingDO wmScSchoolBuildingDOBefore = wmScSchoolBuildingMapper.selectByPrimaryId(editBuildingDetailDTO.getBuildingId());
        if (isSchoolBuildingBasicInfoSame(wmScSchoolBuildingDOBefore, editBuildingDetailDTO)) {
            log.info("[WmScSchoolBuildingService.updateSchoolBuildingBasicInfo] info is the same, return.");
            return;
        }

        WmScSchoolBuildingDO wmScSchoolBuildingDOUpdate = new WmScSchoolBuildingDO();
        wmScSchoolBuildingDOUpdate.setId(wmScSchoolBuildingDOBefore.getId());
        wmScSchoolBuildingDOUpdate = setUpdateSchoolBuildingBasicInfo(wmScSchoolBuildingDOBefore, wmScSchoolBuildingDOUpdate, editBuildingDetailDTO);
        wmScSchoolBuildingDOUpdate.setMuid(wmScSchoolBuildingSaveDTO.getUserId().longValue());

        WmScSchoolBuildingDO wmScSchoolBuildingDOTemp = new WmScSchoolBuildingDO();
        BeanUtil.copyProperties(wmScSchoolBuildingDOBefore, wmScSchoolBuildingDOTemp);
        wmScSchoolBuildingDOTemp = setUpdateSchoolBuildingBasicInfo(wmScSchoolBuildingDOBefore, wmScSchoolBuildingDOTemp, editBuildingDetailDTO);
        // 计算楼宇状态
        Integer buildingStatus = getSchoolBuildingStatus(wmScSchoolBuildingDOTemp);
        if (!wmScSchoolBuildingDOBefore.getBuildingStatus().equals(buildingStatus)) {
            wmScSchoolBuildingDOUpdate.setBuildingStatus(buildingStatus);
        }

        int result = wmScSchoolBuildingMapper.updateByPrimaryKeySelective(wmScSchoolBuildingDOUpdate);
        if (result > 0) {
            // 记录到操作日志
            BeanUtils.copyProperties(wmScSchoolBuildingDOBefore, wmScSchoolBuildingDOUpdate);
            wmScSchoolBuildingDOUpdate = setUpdateSchoolBuildingBasicInfo(wmScSchoolBuildingDOBefore, wmScSchoolBuildingDOUpdate, editBuildingDetailDTO);
            if (!wmScSchoolBuildingDOBefore.getBuildingStatus().equals(buildingStatus)) {
                wmScSchoolBuildingDOUpdate.setBuildingStatus(buildingStatus);
            }

            WmScSchoolBuildingLogBO wmScSchoolBuildingLogBO = new WmScSchoolBuildingLogBO();
            wmScSchoolBuildingLogBO.setUserId(wmScSchoolBuildingSaveDTO.getUserId());
            wmScSchoolBuildingLogBO.setUserName(wmScSchoolBuildingSaveDTO.getUserName());
            wmScSchoolBuildingLogBO.setWmScSchoolBuildingDoBefore(wmScSchoolBuildingDOBefore);
            wmScSchoolBuildingLogBO.setWmScSchoolBuildingDoAfter(wmScSchoolBuildingDOUpdate);
            wmScLogSchoolInfoService.recordSchoolBuildingUpdateLog(wmScSchoolBuildingLogBO);
        }
        log.info("[WmScSchoolBuildingService.updateSchoolBuildingBasicInfo] update success. wmScSchoolBuildingDOUpdate = {}",
                JSONObject.toJSONString(wmScSchoolBuildingDOUpdate));
    }

    /**
     * 设置需要更新的学校楼宇字段内容
     * @param wmScSchoolBuildingDoBefore 更新前DO
     * @param wmScSchoolBuildingDoUpdate 更新后DO
     * @param wmScSchoolBuildingDetailDTO 当前修改的DTO
     */
    public WmScSchoolBuildingDO setUpdateSchoolBuildingBasicInfo(WmScSchoolBuildingDO wmScSchoolBuildingDoBefore,
                                                            WmScSchoolBuildingDO wmScSchoolBuildingDoUpdate,
                                                            WmScSchoolBuildingDetailDTO wmScSchoolBuildingDetailDTO) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.service.sc.WmScSchoolBuildingService.setUpdateSchoolBuildingBasicInfo(WmScSchoolBuildingDO,WmScSchoolBuildingDO,WmScSchoolBuildingDetailDTO)");
        if (!wmScSchoolBuildingDoBefore.getBuildingName().equals(wmScSchoolBuildingDetailDTO.getBuildingName())) {
            wmScSchoolBuildingDoUpdate.setBuildingName(wmScSchoolBuildingDetailDTO.getBuildingName());
        }

        if (!wmScSchoolBuildingDoBefore.getBuildingNickname().equals(wmScSchoolBuildingDetailDTO.getBuildingNickname())) {
            wmScSchoolBuildingDoUpdate.setBuildingNickname(wmScSchoolBuildingDetailDTO.getBuildingNickname());
        }

        if (!wmScSchoolBuildingDoBefore.getBuildingElevator().equals(wmScSchoolBuildingDetailDTO.getBuildingElevator())) {
            wmScSchoolBuildingDoUpdate.setBuildingElevator(wmScSchoolBuildingDetailDTO.getBuildingElevator());
        }

        if (!wmScSchoolBuildingDoBefore.getBuildingFloor().equals(wmScSchoolBuildingDetailDTO.getBuildingFloor())) {
            wmScSchoolBuildingDoUpdate.setBuildingFloor(wmScSchoolBuildingDetailDTO.getBuildingFloor());
        }

        if (!wmScSchoolBuildingDoBefore.getBuildingType().equals(wmScSchoolBuildingDetailDTO.getBuildingType())) {
            wmScSchoolBuildingDoUpdate.setBuildingType(wmScSchoolBuildingDetailDTO.getBuildingType());
        }

        if (!wmScSchoolBuildingDoBefore.getBuildingPersonNum().equals(wmScSchoolBuildingDetailDTO.getBuildingPersonNum())) {
            wmScSchoolBuildingDoUpdate.setBuildingPersonNum(wmScSchoolBuildingDetailDTO.getBuildingPersonNum());
        }

        if (!wmScSchoolBuildingDoBefore.getBuildingLocation().equals(wmScSchoolBuildingDetailDTO.getBuildingLocation())) {
            wmScSchoolBuildingDoUpdate.setBuildingLocation(wmScSchoolBuildingDetailDTO.getBuildingLocation());
        }
        return wmScSchoolBuildingDoUpdate;
    }

    /**
     * 判断楼宇基本信息是否一致
     * @param wmScSchoolBuildingDOBefore 当前DB中的信息
     * @param editBuildingDetailDTO 要保存的信息
     * @return true: 一致 / false: 不一致
     */
    public Boolean isSchoolBuildingBasicInfoSame(WmScSchoolBuildingDO wmScSchoolBuildingDOBefore, WmScSchoolBuildingDetailDTO editBuildingDetailDTO) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.sc.WmScSchoolBuildingService.isSchoolBuildingBasicInfoSame(WmScSchoolBuildingDO,WmScSchoolBuildingDetailDTO)");
        // 先将wmScSchoolBuildingDetailDTO的空值设置为默认值
        if (StringUtils.isBlank(editBuildingDetailDTO.getBuildingArea())) {
            editBuildingDetailDTO.setBuildingArea("[]");
        }

        if (StringUtils.isBlank(editBuildingDetailDTO.getBuildingCoordinate())) {
            editBuildingDetailDTO.setBuildingCoordinate("[]");
        }

        if (editBuildingDetailDTO.getBuildingFloor() == null) {
            editBuildingDetailDTO.setBuildingFloor(-1);
        }

        if (StringUtils.isEmpty(editBuildingDetailDTO.getBuildingNickname())) {
            editBuildingDetailDTO.setBuildingNickname("");
        }

        if (editBuildingDetailDTO.getBuildingPersonNum() == null) {
            editBuildingDetailDTO.setBuildingPersonNum(-1);
        }

        if (editBuildingDetailDTO.getBuildingElevator() == null) {
            editBuildingDetailDTO.setBuildingElevator(0);
        }

        return wmScSchoolBuildingDOBefore.getBuildingName().equals(editBuildingDetailDTO.getBuildingName())
                && wmScSchoolBuildingDOBefore.getBuildingFloor().equals(editBuildingDetailDTO.getBuildingFloor())
                && wmScSchoolBuildingDOBefore.getBuildingElevator().equals(editBuildingDetailDTO.getBuildingElevator())
                && wmScSchoolBuildingDOBefore.getBuildingLocation().equals(editBuildingDetailDTO.getBuildingLocation())
                && wmScSchoolBuildingDOBefore.getBuildingNickname().equals(editBuildingDetailDTO.getBuildingNickname())
                && wmScSchoolBuildingDOBefore.getBuildingType().equals(editBuildingDetailDTO.getBuildingType())
                && wmScSchoolBuildingDOBefore.getBuildingPersonNum().equals(editBuildingDetailDTO.getBuildingPersonNum());
    }

    /**
     * 保存楼宇信息-坐标信息
     * @param wmScSchoolBuildingSaveDTO wmScSchoolBuildingSaveDTO
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     * @throws TException org.apache.thrift.TException
     */
    public void saveSchoolBuildingCoordinateInfo(WmScSchoolBuildingSaveDTO wmScSchoolBuildingSaveDTO) throws WmSchCantException, TException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.service.sc.WmScSchoolBuildingService.saveSchoolBuildingCoordinateInfo(com.sankuai.meituan.waimai.thrift.customer.domain.sc.WmScSchoolBuildingSaveDTO)");
        if (wmScSchoolBuildingSaveDTO == null
                || wmScSchoolBuildingSaveDTO.getEditBuildingDetailDTO() == null
                || wmScSchoolBuildingSaveDTO.getEditBuildingDetailDTO().getBuildingId() == null
                || wmScSchoolBuildingSaveDTO.getSchoolPrimaryId() == null) {
            log.error("[WmScSchoolBuildingService.saveSchoolBuildingCoordinateInfo] wmScSchoolBuildingSaveDTO is null. wmScSchoolBuildingSaveDTO = {}",
                    JSONObject.toJSONString(wmScSchoolBuildingSaveDTO));
            throw new WmSchCantException(BIZ_PARA_ERROR, "楼宇基本信息为空");
        }

        WmScSchoolBuildingDetailDTO editBuildingDetailDTO = wmScSchoolBuildingSaveDTO.getEditBuildingDetailDTO();
        WmScSchoolBuildingDO wmScSchoolBuildingDOBefore = wmScSchoolBuildingMapper.selectByPrimaryId(wmScSchoolBuildingSaveDTO.getEditBuildingDetailDTO().getBuildingId());
        if (wmScSchoolBuildingDOBefore.getBuildingCoordinate().equals(editBuildingDetailDTO.getBuildingCoordinate())) {
            log.info("[WmScSchoolBuildingService.saveSchoolBuildingCoordinateInfo] coordinate info is the same, return.");
            return;
        }

        WmScSchoolBuildingDO wmScSchoolBuildingDOUpdate = new WmScSchoolBuildingDO();
        wmScSchoolBuildingDOUpdate.setId(editBuildingDetailDTO.getBuildingId());
        wmScSchoolBuildingDOUpdate.setBuildingCoordinate(editBuildingDetailDTO.getBuildingCoordinate());
        wmScSchoolBuildingDOUpdate.setMuid(wmScSchoolBuildingSaveDTO.getUserId().longValue());

        WmScSchoolBuildingDO wmScSchoolBuildingDOTemp = new WmScSchoolBuildingDO();
        BeanUtils.copyProperties(wmScSchoolBuildingDOBefore, wmScSchoolBuildingDOTemp);
        wmScSchoolBuildingDOTemp.setBuildingCoordinate(editBuildingDetailDTO.getBuildingCoordinate());
        Integer buildingStatus = getSchoolBuildingStatus(wmScSchoolBuildingDOTemp);
        log.info("[WmScSchoolBuildingService.saveSchoolBuildingCoordinateInfo] buildingStatus = {}", buildingStatus);
        if (!wmScSchoolBuildingDOBefore.getBuildingStatus().equals(buildingStatus)) {
            wmScSchoolBuildingDOUpdate.setBuildingStatus(buildingStatus);
        }

        int result = wmScSchoolBuildingMapper.updateByPrimaryKeySelective(wmScSchoolBuildingDOUpdate);
        if (result > 0) {
            // 记录到操作日志
            BeanUtils.copyProperties(wmScSchoolBuildingDOBefore, wmScSchoolBuildingDOUpdate);
            wmScSchoolBuildingDOUpdate.setBuildingCoordinate(editBuildingDetailDTO.getBuildingCoordinate());
            if (!wmScSchoolBuildingDOBefore.getBuildingStatus().equals(buildingStatus)) {
                wmScSchoolBuildingDOUpdate.setBuildingStatus(buildingStatus);
            }

            WmScSchoolBuildingLogBO wmScSchoolBuildingLogBO = new WmScSchoolBuildingLogBO();
            wmScSchoolBuildingLogBO.setUserId(wmScSchoolBuildingSaveDTO.getUserId());
            wmScSchoolBuildingLogBO.setUserName(wmScSchoolBuildingSaveDTO.getUserName());
            wmScSchoolBuildingLogBO.setWmScSchoolBuildingDoBefore(wmScSchoolBuildingDOBefore);
            wmScSchoolBuildingLogBO.setWmScSchoolBuildingDoAfter(wmScSchoolBuildingDOUpdate);
            wmScLogSchoolInfoService.recordSchoolBuildingUpdateLog(wmScSchoolBuildingLogBO);
        }
        log.info("[WmScSchoolBuildingService.saveSchoolBuildingCoordinateInfo] save success. wmScSchoolBuildingDOUpdate = {}",
                JSONObject.toJSONString(wmScSchoolBuildingDOUpdate));
    }

    /**
     * 保存楼宇信息-范围信息
     * @param wmScSchoolBuildingSaveDTO wmScSchoolBuildingSaveDTO
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     * @throws TException org.apache.thrift.TException
     */
    public void saveSchoolBuildingAreaInfo(WmScSchoolBuildingSaveDTO wmScSchoolBuildingSaveDTO) throws WmSchCantException, TException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_2", "com.sankuai.meituan.waimai.customer.service.sc.WmScSchoolBuildingService.saveSchoolBuildingAreaInfo(com.sankuai.meituan.waimai.thrift.customer.domain.sc.WmScSchoolBuildingSaveDTO)");
        if (wmScSchoolBuildingSaveDTO == null
                || wmScSchoolBuildingSaveDTO.getEditBuildingDetailDTO() == null
                || wmScSchoolBuildingSaveDTO.getEditBuildingDetailDTO().getBuildingId() == null
                || wmScSchoolBuildingSaveDTO.getSchoolPrimaryId() == null) {
            log.error("[WmScSchoolBuildingService.saveSchoolBuildingAreaInfo] wmScSchoolBuildingSaveDTO is null. wmScSchoolBuildingSaveDTO = {}",
                    JSONObject.toJSONString(wmScSchoolBuildingSaveDTO));
            throw new WmSchCantException(BIZ_PARA_ERROR, "楼宇基本信息为空");
        }

        WmScSchoolBuildingDetailDTO editBuildingDetailDTO = wmScSchoolBuildingSaveDTO.getEditBuildingDetailDTO();
        WmScSchoolBuildingDO wmScSchoolBuildingDOBefore = wmScSchoolBuildingMapper.selectByPrimaryId(wmScSchoolBuildingSaveDTO.getEditBuildingDetailDTO().getBuildingId());
        // 保存前后楼宇范围一致且楼宇状态为生效, 则直接返回
        if (wmScSchoolBuildingDOBefore.getBuildingArea().equals(editBuildingDetailDTO.getBuildingArea())
                && wmScSchoolBuildingDOBefore.getBuildingStatus().equals((int) SchoolBuildingStatusEnum.VALID.getType())) {
            log.info("[WmScSchoolBuildingService.saveSchoolBuildingAreaInfo] area info is the same, return.]");
            return;
        }

        WmScSchoolBuildingDO wmScSchoolBuildingDOUpdate = new WmScSchoolBuildingDO();
        wmScSchoolBuildingDOUpdate.setId(editBuildingDetailDTO.getBuildingId());
        wmScSchoolBuildingDOUpdate.setBuildingArea(editBuildingDetailDTO.getBuildingArea());
        wmScSchoolBuildingDOUpdate.setBuildingStatus((int) SchoolBuildingStatusEnum.VALID.getType());
        wmScSchoolBuildingDOUpdate.setMuid(wmScSchoolBuildingSaveDTO.getUserId().longValue());

        int result = wmScSchoolBuildingMapper.updateByPrimaryKeySelective(wmScSchoolBuildingDOUpdate);
        if (result > 0) {
            // 记录到操作日志
            BeanUtils.copyProperties(wmScSchoolBuildingDOBefore, wmScSchoolBuildingDOUpdate);
            wmScSchoolBuildingDOUpdate.setBuildingArea(editBuildingDetailDTO.getBuildingArea());
            wmScSchoolBuildingDOUpdate.setBuildingStatus((int) SchoolBuildingStatusEnum.VALID.getType());

            WmScSchoolBuildingLogBO wmScSchoolBuildingLogBO = new WmScSchoolBuildingLogBO();
            wmScSchoolBuildingLogBO.setUserId(wmScSchoolBuildingSaveDTO.getUserId());
            wmScSchoolBuildingLogBO.setUserName(wmScSchoolBuildingSaveDTO.getUserName());
            wmScSchoolBuildingLogBO.setWmScSchoolBuildingDoBefore(wmScSchoolBuildingDOBefore);
            wmScSchoolBuildingLogBO.setWmScSchoolBuildingDoAfter(wmScSchoolBuildingDOUpdate);
            wmScLogSchoolInfoService.recordSchoolBuildingUpdateLog(wmScSchoolBuildingLogBO);
        }
        log.info("[WmScSchoolBuildingService.saveSchoolBuildingAreaInfo] save success. wmScSchoolBuildingDOUpdate = {}",
                JSONObject.toJSONString(wmScSchoolBuildingDOUpdate));
    }

    /**
     * 单条楼宇信息保存的校验
     * @param wmScSchoolBuildingSaveDTO wmScSchoolBuildingSaveDTO
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     * @throws TException org.apache.thrift.TException
     */
    public void checkSingleSchoolBuildingSave(WmScSchoolBuildingSaveDTO wmScSchoolBuildingSaveDTO) throws WmSchCantException, TException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_1", "com.sankuai.meituan.waimai.customer.service.sc.WmScSchoolBuildingService.checkSingleSchoolBuildingSave(com.sankuai.meituan.waimai.thrift.customer.domain.sc.WmScSchoolBuildingSaveDTO)");
        if (wmScSchoolBuildingSaveDTO == null
                || wmScSchoolBuildingSaveDTO.getSchoolPrimaryId() == null
                || wmScSchoolBuildingSaveDTO.getSchoolPrimaryId() <= 0) {
            throw new WmSchCantException(BIZ_PARA_ERROR, "学校ID不能为空");
        }

        if (wmScSchoolBuildingSaveDTO.getOpType() == null
                || SchoolBuildingOpTypeEnum.getByType(wmScSchoolBuildingSaveDTO.getOpType()) == null) {
            throw new WmSchCantException(BIZ_PARA_ERROR, "楼宇操作类型不能为空");
        }

        if (wmScSchoolBuildingSaveDTO.getEditBuildingDetailDTO() == null) {
            throw new WmSchCantException(BIZ_PARA_ERROR, "保存的楼宇信息不能为空");
        }
    }

    /**
     * 校验某个楼宇的基本信息(必填项 和 楼宇名称是否重复)
     * @param wmScSchoolBuildingSaveDTO wmScSchoolBuildingSaveDTO
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     * @throws TException org.apache.thrift.TException
     */
    public void checkSchoolBuildingBasicInfo(WmScSchoolBuildingSaveDTO wmScSchoolBuildingSaveDTO) throws WmSchCantException, TException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_2", "com.sankuai.meituan.waimai.customer.service.sc.WmScSchoolBuildingService.checkSchoolBuildingBasicInfo(com.sankuai.meituan.waimai.thrift.customer.domain.sc.WmScSchoolBuildingSaveDTO)");
        log.info("[WmScSchoolBuildingService.checkSchoolBuildingBasicInfo] input param: wmScSchoolBuildingSaveDTO = {}",
                JSONObject.toJSONString(wmScSchoolBuildingSaveDTO));
        if (wmScSchoolBuildingSaveDTO == null
                || wmScSchoolBuildingSaveDTO.getSchoolPrimaryId() == null
                || wmScSchoolBuildingSaveDTO.getEditBuildingDetailDTO() == null
                || CollectionUtils.isEmpty(wmScSchoolBuildingSaveDTO.getWmScSchoolBuildingDetailDTOList())) {
            log.error("[WmScSchoolBuildingService.checkSchoolBuildingBasicInfo] wmScSchoolBuildingDetailDTO is null.");
            throw new WmSchCantException(BIZ_PARA_ERROR, "学校楼宇信息为空");
        }

        WmSchoolDB wmSchoolDB = wmSchoolMapper.selectSchoolById(wmScSchoolBuildingSaveDTO.getSchoolPrimaryId());
        if (wmSchoolDB == null) {
            log.error("[WmScSchoolBuildingService.checkSchoolBuildingBasicInfo] wmSchoolDB is null.");
            throw new WmSchCantException(BIZ_PARA_ERROR, "学校信息为空");
        }

        WmScSchoolBuildingDetailDTO editBuildingDTO = wmScSchoolBuildingSaveDTO.getEditBuildingDetailDTO();
        if (editBuildingDTO.getBuildingLocation() == null
                || SchoolBuildingLocationEnum.getByType(editBuildingDTO.getBuildingLocation()) == null) {
            throw new WmSchCantException(BIZ_PARA_ERROR, "楼宇位置为必填项");
        }

        if (StringUtils.isBlank(editBuildingDTO.getBuildingName())) {
            throw new WmSchCantException(BIZ_PARA_ERROR, "楼宇名称为必填项");
        }

        if (editBuildingDTO.getBuildingFloor() != null
                && (editBuildingDTO.getBuildingFloor() < 0 || editBuildingDTO.getBuildingFloor() > 20000)) {
            throw new WmSchCantException(BIZ_PARA_ERROR, "楼宇楼层范围为0～20000");
        }
        // 楼宇类型=宿舍楼时: 楼层、人数和电梯为必填项
        if (editBuildingDTO.getBuildingType().equals((int) SchoolBuildingTypeEnum.DORMITORY.getType())) {
            checkAreaInfoService.checkSchoolDormitoryBasicInfo(editBuildingDTO);
        }
        // 宿舍楼总人数
        int buildingPersonNumTotal = wmScSchoolBuildingSaveDTO.getWmScSchoolBuildingDetailDTOList().stream()
                .filter(dto -> dto.getBuildingPersonNum() != null && dto.getBuildingPersonNum() >= 0)
                .mapToInt(WmScSchoolBuildingDetailDTO::getBuildingPersonNum)
                .sum();
        if (editBuildingDTO.getBuildingType().equals((int) SchoolBuildingTypeEnum.DORMITORY.getType()) &&
                buildingPersonNumTotal > wmSchoolDB.getTeaStuNum()) {
            throw new WmSchCantException(BIZ_PARA_ERROR, "宿舍楼总人数大于在校师生人数");
        }

        HashSet<String> hashSet = wmScSchoolBuildingSaveDTO.getWmScSchoolBuildingDetailDTOList().stream()
                .filter(dto -> dto.getBuildingId() != null && !dto.getBuildingId().equals(editBuildingDTO.getBuildingId()))
                .map(WmScSchoolBuildingDetailDTO::getBuildingName)
                .collect(Collectors.toCollection(HashSet::new));
        log.info("[WmScSchoolBuildingService.checkSchoolBuildingBasicInfo] hashSet = {}", JSONObject.toJSONString(hashSet));
        if (hashSet.contains(editBuildingDTO.getBuildingName())) {
            throw new WmSchCantException(BIZ_PARA_ERROR, "楼宇名称不可重复");
        }
    }

    /**
     * 校验楼宇扎点时保存的信息
     * @param wmScSchoolBuildingSaveDTO wmScSchoolBuildingSaveDTO
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     * @throws TException org.apache.thrift.TException
     */
    public void checkSchoolBuildingCoordinateInfo(WmScSchoolBuildingSaveDTO wmScSchoolBuildingSaveDTO) throws WmSchCantException, TException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_2", "com.sankuai.meituan.waimai.customer.service.sc.WmScSchoolBuildingService.checkSchoolBuildingCoordinateInfo(com.sankuai.meituan.waimai.thrift.customer.domain.sc.WmScSchoolBuildingSaveDTO)");
        log.info("[WmScSchoolBuildingService.checkSchoolBuildingCoordinateInfo] input param: wmScSchoolBuildingSaveDTO = {}",
                JSONObject.toJSONString(wmScSchoolBuildingSaveDTO));
        if (wmScSchoolBuildingSaveDTO == null
                || wmScSchoolBuildingSaveDTO.getSchoolPrimaryId() == null
                || wmScSchoolBuildingSaveDTO.getEditBuildingDetailDTO() == null
                || CollectionUtils.isEmpty(wmScSchoolBuildingSaveDTO.getWmScSchoolBuildingDetailDTOList())) {
            log.error("[WmScSchoolBuildingService.checkSchoolBuildingCoordinateInfo] wmScSchoolBuildingDetailDTO is null.");
            throw new WmSchCantException(BIZ_PARA_ERROR, "学校楼宇信息为空");
        }

        WmScSchoolBuildingDetailDTO editBuildingDetailDTO = wmScSchoolBuildingSaveDTO.getEditBuildingDetailDTO();
        if (editBuildingDetailDTO.getBuildingLocation() == null
                || SchoolBuildingLocationEnum.getByType(editBuildingDetailDTO.getBuildingLocation()) == null) {
            throw new WmSchCantException(BIZ_PARA_ERROR, "请先维护楼宇信息后再进行扎点");
        }

        if (StringUtils.isBlank(editBuildingDetailDTO.getBuildingCoordinate())
                || "[]".equals(editBuildingDetailDTO.getBuildingCoordinate())) {
             throw new WmSchCantException(BIZ_PARA_ERROR, "楼宇扎点信息为空");
        }

        // 获取学校范围信息
        List<String> schoolAreaList = wmScSchoolAreaService.getSchoolAreaListBySchoolPrimaryId(wmScSchoolBuildingSaveDTO.getSchoolPrimaryId());
        if (CollectionUtils.isEmpty(schoolAreaList)) {
            throw new WmSchCantException(BIZ_PARA_ERROR, "不存在学校范围, 无法编辑楼宇信息");
        }

        WmScPoint buildingCoordinate = getSchoolBuilidngCoordinate(editBuildingDetailDTO.getBuildingCoordinate());
        // 校验楼宇扎点是否位于校内
        if (editBuildingDetailDTO.getBuildingLocation().equals((int) SchoolBuildingLocationEnum.INSCHOOL.getType())
                && !WmRtreeUtil.withinAreaList(buildingCoordinate.getX(), buildingCoordinate.getY(), schoolAreaList)) {
            throw new WmSchCantException(BIZ_PARA_ERROR, "楼宇(" + editBuildingDetailDTO.getBuildingName() + ")扎点不在学校范围内");
        }
        // 校验楼宇扎点是否位于校外
        if (editBuildingDetailDTO.getBuildingLocation().equals((int) SchoolBuildingLocationEnum.OUTOFSCHOOL.getType())
                && WmRtreeUtil.withinAreaList(buildingCoordinate.getX(), buildingCoordinate.getY(), schoolAreaList)) {
            throw new WmSchCantException(BIZ_PARA_ERROR, "楼宇(" + editBuildingDetailDTO.getBuildingName() + ")扎点不在学校范围外");
        }
    }

    /**
     * 校验楼宇编辑范围时保存的信息
     * @param wmScSchoolBuildingSaveDTO wmScSchoolBuildingSaveDTO
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     * @throws TException org.apache.thrift.TException
     */
    public void checkSchoolBuildingAreaInfo(WmScSchoolBuildingSaveDTO wmScSchoolBuildingSaveDTO) throws WmSchCantException, TException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_1", "com.sankuai.meituan.waimai.customer.service.sc.WmScSchoolBuildingService.checkSchoolBuildingAreaInfo(com.sankuai.meituan.waimai.thrift.customer.domain.sc.WmScSchoolBuildingSaveDTO)");
        log.info("[WmScSchoolBuildingService.checkSchoolBuildingAreaInfo] input param: wmScSchoolBuildingSaveDTO = {}",
                JSONObject.toJSONString(wmScSchoolBuildingSaveDTO));
        if (wmScSchoolBuildingSaveDTO == null
                || wmScSchoolBuildingSaveDTO.getSchoolPrimaryId() == null
                || wmScSchoolBuildingSaveDTO.getEditBuildingDetailDTO() == null
                || CollectionUtils.isEmpty(wmScSchoolBuildingSaveDTO.getWmScSchoolBuildingDetailDTOList())) {
            log.error("[WmScSchoolBuildingService.checkSchoolBuildingAreaInfo] wmScSchoolBuildingDetailDTO is null.");
            throw new WmSchCantException(BIZ_PARA_ERROR, "学校楼宇信息为空");
        }

        WmScSchoolBuildingDetailDTO editBuildingDetailDTO = wmScSchoolBuildingSaveDTO.getEditBuildingDetailDTO();
        if (StringUtils.isBlank(editBuildingDetailDTO.getBuildingCoordinate())
                || "[]".equals(editBuildingDetailDTO.getBuildingCoordinate())) {
            throw new WmSchCantException(BIZ_PARA_ERROR, "请先维护楼宇扎点后再维护楼宇范围");
        }

        if (StringUtils.isBlank(editBuildingDetailDTO.getBuildingArea())
                || "[]".equals(editBuildingDetailDTO.getBuildingArea())) {
            throw new WmSchCantException(BIZ_PARA_ERROR, "楼宇范围信息为空");
        }
        // 校验楼宇范围是否交叉
        if (!checkAreaInfoService.checkAreaIsIntersect(editBuildingDetailDTO.getBuildingArea())) {
            throw new WmSchCantException(BIZ_PARA_ERROR, "楼宇(" + editBuildingDetailDTO.getBuildingName() + ")范围不允许出现交叉");
        }
        WmScPoint buildingCoordinate = getSchoolBuilidngCoordinate(editBuildingDetailDTO.getBuildingCoordinate());
        // 判断楼宇扎点是否在楼宇范围内
        if (!WmRtreeUtil.withinArea(buildingCoordinate.getX(), buildingCoordinate.getY(), editBuildingDetailDTO.getBuildingArea())) {
            throw new WmSchCantException(BIZ_PARA_ERROR, "楼宇(" + editBuildingDetailDTO.getBuildingName() + ")扎点不在楼宇范围内");
        }
        // 获取学校范围信息
        List<String> schoolAreaList = wmScSchoolAreaService.getSchoolAreaListBySchoolPrimaryId(wmScSchoolBuildingSaveDTO.getSchoolPrimaryId());
        if (CollectionUtils.isEmpty(schoolAreaList)) {
            throw new WmSchCantException(BIZ_PARA_ERROR, "不存在学校范围, 无法编辑楼宇信息");
        }
        // 校验楼宇范围是否位于校内
        if (editBuildingDetailDTO.getBuildingLocation().equals((int) SchoolBuildingLocationEnum.INSCHOOL.getType())
                && !checkAreaInfoService.areaContains(schoolAreaList, editBuildingDetailDTO.getBuildingArea())) {
            throw new WmSchCantException(BIZ_PARA_ERROR, "楼宇(" + editBuildingDetailDTO.getBuildingName() + ")范围不在学校范围内");
        }
        // 校验楼宇范围是否位于校外
        List<String> checkBuildingOutSchoolList = new ArrayList<>(schoolAreaList);
        checkBuildingOutSchoolList.add(editBuildingDetailDTO.getBuildingArea());
        if (editBuildingDetailDTO.getBuildingLocation().equals((int) SchoolBuildingLocationEnum.OUTOFSCHOOL.getType())
                && checkAreaInfoService.checkAreaListIntersect(checkBuildingOutSchoolList)) {
            throw new WmSchCantException(BIZ_PARA_ERROR, "楼宇(" + editBuildingDetailDTO.getBuildingName() + ")范围不在学校范围外");
        }
        // 楼宇范围之间不能存在交集
        List<WmScSchoolBuildingDetailDTO> wmScSchoolBuildingDetailDTOList = wmScSchoolBuildingSaveDTO.getWmScSchoolBuildingDetailDTOList();
        for (WmScSchoolBuildingDetailDTO dto : wmScSchoolBuildingDetailDTOList) {
            if (StringUtils.isBlank(dto.getBuildingArea()) || "[]".equals(dto.getBuildingArea())) {
                continue;
            }

            if (dto.getBuildingId().equals(editBuildingDetailDTO.getBuildingId())) {
                continue;
            }

            if (checkAreaInfoService.intersects(dto.getBuildingArea(), editBuildingDetailDTO.getBuildingArea())) {
                throw new WmSchCantException(BIZ_PARA_ERROR, "学校楼宇范围存在交集");
            }
        }
    }

    /**
     * 获取学校楼宇坐标信息
     * @return 楼宇坐标信息
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    public WmScPoint getSchoolBuilidngCoordinate(String buildingCoordinate) throws WmSchCantException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_2", "com.sankuai.meituan.waimai.customer.service.sc.WmScSchoolBuildingService.getSchoolBuilidngCoordinate(java.lang.String)");
        List<WmScPoint> wmScPoints = JSONObject.parseArray(buildingCoordinate, WmScPoint.class);
        if (wmScPoints.size() != 1) {
            throw new WmSchCantException(BIZ_PARA_ERROR, "楼宇扎点数有且仅有一个");
        }
        return wmScPoints.get(0);
    }

    /**
     * 查询楼宇位置=校内但不在学校范围内的楼宇列表
     * @param schoolPrimaryId 学校主键ID
     * @param wmScSchoolAoiDTOList 学校范围信息列表
     * @return 楼宇列表
     */
    public List<WmScSchoolBuildingDO> getSchoolBuildingWithLocationNotMatch(Integer schoolPrimaryId, List<WmScSchoolAoiDTO> wmScSchoolAoiDTOList)
            throws WmSchCantException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.service.sc.WmScSchoolBuildingService.getSchoolBuildingWithLocationNotMatch(java.lang.Integer,java.util.List)");
        log.info("[WmScSchoolBuildingService.getIllegalSchoolBuildingInSchoolArea] input param: schoolPrimaryId = {}, wmScSchoolAoiDTOList = {}",
                schoolPrimaryId, JSONObject.toJSONString(wmScSchoolAoiDTOList));
        if (CollectionUtils.isEmpty(wmScSchoolAoiDTOList)) {
            throw new WmSchCantException(BIZ_PARA_ERROR, "学校范围不能为空");
        }
        List<WmScSchoolBuildingDO> resList = new ArrayList<>();

        List<WmScSchoolBuildingDO> wmScSchoolBuildingDOList = wmScSchoolBuildingMapper.selectBySchoolPrimaryId(schoolPrimaryId);
        if (CollectionUtils.isEmpty(wmScSchoolBuildingDOList)) {
            log.info("[WmScSchoolBuildingService.getIllegalSchoolBuildingInSchoolArea] wmScSchoolBuildingDOList is empty, return.");
            return resList;
        }

        List<String> schoolAreaList = wmScSchoolAoiDTOList.stream()
                .map(WmScSchoolAoiDTO::getAoiArea)
                .filter(area -> StringUtils.isNotBlank(area) && !"[]".equals(area))
                .collect(Collectors.toList());

        for (WmScSchoolBuildingDO wmScSchoolBuildingDO : wmScSchoolBuildingDOList) {
            if (StringUtils.isBlank(wmScSchoolBuildingDO.getBuildingCoordinate())
                    || "[]".equals(wmScSchoolBuildingDO.getBuildingCoordinate())) {
                continue;
            }
            // 楼宇坐标经纬度
            List<WmScPoint> wmScPoints = JSONObject.parseArray(wmScSchoolBuildingDO.getBuildingCoordinate(), WmScPoint.class);
            if (CollectionUtils.isEmpty(wmScPoints)) {
                continue;
            }
            int buildingLatitude = wmScPoints.get(0).getX();
            int buidlingLongitude = wmScPoints.get(0).getY();

            // 校验楼宇坐标与学校范围的关系不通过
            if (wmScSchoolBuildingDO.getBuildingLocation().equals((int) SchoolBuildingLocationEnum.INSCHOOL.getType())
                    && !WmRtreeUtil.withinAreaList(buildingLatitude, buidlingLongitude, schoolAreaList)) {
                resList.add(wmScSchoolBuildingDO);
            }

            if (wmScSchoolBuildingDO.getBuildingLocation().equals((int) SchoolBuildingLocationEnum.OUTOFSCHOOL.getType())
                    && WmRtreeUtil.withinAreaList(buildingLatitude, buidlingLongitude, schoolAreaList)) {
                resList.add(wmScSchoolBuildingDO);
            }
        }
        log.info("[WmScSchoolBuildingService.getIllegalSchoolBuildingInSchoolArea] resList = {}", JSONObject.toJSONString(resList));
        return resList;
    }

    /**
     * 更新学校范围时，重新计算学校楼宇状态并更新
     * @param schoolAreaBo 学校范围信息
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    public void updateSchoolBuildingStatusBySchoolArea(SchoolAreaBo schoolAreaBo) throws WmSchCantException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_2", "com.sankuai.meituan.waimai.customer.service.sc.WmScSchoolBuildingService.updateSchoolBuildingStatusBySchoolArea(com.sankuai.meituan.waimai.thrift.customer.domain.sc.SchoolAreaBo)");
        log.info("[WmScSchoolBuildingService.updateSchoolBuildingStatusWithSchoolArea] input param: schoolAreaBo = {}",
                JSONObject.toJSONString(schoolAreaBo));
        if (schoolAreaBo == null || schoolAreaBo.getSchoolId() <= 0) {
            throw new WmSchCantException(BIZ_PARA_ERROR, "学校ID不能为空");
        }

        if (CollectionUtils.isEmpty(schoolAreaBo.getWmScSchoolAoiDTOList())) {
            throw new WmSchCantException(BIZ_PARA_ERROR, "学校范围信息为空");
        }

        List<WmScSchoolBuildingDO> wmScSchoolBuildingDOList = wmScSchoolBuildingMapper.selectBySchoolPrimaryId(schoolAreaBo.getSchoolId());
        if (CollectionUtils.isEmpty(wmScSchoolBuildingDOList)) {
            log.info("[WmScSchoolBuildingService.updateSchoolBuildingStatusWithSchoolArea] wmScSchoolBuildingDOList is empty, return.");
            return;
        }

        List<WmScSchoolAoiDTO> wmScSchoolAoiDTOList = schoolAreaBo.getWmScSchoolAoiDTOList();
        List<String> schoolAreaList = wmScSchoolAoiDTOList.stream()
                .map(WmScSchoolAoiDTO::getAoiArea)
                .filter(area -> StringUtils.isNotBlank(area) && !"[]".equals(area))
                .collect(Collectors.toList());

        WmEmploy wmEmploy = wmEmployClient.getEmployById(schoolAreaBo.getCuid().intValue());
        Integer userId = schoolAreaBo.getCuid().intValue();
        String userName = wmEmploy == null ? "系统自动更新" : wmEmploy.getName() + "(" + wmEmploy.getMisId() + ")";

        for (WmScSchoolBuildingDO wmScSchoolBuildingDOBefore : wmScSchoolBuildingDOList)  {
            Integer buildingStatus = getSchoolBuildingStatusWithSchoolArea(wmScSchoolBuildingDOBefore, schoolAreaList);
            if (wmScSchoolBuildingDOBefore.getBuildingStatus().equals(buildingStatus)) {
                continue;
            }

            WmScSchoolBuildingDO wmScSchoolBuildingDOUpdate = new WmScSchoolBuildingDO();
            wmScSchoolBuildingDOUpdate.setId(wmScSchoolBuildingDOBefore.getId());
            wmScSchoolBuildingDOUpdate.setBuildingStatus(buildingStatus);
            wmScSchoolBuildingDOUpdate.setMuid(userId.longValue());

            int result = wmScSchoolBuildingMapper.updateByPrimaryKeySelective(wmScSchoolBuildingDOUpdate);
            if (result > 0) {
                // 记录到操作日志
                BeanUtils.copyProperties(wmScSchoolBuildingDOBefore, wmScSchoolBuildingDOUpdate);
                wmScSchoolBuildingDOUpdate.setBuildingStatus(buildingStatus);

                WmScSchoolBuildingLogBO wmScSchoolBuildingLogBO = new WmScSchoolBuildingLogBO();
                wmScSchoolBuildingLogBO.setUserId(userId);
                wmScSchoolBuildingLogBO.setUserName(userName);
                wmScSchoolBuildingLogBO.setWmScSchoolBuildingDoBefore(wmScSchoolBuildingDOBefore);
                wmScSchoolBuildingLogBO.setWmScSchoolBuildingDoAfter(wmScSchoolBuildingDOUpdate);
                wmScLogSchoolInfoService.recordSchoolBuildingUpdateLog(wmScSchoolBuildingLogBO);
            }
        }
    }

    /**
     * 更新学校范围时，重新计算学校楼宇状态并更新
     * @param schoolPrimaryId 学校主键ID
     * @param userId 用户ID
     * @param userName 用户名称
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    public void updateSchoolBuildingStatusBySchoolPrimaryId(Integer schoolPrimaryId, Integer userId, String userName) throws WmSchCantException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_1", "com.sankuai.meituan.waimai.customer.service.sc.WmScSchoolBuildingService.updateSchoolBuildingStatusBySchoolPrimaryId(java.lang.Integer,java.lang.Integer,java.lang.String)");
        log.info("[WmScSchoolBuildingService.updateSchoolBuildingStatusBySchoolPrimaryId] input param: schoolPrimaryId = {}, userId = {}, userName = {}",
                schoolPrimaryId, userId, userName);

        if (schoolPrimaryId == null || schoolPrimaryId <= 0) {
            throw new WmSchCantException(BIZ_PARA_ERROR, "学校ID不能为空");
        }

        List<WmScSchoolAreaDO> wmScSchoolAreaDOList = wmScSchoolAreaMapper.selectBySchoolId(schoolPrimaryId);
        if (CollectionUtils.isEmpty(wmScSchoolAreaDOList)) {
            log.info("[WmScSchoolBuildingService.updateSchoolBuildingStatusBySchoolPrimaryId] wmScSchoolAreaDOList is empty, return.");
            return;
        }

        List<String> schoolAreaList = wmScSchoolAreaDOList.stream()
                .map(WmScSchoolAreaDO::getArea)
                .filter(area -> StringUtils.isNotBlank(area) && !"[]".equals(area))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(schoolAreaList)) {
            log.info("[WmScSchoolBuildingService.updateSchoolBuildingStatusBySchoolPrimaryId] schoolAreaList is empty, return.");
            return;
        }
        log.info("[WmScSchoolBuildingService.updateSchoolBuildingStatusBySchoolPrimaryId] schoolAreaList = {}", JSONObject.toJSONString(schoolAreaList));

        List<WmScSchoolBuildingDO> wmScSchoolBuildingDOList = wmScSchoolBuildingMapper.selectBySchoolPrimaryId(schoolPrimaryId);
        if (CollectionUtils.isEmpty(wmScSchoolBuildingDOList)) {
            log.info("[WmScSchoolBuildingService.updateSchoolBuildingStatusBySchoolPrimaryId] wmScSchoolBuildingDOList is empty, return.");
            return;
        }

        for (WmScSchoolBuildingDO wmScSchoolBuildingDOBefore : wmScSchoolBuildingDOList)  {
            Integer buildingStatus = getSchoolBuildingStatusWithSchoolArea(wmScSchoolBuildingDOBefore, schoolAreaList);
            // 楼宇状态无变更, 直接返回
            if (wmScSchoolBuildingDOBefore.getBuildingStatus().equals(buildingStatus)) {
                log.info("[WmScSchoolBuildingService.updateSchoolBuildingStatusBySchoolPrimaryId] building status is the same. buildingId = {}",
                        wmScSchoolBuildingDOBefore.getId());
                continue;
            }

            WmScSchoolBuildingDO wmScSchoolBuildingDOUpdate = new WmScSchoolBuildingDO();
            wmScSchoolBuildingDOUpdate.setId(wmScSchoolBuildingDOBefore.getId());
            wmScSchoolBuildingDOUpdate.setBuildingStatus(buildingStatus);
            wmScSchoolBuildingDOUpdate.setMuid(userId.longValue());

            int result = wmScSchoolBuildingMapper.updateByPrimaryKeySelective(wmScSchoolBuildingDOUpdate);
            if (result > 0) {
                // 记录到操作日志
                BeanUtils.copyProperties(wmScSchoolBuildingDOBefore, wmScSchoolBuildingDOUpdate);
                wmScSchoolBuildingDOUpdate.setBuildingStatus(buildingStatus);

                WmScSchoolBuildingLogBO wmScSchoolBuildingLogBO = new WmScSchoolBuildingLogBO();
                wmScSchoolBuildingLogBO.setUserId(userId);
                wmScSchoolBuildingLogBO.setUserName(userName);
                wmScSchoolBuildingLogBO.setWmScSchoolBuildingDoBefore(wmScSchoolBuildingDOBefore);
                wmScSchoolBuildingLogBO.setWmScSchoolBuildingDoAfter(wmScSchoolBuildingDOUpdate);
                wmScLogSchoolInfoService.recordSchoolBuildingUpdateLog(wmScSchoolBuildingLogBO);
            }
        }

    }

}
