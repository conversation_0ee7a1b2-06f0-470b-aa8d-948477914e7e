package com.sankuai.meituan.waimai.customer.service.customer.check.customer;

import com.alibaba.fastjson.JSONObject;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerDB;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerRealAuthService;
import com.sankuai.meituan.waimai.customer.service.customer.check.BaseWmCustomerValidator;
import com.sankuai.meituan.waimai.customer.service.customer.check.IWmCustomerValidator;
import com.sankuai.meituan.waimai.customer.service.gray.WmCustomerFalseStoreGrayService;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerConstants;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerType;
import com.sankuai.meituan.waimai.thrift.customer.constant.customer.CertificateAuthTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.customer.PreAuthResultTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.PreAuthResultBO;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.ValidateResultBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.WmCustomerBasicBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 营业执照企业三要素认证
 */
@Service
@Slf4j
public class WmCustomerRealNameThreeEleVerifyValidator extends BaseWmCustomerValidator implements IWmCustomerValidator {

    @Autowired
    private WmCustomerRealAuthService wmCustomerRealAuthService;

    @Autowired
    private WmCustomerFalseStoreGrayService wmCustomerFalseStoreGrayService;


    @Override
    public ValidateResultBo valid(ValidateResultBo validateResultBo, WmCustomerBasicBo wmCustomerBasicBo, Boolean force, Boolean isAudit, Integer opUid) throws WmCustomerException {
        //非营业执照不做三要素认证
        if (wmCustomerBasicBo.getCustomerType() != CustomerType.CUSTOMER_TYPE_BUSINESS.getCode()) {
            return checkPass(validateResultBo);
        }
        //客户对象
        WmCustomerDB wmCustomerDB = null;
        //操作人ID
        Integer owenUid = null;
        if (wmCustomerBasicBo.getId() <= 0) {
            //新增客户
            owenUid = opUid;
            wmCustomerDB = new WmCustomerDB();
            wmCustomerDB.setCustomerNumber(wmCustomerBasicBo.getCustomerNumber());
            wmCustomerDB.setCustomerType(wmCustomerBasicBo.getCustomerType());
        } else {
            //编辑客户
            wmCustomerDB = getExistWmCustomer(wmCustomerBasicBo.getId());
            owenUid = (wmCustomerDB == null ? null : wmCustomerDB.getOwnerUid());
        }
        //校验是否需要三要素校验
        if (!wmCustomerFalseStoreGrayService.isNeedAuthGray(owenUid, wmCustomerDB, CertificateAuthTypeEnum.ENTERPRISE_THREE_ELEMENT_AUTH, true)) {
            return checkPass(validateResultBo);
        }
        //构造请求参数对象
        try {
            PreAuthResultBO result = wmCustomerRealAuthService.enterpriseThreeElePreAuth(wmCustomerBasicBo.getCustomerName(), wmCustomerBasicBo.getCustomerNumber(), wmCustomerBasicBo.getLegalPerson());
            if (result == null
                    || result.getResult() == PreAuthResultTypeEnum.SUCCESS.getType()
                    || result.getResult() == PreAuthResultTypeEnum.NO_RESULT.getType()) {
                return checkPass(validateResultBo);
            } else {
                return checkFail(validateResultBo, CustomerConstants.RESULT_CODE_VERIFY_INPUT_ERROR, result.getMsg());
            }
        } catch (Exception e) {
            log.error("资质主体三要素预认证失败 wmCustomerBasicBo={}", JSONObject.toJSONString(wmCustomerBasicBo), e);
        }

        return checkFail(validateResultBo, CustomerConstants.RESULT_CODE_VERIFY_INPUT_ERROR, "预认证服务异常");
    }

    /**
     * 是否营业执照
     *
     * @param wmCustomerDB
     * @return
     */
    private boolean isBusiness(WmCustomerDB wmCustomerDB) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.service.customer.check.customer.WmCustomerRealNameThreeEleVerifyValidator.isBusiness(com.sankuai.meituan.waimai.customer.domain.WmCustomerDB)");
        return (wmCustomerDB != null
                && wmCustomerDB.getCustomerType() != null
                && wmCustomerDB.getCustomerType() == CustomerType.CUSTOMER_TYPE_BUSINESS.getCode());
    }

}
