package com.sankuai.meituan.waimai.customer.service.sc.poi;

import com.alibaba.fastjson.JSONObject;
import com.dianping.lion.client.util.CollectionUtils;
import com.dianping.lion.client.util.StringUtils;
import com.google.common.base.Stopwatch;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.sankuai.meituan.waimai.crm.ticket.thrift.domain.WmTicketDto;
import com.sankuai.meituan.waimai.customer.adapter.WmAorServiceAdapter;
import com.sankuai.meituan.waimai.customer.adapter.WmCrmTicketThriftServiceAdapter;
import com.sankuai.meituan.waimai.customer.adapter.WmEmployClient;
import com.sankuai.meituan.waimai.customer.dao.sc.*;
import com.sankuai.meituan.waimai.customer.ddd.adapter.WmPoiQueryAdapter;
import com.sankuai.meituan.waimai.customer.domain.sc.*;
import com.sankuai.meituan.waimai.customer.dao.sc.WmCanteenMapper;
import com.sankuai.meituan.waimai.customer.dao.sc.WmScCanteenPoiTaskAuditMinutiaMapper;
import com.sankuai.meituan.waimai.customer.dao.sc.WmScCanteenPoiTaskMapper;
import com.sankuai.meituan.waimai.customer.domain.sc.WmCanteenDB;
import com.sankuai.meituan.waimai.customer.domain.sc.WmScCanteenPoiTaskAuditMinutiaDO;
import com.sankuai.meituan.waimai.customer.domain.sc.WmScCanteenPoiTaskAuditMinutiaSearchCondition;
import com.sankuai.meituan.waimai.customer.domain.sc.WmScCanteenPoiTaskDO;
import com.sankuai.meituan.waimai.customer.service.sc.area.WmRtreeUtil;
import com.sankuai.meituan.waimai.customer.service.sc.check.WmCanteenPoiCheckService;
import com.sankuai.meituan.waimai.customer.service.sc.dao.WmScCanteenDao;
import com.sankuai.meituan.waimai.customer.service.sc.workflow.WmCanteenPoiTaskService;
import com.sankuai.meituan.waimai.customer.service.sc.workflow.WmCanteenPoiTaskSimpleService;
import com.sankuai.meituan.waimai.customer.service.sign.applyecontract.WmEcontractApplyService;
import com.sankuai.meituan.waimai.infra.domain.WmEmploy;
import com.sankuai.meituan.waimai.poi.constants.attribute.WmPoiValidEnum;
import com.sankuai.meituan.waimai.thrift.constants.WmPoiFieldQueryConstant;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.*;
import com.sankuai.meituan.waimai.thrift.customer.domain.sc.*;
import com.sankuai.meituan.waimai.thrift.customer.domain.sc.dto.*;
import com.sankuai.meituan.waimai.thrift.domain.WmPoiAggre;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import com.sankuai.meituan.waimai.thrift.exception.WmSchCantException;
import lombok.extern.slf4j.Slf4j;
import org.apache.thrift.TException;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.sankuai.meituan.waimai.thrift.constants.WmPoiFieldQueryConstant.*;
import static com.sankuai.meituan.waimai.thrift.customer.constant.sc.WmScCodeConstants.BIZ_PARA_ERROR;

/**
 * 食堂门店服务
 */
@Slf4j
@Service
public class WmScCanteenPoiService {

    // 默认流程流转序号
    private static final int DEFAULT_CURRENT_AUDIT_ORDER = 0;
    // 默认流程节点序号
    private static final int DEFAULT_AUDIT_NODE_ORDER = 1;
    // 默认打标次数
    private static final int DEFAULT_ADD_TAG_NUM = 0;

    // 门店删除
    public static final int WM_POI_ID_IS_DELETE = 1;

    @Autowired
    private List<WmCanteenPoiTaskService> wmCanteenPoiTaskServiceList;

    @Autowired
    private List<WmCanteenPoiTaskSimpleService> wmCanteenPoiTaskSimpleServiceList;

    @Autowired
    private WmEcontractApplyService wmEcontractApplyService;

    @Autowired
    private WmEmployClient wmEmployClient;

    @Autowired
    private WmPoiQueryAdapter wmPoiQueryAdapter;

    @Autowired
    private WmCanteenMapper wmCanteenMapper;

    @Autowired
    private WmSchoolMapper wmSchoolMapper;

    @Autowired
    private WmScCanteenPoiTaskMapper wmScCanteenPoiTaskMapper;

    @Autowired
    private WmScCanteenPoiTaskAuditMinutiaMapper wmScCanteenPoiTaskAuditMinutiaMapper;

    @Autowired
    private WmScCanteenPoiAttributeMapper wmScCanteenPoiAttributeMapper;

    @Autowired
    private WmScCanteenPoiHistoryMapper wmScCanteenPoiHistoryMapper;

    @Autowired
    private WmScSchoolAreaMapper wmScSchoolAreaMapper;

    @Autowired
    private WmScCanteenDao wmScCanteenDao;

    @Autowired
    private WmScCanteenInfoService wmScCanteenInfoService;

    @Autowired
    private WmAorServiceAdapter wmAorServiceAdapter;

    @Autowired
    private WmScCanteenPoiTaskSimpleService wmScCanteenPoiTaskSimpleService;

    @Autowired
    private WmCrmTicketThriftServiceAdapter wmCrmTicketThriftServiceAdapter;

    @Autowired
    private WmCanteenPoiCheckService wmCanteenPoiCheckService;

    /**
     * 获取流程步骤
     */
    public WmCanteenPoiProgressDTO getPoiBindProgress(int canteenPrimaryKey) throws WmSchCantException {
        WmCanteenPoiProgressDTO poiProgressDTO = new WmCanteenPoiProgressDTO();
        poiProgressDTO.setCurrentOrder(DEFAULT_CURRENT_AUDIT_ORDER);
        poiProgressDTO.setAuditStatus(CanteenPoiAuditStatusV2Enum.WAITING_SUBMIT.getCode());
        poiProgressDTO.setAuditStatusDesc(CanteenPoiAuditStatusV2Enum.WAITING_SUBMIT.getName());
        poiProgressDTO.setPrgressSteps(Lists.newArrayList());

        // 2、默认使用BD流程
        WmCanteenDB wmCanteenDB = wmCanteenMapper.selectCanteenById(canteenPrimaryKey);
        CanteenAuditProgressEnum progressEnum = CanteenAuditProgressEnum.BD_CONTRACTOR_PROGRESS;
        if (wmCanteenDB.getCanteenAttribute() != null && wmCanteenDB.getCanteenAttribute() != CanteenAttributeEnum.CONTRACTOR.getTypeInt()) {
            progressEnum = CanteenAuditProgressEnum.BD_SINGLE_OR_DIRECT_PROGRESS;
        }
        Map<Integer, WmScCanteenPoiTaskAuditMinutiaDO> minutiaDOMap = Maps.newHashMap();
        // 3、获取最近一条的taskId（取目标食堂id）
        WmScCanteenPoiTaskDO wmScCanteenPoiTaskDO = wmScCanteenPoiTaskMapper.selectLatestTaskByCanteenPrimaryKey(canteenPrimaryKey);

        //4 .如果审核状态为已生效 但没有绑定任何门店的话 需要变成待提交 待提交的所有节点是灰色的
        List<WmScCanteenPoiAttributeDO> wmScCanteenPoiAttributeDOList = wmScCanteenPoiAttributeMapper.selectBycanteenPrimaryId(canteenPrimaryKey);

        boolean isEffective = false;
        boolean effectiveCanteenUnbindPoi = false;
        if (wmScCanteenPoiTaskDO != null) {
            CanteenPoiAuditStatusV2Enum taskStatusEnum = CanteenPoiAuditStatusV2Enum.of(wmScCanteenPoiTaskDO.getAuditStatus());
            if (taskStatusEnum != null) {
                poiProgressDTO.setAuditStatus(taskStatusEnum.getCode());
                poiProgressDTO.setAuditStatusDesc(taskStatusEnum.getName());
                isEffective = taskStatusEnum == CanteenPoiAuditStatusV2Enum.EFFECTED;
                if(taskStatusEnum == CanteenPoiAuditStatusV2Enum.EFFECTED && CollectionUtils.isEmpty(wmScCanteenPoiAttributeDOList)){
                    poiProgressDTO.setAuditStatus(CanteenPoiAuditStatusV2Enum.WAITING_SUBMIT.getCode());
                    poiProgressDTO.setAuditStatusDesc(CanteenPoiAuditStatusV2Enum.WAITING_SUBMIT.getName());
                    isEffective = false;
                    effectiveCanteenUnbindPoi = true;
                }
            }
            List<WmScCanteenPoiTaskAuditMinutiaDO> minutiaDOList = wmScCanteenPoiTaskAuditMinutiaMapper.selectByCanPoiTaskId(wmScCanteenPoiTaskDO.getId());
            minutiaDOMap = minutiaDOList.stream().collect(Collectors.toMap(WmScCanteenPoiTaskAuditMinutiaDO::getAuditNode, item -> item));
            // 获取对应的流程id
            CanteenAuditProgressEnum taskProgressEnum = CanteenAuditProgressEnum.of(wmScCanteenPoiTaskDO.getAuditNodeType());
            if (taskProgressEnum != null) {
                progressEnum = taskProgressEnum;
            }
        }

        int auditNodeOrder = DEFAULT_AUDIT_NODE_ORDER;
        int currentOrder = DEFAULT_CURRENT_AUDIT_ORDER;
        // 5、加工流程步骤
        for (Integer auditNode : progressEnum.getProgress()) {
            CanteenAuditNodeEnum auditNodeEnum = CanteenAuditNodeEnum.of(auditNode);
            if (auditNodeEnum != null) {
                ProgressNodeDTO progressNodeDTO = new ProgressNodeDTO();
                progressNodeDTO.setCode(auditNodeEnum.getCode());
                progressNodeDTO.setName(auditNodeEnum.getName());
                progressNodeDTO.setOrder(auditNodeOrder);
                auditNodeOrder++;
                if (minutiaDOMap.get(auditNode) != null) {
                    WmScCanteenPoiTaskAuditMinutiaDO minutiaDO = minutiaDOMap.get(auditNode);
                    progressNodeDTO.setAuditResult(minutiaDO.getAuditResult());
                    if (minutiaDO.getAuditResult() == CanteeAuditNodeResultEnum.REJECT.getCode()) {
                        currentOrder = DEFAULT_CURRENT_AUDIT_ORDER;
                    } else {
                        currentOrder++;
                    }
                }
                poiProgressDTO.getPrgressSteps().add(progressNodeDTO);
            }
        }
        // 审核通过要流转到最后节点
        if (isEffective) {
            currentOrder = progressEnum.getProgress().size() + 1;
        }
        // 审核通过但是没有绑定上任何门店 就是待提交
        if(effectiveCanteenUnbindPoi){
            currentOrder = DEFAULT_CURRENT_AUDIT_ORDER;
        }
        poiProgressDTO.setCurrentOrder(currentOrder);
        return poiProgressDTO;
    }

    /**
     * 食堂门店流程确认中重发短信
     * @param canteenPrimaryKey 食堂主键ID
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     * @throws TException org.apache.thrift.TException
     */
    public void reSendEContractSMS(int canteenPrimaryKey) throws WmSchCantException, TException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.service.sc.poi.WmScCanteenPoiService.reSendEContractSMS(int)");
        log.info("reSendEContractSMS canteenPrimaryKey={}",canteenPrimaryKey);
        WmScCanteenPoiTaskDO wmScCanteenPoiTaskDO = wmScCanteenPoiTaskMapper.selectLatestTaskByCanteenPrimaryKey(canteenPrimaryKey);
        if (wmScCanteenPoiTaskDO == null
                || wmScCanteenPoiTaskDO.getAuditStatus() == null
                || wmScCanteenPoiTaskDO.getAuditStatus() != CanteenPoiAuditStatusV2Enum.CONFIRMING.getCode()) {
            throw new WmSchCantException(WmScCodeConstants.BIZ_PARA_ERROR, "重新发送短信参数异常");
        }

        WmScCanteenPoiTaskAuditMinutiaSearchCondition condition = new WmScCanteenPoiTaskAuditMinutiaSearchCondition();
        condition.setCanteenPoiTaskId(wmScCanteenPoiTaskDO.getId());
        condition.setAuditSystemType(CanteenAuditSystemEnum.ELECTRONIC_SIGN.getCode());
        condition.setAuditResult(CanteeAuditNodeResultEnum.AUDITING.getCode());
        List<WmScCanteenPoiTaskAuditMinutiaDO> minutiaDOList = wmScCanteenPoiTaskAuditMinutiaMapper.selectByCondition(condition);
        if (CollectionUtils.isEmpty(minutiaDOList)) {
            log.error("重新发送短信异常：未找到对应的审核明细任务 canteenPrimaryKey={}", canteenPrimaryKey);
            throw new WmSchCantException(WmScCodeConstants.SERVER_ERROR, "重新发送短信参数异常,请稍后重试");
        }

        String auditSystemId = minutiaDOList.get(minutiaDOList.size() - 1).getAuditSystemId();
        if (StringUtils.isBlank(auditSystemId)) {
            log.error("重新发送短信异常：未找到对应的电子合同ID canteenPrimaryKey={}", canteenPrimaryKey);
            throw new WmSchCantException(WmScCodeConstants.SERVER_ERROR, "重新发送短信参数异常,请稍后重试");
        }

        try {
            wmEcontractApplyService.reSendEContractSMS(auditSystemId);
        } catch (WmCustomerException e) {
            log.error("[食堂管理]调用电子合同重新发送信息异常,canteenId={},electronicId={}", canteenPrimaryKey, auditSystemId, e);
            throw new WmSchCantException(WmScCodeConstants.ECONTRON_EXCEPTION, "调用电子合同重新发送信息失败:" + e.getMsg());
        }
    }

    /**
     * 查询食堂已绑定的门店列表
     * @param wmCanteenPoiQueryDTO 查询入参
     * @return 食堂已绑定的门店列表
     * @throws TException org.apache.thrift.TException
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    public WmCanteenPoiDTO getCanteensPoiInfo(WmCanteenPoiQueryDTO wmCanteenPoiQueryDTO) throws TException, WmSchCantException {
        log.info("[getCanteensPoiInfo] wmCanteenPoiQueryDTO={}", JSONObject.toJSONString(wmCanteenPoiQueryDTO));
        if (wmCanteenPoiQueryDTO == null || wmCanteenPoiQueryDTO.getCanteenPrimaryKey() <= 0) {
            throw new WmSchCantException(WmScCodeConstants.BIZ_PARA_ERROR, "查询食堂已绑定的门店列表参数异常");
        }

        WmCanteenPoiDTO wmCanteenPoiDTO = new WmCanteenPoiDTO();
        wmCanteenPoiDTO.setTotal(0);
        wmCanteenPoiDTO.setWmCanPoiVos(Lists.newArrayList());

        // 获取当前食堂已绑定门店列表信息
        List<Long> wmPoiIdList = getWmPoiIdListByCanteenPoiQueryDTO(wmCanteenPoiQueryDTO);
        if (CollectionUtils.isEmpty(wmPoiIdList)) {
            return wmCanteenPoiDTO;
        }
        wmCanteenPoiDTO.setTotal(wmPoiIdList.size());

        // 获取门店信息
        List<WmPoiAggre> wmPoiAggreList = wmPoiQueryAdapter.getWmPoiAggre(wmPoiIdList, Sets.newHashSet(
                WmPoiFieldQueryConstant.WM_POI_FIELD_WM_POI_ID,
                WmPoiFieldQueryConstant.WM_POI_FIELD_NAME,
                WmPoiFieldQueryConstant.WM_POI_FIELD_VALID,
                WmPoiFieldQueryConstant.WM_POI_FIELD_FIRST_ONLINE_TIME,
                WmPoiFieldQueryConstant.WM_POI_FIELD_AOR_ID,
                WmPoiFieldQueryConstant.WM_POI_FIELD_SUB_WM_POI_TYPE)
        );
        Map<Long, WmPoiAggre> wmPoiAggreMap = wmPoiAggreList.stream()
                .collect(Collectors.toMap(WmPoiAggre::getWm_poi_id, item -> item));

        // 获取门店打标历史
        Map<Long, Integer> poiAddTagNumMap = getAddTagNum(wmPoiIdList);

        // 获取解换绑次数
        Map<Long,Integer> wmPoiIdTransferCountMap = wmCanteenPoiCheckService.getRemainingTransferCount(wmPoiIdList);

        // 获取待解绑信息
        for (Long wmPoiId : wmPoiIdList) {
            WmCanteenPoiInfoDTO wmCanteenPoiInfoDTO = new WmCanteenPoiInfoDTO();
            wmCanteenPoiInfoDTO.setWmPoiId(wmPoiId);
            if (wmPoiAggreMap.get(wmPoiId) != null) {
                WmPoiAggre wmPoiAggre = wmPoiAggreMap.get(wmPoiId);
                // 判断门店状态是否满足查询条件
                if (!checkCanteenPoiStatus(wmCanteenPoiQueryDTO.getPoiStatus(), wmPoiAggre.getValid())) {
                    continue;
                }
                if (!checkCanteenPoiSubWmPoi(wmCanteenPoiQueryDTO.getSubWmPoi(), wmPoiAggre.getSub_wm_poi_type())) {
                    continue;
                }

                wmCanteenPoiInfoDTO.setWmPoiName(wmPoiAggre.getName());
                wmCanteenPoiInfoDTO.setWmPoiStatus(wmPoiAggre.getValid());
                WmPoiValidEnum wmPoiValidEnum = WmPoiValidEnum.of(wmPoiAggre.getValid());
                if (wmPoiValidEnum != null) {
                    wmCanteenPoiInfoDTO.setWmPoiStatusDes(wmPoiValidEnum.getDesc());
                }
                wmCanteenPoiInfoDTO.setFirstOnlineTime((long) wmPoiAggre.getFirst_online_time());
                wmCanteenPoiInfoDTO.setAorId((int) wmPoiAggre.getAor_id());
                wmCanteenPoiInfoDTO.setAorName(wmAorServiceAdapter.getAorNameByAorId(wmCanteenPoiInfoDTO.getAorId()));

                // 是否子门店
                if (wmPoiAggre.getSub_wm_poi_type() > 0) {
                    wmCanteenPoiInfoDTO.setSubWmPoi((int) CanteenPoiSubWmPoiEnum.YES.getType());
                    wmCanteenPoiInfoDTO.setSubWmPoiDesc(CanteenPoiSubWmPoiEnum.YES.getName());
                } else {
                    wmCanteenPoiInfoDTO.setSubWmPoi((int) CanteenPoiSubWmPoiEnum.NO.getType());
                    wmCanteenPoiInfoDTO.setSubWmPoiDesc(CanteenPoiSubWmPoiEnum.NO.getName());
                }
            }

            // 打标次数
            if (poiAddTagNumMap.get(wmPoiId) != null) {
                wmCanteenPoiInfoDTO.setAddTagNum(poiAddTagNumMap.get(wmPoiId));
            } else {
                wmCanteenPoiInfoDTO.setAddTagNum(DEFAULT_ADD_TAG_NUM);
            }

            // 绑定状态
            wmCanteenPoiInfoDTO.setBindStatus(CanteenPoiBindStatusEnum.HAS_BIND.getCode());
            wmCanteenPoiInfoDTO.setBindStatusDesc(CanteenPoiBindStatusEnum.HAS_BIND.getName());

            // 解换绑次数设置
            wmCanteenPoiInfoDTO.setUnbindOperationCount(wmPoiIdTransferCountMap.get(wmPoiId));

            wmCanteenPoiDTO.getWmCanPoiVos().add(wmCanteenPoiInfoDTO);
        }

        log.info("[WmScCanteenPoiService.getCanteensPoiInfo] wmCanteenPoiDTO = {}", JSONObject.toJSONString(wmCanteenPoiDTO));
        return wmCanteenPoiDTO;
    }

    private List<Long> getWmPoiIdListByCanteenPoiQueryDTO(WmCanteenPoiQueryDTO wmCanteenPoiQueryDTO) {
        // 获取当前食堂已绑定门店信息
        WmScCanteenPoiAttributeSearchCondition condition = new WmScCanteenPoiAttributeSearchCondition();
        condition.setCanteenPrimaryId(wmCanteenPoiQueryDTO.getCanteenPrimaryKey());
        condition.setWmPoiId(wmCanteenPoiQueryDTO.getWmPoiId());
        condition.setValid(ValidEnum.VALID.getTypeInt());
        List<WmScCanteenPoiAttributeDO> wmScCanteenPoiAttributeDOList = wmScCanteenPoiAttributeMapper.selectByCondition(condition);

        List<Long> wmPoiIdList = wmScCanteenPoiAttributeDOList.stream()
                .map(WmScCanteenPoiAttributeDO::getWmPoiId)
                .collect(Collectors.toList());

        log.info("[WmScCanteenPoiService.getWmPoiIdListByCanteenPoiQueryDTO] wmPoiIdList = {}", JSONObject.toJSONString(wmPoiIdList));
        return wmPoiIdList;
    }

    /**
     * 判断门店状态是否为查询条件中的门店状态
     * @param queryPoiStatus 查询条件-门店状态
     * @param realPoiStatus 实际门店状态
     * @return 状态相同返回true，否则false
     */
    private Boolean checkCanteenPoiStatus(Integer queryPoiStatus, Integer realPoiStatus) {
        if (queryPoiStatus == null || queryPoiStatus < 0) {
            // 查询条件为全部
            return true;
        }
        return queryPoiStatus.equals(realPoiStatus);
    }

    private Boolean checkCanteenPoiSubWmPoi(Integer querySubWmPoi, Integer realSubWmPoi) {
        if (querySubWmPoi == null || querySubWmPoi < 0) {
            // 查询条件为全部
            return true;
        }

        if (querySubWmPoi.equals((int) CanteenPoiSubWmPoiEnum.YES.getType())) {
            return realSubWmPoi > 0;
        } else {
            return realSubWmPoi <= 0;
        }
    }

    /**
     * 获取门店打标次数
     */
    private Map<Long, Integer> getAddTagNum(List<Long> wmPoiIdList) {
        List<WmScCanteenPoiHistoryBO> wmScCanteenPoiHistoryDOList = wmScCanteenPoiHistoryMapper.getAddTagNum(wmPoiIdList);
        log.info("[WmScCanteenPoiService.getAddTagNum] wmPoiIdList = {}, wmScCanteenPoiHistoryDOList= {}",
                JSONObject.toJSONString(wmPoiIdList), JSONObject.toJSONString(wmScCanteenPoiHistoryDOList));
        if (CollectionUtils.isEmpty(wmScCanteenPoiHistoryDOList)) {
            return Maps.newHashMap();
        }

        return wmScCanteenPoiHistoryDOList.stream()
                .collect(Collectors.toMap(WmScCanteenPoiHistoryBO::getWmPoiId, WmScCanteenPoiHistoryBO::getAddTagNum));
    }

    /**
     * 获取食堂换绑中的任务
     * @param canteenPrimaryKey 食堂主键ID
     * @return 换绑中任务列表
     */
    public List<WmScCanteenPoiTaskDO> getCanteenTransferBindTask(Integer canteenPrimaryKey) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_2", "com.sankuai.meituan.waimai.customer.service.sc.poi.WmScCanteenPoiService.getCanteenTransferBindTask(java.lang.Integer)");
        log.info("[WmScCanteenPoiService.getCanteenTransferBindTask] input param: canteenPrimaryKey = {}", canteenPrimaryKey);
        // 获取换绑中的任务
        WmScCanteenPoiTaskSearchCondition taskSearchCondition = new WmScCanteenPoiTaskSearchCondition();
        taskSearchCondition.setCanteenIdFrom(canteenPrimaryKey);
        taskSearchCondition.setTaskType(CanteenPoiTaskTypeEnum.TRANSFER_BIND.getCode());
        taskSearchCondition.setAuditStatusList(
                Lists.newArrayList(
                        CanteenPoiAuditStatusV2Enum.FIRST_AUDITING.getCode(),
                        CanteenPoiAuditStatusV2Enum.SECOND_AUDITING.getCode(),
                        CanteenPoiAuditStatusV2Enum.CONFIRMING.getCode()
                )
        );
        return wmScCanteenPoiTaskMapper.selectByCondition(taskSearchCondition);
    }

    /**
     * 获取门店打标历史
     */
    public List<CanteenPoiHistoryDTO> getPoiHistory(CanteenPoiHistoryQueryDTO canteenPoiHistoryQueryDTO) throws WmSchCantException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.sc.poi.WmScCanteenPoiService.getPoiHistory(com.sankuai.meituan.waimai.thrift.customer.domain.sc.dto.CanteenPoiHistoryQueryDTO)");
        if (canteenPoiHistoryQueryDTO == null) {
            throw new WmSchCantException(WmScCodeConstants.BIZ_PARA_ERROR, "获取门店打标历史参数异常");
        }
        // 获取打标历史
        List<CanteenPoiHistoryDTO> canteenPoiHistoryDTOList = Lists.newArrayList();
        WmScCanteenPoiHistorySearchCondition condition = new WmScCanteenPoiHistorySearchCondition();
        BeanUtils.copyProperties(canteenPoiHistoryQueryDTO, condition);
        List<WmScCanteenPoiHistoryDO> wmScCanteenPoiHistoryDOList = wmScCanteenPoiHistoryMapper.selectByCondition(condition);
        if (CollectionUtils.isEmpty(wmScCanteenPoiHistoryDOList)) {
            return canteenPoiHistoryDTOList;
        }

        // 获取食堂信息
        List<Integer> canteenPrimaryKeyList = wmScCanteenPoiHistoryDOList.stream()
                .map(WmScCanteenPoiHistoryDO::getCanteenIdTo)
                .collect(Collectors.toList());
        List<WmCanteenDB> wmCanteenDBList = wmCanteenMapper.selectCanteensByIdsALL(canteenPrimaryKeyList);
        Map<Integer, WmCanteenDB> canteenMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(wmCanteenDBList)) {
            canteenMap = wmCanteenDBList.stream().collect(Collectors.toMap(WmCanteenDB::getId, item -> item));
        }

        // 获取操作人信息
        List<Integer> userIdList = wmScCanteenPoiHistoryDOList.stream()
                .map(WmScCanteenPoiHistoryDO::getUserId)
                .collect(Collectors.toList());
        List<WmEmploy> empList = wmEmployClient.getByIds(userIdList);
        Map<Integer, WmEmploy> wmEmployMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(empList)) {
            wmEmployMap = empList.stream().collect(Collectors.toMap(WmEmploy::getUid, emp -> emp));
        }

        // 加工打标信息
        for (WmScCanteenPoiHistoryDO wmScCanteenPoiHistoryDO : wmScCanteenPoiHistoryDOList) {
            CanteenPoiHistoryDTO historyDTO = new CanteenPoiHistoryDTO();
            historyDTO.setWmPoiId(wmScCanteenPoiHistoryDO.getWmPoiId());
            historyDTO.setCtime(wmScCanteenPoiHistoryDO.getCtime());
            historyDTO.setCanteenId(wmScCanteenPoiHistoryDO.getCanteenIdTo());
            WmCanteenDB wmCanteenDB = canteenMap.get(wmScCanteenPoiHistoryDO.getCanteenIdTo());
            if (wmCanteenDB != null) {
                historyDTO.setCanteenName(wmCanteenDB.getCanteenName());
                historyDTO.setCanteenId(wmCanteenDB.getCanteenId());
                historyDTO.setIsScDelete(false);
                if (wmCanteenDB.getValid() == ValidEnum.INVALID.getTypeInt()) {
                    historyDTO.setCanteenName("食堂已删除");
                    historyDTO.setIsScDelete(true);
                }
            } else {
                historyDTO.setCanteenName("食堂已删除");
                historyDTO.setIsScDelete(true);
            }
            historyDTO.setUserId(wmScCanteenPoiHistoryDO.getUserId());
            if (wmEmployMap.get(wmScCanteenPoiHistoryDO.getUserId()) != null) {
                WmEmploy employ = wmEmployMap.get(wmScCanteenPoiHistoryDO.getUserId());
                historyDTO.setUserName(String.format("%s(%s)", employ.getName(), employ.getMisId()));
            } else {
                historyDTO.setUserName(String.format("%s(%s)", wmScCanteenPoiHistoryDO.getUserName(), "未知"));
            }
            canteenPoiHistoryDTOList.add(historyDTO);
        }
        return canteenPoiHistoryDTOList;
    }


    private Optional<WmCanteenPoiTaskService> getTaskService(Integer taskType) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.service.sc.poi.WmScCanteenPoiService.getTaskService(java.lang.Integer)");
        if (taskType == null) {
            return Optional.empty();
        }
        return wmCanteenPoiTaskServiceList.stream().filter(i -> i.getTaskType().getCode() == taskType).findAny();
    }

    private Optional<WmCanteenPoiTaskSimpleService> getTaskSimpleService(Integer taskType) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_1", "com.sankuai.meituan.waimai.customer.service.sc.poi.WmScCanteenPoiService.getTaskSimpleService(java.lang.Integer)");
        if (taskType == null) {
            return Optional.empty();
        }
        return wmCanteenPoiTaskSimpleServiceList.stream().filter(i -> i.getTaskType().getCode() == taskType).findAny();
    }
    /**
     * 食堂绑定/换绑门店任务提交
     * @param wmScCanteenPoiTaskDTO wmScCanteenPoiTaskDTO
     * @return 任务ID
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     * @throws TException org.apache.thrift.TException
     */
    public Long submitTask(WmScCanteenPoiTaskDTO wmScCanteenPoiTaskDTO) throws WmSchCantException, TException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_2", "com.sankuai.meituan.waimai.customer.service.sc.poi.WmScCanteenPoiService.submitTask(com.sankuai.meituan.waimai.thrift.customer.domain.sc.dto.WmScCanteenPoiTaskDTO)");
        log.info("[WmScCanteenPoiService.submitTask] input: wmScCanteenPoiTaskDTO = {}", JSONObject.toJSONString(wmScCanteenPoiTaskDTO));
        Stopwatch sw = Stopwatch.createStarted();

        // 信息加工
        WmCanteenPoiTaskBO wmCanteenPoiTaskBO = buildTaskInfo(wmScCanteenPoiTaskDTO);
        // 获取处理服务
        Optional<WmCanteenPoiTaskService> taskService = getTaskService(wmCanteenPoiTaskBO.getTaskType());
        if (!taskService.isPresent()) {
            throw new WmSchCantException(WmScCodeConstants.SERVER_ERROR, "创建任务异常:处理服务未找到");
        }
        // 信息校验
        taskService.get().checkTask(wmCanteenPoiTaskBO);
        // 任务创建
        taskService.get().createTask(wmCanteenPoiTaskBO);
        // 是否已经生效，若生效进行生效动作
        if (wmCanteenPoiTaskBO.getAuditStatus() != null
                && wmCanteenPoiTaskBO.getAuditStatus() == CanteenPoiAuditStatusV2Enum.EFFECTED.getCode()) {
            taskService.get().effectTask(wmCanteenPoiTaskBO);
        }
        log.info("[WmScCanteenPoiService.submitTask] output: task id = {}, time(ms) = {}", wmCanteenPoiTaskBO.getId(), sw.elapsed(TimeUnit.MILLISECONDS));
        return wmCanteenPoiTaskBO.getId();
    }

    /**
     * 食堂绑定门店对象加工
     * @param wmScCanteenPoiTaskDTO wmScCanteenPoiTaskDTO
     * @return WmCanteenPoiTaskBO
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    private WmCanteenPoiTaskBO buildTaskInfo(WmScCanteenPoiTaskDTO wmScCanteenPoiTaskDTO) throws WmSchCantException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.service.sc.poi.WmScCanteenPoiService.buildTaskInfo(com.sankuai.meituan.waimai.thrift.customer.domain.sc.dto.WmScCanteenPoiTaskDTO)");
        if (wmScCanteenPoiTaskDTO == null) {
            throw new WmSchCantException(WmScCodeConstants.BIZ_PARA_ERROR, "创建任务参数异常");
        }
        WmCanteenPoiTaskBO wmCanteenPoiTaskBO = new WmCanteenPoiTaskBO();
        CanteenPoiTaskTypeEnum canteenPoiTaskTypeEnum = CanteenPoiTaskTypeEnum.of(wmScCanteenPoiTaskDTO.getTaskType());
        if (canteenPoiTaskTypeEnum == CanteenPoiTaskTypeEnum.UNKNOW) {
            throw new WmSchCantException(WmScCodeConstants.BIZ_PARA_ERROR, "创建任务参数异常");
        }
        wmCanteenPoiTaskBO.setTaskType(wmScCanteenPoiTaskDTO.getTaskType());

        if (canteenPoiTaskTypeEnum == CanteenPoiTaskTypeEnum.TRANSFER_BIND) {
            if (wmScCanteenPoiTaskDTO.getCanteenIdFrom() == null) {
                throw new WmSchCantException(WmScCodeConstants.BIZ_PARA_ERROR, "创建任务参数异常:原食堂必填");
            }
            if (wmScCanteenPoiTaskDTO.getTaskReasonType() == null && StringUtils.isBlank(wmScCanteenPoiTaskDTO.getTaskReason())) {
                throw new WmSchCantException(WmScCodeConstants.BIZ_PARA_ERROR, "创建任务参数异常:换绑原因必填");
            }
        }
        wmCanteenPoiTaskBO.setTaskReasonType(wmScCanteenPoiTaskDTO.getTaskReasonType());
        wmCanteenPoiTaskBO.setTaskReason(wmScCanteenPoiTaskDTO.getTaskReason());
        wmCanteenPoiTaskBO.setCanteenIdFrom(wmScCanteenPoiTaskDTO.getCanteenIdFrom());
        wmCanteenPoiTaskBO.setCanteenIdTo(wmScCanteenPoiTaskDTO.getCanteenIdTo());
        wmCanteenPoiTaskBO.setUserId(wmScCanteenPoiTaskDTO.getUserId());
        wmCanteenPoiTaskBO.setUserName(wmScCanteenPoiTaskDTO.getUserName());

        if (CollectionUtils.isEmpty(wmScCanteenPoiTaskDTO.getWmPoiIdList())) {
            throw new WmSchCantException(WmScCodeConstants.BIZ_PARA_ERROR, "创建任务参数异常:门店必填");
        }
        wmCanteenPoiTaskBO.setWmPoiIdList(wmScCanteenPoiTaskDTO.getWmPoiIdList());

        if (canteenPoiTaskTypeEnum == CanteenPoiTaskTypeEnum.TRANSFER_BIND) {
            WmCanteenDB wmCanteenFrom = wmScCanteenDao.getById(wmScCanteenPoiTaskDTO.getCanteenIdFrom());
            if (wmCanteenFrom == null) {
                throw new WmSchCantException(WmScCodeConstants.BIZ_PARA_ERROR, "创建任务参数异常:原食堂未找到");
            }
            wmCanteenPoiTaskBO.setCanteenFrom(wmCanteenFrom);
        }

        WmCanteenDB wmCanteenDB = wmScCanteenDao.getById(wmScCanteenPoiTaskDTO.getCanteenIdTo());
        if (wmCanteenDB == null) {
            throw new WmSchCantException(WmScCodeConstants.BIZ_PARA_ERROR, "创建任务参数异常:待绑食堂未找到");
        }
        wmCanteenPoiTaskBO.setCanteenTo(wmCanteenDB);

        WmSchoolDB wmSchoolDB = wmSchoolMapper.selectSchoolById(wmCanteenDB.getSchoolId());
        if (wmSchoolDB == null) {
            throw new WmSchCantException(WmScCodeConstants.BIZ_PARA_ERROR, "创建任务参数异常:待绑食堂学校未找到");
        }
        wmCanteenPoiTaskBO.setSchoolTo(wmSchoolDB);
        return wmCanteenPoiTaskBO;
    }

    /**
     * 取消食堂门店绑定/换绑任务
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    public void cancelTask(WmCanCancelBo bo) throws TException, WmSchCantException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_2", "com.sankuai.meituan.waimai.customer.service.sc.poi.WmScCanteenPoiService.cancelTask(com.sankuai.meituan.waimai.thrift.customer.domain.sc.WmCanCancelBo)");
        log.info("[WmScCanteenPoiService.cancelTask] input param: bo = {}", JSONObject.toJSONString(bo));
        // 通过食堂主键ID查询最近的一次任务记录
        WmScCanteenPoiTaskDO wmScCanteenPoiTaskDO = wmScCanteenPoiTaskMapper.selectLatestTaskByCanteenPrimaryKey(bo.getId());
        if (wmScCanteenPoiTaskDO == null) {
            throw new WmSchCantException(WmScCodeConstants.BIZ_PARA_ERROR, "审核任务不存在");
        }
        // 任务状态校验
        Integer auditStatus = wmScCanteenPoiTaskDO.getAuditStatus();
        if (auditStatus == null
                || auditStatus == CanteenPoiAuditStatusV2Enum.WAITING_SUBMIT.getCode()
                || auditStatus == CanteenPoiAuditStatusV2Enum.FAILED.getCode()
                || auditStatus == CanteenPoiAuditStatusV2Enum.EFFECTED.getCode()) {
            throw new WmSchCantException(WmScCodeConstants.BIZ_PARA_ERROR, "取消失败：此状态不可取消");
        }

        Long latestCanteenAuditId = wmScCanteenPoiTaskDO.getId();
        // 获取处理服务
        Optional<WmCanteenPoiTaskService> taskService = getTaskService(wmScCanteenPoiTaskDO.getTaskType());
        if (!taskService.isPresent()) {
            throw new WmSchCantException(WmScCodeConstants.SERVER_ERROR, "创建任务异常:处理服务未找到");
        }
        taskService.get().cancelTask(latestCanteenAuditId, bo.getUserId(), bo.getUserName());
    }

    /**
     * 获取不合法门店列表: 门店下线\门店食堂\门店坐标不在学校范围内
     * @param wmPoiIdList 需要校验的门店ID列表
     * @param schoolPrimaryId 门店所处学校主键id
     * @return List<WmScCanIllegalPoi>
     */
    public List<WmScCanIllegalPoi> getIllegalCanPois(List<Long> wmPoiIdList, int schoolPrimaryId) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.sc.poi.WmScCanteenPoiService.getIllegalCanPois(java.util.List,int)");
        log.info("getIllegalCanPois wmPoiIdList={},schoolPrimaryId={}", JSONObject.toJSONString(wmPoiIdList), schoolPrimaryId);
        // 1. 查询学校范围  todo:仅保留门店是否下线或者被删除
        List<WmScSchoolAreaDO> wmScSchoolAreaDOList = wmScSchoolAreaMapper.selectBySchoolId(schoolPrimaryId);
        List<WmScCanIllegalPoi> wmScCanIllegalPoiList = Lists.newLinkedList();
        // 2. 获取门店信息
        List<WmPoiAggre> wmPoiAggreList = wmPoiQueryAdapter.getWmPoiAggre(wmPoiIdList,
                Sets.newHashSet(
                        WM_POI_FIELD_WM_POI_ID,
                        WM_POI_FIELD_NAME,
                        WM_POI_FIELD_VALID,
                        WM_POI_FIELD_LATITUDE,
                        WM_POI_FIELD_LONGITUDE,
                        WM_POI_FIELD_IS_DELETE
                )
        );
        // 如果门店信息为空，则直接返回空列表
        if (CollectionUtils.isEmpty(wmPoiAggreList)) {
            return wmScCanIllegalPoiList;
        }

        // 3. 检查是否存在有效的学校区域
        boolean isHaveArea = false;
        for (WmScSchoolAreaDO wmScSchoolAreaDO : wmScSchoolAreaDOList) {
            if (org.apache.commons.lang3.StringUtils.isNotBlank(wmScSchoolAreaDO.getArea())) {
                isHaveArea = true;
                break;
            }
        }
        // 3.1 如果没有学校范围或没有有效区域
        if (CollectionUtils.isEmpty(wmScSchoolAreaDOList) || !isHaveArea) {
            log.error("getIllegalCanPois方法，学校未录入学校范围 wmPoiIdList={},schoolPrimary={}", wmPoiIdList, schoolPrimaryId);
            // 将所有门店标记为校外门店
            for (WmPoiAggre wmPoiAggre : wmPoiAggreList) {
                WmScCanIllegalPoi wmScCanIllegalPoi = new WmScCanIllegalPoi();
                wmScCanIllegalPoi.setWmPoiId(wmPoiAggre.getWm_poi_id());
                wmScCanIllegalPoi.setPoiName(wmPoiAggre.getName());
                wmScCanIllegalPoi.setSchoolPrimaryId(schoolPrimaryId);
                wmScCanIllegalPoi.setIllegalPoiType(WmIllegalCanPoiTypeEnum.POI_OUTSIDE_SCHOOL.getCode());
                wmScCanIllegalPoiList.add(wmScCanIllegalPoi);
            }
            return wmScCanIllegalPoiList;
        }
        // 4. 获取学校范围列表
        List<String> schoolAreaList = new ArrayList<>();
        for (WmScSchoolAreaDO wmScSchoolAreaDO : wmScSchoolAreaDOList) {
            if (org.apache.commons.lang3.StringUtils.isNotBlank(wmScSchoolAreaDO.getArea())) {
                schoolAreaList.add(wmScSchoolAreaDO.getArea());
            }
        }

        // 5. 校验门店状态
        for (WmPoiAggre wmPoiAggre : wmPoiAggreList) {
            WmScCanIllegalPoi wmScCanIllegalPoi = new WmScCanIllegalPoi();
            wmScCanIllegalPoi.setWmPoiId(wmPoiAggre.getWm_poi_id());
            wmScCanIllegalPoi.setPoiName(wmPoiAggre.getName());
            wmScCanIllegalPoi.setSchoolPrimaryId(schoolPrimaryId);
            // 5.1 校验门店是否下线
            if (wmPoiAggre.getValid() == CanteenPoiStatusEnum.OFFLINE.getCode()) {
                wmScCanIllegalPoi.setIllegalPoiType(WmIllegalCanPoiTypeEnum.POI_OFFLINE.getCode());
                wmScCanIllegalPoiList.add(wmScCanIllegalPoi);
                continue;
            }
            // 5.2 校验门店是否已删除
            if (wmPoiAggre.getIs_delete() == WM_POI_ID_IS_DELETE) {
                wmScCanIllegalPoi.setIllegalPoiType(WmIllegalCanPoiTypeEnum.POI_DELETED.getCode());
                wmScCanIllegalPoiList.add(wmScCanIllegalPoi);
                continue;
            }
            // 5.3 校验门店坐标是否在学校范围外
            if (!WmRtreeUtil.withinAreaList((int) wmPoiAggre.getLatitude(), (int) wmPoiAggre.getLongitude(), schoolAreaList)) {
                wmScCanIllegalPoi.setIllegalPoiType(WmIllegalCanPoiTypeEnum.POI_OUTSIDE_SCHOOL.getCode());
                wmScCanIllegalPoiList.add(wmScCanIllegalPoi);
            }
        }
        // 返回不合法的门店列表
        return wmScCanIllegalPoiList;
    }

    /**
     * 任务审批提交
     * @param minutiaBO：必传参数：操作系统、操作系统id、操作结果、操作人信息
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     * @throws TException org.apache.thrift.TException
     */
    public void commitTask(WmCanteenPoiTaskAuditMinutiaBO minutiaBO) throws WmSchCantException, TException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.sc.poi.WmScCanteenPoiService.commitTask(com.sankuai.meituan.waimai.customer.domain.sc.WmCanteenPoiTaskAuditMinutiaBO)");
        log.info("[WmScCanteenPoiService.commitTask] input param: minutiaBO = {}", JSONObject.toJSONString(minutiaBO));
        if (StringUtils.isBlank(minutiaBO.getAuditSystemId())) {
            throw new WmSchCantException(WmScCodeConstants.SERVER_ERROR, "任务审批参数异常");
        }

        WmScCanteenPoiTaskAuditMinutiaDO minutiaDO = wmScCanteenPoiTaskAuditMinutiaMapper.selectByAuditSystem(minutiaBO.getAuditSystemType(), minutiaBO.getAuditSystemId());
        if (minutiaDO == null) {
            throw new WmSchCantException(WmScCodeConstants.SERVER_ERROR, "任务审批参数异常,未找到任务明细");
        }

        WmScCanteenPoiTaskDO taskDO = wmScCanteenPoiTaskMapper.selectByPrimaryKey(minutiaDO.getCanteenPoiTaskId());
        if (taskDO == null) {
            throw new WmSchCantException(WmScCodeConstants.SERVER_ERROR, "任务审批参数异常,未找到审批任务");
        }

        minutiaBO.setAuditNode(minutiaDO.getAuditNode());
        minutiaBO.setCanteenPoiTaskId(taskDO.getId());
        CanteenPoiTaskTypeEnum taskTypeEnum = CanteenPoiTaskTypeEnum.of(taskDO.getTaskType());
        minutiaBO.setTaskTypeEnum(taskTypeEnum);

        // 获取处理服务
        Optional<WmCanteenPoiTaskService> taskService = getTaskService(taskDO.getTaskType());
        if (!taskService.isPresent()) {
            throw new WmSchCantException(WmScCodeConstants.SERVER_ERROR, "创建任务异常:处理服务未找到");
        }

        CanteenPoiAuditStatusV2Enum canteenPoiAuditStatusV2Enum = taskService.get().commitTask(minutiaBO);
        // 若任务生效, 则进行生效处理
        if (canteenPoiAuditStatusV2Enum == CanteenPoiAuditStatusV2Enum.EFFECTED) {
            WmCanteenPoiTaskBO taskBO = wmScCanteenInfoService.buildTaskBO(taskDO.getId());
            taskService.get().effectTask(taskBO);
        }
    }

    /**
     * 获取审核任务详情
     *
     * @param ticketId
     * @return
     * @throws TException
     * @throws WmSchCantException
     */
    public WmCanPoiTaskSumBo getAuditInfoByChildId(long ticketId) throws TException, WmSchCantException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.service.sc.poi.WmScCanteenPoiService.getAuditInfoByChildId(long)");
        log.info("getAuditInfoByChildId ticketId={}", ticketId);
        WmTicketDto ticketDto = wmCrmTicketThriftServiceAdapter.getTicketById((int) ticketId);
        if (ticketDto == null) {
            log.error("[getAuditInfoByChildId] ticketDto is null. auditSystemId = {}",ticketId);
            throw new WmSchCantException(BIZ_PARA_ERROR, "获取父任务id为空");
        }

        String parames = ticketDto.getParams();
        JSONObject paramesJsonObject = JSONObject.parseObject(parames);
        String taskId = paramesJsonObject.getString("taskId");
        WmScCanteenPoiTaskDO wmScCanteenPoiTaskDO = wmScCanteenPoiTaskSimpleService.getCanteenTransferBindTaskByTaskId(Long.valueOf(taskId));
        if (wmScCanteenPoiTaskDO == null) {
            throw new WmSchCantException(WmScCodeConstants.BIZ_PARA_ERROR, "查询任务系统查询任务异常");
        }
        WmCanPoiTaskSumBo wmCanPoiTaskSumBo = new WmCanPoiTaskSumBo();
        wmCanPoiTaskSumBo.setUserId(wmScCanteenPoiTaskDO.getUserId());
        wmCanPoiTaskSumBo.setUserName(String.format("%s（%s）", wmScCanteenPoiTaskDO.getUserName(), "未知"));
        WmEmploy wmEmploy = wmEmployClient.getEmployById(wmScCanteenPoiTaskDO.getUserId());
        if (wmEmploy != null) {
            wmCanPoiTaskSumBo.setUserName(String.format("%s（%s）", wmEmploy.getName(), wmEmploy.getMisId()));
        }
        wmCanPoiTaskSumBo.setCtime(wmScCanteenPoiTaskDO.getCtime());

        // 获取处理服务
        Optional<WmCanteenPoiTaskSimpleService> taskService = getTaskSimpleService(wmScCanteenPoiTaskDO.getTaskType());
        if (!taskService.isPresent()) {
            throw new WmSchCantException(WmScCodeConstants.SERVER_ERROR, "获取任务异常:处理服务未找到");
        }
        taskService.get().getAuditInfo(wmScCanteenPoiTaskDO, wmCanPoiTaskSumBo);
        return wmCanPoiTaskSumBo;
    }

    /**
     * 获取审核任务详情
     *
     * @param ticketId
     * @return
     * @throws TException
     * @throws WmSchCantException
     */
    public WmCanPoiTaskSumBo getCanteenUnbindAuditInfo(long ticketId) throws TException, WmSchCantException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_2", "com.sankuai.meituan.waimai.customer.service.sc.poi.WmScCanteenPoiService.getCanteenUnbindAuditInfo(long)");
        log.info("getAuditInfoByChildId ticketId={}", ticketId);
        WmTicketDto ticketDto = wmCrmTicketThriftServiceAdapter.getTicketById((int) ticketId);
        if (ticketDto == null) {
            log.error("[getAuditInfoByChildId] ticketDto is null. auditSystemId = {}",ticketId);
            throw new WmSchCantException(BIZ_PARA_ERROR, "获取父任务id为空");
        }
        WmScCanteenPoiTaskDO wmScCanteenPoiTaskDO = wmScCanteenPoiTaskSimpleService.getCanteenTransferBindTaskByTicketId(ticketDto.getParentTicketId());
        if (wmScCanteenPoiTaskDO == null) {
            throw new WmSchCantException(WmScCodeConstants.BIZ_PARA_ERROR, "查询任务系统查询任务异常");
        }
        WmCanPoiTaskSumBo wmCanPoiTaskSumBo = new WmCanPoiTaskSumBo();
        wmCanPoiTaskSumBo.setUserId(wmScCanteenPoiTaskDO.getUserId());
        wmCanPoiTaskSumBo.setUserName(String.format("%s（%s）", wmScCanteenPoiTaskDO.getUserName(), "未知"));
        WmEmploy wmEmploy = wmEmployClient.getEmployById(wmScCanteenPoiTaskDO.getUserId());
        if (wmEmploy != null) {
            wmCanPoiTaskSumBo.setUserName(String.format("%s（%s）", wmEmploy.getName(), wmEmploy.getMisId()));
        }
        wmCanPoiTaskSumBo.setCtime(wmScCanteenPoiTaskDO.getCtime());

        // 获取处理服务
        Optional<WmCanteenPoiTaskSimpleService> taskService = getTaskSimpleService(wmScCanteenPoiTaskDO.getTaskType());
        if (!taskService.isPresent()) {
            throw new WmSchCantException(WmScCodeConstants.SERVER_ERROR, "获取任务异常:处理服务未找到");
        }
        taskService.get().getAuditInfo(wmScCanteenPoiTaskDO, wmCanPoiTaskSumBo);
        return wmCanPoiTaskSumBo;
    }


    /**
     * 获取审核任务详情
     *
     * @param ticketId
     * @return
     * @throws TException
     * @throws WmSchCantException
     */
    public WmCanPoiTaskSumBo getAuditInfo(long ticketId) throws TException, WmSchCantException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_2", "com.sankuai.meituan.waimai.customer.service.sc.poi.WmScCanteenPoiService.getAuditInfo(long)");
        log.info("getAuditInfo ticketId={}", ticketId);
        WmScCanteenPoiTaskAuditMinutiaDO taskAuditMinutiaDO = wmScCanteenPoiTaskAuditMinutiaMapper.selectByAuditSystem(CanteenAuditSystemEnum.TASK_SYSTEM.getCode(),
                String.valueOf(ticketId));
        if (taskAuditMinutiaDO == null) {
            throw new WmSchCantException(WmScCodeConstants.BIZ_PARA_ERROR, "查询任务系统查询任务异常");
        }
        long canPoiTaskId = taskAuditMinutiaDO.getCanteenPoiTaskId();
        WmScCanteenPoiTaskDO wmScCanteenPoiTaskDO = wmScCanteenPoiTaskMapper.selectByPrimaryKey(canPoiTaskId);
        if (wmScCanteenPoiTaskDO == null) {
            throw new WmSchCantException(WmScCodeConstants.BIZ_PARA_ERROR, "查询任务系统查询任务异常");
        }
        WmCanPoiTaskSumBo wmCanPoiTaskSumBo = new WmCanPoiTaskSumBo();
        wmCanPoiTaskSumBo.setUserId(wmScCanteenPoiTaskDO.getUserId());
        wmCanPoiTaskSumBo.setUserName(String.format("%s（%s）", wmScCanteenPoiTaskDO.getUserName(), "未知"));
        WmEmploy wmEmploy = wmEmployClient.getEmployById(wmScCanteenPoiTaskDO.getUserId());
        if (wmEmploy != null) {
            wmCanPoiTaskSumBo.setUserName(String.format("%s（%s）", wmEmploy.getName(), wmEmploy.getMisId()));
        }
        wmCanPoiTaskSumBo.setCtime(taskAuditMinutiaDO.getCtime());

        // 获取处理服务
        Optional<WmCanteenPoiTaskService> taskService = getTaskService(wmScCanteenPoiTaskDO.getTaskType());
        if (!taskService.isPresent()) {
            throw new WmSchCantException(WmScCodeConstants.SERVER_ERROR, "获取任务异常:处理服务未找到");
        }
        taskService.get().getAuditInfo(wmScCanteenPoiTaskDO, wmCanPoiTaskSumBo);
        return wmCanPoiTaskSumBo;
    }


}
