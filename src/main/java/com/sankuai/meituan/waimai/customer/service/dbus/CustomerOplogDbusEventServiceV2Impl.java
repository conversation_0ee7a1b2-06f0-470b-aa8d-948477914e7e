package com.sankuai.meituan.waimai.customer.service.dbus;

import com.alibaba.fastjson.JSON;
import com.meituan.dbus.common.DbusUtils;
import com.meituan.dbus.common.StaticUtils;
import com.meituan.dbus.thriftV2.DataBusEventServiceV2;
import com.meituan.dbus.thriftV2.utils.ThriftV2Utils;
import com.sankuai.meituan.waimai.customer.ddd.domain.common.aggre.WmCustomerOplogAggre;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerOplogWithBLOBs;
import org.apache.thrift.TException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.Map;

@Service
public class CustomerOplogDbusEventServiceV2Impl implements DataBusEventServiceV2.Iface {

    private static final Logger LOGGER = LoggerFactory.getLogger(CustomerOplogDbusEventServiceV2Impl.class);

    @Override
    public String handleUpdate(Map<String, String> metaJsonData, String dataMapJson, String diffJson) throws TException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.service.dbus.CustomerOplogDbusEventServiceV2Impl.handleUpdate(java.util.Map,java.lang.String,java.lang.String)");
        return StaticUtils.ok;
    }

    @Override
    public String handleInsert(Map<String, String> metaJsonData, String dataMapJson) throws TException {
        LOGGER.info("#databus监听客户操作日志表变更# 新增数据");
        DbusUtils utils = ThriftV2Utils.newThriftV2InstanceForInsert(metaJsonData, dataMapJson);
        WmCustomerOplogWithBLOBs wmCustomerOplogWithBLOBs = transToBo(utils);
        return syncToEs(wmCustomerOplogWithBLOBs);
    }

    @Override
    public String handleDelete(Map<String, String> metaJsonData, String dataMapJson) throws TException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.dbus.CustomerOplogDbusEventServiceV2Impl.handleDelete(java.util.Map,java.lang.String)");
        return StaticUtils.ok;
    }


    private WmCustomerOplogWithBLOBs transToBo(DbusUtils utils) {
        Map<String, Object> aftMap = utils.getAftMap();
        String jsonString = JSON.toJSONString(aftMap);
        return JSON.parseObject(jsonString, WmCustomerOplogWithBLOBs.class);
    }

    private String syncToEs(WmCustomerOplogWithBLOBs wmCustomerOplogWithBLOBs) {
        try {
            WmCustomerOplogAggre.Factory.make().syncCustomerOplogToES(wmCustomerOplogWithBLOBs);
        } catch (TException e) {
            LOGGER.error("Dbus同步客户操作日志到ES异常", e);
            return StaticUtils.fail;
        }
        return StaticUtils.ok;
    }
}
