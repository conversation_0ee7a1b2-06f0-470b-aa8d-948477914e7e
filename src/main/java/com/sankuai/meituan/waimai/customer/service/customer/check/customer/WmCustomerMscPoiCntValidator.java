package com.sankuai.meituan.waimai.customer.service.customer.check.customer;

import com.alibaba.fastjson.JSON;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerDB;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerMSCLabelService;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerPlatformDataParseService;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerService;
import com.sankuai.meituan.waimai.customer.service.customer.check.BaseWmCustomerValidator;
import com.sankuai.meituan.waimai.customer.service.customer.check.IWmCustomerValidator;
import com.sankuai.meituan.waimai.customer.service.gray.WmCustomerGrayService;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerConstants;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerRealTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerSource;
import com.sankuai.meituan.waimai.thrift.customer.constant.customer.CustomerMscVersionEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.CustomerMscUsedPoiDTO;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.CustomerRealTypeSpInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.ValidateResultBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.WmCustomerBasicBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 20231010
 * @desc 美食城客户档口数校验
 */
@Service
@Slf4j
public class WmCustomerMscPoiCntValidator extends BaseWmCustomerValidator implements IWmCustomerValidator {

    @Autowired
    private WmCustomerGrayService wmCustomerGrayService;

    @Autowired
    private WmCustomerMSCLabelService wmCustomerMSCLabelService;

    @Autowired
    private WmCustomerService wmCustomerService;

    @Autowired
    private WmCustomerPlatformDataParseService wmCustomerPlatformDataParseService;



    @Override
    public ValidateResultBo valid(ValidateResultBo validateResultBo, WmCustomerBasicBo wmCustomerBasicBo,
                                  Boolean force, Boolean isAudit, Integer opUid) throws WmCustomerException {

        //判断是否命中客户责任人组织架构灰度
        Integer ownerUid = opUid;
        if(wmCustomerBasicBo.getId() > 0){
            WmCustomerDB wmCustomerDB = getExistWmCustomer(wmCustomerBasicBo.getId());
            ownerUid = wmCustomerDB.getOwnerUid();
        }
        if (wmCustomerGrayService.isGrayMscPoiCntCheckNew(ownerUid)) {
            return checkPass(validateResultBo);
        }

        Integer customerRealType = wmCustomerBasicBo.getCustomerRealType();
        //客户类型非美食城则返回通过
        if (customerRealType != CustomerRealTypeEnum.MEISHICHENG.getValue()) {
            return checkPass(validateResultBo);
        }
        Integer customerId = wmCustomerBasicBo.getId();
        CustomerRealTypeSpInfoBo customerRealTypeSpInfoBo = wmCustomerBasicBo.getCustomerRealTypeSpInfoBo();

        //未命中灰度直接返回校验通过
        if (customerRealTypeSpInfoBo.getVersion() == null
                || customerRealTypeSpInfoBo.getVersion() != CustomerMscVersionEnum.QUA_COMMON_USE.getCode()) {
            return checkPass(validateResultBo);
        }

        //如果美食城客户信息为空直接返回失败
        if (customerRealTypeSpInfoBo == null) {
            return checkFail(validateResultBo, CustomerConstants.RESULT_CODE_ERROR, "美食城类型客户扩展信息不能为空");
        }

        CustomerSource customerSource = wmCustomerBasicBo.getCustomerSource();
        //档口数量有则校验必须是正整数
        if (customerRealTypeSpInfoBo.getFoodCityPoiCount() != null && customerRealTypeSpInfoBo.getFoodCityPoiCount() <= 0) {
            return checkFail(validateResultBo, CustomerConstants.RESULT_CODE_ERROR, "档口数量必须为正整数");
        }
        //添加档口数上限要求
        if (customerRealTypeSpInfoBo.getFoodCityPoiCount() != null && customerRealTypeSpInfoBo.getFoodCityPoiCount() > 10000) {
            return checkFail(validateResultBo, CustomerConstants.RESULT_CODE_ERROR, "档口数量不能超过10000");
        }

        //新增场景-BD上单校验视频和档口数量非空
        if (customerId == 0 && customerSource == CustomerSource.WAIMAI_BD
                && (customerRealTypeSpInfoBo.getFoodCityPoiCount() == null
                || StringUtils.isBlank(customerRealTypeSpInfoBo.getFoodCityVideo()))) {
            return checkFail(validateResultBo, CustomerConstants.RESULT_CODE_ERROR, "档口数量和美食城视频均不能为空");
        }
        //修改场景
        if (customerId > 0) {
            WmCustomerDB wmCustomerDB = wmCustomerPlatformDataParseService.selectCustomerFromPlatformById(wmCustomerBasicBo.getId());
            if (wmCustomerDB == null) {
                return checkFail(validateResultBo, CustomerConstants.RESULT_CODE_ERROR, "未查到有效客户信息");
            }
            Long mtCustomerId = wmCustomerDB.getMtCustomerId();
            boolean hasQuaComCusTag = wmCustomerMSCLabelService.checkHasQuaComCustomerLabel(mtCustomerId);
            //有资质共用标签则直接返回通过
            if (hasQuaComCusTag) {
                return checkPass(validateResultBo);
            }
            //命中灰度+客户无资质共用标则添加校验 1 视频和档口数量非空，档口数只能是正整数 2 档口数量大于等于已占用档口数
            if (customerSource != null && customerSource == CustomerSource.WAIMAI_BD) {
                return checkMscVideoAndPoiOnBD(validateResultBo, customerRealTypeSpInfoBo, customerId);
            } else {
                return checkMscVideoAndPoiOnNotBD(validateResultBo, customerRealTypeSpInfoBo, wmCustomerDB);
            }
        }
        return checkPass(validateResultBo);
    }

    /**
     * BD入驻渠道校验美食城档口数和视频
     *
     * @param validateResultBo         校验器返回结果
     * @param customerRealTypeSpInfoBo 美食城信息对象
     * @param customerId               外卖客户ID
     * @return
     * @throws WmCustomerException
     */
    private ValidateResultBo checkMscVideoAndPoiOnBD(ValidateResultBo validateResultBo,
                                                     CustomerRealTypeSpInfoBo customerRealTypeSpInfoBo,
                                                     Integer customerId) throws WmCustomerException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.service.customer.check.customer.WmCustomerMscPoiCntValidator.checkMscVideoAndPoiOnBD(ValidateResultBo,CustomerRealTypeSpInfoBo,Integer)");
        if (StringUtils.isBlank(customerRealTypeSpInfoBo.getFoodCityVideo())) {
            return checkFail(validateResultBo, CustomerConstants.RESULT_CODE_ERROR, "美食城视频不能为空");
        }
        if (customerRealTypeSpInfoBo.getFoodCityPoiCount() == null || customerRealTypeSpInfoBo.getFoodCityPoiCount() <= 0) {
            return checkFail(validateResultBo, CustomerConstants.RESULT_CODE_ERROR, "档口数量仅可为正整数");
        }
        CustomerMscUsedPoiDTO customerMscUsedPoiDTO = wmCustomerService.getMscUsedPoiDTO(customerId);
        if (customerMscUsedPoiDTO != null && customerRealTypeSpInfoBo.getFoodCityPoiCount() < customerMscUsedPoiDTO.getUsedPoiCnt()) {
            String failMsg = String.format("档口数量不可小于已占用档口数量（已占用%s个）,请核实后再提交", customerMscUsedPoiDTO.getUsedPoiCnt());
            return checkFail(validateResultBo, CustomerConstants.RESULT_CODE_ERROR, failMsg);
        }
        return checkPass(validateResultBo);
    }

    /**
     * 非BD入驻渠道校验美食城档口数和视频
     *
     * @param validateResultBo         校验器返回结果
     * @param customerRealTypeSpInfoBo 美食城信息对象
     * @param wmCustomerDB               外卖客户ID
     * @return
     * @throws WmCustomerException
     */
    private ValidateResultBo checkMscVideoAndPoiOnNotBD(ValidateResultBo validateResultBo,
                                                        CustomerRealTypeSpInfoBo customerRealTypeSpInfoBo,
                                                        WmCustomerDB wmCustomerDB) throws WmCustomerException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_1", "com.sankuai.meituan.waimai.customer.service.customer.check.customer.WmCustomerMscPoiCntValidator.checkMscVideoAndPoiOnNotBD(ValidateResultBo,CustomerRealTypeSpInfoBo,WmCustomerDB)");
        Integer foodCityPoiCnt = customerRealTypeSpInfoBo.getFoodCityPoiCount();
        //客户是生效状态则要求必须有视频和档口数
        if (wmCustomerDB.isEffectived()) {
            if (StringUtils.isBlank(customerRealTypeSpInfoBo.getFoodCityVideo())) {
                return checkFail(validateResultBo, CustomerConstants.RESULT_CODE_ERROR, "美食城视频不能为空");
            }
            if (customerRealTypeSpInfoBo.getFoodCityPoiCount() == null || customerRealTypeSpInfoBo.getFoodCityPoiCount() <= 0) {
                return checkFail(validateResultBo, CustomerConstants.RESULT_CODE_ERROR, "档口数量仅可为正整数");
            }
        }
        //档口数为空或者小于等于0不校验+未生效
        if (wmCustomerDB.isUnEffectived() && (foodCityPoiCnt == null || foodCityPoiCnt <= 0)) {
            return checkPass(validateResultBo);
        }
        //美食城档口数非空则需要校验档口数不能小于已占用档口数
        CustomerMscUsedPoiDTO customerMscUsedPoiDTO = wmCustomerService.getMscUsedPoiDTO(wmCustomerDB.getId());
        if (customerMscUsedPoiDTO != null && customerRealTypeSpInfoBo.getFoodCityPoiCount() < customerMscUsedPoiDTO.getUsedPoiCnt()) {
            String failMsg = String.format("档口数量不可小于已占用档口数量（已占用%s个），请核实后再提交", customerMscUsedPoiDTO.getUsedPoiCnt());
            return checkFail(validateResultBo, CustomerConstants.RESULT_CODE_ERROR, failMsg);
        }
        return checkPass(validateResultBo);
    }
}
