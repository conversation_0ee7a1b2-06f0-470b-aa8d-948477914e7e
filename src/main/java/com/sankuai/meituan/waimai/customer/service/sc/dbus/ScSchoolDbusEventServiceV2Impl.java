package com.sankuai.meituan.waimai.customer.service.sc.dbus;

import com.alibaba.fastjson.JSON;
import com.meituan.dbus.thriftV2.DataBusEventServiceV2;
import com.sankuai.meituan.waimai.customer.constant.sc.WmScTableDbusEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * 监听校园食堂表变更的DTS逻辑
 */
@Slf4j
@Service
public class ScSchoolDbusEventServiceV2Impl implements DataBusEventServiceV2.Iface {

    @Autowired
    private TableEventHandleService tableEventHandleService;

    private final String TABLE_NAME_KEY = "tableName";

    @Override
    public String handleUpdate(Map<String, String> metaJsonData, String dataMapJson, String diffJson) throws TException {
        log.info("handleUpdate::监听校园食堂表变更metaJsonData = {}", JSON.toJSONString(metaJsonData));
        return tableEventHandleService.handleUpdate(makeTableEvent(metaJsonData, dataMapJson, diffJson));
    }


    @Override
    public String handleInsert(Map<String, String> metaJsonData, String dataMapJson) throws TException {
        log.info("handleInsert::监听校园食堂表变更metaJsonData = {}", JSON.toJSONString(metaJsonData));
        return tableEventHandleService.handleInsert(makeTableEvent(metaJsonData, dataMapJson));
    }

    @Override
    public String handleDelete(Map<String, String> metaJsonData, String dataMapJson) throws TException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.service.sc.dbus.ScSchoolDbusEventServiceV2Impl.handleDelete(java.util.Map,java.lang.String)");
        log.info("handleDelete::监听校园食堂表变更metaJsonData = {}", JSON.toJSONString(metaJsonData));
        return tableEventHandleService.handleDelete(makeTableEvent(metaJsonData, dataMapJson));
    }


    private TableEvent makeTableEvent(Map<String, String> metaJsonData, String dataMapJson, String diffJson) {
        if (MapUtils.isEmpty(metaJsonData)
                || !metaJsonData.containsKey(TABLE_NAME_KEY)
                || StringUtils.isBlank(metaJsonData.get(TABLE_NAME_KEY))) {
            return null;
        }
        TableEvent tableEvent = new TableEvent();
        WmScTableDbusEnum tableDbusEnum = WmScTableDbusEnum.getMap().get(metaJsonData.get(TABLE_NAME_KEY));
        if (tableDbusEnum == null) {
            return null;
        }
        tableEvent.setTable(tableDbusEnum);
        tableEvent.setDataMapJson(dataMapJson);
        tableEvent.setDiffJson(diffJson);
        tableEvent.setMetaJsonData(metaJsonData);
        return tableEvent;
    }


    private TableEvent makeTableEvent(Map<String, String> metaJsonData, String dataMapJson) {
        return makeTableEvent(metaJsonData, dataMapJson, "");
    }


}
