package com.sankuai.meituan.waimai.customer.service.sc;

import com.cip.crane.netty.utils.SleepUtils;
import com.sankuai.meituan.waimai.customer.config.MccScConfig;
import com.sankuai.meituan.waimai.customer.constant.sc.WmScTairLockSceneEnum;
import com.sankuai.meituan.waimai.customer.util.TairLocker;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.WmScCodeConstants;
import com.sankuai.meituan.waimai.thrift.exception.WmSchCantException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 校园食堂Tair锁相关服务
 * <AUTHOR>
 * @date 2023/07/26
 * @email <EMAIL>
 */
@Slf4j
@Service
public class WmScTairService {

    @Resource(name = "customerTairLocker")
    private TairLocker tairLocker;

    /**
     * 防重锁解锁重试次数
     */
    public static final int UNLOCK_RETRY_MAX_TIMES = 3;

    /**
     * 校园食堂并发问题任务尝试加锁
     * @param tairLockKey 加锁key值
     * @param tairLockScene 加锁场景 {@link WmScTairLockSceneEnum}
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    public void tryLockWithScene(String tairLockKey, WmScTairLockSceneEnum tairLockScene) throws WmSchCantException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_2", "com.sankuai.meituan.waimai.customer.service.sc.WmScTairService.tryLockWithScene(java.lang.String,com.sankuai.meituan.waimai.customer.constant.sc.WmScTairLockSceneEnum)");
        log.info("[WmScTairService.tryLockWithScene] input param: tairLockKey = {}, tairLockScene = {}", tairLockKey, tairLockScene);
        if (StringUtils.isBlank(tairLockKey) || tairLockScene == null) {
            log.warn("[WmScTairService.tryLockWithScene] invalid input param. tairLockKey = {}, tairLockScene = {}", tairLockKey, tairLockScene);
            return;
        }
        // 根据加锁场景获取加锁超时时间
        Integer expireSeconds = getLockExpireSecondsBySceneEnum(tairLockScene);
        // 尝试加锁
        if (!tairLocker.tryLock(tairLockKey, expireSeconds)) {
            log.error("[WmScTairService.tryLockWithScene] tryLock failed. tairLockScene = {} tairLockKey = {}, expireSeconds = {}",
                    tairLockScene.getDesc(), tairLockKey, expireSeconds);
            throw new WmSchCantException(WmScCodeConstants.SERVER_ERROR, "相同任务正在流程中, 请稍后重试");
        }
        log.info("[WmScTairService.tryLockWithScene] tryLock success. tairLockKey = {}, expireSeconds = {}", tairLockKey, expireSeconds);
    }

    /**
     * 校园食堂并发问题任务尝试解锁
     * @param tairLockKey 加锁key值
     * @param tairLockScene 加锁场景 {@link WmScTairLockSceneEnum}
     */
    public void unLockWithScene(String tairLockKey, WmScTairLockSceneEnum tairLockScene) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.service.sc.WmScTairService.unLockWithScene(java.lang.String,com.sankuai.meituan.waimai.customer.constant.sc.WmScTairLockSceneEnum)");
        log.info("[WmScTairService.unLockWithScene] input param: tairLockKey = {}, tairLockScene = {}", tairLockKey, tairLockScene);
        if (StringUtils.isBlank(tairLockKey) || tairLockScene == null) {
            log.warn("[WmScTairService.unLockWithScene] invalid input param. tairLockKey = {}, tairLockScene = {}", tairLockKey, tairLockScene);
            return;
        }

        for (int times = 0; times < UNLOCK_RETRY_MAX_TIMES; times ++) {
            if (tairLocker.unLockWithReturn(tairLockKey)) {
                log.info("[WmScTairService.unLockWithScene] unLock success. tairLockKey = {}", tairLockKey);
                break;
            }

            if (times == UNLOCK_RETRY_MAX_TIMES - 1) {
                log.error("[WmScTairService.unLockWithScene] unLock failed. tairLockKey = {}, tairLockScene = {}", tairLockKey, tairLockScene);
            }
            // 默认等待100ms
            SleepUtils.sleep(MccScConfig.getTairLockUnlockTryMillis());
        }
    }

    /**
     * 根据加锁场景获取加锁超时时间
     * @param tairLockSceneEnum 加锁场景枚举
     * @return 加锁超时时间
     */
    public Integer getLockExpireSecondsBySceneEnum(WmScTairLockSceneEnum tairLockSceneEnum) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.sc.WmScTairService.getLockExpireSecondsBySceneEnum(com.sankuai.meituan.waimai.customer.constant.sc.WmScTairLockSceneEnum)");
        switch (tairLockSceneEnum) {
            case INSERT_SCHOOL:
                return MccScConfig.getInsertSchoolLockExpireSeconds();
            case INSERT_SCHOOL_TIME:
                return MccScConfig.getInsertSchoolTimeLockExpireSeconds();
            case INSERT_SCHOOL_BUILDING:
                return MccScConfig.getInsertSchoolBuildingLockExpireSeconds();
            case CANTEEN_POI_TASK:
                return MccScConfig.getCanteenPoiTaskLockExpireSeconds();
            case SCHOOL_DELIVERY_SUBMIT_AUDIT:
                return MccScConfig.getSchoolDeliveryAuditTaskLockExpireSeconds();
            default:
                return 0;
        }
    }

}
