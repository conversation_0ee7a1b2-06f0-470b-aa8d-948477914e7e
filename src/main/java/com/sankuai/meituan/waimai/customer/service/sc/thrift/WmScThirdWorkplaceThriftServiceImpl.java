package com.sankuai.meituan.waimai.customer.service.sc.thrift;

import com.meituan.gecko.boot.util.JacksonUtils;
import com.meituan.mtrace.thread.TraceRunnable;
import com.sankuai.meituan.waimai.customer.adapter.WmEmployClient;
import com.sankuai.meituan.waimai.customer.service.sc.thirdworkplace.ThirdWorkplaceService;
import com.sankuai.meituan.waimai.customer.util.SSOUtil;
import com.sankuai.meituan.waimai.thrift.customer.domain.sc.CommonResponse;
import com.sankuai.meituan.waimai.thrift.customer.domain.sc.thirdworkplace.*;
import com.sankuai.meituan.waimai.thrift.customer.service.sc.WmScThirdWorkplaceThriftService;
import com.sankuai.meituan.waimai.thrift.exception.WmSchCantException;
import com.sankuai.meituan.waimai.util.DaxiangUtilV2;
import lombok.extern.slf4j.Slf4j;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Optional;


@Slf4j
@Service
public class WmScThirdWorkplaceThriftServiceImpl implements WmScThirdWorkplaceThriftService {

    @Autowired
    private ThirdWorkplaceService thirdWorkplaceService;

    @Autowired
    private WmEmployClient wmEmployClient;

    @Override
    public WmScThirdWorkplaceQueryListResp queryThirdWorkplaceList(WmScThirdWorkplaceQueryBo wmScThirdWorkplaceQueryBo) {
        return ThriftUtils.exec(() -> {
            log.info("WmScThirdWorkplaceThriftServiceImpl.queryThirdWorkplaceList入参:{}", JacksonUtils.toJson(wmScThirdWorkplaceQueryBo));

            WmScThirdWorkplaceQueryListDTO wmScThirdWorkplaceQueryListDTO = null;

            try {
                wmScThirdWorkplaceQueryListDTO = thirdWorkplaceService.queryThirdWorkplaceListDTO(wmScThirdWorkplaceQueryBo);
            } catch (WmSchCantException e) {
                log.error("thirdWorkplaceService.queryThirdWorkplaceListDTO error,e:{}", e.getMessage());
            }

            return wmScThirdWorkplaceQueryListDTO;
        }, new WmScThirdWorkplaceQueryListResp());
    }

    @Override
    public WmScThirdWorkplaceQueryMetricsResp queryThirdWorkplaceMetrics(WmScThirdWorkplaceQueryBo wmScThirdWorkplaceQueryBo) {
        return ThriftUtils.exec(() -> {
            log.info("WmScThirdWorkplaceThriftServiceImpl.queryThirdWorkplaceMetrics入参:{}", JacksonUtils.toJson(wmScThirdWorkplaceQueryBo));

            WmScThirdWorkplaceQueryMetricsDTO wmScThirdWorkplaceQueryMetricsDTO = null;

            try {
                wmScThirdWorkplaceQueryMetricsDTO = thirdWorkplaceService.queryThirdWorkplaceMetricsDTO(wmScThirdWorkplaceQueryBo);
            } catch (WmSchCantException e) {
                log.error("thirdWorkplaceService.queryThirdWorkplaceListDTO error,e:{}", e.getMessage());
            }

            return wmScThirdWorkplaceQueryMetricsDTO;
        }, new WmScThirdWorkplaceQueryMetricsResp());
    }

    @Override
    public CommonResponse exportThirdWorkplaceListData(WmScThirdWorkplaceQueryBo wmScThirdWorkplaceQueryBo) throws WmSchCantException, TException {
        return ThriftUtils.exec(() -> {
            log.info("WmScThirdWorkplaceThriftServiceImpl.exportThirdWorkplaceListData入参:{}", JacksonUtils.toJson(wmScThirdWorkplaceQueryBo));

            // 获取当前用户
            SSOUtil.SsoUser user = Optional.ofNullable(SSOUtil.getUser())
                    .orElseThrow(() -> new RuntimeException("获取用户信息失败"));

            new Thread(new TraceRunnable(new Runnable() {
                @Override
                public void run() {
                    String msg = "";
                    try {
                        // 1. 执行导出
                        String url = thirdWorkplaceService.exportThirdWorkplaceListData(wmScThirdWorkplaceQueryBo, user);
                        log.info("[exportThirdWorkplaceListData] url = {}", url);

                        // 2. 构建下载消息
                        msg = "三方工作台列表数据已导出完成, 请点此下载: " + "[第三方工作台列表 |" + url + " ]";
                    } catch (Exception e) {
                        log.error("[exportThirdWorkplaceListData][Exception]", e);
                        msg = "导出第三方工作台列表异常";
                    } finally {
                        // 3. 发送大象通知
                        String misById = wmEmployClient.getMisById((int) user.getId());
                        DaxiangUtilV2.push(msg, misById);
                    }
                }
            })).start();
            return "第三方工作台列表数据将异步导出, 导出完成后大象通知（附下载链接）";
        }, new CommonResponse());
    }

    @Override
    public CommonResponse exportThirdWorkplaceMetricsData(WmScThirdWorkplaceQueryBo wmScThirdWorkplaceQueryBo) throws WmSchCantException, TException {
        return ThriftUtils.exec(() -> {
            log.info("WmScThirdWorkplaceThriftServiceImpl.exportThirdWorkplaceMetricsData入参:{}", JacksonUtils.toJson(wmScThirdWorkplaceQueryBo));

            // 获取当前用户
            SSOUtil.SsoUser user = Optional.ofNullable(SSOUtil.getUser())
                    .orElseThrow(() -> new RuntimeException("获取用户信息失败"));

            new Thread(new TraceRunnable(new Runnable() {
                @Override
                public void run() {
                    String msg = "";
                    try {
                        // 1. 执行导出
                        String url = thirdWorkplaceService.exportThirdWorkplaceMetricsData(wmScThirdWorkplaceQueryBo, user);
                        log.info("[exportThirdWorkplaceMetricsData] url = {}", url);

                        // 2. 构建下载消息
                        msg = "三方工作台指标数据已导出完成, 请点此下载: " + "[第三方工作台指标 |" + url + " ]";
                    } catch (Exception e) {
                        log.error("[exportThirdWorkplaceMetricsData][Exception]", e);
                        msg = "导出第三方工作台指标异常";
                    } finally {
                        // 3. 发送大象通知
                        String misById = wmEmployClient.getMisById((int) user.getId());
                        DaxiangUtilV2.push(msg, misById);
                    }
                }
            })).start();
            return "第三方工作台指标数据将异步导出, 导出完成后大象通知（附下载链接）";
        }, new CommonResponse());
    }

    @Override
    public WmScThirdWorkplaceQueryEnumResp getThirdWorkplaceQueryEnums() throws WmSchCantException, TException {
        return ThriftUtils.exec(() -> {

            WmScThirdWorkplaceQueryEnumDTO wmScThirdWorkplaceQueryEnumDTO = null;

            try {
                wmScThirdWorkplaceQueryEnumDTO = thirdWorkplaceService.getThirdWorkplaceQueryEnum();
            } catch (WmSchCantException e) {
                log.error("thirdWorkplaceService.queryThirdWorkplaceListDTO error,e:{}", e.getMessage());
            }

            return wmScThirdWorkplaceQueryEnumDTO;
        }, new WmScThirdWorkplaceQueryEnumResp());
    }

    @Override
    public AssertUserRoleResp assertUserRole() throws WmSchCantException, TException {
        return ThriftUtils.exec(() -> {
            // 获取当前用户
            SSOUtil.SsoUser user = Optional.ofNullable(SSOUtil.getUser())
                    .orElseThrow(() -> new RuntimeException("获取用户信息失败"));

            AssertUserRoleDTO userAssertResult = null;
            try {
                userAssertResult = thirdWorkplaceService.getUserAssertResult(user);
            } catch (WmSchCantException e) {
                log.error("thirdWorkplaceService.assertUserRole error,e:{}", e.getMessage());
            }
            return userAssertResult;
        }, new AssertUserRoleResp());
    }
}
