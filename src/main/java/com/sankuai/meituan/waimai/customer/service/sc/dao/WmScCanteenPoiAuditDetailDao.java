package com.sankuai.meituan.waimai.customer.service.sc.dao;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.sankuai.meituan.auth.util.CollectionUtils;
import com.sankuai.meituan.auth.util.TimeUtil;
import com.sankuai.meituan.waimai.customer.dao.sc.WmScCanteenPoiAttributeMapper;
import com.sankuai.meituan.waimai.customer.dao.sc.WmScCanteenPoiAuditDetailMapper;
import com.sankuai.meituan.waimai.customer.domain.sc.*;
import com.sankuai.meituan.waimai.thrift.constants.WmYesNoCons;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.ValidEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * wm_sc_canteen_poi_audit_detail表相关的数据库交互层
 */
@Slf4j
@Service
public class WmScCanteenPoiAuditDetailDao {

    @Autowired
    private WmScCanteenPoiAuditDetailMapper wmScCanteenPoiAuditDetailMapper;

    @Autowired
    private WmScCanteenDao wmScCanteenDao;

    @Autowired
    private WmScCanteenPoiAttributeMapper wmScCanteenPoiAttributeMapper;


    /**
     * 强制解绑
     *
     * @param auditId 审核ID
     * @param wmPoiId 门店ID
     */
    public void forceUnbind(Integer canteenId, Long auditId, Long wmPoiId) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.service.sc.dao.WmScCanteenPoiAuditDetailDao.forceUnbind(java.lang.Integer,java.lang.Long,java.lang.Long)");
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.service.sc.dao.WmScCanteenPoiAuditDetailDao.forceUnbind(java.lang.Integer,java.lang.Long,java.lang.Long)");
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.service.sc.dao.WmScCanteenPoiAuditDetailDao.forceUnbind(java.lang.Integer,java.lang.Long,java.lang.Long)");
        WmScCanteenPoiAuditDetailDBExample example = new WmScCanteenPoiAuditDetailDBExample();
        WmScCanteenPoiAuditDetailDBExample.Criteria cri = example.createCriteria();
        cri.andCanteenPoiAuditIdEqualTo(auditId);
        if (wmPoiId != null && wmPoiId > 0) {
            cri.andWmPoiIdEqualTo(wmPoiId);
        }

        WmScCanteenPoiAuditDetailDB record = WmScCanteenPoiAuditDetailDB.builder()
                .valid(WmYesNoCons.NO.getValue())
                .utime(TimeUtil.unixtime())
                .build();
        wmScCanteenPoiAuditDetailMapper.updateByExampleSelective(record, example);

        WmCanteenDB canteen = wmScCanteenDao.getById(canteenId);
        WmScCanteenPoiAuditDetailDBExample countExample = new WmScCanteenPoiAuditDetailDBExample();
        countExample.createCriteria().andCanteenPoiAuditIdEqualTo(auditId).andCanteenIdEqualTo(canteenId).andValidEqualTo(WmYesNoCons.YES.getValue());
        int count = wmScCanteenPoiAuditDetailMapper.countByExample(countExample);
        canteen.setStoreNum(count);
        wmScCanteenDao.updateStoreNum(canteen);
    }

    /**
     * 查询食堂上绑定的门店ID（食堂关联的已经生效的门店ID）
     * @param canteenId 食堂主键ID
     * @return 门店列表
     */
    public List<Long> getEffectPoiIdListByCanteenId(Integer canteenId) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.service.sc.dao.WmScCanteenPoiAuditDetailDao.getEffectPoiIdListByCanteenId(java.lang.Integer)");
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.service.sc.dao.WmScCanteenPoiAuditDetailDao.getEffectPoiIdListByCanteenId(java.lang.Integer)");
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.service.sc.dao.WmScCanteenPoiAuditDetailDao.getEffectPoiIdListByCanteenId(java.lang.Integer)");
        log.info("[WmScCanteenPoiAuditDetailDao.getEffectPoiIdListByCanteenId] canteenId = {}", canteenId);
        List<WmScCanteenPoiAttributeDO> wmScCanteenPoiAttributeDOList = wmScCanteenPoiAttributeMapper.selectBycanteenPrimaryId(canteenId);
        List<Long> wmPoiIdList = wmScCanteenPoiAttributeDOList.stream()
                .map(WmScCanteenPoiAttributeDO::getWmPoiId)
                .collect(Collectors.toList());

        log.info("[WmScCanteenPoiAuditDetailDao.getEffectPoiIdListByCanteenId] wmPoiIdList = {}", JSONObject.toJSONString(wmPoiIdList));
        return wmPoiIdList;
    }

    /**
     * 根据门店ID查询门店食堂门店绑定信息
     *
     * @param wmPoiId
     * @return
     */
    public List<WmScCanteenPoiAuditDetailDB> getByWmPoiId(Long wmPoiId) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.sc.dao.WmScCanteenPoiAuditDetailDao.getByWmPoiId(java.lang.Long)");
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.sc.dao.WmScCanteenPoiAuditDetailDao.getByWmPoiId(java.lang.Long)");
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.sc.dao.WmScCanteenPoiAuditDetailDao.getByWmPoiId(java.lang.Long)");
        if (wmPoiId == null || wmPoiId <= 0) {
            return Lists.newArrayList();
        }
        return wmScCanteenPoiAuditDetailMapper.selectPoiByWmPoiId(wmPoiId);
    }

    /**
     * 查询该审核任务下指定门店的有效（ valid=1）信息
     * @param wmPoiId 门店ID
     * @param canteenPoiAuditId 食堂门店任务ID
     * @return WmScCanteenPoiAuditDetailDB
     */
    public WmScCanteenPoiAuditDetailDB getValidBindPoi(Long canteenPoiAuditId, Long wmPoiId) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.service.sc.dao.WmScCanteenPoiAuditDetailDao.getValidBindPoi(java.lang.Long,java.lang.Long)");
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.service.sc.dao.WmScCanteenPoiAuditDetailDao.getValidBindPoi(java.lang.Long,java.lang.Long)");
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.service.sc.dao.WmScCanteenPoiAuditDetailDao.getValidBindPoi(java.lang.Long,java.lang.Long)");
        log.info("[WmScCanteenPoiAuditDetailDao.getValidBindPoi] input param: canteenPoiAuditId = {}, wmPoiId = {}", canteenPoiAuditId, wmPoiId);
        if (wmPoiId == null || wmPoiId <= 0) {
            return null;
        }
        WmScCanteenPoiAuditDBCondition condition = new WmScCanteenPoiAuditDBCondition();
        condition.setValid(ValidEnum.VALID.getTypeInt());
        condition.setCanteenPoiAuditId(canteenPoiAuditId);
        condition.setWmPoiIdList(Lists.newArrayList(wmPoiId));
        List<WmScCanteenPoiAuditDetailDB> wmScCanteenPoiAuditDetailDBList = wmScCanteenPoiAuditDetailMapper.selectPoiByCondition(condition);
        if (CollectionUtils.isEmpty(wmScCanteenPoiAuditDetailDBList)) {
            return null;
        }
        if (wmScCanteenPoiAuditDetailDBList.size() > 1) {
            log.error("[WmScCanteenPoiAuditDetailDao.getValidBindPoi] wmScCanteenPoiAuditDetailDBList mutiple. canteenPoiAuditId = {}, wmPoiId = {}",
                    canteenPoiAuditId, wmPoiId);
        }
        return wmScCanteenPoiAuditDetailDBList.get(0);
    }

    /**
     * 查询该审核任务下绑定的门店数量
     *
     * @param canteenPoiAuditId
     * @return
     */
    public int getValidBindPoiCount(Long canteenPoiAuditId) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_2", "com.sankuai.meituan.waimai.customer.service.sc.dao.WmScCanteenPoiAuditDetailDao.getValidBindPoiCount(java.lang.Long)");
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_2", "com.sankuai.meituan.waimai.customer.service.sc.dao.WmScCanteenPoiAuditDetailDao.getValidBindPoiCount(java.lang.Long)");
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_2", "com.sankuai.meituan.waimai.customer.service.sc.dao.WmScCanteenPoiAuditDetailDao.getValidBindPoiCount(java.lang.Long)");
        if (canteenPoiAuditId == null || canteenPoiAuditId <= 0) {
            return 0;
        }
        WmScCanteenPoiAuditDBCondition condition = new WmScCanteenPoiAuditDBCondition();
        condition.setValid(ValidEnum.VALID.getTypeInt());
        condition.setCanteenPoiAuditId(canteenPoiAuditId);
        List<WmScCanteenPoiAuditDetailDB> wmScCanteenPoiAuditDetailDBList = wmScCanteenPoiAuditDetailMapper.selectPoiByCondition(condition);
        if (wmScCanteenPoiAuditDetailDBList == null) {
            return 0;
        }
        return wmScCanteenPoiAuditDetailDBList.size();
    }

}
