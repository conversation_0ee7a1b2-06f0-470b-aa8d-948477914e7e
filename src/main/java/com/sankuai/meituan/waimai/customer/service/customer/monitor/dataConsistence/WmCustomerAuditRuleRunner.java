package com.sankuai.meituan.waimai.customer.service.customer.monitor.dataConsistence;

import com.dianping.frog.sdk.alarm.DXUtil;
import com.dianping.frog.sdk.data.MafkaRawData;
import com.dianping.frog.sdk.data.RawData;
import com.dianping.frog.sdk.rpc.RpcService;
import com.dianping.frog.sdk.rpc.RpcServiceFactory;
import com.dianping.frog.sdk.runner.DefaultRuleRunner;
import com.dianping.frog.sdk.util.BcpLoggerUtils;
import com.google.common.collect.Lists;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.reflect.TypeToken;
import org.apache.commons.lang3.StringUtils;

import java.util.Map;

public class WmCustomerAuditRuleRunner extends DefaultRuleRunner {

    private Gson gson = new GsonBuilder().create();

    @Override
    public boolean filter(RawData rawData, RawData... targetData) throws Exception {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.service.customer.monitor.dataConsistence.WmCustomerAuditRuleRunner.filter(com.dianping.frog.sdk.data.RawData,com.dianping.frog.sdk.data.RawData[])");
        MafkaRawData triggerMsgData = (MafkaRawData) rawData;
        Object body = triggerMsgData.getMafkaMessage().getBody();

        if (null == body || StringUtils.isBlank(body.toString())) {
            return false;
        }
        Map<String, Object> triggerMsg = toMap(body.toString());
        if (triggerMsg.isEmpty()) {
            return false;
        }
        Integer bizType = (Integer) triggerMsg.get("bizType");
        // 客户审核事件
        if (bizType == null || (bizType.intValue() != 40 && bizType.intValue() != 41)) {
            return false;
        }
        // 参数校验
        Integer customerId = (Integer) triggerMsg.get("customerId");
        if (customerId == null || customerId <= 0) {
            return false;
        }
        Integer bizId = (Integer) triggerMsg.get("bizId");
        if (bizId == null || bizId <= 0) {
            return false;
        }
        return true;
    }

    @Override
    public String check(RawData rawData, RawData... rawData2) throws Exception {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.service.customer.monitor.dataConsistence.WmCustomerAuditRuleRunner.check(com.dianping.frog.sdk.data.RawData,com.dianping.frog.sdk.data.RawData[])");
        MafkaRawData triggerMsgData = (MafkaRawData) rawData;
        Object body = triggerMsgData.getMafkaMessage().getBody();

        try {
            if (body == null) {
                return null;
            }
            Map<String, Object> triggerMsg = toMap(body.toString());
            if (triggerMsg.isEmpty()) {
                return null;
            }
            Integer bizType = (Integer) triggerMsg.get("bizType");
            // 客户审核事件
            if (bizType == null || (bizType.intValue() != 40 && bizType.intValue() != 41)) {
                return null;
            }
            // 参数校验
            Integer customerId = (Integer) triggerMsg.get("customerId");
            if (customerId == null || customerId <= 0) {
                return null;
            }
            Integer bizId = (Integer) triggerMsg.get("bizId");
            if (bizId == null || bizId <= 0) {
                return null;
            }
            RpcService rpcService = RpcServiceFactory.getThriftInstance("com.sankuai.meituan.waimai.thrift.customer.service.WmCustomerThriftService",
                    "com.sankuai.waimai.e.customer", 10000, null, "8433");
            String result = rpcService.invoke("monitorCustomerAudit",
                    Lists.newArrayList("java.lang.Integer", "java.lang.Integer", "java.lang.Integer"),
                    Lists.newArrayList(bizType.toString(), customerId.toString(), bizId.toString()));

            if (!StringUtils.isBlank(result) && !"\"\"".equals(result)) {
                return result;
            }
        } catch (Exception e) {
            BcpLoggerUtils.info("类型转换失败:msg={}" + body.toString() + ";异常日志:" + e.getStackTrace());    // 打印日志
        }
        return null;
    }

    private Map<String, Object> toMap(String json) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_1", "com.sankuai.meituan.waimai.customer.service.customer.monitor.dataConsistence.WmCustomerAuditRuleRunner.toMap(java.lang.String)");
        TypeToken<?> parameterized = TypeToken.getParameterized(Map.class, String.class, Object.class);
        return gson.fromJson(json, parameterized.getType());
    }

    @Override
    public void alarm(String s, RawData rawData, RawData... rawData2) throws Exception {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_1", "com.sankuai.meituan.waimai.customer.service.customer.monitor.dataConsistence.WmCustomerAuditRuleRunner.alarm(java.lang.String,com.dianping.frog.sdk.data.RawData,com.dianping.frog.sdk.data.RawData[])");
        //通过大象公众号发送告警消息, 收件人为告警配置中的告警接收人。
        DXUtil.sendAlarm(s);
    }
}
