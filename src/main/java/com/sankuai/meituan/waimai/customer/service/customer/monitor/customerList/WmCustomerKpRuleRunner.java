package com.sankuai.meituan.waimai.customer.service.customer.monitor.customerList;

import com.dianping.frog.sdk.alarm.DXUtil;
import com.dianping.frog.sdk.data.BinlogRawData;
import com.dianping.frog.sdk.data.DmlType;
import com.dianping.frog.sdk.data.RawData;
import com.dianping.frog.sdk.rpc.RpcService;
import com.dianping.frog.sdk.rpc.RpcServiceFactory;
import com.dianping.frog.sdk.runner.DefaultRuleRunner;
import com.dianping.frog.sdk.util.JsonUtils;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.lang3.StringUtils;

import java.util.Map;

public class WmCustomerKpRuleRunner extends DefaultRuleRunner {
    @Override
    public boolean filter(RawData rawData, RawData... targetData) throws Exception {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.service.customer.monitor.customerList.WmCustomerKpRuleRunner.filter(com.dianping.frog.sdk.data.RawData,com.dianping.frog.sdk.data.RawData[])");
        BinlogRawData binlogRawData = (BinlogRawData) rawData;
        if (binlogRawData.getDmlType() == DmlType.DELETE) {
            return false;
        }
        String effectiveStr = binlogRawData.getColumnInfoMap().get("effective").getNewValue().toString();
        if (StringUtils.isBlank(effectiveStr)) {
            return false;
        }
        if (!effectiveStr.equals("1")) {
            return false;
        }
        String kpTypeStr = binlogRawData.getColumnInfoMap().get("kp_type").getNewValue().toString();
        if (StringUtils.isBlank(kpTypeStr)) {
            return false;
        }
        if (!kpTypeStr.equals("1")) {
            return false;
        }
        return true;
    }

    @Override
    public String check(RawData rawData, RawData... rawData2) throws Exception {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.customer.monitor.customerList.WmCustomerKpRuleRunner.check(com.dianping.frog.sdk.data.RawData,com.dianping.frog.sdk.data.RawData[])");
        BinlogRawData binlogRawData = (BinlogRawData) rawData;
        Map<String, Object> params = Maps.newHashMap();
        String customerId = binlogRawData.getColumnInfoMap().get("customer_id").getNewValue().toString();
        String signerId = binlogRawData.getColumnInfoMap().get("id").getNewValue().toString();
        String signerType = binlogRawData.getColumnInfoMap().get("signer_type").getNewValue().toString();
        String signerCertType = binlogRawData.getColumnInfoMap().get("cert_type").getNewValue().toString();
        String signerCompellation = binlogRawData.getColumnInfoMap().get("compellation").getNewValue().toString();
        String signerCertNumberToken = binlogRawData.getColumnInfoMap().get("cert_number_token").getNewValue().toString();
        String signerCertNumberEncryption = binlogRawData.getColumnInfoMap().get("cert_number_encryption").getNewValue().toString();
        String signerPhoneNumToken = binlogRawData.getColumnInfoMap().get("phone_num_token").getNewValue().toString();
        String signerPhoneNumEncryption = binlogRawData.getColumnInfoMap().get("phone_num_encryption").getNewValue().toString();
        String valid = binlogRawData.getColumnInfoMap().get("valid").getNewValue().toString();
        params.put("customerId", Integer.valueOf(customerId));
        params.put("signerId", Integer.valueOf(signerId));
        params.put("signerType", Integer.valueOf(signerType));
        params.put("signerCertType", Integer.valueOf(signerCertType));
        params.put("signerCompellation", signerCompellation);
        params.put("signerCertNumberToken", signerCertNumberToken);
        params.put("signerCertNumberEncryption", signerCertNumberEncryption);
        params.put("signerPhoneNumToken", signerPhoneNumToken);
        params.put("signerPhoneNumEncryption", signerPhoneNumEncryption);
        params.put("valid", Integer.valueOf(valid));
        RpcService rpcService = RpcServiceFactory.getThriftInstance("com.sankuai.meituan.waimai.thrift.customer.service.WmCustomerKpThriftService",
                "com.sankuai.waimai.e.customer", 10000, null, "8430");
        String result = rpcService.invoke("monitorKpES",
                Lists.newArrayList("com.sankuai.meituan.waimai.thrift.customer.domain.customer.monitor.MonitorCustomerKpESDTO"),
                Lists.newArrayList(JsonUtils.toJson(params)));
        if (!StringUtils.isBlank(result) && !"\"\"".equals(result)) {
            return result;
        }
        return null;

    }


    @Override
    public void alarm(String s, RawData rawData, RawData... rawData2) throws Exception {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.service.customer.monitor.customerList.WmCustomerKpRuleRunner.alarm(java.lang.String,com.dianping.frog.sdk.data.RawData,com.dianping.frog.sdk.data.RawData[])");
        //通过大象公众号发送告警消息, 收件人为告警配置中的告警接收人。
        DXUtil.sendAlarm(s);
    }
}
