package com.sankuai.meituan.waimai.customer.service.sc.thirdworkplace;

import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class WmScThirdWorkplaceQueryListItem {

    /**
     * 学校ID
     */
    private Long schoolId;

    /**
     * 学校名称
     */
    private String schoolName;

    /**
     * 学校类型
     */
    private Integer scSchoolType;

    /**
     * 一级物理城市
     */
    private Integer firstPhysicalCity;

    /**
     * 二级物理城市
     */
    private Integer secondPhysicalCity;

    /**
     * 组织
     */
    private Integer org;

    /**
     * AOR
     */
    private Integer aor;

    /**
     * AOR类型-学校业务类型
     */
    private Integer aorType;

    /**
     * 城市负责人
     */
    private Integer responsiblePerson;

    /**
     * 食堂ID
     */
    private Integer cafeteriaId;

    /**
     * 学校合作状态
     */
    private Integer schoolCoStatus;

    /**
     * 学校等级
     */
    private Integer schoolLevelNum;

    /**
     * 渠道负责人id
     */
    private Integer channelOwnerMis;

    /**
     * 食堂类别
     */
    private Integer canteenCategory;

    /**
     * 学校三方组织类型
     */
    private Integer schoolTrilateralOrgType;

    /**
     * 学校负责人-校企经理
     */
    private String schoolResponsiblePerson;

    /**
     * 学校有效性
     */
    private Integer schoolValid;

    /**
     * 学校类别
     */
    private Integer schoolCategory;

    /**
     * 学校签约类型
     */
    private Integer schoolSigningType;

    /**
     * 学校签约方式
     */
    private Integer schoolSigningMethod;

    /**
     * 学校是否独家合作
     */
    private Integer schoolIsExclusiveCooperation;

    /**
     * 是否独家平台合作
     */
    private Integer isExclusivePlatformCooperation;

    /**
     * 合同交付状态
     */
    private Integer contractDeliverStatus;

    /**
     * 合同流失状态
     */
    private Integer contractLossStatus;

    /**
     * 学校交付方式
     */
    private Integer schoolDeliveryMethod;

    /**
     * 是否存在招标
     */
    private Integer isTenderExisted;

    /**
     * 档口数量
     */
    private String stallCountNum;

    /**
     * 合同流失审批日期
     */
    private String contractLossApprovalDate;

    /**
     * 在线档口数
     */
    private String onlinePoiNum;

    /**
     * 在线档口数环比
     */
    private String onlinePoiNumMom;

    /**
     * 交易档口数
     */
    private String txnPoiNum;

    /**
     * 交易档口数环比
     */
    private String txnPoiNumMom;

    /**
     * 档口动销率
     */
    private String txnrate;

    /**
     * 档口动销率环比
     */
    private String txnrateMom;

    /**
     * 消费单均价-客单价
     */
    private String ordAvgFinActualAmt;

    /**
     * 消费单均价环比
     */
    private String ordAvgFinActualAmtMom;

    /**
     * 交易用户数
     */
    private String finUsrNum;

    /**
     * 交易用户数环比
     */
    private String finUsrNumMom;

    /**
     * 消费GTV
     */
    private String finActualAmt;

    /**
     * 消费GTV环比
     */
    private String finActualAmtMom;

    /**
     * 消费订单量
     */
    private String finOrdNum;

    /**
     * 消费订单量环比
     */
    private String finOrdNumMom;

    /**
     * 营业档口数
     */
    private String openPoiNum;

    /**
     * 营业档口数环比
     */
    private String openPoiNumMom;

    /**
     * 人均交易频次
     */
    private String usrAvgFinOrdNum;

    /**
     * 人均交易频次环比
     */
    private String usrAvgFinOrdNumMom;

    /**
     * 新增档口数
     */
    private String newPoiNum;

    /**
     * 新增档口数环比
     */
    private String newPoiNumMom;

    /**
     * 档口平均结算订单数-单产
     */
    private String poiAvgSettleOrdNum;

    /**
     * 档口平均结算订单数环比
     */
    private String poiAvgSettleOrdNumMom;

    /**
     * 档口数量
     */
    private Integer stallNum;

    /**
     * 档口数量环比
     */
    private String stallNumMom;

    /**
     * 食堂新增用户数
     */
    private String cafeteriaNewUserNum;

    /**
     * 食堂新增用户数环比
     */
    private String cafeteriaNewUserNumMom;

    /**
     * 营业档口动销率GMV
     */
    private String openPoiTxnrateGmv;

    /**
     * 档口渗透率  // 停用
     */
    private String stallPermeateRatio;

    /**
     * 食堂档口开业率 // 停用
     */
    private String canteenPoiOpenRate;

    /**
     * 食堂档口交易率 // 停用
     */
    private String canteenPoiTxnRate;

    /**
     * 实际ARPU - 客单价
     */
    private String actualArpu;

    /**
     * 实际ARPU - 客单价环比
     */
    private String actualArpuMom;

    /**
     * 学校数量 // 停用
     */
    private Integer schoolNum;

    /**
     * 校园食堂数量
     */
    private Integer scCanteenNum;

    /**
     * 线索合伙人数量
     */
    private Integer cluePartnerNum;

    /**
     * 线索合伙人数量环比
     */
    private String cluePartnerNumMom;

    /**
     * 意向合伙人数量
     */
    private Integer intentionPartnerNum;

    /**
     * 意向合伙人数量环比
     */
    private String intentionPartnerNumMom;

    /**
     * 委托合伙人数量
     */
    private Integer entrustPartnerNum;

    /**
     * 委托合伙人数量环比
     */
    private String entrustPartnerNumMom;

    /**
     * 签约合伙人数量
     */
    private Integer signPartnerNum;

    /**
     * 签约合伙人数量环比
     */
    private String signPartnerNumMom;

    /**
     * 食堂线下营业档口数
     */
    private Integer cafeteriaOfflineOpenStallNum;

    /**
     * 食堂线下营业档口数环比
     */
    private String cafeteriaOfflineOpenStallNumMom;

    /**
     * 在校师生数
     */
    private Integer teaStuNum;

    /**
     * 在校师生数环比
     */
    private String teaStuNumMom;

    /**
     * 学校楼宇数量
     */
    private Integer schoolBuildingNum;

    /**
     * 学校其他合作平台数量
     */
    private Integer schoolOtherCoopPltfmCnt;

    /**
     * 学校其他合作平台信息
     */
    private String schoolOtherCoopPltfmInfo;

    /**
     * 其他平台学校内档口订单数
     */
    private Integer otherPlatformSchoolInPoiOrderCount;

    /**
     * 其他平台学校外档口订单数
     */
    private Integer otherPlatformSchoolOutPoiOrderCount;

    /**
     * 学校承包商数量
     */
    private Integer schoolContractorCnt;

    /**
     * 委托学校合伙人信息
     */
    private String mandateSchoolPartnerInfos;

    /**
     * 签约学校合伙人信息
     */
    private String signingSchoolPartnerInfos;

    /**
     * 意向学校合伙人信息
     */
    private String intentionSchoolPartnerInfos;

    /**
     * 线索学校合伙人信息
     */
    private String leadsSchoolPartnerInfos;

    /**
     * 承包食堂线下营业档口数
     */
    private Integer contractedCafeteriaOfflineOpenStallNum;

    /**
     * 直营档口数
     */
    private Integer directlyOperatedCafeteriaOfflineOpenStallNum;

    /**
     * 单一校园食堂数量
     */
    private Integer singleScCanteenNum;

    /**
     * 承包校园食堂数量
     */
    private Integer contractedScCanteenNum;

    /**
     * 直营校园食堂数量
     */
    private Integer directlyOperatedScCanteenNum;

    /**
     * 单一食堂线下营业档口数
     */
    private Integer singleCafeteriaOfflineOpenStallNum;

    /**
     * 承包食堂线下营业档口率
     */
    private String contractedCafeteriaOfflineOpenStallRate;

    /**
     * 单一食堂线下营业档口率
     */
    private String singleCafeteriaOfflineOpenStallRate;

    /**
     * 直营档口占比
     */
    private String directlyOperatedCafeteriaOfflineOpenStallRate;

    /**
     * 食堂可上线营业档口数
     */
    private Integer canteenPreOnlineStallNum;

    /**
     * 食堂可上线营业档口数环比
     */
    private String canteenPreOnlineStallNumMom;

    /**
     * 食堂在线档口渗透率
     */
    private String cafeteriaOnlineStallInfiltrationRate;

    /**
     * 食堂在线档口渗透率环比
     */
    private String cafeteriaOnlineStallInfiltrationRateMom;

    /**
     * 食堂营业档口渗透率
     */
    private String cafeteriaOpenStallInfiltrationRate;

    /**
     * 食堂营业档口渗透率环比
     */
    private String cafeteriaOpenStallInfiltrationRateMom;

    /**
     * 食堂交易档口渗透率
     */
    private String cafeteriaTxnStallInfiltrationRate;

    /**
     * 食堂交易档口渗透率环比
     */
    private String cafeteriaTxnStallInfiltrationRateMom;

    /**
     * 合同/授权开始时间
     */
    private String contractBeginTime;

    /**
     * 合同/授权结束时间
     */
    private String contractEndTime;

    /**
     * 交付id
     */
    private Integer contractDeliver;

    /**
     * 流失id
     */
    private Integer contractLoss;
}