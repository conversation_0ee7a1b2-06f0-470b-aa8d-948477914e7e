package com.sankuai.meituan.waimai.customer.service.kp.statemachine.machine;

import com.alibaba.fastjson.JSON;
import com.sankuai.meituan.waimai.customer.service.kp.statemachine.constant.event.KpSignerEventEnum;
import com.sankuai.meituan.waimai.customer.service.kp.statemachine.context.KpSignerStatusMachineContext;
import com.sankuai.meituan.waimai.thrift.customer.constant.KpSignerStateMachine;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.squirrelframework.foundation.fsm.impl.AbstractStateMachine;

/**
 * <AUTHOR>
 * @date 20230821
 * @desc 签约人KP变更状态机
 */
@Slf4j
@Service
public class KpSignerBaseSM extends AbstractStateMachine<KpSignerBaseSM, KpSignerStateMachine, KpSignerEventEnum, KpSignerStatusMachineContext> {
    /**
     * 异常处理
     *
     * @param fromState
     * @param toState
     * @param event
     * @param context
     */
    @Override
    protected void afterTransitionCausedException(KpSignerStateMachine fromState, KpSignerStateMachine toState, KpSignerEventEnum event, KpSignerStatusMachineContext context) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "c.s.meituan.waimai.customer.service.kp.statemachine.machine.KpSignerBaseSM.afterTransitionCausedException(KpSignerStateMachine,KpSignerStateMachine,KpSignerEventEnum,KpSignerStatusMachineContext)");
        log.info("afterTransitionCausedException,fromState={},toState={},event={},context={}", JSON.toJSONString(fromState), JSON.toJSONString(toState), JSON.toJSONString(event), JSON.toJSONString(context));
        super.afterTransitionCausedException(fromState, toState, event, context);
    }

    /**
     * 每次流转完成时
     *
     * @param fromState
     * @param toState
     * @param event
     * @param context
     */
    @Override
    protected void afterTransitionCompleted(KpSignerStateMachine fromState, KpSignerStateMachine toState, KpSignerEventEnum event, KpSignerStatusMachineContext context) {
        log.info("afterTransitionCompleted,fromState={},toState={},event={},context={}", JSON.toJSONString(fromState), JSON.toJSONString(toState), JSON.toJSONString(event), JSON.toJSONString(context));
        // 更新KP状态
        super.afterTransitionCompleted(fromState, toState, event, context);
    }

    /**
     * 流程实例初始节点开始前
     *
     * @param fromState
     * @param event
     * @param context
     */
    @Override
    protected void beforeTransitionBegin(KpSignerStateMachine fromState, KpSignerEventEnum event, KpSignerStatusMachineContext context) {
        log.info("beforeTransitionBegin,fromState={},event={},context={}", JSON.toJSONString(fromState), JSON.toJSONString(event), JSON.toJSONString(context));
    }

    @Override
    protected void afterTransitionEnd(KpSignerStateMachine fromState, KpSignerStateMachine toState, KpSignerEventEnum event, KpSignerStatusMachineContext context) {
        log.info("afterTransitionEnd,fromState={},toState={},event={},context={}", JSON.toJSONString(fromState), JSON.toJSONString(toState), JSON.toJSONString(event), JSON.toJSONString(context));
    }

    /**
     * 每一个action执行前
     *
     * @param fromState
     * @param toState
     * @param event
     * @param context
     */
    @Override
    protected void beforeActionInvoked(KpSignerStateMachine fromState, KpSignerStateMachine toState, KpSignerEventEnum event, KpSignerStatusMachineContext context) {
        log.info("beforeActionInvoked,fromState={},toState={},event={},context={}", JSON.toJSONString(fromState), JSON.toJSONString(toState), JSON.toJSONString(event), JSON.toJSONString(context));
    }

    /**
     * 每一个action执行后
     *
     * @param fromState
     * @param toState
     * @param event
     * @param context
     */
    @Override
    protected void afterActionInvoked(KpSignerStateMachine fromState, KpSignerStateMachine toState, KpSignerEventEnum event, KpSignerStatusMachineContext context) {
        log.info("afterActionInvoked,fromState={},toState={},event={},context={}", JSON.toJSONString(fromState), JSON.toJSONString(toState), JSON.toJSONString(event), JSON.toJSONString(context));
    }

    @Override
    public KpSignerStateMachine getCurrentState() {
        return super.getCurrentState();
    }

    @Override
    public void start() {
        super.start();
    }
}
