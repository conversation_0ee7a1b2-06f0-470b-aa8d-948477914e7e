package com.sankuai.meituan.waimai.customer.service.common;

import com.google.common.base.MoreObjects;
import com.google.common.collect.Lists;
import com.sankuai.meituan.waimai.customer.adapter.WmCommercialAgentAdapter;
import com.sankuai.meituan.waimai.customer.contract.service.impl.WmContractAgentService;
import com.sankuai.meituan.waimai.customer.domain.WmContractAgentInfo;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerDB;
import com.sankuai.meituan.waimai.customer.util.GrayUtil;
import com.sankuai.meituan.waimai.infra.domain.WmEmploy;
import com.sankuai.meituan.waimai.infra.service.WmEmployService;
import com.sankuai.meituan.waimai.thrift.customer.constant.ContractSignSubjectEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.WmTempletContractTypeEnum;
import com.sankuai.meituan.waimai.thrift.agent.domain.WmCommercialAgentInfo;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.WmTempletContractBasicBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.WmTempletContractSignBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.WmCustomerCommonBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.kp.WmCustomerKp;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import com.sankuai.meituan.waimai.thrift.exception.WmServerException;
import com.sankuai.meituan.waimai.thrift.util.DateUtil;
import org.apache.thrift.TException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

@Service
public class WmCustomerCommonWrapService {

    private static Logger logger = LoggerFactory.getLogger(WmCustomerCommonWrapService.class);

    @Autowired
    protected WmEmployService.Iface wmEmployService;

    private static final String PARTY_B_BJSK = "北京三快在线科技有限公司";

    @Autowired
    private EmpServiceAdaptor empServiceAdaptor;

    @Resource
    private WmContractAgentService wmContractAgentService;

    public WmCustomerCommonBo wrapWmCustomerCommonBo(int code, String msg, int customerId) {
        WmCustomerCommonBo wmCustomerCommonBo = new WmCustomerCommonBo();
        wmCustomerCommonBo.setCode(code);
        wmCustomerCommonBo.setMsg(msg);
        wmCustomerCommonBo.setCustomerId(customerId);
        return wmCustomerCommonBo;
    }

    public WmTempletContractBasicBo wrapWmTempletC1ContractBasicBo(int customerId, long dueDate) {
        return wrapWmTempletContractBasicBo(customerId, dueDate, WmTempletContractTypeEnum.C1_E);
    }

    public WmTempletContractBasicBo wrapWmTempletC2ContractBasicBo(int customerId, long dueDate) {
        return wrapWmTempletContractBasicBo(customerId, dueDate, WmTempletContractTypeEnum.C2_E);
    }

    public WmTempletContractBasicBo wrapWmTempletFoodcityStatementBasicBo(int customerId, long dueDate) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.common.WmCustomerCommonWrapService.wrapWmTempletFoodcityStatementBasicBo(int,long)");
        return wrapWmTempletContractBasicBo(customerId, dueDate, WmTempletContractTypeEnum.FOODCITY_STATEMENT_E);
    }

    public WmTempletContractBasicBo wrapWmTempletSubjectChangeSupplementBasicBo(int customerId, long dueDate) {
        return wrapWmTempletContractBasicBo(customerId, dueDate, WmTempletContractTypeEnum.SUBJECT_CHANGE_SUPPLEMENT_E);
    }

    private WmTempletContractBasicBo wrapWmTempletContractBasicBo(int customerId, long dueDate, WmTempletContractTypeEnum type) {
        WmTempletContractBasicBo wmTempletContractBasicBo = new WmTempletContractBasicBo();
        wmTempletContractBasicBo.setParentId(customerId);

        if (WmTempletContractTypeEnum.C2_E == type && dueDate == 0) {
            wmTempletContractBasicBo.setDueDate(dueDate);
        } else {
            Date date = DateUtil.string2DateSecond24(DateUtil.Date2String(DateUtil.fromUnixTime((int) dueDate), DateUtil.DefaultShortFormat) + " 23:59:59");
            wmTempletContractBasicBo.setDueDate(DateUtil.date2Unixtime(date));
        }
        wmTempletContractBasicBo.setType(type.getCode());
        wmTempletContractBasicBo.setExtStr("");
        return wmTempletContractBasicBo;
    }

    public List<WmTempletContractSignBo> wrapWmTempletC1ContractSignBoList(WmCustomerDB wmCustomerDB, WmCustomerKp wmCustomerKp, int opUid, String opName) throws TException {
        WmTempletContractSignBo signPartyA = new WmTempletContractSignBo();
        initPartyA(wmCustomerDB, wmCustomerKp, signPartyA);
        WmTempletContractSignBo signPartyB = new WmTempletContractSignBo();
        initC1PartB(opUid, opName, signPartyB);
        return Lists.newArrayList(signPartyA, signPartyB);
    }

    public List<WmTempletContractSignBo> wrapWmTempletC2ContractSignBoList(WmCustomerDB wmCustomerDB, WmCustomerKp wmCustomerKp, int agentId, int opUid, String opName) throws TException, WmCustomerException {
        WmTempletContractSignBo signPartyA = new WmTempletContractSignBo();
        initPartyA(wmCustomerDB, wmCustomerKp, signPartyA);
        WmTempletContractSignBo signPartyB = new WmTempletContractSignBo();
        initC2PartyB(opUid, opName, agentId, signPartyB);
        return Lists.newArrayList(signPartyA, signPartyB);
    }

    public List<WmTempletContractSignBo> wrapWmTempletFoodcityStatementSignBoList(WmCustomerDB wmCustomerDB, WmCustomerKp wmCustomerKp, int agentId, int opUid, String opName) throws TException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.service.common.WmCustomerCommonWrapService.wrapWmTempletFoodcityStatementSignBoList(WmCustomerDB,WmCustomerKp,int,int,String)");
        WmTempletContractSignBo signPartyA = new WmTempletContractSignBo();
        initPartyA(wmCustomerDB, wmCustomerKp, signPartyA);
        WmTempletContractSignBo signPartyB = new WmTempletContractSignBo();
        initC1PartB(opUid, opName, signPartyB);
        return Lists.newArrayList(signPartyA, signPartyB);
    }

    public List<WmTempletContractSignBo> wrapWmTempletSubjectChangeSupplementSignBoList(WmCustomerDB wmCustomerDB, WmCustomerKp wmCustomerKp, int opUid, String opName) throws TException {
        WmTempletContractSignBo signPartyA = new WmTempletContractSignBo();
        initPartyA(wmCustomerDB, wmCustomerKp, signPartyA);
        WmTempletContractSignBo signPartyB = new WmTempletContractSignBo();
        initSubjectChangeSupplementPartyB(opUid, opName, signPartyB);
        WmTempletContractSignBo signPartyC = new WmTempletContractSignBo();
        initSubjectChangeSupplementPartyC(opUid, opName, signPartyC);
        return Lists.newArrayList(signPartyA, signPartyB, signPartyC);
    }

    private void initSignTime(WmTempletContractSignBo signParty){
        String today = DateUtil.secondsToString(DateUtil.unixTime());
        signParty.setSignTime(today);
    }

    /**
     * 初始化C1、C2合同甲方签约人信息，C1、C2甲方签约人信息相同
     * @param wmCustomerDB
     * @param wmCustomerKp
     * @param signPartyA
     */
    private void initPartyA(WmCustomerDB wmCustomerDB, WmCustomerKp wmCustomerKp, WmTempletContractSignBo signPartyA) {
        signPartyA.setSignType("A");
        signPartyA.setSignId(wmCustomerDB.getId());
        signPartyA.setSignName(wmCustomerDB.getCustomerName());
        signPartyA.setSignPeople(wmCustomerKp.getCompellation());
        signPartyA.setSignPhone(wmCustomerKp.getPhoneNum());
        signPartyA.setTempletContractId(0);
        initSignTime(signPartyA);
    }


    private void initC1PartB(int opUid, String opName, WmTempletContractSignBo signPartyB) throws TException {
        signPartyB.setSignType("B");
        signPartyB.setSignId(0);
        signPartyB.setSignName(PARTY_B_BJSK);
        signPartyB.setSignPeople(opName);
        signPartyB.setTempletContractId(0);
        setSignBPhone(opUid, signPartyB);
        initSignTime(signPartyB);
    }

    private void initC2PartyB(int opUid, String opName,int agentId, WmTempletContractSignBo signPartyB) throws WmCustomerException {
        WmContractAgentInfo agentInfo = wmContractAgentService.queryAgentInfoById(agentId);
        signPartyB.setSignType("B");
        signPartyB.setSignId(agentId);
        signPartyB.setSignName(agentInfo.getName());
        signPartyB.setSignPeople(agentInfo.getLegalPerson());
        signPartyB.setTempletContractId(0);
        signPartyB.setSignPhone(MoreObjects.firstNonNull(agentInfo.getLegalPersonPhone(),""));
        initSignTime(signPartyB);
    }

    private void initSubjectChangeSupplementPartyB(int opUid, String opName, WmTempletContractSignBo signPartyB) throws TException {
        signPartyB.setSignType("B");
        signPartyB.setSignId(0);
        signPartyB.setSignName(ContractSignSubjectEnum.HAINAN_LIANGXIN.getDesc());
        signPartyB.setSignPeople(opName);
        signPartyB.setTempletContractId(0);
        setSignBPhone(opUid, signPartyB);
        initSignTime(signPartyB);
    }

    private void initSubjectChangeSupplementPartyC(int opUid, String opName, WmTempletContractSignBo signPartyC) throws TException {
        signPartyC.setSignType("C");
        signPartyC.setSignId(0);
        signPartyC.setSignName(ContractSignSubjectEnum.BEIJING_SANKUAI.getDesc());
        signPartyC.setSignPeople(opName);
        signPartyC.setTempletContractId(0);
        setSignBPhone(opUid, signPartyC);
        initSignTime(signPartyC);
    }

    private void setSignBPhone(int opUid, WmTempletContractSignBo signPartyB) throws TException {
        signPartyB.setSignPhone(MoreObjects.firstNonNull(empServiceAdaptor.getPhone(opUid),""));
    }
}
