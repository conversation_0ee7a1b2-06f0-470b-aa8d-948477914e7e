package com.sankuai.meituan.waimai.customer.service.customer.monitor;

import com.dianping.frog.sdk.alarm.DXUtil;
import com.dianping.frog.sdk.data.MafkaRawData;
import com.dianping.frog.sdk.data.RawData;
import com.dianping.frog.sdk.rpc.RpcService;
import com.dianping.frog.sdk.rpc.RpcServiceFactory;
import com.dianping.frog.sdk.runner.DefaultRuleRunner;
import com.dianping.frog.sdk.util.BcpLoggerUtils;
import com.dianping.lion.client.util.JsonUtils;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import org.apache.commons.lang3.StringUtils;

import java.util.Map;

public class WmCustomerPoiLabelRuleRunner extends DefaultRuleRunner {

    private Gson gson = new GsonBuilder().create();


    @Override
    public boolean filter(RawData rawData, RawData... targetData) throws Exception {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.customer.monitor.WmCustomerPoiLabelRuleRunner.filter(com.dianping.frog.sdk.data.RawData,com.dianping.frog.sdk.data.RawData[])");
        MafkaRawData triggerMsgData = (MafkaRawData) rawData;
        Object body = triggerMsgData.getMafkaMessage().getBody();

        if (null == body || StringUtils.isBlank(body.toString())) {
            return false;
        }

        WmLabelUpdateInfo updateMsg = gson.fromJson(body.toString(), WmLabelUpdateInfo.class);
        if (updateMsg == null) {
            return false;
        }
        // 表名过滤
        String tableName = updateMsg.getTableName();
        if (!tableName.contains("wm_poi_label_rel")) {
            return false;
        }
        WmLabelUpdateInfo.Data data = updateMsg.getData();
        if (data == null) {
            return false;
        }
        Integer sujectType = data.getSubject_type();
        // 业务类型过滤，sujectType=2表示客户
        if (sujectType == null || sujectType.intValue() != 2) {
            return false;
        }
        return true;
    }

    @Override
    public String check(RawData rawData, RawData... rawData2) throws Exception {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.service.customer.monitor.WmCustomerPoiLabelRuleRunner.check(com.dianping.frog.sdk.data.RawData,com.dianping.frog.sdk.data.RawData[])");
        MafkaRawData triggerMsgData = (MafkaRawData) rawData;
        Object body = triggerMsgData.getMafkaMessage().getBody();
        WmLabelUpdateInfo updateMsg = gson.fromJson(body.toString(), WmLabelUpdateInfo.class);
        try {
            if (updateMsg == null) {
                return null;
            }
            WmLabelUpdateInfo.Data data = updateMsg.getData();
            if (data == null) {
                return null;
            }
            // 获取客户id，调用下游处理消息
            Long mtCustomerId = data.getSubject_id();
            Integer opUid = data.getOp_uid();
            String opUname = data.getOp_uname();
            Long wmLabelId = data.getWm_label_id();
            //获取打企客标事件
            String operateType = updateMsg.getType();
            Map<String, Object> params = Maps.newHashMap();
            params.put("mtCustomerId", mtCustomerId);
            params.put("opUid", opUid);
            params.put("opUname", opUname);
            params.put("wmLabelId", wmLabelId);
            params.put("operateType", operateType);
            RpcService rpcService = RpcServiceFactory.getThriftInstance("com.sankuai.meituan.waimai.thrift.customer.service.WmCustomerPoiThriftService",
                    "com.sankuai.waimai.e.customer", 10000, null, "8435");
            String result = rpcService.invoke("monitorCustomerPoiLabel",
                    Lists.newArrayList("com.sankuai.meituan.waimai.thrift.customer.domain.customer.WmCustomerPoiLabelInfo"),
                    Lists.newArrayList(JsonUtils.toJson(params)));

            if (!StringUtils.isBlank(result) && !"\"\"".equals(result)) {
                return result;
            }
        } catch (Exception e) {
            BcpLoggerUtils.info("类型转换失败:msg={}" + body.toString() + ";异常日志:" + e.getStackTrace());    // 打印日志
        }
        return null;
    }

    @Override
    public void alarm(String s, RawData rawData, RawData... rawData2) throws Exception {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.customer.monitor.WmCustomerPoiLabelRuleRunner.alarm(java.lang.String,com.dianping.frog.sdk.data.RawData,com.dianping.frog.sdk.data.RawData[])");
        //通过大象公众号发送告警消息, 收件人为告警配置中的告警接收人。
        DXUtil.sendAlarm(s);
    }


    public class WmLabelUpdateInfo {

        private String tableName;
        private Long timestamp;
        private Long scn;
        private String type;
        private String sourceIP;
        private Data data;

        public String getTableName() {
            return tableName;
        }

        public void setTableName(String tableName) {
            this.tableName = tableName;
        }

        public Long getTimestamp() {
            return timestamp;
        }

        public void setTimestamp(Long timestamp) {
            this.timestamp = timestamp;
        }

        public Long getScn() {
            return scn;
        }

        public void setScn(Long scn) {
            this.scn = scn;
        }

        public String getType() {
            return type;
        }

        public void setType(String type) {
            this.type = type;
        }

        public String getSourceIP() {
            return sourceIP;
        }

        public void setSourceIP(String sourceIP) {
            this.sourceIP = sourceIP;
        }

        public Data getData() {
            return data;
        }

        public void setData(Data data) {
            this.data = data;
        }

        public class Data {
            private Long subject_id;
            private Integer subject_type;
            private Long utime;
            private Integer notice_status;
            private Integer apply_status;
            private Long task_id;
            private String c_uname;
            private Integer valid;
            private Long wm_poi_id;
            private Integer op_uid;
            private String apply_reason;
            private Integer c_uid;
            private String refused_reason;
            private Long ctime;
            private Long id;
            private String pic_url;
            private Long wm_label_id;
            private String op_uname;

            public Long getSubject_id() {
                return subject_id;
            }

            public void setSubject_id(Long subject_id) {
                this.subject_id = subject_id;
            }

            public Integer getSubject_type() {
                return subject_type;
            }

            public void setSubject_type(Integer subject_type) {
                this.subject_type = subject_type;
            }

            public Long getUtime() {
                return utime;
            }

            public void setUtime(Long utime) {
                this.utime = utime;
            }

            public Integer getNotice_status() {
                return notice_status;
            }

            public void setNotice_status(Integer notice_status) {
                this.notice_status = notice_status;
            }

            public Integer getApply_status() {
                return apply_status;
            }

            public void setApply_status(Integer apply_status) {
                this.apply_status = apply_status;
            }

            public Long getTask_id() {
                return task_id;
            }

            public void setTask_id(Long task_id) {
                this.task_id = task_id;
            }

            public String getC_uname() {
                return c_uname;
            }

            public void setC_uname(String c_uname) {
                this.c_uname = c_uname;
            }

            public Integer getValid() {
                return valid;
            }

            public void setValid(Integer valid) {
                this.valid = valid;
            }

            public Long getWm_poi_id() {
                return wm_poi_id;
            }

            public void setWm_poi_id(Long wm_poi_id) {
                this.wm_poi_id = wm_poi_id;
            }

            public Integer getOp_uid() {
                return op_uid;
            }

            public void setOp_uid(Integer op_uid) {
                this.op_uid = op_uid;
            }

            public String getApply_reason() {
                return apply_reason;
            }

            public void setApply_reason(String apply_reason) {
                this.apply_reason = apply_reason;
            }

            public Integer getC_uid() {
                return c_uid;
            }

            public void setC_uid(Integer c_uid) {
                this.c_uid = c_uid;
            }

            public String getRefused_reason() {
                return refused_reason;
            }

            public void setRefused_reason(String refused_reason) {
                this.refused_reason = refused_reason;
            }

            public Long getCtime() {
                return ctime;
            }

            public void setCtime(Long ctime) {
                this.ctime = ctime;
            }

            public Long getId() {
                return id;
            }

            public void setId(Long id) {
                this.id = id;
            }

            public String getPic_url() {
                return pic_url;
            }

            public void setPic_url(String pic_url) {
                this.pic_url = pic_url;
            }

            public Long getWm_label_id() {
                return wm_label_id;
            }

            public void setWm_label_id(Long wm_label_id) {
                this.wm_label_id = wm_label_id;
            }

            public String getOp_uname() {
                return op_uname;
            }

            public void setOp_uname(String op_uname) {
                this.op_uname = op_uname;
            }
        }

    }
}
