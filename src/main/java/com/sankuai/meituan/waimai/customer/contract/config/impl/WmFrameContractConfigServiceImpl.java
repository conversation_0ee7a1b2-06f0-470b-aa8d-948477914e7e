package com.sankuai.meituan.waimai.customer.contract.config.impl;

import com.alibaba.fastjson.JSON;
import com.sankuai.meituan.waimai.customer.contract.config.WmFrameContractConfigActivityService;
import com.sankuai.meituan.waimai.customer.contract.config.WmFrameContractConfigService;
import com.sankuai.meituan.waimai.customer.domain.WmEcontractSignManualTaskDB;
import com.sankuai.meituan.waimai.customer.domain.WmEcontractSignTaskDB;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerRealTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractTaskApplyTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.config.ConfigContractQueryRequestDTO;
import com.sankuai.meituan.waimai.thrift.customer.domain.config.ContractConfigInfo;
import com.sankuai.meituan.waimai.thrift.customer.domain.config.CustomerTypeInfoResponse;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import lombok.extern.slf4j.Slf4j;
import org.apache.thrift.TException;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * @description:
 * @author: liuyunjie05
 * @create: 2024/5/19 15:57
 */
@Slf4j
@Service
public class WmFrameContractConfigServiceImpl implements WmFrameContractConfigService {

    @Resource
    private WmFrameContractConfigActivityService wmFrameContractConfigActivityService;

    @Override
    public List<CustomerTypeInfoResponse> getAllCustomerTypeInfo() {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_1", "com.sankuai.meituan.waimai.customer.contract.config.impl.WmFrameContractConfigServiceImpl.getAllCustomerTypeInfo()");
        List<CustomerTypeInfoResponse> responseList = new ArrayList<>();
        for (CustomerRealTypeEnum value : CustomerRealTypeEnum.values()) {
            if (value == CustomerRealTypeEnum.QINGXUANZE || value == CustomerRealTypeEnum.DEFAULT_TYPE) {
                continue;
            }
            CustomerTypeInfoResponse response = new CustomerTypeInfoResponse();
            response.setValue(value.getValue());
            response.setName(value.getName());
            response.setBizOrgCode(value.getBizOrgCode());
            responseList.add(response);
        }
        return responseList;
    }

    @Override
    public List<ContractConfigInfo> queryConfigFrameContract(ConfigContractQueryRequestDTO requestDTO) throws TException, WmCustomerException {
        return wmFrameContractConfigActivityService.queryConfigFrameContract(requestDTO.getOperatorId(), requestDTO.getCustomerId(), requestDTO.getDeviceType());
    }

    @Override
    public ContractConfigInfo queryContractConfigInfo(Integer contractId) throws WmCustomerException {
        return wmFrameContractConfigActivityService.queryContractConfigInfo(contractId);
    }

    @Override
    public ContractConfigInfo queryContractConfigInfo(String contractCode) throws WmCustomerException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.contract.config.impl.WmFrameContractConfigServiceImpl.queryContractConfigInfo(java.lang.String)");
        return wmFrameContractConfigActivityService.queryContractConfigInfo(contractCode);
    }

    @Override
    public List<ContractConfigInfo> allConfigFrameContract() {
        return wmFrameContractConfigActivityService.allConfigFrameContract();
    }

    /**
     * 将配置化合同的module映射成 common_config_frame_contract_agreement
     * @param signManualTaskDB 手动打包任务
     */
    @Override
    public String handleManualTaskModule(WmEcontractSignManualTaskDB signManualTaskDB) {
        EcontractTaskApplyTypeEnum applyTypeEnum = EcontractTaskApplyTypeEnum.getByName(signManualTaskDB.getModule());
        if (applyTypeEnum != EcontractTaskApplyTypeEnum.UNKNOW) {
            return signManualTaskDB.getModule();
        }
        return handleConfigContractManualTaskModule(signManualTaskDB);
    }

    /**
     * 将配置化合同的applyType映射成 common_config_frame_contract_agreement
     * @param taskDB 手动打包任务
     */
    @Override
    public String handleSignTaskApplyType(WmEcontractSignTaskDB taskDB) {
        EcontractTaskApplyTypeEnum applyTypeEnum = EcontractTaskApplyTypeEnum.getByName(taskDB.getApplyType());
        if (applyTypeEnum != EcontractTaskApplyTypeEnum.UNKNOW) {
            return taskDB.getApplyType();
        }
        return handleConfigContractSignTaskApplyType(taskDB);
    }

    private String handleConfigContractSignTaskApplyType(WmEcontractSignTaskDB taskDB) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.contract.config.impl.WmFrameContractConfigServiceImpl.handleConfigContractSignTaskApplyType(com.sankuai.meituan.waimai.customer.domain.WmEcontractSignTaskDB)");
        try {
            ContractConfigInfo configInfo = queryContractConfigInfo(taskDB.getApplyType());
            if (configInfo != null) {
                return EcontractTaskApplyTypeEnum.COMMON_CONFIG_FRAME_CONTRACT_AGREEMENT.getName();
            } else {
                return taskDB.getApplyType();
            }
        } catch (Exception e) {
            log.error("WmFrameContractConfigServiceImpl#handleSignTaskApplyType, taskDB: {}, error", JSON.toJSONString(taskDB), e);
            return taskDB.getApplyType();
        }
    }

    private String handleConfigContractManualTaskModule(WmEcontractSignManualTaskDB signManualTaskDB) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.contract.config.impl.WmFrameContractConfigServiceImpl.handleConfigContractManualTaskModule(com.sankuai.meituan.waimai.customer.domain.WmEcontractSignManualTaskDB)");
        try {
            ContractConfigInfo configInfo = queryContractConfigInfo(signManualTaskDB.getModule());
            if (configInfo != null) {
                return EcontractTaskApplyTypeEnum.COMMON_CONFIG_FRAME_CONTRACT_AGREEMENT.getName();
            }
            return signManualTaskDB.getModule();
        } catch (Exception e) {
            log.error("WmFrameContractConfigServiceImpl#handleManualTaskModule, signManualTaskDB: {}, error", JSON.toJSONString(signManualTaskDB), e);
            return signManualTaskDB.getModule();
        }
    }
}
