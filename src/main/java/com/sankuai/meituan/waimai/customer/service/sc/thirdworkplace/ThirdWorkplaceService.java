package com.sankuai.meituan.waimai.customer.service.sc.thirdworkplace;

import com.meituan.waimai.common.utils.JacksonUtils;
import com.sankuai.djdata.readata.openapi.QueryContext;
import com.sankuai.meituan.waimai.customer.config.MccConfig;
import com.sankuai.meituan.waimai.customer.service.common.ExcelExportService;
import com.sankuai.meituan.waimai.customer.service.sc.thirdworkplace.wrapper.OneServiceQueryResultBo;
import com.sankuai.meituan.waimai.customer.service.sc.thirdworkplace.wrapper.OneServiceWrapper;
import com.sankuai.meituan.waimai.customer.service.sc.thrift.WmScThirdWorkplaceThriftAssemble;
import com.sankuai.meituan.waimai.customer.util.SSOUtil;
import com.sankuai.meituan.waimai.thrift.customer.domain.sc.thirdworkplace.*;
import com.sankuai.meituan.waimai.thrift.exception.WmSchCantException;
import com.sankuai.waimai.crm.authenticate.client.service.AuthenticateRoleService;
import com.sankuai.waimai.crm.authenticate.client.service.response.AuthenticateUserRoleAssertResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

import static com.sankuai.meituan.waimai.thrift.customer.constant.sc.WmScCodeConstants.BIZ_PARA_ERROR;

@Slf4j
@Component
public class ThirdWorkplaceService {
    @Autowired
    private OneServiceWrapper oneServiceWrapper;

    @Autowired
    private WmScThirdWorkplaceThriftAssemble wmScThirdWorkplaceThriftAssemble;

    @Autowired
    private AuthenticateRoleService authenticateRoleService;

    @Resource
    private ExcelExportService excelExportService;


    public static final String WM_WORKPLACE_ADMIN_ROLE_CODE = "campus_third_workplace_admin";

    public static final String WM_WORKPLACE_CITY_ROLE_CODE = "campus_third_workplace_city";

    public static final String WM_WORKPLACE_KA_ROLE_CODE = "campus_third_workplace_ka";

    public static final String WM_WORKPLACE_PS_ROLE_CODE = "campus_third_workplace_ps";

    public static final Integer WM_TENANT_ID = 1000008;

    public WmScThirdWorkplaceQueryListDTO queryThirdWorkplaceListDTO(WmScThirdWorkplaceQueryBo wmScThirdWorkplaceQueryBo) throws WmSchCantException {

        // 构建查询上下文
        WmScThirdWorkplaceQueryBo countQueryBo = deepCopyQueryBoForCount(wmScThirdWorkplaceQueryBo);
        QueryContext listQueryContext = wmScThirdWorkplaceThriftAssemble.wmScThirdWorkplaceQueryBO2ListQueryContext(wmScThirdWorkplaceQueryBo);
        QueryContext countQueryContext = wmScThirdWorkplaceThriftAssemble.wmScThirdWorkplaceQueryBO2CountQueryContext(countQueryBo);

        try {
            // 创建并发任务
            CompletableFuture<OneServiceQueryResultBo> listQuery = CompletableFuture.supplyAsync(() -> {
                log.info("列表查询开始");
                try {
                    return oneServiceWrapper.queryDateFromOneService(listQueryContext);
                } catch (Exception e) {
                    log.error("列表查询异常", e);
                    throw new RuntimeException(e);
                }
            });

            CompletableFuture<Long> countQuery = CompletableFuture.supplyAsync(() -> {
                log.info("总数查询开始");
                try {
                    return oneServiceWrapper.queryTotalCountFromOneService(countQueryContext);
                } catch (Exception e) {
                    log.error("总数查询异常", e);
                    return 0L;
                }
            });

            // 等待所有任务完成并获取结果
            CompletableFuture<WmScThirdWorkplaceQueryListDTO> resultFuture = listQuery.thenCombine(countQuery,
                    (listResult, countResult) -> {
                        return wmScThirdWorkplaceThriftAssemble.toQueryListDTO(listResult, countResult);
                    });

            // 阻塞等待最终结果
            WmScThirdWorkplaceQueryListDTO wmScThirdWorkplaceQueryListDTO = resultFuture.get(60, TimeUnit.SECONDS);
            wmScThirdWorkplaceQueryListDTO.setPageNum(wmScThirdWorkplaceQueryBo.getPageNo());
            wmScThirdWorkplaceQueryListDTO.setPageSize(wmScThirdWorkplaceQueryBo.getPageSize());
            return wmScThirdWorkplaceQueryListDTO;


        } catch (java.util.concurrent.TimeoutException e) {
            log.error("查询超时", e);
            throw new WmSchCantException(BIZ_PARA_ERROR, "查询超时");
        } catch (Exception e) {
            log.error("并发查询失败", e);
            throw new WmSchCantException(BIZ_PARA_ERROR, "查询失败: " + e.getMessage());
        }
    }

    public WmScThirdWorkplaceQueryMetricsDTO queryThirdWorkplaceMetricsDTO(WmScThirdWorkplaceQueryBo wmScThirdWorkplaceQueryBo) throws WmSchCantException {

        QueryContext queryContext = wmScThirdWorkplaceThriftAssemble.wmScThirdWorkplaceQueryBO2MetricsQueryContext(wmScThirdWorkplaceQueryBo);

        OneServiceQueryResultBo oneServiceQueryResultBo = oneServiceWrapper.queryDateFromOneService(queryContext);

        return wmScThirdWorkplaceThriftAssemble.toQueryMetricsDTO(oneServiceQueryResultBo);
    }

    public WmScThirdWorkplaceQueryEnumDTO getThirdWorkplaceQueryEnum() throws WmSchCantException {
        try {
            return MccConfig.getThirdWorkplaceQueryEnum();
        } catch (IOException e) {
            throw new WmSchCantException(BIZ_PARA_ERROR, "配置解析错误");
        }
    }

    /**
     * 导出第三方工作台列表数据
     *
     * @param wmScThirdWorkplaceQueryBo 查询参数
     * @param user 当前用户
     * @return 导出文件下载链接
     * @throws WmSchCantException 业务异常
     */
    public String exportThirdWorkplaceListData(WmScThirdWorkplaceQueryBo wmScThirdWorkplaceQueryBo, SSOUtil.SsoUser user) throws WmSchCantException {
        log.info("开始导出第三方工作台列表数据, queryBo={}, user={}", wmScThirdWorkplaceQueryBo, user);

        try {
            // 1 参数校验
            if (Objects.isNull(wmScThirdWorkplaceQueryBo)) {
                throw new IllegalArgumentException("查询参数不能为空");
            }
            String statisticalDataStr = wmScThirdWorkplaceQueryBo.getStatisticalDataStr();

            List<WmScThirdWorkplaceQueryListItemExcelModel> excelModels = new ArrayList<>();
            // 2 识别导出场景
            // 导出场景：2.1 导出全量与导出筛选场景(该筛选不包含筛选学校id的场景)
            if(CollectionUtils.isEmpty(wmScThirdWorkplaceQueryBo.getSchools())){
                // 导出场景：2.1 导出全量,先查询总数，再分批次查询
                log.info("开始全量导出，先查询总数");

                // 2.1.1 查询总数
                Long totalCount = getTotalCountForExport(wmScThirdWorkplaceQueryBo);
                log.info("查询到总数据量: {}", totalCount);

                if (totalCount == 0) {
                    log.info("没有数据需要导出");
                    // 导出空Excel
                    try (ByteArrayOutputStream os = new ByteArrayOutputStream()) {
                        return excelExportService.exportExcel(Collections.emptyList(), WmScThirdWorkplaceQueryListItemExcelModel.class, os, user);
                    }
                }

                // 2.1.2 根据总数计算分页参数
                int pageSize = 100;
                int totalPages = (int) Math.ceil((double) totalCount / pageSize);
                log.info("总数据量: {}, 每页大小: {}, 总页数: {}", totalCount, pageSize, totalPages);

                // 2.1.3 分批次查询数据
                for(int pageNo = 1; pageNo <= totalPages; pageNo++){
                    log.info("开始查询第 {}/{} 页数据", pageNo, totalPages);

                    // 创建分页查询参数
                    WmScThirdWorkplaceQueryBo pageQueryBo = deepCopyQueryBoForExport(wmScThirdWorkplaceQueryBo);
                    pageQueryBo.setPageNo(pageNo);
                    pageQueryBo.setPageSize(pageSize);

                    List<WmScThirdWorkplaceQueryListItemExcelModel> partitionData = getWmScThirdWorkplaceQueryListItemExcelModels(pageQueryBo);

                    // 构建日期信息
                    partitionData.forEach(excelModel ->{
                        excelModel.setDateRange(statisticalDataStr);
                    });

                    if (CollectionUtils.isNotEmpty(partitionData)) {
                        excelModels.addAll(partitionData);
                        log.info("第 {}/{} 页查询完成，本页数据量: {}, 累计数据量: {}",
                                pageNo, totalPages, partitionData.size(), excelModels.size());
                    } else {
                        log.warn("第 {}/{} 页查询结果为空", pageNo, totalPages);
                    }

                    // 如果当前页数据量小于pageSize，说明已经是最后一页
                    if (partitionData.size() < pageSize) {
                        log.info("当前页数据量({})小于页大小({})，提前结束分页查询", partitionData.size(), pageSize);
                        break;
                    }
                }

                log.info("全量导出数据查询完成，总数据量: {}", excelModels.size());
            }else{
                // 导出场景：2.2 导出勾选学校id的场景或筛选学校id的场景
                wmScThirdWorkplaceQueryBo.setPageNo(1);
                wmScThirdWorkplaceQueryBo.setPageSize(wmScThirdWorkplaceQueryBo.getSchools().size());
                List<WmScThirdWorkplaceQueryListItemExcelModel> excelData = getWmScThirdWorkplaceQueryListItemExcelModels(wmScThirdWorkplaceQueryBo);
                // 构建日期信息
                excelData.forEach(excelModel ->{
                    excelModel.setDateRange(statisticalDataStr);
                });
                if (CollectionUtils.isNotEmpty(excelData))
                    excelModels.addAll(excelData);
            }



            // 3 导出Excel
            try (ByteArrayOutputStream os = new ByteArrayOutputStream()) {
                String downloadUrl = excelExportService.exportExcel(excelModels, WmScThirdWorkplaceQueryListItemExcelModel.class, os, user);
                log.info("第三方工作台列表导出完成, 数据量={}, downloadUrl={}", excelModels.size(), downloadUrl);
                return downloadUrl;
            }

        } catch (Exception e) {
            log.error("导出第三方工作台列表数据失败", e);
            throw new WmSchCantException(BIZ_PARA_ERROR, "导出失败:" + e.getMessage());
        }
    }


    /**
     * 获取导出数据的总数量
     */
    private Long getTotalCountForExport(WmScThirdWorkplaceQueryBo wmScThirdWorkplaceQueryBo) throws WmSchCantException {
        try {
            // 创建用于查询总数的参数对象
            WmScThirdWorkplaceQueryBo countQueryBo = deepCopyQueryBoForCount(wmScThirdWorkplaceQueryBo);

            // 构建总数查询上下文
            QueryContext countQueryContext = wmScThirdWorkplaceThriftAssemble.wmScThirdWorkplaceQueryBO2CountQueryContext(countQueryBo);

            // 查询总数
            Long totalCount = oneServiceWrapper.queryTotalCountFromOneService(countQueryContext);

            log.info("导出数据总数查询完成: {}", totalCount);
            return totalCount != null ? totalCount : 0L;

        } catch (Exception e) {
            log.error("查询导出数据总数失败", e);
            throw new WmSchCantException(BIZ_PARA_ERROR, "查询数据总数失败: " + e.getMessage());
        }
    }

    /**
     * 为导出创建查询参数的深拷贝（保留原始分页信息）
     */
    private WmScThirdWorkplaceQueryBo deepCopyQueryBoForExport(WmScThirdWorkplaceQueryBo original) {
        WmScThirdWorkplaceQueryBo copy = new WmScThirdWorkplaceQueryBo();

        // 拷贝所有字段
        deepCopyQueryBo(original, copy);

        // 保留原始的分页信息，后续会被重新设置
        copy.setPageNo(original.getPageNo());
        copy.setPageSize(original.getPageSize());

        return copy;
    }



    private List<WmScThirdWorkplaceQueryListItemExcelModel> getWmScThirdWorkplaceQueryListItemExcelModels(WmScThirdWorkplaceQueryBo wmScThirdWorkplaceQueryBo) throws WmSchCantException {
        // 1. 构建查询上下文
        QueryContext queryContext = wmScThirdWorkplaceThriftAssemble.wmScThirdWorkplaceQueryBO2ListQueryContext(wmScThirdWorkplaceQueryBo);

        // 2. 查询数据
        OneServiceQueryResultBo oneServiceQueryResultBo = oneServiceWrapper.queryDateFromOneService(queryContext);

        // 3. 转换为业务对象列表
        List<WmScThirdWorkplaceQueryListItem> businessItems =
                wmScThirdWorkplaceThriftAssemble.getPublicOneServiceDataConverter().convertToWorkplaceListItems(
                        wmScThirdWorkplaceThriftAssemble.getPublicOneServiceQueryMappingConfig(),
                        oneServiceQueryResultBo);

        // 4. 转换为Excel模型列表
        return wmScThirdWorkplaceThriftAssemble.convertToListItemExcelModels(businessItems);
    }

    /**
     * 导出第三方工作台指标数据
     *
     * @param wmScThirdWorkplaceQueryBo 查询参数
     * @param user 当前用户
     * @return 导出文件下载链接
     * @throws WmSchCantException 业务异常
     */
    public String exportThirdWorkplaceMetricsData(WmScThirdWorkplaceQueryBo wmScThirdWorkplaceQueryBo, SSOUtil.SsoUser user) throws WmSchCantException {
        log.info("开始导出第三方工作台指标数据, queryBo={}, user={}", wmScThirdWorkplaceQueryBo, user);

        try {
            // 1. 参数校验
            if (wmScThirdWorkplaceQueryBo == null) {
                throw new IllegalArgumentException("查询参数不能为空");
            }

            // 2. 构建查询上下文
            QueryContext queryContext = wmScThirdWorkplaceThriftAssemble.wmScThirdWorkplaceQueryBO2MetricsQueryContext(wmScThirdWorkplaceQueryBo);

            // 3. 查询数据
            OneServiceQueryResultBo oneServiceQueryResultBo = oneServiceWrapper.queryDateFromOneService(queryContext);

            // 4. 转换为业务对象
            WmScThirdWorkplaceQueryMetrics businessItem =
                    wmScThirdWorkplaceThriftAssemble.getPublicOneServiceDataConverter().convertToWorkplaceMetrics(
                            wmScThirdWorkplaceThriftAssemble.getPublicOneServiceQueryMappingConfig(),
                            oneServiceQueryResultBo);

            // 5. 转换为Excel模型
            WmScThirdWorkplaceQueryMetricsExcelModel excelModel = wmScThirdWorkplaceThriftAssemble.convertToMetricsExcelModel(businessItem);

            // 在excel中记录日期信息
            excelModel.setDateRange(wmScThirdWorkplaceQueryBo.getStatisticalDataStr());

            // 6. 导出Excel
            try (ByteArrayOutputStream os = new ByteArrayOutputStream()) {
                List<WmScThirdWorkplaceQueryMetricsExcelModel> excelModels = Collections.singletonList(excelModel);
                String downloadUrl = excelExportService.exportExcel(excelModels, WmScThirdWorkplaceQueryMetricsExcelModel.class, os, user);
                log.info("第三方工作台指标导出完成, downloadUrl={}", downloadUrl);
                return downloadUrl;
            }

        } catch (Exception e) {
            log.error("导出第三方工作台指标数据失败", e);
            throw new WmSchCantException(BIZ_PARA_ERROR, "导出失败:" + e.getMessage());
        }
    }

    public AssertUserRoleDTO getUserAssertResult(SSOUtil.SsoUser user) throws WmSchCantException {
        AssertUserRoleDTO assertUserRoleDTO = new AssertUserRoleDTO();
        // 顺序勿动，影响角色优先级判断优先判定为总部-城市角色-校企-配送
        ThirdWorkplaceQueryRoleEnum roleEnum = ThirdWorkplaceQueryRoleEnum.UNKNOWN;
        if (userRoleAssert(WM_TENANT_ID, (int) user.getId(), WM_WORKPLACE_PS_ROLE_CODE)){
            assertUserRoleDTO.setIsPs(true);
            roleEnum = ThirdWorkplaceQueryRoleEnum.PS;
        }
        if (userRoleAssert(WM_TENANT_ID, (int) user.getId(), WM_WORKPLACE_KA_ROLE_CODE)){
            assertUserRoleDTO.setIsCampus(true);
            roleEnum = ThirdWorkplaceQueryRoleEnum.KA;
        }
        if (userRoleAssert(WM_TENANT_ID, (int) user.getId(), WM_WORKPLACE_CITY_ROLE_CODE)){
            assertUserRoleDTO.setIsCity(true);
            roleEnum = ThirdWorkplaceQueryRoleEnum.CITY;
        }
        if (userRoleAssert(WM_TENANT_ID, (int) user.getId(), WM_WORKPLACE_ADMIN_ROLE_CODE)){
            assertUserRoleDTO.setIsHq(true);
            roleEnum = ThirdWorkplaceQueryRoleEnum.ADMIN;
        }
        assertUserRoleDTO.setSchoolTrilateralOrgType(roleEnum.getCode());
        return assertUserRoleDTO;
    }

    private boolean userRoleAssert(Integer tenantId, Integer uid, String roleCode) {
        try {
            log.info("请求权限进行角色的判定: uid: {}, roleCode: {}", uid, roleCode);

            AuthenticateUserRoleAssertResponse response = authenticateRoleService.userRoleAssert(
                    tenantId, uid, roleCode
            );
            log.info("角色判定返回结果: {}", JacksonUtils.toJson(response));
            if (Objects.isNull(response) || response.getCode() != 0) {
                log.warn("调用权限服务失败");
                throw new RuntimeException("调用权限失败");
            }

            return response.getAssertResult();

        } catch (Exception e) {
            log.warn("调用权限失败", e);
            throw new RuntimeException("调用权限失败");
        }
    }

    private WmScThirdWorkplaceQueryBo deepCopyQueryBoForCount(WmScThirdWorkplaceQueryBo original) {
        WmScThirdWorkplaceQueryBo copy = new WmScThirdWorkplaceQueryBo();

        // 拷贝所有字段（根据实际的字段进行调整）
        deepCopyQueryBo(original, copy);

        // count查询不需要分页参数，可以设置为默认值
        copy.setPageNo(1);
        copy.setPageSize(1);

        return copy;
    }

    private static void deepCopyQueryBo(WmScThirdWorkplaceQueryBo original, WmScThirdWorkplaceQueryBo copy) {
        copy.setStatisticalDataStr(original.getStatisticalDataStr());
        copy.setStatisticalDimension(original.getStatisticalDimension());
        copy.setSchoolTrilateralOrgType(original.getSchoolTrilateralOrgType());
        copy.setSchoolBusinessType(original.getSchoolBusinessType());
        copy.setSchoolCategory(original.getSchoolCategory() != null ? new ArrayList<>(original.getSchoolCategory()) : null);
        copy.setCooperationStatus(original.getCooperationStatus() != null ? new ArrayList<>(original.getCooperationStatus()) : null);
        copy.setContractTypes(original.getContractTypes() != null ? new ArrayList<>(original.getContractTypes()) : null);
        copy.setCities(original.getCities() != null ? new ArrayList<>(original.getCities()) : null);
        copy.setContractMethods(original.getContractMethods() != null ? new ArrayList<>(original.getContractMethods()) : null);
        copy.setContractOneToOne(original.getContractOneToOne());
        copy.setRealOneToOne(original.getRealOneToOne());
        copy.setPartnerStage(original.getPartnerStage() != null ? new ArrayList<>(original.getPartnerStage()) : null);
        copy.setDeliveryStatus(original.getDeliveryStatus() != null ? new ArrayList<>(original.getDeliveryStatus()) : null);
        copy.setChurnStatus(original.getChurnStatus() != null ? new ArrayList<>(original.getChurnStatus()) : null);
        copy.setSchools(original.getSchools() != null ? new ArrayList<>(original.getSchools()) : null);
        copy.setSchoolEnterpriseManagers(original.getSchoolEnterpriseManagers() != null ? new ArrayList<>(original.getSchoolEnterpriseManagers()) : null);
        copy.setSchoolEnterpriseOrgStructure(original.getSchoolEnterpriseOrgStructure() != null ? new ArrayList<>(original.getSchoolEnterpriseOrgStructure()) : null);
        copy.setCampusDeliveryOrgStructure(original.getCampusDeliveryOrgStructure() != null ? new ArrayList<>(original.getCampusDeliveryOrgStructure()) : null);
        copy.setCityOrgStructure(original.getCityOrgStructure() != null ? new ArrayList<>(original.getCityOrgStructure()) : null);
    }
}
