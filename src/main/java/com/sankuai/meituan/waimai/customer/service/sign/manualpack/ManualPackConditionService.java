package com.sankuai.meituan.waimai.customer.service.sign.manualpack;

import static com.sankuai.meituan.waimai.customer.constant.WmEcontractConstant.MANUALBATCH_REACHED_STATUS;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.concurrent.*;

import com.dianping.cat.Cat;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.meituan.mtrace.thread.pool.ExecutorServiceTraceWrapper;
import com.sankuai.meituan.waimai.customer.adapter.WmCustomerGrayServiceAdapter;
import com.sankuai.meituan.waimai.customer.config.MccConfig;
import com.sankuai.meituan.waimai.customer.service.sign.manualpack.strategy.precheck.PreCheckStrategyFactory;
import org.apache.thrift.TException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.meituan.waimai.customer.contract.service.impl.WmContractService;
import com.sankuai.meituan.waimai.customer.domain.WmEcontractSignManualBatchDB;
import com.sankuai.meituan.waimai.customer.domain.WmEcontractSignManualTaskDB;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerPoiService;
import com.sankuai.meituan.waimai.customer.service.sign.biz.WmEcontractManualBatchBizService;
import com.sankuai.meituan.waimai.customer.service.sign.biz.WmEcontractManualTaskBizService;
import com.sankuai.meituan.waimai.customer.service.sign.lock.ManualTaskLockService;
import com.sankuai.meituan.waimai.customer.service.sign.pack.check.WmEcontractPackTaskCheckService;
import com.sankuai.meituan.waimai.customer.settle.ddd.domain.input.dservice.impl.WmSettleInputCommitDomainServiceImpl;
import com.sankuai.meituan.waimai.customer.settle.service.WmSettleSwitchService;
import com.sankuai.meituan.waimai.poilogistics.thrift.service.WmLogisticsFeeThriftService;
import com.sankuai.meituan.waimai.poilogistics.thrift.service.WmPoiLogisticsSignThriftService;
import com.sankuai.meituan.waimai.thrift.constant.WmContractErrorCodeConstant;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;

@Service
public class ManualPackConditionService {
    private static final Logger LOGGER = LoggerFactory.getLogger(ManualPackConditionService.class);

    // 线程池
    private static final ExecutorService MANUAL_PACK_CHECK_EXECUTOR_POOL = new ExecutorServiceTraceWrapper(
            new ThreadPoolExecutor(16,
                    32,
                    60L,
                    TimeUnit.SECONDS,
                    new ArrayBlockingQueue<>(1024),
                    new ThreadFactoryBuilder().setNameFormat("manual-pack-check-thread-%d").build(),
                    new ThreadPoolExecutor.DiscardPolicy())
    );

    @Autowired
    private WmCustomerPoiService wmCustomerPoiService;
    @Autowired
    private WmEcontractPackTaskCheckService wmEcontractPackTaskCheckService;
    @Autowired
    private WmEcontractManualTaskBizService wmEcontractManualTaskBizService;
    @Autowired
    private WmEcontractManualBatchBizService wmEcontractManualBatchBizService;
    @Autowired
    private ManualTaskLockService manualTaskLockService;
    @Autowired
    private WmContractService wmContractService;
    @Autowired
    private WmLogisticsFeeThriftService.Iface wmLogisticsFeeThriftService;
    @Autowired
    private WmSettleInputCommitDomainServiceImpl wmSettleInputCommitDomainService;
    @Autowired
    private WmSettleSwitchService wmSettleSwitchService;
    @Autowired
    private WmPoiLogisticsSignThriftService.Iface wmPoiLogisticsSignThriftService;
    @Autowired
    private WmCustomerGrayServiceAdapter wmCustomerGrayServiceAdapter;
    @Autowired
    private PreCheckStrategyFactory preCheckStrategyFactory;

    public boolean hasMultiPoi(int wmCustomerId){
        return wmCustomerPoiService.hasMultiPoi(wmCustomerId);
    }

    public boolean hasEffectModule(int wmCustomerId) throws WmCustomerException {
        try {
            return wmEcontractPackTaskCheckService.hasEffectDate(wmCustomerId);
        } catch (TException e) {
            LOGGER.error("hasEffectDate异常",e);
        }
        return false;
    }

    private Map<String,List<WmEcontractSignManualTaskDB>> getTaskGroup(List<WmEcontractSignManualTaskDB> taskDBList) {
        Map<String, List<WmEcontractSignManualTaskDB>> result = Maps.newHashMap();
        if (CollectionUtils.isEmpty(taskDBList)) {
            return result;
        }
        List<WmEcontractSignManualTaskDB> listTemp = null;
        for (WmEcontractSignManualTaskDB temp : taskDBList) {
            listTemp = result.get(temp.getModule());
            if (CollectionUtils.isEmpty(listTemp)) {
                result.put(temp.getModule(), Lists.newArrayList(temp));
            } else {
                listTemp.add(temp);
            }
        }
        return result;
    }

    private void checkManualTaskIds(List<Long> manualTaskIds,List<WmEcontractSignManualTaskDB> taskDBList) throws WmCustomerException{
        // 入参待打包任务数和查询出来的待打包任务数不一致
        if(manualTaskIds.size() != taskDBList.size()){
            throw new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR, "打包异常!请刷新页面重试");
        }
        int wmCustomerId = taskDBList.get(0).getCustomerId();
        for(WmEcontractSignManualTaskDB temp : taskDBList){
            // 客户不一致
            if(temp.getCustomerId() != wmCustomerId){
                LOGGER.info("#checkManualTaskIds异常 客户校验异常 wmCustomerId={} temp={}", wmCustomerId, JSONObject.toJSONString(temp));
                throw new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR, "打包异常!请刷新页面重试");
            }
            // 该任务已经发起打包
            if (temp.getManualBatchId() > 0) {
                LOGGER.info("#checkManualTaskIds异常 待打包任务已发起打包 temp={}", JSONObject.toJSONString(temp));
                throw new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR, "打包异常!请刷新页面重试");
            }
        }
    }

    public Map<String,List<WmEcontractSignManualTaskDB>> canApplyManualPack(List<Long> manualTaskIds,int commitUid) throws WmCustomerException,TException{
        // task有效性校验(是否属于同一个客户、是否还未发起打包)
        List<WmEcontractSignManualTaskDB> taskDBList = wmEcontractManualTaskBizService.batchGetByManualTaskIdsRT(manualTaskIds);
        checkManualTaskIds(manualTaskIds,taskDBList);
        // task锁校验-判断是否被其他线程占用锁
        manualTaskLockService.checkLock(manualTaskIds);
        // task锁校验-锁未被占用，则加锁
        manualTaskLockService.lockManualTaskId(manualTaskIds);
        // 业务规则校验
        Map<String,List<WmEcontractSignManualTaskDB>> taskMap = getTaskGroup(taskDBList);
        try {
            //回调各模块
            tryCheckForByModuleV2(taskMap, commitUid);
        } catch (WmCustomerException e) {
            // 异常情况则表示校验不通过，释放锁
            manualTaskLockService.unLockManualTaskId(manualTaskIds);
            throw e;
        } catch (Exception e) {
            LOGGER.error("#canApplyManualPack 发起打包模块前置校验异常 manualTaskIds:{}", manualTaskIds, e);
            // 异常情况则表示校验不通过，释放锁
            manualTaskLockService.unLockManualTaskId(manualTaskIds);
            Cat.logMetricForCount("can_apply_manual_pack_fail");
            throw new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR, "打包签约前置校验异常!");
        }
        return taskMap;
    }

    /**
     * 异步执行模块化前置校验V2版本
     *
     * @param taskMap 按模块分组的任务映射
     * @param commitUid 提交用户ID
     * @throws WmCustomerException 业务异常
     * @throws TException Thrift异常
     */
    private void tryCheckForByModuleV2(Map<String, List<WmEcontractSignManualTaskDB>> taskMap, int commitUid) throws WmCustomerException, TException {
        if (CollectionUtils.isEmpty(taskMap)) {
            return;
        }

        int taskGroupSize = MccConfig.getManualPackGroupSize();
        List<CompletableFuture<PreCheckResult>> allFutures = createAsyncTasks(taskMap, taskGroupSize, commitUid);
        executeAndHandleResults(allFutures);
    }

        /**
         * 创建异步任务列表
         *
         * @param taskMap 任务映射
         * @param taskGroupSize 任务分组大小
         * @param commitUid 提交用户ID
         * @return 异步任务列表
         */
        private List<CompletableFuture<PreCheckResult>> createAsyncTasks(Map<String, List<WmEcontractSignManualTaskDB>> taskMap,
                                                                         int taskGroupSize, int commitUid) {
            List<CompletableFuture<PreCheckResult>> allFutures = new ArrayList<>();

            for (Entry<String, List<WmEcontractSignManualTaskDB>> entry : taskMap.entrySet()) {
                String module = entry.getKey();
                List<WmEcontractSignManualTaskDB> taskInfos = entry.getValue();
                LOGGER.info("ManualPackConditionService.createAsyncTasks, module: {}, taskSize: {}", module, taskInfos.size());

                List<List<WmEcontractSignManualTaskDB>> taskGroups = Lists.partition(taskInfos, taskGroupSize);

                for (List<WmEcontractSignManualTaskDB> group : taskGroups) {
                    CompletableFuture<PreCheckResult> future = createPreCheckTask(module, group, commitUid);
                    allFutures.add(future);
                }
            }
            return allFutures;
        }

        /**
         * 创建单个前置校验任务
         *
         * @param module 模块名
         * @param group 任务组
         * @param commitUid 提交用户ID
         * @return 异步任务
         */
        private CompletableFuture<PreCheckResult> createPreCheckTask(String module, List<WmEcontractSignManualTaskDB> group, int commitUid) {

            return CompletableFuture.supplyAsync(() -> {
                try {
                    preCheckStrategyFactory.getPreCheckByMoudle(module).preCheck(module, group, commitUid);
                    return PreCheckResult.success();
                } catch (WmCustomerException e) {
                    LOGGER.warn("ManualPackConditionService#createPreCheckTask, WmCustomerException, module: {}", module, e);
                    return PreCheckResult.businessError(e);
                } catch (TException e) {
                    LOGGER.error("ManualPackConditionService#createPreCheckTask, TException, module: {}", module, e);
                    return PreCheckResult.thriftError(e);
                } catch (Exception e) {
                    LOGGER.error("ManualPackConditionService#createPreCheckTask, error, module: {}", module, e);
                    return PreCheckResult.businessError(new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR, "打包签约前置校验异常"));
                }
            }, MANUAL_PACK_CHECK_EXECUTOR_POOL);
        }

        /**
         * 执行所有异步任务并处理结果
         *
         * @param allFutures 异步任务列表
         * @throws WmCustomerException 业务异常
         * @throws TException Thrift异常
         */
        private void executeAndHandleResults(List<CompletableFuture<PreCheckResult>> allFutures) throws WmCustomerException, TException {
            try {
                CompletableFuture<Void> allTasks = CompletableFuture.allOf(allFutures.toArray(new CompletableFuture[0]));
                allTasks.get();

                // 检查是否有失败的任务
                for (CompletableFuture<PreCheckResult> future : allFutures) {
                    PreCheckResult result = future.get();
                    if (!result.isSuccess()) {
                        throw result.getException();
                    }
                }
            } catch (WmCustomerException e) {
                LOGGER.warn("ManualPackConditionService#executeAndHandleResults, warn", e);
                throw e;
            } catch (InterruptedException e) {
                LOGGER.warn("ManualPackConditionService#executeAndHandleResults, InterruptedException", e);
                Thread.currentThread().interrupt();
                throw new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR, "打包签约前置校验被中断");
            } catch (Exception e) {
                LOGGER.error("ManualPackConditionService#executeAndHandleResults, error", e);
                throw new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR, "打包签约前置校验执行异常");
            }
        }

        /**
         * 前置校验结果封装类
         */
        private static class PreCheckResult {
            private final boolean success;
            private final Exception exception;

            private PreCheckResult(boolean success, Exception exception) {
                this.success = success;
                this.exception = exception;
            }

            public static PreCheckResult success() {
                return new PreCheckResult(true, null);
            }

            public static PreCheckResult businessError(WmCustomerException e) {
                return new PreCheckResult(false, e);
            }

            public static PreCheckResult thriftError(TException e) {
                return new PreCheckResult(false, e);
            }


            public boolean isSuccess() {
                return success;
            }

            public Exception getException() throws WmCustomerException, TException {
                if (exception instanceof WmCustomerException) {
                    throw (WmCustomerException) exception;
                } else if (exception instanceof TException) {
                    throw (TException) exception;
                } else {
                    throw new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR, "系统异常, 请稍后重试");
                }
            }
        }

    public boolean isAllNeedPackModuleReached(long manualBatchId) {
        WmEcontractSignManualBatchDB manualBatch = wmEcontractManualBatchBizService
                .getManualBatchFromMaster(manualBatchId);
        if (manualBatch == null) {
            return false;
        }
        LOGGER.info("#isAllNeedPackModuleReached,manualBatch={}",JSONObject.toJSONString(manualBatch));
        //女娲二期灰度客户无需判断结算状态
        boolean reachStatus;
        if (wmCustomerGrayServiceAdapter.isGrayCustomer(manualBatch.getCustomerId())){
            reachStatus = MANUALBATCH_REACHED_STATUS.contains(manualBatch.getC1contractStatus()) &&
                            MANUALBATCH_REACHED_STATUS.contains(manualBatch.getDeliveryStatus());
        } else {
            reachStatus = MANUALBATCH_REACHED_STATUS.contains(manualBatch.getC1contractStatus()) &&
                            MANUALBATCH_REACHED_STATUS.contains(manualBatch.getSettleStatus()) &&
                            MANUALBATCH_REACHED_STATUS.contains(manualBatch.getDeliveryStatus());
        }
        return reachStatus;
    }

    public boolean hasSwitchingWmPoi(int customerId) {
        return wmSettleSwitchService.getSwitchingWmPoiIdList(customerId,true).size() > 0;
    }
}
