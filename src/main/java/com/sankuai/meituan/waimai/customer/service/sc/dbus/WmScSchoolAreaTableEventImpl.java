package com.sankuai.meituan.waimai.customer.service.sc.dbus;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.meituan.dbus.common.DbusUtils;
import com.meituan.dbus.common.StaticUtils;
import com.meituan.dbus.thriftV2.utils.ThriftV2Utils;
import com.sankuai.meituan.waimai.customer.constant.YesOrNoEnum;
import com.sankuai.meituan.waimai.customer.constant.sc.WmScTableColumnConstants;
import com.sankuai.meituan.waimai.customer.constant.sc.WmScTableDbusEnum;
import com.sankuai.meituan.waimai.customer.domain.sc.WmScSchoolAreaDO;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.WmScAreaTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.WmScUpdateTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.sc.WmAreaUpdateInfo;
import com.sankuai.meituan.waimai.customer.service.sc.area.WmScAreaService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021/8/31
 */
@Service
@Slf4j
public class WmScSchoolAreaTableEventImpl implements ITableEvent {

    @Autowired
    private WmScAreaService wmScAreaService;

    @Override
    public WmScTableDbusEnum getTable() {
        return WmScTableDbusEnum.TABLE_WM_SC_SCHOOL_AREA;
    }

    @Override
    public String handleUpdate(TableEvent tableEvent) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.service.sc.dbus.WmScSchoolAreaTableEventImpl.handleUpdate(com.sankuai.meituan.waimai.customer.service.sc.dbus.TableEvent)");
        log.info("监听学校范围表变更handleUpdate::tableEvent = {}", JSON.toJSONString(tableEvent));
        if (!checkParamWhenHandleUpdate(tableEvent)) {
            return StaticUtils.ok;
        }
        DbusUtils utils = ThriftV2Utils.newThriftV2InstanceForUpdate(tableEvent.getMetaJsonData(), tableEvent.getDataMapJson(), tableEvent.getDiffJson());
        refreshRtreeWhenHandleUpdate(utils);
        return StaticUtils.ok;
    }

    @Override
    public String handleInsert(TableEvent tableEvent) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_1", "com.sankuai.meituan.waimai.customer.service.sc.dbus.WmScSchoolAreaTableEventImpl.handleInsert(com.sankuai.meituan.waimai.customer.service.sc.dbus.TableEvent)");
        log.info("监听学校范围表变更handleInsert::tableEvent = {}", JSON.toJSONString(tableEvent));
        if (!checkParamWhenHandleInsert(tableEvent)) {
            return StaticUtils.ok;
        }
        DbusUtils utils = ThriftV2Utils.newThriftV2InstanceForInsert(tableEvent.getMetaJsonData(), tableEvent.getDataMapJson());
        refreshRtreeWhenHandleInsert(utils);
        return StaticUtils.ok;
    }

    @Override
    public String handleDelete(TableEvent tableEvent) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.service.sc.dbus.WmScSchoolAreaTableEventImpl.handleDelete(com.sankuai.meituan.waimai.customer.service.sc.dbus.TableEvent)");
        // 无物理删除，不会走到这里
        return StaticUtils.ok;
    }


    private WmScSchoolAreaDO transToBo(DbusUtils utils) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.service.sc.dbus.WmScSchoolAreaTableEventImpl.transToBo(com.meituan.dbus.common.DbusUtils)");
        Map<String, Object> aftMap = utils.getAftMap();
        String jsonString = JSON.toJSONString(aftMap);
        return JSON.parseObject(jsonString, WmScSchoolAreaDO.class);
    }

    /**
     * 学校范围变更时刷新学校范围rtree
     *
     * @param utils
     */
    private void refreshRtreeWhenHandleUpdate(DbusUtils utils) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_2", "com.sankuai.meituan.waimai.customer.service.sc.dbus.WmScSchoolAreaTableEventImpl.refreshRtreeWhenHandleUpdate(com.meituan.dbus.common.DbusUtils)");
        log.info("[refreshRtreeWhenHandleUpdate] utils = {}", JSONObject.toJSONString(utils));
        WmScSchoolAreaDO bean = transToBo(utils);
        if (bean == null) {
            return;
        }
        WmAreaUpdateInfo info = new WmAreaUpdateInfo();
        info.setId(bean.getId().intValue());
        info.setArea(bean.getArea());
        info.setAreaType(WmScAreaTypeEnum.SCHOOL_AREA.getCode());
        if (YesOrNoEnum.getEnumPreMap().get(bean.getValid()) == YesOrNoEnum.NO) {
            // 删除
            info.setUpdateType(WmScUpdateTypeEnum.DELETE.getCode());
        } else {
            Map<String, Object> diffMap = utils.getDiffMap();
            if (MapUtils.isEmpty(diffMap)
                    || !diffMap.containsKey(WmScTableColumnConstants.WmScSchoolAreaTable.TABLE_COLUMN_AREA)) {
                // 修改，判断范围是否发生了变化，如果未发生变化则不更新范围的rtree
                return;
            }
            info.setUpdateType(WmScUpdateTypeEnum.UPDATE.getCode());
        }
        log.info("更新学校范围的rtree，refreshRtreeWhenHandleUpdate::info = {}", JSON.toJSONString(info));
        wmScAreaService.refreshV2(info);
    }

    /**
     * 新增学校范围时刷新范围rtree处理
     *
     * @param utils
     */
    private void refreshRtreeWhenHandleInsert(DbusUtils utils) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_2", "com.sankuai.meituan.waimai.customer.service.sc.dbus.WmScSchoolAreaTableEventImpl.refreshRtreeWhenHandleInsert(com.meituan.dbus.common.DbusUtils)");
        log.info("[refreshRtreeWhenHandleInsert] utils = {}", JSONObject.toJSONString(utils));
        WmScSchoolAreaDO bean = transToBo(utils);
        if (bean == null) {
            return;
        }
        // 新增,如果未录入学校范围则不新范围的rtree
        if (StringUtils.isBlank(bean.getArea())) {
            return;
        }
        WmAreaUpdateInfo info = new WmAreaUpdateInfo();
        info.setId(bean.getId().intValue());
        info.setArea(bean.getArea());
        info.setUpdateType(WmScUpdateTypeEnum.ADD.getCode());
        info.setAreaType(WmScAreaTypeEnum.SCHOOL_AREA.getCode());
        log.info("更新学校范围的rtree，refreshRtreeWhenHandleInsert::info = {}", JSON.toJSONString(info));
        wmScAreaService.refreshV2(info);
    }
}
