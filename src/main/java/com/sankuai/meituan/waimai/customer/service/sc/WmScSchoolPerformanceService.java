package com.sankuai.meituan.waimai.customer.service.sc;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.sankuai.meituan.waimai.customer.constant.sc.WmScGrayPlatformConstant;
import com.sankuai.meituan.waimai.customer.dao.sc.WmScSchoolPerformanceUnitMapper;
import com.sankuai.meituan.waimai.customer.dao.sc.WmScSchoolPerformanceMapper;
import com.sankuai.meituan.waimai.customer.domain.sc.WmScSchoolPerformanceDO;
import com.sankuai.meituan.waimai.customer.domain.sc.WmScSchoolPerformanceLogBO;
import com.sankuai.meituan.waimai.customer.domain.sc.WmScSchoolPerformanceUnitDO;
import com.sankuai.meituan.waimai.customer.util.WmScTransUtil;
import com.sankuai.meituan.waimai.e.graycenter.sdk.GrayConfigClient;
import com.sankuai.meituan.waimai.e.graycenter.sdk.domain.GrayParams;
import com.sankuai.meituan.waimai.e.graycenter.sdk.exception.InvalidParamGrayException;
import com.sankuai.meituan.waimai.e.graycenter.sdk.exception.KeyNotExistGrayException;
import com.sankuai.meituan.waimai.e.graycenter.sdk.exception.UnexpectedGrayException;
import com.sankuai.meituan.waimai.thrift.bo.WmCustomerDiffCellBo;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.WmScCodeConstants;
import com.sankuai.meituan.waimai.thrift.customer.domain.sc.WmScSchoolPerformanceDTO;
import com.sankuai.meituan.waimai.thrift.customer.domain.sc.WmScSchoolPerformanceSaveDTO;
import com.sankuai.meituan.waimai.thrift.customer.domain.sc.WmScSchoolPerformanceUnitDTO;
import com.sankuai.meituan.waimai.thrift.customer.domain.sc.WmScSchoolPerformanceUnitSaveDTO;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import com.sankuai.meituan.waimai.thrift.exception.WmSchCantException;
import com.sankuai.meituan.waimai.thrift.util.DiffUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.thrift.TException;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

import static com.sankuai.meituan.waimai.customer.constant.ScFieldConstant.schoolPerformanceFiled;
import static com.sankuai.meituan.waimai.customer.constant.ScFieldConstant.schoolPerformanceUnitFiled;
import static com.sankuai.meituan.waimai.thrift.customer.constant.sc.WmScCodeConstants.BIZ_PARA_ERROR;

/**
 * 学校履约管控信息处理逻辑V2
 * <AUTHOR>
 * @date 2024/06/06
 * @email <EMAIL>
 */
@Slf4j
@Service
public class WmScSchoolPerformanceService {

    @Autowired
    private WmScSchoolPerformanceMapper wmScSchoolPerformanceMapper;
    @Autowired
    private WmScSchoolPerformanceUnitMapper wmScSchoolPerformanceUnitMapper;

    @Autowired
    private WmScLogSchoolInfoService wmScLogSchoolInfoService;

    /**
     * 查询学校的履约管控信息
     * @param schoolPrimaryId 学校主键ID
     * @return 履约管控信息
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     * @throws TException org.apache.thrift.TException
     */
    public WmScSchoolPerformanceDTO getSchoolPerformanceInfo(Integer schoolPrimaryId) throws WmSchCantException, TException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.sc.WmScSchoolPerformanceService.getSchoolPerformanceInfo(java.lang.Integer)");
        log.info("[WmScSchoolPerformanceService.getSchoolPerformanceInfo] input param: schoolPrimaryId = {}", schoolPrimaryId);
        if (schoolPrimaryId == null || schoolPrimaryId <= 0) {
            log.error("[WmScSchoolPlatformService.getSchoolPlatformList] schoolPrimaryId error. schoolPrimaryId = {}", schoolPrimaryId);
            throw new WmSchCantException(WmScCodeConstants.BIZ_PARA_ERROR, "学校主键ID为空");
        }

        WmScSchoolPerformanceDO wmScSchoolPerformanceDO = wmScSchoolPerformanceMapper.selectBySchoolPrimaryId(schoolPrimaryId);
        if (wmScSchoolPerformanceDO == null) {
            return null;
        }
        List<WmScSchoolPerformanceUnitDO> wmScSchoolPerformanceUnitDOList = wmScSchoolPerformanceUnitMapper.selectByPerformancePrimaryId(wmScSchoolPerformanceDO.getId());
        wmScSchoolPerformanceDO.setWmScSchoolPerformanceUnitDOList(wmScSchoolPerformanceUnitDOList);
        return WmScTransUtil.transSchoolPerformanceDOToDTO(wmScSchoolPerformanceDO);
    }

    /**
     * 保存学校履约管控信息
     * @param wmScSchoolPerformanceSaveDTO wmScSchoolPerformanceSaveDTO
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     * @throws TException org.apache.thrift.TException
     */
    public void saveSchoolPerformanceInfo(WmScSchoolPerformanceSaveDTO wmScSchoolPerformanceSaveDTO) throws WmSchCantException, TException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.sc.WmScSchoolPerformanceService.saveSchoolPerformanceInfo(com.sankuai.meituan.waimai.thrift.customer.domain.sc.WmScSchoolPerformanceSaveDTO)");
        log.info("[WmScSchoolPerformanceService.saveSchoolPerformanceInfo] input param: wmScSchoolPerformanceSaveDTO = {}", JSONObject.toJSONString(wmScSchoolPerformanceSaveDTO));
        if (wmScSchoolPerformanceSaveDTO == null
                || wmScSchoolPerformanceSaveDTO.getSchoolPrimaryId() == null
                || wmScSchoolPerformanceSaveDTO.getSchoolPrimaryId() <= 0) {
            log.error("[WmScSchoolPerformanceService.saveSchoolPerformanceInfo] param error. wmScSchoolPerformanceSaveDTO = {}", JSONObject.toJSONString(wmScSchoolPerformanceSaveDTO));
            throw new WmSchCantException(WmScCodeConstants.BIZ_PARA_ERROR, "学校履约管控信息参数异常");
        }

        // 若履约管控信息主键ID为空, 则执行新增操作; 否则执行修改操作
        if (wmScSchoolPerformanceSaveDTO.getId() == null) {
            insertSchoolPerformance(wmScSchoolPerformanceSaveDTO);
        } else {
            WmScSchoolPerformanceDO wmScSchoolPerformanceDO = wmScSchoolPerformanceMapper.selectByPrimaryKey(wmScSchoolPerformanceSaveDTO.getId());
            if (wmScSchoolPerformanceDO == null) {
                log.error("[WmScSchoolPerformanceService.saveSchoolPerformanceInfo] select error. primaryKey = {}", wmScSchoolPerformanceSaveDTO.getId());
                throw new WmSchCantException(BIZ_PARA_ERROR, "查询履约管控信息参数异常");
            }
            List<WmScSchoolPerformanceUnitDO> wmScSchoolPerformanceUnitDOList = wmScSchoolPerformanceUnitMapper.selectByPerformancePrimaryId(wmScSchoolPerformanceDO.getId());
            if (CollectionUtils.isNotEmpty(wmScSchoolPerformanceUnitDOList)) {
                wmScSchoolPerformanceDO.setWmScSchoolPerformanceUnitDOList(wmScSchoolPerformanceUnitDOList);
            }
            updateSchoolPerformance(wmScSchoolPerformanceSaveDTO, wmScSchoolPerformanceDO);
        }
    }

    /**
     * 更新学校履约管控信息
     * @param wmScSchoolPerformanceSaveDTO wmScSchoolPerformanceSaveDTO
     * @param wmScSchoolPerformanceDO wmScSchoolPerformanceDO
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     * @throws TException org.apache.thrift.TException
     */
    private void updateSchoolPerformance(WmScSchoolPerformanceSaveDTO wmScSchoolPerformanceSaveDTO, WmScSchoolPerformanceDO wmScSchoolPerformanceDO) throws WmSchCantException, TException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.service.sc.WmScSchoolPerformanceService.updateSchoolPerformance(WmScSchoolPerformanceSaveDTO,WmScSchoolPerformanceDO)");
        log.info("[WmScSchoolPerformanceService.updateSchoolPerformance] input param: wmScSchoolPerformanceSaveDTO = {}, wmScSchoolPerformanceDO = {}",
                JSONObject.toJSONString(wmScSchoolPerformanceSaveDTO), JSONObject.toJSONString(wmScSchoolPerformanceDO));
        if (wmScSchoolPerformanceSaveDTO == null || wmScSchoolPerformanceDO == null) {
            log.error("[WmScSchoolPerformanceService.updateSchoolPerformance] input error. wmScSchoolPerformanceSaveDTO = {}, wmScSchoolPerformanceDO = {}",
                    JSONObject.toJSONString(wmScSchoolPerformanceSaveDTO), JSONObject.toJSONString(wmScSchoolPerformanceDO));
            throw new WmSchCantException(BIZ_PARA_ERROR, "请求参数为空");
        }

        // 校验保存信息和当前信息是否一致
        if (isSchoolPerformanceInfoSame(wmScSchoolPerformanceSaveDTO, wmScSchoolPerformanceDO)) {
            log.info("[WmScSchoolPerformanceService.updateSchoolPerformance] the same info, return now. wmScSchoolPerformanceSaveDTO = {}, wmScSchoolPerformanceDO = {}",
                    JSONObject.toJSONString(wmScSchoolPerformanceSaveDTO), JSONObject.toJSONString(wmScSchoolPerformanceDO));
            return;
        }

        WmScSchoolPerformanceDO wmScSchoolPerformanceDOUpdate = new WmScSchoolPerformanceDO();
        wmScSchoolPerformanceDOUpdate.setId(wmScSchoolPerformanceSaveDTO.getId());
        composeSchoolPerformanceUpdate(wmScSchoolPerformanceSaveDTO, wmScSchoolPerformanceDO, wmScSchoolPerformanceDOUpdate);

        // 执行更新操作
        log.info("[WmScSchoolPerformanceService.updateSchoolPerformance] updateByPrimaryKeySelective wmScSchoolPerformanceDOUpdate = {}",
                JSONObject.toJSONString(wmScSchoolPerformanceDOUpdate));
        int result = wmScSchoolPerformanceMapper.updateByPrimaryKeySelective(wmScSchoolPerformanceDOUpdate);
        if (CollectionUtils.isNotEmpty(wmScSchoolPerformanceDO.getWmScSchoolPerformanceUnitDOList())) {
            log.info("[WmScSchoolPerformanceService.updateSchoolPerformance] batchInvalidByPrimaryKey wmScSchoolPerformanceUnitDOList = {}",
                    JSONObject.toJSONString(wmScSchoolPerformanceDO.getWmScSchoolPerformanceUnitDOList()));
            wmScSchoolPerformanceUnitMapper.batchInvalidByPrimaryKey(wmScSchoolPerformanceDO.getWmScSchoolPerformanceUnitDOList(), wmScSchoolPerformanceSaveDTO.getUserId().longValue());
        }

        WmScSchoolPerformanceDO wmScSchoolPerformanceDOInsert = WmScTransUtil.transWmScSchoolPerformanceSaveDTOToDO(wmScSchoolPerformanceSaveDTO);
        if (CollectionUtils.isNotEmpty(wmScSchoolPerformanceDOInsert.getWmScSchoolPerformanceUnitDOList())) {
            log.info("[WmScSchoolPerformanceService.updateSchoolPerformance] batchInsertSelective wmScSchoolPerformanceUnitDOList = {}",
                    JSONObject.toJSONString(wmScSchoolPerformanceDOInsert.getWmScSchoolPerformanceUnitDOList()));
            wmScSchoolPerformanceUnitMapper.batchInsertSelective(wmScSchoolPerformanceDOInsert.getWmScSchoolPerformanceUnitDOList());
        }

        if (result > 0) {
            try {
                // 记录到操作日志
                BeanUtils.copyProperties(wmScSchoolPerformanceDOInsert, wmScSchoolPerformanceDOUpdate);
                composeSchoolPerformanceUnitUpdate(wmScSchoolPerformanceSaveDTO, wmScSchoolPerformanceDO, wmScSchoolPerformanceDOUpdate);
                WmScSchoolPerformanceLogBO wmScSchoolPerformanceLogBO = new WmScSchoolPerformanceLogBO.Builder()
                        .userId(wmScSchoolPerformanceSaveDTO.getUserId())
                        .userName(wmScSchoolPerformanceSaveDTO.getUserName())
                        .wmScSchoolPerformanceDOBefore(wmScSchoolPerformanceDO)
                        .wmScSchoolPerformanceDOAfter(wmScSchoolPerformanceDOUpdate)
                        .build();
                wmScLogSchoolInfoService.recordSchoolPerformanceUpdateLog(wmScSchoolPerformanceLogBO);
            } catch (Exception e) {
                log.error("[WmScSchoolPerformanceService.updateSchoolPerformance] Record operation logs error, without business impact. wmScSchoolPerformanceDOUpdate = {}",
                        JSONObject.toJSONString(wmScSchoolPerformanceDOUpdate), e);
            }
        }
    }

    /**
     * 设置学校履约管控信息-校方态度板块DO
     * @param wmScSchoolPerformanceSaveDTO wmScSchoolPerformanceSaveDTO
     * @param wmScSchoolPerformanceDOBefore wmScSchoolPerformanceDOBefore
     * @param wmScSchoolPerformanceDOUpdate wmScSchoolPerformanceDOUpdate
     */
    private void composeSchoolPerformanceUpdate(WmScSchoolPerformanceSaveDTO wmScSchoolPerformanceSaveDTO,
                                                 WmScSchoolPerformanceDO wmScSchoolPerformanceDOBefore,
                                                 WmScSchoolPerformanceDO wmScSchoolPerformanceDOUpdate) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_1", "com.sankuai.meituan.waimai.customer.service.sc.WmScSchoolPerformanceService.composeSchoolPerformanceUpdate(WmScSchoolPerformanceSaveDTO,WmScSchoolPerformanceDO,WmScSchoolPerformanceDO)");
        // 校方是否允许配送进校
        if (!wmScSchoolPerformanceDOBefore.getSchoolAllowDelivery().equals(wmScSchoolPerformanceSaveDTO.getSchoolAllowDelivery())) {
            wmScSchoolPerformanceDOUpdate.setSchoolAllowDelivery(wmScSchoolPerformanceSaveDTO.getSchoolAllowDelivery());
        }
        // 校方是否允许配送进校其他信息
        if (!wmScSchoolPerformanceDOBefore.getSchoolAllowDeliveryInfo().equals(wmScSchoolPerformanceSaveDTO.getSchoolAllowDeliveryInfo())) {
            wmScSchoolPerformanceDOUpdate.setSchoolAllowDeliveryInfo(wmScSchoolPerformanceSaveDTO.getSchoolAllowDeliveryInfo());
        }
    }

    /**
     * 设置学校履约管控信息-履约信息板块DO
     *
     * @param wmScSchoolPerformanceSaveDTO  wmScSchoolPerformanceSaveDTO
     * @param wmScSchoolPerformanceDOBefore wmScSchoolPerformanceDOBefore
     * @param wmScSchoolPerformanceDOUpdate wmScSchoolPerformanceDOUpdate
     */
    private void composeSchoolPerformanceUnitUpdate(WmScSchoolPerformanceSaveDTO wmScSchoolPerformanceSaveDTO,
                                                WmScSchoolPerformanceDO wmScSchoolPerformanceDOBefore,
                                                WmScSchoolPerformanceDO wmScSchoolPerformanceDOUpdate) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.service.sc.WmScSchoolPerformanceService.composeSchoolPerformanceUnitUpdate(WmScSchoolPerformanceSaveDTO,WmScSchoolPerformanceDO,WmScSchoolPerformanceDO)");
        List<WmScSchoolPerformanceUnitDO> wmScSchoolPerformanceUnitDOBeforeList = wmScSchoolPerformanceDOBefore.getWmScSchoolPerformanceUnitDOList();
        List<WmScSchoolPerformanceUnitDO> wmScSchoolPerformanceUnitDOUpdateList = wmScSchoolPerformanceDOUpdate.getWmScSchoolPerformanceUnitDOList();

        // 对齐修改前后的履约管控信息，缺失的数据用初始化的对象替代
        if (CollectionUtils.isEmpty(wmScSchoolPerformanceUnitDOUpdateList)) {
            return;
        }
        if (CollectionUtils.isEmpty(wmScSchoolPerformanceUnitDOBeforeList)) {
            for (WmScSchoolPerformanceUnitDO wmScSchoolPerformanceUnitDO : wmScSchoolPerformanceUnitDOUpdateList) {
                wmScSchoolPerformanceUnitDOBeforeList.add(new WmScSchoolPerformanceUnitDO());
            }
            return;
        }
        for (int i = 0; i < wmScSchoolPerformanceUnitDOBeforeList.size(); i++) {
            if (i >= wmScSchoolPerformanceUnitDOUpdateList.size()) {
                wmScSchoolPerformanceUnitDOUpdateList.add(new WmScSchoolPerformanceUnitDO());
            }
            if (!wmScSchoolPerformanceUnitDOBeforeList.get(i).getId().equals(wmScSchoolPerformanceUnitDOUpdateList.get(i).getId())) {
                wmScSchoolPerformanceUnitDOUpdateList.add(i, new WmScSchoolPerformanceUnitDO());
            }
        }
        for (int i = wmScSchoolPerformanceUnitDOBeforeList.size(); i < wmScSchoolPerformanceUnitDOUpdateList.size(); i++) {
            wmScSchoolPerformanceUnitDOBeforeList.add(new WmScSchoolPerformanceUnitDO());
        }

    }

    private Boolean isSchoolPerformanceInfoSame(WmScSchoolPerformanceSaveDTO wmScSchoolPerformanceSaveDTO, WmScSchoolPerformanceDO wmScSchoolPerformanceDO) throws WmSchCantException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_2", "com.sankuai.meituan.waimai.customer.service.sc.WmScSchoolPerformanceService.isSchoolPerformanceInfoSame(WmScSchoolPerformanceSaveDTO,WmScSchoolPerformanceDO)");
        if (wmScSchoolPerformanceSaveDTO == null || wmScSchoolPerformanceDO == null) {
            return false;
        }

        // 将wmScSchoolPerformanceSaveDTO转为DO
        WmScSchoolPerformanceDO wmScSchoolPerformanceDOUpdate = WmScTransUtil.transWmScSchoolPerformanceSaveDTOToDO(wmScSchoolPerformanceSaveDTO);

        List<WmScSchoolPerformanceUnitDO> wmScSchoolPerformanceUnitDOUpdateList = wmScSchoolPerformanceDOUpdate.getWmScSchoolPerformanceUnitDOList();
        List<WmScSchoolPerformanceUnitDO> wmScSchoolPerformanceUnitDOList = wmScSchoolPerformanceDO.getWmScSchoolPerformanceUnitDOList();
        if (CollectionUtils.isEmpty(wmScSchoolPerformanceUnitDOUpdateList) || CollectionUtils.isEmpty(wmScSchoolPerformanceUnitDOList)) {
            return false;
        }
        int doUpdateSize = wmScSchoolPerformanceUnitDOUpdateList.size();
        int doSize = wmScSchoolPerformanceUnitDOList.size();
        if (doUpdateSize != doSize) {
            return false;
        }

        List<WmCustomerDiffCellBo> diffList = Lists.newArrayList();
        try {
            diffList = DiffUtil.compare(wmScSchoolPerformanceDO, wmScSchoolPerformanceDOUpdate, schoolPerformanceFiled);
        } catch (WmCustomerException e) {
            log.error("[WmScSchoolPerformanceService.isSchoolPerformanceInfoSame] diff is error. wmScSchoolPerformanceDO = {},wmScSchoolPerformanceDOUpdate = {} "
                    , JSONObject.toJSONString(wmScSchoolPerformanceDO), JSONObject.toJSONString(wmScSchoolPerformanceDOUpdate), e);
            throw new WmSchCantException(e.getCode(), e.getMsg());
        }

        if (CollectionUtils.isNotEmpty(diffList)) {
            return false;
        }

        for (int i = 0; i < doSize; i++) {
            boolean isSame = wmScSchoolPerformanceUnitDOList.get(i).getId().equals(wmScSchoolPerformanceUnitDOUpdateList.get(i).getId())
                    && wmScSchoolPerformanceUnitDOList.get(i).getPerformancePrimaryId().equals(wmScSchoolPerformanceUnitDOUpdateList.get(i).getPerformancePrimaryId())
                    && wmScSchoolPerformanceUnitDOList.get(i).getSchoolInDeliveryType().equals(wmScSchoolPerformanceUnitDOUpdateList.get(i).getSchoolInDeliveryType())
                    && wmScSchoolPerformanceUnitDOList.get(i).getPoiSelfDeliveryType().equals(wmScSchoolPerformanceUnitDOUpdateList.get(i).getPoiSelfDeliveryType())
                    && wmScSchoolPerformanceUnitDOList.get(i).getPoiSelfDeliveryInfo().equals(wmScSchoolPerformanceUnitDOUpdateList.get(i).getPoiSelfDeliveryInfo())
                    && wmScSchoolPerformanceUnitDOList.get(i).getDeliverySpecificLocation().equals(wmScSchoolPerformanceUnitDOUpdateList.get(i).getDeliverySpecificLocation())
                    && wmScSchoolPerformanceUnitDOList.get(i).getDeliveryNotGateReason().equals(wmScSchoolPerformanceUnitDOUpdateList.get(i).getDeliveryNotGateReason())
                    && wmScSchoolPerformanceUnitDOList.get(i).getDeliveryNotGateInfo().equals(wmScSchoolPerformanceUnitDOUpdateList.get(i).getDeliveryNotGateInfo())
                    && wmScSchoolPerformanceUnitDOList.get(i).getDeliveryNotEnterReason().equals(wmScSchoolPerformanceUnitDOUpdateList.get(i).getDeliveryNotEnterReason())
                    && wmScSchoolPerformanceUnitDOList.get(i).getDeliveryNotEnterInfo().equals(wmScSchoolPerformanceUnitDOUpdateList.get(i).getDeliveryNotEnterInfo())
                    && wmScSchoolPerformanceUnitDOList.get(i).getDeliveryEnterReason().equals(wmScSchoolPerformanceUnitDOUpdateList.get(i).getDeliveryEnterReason())
                    && wmScSchoolPerformanceUnitDOList.get(i).getDeliveryEnterInfo().equals(wmScSchoolPerformanceUnitDOUpdateList.get(i).getDeliveryEnterInfo())
                    && wmScSchoolPerformanceUnitDOList.get(i).getDeliveryUpstairsReason().equals(wmScSchoolPerformanceUnitDOUpdateList.get(i).getDeliveryUpstairsReason())
                    && wmScSchoolPerformanceUnitDOList.get(i).getDeliveryUpstairsInfo().equals(wmScSchoolPerformanceUnitDOUpdateList.get(i).getDeliveryUpstairsInfo());
            if (!isSame) {
                return false;
            }
        }

        return true;
    }

    /**
     * 新增学校履约管控信息
     * @param wmScSchoolPerformanceSaveDTO wmScSchoolPerformanceSaveDTO
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     * @throws TException org.apache.thrift.TException
     */
    public void insertSchoolPerformance(WmScSchoolPerformanceSaveDTO wmScSchoolPerformanceSaveDTO) throws WmSchCantException, TException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.service.sc.WmScSchoolPerformanceService.insertSchoolPerformance(com.sankuai.meituan.waimai.thrift.customer.domain.sc.WmScSchoolPerformanceSaveDTO)");
        log.info("[WmScSchoolPerformanceService.insertSchoolPerformance] input param: wmScSchoolPerformanceSaveDTO = {}", JSONObject.toJSONString(wmScSchoolPerformanceSaveDTO));
        if (wmScSchoolPerformanceSaveDTO == null
                || wmScSchoolPerformanceSaveDTO.getSchoolPrimaryId() == null
                || wmScSchoolPerformanceSaveDTO.getSchoolPrimaryId() <= 0) {
            log.error("[WmScSchoolPlatformService.insertSchoolPerformance] input error. wmScSchoolPerformanceSaveDTO = {}", JSONObject.toJSONString(wmScSchoolPerformanceSaveDTO));
            throw new WmSchCantException(BIZ_PARA_ERROR, "请求参数为空");
        }

        WmScSchoolPerformanceDO wmScSchoolPerformanceDOInsert = WmScTransUtil.transWmScSchoolPerformanceSaveDTOToDO(wmScSchoolPerformanceSaveDTO);
        // 插入到履约管控信息主表wm_school_performance一条数据
        log.info("[WmScSchoolPerformanceService.insertSchoolPerformance] insertSelective. wmScSchoolPerformanceDOInsert = {}"
                , JSONObject.toJSONString(wmScSchoolPerformanceDOInsert));
        int result = wmScSchoolPerformanceMapper.insertSelective(wmScSchoolPerformanceDOInsert);
        // 赋值performancePrimaryId并插入到履约管控信息单元表wm_school_performance_unit N条数据（N=履约管控信息单元数）
        for (WmScSchoolPerformanceUnitDO wmScSchoolPerformanceUnitDO : wmScSchoolPerformanceDOInsert.getWmScSchoolPerformanceUnitDOList()) {
            wmScSchoolPerformanceUnitDO.setPerformancePrimaryId(wmScSchoolPerformanceDOInsert.getId());
        }
        log.info("[WmScSchoolPerformanceService.insertSchoolPerformance] batchInsertSelective. wmScSchoolPerformanceUnitDOList = {}"
                , JSONObject.toJSONString(wmScSchoolPerformanceDOInsert.getWmScSchoolPerformanceUnitDOList()));
        int unitResult = wmScSchoolPerformanceUnitMapper.batchInsertSelective(wmScSchoolPerformanceDOInsert.getWmScSchoolPerformanceUnitDOList());
        // 记录日志
        if (result > 0 && unitResult > 0) {
            try {
                WmScSchoolPerformanceLogBO wmScSchoolPerformanceLogBO = new WmScSchoolPerformanceLogBO.Builder()
                        .wmScSchoolPerformanceDOInsert(wmScSchoolPerformanceDOInsert)
                        .userId(wmScSchoolPerformanceSaveDTO.getUserId())
                        .userName(wmScSchoolPerformanceSaveDTO.getUserName())
                        .build();
                wmScLogSchoolInfoService.recordSchoolPerformanceInsertLog(wmScSchoolPerformanceLogBO);
            } catch (Exception e) {
                log.error("[WmScSchoolPlatformService.insertSchoolPerformance] Record operation logs error, without business impact. wmScSchoolPerformanceDOInsert = {}",
                        JSONObject.toJSONString(wmScSchoolPerformanceDOInsert), e);
            }
        }
        log.info("[WmScSchoolPerformanceService.insertSchoolPerformance] insert success. wmScSchoolPerformanceDOInsert = {}", JSONObject.toJSONString(wmScSchoolPerformanceDOInsert));
    }

}