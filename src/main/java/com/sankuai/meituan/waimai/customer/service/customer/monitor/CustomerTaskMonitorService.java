package com.sankuai.meituan.waimai.customer.service.customer.monitor;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.sankuai.meituan.waimai.customer.config.MccCustomerConfig;
import com.sankuai.meituan.waimai.customer.dao.WmCustomerPoiDBMapper;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerPoiDB;
import com.sankuai.meituan.waimai.customer.domain.task.WmCustomerTask;
import com.sankuai.meituan.waimai.customer.service.customer.task.CustomerTaskService;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerConstants;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerRelationStatusEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.customer.task.CustomerTaskStatusEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.customer.task.CustomerTaskTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.monitor.MonitorCustomerPoiRelDTO;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.monitor.MonitorCustomerTaskDTO;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 20230731
 * @desc 客户任务模型数据正确性监控服务
 */
@Service
@Slf4j
public class CustomerTaskMonitorService {

    @Autowired
    private CustomerTaskService customerTaskService;

    @Autowired
    private WmCustomerPoiDBMapper wmCustomerPoiDBMapper;

    /**
     * 新增绑定记录的校验状态
     */
    private static List<Integer> insertPoiRelState = Lists.newArrayList(4, 5);


    /**
     * 客户门店关系变更判断是否存在合法任务数据
     *
     * @param monitorCustomerPoiRelDTO
     * @return
     */
    public String checkTaskOnCustomerRelChange(MonitorCustomerPoiRelDTO monitorCustomerPoiRelDTO) {
        log.info("checkTaskOnCustomerRelChange,客户门店关系表发生变更，对关联任务数据进行监控，monitorCustomerPoiRelDTO={}",
                JSON.toJSONString(monitorCustomerPoiRelDTO));
        String operateType = monitorCustomerPoiRelDTO.getOperateType();
        String result = null;
        switch (operateType) {
            case "INSERT":
                result = checkPoiRelInsertData(monitorCustomerPoiRelDTO);
                break;
            case "UPDATE":
                result = checkPoiRelUpdateData(monitorCustomerPoiRelDTO);
                break;
            case "DELETE":
                result = checkPoiRelDeleteData(monitorCustomerPoiRelDTO);
                break;
            default:
                return null;
        }
        return result;
    }

    /**
     * 客户任务记录表变更对客户任务数据进行监控
     *
     * @param monitorCustomerTaskDTO
     * @return
     */
    public String checkTaskOnCustomerTaskChange(MonitorCustomerTaskDTO monitorCustomerTaskDTO) {
        log.info("checkTaskOnCustomerTaskChange,客户任务表发生变更，对任务数据进行监控，monitorCustomerTaskDTO={}",
                JSON.toJSONString(monitorCustomerTaskDTO));
        String operateType = monitorCustomerTaskDTO.getOperateType();
        String result = null;
        switch (operateType) {
            case "INSERT":
                result = checkInsertTaskData(monitorCustomerTaskDTO);
                break;
            case "UPDATE":
                result = checkUpdateTaskData(monitorCustomerTaskDTO);
                break;
            default:
                return null;
        }
        return result;
    }

    /**
     * 客户任务新增场景对任务数据正确性做检验
     *
     * @param monitorCustomerTaskDTO
     * @return
     */
    private String checkInsertTaskData(MonitorCustomerTaskDTO monitorCustomerTaskDTO) {
        Integer customerId = monitorCustomerTaskDTO.getCustomerId();
        Integer taskType = monitorCustomerTaskDTO.getTaskType();
        Long wmPoiId = monitorCustomerTaskDTO.getBizId();
        Integer taskId = monitorCustomerTaskDTO.getId();
        try {
            Integer count = customerTaskService.countByCustomerIdAndBizIdAndTaskType(customerId, wmPoiId, taskType);
            if (MccCustomerConfig.getCheckTaskInsertExistSwitch()
                    && (count == null || count < 1)) {
                WmCustomerTask task = customerTaskService.getTaskById(taskId.longValue());
                //查询到任务且当前状态是成功则过滤
                if (task != null
                        && task.getStatus().equals(CustomerTaskStatusEnum.SUC.getCode())) {
                    return null;
                }
                return String.format("新增任务记录监控发生异常,未查询到处理中任务,customerId=%s,wmPoiId=%s,count=%s",
                        customerId, wmPoiId, count);
            }
            if (count > 1) {
                return String.format("新增任务记录监控发生异常,当前存在处理中记录超过1条,customerId=%s,wmPoiId=%s,count=%s",
                        customerId, wmPoiId, count);
            }
        } catch (Exception e) {
            log.error("checkInsertTaskData,新增客户任务记录监控客户发生异常,customerId={},wmPoiId={}"
                    , customerId, wmPoiId, e);
        }
        return null;
    }

    /**
     * 客户任务记录修改场景对任务数据做正确性校验
     *
     * @param monitorCustomerTaskDTO
     * @return
     */
    private String checkUpdateTaskData(MonitorCustomerTaskDTO monitorCustomerTaskDTO) {
        Integer signTaskId = monitorCustomerTaskDTO.getSignTaskId();
        Integer oldSignTaskId = monitorCustomerTaskDTO.getOldSignTaskId();
        Integer state = monitorCustomerTaskDTO.getStatus();
        Integer oldState = monitorCustomerTaskDTO.getOldStatus();
        try {
            //签约任务ID更新
            if (!signTaskId.equals(oldSignTaskId) && signTaskId > 0) {
                return checkOnSignTaskIdChange(monitorCustomerTaskDTO);
            }
            //状态变更
            if (!state.equals(oldState) && CustomerTaskStatusEnum.SUC.getCode() == state) {
                return checkUpdateTask2Suc(monitorCustomerTaskDTO);
            }
            //状态更新为失败或取消
            if (!state.equals(oldState)
                    && (CustomerTaskStatusEnum.FAIL.getCode() == state
                    || CustomerTaskStatusEnum.CANCEL.getCode() == state)) {
                return checkUpdateTask2FailOrCancel(monitorCustomerTaskDTO);
            }
        } catch (Exception e) {
            log.error("checkUpdateTaskData,客户任务记录发生变更监控数据发生异常,monitorCustomerTaskDTO={}",
                    JSON.toJSONString(monitorCustomerTaskDTO), e);
        }
        return null;
    }

    /**
     * 更新任务状态为成功
     *
     * @param monitorCustomerTaskDTO
     * @return
     */
    private String checkUpdateTask2Suc(MonitorCustomerTaskDTO monitorCustomerTaskDTO) {
        Integer taskType = monitorCustomerTaskDTO.getTaskType();
        Integer customerId = monitorCustomerTaskDTO.getCustomerId();
        Long wmPoiId = monitorCustomerTaskDTO.getBizId();
        Integer taskId = monitorCustomerTaskDTO.getId();

        WmCustomerPoiDB wmCustomerPoiDB = wmCustomerPoiDBMapper.getValidRelByCustomerIdAndWmPoiId(customerId, wmPoiId);
        if (CustomerTaskTypeEnum.CUSTOMER_BIND_POI.getCode() == taskType
                && wmCustomerPoiDB != null
                && wmCustomerPoiDB.getRelationStatus() != CustomerRelationStatusEnum.BIND.getCode()) {
            if (!wmCustomerPoiDB.getBizTaskId().equals(taskId)) {
                log.info("checkUpdateTask2Suc,客户门店关系当前关联的任务ID已发生变更,customerId={},wmPoiId={},taskId={}", customerId, wmPoiId, taskId);
                return null;
            }
            return String.format("客户与门店关联关系当前不是已绑定状态,customerId=%s,wmPoiId=%s,taskId=%s,relationStatus=%s",
                    customerId, wmPoiId, monitorCustomerTaskDTO.getId(), wmCustomerPoiDB.getRelationStatus());
        }

        if (CustomerTaskTypeEnum.CUSTOMER_UNBIND_POI.getCode() == taskType
                && wmCustomerPoiDB != null) {
            if (!wmCustomerPoiDB.getBizTaskId().equals(taskId)) {
                log.info("checkUpdateTask2Suc,客户门店关系当前关联的任务ID已发生变更,customerId={},wmPoiId={},taskId={}", customerId, wmPoiId, taskId);
                return null;
            }
            return String.format("客户与门店关联关系当前不是解绑状态,customerId=%s,wmPoiId=%s,taskId=%s,relationStatus=%s",
                    customerId, wmPoiId, monitorCustomerTaskDTO.getId(),
                    CustomerRelationStatusEnum.of(wmCustomerPoiDB.getRelationStatus()).getDesc());
        }
        return null;
    }

    /**
     * 更新任务状态到失败或者取消
     *
     * @param monitorCustomerTaskDTO
     * @return
     */
    private String checkUpdateTask2FailOrCancel(MonitorCustomerTaskDTO monitorCustomerTaskDTO) {

        Integer taskType = monitorCustomerTaskDTO.getTaskType();
        Integer customerId = monitorCustomerTaskDTO.getCustomerId();
        Long wmPoiId = monitorCustomerTaskDTO.getBizId();

        //绑定任务取消或失败，客户门店关系如果存在则数据存在问题
        WmCustomerPoiDB wmCustomerPoiDB = wmCustomerPoiDBMapper.getValidRelByCustomerIdAndWmPoiId(customerId, wmPoiId);
        if (CustomerTaskTypeEnum.CUSTOMER_BIND_POI.getCode() == taskType
                && wmCustomerPoiDB != null) {
            return String.format("客户门店绑定任务失败或取消，客户门店关系仍存在,customerId=%s,wmPoiId=%s,taskId=%s,relationStatus=%s",
                    customerId, wmPoiId, monitorCustomerTaskDTO.getId(), wmCustomerPoiDB.getRelationStatus());
        }
        //解绑任务取消或者失败，原客户门店关系不存在则数据不正确
        if (CustomerTaskTypeEnum.CUSTOMER_UNBIND_POI.getCode() == taskType
                && wmCustomerPoiDB == null
                && !checkPoiReleaseCancel(monitorCustomerTaskDTO.getId())) {
            return String.format("客户门店关系解绑任务失败或取消，客户门店关系不存在,customerId=%s,wmPoiId=%s,taskId=%s",
                    customerId, wmPoiId, monitorCustomerTaskDTO.getId());
        }

        return null;
    }

    /**
     * 签约任务ID更新校验
     *
     * @param monitorCustomerTaskDTO
     * @return
     */
    private String checkOnSignTaskIdChange(MonitorCustomerTaskDTO monitorCustomerTaskDTO) throws WmCustomerException {
        Integer signTaskId = monitorCustomerTaskDTO.getSignTaskId();
        Integer taskId = monitorCustomerTaskDTO.getId();
        //是否关联多个切换任务ID
        List<Integer> relBizTaskIdList = customerTaskService.listDoingBizTaskIdBySignTaskId(signTaskId);
        if (CollectionUtils.isNotEmpty(relBizTaskIdList) && relBizTaskIdList.size() > 1) {
            log.info("checkOnSignTaskIdChange,根据签约任务ID查询到多个处理中切换任务ID,taskId={},signTaskId={},relBizTaskIdList={}",
                    taskId, signTaskId, JSON.toJSONString(relBizTaskIdList));
            return String.format("当前签约任务ID查询到多个切换任务ID,signTaskId=%s,taskId=%s,relBizTaskIdList=%s", signTaskId, taskId, JSON.toJSONString(relBizTaskIdList));
        }
        //更新之前当前记录为无效
        if (CustomerConstants.UNVALID == monitorCustomerTaskDTO.getOldValid()) {
            log.info("checkOnSignTaskIdChange,签约任务ID更新之前记录的valid为无效,taskId={},signTaskId={}",
                    taskId, signTaskId);
            return String.format("签约任务ID更新之前记录的valid为无效,signTaskId=%s,taskId=%s", signTaskId, taskId);
        }
        //更新之前当前记录状态非处理中
        if (CustomerTaskStatusEnum.DOING.getCode() != monitorCustomerTaskDTO.getOldStatus()) {
            log.info("checkOnSignTaskIdChange,签约任务ID更新之前记录的状态不是处理中,taskId={},signTaskId={}",
                    taskId, signTaskId);
            return String.format("签约任务ID更新之前记录的状态不是处理中,signTaskId=%s,taskId=%s", signTaskId, taskId);
        }
        return null;
    }

    /**
     * 物理删除
     *
     * @param monitorCustomerPoiRelDTO
     * @return
     */
    private String checkPoiRelDeleteData(MonitorCustomerPoiRelDTO monitorCustomerPoiRelDTO) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.service.customer.monitor.CustomerTaskMonitorService.checkPoiRelDeleteData(MonitorCustomerPoiRelDTO)");
        if (MccCustomerConfig.getCheckCustomerPoiRelDeleteSwitch()) {
            return String.format("客户门店关系物理删除,customerId=%s,wmPoiId=%s,id=%s",
                    monitorCustomerPoiRelDTO.getCustomerId(), monitorCustomerPoiRelDTO.getWmPoiId(), monitorCustomerPoiRelDTO.getId());
        }
        return null;
    }


    /**
     * 校验新增
     *
     * @param monitorCustomerPoiRelDTO
     * @return
     */
    private String checkPoiRelInsertData(MonitorCustomerPoiRelDTO monitorCustomerPoiRelDTO) {

        Integer status = monitorCustomerPoiRelDTO.getStatus();
        Long wmPoiId = monitorCustomerPoiRelDTO.getWmPoiId();
        Integer customerId = monitorCustomerPoiRelDTO.getCustomerId();
        try {
            if (!insertPoiRelState.contains(status)) {
                log.info("checkInsertData,客户门店新增场景下state非关注状态,status={},customerId={},wmPoiId={}",
                        status, customerId, wmPoiId);
                return null;
            }
            //待发起确认，查询存在流程中任务，不存在需要告警
            if (MccCustomerConfig.getCheckPoiRelInsertExistTaskSwitch()
                    && status.equals(CustomerRelationStatusEnum.TO_APPLY_BIND.getCode())) {
                List<WmCustomerTask> taskList = customerTaskService.listBindingTaskByCustomerIdAndBizId(customerId, wmPoiId);
                if (CollectionUtils.isEmpty(taskList)) {
                    return String.format("新增客户门店关系，未查询到相关任务记录，customerId=%s,wmPoiId=%s",
                            customerId, wmPoiId);
                }
            }

        } catch (Exception e) {
            log.error("checkInsertData,新增客户门店关系监控客户任务数据发生异常,customerId={},wmPoiId={}"
                    , customerId, wmPoiId, e);
        }
        return null;
    }

    /**
     * 校验更新
     *
     * @param monitorCustomerPoiRelDTO
     * @return
     */
    private String checkPoiRelUpdateData(MonitorCustomerPoiRelDTO monitorCustomerPoiRelDTO) {
        Integer status = monitorCustomerPoiRelDTO.getStatus();
        Integer oldStatus = monitorCustomerPoiRelDTO.getOldStatus();
        Integer oldValid = monitorCustomerPoiRelDTO.getOldValid();
        Integer valid = monitorCustomerPoiRelDTO.getValid();
        Long bizTaskId = monitorCustomerPoiRelDTO.getBizTaskId();
        Integer customerId = monitorCustomerPoiRelDTO.getCustomerId();
        Long wmPoiId = monitorCustomerPoiRelDTO.getWmPoiId();
        Integer id = monitorCustomerPoiRelDTO.getId();

        try {
            if (bizTaskId == null) {
                return String.format("客户门店关系已绑定，但是关联的任务ID为空,customerId=%s,wmPoiId=%s,id=%s",
                        customerId, wmPoiId, id);
            }

            WmCustomerTask task = customerTaskService.getTaskById(bizTaskId);
            log.info("checkPoiRelUpdateData,根据任务ID查询到关联的任务信息,customerId={},wmPoiId={},task={}",
                    customerId, wmPoiId, JSON.toJSONString(task));
            if (task == null) {
                return String.format("客户门店关系变更，未查询到有效的任务记录,customerId=%s,wmPoiId=%s,id=%s,taskId=%s",
                        customerId, wmPoiId, id, bizTaskId);
            }

            //最新状态是已绑定、且存在有效关联记录
            if (CustomerConstants.VALID == valid
                    && CustomerRelationStatusEnum.BIND.getCode() == status
                    && !oldStatus.equals(status)
                    && task.getTaskType().equals(CustomerTaskTypeEnum.CUSTOMER_BIND_POI.getCode())
                    && (!task.getStatus().equals(CustomerTaskStatusEnum.SUC.getCode()) && !task.getStatus().equals(CustomerTaskStatusEnum.CANCEL.getCode()))) {
                log.info("checkPoiRelUpdateData,客户门店关系已绑定，关联的任务状态非成功,wmPoiId={},customerId={},task={}", customerId, wmPoiId, JSON.toJSONString(task));
                return String.format("客户门店关系已绑定，关联的任务状态非成功,customerId=%s,wmPoiId=%s,id=%s,taskId=%s,taskStatus=%s",
                        customerId, wmPoiId, id, bizTaskId,
                        CustomerTaskStatusEnum.of(task.getStatus()) == null ? "" : CustomerTaskStatusEnum.of(task.getStatus()).getDesc());
            }

            //更新成无效-即为解绑
            if (CustomerConstants.UNVALID == valid
                    && !valid.equals(oldValid)
                    && task.getTaskType().equals(CustomerTaskTypeEnum.CUSTOMER_UNBIND_POI.getCode())
                    && !task.getStatus().equals(CustomerTaskStatusEnum.SUC.getCode())) {
                log.info("checkPoiRelUpdateData,客户门店关系已解绑，关联的任务状态非成功,wmPoiId={},customerId={},task={}", wmPoiId, customerId, JSON.toJSONString(task));
                return String.format("客户门店关系已解绑，关联的任务状态非成功,customerId=%s,wmPoiId=%s,id=%s,taskId=%s,taskStatus=%s",
                        customerId, wmPoiId, id, bizTaskId,
                        CustomerTaskStatusEnum.of(task.getStatus()) == null ? "" : CustomerTaskStatusEnum.of(task.getStatus()).getDesc());
            }
        } catch (Exception e) {
            log.error("checkUpdateData,客户门店关系变更监控客户任务数据发生异常,customerId={},wmPoiId={},id={}"
                    , customerId, wmPoiId, id, e);
        }
        return null;
    }


    /**
     * 判断是否因为门店释放取消的解绑任务
     *
     * @param taskId
     * @return
     */
    public boolean checkPoiReleaseCancel(Integer taskId) {
        try {
            WmCustomerTask task = customerTaskService.getTaskById(taskId.longValue());
            if (task != null && StringUtils.isNotEmpty(task.getProcessMsg())
                    && task.getProcessMsg().contains("门店自动取消")) {
                return true;
            }
        } catch (Exception e) {
            log.error("根据任务ID查询任务信息发生异常,taskId={}", taskId, e);
        }
        return false;
    }
}
