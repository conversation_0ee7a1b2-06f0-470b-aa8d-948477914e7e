package com.sankuai.meituan.waimai.customer.service.customer.monitor.customerPoiList;

import com.dianping.frog.sdk.data.DmlType;
import com.google.common.base.MoreObjects;
import com.sankuai.meituan.waimai.customer.constant.WmCustomerPoiListESFields;
import com.sankuai.meituan.waimai.customer.constant.customer.WmPoiRelTableDbusEnum;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerPoiListVo;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerPoiListEsService;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.WmColumnInfo;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.WmCustomerPoiListInfoDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Slf4j
@Service
public class WmPoiControlCheck implements InfoUpdateCheck {

    @Autowired
    private WmCustomerPoiListEsService wmCustomerPoiListEsService;

    @Override
    public String check(String tableName, DmlType operateType, Map<String, WmColumnInfo> columnInfoMap) {
        if (!tableName.equals(WmPoiRelTableDbusEnum.TABLE_WM_POI_CONTROL.getCode())) {
            return null;
        }
        switch (operateType) {
            case INSERT:
            case DELETE:
                return null;
            case UPDATE:
                return checkUpdate(columnInfoMap);
            default:
                return null;
        }
    }

    private String checkUpdate(Map<String, WmColumnInfo> columnInfoMap) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.service.customer.monitor.customerPoiList.WmPoiControlCheck.checkUpdate(java.util.Map)");
        int wmPoiId = MoreObjects.firstNonNull(Integer.valueOf(columnInfoMap.get(WmCustomerPoiListESFields.WM_POI_ID.getDbField()).getNewValue()), 0);
        int valid = MoreObjects.firstNonNull(Integer.valueOf(columnInfoMap.get(WmCustomerPoiListESFields.POI_STATUS.getDbField()).getNewValue()), 0);
        int isDelete = MoreObjects.firstNonNull(Integer.valueOf(columnInfoMap.get(WmCustomerPoiListESFields.IS_DELETE.getDbField()).getNewValue()), 0);

        WmCustomerPoiListVo condition = new WmCustomerPoiListVo();
        condition.setWmPoiId(wmPoiId);
        condition.setPageNo(1);
        condition.setPageSize(10);
        List<WmCustomerPoiListInfoDTO> list = wmCustomerPoiListEsService.queryData(condition);
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        StringBuffer errMsg = new StringBuffer();
        for (WmCustomerPoiListInfoDTO dto : list) {
            if (dto.getPoiStatus().intValue() != valid) {
                errMsg.append(String.format("门店状态不一致:%s:%s,%s:%s(%s);", WmCustomerPoiListESFields.ID.getField(), dto.getId(), WmCustomerPoiListESFields.POI_STATUS.getField(), valid, dto.getPoiStatus()));
            }
            if (dto.getIsDelete().intValue() != isDelete) {
                errMsg.append(String.format("门店状态不一致:%s:%s,%s:%s(%s);", WmCustomerPoiListESFields.ID.getField(), dto.getId(), WmCustomerPoiListESFields.IS_DELETE.getField(), isDelete, dto.getIsDelete()));
            }
        }
        return errMsg.toString();
    }
}
