package com.sankuai.meituan.waimai.customer.service.gray;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.sankuai.meituan.waimai.customer.adapter.*;
import com.sankuai.meituan.waimai.customer.bo.WmPoiDomain;
import com.sankuai.meituan.waimai.customer.config.MccCustomerConfig;
import com.sankuai.meituan.waimai.customer.dao.WmCustomerDBMapper;
import com.sankuai.meituan.waimai.customer.constant.customer.CustomerPoiUnBindTypeEnum;
import com.sankuai.meituan.waimai.customer.dao.WmCustomerKpDBMapper;
import com.sankuai.meituan.waimai.customer.domain.CusQuaComOrgGrayVO;
import com.sankuai.meituan.waimai.customer.domain.CustomerGrayTeamMapCityVo;
import com.sankuai.meituan.waimai.customer.domain.CustomerKpMerchantGrayCityVo;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerDB;
import com.sankuai.meituan.waimai.infra.constants.WmVirtualOrgSourceEnum;
import com.sankuai.meituan.waimai.infra.domain.WmVirtualOrgCity;
import com.sankuai.meituan.waimai.infra.domain.WmVirtualOrgTeamType;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerConstants;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerRealTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerSource;
import com.sankuai.meituan.waimai.thrift.customer.constant.customer.CustomerVersionEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.CustomerSceneInfoBO;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.WmCustomerBasicBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 客户灰度专用服务
 */
@Slf4j
@Service
public class WmCustomerGrayService {

    @Autowired
    private WmVirtualOrgServiceAdaptor wmVirtualOrgServiceAdaptor;

    @Autowired
    private WmEmployClient wmEmployClient;

    @Resource
    private WmPoiClient wmPoiClient;

    @Autowired
    private WmKayleAdaptor wmKayleAdaptor;

    @Autowired
    private WmCustomerKpDBMapper customerKpDBMapper;

    @Autowired
    private WmCustomerDBMapper wmCustomerDBMapper;

    @Autowired
    private WmOrgClient wmOrgClient;

    /**
     * 客户管理V1.4.1（Part2） 一线咨询问题项目之KP信息,灰度开关
     * prd :https://km.sankuai.com/page/1084564981，有效期至2021-12-01
     *
     * @return
     */

    public boolean isNewForCustomerKp() {
        List<Integer> grayCityIds = MccCustomerConfig.getCustomerKpGrayCityIds();
        if (CollectionUtils.isEmpty(grayCityIds)) {
            return true;
        }
        return false;
    }

    /**
     * 客户管理V1.4.5 【女娲二期】切换客户任务系统&客户系统兼容,灰度开关
     * prd :https://km.sankuai.com/page/1213794726
     *
     * @param ownerUid
     * @return
     */
    public boolean isGrayForGoddess(Integer ownerUid) {
        // 1.判断是否全量开关打开
        boolean isSwitch = MccCustomerConfig.getGoddessCustomerSwitch();
        if (isSwitch) {
            return true;
        }
        // 2.判断是否命中灰度人员
        List<Integer> grayUids = MccCustomerConfig.getGoddessCustomerGrayUid();
        if (grayUids.contains(ownerUid)) {
            return true;
        }

        // 3.1获取当前人员组织结构类型
        List<Byte> sources = wmVirtualOrgServiceAdaptor.getUserSources(ownerUid);


        // 3.2判断是否包含外卖和合作商节点
        if (!sources.contains(WmVirtualOrgSourceEnum.WAIMAI) && sources.contains(WmVirtualOrgSourceEnum.JIAMENGSHANG)) {
            return false;
        }

        // 3.3获取人员组织节点
        List<Integer> orgIds = wmVirtualOrgServiceAdaptor.getOrgIdsByUid(ownerUid);
        if (CollectionUtils.isEmpty(orgIds)) {
            return false;
        }
        // 3.4获取组织节点所在团队类型
        List<WmVirtualOrgTeamType> wmVirtualOrgTeamTypes = wmVirtualOrgServiceAdaptor.getOrgTeamTypeByOrgIds(Lists.newArrayList(orgIds));
        if (CollectionUtils.isEmpty(wmVirtualOrgTeamTypes)) {
            return false;
        }
        // 3.5获取组织节点所在物理城市
        List<WmVirtualOrgCity> wmVirtualOrgCities = wmVirtualOrgServiceAdaptor.getCitysByOrgIds(Lists.newArrayList(orgIds));
        if (CollectionUtils.isEmpty(wmVirtualOrgCities)) {
            return false;
        }

        Map<Integer, WmVirtualOrgTeamType> orgTeamTypeMap = wmVirtualOrgTeamTypes.stream().collect(Collectors.toMap(WmVirtualOrgTeamType::getOrgId, x -> x));
        Map<Integer, WmVirtualOrgCity> orgCityMap = wmVirtualOrgCities.stream().collect(Collectors.toMap(WmVirtualOrgCity::getOrgId, x -> x));

        // 3.6获取当前组织节点团队类型城市配置
        List<CustomerGrayTeamMapCityVo> teamMapCityVos = MccCustomerConfig.getGoddessCustomerGrayTeamCity();
        if (CollectionUtils.isEmpty(teamMapCityVos)) {
            return false;
        }
        Map<String, CustomerGrayTeamMapCityVo> teamMapCity = teamMapCityVos.stream().collect(Collectors.toMap(k -> k.getSourceType() + "&" + k.getTeamType(), x -> x));
        for (Integer orgId : orgIds) {
            WmVirtualOrgTeamType teamType = orgTeamTypeMap.get(orgId);
            WmVirtualOrgCity orgCity = orgCityMap.get(orgId);
            if (teamType == null || orgCity == null || CollectionUtils.isEmpty(orgCity.getCityIds())) {
                continue;
            }
            for (Byte source : sources) {
                CustomerGrayTeamMapCityVo teamMapCityVo = teamMapCity.get(source + "&" + teamType.getTeamType());
                if (teamMapCityVo != null && !Collections.disjoint(orgCity.getCityIds(), teamMapCityVo.getCityIds())) {
                    return true;
                }
            }

        }
        return false;

    }

    /**
     * 是否有美食城特殊数据
     *
     * @param wmCustomerBasicBo
     * @return true 有 false 没有
     */
    public boolean isGrayForNewMsc(WmCustomerBasicBo wmCustomerBasicBo) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.gray.WmCustomerGrayService.isGrayForNewMsc(com.sankuai.meituan.waimai.thrift.customer.domain.customer.WmCustomerBasicBo)");
        if (wmCustomerBasicBo == null) {
            return false;
        }
        if (wmCustomerBasicBo.getCustomerRealTypeSpInfoBo() != null) {
            return true;
        }
        return false;
    }

    /**
     * 美食城客户初始状态判断
     *
     * @param wmCustomerDB
     * @return <br/>true 需要提审 状态初始化为 待生效
     * <br/>false 无需提审 直接生效 状态初始化为 已生效
     */
    public boolean isGrayForNewMscInitStatus(WmCustomerDB wmCustomerDB) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.gray.WmCustomerGrayService.isGrayForNewMscInitStatus(com.sankuai.meituan.waimai.customer.domain.WmCustomerDB)");
        if (wmCustomerDB == null) {
            return false;
        }
        //美食城类型兼容:如信息完备则可直接提审,如信息缺失则不可直接提审
        if (wmCustomerDB.getCustomerRealType() == CustomerRealTypeEnum.MEISHICHENG.getValue()) {
            if (StringUtils.isNotBlank(wmCustomerDB.getCustomerRealTypeSpInfo())) {
                return true;
            } else {
                return false;
            }
        }
        return false;
    }

    /**
     * 校验是否为高风险城市
     *
     * @param wmPoiId
     * @return
     */
    public boolean checkWmPoiIdHighRiskCityId(Integer wmPoiId) {
        if (wmPoiId == null || wmPoiId <= 0) {
            return false;
        }
        try {
            //获取运营配置的商家端-高风险城市：二级、三级物理城市
            CustomerKpMerchantGrayCityVo kpMerchantGrayCityVo = wmKayleAdaptor.getMerchantHighRiskCityIds();
            if (kpMerchantGrayCityVo == null) {
                log.info("WmCustomerGrayService.checkWmPoiIdHighRiskCityId,未获取到商家端配置的高风险物理城市对象信息,未命中灰度策略,wmPoiId={}", wmPoiId);
                return false;
            }

            String merchantSecondCityIds = kpMerchantGrayCityVo.getSecondCityIds();
            String merchantThirdCityIds = kpMerchantGrayCityVo.getThirdCityIds();
            log.info("WmCustomerGrayService.checkWmPoiIdHighRiskCityId,wmPoiId={},merchantSecondCityIds={},merchantThirdCityIds={}", wmPoiId, merchantSecondCityIds, merchantThirdCityIds);
            if (StringUtils.isBlank(merchantSecondCityIds) && StringUtils.isBlank(merchantThirdCityIds)) {
                return false;
            }
            //获取门店所在的物理城市
            WmPoiDomain wmPoiDomain = wmPoiClient.getWmPoiById(wmPoiId);
            Integer secCityId = wmPoiDomain.getCityLocationId();
            Integer thirdCityId = wmPoiDomain.getLocationId();
            log.info("WmCustomerGrayService.checkWmPoiIdHighRiskCityId,门店所在城市信息,secCityId={},thirdCityId={},wmPoiId={}", secCityId, thirdCityId, wmPoiId);
            //命中高风险的二级物理城市
            if (StringUtils.isNotBlank(merchantSecondCityIds)) {
                List<String> secRiskCityIds = JSON.parseArray(merchantSecondCityIds.trim(), String.class);
                if (CollectionUtils.isNotEmpty(secRiskCityIds) && secRiskCityIds.contains(secCityId.toString())) {
                    log.info("WmCustomerGrayService.checkWmPoiIdHighRiskCityId,门店所在二级物理城市命中灰度城市,wmPoiId={}", wmPoiId);
                    return true;
                }
            }
            //命中高风险的二级物理城市
            if (StringUtils.isNotBlank(merchantThirdCityIds)) {
                List<String> thirdRiskCityIds = JSON.parseArray(merchantThirdCityIds, String.class);
                if (CollectionUtils.isNotEmpty(thirdRiskCityIds) && thirdRiskCityIds.contains(thirdCityId.toString())) {
                    log.info("WmCustomerGrayService.checkWmPoiIdHighRiskCityId,门店所在三级物理城市命中灰度城市,wmPoiId={}", wmPoiId);
                    return true;
                }
            }
        } catch (Exception e) {
            log.error("WmCustomerGrayService.checkWmPoiIdHighRiskCityId,校验商家端门店是否命中高风险城市发生异常,wmPoiId={}", wmPoiId, e);
        }
        return false;
    }

    /**
     * 根据操作人以及客户ID判断是否命中外卖单店个人资质灰度
     *
     * @param opUid
     * @param wmCustomerBasicBo
     * @return
     */
    public Boolean hitWmSinglePerCertifyGray(Integer opUid, WmCustomerBasicBo wmCustomerBasicBo) {
        Integer customerId = wmCustomerBasicBo.getId();
        try {
            //全量开关开启
            if (MccCustomerConfig.getWmSinglePerCertifyAllSwitch()) {
                return true;
            }

            CustomerSource customerSource = wmCustomerBasicBo.getCustomerSource();
            if (customerSource != null && customerSource == CustomerSource.WAIMAI_BD) {
                return checkHitPersonCertifyGrayXF(customerId, opUid);
            } else {
                return checkHitPersonCertifyGrayNotXF(wmCustomerBasicBo);
            }
        } catch (Exception e) {
            log.error("hitWmSinglePerCertifyGray,判断客户是否命中个人资质灰度发生异常,customerId={},opUid={}", customerId, opUid, e);
        }
        return false;
    }

    /**
     * 判断先富渠道是否命中灰度
     *
     * @param customerId
     * @param opUid
     * @return
     */
    public boolean checkHitPersonCertifyGrayXF(Integer customerId, Integer opUid) {
        try {
            //全量开关开启
            if (MccCustomerConfig.getWmSinglePerCertifyAllSwitch()) {
                return true;
            }
            //客户ID是否在灰度配置中
            if (customerId != null
                    && CollectionUtils.isNotEmpty(MccCustomerConfig.getWmSinglePerCertifyGrayCustomerIds())
                    && MccCustomerConfig.getWmSinglePerCertifyGrayCustomerIds().contains(customerId)) {
                return true;
            }
            //操作人所在组织节点是否命中灰度组织节点ID
            if (opUid != null && opUid > 0 && checkOpUidHitGrayOrgIdsOnPerCertify(opUid)) {
                return true;
            }

            //修改场景
            if (customerId != null && customerId > 0) {
                //客户的美食城信息是否设置版本号信息
                WmCustomerDB wmCustomerDB = wmCustomerDBMapper.selectCustomerById(customerId);
                if (wmCustomerDB == null) {
                    throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "未查询到有效客户");
                }
                //判断场景信息是否非空，非空则命中灰度
                String sceneInfo = wmCustomerDB.getSceneInfo();
                if (StringUtils.isNotEmpty(sceneInfo)) {
                    return true;
                }
            }
        } catch (Exception e) {
            log.error("checkHitPersonCertifyGrayXF,判断客户是否命中个人资质灰度发生异常,customerId={},opUid={}", customerId, opUid, e);
        }
        return false;
    }

    /**
     * 判断非先富渠道是否命中灰度
     *
     * @param wmCustomerBasicBo
     * @return
     */
    public boolean checkHitPersonCertifyGrayNotXF(WmCustomerBasicBo wmCustomerBasicBo) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.gray.WmCustomerGrayService.checkHitPersonCertifyGrayNotXF(com.sankuai.meituan.waimai.thrift.customer.domain.customer.WmCustomerBasicBo)");
        try {
            //全量开关开启
            if (MccCustomerConfig.getWmSinglePerCertifyAllSwitch()) {
                return true;
            }

            //客户有场景信息属性且version=1则是命中灰度
            CustomerSceneInfoBO customerSceneInfoBO = wmCustomerBasicBo.getSceneInfoBO();
            if (customerSceneInfoBO != null
                    && customerSceneInfoBO.getVersion() != null
                    && customerSceneInfoBO.getVersion() == CustomerVersionEnum.V1.getCode()) {
                return true;
            }
        } catch (Exception e) {
            log.error("checkHitPersonCertifyGrayNotXF,判断客户是否命中个人资质灰度发生异常,wmCustomerBasicBo={}", JSON.toJSONString(wmCustomerBasicBo), e);
        }
        return false;
    }

    /**
     * 判断操作人是否在灰度的组织节点ID列表
     * 所在组织节点以及上级组织节点命中灰度都算命中灰度
     *
     * @param opUid
     * @return
     */
    private boolean checkOpUidHitGrayOrgIdsOnPerCertify(Integer opUid) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_1", "com.sankuai.meituan.waimai.customer.service.gray.WmCustomerGrayService.checkOpUidHitGrayOrgIdsOnPerCertify(java.lang.Integer)");
        try {
            //灰度组织节点ID配置为空则直接返回
            if (StringUtils.isEmpty(MccCustomerConfig.getWmSinglePerCertifyOrgGrayConfig())) {
                return false;
            }
            //查询操作人所在组织架构列表
            List<Byte> sources = wmVirtualOrgServiceAdaptor.getUserSources(opUid);
            if (CollectionUtils.isEmpty(sources)) {
                return false;
            }
            log.info("checkOpUidHitGrayOrgIdsOnPerCertify,查询当前操作人所在组织节点结果,opUid={},sources={}",
                    opUid, JSON.toJSONString(sources));
            //操作人非 外卖城市、外卖合作商（只读）、外卖合作总商 则直接返回
            if (!sources.contains(WmVirtualOrgSourceEnum.WAIMAI.getSource())
                    && !sources.contains(WmVirtualOrgSourceEnum.JIAMENGSHANG.getSource())
                    && !sources.contains(WmVirtualOrgSourceEnum.WM_HE_ZUO_SHANG_ZONG_SHANG.getSource())) {
                return false;
            }

            //转换为数组
            List<CusQuaComOrgGrayVO> cusQuaComOrgGrayVOList = JSON.parseArray(MccCustomerConfig.getWmSinglePerCertifyOrgGrayConfig(), CusQuaComOrgGrayVO.class);
            Map<Integer, List<Integer>> orgGrayMaps = cusQuaComOrgGrayVOList.stream()
                    .collect(Collectors.toMap(CusQuaComOrgGrayVO::getSource, CusQuaComOrgGrayVO::getOrgIds));

            //查询所在组织节点是否在灰度范围内
            for (Byte source : sources) {
                List<Integer> orgIds = wmOrgClient.getSuperiorBySource(opUid, source);
                log.info("checkOpUidHitGrayOrgIdsOnPerCertify,获取当前人员组织节点:{}, opUid:{}", JSONObject.toJSONString(orgIds), opUid);
                //如果存在交集则为true
                if (CollectionUtils.isNotEmpty(orgIds)
                        && CollectionUtils.isNotEmpty(orgGrayMaps.get(source.intValue()))
                        && !Collections.disjoint(orgGrayMaps.get(source.intValue()), orgIds)) {
                    return true;
                }
            }
        } catch (Exception e) {
            log.error("checkOpUidHitGrayOrgIdsOnPerCertify,根据opUid查询所属组织架构列表发生异常,opUid={}", opUid, e);
        }
        return false;
    }

    /**
     * 美食城门店数量&视频校验新逻辑灰度
     * @param opUid
     * @return
     */
    public boolean isGrayMscPoiCntCheckNew(Integer opUid){
        try {
            if (MccCustomerConfig.getMscPoiCntCheckNewSwitch()){
                // 全量开关判断
                return true;
            }
            //灰度组织节点ID配置为空则直接返回
            if (StringUtils.isEmpty(MccCustomerConfig.getMscPoiCntCheckNewOrgConfig())) {
                return false;
            }
            //查询操作人所在组织架构列表
            List<Byte> sources = wmVirtualOrgServiceAdaptor.getUserSources(opUid);
            if (CollectionUtils.isEmpty(sources)) {
                return false;
            }
            log.info("isGrayMscPoiCntCheckNew,查询当前操作人所在组织节点结果,opUid={},sources={}", opUid, JSON.toJSONString(sources));
            //操作人非 外卖城市、外卖合作商（下沉市场）、外卖合作总商（下沉市场总商）、全国KA、校园聚合配送、外卖校园大使、拼好饭业务
            if (!sources.contains(WmVirtualOrgSourceEnum.WAIMAI.getSource())
                    && !sources.contains(WmVirtualOrgSourceEnum.JIAMENGSHANG.getSource())
                    && !sources.contains(WmVirtualOrgSourceEnum.WM_HE_ZUO_SHANG_ZONG_SHANG.getSource())
                    && !sources.contains(WmVirtualOrgSourceEnum.DAKEHU.getSource())
                    && !sources.contains(WmVirtualOrgSourceEnum.XIAO_YUAN_JU_HE_PEI_SONG.getSource())
                    && !sources.contains(WmVirtualOrgSourceEnum.WAI_MAI_XIAO_YUAN_DA_SHI.getSource())
                    && !sources.contains(WmVirtualOrgSourceEnum.PIN_HAO_FAN.getSource())) {
                return false;
            }

            //转换为数组
            List<CusQuaComOrgGrayVO> cusQuaComOrgGrayVOList = JSON.parseArray(MccCustomerConfig.getMscPoiCntCheckNewOrgConfig(), CusQuaComOrgGrayVO.class);
            Map<Integer, List<Integer>> orgGrayMaps = cusQuaComOrgGrayVOList.stream()
                    .collect(Collectors.toMap(CusQuaComOrgGrayVO::getSource, CusQuaComOrgGrayVO::getOrgIds));

            //查询所在组织节点是否在灰度范围内
            for (Byte source : sources) {
                List<Integer> orgIds = wmOrgClient.getSuperiorBySource(opUid, source);
                log.info("isGrayMscPoiCntCheckNew,获取当前人员组织节点:{}, opUid:{}", JSONObject.toJSONString(orgIds), opUid);
                //如果存在交集则为true
                if (CollectionUtils.isNotEmpty(orgIds)
                        && CollectionUtils.isNotEmpty(orgGrayMaps.get(source.intValue()))
                        && !Collections.disjoint(orgGrayMaps.get(source.intValue()), orgIds)) {
                    return true;
                }
            }
        } catch (Exception e) {
            log.error("isGrayMscPoiCntCheckNew,根据opUid查询所属组织架构列表发生异常,opUid={}", opUid, e);
        }
        return false;
    }

    /**
     * 客户前端工程拆分逻辑灰度
     * 
     * @param opUid
     * @return
     */
    public boolean isGrayCustomerFrontendSplit(Integer opUid) {
        try {
            if (MccCustomerConfig.getCustomerFrontendSplitSwitch()) {
                // 全量开关判断
                return true;
            }
            // 灰度组织节点ID配置为空则直接返回
            if (StringUtils.isEmpty(MccCustomerConfig.getCustomerFrontendSplitOrgConfig())) {
                return false;
            }
            // 查询操作人所在组织架构列表
            List<Byte> sources = wmVirtualOrgServiceAdaptor.getUserSources(opUid);
            if (CollectionUtils.isEmpty(sources)) {
                return false;
            }
            log.info("isGrayCustomerFrontendSplit,查询当前操作人所在组织节点结果,opUid={},sources={}", opUid, JSON.toJSONString(sources));
            if (!sources.contains(WmVirtualOrgSourceEnum.WAIMAI.getSource())
                    && !sources.contains(WmVirtualOrgSourceEnum.WM_HE_ZUO_SHANG_ZONG_SHANG.getSource())
                    && !sources.contains(WmVirtualOrgSourceEnum.YI_YAO_YE_WU_ZONG_BU.getSource())
                    && !sources.contains(WmVirtualOrgSourceEnum.SHAN_GOU.getSource())) {
                return false;
            }
//转换为数组
            List<CusQuaComOrgGrayVO> cusQuaComOrgGrayVOList = JSON
                    .parseArray(MccCustomerConfig.getCustomerFrontendSplitOrgConfig(), CusQuaComOrgGrayVO.class);
            Map<Integer, List<Integer>> orgGrayMaps = cusQuaComOrgGrayVOList.stream()
                    .collect(Collectors.toMap(CusQuaComOrgGrayVO::getSource, CusQuaComOrgGrayVO::getOrgIds));

            // 查询所在组织节点是否在灰度范围内
            for (Byte source : sources) {
                List<Integer> orgIds = wmOrgClient.getSuperiorBySource(opUid, source);
                log.info("isGrayCustomerFrontendSplit,获取当前人员组织节点:{}, opUid:{}", JSONObject.toJSONString(orgIds), opUid);
                // 如果存在交集则为true
                if (CollectionUtils.isNotEmpty(orgIds) && CollectionUtils.isNotEmpty(orgGrayMaps.get(source.intValue()))
                        && !Collections.disjoint(orgGrayMaps.get(source.intValue()), orgIds)) {
                    return true;
                }
            }
        } catch (Exception e) {
            log.error("isGrayCustomerFrontendSplit,根据opUid查询所属组织架构列表发生异常,opUid={}", opUid, e);
        }
        return false;
    }

    /**
     * 客户门店解绑新逻辑灰度
     *
     * @param typeEnum
     * @param grayField
     * @return
     */
    public boolean isGrayNewCustomerUnBindPoi(CustomerPoiUnBindTypeEnum typeEnum, int grayField) {
        if (typeEnum == null) {
            return false;
        }
        Map<String, Integer> map = MccCustomerConfig.getPoiUnBindCustomerNewGrayPercent();
        if (map == null || map.isEmpty()) {
            return false;
        }
        Integer grayPercent = map.get(typeEnum.name());
        if (grayPercent == null || grayField % 100 >= grayPercent.intValue()) {
            return false;
        }
        return true;
    }

    /**
     * 是否命中资质同步以及KP新流程灰度
     *
     * @param customerId
     * @return
     */
    public boolean hitPoiQuaAutoSyncCustomerFlowGray(Integer customerId) {
        try {
            //客户的美食城信息是否设置版本号信息
            WmCustomerDB wmCustomerDB = wmCustomerDBMapper.selectCustomerById(customerId);
            if (wmCustomerDB == null) {
                log.error("hitQuaAndKpNewFlowGray,客户ID对应的客户对象不存在，默认为命中灰度,customerId={}", customerId);
                return false;
            }

            //客户ID是否命中灰度
            if (customerId % 100 < MccCustomerConfig.getPoiQuaAutoSyncCustomerGrayConfig()) {
                return true;
            }

        } catch (Exception e) {
            log.error("根据客户ID判断是否命中资质同步以及KP电销创建无短信授权流程灰度异常,customerId={}", customerId, e);
        }
        return false;
    }

    /**
     * KP签约代理人-电销首次提审通过新流程灰度开关
     *
     * @param customerId
     * @return
     */
    public boolean hitDxKpSignerFirstAuditSucGray(Integer customerId) {
        try {
            WmCustomerDB wmCustomerDB = wmCustomerDBMapper.selectCustomerById(customerId);
            if (wmCustomerDB == null) {
                log.error("hitDxKpSignerFirstAuditSucGray,客户ID对应的客户对象不存在，默认为命中灰度,customerId={}", customerId);
                return false;
            }

            //客户ID是否命中灰度
            if (customerId % 100 < MccCustomerConfig.getKpSignerAgentDxFirstAuditSucFlowGrayConfig()) {
                return true;
            }
        } catch (Exception e) {
            log.error("hitDxKpSignerFirstAuditSucGray,根据客户ID判断KP签约代理人电销首次提审通过新流程灰度异常,customerId={}", customerId, e);
        }
        return false;
    }

    /**
     * 美食城客户一店多开灰度配置
     * true：命中灰度，走新逻辑
     * false：不命中灰度，走老逻辑
     * @return
     */
    public boolean checkHitMscCustomerUsedPoiGray(){
        return MccCustomerConfig.getMscCustomerUsedPoiGrayConfig();
    }


    /**
     * 判断是否允许不上传银行流水操作KP
     *
     * @param customerId
     * @return
     */
    public Boolean checkHitAllowNoBankStatementKpOperate(Long customerId) {

        //开关开启则直接命中灰度
        if (MccCustomerConfig.getKpNotCardAllowNoBankStatementSwitch()) {
            return true;
        }
        //开关未开启判断客户ID是否在灰度名单内
        if (customerId != null &&
                MccCustomerConfig.getAllowNoBankStatementCustomerIds().contains(customerId)) {
            return true;
        }

        //其他情况直接返回未命中灰度
        return false;
    }

}
