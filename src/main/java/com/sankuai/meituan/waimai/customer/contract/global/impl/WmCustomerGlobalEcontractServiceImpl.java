package com.sankuai.meituan.waimai.customer.contract.global.impl;

import com.meituan.mtrace.Tracer;
import com.sankuai.meituan.auth.util.UserUtils;
import com.sankuai.meituan.waimai.customer.adapter.uac.UacRoleRemoteServiceAdapter;
import com.sankuai.meituan.waimai.customer.contract.global.IWmCustomerGlobalEcontractService;
import com.sankuai.meituan.waimai.customer.contract.global.IWmGlobalContractRoleAuthorityService;
import com.sankuai.meituan.waimai.customer.contract.global.WmGlobalEcontractQueryService;
import com.sankuai.meituan.waimai.customer.util.SSOUtil;
import com.sankuai.meituan.waimai.thrift.customer.domain.base.BaseResponse;
import com.sankuai.meituan.waimai.thrift.customer.domain.base.BooleanResult;
import com.sankuai.meituan.waimai.thrift.customer.domain.global.*;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import lombok.extern.slf4j.Slf4j;
import org.apache.thrift.TException;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import java.util.List;
import java.util.Optional;

/**
 * @description:
 * @author: liuyunjie05
 * @create: 2024/1/10 15:14
 */
@Service
@Slf4j
public class WmCustomerGlobalEcontractServiceImpl implements IWmCustomerGlobalEcontractService {

    @Resource
    private WmGlobalEcontractQueryService wmGlobalEcontractQueryService;

    @Resource
    private IWmGlobalContractRoleAuthorityService wmGlobalContractRoleAuthorityService;

    @Resource
    private UacRoleRemoteServiceAdapter uacRoleRemoteServiceAdapter;

    private static final String EXPORT_NUMBER_UPPER_MSG = "查询结果超过1万条，无法导出，请缩小范围后重新查询";

    private static final String EXPORT_EXCEPTION_MSG = "系统异常，合同台账导出失败，请联系管理员。traceID: ";

    private static final String DEFAULT_EXCEPTION_MSG = "系统异常，请稍后重试";

    @Override
    public BaseResponse<EContractRoleInfoResponse> queryContractRole(PageInfoSearchParam pageInfoSearchParam) throws TException, WmCustomerException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.contract.global.impl.WmCustomerGlobalEcontractServiceImpl.queryContractRole(com.sankuai.meituan.waimai.thrift.customer.domain.global.PageInfoSearchParam)");
        try {
            // TODO: 如果需要鉴权，放在这里
            int pageNumber = Optional.ofNullable(pageInfoSearchParam).map(PageInfoSearchParam::getPageNumber).orElse(1);
            int pageSize = Optional.ofNullable(pageInfoSearchParam).map(PageInfoSearchParam::getPageSize).orElse(100);
            EContractRoleInfoResponse eContractRoleInfoResponse = wmGlobalContractRoleAuthorityService.selectAllListByPageParam(pageNumber, pageSize);
            return BaseResponse.success(eContractRoleInfoResponse);
        } catch (Exception e) {
            log.error("WmCustomerGlobalEcontractServiceImpl#queryContractRole, error", e);
            return BaseResponse.error(-1, DEFAULT_EXCEPTION_MSG);
        }
    }

    @Override
    public BaseResponse<List<ContractTypeInfo>> queryContractType() {
        try {
            List<ContractTypeInfo> contractTypeInfoList = wmGlobalEcontractQueryService.queryContractType();
            return BaseResponse.success(contractTypeInfoList);
        } catch (Exception e) {
            log.error("WmCustomerGlobalEcontractServiceImpl#queryContractType, error", e);
            return BaseResponse.error(-1, DEFAULT_EXCEPTION_MSG);
        }
    }

    @Override
    public BaseResponse<BooleanResult> saveContractRole(ContractRoleSaveParam contractRoleSaveParam) throws TException, WmCustomerException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.contract.global.impl.WmCustomerGlobalEcontractServiceImpl.saveContractRole(com.sankuai.meituan.waimai.thrift.customer.domain.global.ContractRoleSaveParam)");
        try {
            // TODO: 如果需要鉴权，放在这里
            boolean result = wmGlobalContractRoleAuthorityService.saveContractRole(contractRoleSaveParam);
            BooleanResult booleanResult = new BooleanResult();
            booleanResult.setRes(result);
            return BaseResponse.success(booleanResult);
        } catch (Exception e) {
            log.error("WmCustomerGlobalEcontractServiceImpl#saveContractRole, error", e);
            return BaseResponse.error(-1, DEFAULT_EXCEPTION_MSG);
        }
    }

    @Override
    public BaseResponse<UacRoleListResponse> queryUacAllRole(UacRuleQueryParam uacRuleQueryParam) throws TException, WmCustomerException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.contract.global.impl.WmCustomerGlobalEcontractServiceImpl.queryUacAllRole(com.sankuai.meituan.waimai.thrift.customer.domain.global.UacRuleQueryParam)");
        try {
            // TODO: 如果需要鉴权，放在这里
            UacRoleListResponse uacRoleListResponse = uacRoleRemoteServiceAdapter.queryUacAllRole(uacRuleQueryParam);
            return BaseResponse.success(uacRoleListResponse);
        } catch (Exception e) {
            log.error("WmCustomerGlobalEcontractServiceImpl#queryUacAllRole, error", e);
            return BaseResponse.error(-1, DEFAULT_EXCEPTION_MSG);
        }
    }

    @Override
    public BaseResponse<List<ContractStatusInfo>> queryContractStatus() {
        try {
            List<ContractStatusInfo> contractStatusInfoList = wmGlobalEcontractQueryService.queryContractStatus();
            return BaseResponse.success(contractStatusInfoList);
        } catch (Exception e) {
            log.error("WmCustomerGlobalEcontractServiceImpl#queryContractStatus, error", e);
            return BaseResponse.error(-1, DEFAULT_EXCEPTION_MSG);
        }
    }

    @Override
    public BaseResponse<List<ContractCategoryInfo>> queryContractCategory() {
        try {
            List<ContractCategoryInfo> contractCategoryInfoList = wmGlobalEcontractQueryService.queryContractCategory();
            return BaseResponse.success(contractCategoryInfoList);
        } catch (Exception e) {
            log.error("WmCustomerGlobalEcontractServiceImpl#queryContractCategory, error", e);
            return BaseResponse.error(-1, DEFAULT_EXCEPTION_MSG);
        }
    }

    @Override
    public BaseResponse<GlobalContractResponse> queryContractInfo(GlobalContractSearchParam searchParam) {
        try {
            GlobalContractResponse globalContractResponse = wmGlobalEcontractQueryService.queryGlobalContractInfo(searchParam);
            return BaseResponse.success(globalContractResponse);
        } catch (WmCustomerException e) {
            log.warn("WmCustomerGlobalEcontractServiceImpl#queryContractInfo, WmCustomerException", e);
            return BaseResponse.error(-1, e.getMsg());
        } catch (Exception e) {
            log.error("WmCustomerGlobalEcontractServiceImpl#queryContractInfo, Exception", e);
            return BaseResponse.error(-1, DEFAULT_EXCEPTION_MSG);
        }
    }

    @Override
    public BaseResponse<Boolean> exportContractInfo(GlobalContractBaseParam exportParam) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.contract.global.impl.WmCustomerGlobalEcontractServiceImpl.exportContractInfo(com.sankuai.meituan.waimai.thrift.customer.domain.global.GlobalContractBaseParam)");
        try {
            Boolean exportResult = wmGlobalEcontractQueryService.exportGlobalContractInfo(exportParam, SSOUtil.getUser());
            if (!exportResult) {
                return BaseResponse.error(-1, EXPORT_NUMBER_UPPER_MSG);
            }
            return BaseResponse.success(true);
        } catch (Exception e) {
            log.error("WmCustomerGlobalEcontractServiceImpl#exportContractInfo, error", e);
            return BaseResponse.error(-1, EXPORT_EXCEPTION_MSG + Tracer.id());
        }
    }
}
