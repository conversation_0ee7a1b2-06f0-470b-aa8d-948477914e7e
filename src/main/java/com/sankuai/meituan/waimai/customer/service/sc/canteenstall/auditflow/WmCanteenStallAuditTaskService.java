package com.sankuai.meituan.waimai.customer.service.sc.canteenstall.auditflow;

import com.alibaba.fastjson.JSONObject;
import com.sankuai.meituan.waimai.crm.ticket.thrift.domain.WmTicketDto;
import com.sankuai.meituan.waimai.customer.adapter.WmScEmployAdaptor;
import com.sankuai.meituan.waimai.customer.adapter.WmVirtualOrgServiceAdaptor;
import com.sankuai.meituan.waimai.customer.config.MccScConfig;
import com.sankuai.meituan.waimai.customer.dao.sc.WmCanteenMapper;
import com.sankuai.meituan.waimai.customer.dao.sc.WmSchoolMapper;
import com.sankuai.meituan.waimai.customer.domain.sc.WmCanteenDB;
import com.sankuai.meituan.waimai.customer.domain.sc.WmSchoolDB;
import com.sankuai.meituan.waimai.customer.domain.sc.canteenstall.WmCanteenStallAuditTaskBO;
import com.sankuai.meituan.waimai.customer.domain.sc.canteenstall.WmCanteenStallAuditTaskDO;
import com.sankuai.meituan.waimai.customer.domain.sc.canteenstall.WmCanteenStallAuditTaskNodeDO;
import com.sankuai.meituan.waimai.customer.domain.sc.canteenstall.WmCanteenStallAuditorBO;
import com.sankuai.meituan.waimai.infra.constants.WmVirtualOrgRecursiveTypeEnum;
import com.sankuai.meituan.waimai.infra.constants.WmVirtualOrgSourceEnum;
import com.sankuai.meituan.waimai.infra.domain.WmEmploy;
import com.sankuai.meituan.waimai.infra.domain.WmVirtualOrg;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.canteenstall.CanteenStallAuditNodeTypeEnum;
import com.sankuai.meituan.waimai.thrift.exception.WmSchCantException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 食堂档口管理审批任务Service
 * <AUTHOR>
 * @date 2024/05/25
 * @email <EMAIL>
 */
@Slf4j
@Service
public class WmCanteenStallAuditTaskService {

    @Autowired
    private WmCanteenStallAuditFlowCanteenService wmCanteenStallAuditFlowCanteenService;

    @Autowired
    private WmCanteenStallAuditFlowTicketService wmCanteenStallAuditFlowTicketService;

    @Autowired
    private WmCanteenStallAuditFlowGravityService wmCanteenStallAuditFlowGravityService;

    @Autowired
    private WmCanteenMapper wmCanteenMapper;

    @Autowired
    private WmSchoolMapper wmSchoolMapper;

    @Autowired
    private WmScEmployAdaptor wmScEmployAdaptor;

    @Autowired
    private WmVirtualOrgServiceAdaptor wmVirtualOrgServiceAdaptor;


    /**
     * 提交审批任务
     * @param auditTaskBO auditTaskBO
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    public void submitAuditTask(WmCanteenStallAuditTaskBO auditTaskBO) throws WmSchCantException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_1", "com.sankuai.meituan.waimai.customer.service.sc.canteenstall.auditflow.WmCanteenStallAuditTaskService.submitAuditTask(WmCanteenStallAuditTaskBO)");
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_1", "com.sankuai.meituan.waimai.customer.service.sc.canteenstall.auditflow.WmCanteenStallAuditTaskService.submitAuditTask(WmCanteenStallAuditTaskBO)");
        log.info("[WmCanteenStallAuditTaskService.submitAuditTask] input param: auditTaskBO = {}", JSONObject.toJSONString(auditTaskBO));
        // 1-审批任务主表创建主任务(食堂系统)
        WmCanteenStallAuditTaskDO taskDO = wmCanteenStallAuditFlowCanteenService.createAuditTaskBySubmit(auditTaskBO);

        // 2-创建档口审批流审批中信息(学校系统)
        wmCanteenStallAuditFlowCanteenService.createAuditStreamBySubmit(taskDO, auditTaskBO);

        // 3-创建Gravity审批流程实例(Gravity系统)
        String gravityId = wmCanteenStallAuditFlowGravityService.createAuditGravityInstance(taskDO);

        // 4-更新审批任务主表中GravityId和审批节点(食堂系统)
        wmCanteenStallAuditFlowCanteenService.updateAuditTaskGravityIdAndAuditNode(gravityId, taskDO);

        // 5-查询当前审批节点审批人信息
        WmCanteenStallAuditorBO auditorBO = getAuditTaskAuditorInfo(taskDO.getAuditNode(), taskDO.getCanteenPrimaryId());

        // 6-审批任务主表创建子任务(学校系统)
        WmCanteenStallAuditTaskNodeDO taskNodeDO = wmCanteenStallAuditFlowCanteenService.createAuditTaskNodeBySubmit(taskDO, auditorBO);

        // 7-创建Ticket审批任务(任务系统)
        Integer ticketId = wmCanteenStallAuditFlowTicketService.createAuditTicket(taskDO, auditorBO, taskNodeDO);

        // 8-更新审批任务子表中的任务系统ID(学校系统)
        wmCanteenStallAuditFlowCanteenService.updateAuditTaskNodeAuditSystemId(ticketId, taskNodeDO);

        // 9-更新档口绑定任务主表审批状态为审批中(学校系统)
        wmCanteenStallAuditFlowCanteenService.updateCanteenStallBindAuditStatusBySubmit(auditTaskBO.getBindIdList());
    }


    /**
     * 审批任务通过(仅子任务, 非主任务)
     * @param auditTaskBO auditTaskBO
     * @return 审批节点
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     * @throws TException org.apache.thrift.TException
     */
    public Integer passAuditTask(WmCanteenStallAuditTaskBO auditTaskBO) throws WmSchCantException, TException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.service.sc.canteenstall.auditflow.WmCanteenStallAuditTaskService.passAuditTask(WmCanteenStallAuditTaskBO)");
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.service.sc.canteenstall.auditflow.WmCanteenStallAuditTaskService.passAuditTask(WmCanteenStallAuditTaskBO)");
        log.info("[WmCanteenStallAuditTaskService.passAuditTask] input param: auditTaskBO = {}", JSONObject.toJSONString(auditTaskBO));
        // 1-驱动Gravity对流程审批通过(Gravity系统)
        wmCanteenStallAuditFlowGravityService.driveGravityProcessByAuditPass(auditTaskBO);

        // 2-根据主任务ID查询子任务Ticket任务信息(任务系统)
        WmTicketDto ticketDto = wmCanteenStallAuditFlowTicketService.getAuditTicketDTO(auditTaskBO);

        // 3-审批任务子表更新任务状态为已通过(学校系统)
        wmCanteenStallAuditFlowCanteenService.updateAuditTaskNodeByAuditPass(auditTaskBO, ticketDto);

        // 4-查询下一个审批节点(Gravity系统)
        CanteenStallAuditNodeTypeEnum nodeTypeEnum = wmCanteenStallAuditFlowGravityService.getGravityAuditNodeByGravityId(auditTaskBO.getTaskDO().getGravityId());

        // 5-审批任务主表更新审批节点(学校系统)
        wmCanteenStallAuditFlowCanteenService.updateAuditTaskAuditNode(auditTaskBO, nodeTypeEnum.getType());
        return nodeTypeEnum.getType();
    }

    /**
     * 创建下一个审批节点
     * @param auditTaskBO auditTaskBO
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    public void buildNextAuditNode(WmCanteenStallAuditTaskBO auditTaskBO) throws WmSchCantException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_2", "com.sankuai.meituan.waimai.customer.service.sc.canteenstall.auditflow.WmCanteenStallAuditTaskService.buildNextAuditNode(WmCanteenStallAuditTaskBO)");
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_2", "com.sankuai.meituan.waimai.customer.service.sc.canteenstall.auditflow.WmCanteenStallAuditTaskService.buildNextAuditNode(WmCanteenStallAuditTaskBO)");
        WmCanteenStallAuditTaskDO taskDO = auditTaskBO.getTaskDO();
        // 1-查询当前审批节点审批人信息
        WmCanteenStallAuditorBO auditorBO = getAuditTaskAuditorInfo(taskDO.getAuditNode(), taskDO.getCanteenPrimaryId());

        // 2-审批任务子表创建子任务(学校系统)
        WmCanteenStallAuditTaskNodeDO taskNodeDO = wmCanteenStallAuditFlowCanteenService.createAuditTaskNodeBySubmit(taskDO, auditorBO);

        // 3-创建Ticket审批任务(任务系统)
        Integer ticketId = wmCanteenStallAuditFlowTicketService.createAuditTicket(taskDO, auditorBO, taskNodeDO);

        // 4-更新审批任务子表中的任务系统ID(学校系统)
        wmCanteenStallAuditFlowCanteenService.updateAuditTaskNodeAuditSystemId(ticketId, taskNodeDO);
    }

    /**
     * 生效审批任务(主任务)
     * @param auditTaskBO auditTaskBO
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    public void effectAuditTask(WmCanteenStallAuditTaskBO auditTaskBO) throws WmSchCantException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_2", "com.sankuai.meituan.waimai.customer.service.sc.canteenstall.auditflow.WmCanteenStallAuditTaskService.effectAuditTask(WmCanteenStallAuditTaskBO)");
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_2", "com.sankuai.meituan.waimai.customer.service.sc.canteenstall.auditflow.WmCanteenStallAuditTaskService.effectAuditTask(WmCanteenStallAuditTaskBO)");
        log.info("[WmCanteenStallAuditTaskService.effectAuditTask] input param: auditTaskBO = {}", JSONObject.toJSONString(auditTaskBO));
        // 1-审批任务主表更新任务状态为已通过(食堂系统)
        wmCanteenStallAuditFlowCanteenService.updateAuditTaskAuditStatusByAuditEffect(auditTaskBO);

        // 2-创建档口审批流审批通过信息(食堂系统)
        wmCanteenStallAuditFlowCanteenService.createAuditStreamByAuditEffect(auditTaskBO);

        // 3-更新档口绑定任务审批状态为审批通过(食堂系统)
        wmCanteenStallAuditFlowCanteenService.updateCanteenStallBindAuditStatusByAuditEffect(auditTaskBO);

        // 4-更新档口绑定任务线索跟进状态(食堂系统)
        wmCanteenStallAuditFlowCanteenService.updateCanteenStallBindClueFollowUpStatus(auditTaskBO);
    }


    /**
     * 驳回审批任务
     * @param auditTaskBO auditTaskBO
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    public void rejectAuditTask(WmCanteenStallAuditTaskBO auditTaskBO) throws WmSchCantException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.sc.canteenstall.auditflow.WmCanteenStallAuditTaskService.rejectAuditTask(WmCanteenStallAuditTaskBO)");
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.sc.canteenstall.auditflow.WmCanteenStallAuditTaskService.rejectAuditTask(WmCanteenStallAuditTaskBO)");
        log.info("[WmCanteenStallAuditTaskService.rejectAuditTask] input param: auditTaskBO = {}", JSONObject.toJSONString(auditTaskBO));
        // 1-驱动Gravity对流程驳回结束(Gravity系统)
        wmCanteenStallAuditFlowGravityService.driveGravityProcessByAuditReject(auditTaskBO);

        // 2-审批任务主表更新任务状态为已驳回(学校系统)
        wmCanteenStallAuditFlowCanteenService.updateAuditTaskAuditStatusByAuditReject(auditTaskBO);

        // 3-创建档口审批流审批驳回信息(学校系统)
        wmCanteenStallAuditFlowCanteenService.createAuditStreamByAuditReject(auditTaskBO);

        // 4-根据主任务ID查询子任务Ticket任务信息(任务系统)
        WmTicketDto ticketDto = wmCanteenStallAuditFlowTicketService.getAuditTicketDTO(auditTaskBO);

        // 5-审批任务子表更新任务状态为已驳回(学校系统)
        wmCanteenStallAuditFlowCanteenService.updateAuditTaskNodeByAuditReject(auditTaskBO, ticketDto);

        // 6-更新档口绑定任务主表审批状态为审批驳回(学校系统)
        wmCanteenStallAuditFlowCanteenService.updateCanteenStallBindAuditStatusByAuditReject(auditTaskBO);
    }


    /**
     * 终止审批任务
     * @param auditTaskBO auditTaskBO
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    public void stopAuditTask(WmCanteenStallAuditTaskBO auditTaskBO) throws WmSchCantException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.service.sc.canteenstall.auditflow.WmCanteenStallAuditTaskService.stopAuditTask(WmCanteenStallAuditTaskBO)");
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.service.sc.canteenstall.auditflow.WmCanteenStallAuditTaskService.stopAuditTask(WmCanteenStallAuditTaskBO)");
        log.info("[WmCanteenStallAuditTaskService.stopAuditTask] input param: auditTaskBO = {}", JSONObject.toJSONString(auditTaskBO));
        // 1-驱动Gravity对流程驳回结束(Gravity系统)
        wmCanteenStallAuditFlowGravityService.driveGravityProcessByAuditReject(auditTaskBO);

        // 2-审批任务主表更新任务状态为已终止(学校系统)
        wmCanteenStallAuditFlowCanteenService.updateAuditTaskAuditStatusByAuditStop(auditTaskBO);

        // 3-创建档口审批流审批驳回信息(学校系统)
        wmCanteenStallAuditFlowCanteenService.createAuditStreamByAuditReject(auditTaskBO);

        // 4-根据主任务ID查询子任务Ticket任务信息(任务系统)
        WmTicketDto ticketDto = wmCanteenStallAuditFlowTicketService.getAuditTicketDTO(auditTaskBO);

        // 5-审批任务子表更新任务状态为已终止(学校系统)
        wmCanteenStallAuditFlowCanteenService.updateAuditTaskNodeByAuditStop(auditTaskBO, ticketDto);

        // 6-更新档口绑定任务主表审批状态为审批驳回(学校系统)
        wmCanteenStallAuditFlowCanteenService.updateCanteenStallBindAuditStatusByAuditReject(auditTaskBO);
    }


    /**
     * 根据审批节点和食堂主键ID查询审批人信息
     * @param auditNode 审批节点
     * @param canteenPrimaryId 食堂主键ID
     * @return 审批人信息
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    public WmCanteenStallAuditorBO getAuditTaskAuditorInfo(Integer auditNode, Integer canteenPrimaryId)
            throws WmSchCantException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.service.sc.canteenstall.auditflow.WmCanteenStallAuditTaskService.getAuditTaskAuditorInfo(java.lang.Integer,java.lang.Integer)");
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.service.sc.canteenstall.auditflow.WmCanteenStallAuditTaskService.getAuditTaskAuditorInfo(java.lang.Integer,java.lang.Integer)");
        CanteenStallAuditNodeTypeEnum auditNodeTypeEnum = CanteenStallAuditNodeTypeEnum.getByType(auditNode);
        switch (auditNodeTypeEnum) {
            case SCHOOL_KA:
                return getAuditorBOBySchoolKA(canteenPrimaryId);
            case BD:
                return getAuditorBOByBD(canteenPrimaryId);
            case BDM:
                return getAuditorBOByBDM(canteenPrimaryId);
            default:
                return null;
        }
    }

    /**
     * 校企经理 - 食堂关联的学校的学校负责人
     * @param canteenPrimaryId 食堂主键ID
     * @return WmCanteenStallAuditorBO
     */
    public WmCanteenStallAuditorBO getAuditorBOBySchoolKA(Integer canteenPrimaryId) throws WmSchCantException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.sc.canteenstall.auditflow.WmCanteenStallAuditTaskService.getAuditorBOBySchoolKA(java.lang.Integer)");
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.sc.canteenstall.auditflow.WmCanteenStallAuditTaskService.getAuditorBOBySchoolKA(java.lang.Integer)");
        WmCanteenDB wmCanteenDB = wmCanteenMapper.selectCanteenById(canteenPrimaryId);
        if (wmCanteenDB == null) {
            log.warn("[WmCanteenStallAuditTaskService.getAuditorBOBySchoolKA] wmCanteenDB is null. canteenPrimaryId = {}", canteenPrimaryId);
            return getDefaultAuditorBO();
        }

        WmSchoolDB wmSchoolDB = wmSchoolMapper.selectSchoolById(wmCanteenDB.getSchoolId());
        WmEmploy wmEmploy = wmScEmployAdaptor.getWmEmployByUid(wmSchoolDB.getResponsibleUid());
        if (wmEmploy == null || wmEmploy.getValid() == 0) {
            log.warn("[WmCanteenStallAuditTaskService.getAuditorBOBySchoolKA] wmEmploy is null. canteenPrimaryId = {}", canteenPrimaryId);
            return getDefaultAuditorBO();
        }

        WmCanteenStallAuditorBO auditorBO = new WmCanteenStallAuditorBO();
        auditorBO.setAuditorUid(wmEmploy.getUid());
        auditorBO.setAuditorMis(wmEmploy.getMisId());
        auditorBO.setAuditorName(wmEmploy.getName());
        return auditorBO;
    }

    /**
     * BD - 食堂负责人
     * @param canteenPrimaryId 食堂主键ID
     * @return WmCanteenStallAuditorBO
     */
    public WmCanteenStallAuditorBO getAuditorBOByBD(Integer canteenPrimaryId) throws WmSchCantException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.sc.canteenstall.auditflow.WmCanteenStallAuditTaskService.getAuditorBOByBD(java.lang.Integer)");
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.sc.canteenstall.auditflow.WmCanteenStallAuditTaskService.getAuditorBOByBD(java.lang.Integer)");
        WmCanteenDB wmCanteenDB = wmCanteenMapper.selectCanteenById(canteenPrimaryId);
        if (wmCanteenDB == null || StringUtils.isBlank(wmCanteenDB.getResponsiblePerson())) {
            log.warn("[WmCanteenStallAuditTaskService.getAuditorBOByBD] wmCanteenDB is null. canteenPrimaryId = {}", canteenPrimaryId);
            return getDefaultAuditorBO();
        }

        WmEmploy wmEmploy = wmScEmployAdaptor.getByMisId(wmCanteenDB.getResponsiblePerson());
        if (wmEmploy == null || wmEmploy.getValid() == 0) {
            log.warn("[WmCanteenStallAuditTaskService.getAuditorBOByBD] wmEmploy is null. canteenPrimaryId = {}", canteenPrimaryId);
            return getDefaultAuditorBO();
        }

        WmCanteenStallAuditorBO auditorBO = new WmCanteenStallAuditorBO();
        auditorBO.setAuditorUid(wmEmploy.getUid());
        auditorBO.setAuditorMis(wmEmploy.getMisId());
        auditorBO.setAuditorName(wmEmploy.getName());
        return auditorBO;
    }

    /**
     * BDM - 食堂负责人上级(食堂责任人上级，优先取第一级主要负责人，取不到则取第二级主要负责人)
     * @param canteenPrimaryId 食堂主键ID
     * @return WmCanteenStallAuditorBO
     */
    public WmCanteenStallAuditorBO getAuditorBOByBDM(Integer canteenPrimaryId) throws WmSchCantException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.service.sc.canteenstall.auditflow.WmCanteenStallAuditTaskService.getAuditorBOByBDM(java.lang.Integer)");
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.service.sc.canteenstall.auditflow.WmCanteenStallAuditTaskService.getAuditorBOByBDM(java.lang.Integer)");
        WmCanteenDB wmCanteenDB = wmCanteenMapper.selectCanteenById(canteenPrimaryId);
        if (wmCanteenDB == null || StringUtils.isBlank(wmCanteenDB.getResponsiblePerson())) {
            log.warn("[WmCanteenStallAuditTaskService.getAuditorBOByBDM] wmCanteenDB is null. canteenPrimaryId = {}", canteenPrimaryId);
            return getDefaultAuditorBO();
        }

        WmEmploy wmEmploy = wmScEmployAdaptor.getByMisId(wmCanteenDB.getResponsiblePerson());
        if (wmEmploy == null || wmEmploy.getValid() == 0) {
            log.warn("[WmCanteenStallAuditTaskService.getAuditorBOByBDM] wmEmploy is null. canteenPrimaryId = {}", canteenPrimaryId);
            return getDefaultAuditorBO();
        }

        List<WmVirtualOrg> wmVirtualOrgList =  wmVirtualOrgServiceAdaptor.getOrgsByUid(
                wmEmploy.getUid(),
                WmVirtualOrgSourceEnum.WAIMAI.getSource(),
                WmVirtualOrgRecursiveTypeEnum.NONE.getType()
        );

        if (CollectionUtils.isEmpty(wmVirtualOrgList)) {
            log.warn("[WmCanteenStallAuditTaskService.getAuditorBOByBDM] wmVirtualOrgList is empty. canteenPrimaryId = {}", canteenPrimaryId);
            return getDefaultAuditorBO();
        }

        WmCanteenStallAuditorBO auditorBO = new WmCanteenStallAuditorBO();
        // 1-上一级负责人
        WmEmploy wmEmployOne = wmVirtualOrgServiceAdaptor.getMajorOwnerByOrgId(wmVirtualOrgList.get(0).getParentId());
        if (wmEmployOne != null) {
            auditorBO.setAuditorUid(wmEmployOne.getUid());
            auditorBO.setAuditorMis(wmEmployOne.getMisId());
            auditorBO.setAuditorName(wmEmployOne.getName());
            return auditorBO;
        }

        // 2-上上级负责人
        WmVirtualOrg wmVirtualOrg = wmVirtualOrgServiceAdaptor.getVirtualOrgById(wmVirtualOrgList.get(0).getParentId());
        WmEmploy wmEmployTwo = wmVirtualOrgServiceAdaptor.getMajorOwnerByOrgId(wmVirtualOrg.getParentId());
        if (wmEmployTwo != null) {
            auditorBO.setAuditorUid(wmEmployTwo.getUid());
            auditorBO.setAuditorMis(wmEmployTwo.getMisId());
            auditorBO.setAuditorName(wmEmployTwo.getName());
            return auditorBO;
        }

        return getDefaultAuditorBO();
    }


    /**
     * 获取默认审批人信息
     * @return WmCanteenStallAuditorBO
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    public WmCanteenStallAuditorBO getDefaultAuditorBO() throws WmSchCantException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_1", "com.sankuai.meituan.waimai.customer.service.sc.canteenstall.auditflow.WmCanteenStallAuditTaskService.getDefaultAuditorBO()");
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_1", "com.sankuai.meituan.waimai.customer.service.sc.canteenstall.auditflow.WmCanteenStallAuditTaskService.getDefaultAuditorBO()");
        WmCanteenStallAuditorBO auditorBO = new WmCanteenStallAuditorBO();
        WmEmploy employ = wmScEmployAdaptor.getByMisId(MccScConfig.getCanteenStallDefaultAuditorMis());
        auditorBO.setAuditorUid(employ.getUid());
        auditorBO.setAuditorName(employ.getName());
        auditorBO.setAuditorMis(employ.getMisId());
        return auditorBO;
    }
}