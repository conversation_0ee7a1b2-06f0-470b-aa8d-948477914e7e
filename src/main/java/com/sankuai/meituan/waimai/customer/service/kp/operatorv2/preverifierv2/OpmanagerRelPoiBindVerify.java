package com.sankuai.meituan.waimai.customer.service.kp.operatorv2.preverifierv2;

import com.sankuai.meituan.waimai.customer.dao.WmCustomerPoiDBMapper;
import com.sankuai.meituan.waimai.customer.service.kp.WmCustomerKpPoiService;
import com.sankuai.meituan.waimai.customer.util.ThrowUtil;
import com.sankuai.meituan.waimai.thrift.customer.domain.kp.WmCustomerKp;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Author: 窦旭蒙
 * MIS: douxumeng
 * Date: 2020-07-03 18:01
 * Email: <EMAIL>
 * Desc:
 */
@Service
public class OpmanagerRelPoiBindVerify {

    private static final Logger LOGGER = LoggerFactory.getLogger(OpmanagerRelPoiBindVerify.class);

    @Autowired
    private WmCustomerKpPoiService wmCustomerKpPoiService;

    @Autowired
    private WmCustomerPoiDBMapper wmCustomerPoiDBMapper;

    // 单个添加校验
    public boolean singleQueryWmPoiInfo(Integer customerId, Integer kpId, Long wmPoiId) throws WmCustomerException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.service.kp.operatorv2.preverifierv2.OpmanagerRelPoiBindVerify.singleQueryWmPoiInfo(java.lang.Integer,java.lang.Integer,java.lang.Long)");
        // 门店是否在该客户下
        Integer resultCustomerId = wmCustomerPoiDBMapper.selectCustomerPoiByWmPoiId(customerId, wmPoiId);
        if (resultCustomerId == null || resultCustomerId == 0) {
            ThrowUtil.throwClientError("非本客户关联商家或商家正在解绑中，添加失败");
        }
        // 是否有过绑定记录
        boolean isRel = poiRelKp(customerId, kpId, wmPoiId.toString());
        if (!isRel) {
            ThrowUtil.throwClientError("商家已关联其他KP，添加失败");
        }
        return true;
    }

    // 批量添加校验
    public List<Long> batchBindWmPoi(Integer customerId, Integer kpId, String wmPoiIds) throws WmCustomerException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.kp.operatorv2.preverifierv2.OpmanagerRelPoiBindVerify.batchBindWmPoi(java.lang.Integer,java.lang.Integer,java.lang.String)");
        LOGGER.info("#batchBindWmPoi,customerId={},kpId={},wmPoiIds={}", customerId, kpId, wmPoiIds);
        boolean customerBind = poiBindCustomer(customerId, wmPoiIds);
        if (!customerBind) {
            ThrowUtil.throwClientError("非本客户关联商家或商家正在解绑中，添加失败");
        }
        boolean isRel = poiRelKp(customerId, kpId, wmPoiIds);
        if (!isRel) {
            ThrowUtil.throwClientError("商家已关联其他KP，添加失败");
        }
        return wmCustomerKpPoiService.getPoiList(wmPoiIds);
    }


    // 批量删除校验
    public boolean batchUnbindWmPoi(Integer customerId, String wmPoiIds) throws WmCustomerException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.service.kp.operatorv2.preverifierv2.OpmanagerRelPoiBindVerify.batchUnbindWmPoi(java.lang.Integer,java.lang.String)");
        return poiBindCustomer(customerId, wmPoiIds);
    }


    private boolean poiBindCustomer(Integer customerId, String wmPoiIds) throws WmCustomerException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.service.kp.operatorv2.preverifierv2.OpmanagerRelPoiBindVerify.poiBindCustomer(java.lang.Integer,java.lang.String)");
        List<Long> wmPoiList = wmCustomerKpPoiService.getPoiList(wmPoiIds);
        List<Long> distinctWmPoiIds = wmPoiList.stream().distinct().collect(Collectors.toList());
        Integer bindNum = wmCustomerPoiDBMapper.selectWmPoiCountByCustomerId(customerId, distinctWmPoiIds);
        if (bindNum != wmPoiList.size()) {
            LOGGER.info("wmPoiIds:{}中存在未绑定在customerId:{}中的门店", wmPoiIds, customerId);
            return false;
        }
        return true;
    }

    private boolean poiRelKp(Integer customerId, Integer kpId, String wmPoiIds) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.kp.operatorv2.preverifierv2.OpmanagerRelPoiBindVerify.poiRelKp(java.lang.Integer,java.lang.Integer,java.lang.String)");
        WmCustomerKp kp = new WmCustomerKp();
        kp.setCustomerId(customerId);
        kp.setRelPoiInfo(wmPoiIds);
        kp.setId(kpId);
        return wmCustomerKpPoiService.getPoiRelInfo(kp);
    }

}
