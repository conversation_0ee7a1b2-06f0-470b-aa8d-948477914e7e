package com.sankuai.meituan.waimai.customer.service.customer.check.poi;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.sankuai.meituan.waimai.customer.adapter.BaseInfoQueryPhysicalPoiThriftServiceAdapter;
import com.sankuai.meituan.waimai.customer.config.MccCustomerConfig;
import com.sankuai.meituan.waimai.customer.constant.WmCustomerConstant;
import com.sankuai.meituan.waimai.customer.constant.customer.CustomerPoiBindDecideResultEnum;
import com.sankuai.meituan.waimai.customer.constant.customer.CustomerPoiBindTypeEnum;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerDB;
import com.sankuai.meituan.waimai.customer.domain.poi.WmCustomerPoiCheckBo;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerNewSettleService;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerPoiRelService;
import com.sankuai.meituan.waimai.customer.service.customer.check.CustomerPoiBaseValidator;
import com.sankuai.meituan.waimai.customer.service.customer.check.ICustomerPoiValidator;
import com.sankuai.meituan.waimai.customer.service.gray.WmCustomerGrayService;
import com.sankuai.meituan.waimai.poi.constants.attribute.WmPoiValidEnum;
import com.sankuai.meituan.waimai.poibaseinfo.thrift.domain.WmPhysicalPoiRel;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerAuditStatus;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerConstants;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerRealTypeEnum;
import com.sankuai.meituan.waimai.thrift.domain.WmPoiAggre;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 门店要绑定单店类型的客户校验
 */
@Slf4j
@Service
public class BindDandianValidator extends CustomerPoiBaseValidator implements ICustomerPoiValidator {

    @Autowired
    private WmCustomerPoiRelService wmCustomerPoiRelService;

    @Autowired
    private BaseInfoQueryPhysicalPoiThriftServiceAdapter baseInfoQueryPhysicalPoiThriftServiceAdapter;

    @Autowired
    private WmCustomerNewSettleService wmCustomerNewSettleService;

    @Autowired
    private WmCustomerGrayService customerGrayService;

    // 单店客户类型列表
    private static final List<Integer> dandianTypes = Lists.newArrayList(CustomerRealTypeEnum.DANDIAN.getValue(),
            CustomerRealTypeEnum.DANDIAN_SG.getValue(),
            CustomerRealTypeEnum.DANDIAN_YAOPIN.getValue());

    @Override
    public boolean canCheck(int customerRealType) {
        if (dandianTypes.contains(customerRealType)) {
            return true;
        }
        return false;
    }

    @Override
    public Map<CustomerPoiBindDecideResultEnum, List<Long>> validBind(WmCustomerPoiCheckBo wmCustomerPoiCheckBo) throws WmCustomerException, TException {
        WmCustomerDB wmCustomerDB = wmCustomerPoiCheckBo.getWmCustomerDB();

        //添加客户状态卡控：待发起特批、特批中、特批驳回不允许绑定门店
        Integer auditStatus = wmCustomerDB.getAuditStatus();
        if (CustomerConstants.notAllowBindCustomerAuditStatusList.contains(auditStatus)) {
            String errTips = String.format("客户处于“%s”状态，不支持绑定门店；", CustomerAuditStatus.getByCode(auditStatus).getDesc());
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, errTips);
        }

        checkBind(wmCustomerPoiCheckBo);
        Integer customerRealType = wmCustomerDB.getCustomerRealType();
        Map<CustomerPoiBindDecideResultEnum, List<Long>> map = Maps.newHashMap();
        if (!dandianTypes.contains(customerRealType)) {
            return map;
        }
        // 获取待绑定门店
        List<Long> wmPoiIdList = wmCustomerPoiCheckBo.getWmPoiIdList();
        // 参数非空判断
        if (CollectionUtils.isEmpty(wmPoiIdList)) {
            if (wmCustomerPoiCheckBo.getTypeEnum() == CustomerPoiBindTypeEnum.HIGN_SEA_CHECK) {
                return map;
            } else {
                throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR,
                        "参数不合法");
            }
        }
        // 获取待绑定真实门店
        List<Long> realWmPoiIdList = wmCustomerPoiCheckBo.getRealWmPoiIdList();

        // 获取已绑定门店
        List<Long> hasBindWmPoiIdList = wmCustomerPoiRelService.selectWmPoiIdsByCustomerId(wmCustomerDB.getId());
        // 所有门店
        Set<Long> allWmPoiIdSet = Sets.newHashSet(wmPoiIdList);
        if (CollectionUtils.isNotEmpty(hasBindWmPoiIdList)) {
            allWmPoiIdSet.addAll(hasBindWmPoiIdList);
        }
        // 获取所有门店的物理门店
        Map<Long, Long> physicalPoiMap = getWmPoiMapPhysicalPoi(allWmPoiIdSet);
        // 判断是否所有门店都有物理门店
        boolean isAllHasPhysicalPoi = checkAllHasPhysicalPoi(allWmPoiIdSet, physicalPoiMap);
        // 资质一致性校验
        checkPoiQua(realWmPoiIdList, hasBindWmPoiIdList, wmCustomerDB, isAllHasPhysicalPoi);
        // 校验门店数量
        checkPoiCount(allWmPoiIdSet, Sets.newHashSet(physicalPoiMap.values()), isAllHasPhysicalPoi);
        if (wmCustomerPoiCheckBo.getTypeEnum() != CustomerPoiBindTypeEnum.HIGN_SEA_CHECK &&
                CollectionUtils.isNotEmpty(realWmPoiIdList)) {
            // 校验客户与门店的新结算版本是否一致
            wmCustomerNewSettleService.checkCustomerAndPoiVersion(wmCustomerDB.getMtCustomerId(), wmCustomerDB.getCustomerName(), Sets.newHashSet(realWmPoiIdList));
        }
        map.put(CustomerPoiBindDecideResultEnum.CAN_BIND_DIRECT, wmPoiIdList);
        return map;
    }

    /**
     * 所有门店都有物理门店：
     * 单店类型客户，允许经营门店绑定多个，但经营门店必须所属相同一个物理门店。
     * 如不同物理门店的经营门店绑定时，报错提示“单店客户下只可绑定相同物理门店的经营门店”
     * <p>
     * 非所有门店都有物理门店：
     * 单店类型客户只允许绑定一个门店
     *
     * @param bindWmPoiIds
     * @param physicalPoiSet
     * @param isAllHasPhysicalPoi
     * @throws WmCustomerException
     */
    private void checkPoiCount(Set<Long> bindWmPoiIds, Set<Long> physicalPoiSet, boolean isAllHasPhysicalPoi) throws WmCustomerException {
        if (isAllHasPhysicalPoi) {
            // 门店数量校验：物理门店是否是同一个
            if (physicalPoiSet.size() > 1) {
                throw new WmCustomerException(CustomerConstants.RESULT_CODE_CUSTOMER_REAL_TYPE_ERROR, "单店客户下只可绑定相同物理门店的经营门店");
            }
        } else {
            if (bindWmPoiIds.size() > 1) {
                throw new WmCustomerException(CustomerConstants.RESULT_CODE_CUSTOMER_REAL_TYPE_ERROR, "绑定的客户为单店类型客户，若要绑定多个门店，请先切换客户类型");
            }
        }
    }

    /**
     * 所有门店都有物理门店：
     * 1.1 如门店没有“子门店”标，则资质校验与原逻辑一致：门店资质须与客户资质一致
     * 1.2 如门店有“子门店”标，则判断该客户下是否绑定有：与此子门店同一个物理门店、且有“主站子门店（即主站经营门店）”标、且状态为“上线”的门店。
     * 1）如有，则不检验该子门店资质
     * 2）如没有，则该子门店须遵循原资质校验逻辑：门店资质须与客户资质一致
     * <p>
     * 非所有门店都有物理门店：
     * 待绑定所有门店都须与客户资质一致
     *
     * @param realWmPoiIdList
     * @param hasBindWmPoiIdList
     * @param wmCustomerDB
     * @throws WmCustomerException
     */
    private void checkPoiQua(List<Long> realWmPoiIdList, List<Long> hasBindWmPoiIdList, WmCustomerDB wmCustomerDB, boolean isAllHasPhysicalPoi) throws WmCustomerException {
        if (CollectionUtils.isEmpty(realWmPoiIdList)) {
            return;
        }
        // 获取要进行资质校验的门店
        List<Long> readyQuaWmPoiIdList = Lists.newArrayList(realWmPoiIdList);
        // 所有门店都有物理门店的情况，使用子门店校验逻辑
        if (isAllHasPhysicalPoi) {
            List<Long> subWmPoiIdList = getSubPoi(realWmPoiIdList);
            if (CollectionUtils.isNotEmpty(subWmPoiIdList)) {
                List<Long> onlinMainWmPoiIdList = getOnlineMainPoi(hasBindWmPoiIdList);
                // 有主站门店的，不需要校验资质一致性
                if (CollectionUtils.isNotEmpty(onlinMainWmPoiIdList)) {
                    readyQuaWmPoiIdList.removeAll(subWmPoiIdList);
                }
            }
        }
        validateQua(wmCustomerDB, readyQuaWmPoiIdList);
    }


    /**
     * 获取门店中有子门店标签的门店
     *
     * @param readyWmPoiIdList
     * @return
     */
    private List<Long> getSubPoi(List<Long> readyWmPoiIdList) {
        List<Long> subWmPoiIdList = Lists.newArrayList();
        List<WmPoiAggre> wmPoiAggreList = wmPoiQueryAdapter.getWmPoiAggre(readyWmPoiIdList, WM_POI_FIELDS);
        if (CollectionUtils.isEmpty(wmPoiAggreList)) {
            return subWmPoiIdList;
        }

        // 获取支持的子门店标签
        List<Integer> subLabelIdList = MccCustomerConfig.getSubPoiTagId();
        for (WmPoiAggre wmPoiAggre : wmPoiAggreList) {
            if (MccCustomerConfig.getSubPoiIdentifySwitch()) {
                if (!WmCustomerConstant.POI_NOT_SUB_POI_TYPE.equals(wmPoiAggre.getSub_wm_poi_type())) {
                    subWmPoiIdList.add(wmPoiAggre.getWm_poi_id());
                }
            } else {
                if (StringUtils.isBlank(wmPoiAggre.getLabel_ids())) {
                    continue;
                }
                List<Integer> tagIds = Arrays.stream(wmPoiAggre.getLabel_ids().split(","))
                        .map(Integer::valueOf)
                        .collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(subLabelIdList) &&
                        !Collections.disjoint(tagIds, subLabelIdList)) {
                    subWmPoiIdList.add(wmPoiAggre.getWm_poi_id());
                }
            }
        }
        return subWmPoiIdList;
    }

    /**
     * 获取有主站门店标签且是上线状态的门店
     *
     * @param hasBindWmPoiIdList
     * @return
     */
    private List<Long> getOnlineMainPoi(List<Long> hasBindWmPoiIdList) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.service.customer.check.poi.BindDandianValidator.getOnlineMainPoi(java.util.List)");
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.service.customer.check.poi.BindDandianValidator.getOnlineMainPoi(java.util.List)");
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.service.customer.check.poi.BindDandianValidator.getOnlineMainPoi(java.util.List)");
        List<Long> mainWmPoiIdList = Lists.newArrayList();
        if (CollectionUtils.isEmpty(hasBindWmPoiIdList)) {
            return mainWmPoiIdList;
        }
        List<WmPoiAggre> wmPoiAggreList = wmPoiQueryAdapter.getWmPoiAggre(hasBindWmPoiIdList, WM_POI_FIELDS);
        if (CollectionUtils.isEmpty(wmPoiAggreList)) {
            return mainWmPoiIdList;
        }

        // 获取主站门店标签
        int mainLabelId = MccCustomerConfig.getMainPoiTagId();
        for (WmPoiAggre wmPoiAggre : wmPoiAggreList) {
            if (wmPoiAggre.getValid() != WmPoiValidEnum.ONLINE.getValue()) {
                continue;
            }
            if (StringUtils.isBlank(wmPoiAggre.getLabel_ids())) {
                continue;
            }
            List<Integer> tagIds = Arrays.stream(wmPoiAggre.getLabel_ids().split(","))
                    .map(Integer::valueOf)
                    .collect(Collectors.toList());
            if (tagIds.contains(mainLabelId)) {
                mainWmPoiIdList.add(wmPoiAggre.getWm_poi_id());
            }
        }
        return mainWmPoiIdList;
    }

    /**
     * 获取门店对应的物理门店
     *
     * @param wmPoiIdSet
     * @return
     */
    private Map<Long, Long> getWmPoiMapPhysicalPoi(Set<Long> wmPoiIdSet) throws WmCustomerException {
        Map<Long, Long> map = new HashMap<>();
        if(CollectionUtils.isEmpty(wmPoiIdSet)){
            return map;
        }
        // 获取门店的物理门店
        List<WmPhysicalPoiRel> wmPhysicalPoiRelList = baseInfoQueryPhysicalPoiThriftServiceAdapter.getPhysicalPoiRelList(Lists.newArrayList(wmPoiIdSet));
        if (CollectionUtils.isEmpty(wmPhysicalPoiRelList)) {
            return map;
        }
        for (WmPhysicalPoiRel wmPhysicalPoiRel : wmPhysicalPoiRelList) {
            map.put(wmPhysicalPoiRel.getWmPoiId(), wmPhysicalPoiRel.getWmPhysicalPoiId());
        }
        return map;
    }

    /**
     * 校验是否所有门店都有物理门店
     *
     * @param wmPoiIdSet
     * @param map
     * @return
     */
    private boolean checkAllHasPhysicalPoi(Set<Long> wmPoiIdSet, Map<Long, Long> map) {
        if (CollectionUtils.isEmpty(wmPoiIdSet)) {
            return false;
        }
        if (map == null || map.isEmpty()) {
            return false;
        }
        for (Long wmPoiId : wmPoiIdSet) {
            if (!map.containsKey(wmPoiId)) {
                return false;
            }
        }
        return true;
    }

}
