package com.sankuai.meituan.waimai.customer.service.sc;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.dianping.cat.Cat;
import com.google.common.base.Joiner;
import com.google.common.base.Predicate;
import com.google.common.collect.ImmutableSet;
import com.google.common.collect.Iterators;
import com.google.common.collect.Lists;
import com.google.common.primitives.Longs;
import com.sankuai.meituan.common.json.JSONUtil;
import com.sankuai.meituan.org.opensdk.model.domain.Emp;
import com.sankuai.meituan.waimai.customer.adapter.*;
import com.sankuai.meituan.waimai.customer.constant.YesOrNoEnum;
import com.sankuai.meituan.waimai.customer.constant.sc.*;
import com.sankuai.meituan.waimai.customer.dao.sc.*;
import com.sankuai.meituan.waimai.customer.ddd.adapter.WmPoiQueryAdapter;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerVisitKPProDB;
import com.sankuai.meituan.waimai.customer.domain.sc.*;
import com.sankuai.meituan.waimai.customer.service.sc.auth.WmScSchoolAuthService;
import com.sankuai.meituan.waimai.customer.service.sc.canteen.sensitive.WmScCanteenSensitiveWordsService;
import com.sankuai.meituan.waimai.customer.service.sc.poi.WmScCanteenPoiAttributeService;
import com.sankuai.meituan.waimai.customer.service.sc.school.sensitive.WmScSchoolSensitiveWordsService;
import com.sankuai.meituan.waimai.customer.util.*;
import com.sankuai.meituan.waimai.e.graycenter.sdk.GrayConfigClient;
import com.sankuai.meituan.waimai.e.graycenter.sdk.domain.GrayParams;
import com.sankuai.meituan.waimai.e.graycenter.sdk.exception.InvalidParamGrayException;
import com.sankuai.meituan.waimai.e.graycenter.sdk.exception.KeyNotExistGrayException;
import com.sankuai.meituan.waimai.e.graycenter.sdk.exception.UnexpectedGrayException;
import com.sankuai.meituan.waimai.infra.constants.WmOrgConstant;
import com.sankuai.meituan.waimai.infra.constants.WmVirtualOrgRecursiveTypeEnum;
import com.sankuai.meituan.waimai.infra.domain.*;

import com.sankuai.meituan.waimai.label.thrift.domain.WmPoiLabelRel;
import com.sankuai.meituan.waimai.label.thrift.exception.WmServerException;
import com.sankuai.meituan.waimai.operation.label.constants.LabelSubjectTypeEnum;
import com.sankuai.meituan.waimai.label.thrift.domain.WmLabelClassFullFormat;
import com.sankuai.meituan.waimai.label.thrift.domain.WmPoiLabel;
import com.sankuai.meituan.waimai.thrift.bo.WmCustomerDiffCellBo;
import com.sankuai.meituan.waimai.thrift.customer.constant.*;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.*;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.WmCustomerContractorClueDetailBO;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.WmCustomerContractorPoiQueryListBO;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.WmCustomerContractorPoiQueryParamBO;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.WmCustomerContractorPoiQueryResultBO;
import com.sankuai.meituan.waimai.thrift.customer.domain.kp.WmCustomerKp;
import com.sankuai.meituan.waimai.thrift.customer.domain.sc.*;
import com.sankuai.meituan.waimai.thrift.domain.WmPoiAggre;
import com.sankuai.meituan.waimai.thrift.exception.WmSchCantException;
import com.sankuai.meituan.waimai.thrift.util.DiffUtil;
import com.sankuai.meituan.waimai.thrift.util.ObjectUtil;
import com.sankuai.meituan.waimai.util.DaxiangUtilV2;
import com.sankuai.waimai.crm.authenticate.client.service.response.AssertResult;
import com.sankuai.waimaisales.highsea.client.exception.HighseaThriftServiceException;
import com.sankuai.waimaisales.highsea.client.schoolyard.dto.ClueDto;
import com.sankuai.waimaisales.highsea.client.schoolyard.dto.KpDto;
import com.sankuai.waimaisales.highsea.client.schoolyard.service.SchoolyardThriftService;
import com.sankuai.meituan.waimai.label.thrift.service.WmLabelInnerThriftService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.thrift.TException;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Nullable;
import java.util.*;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.sankuai.meituan.waimai.customer.constant.ScFieldConstant.schoolAorMsgField;
import static com.sankuai.meituan.waimai.customer.service.sc.thrift.WmSchoolThriftServiceImpl.SCHOOLID_BEGIN;
import static com.sankuai.meituan.waimai.thrift.constants.WmPoiFieldQueryConstant.*;
import static com.sankuai.meituan.waimai.thrift.customer.constant.sc.ScConstants.SC_SCHOOL_LOG;
import static com.sankuai.meituan.waimai.thrift.customer.constant.sc.WmScCodeConstants.*;
import static com.sankuai.meituan.waimai.thrift.customer.constant.sc.WmScCodeConstants.ORG_EXCEPTION;

/**
 * 校园食堂-学校业务逻辑
 */
@Slf4j
@Service
public class WmScSchoolService extends WmSchoolServerService {

    /**
     * 区县级城市(三级城市)
     */
    public static final Integer CITY_AD_LEVEL_COUNTY = 3;

    @Autowired
    private WmScCanteenService wmScCanteenService;

    @Autowired
    private WmScCanteenPoiAuditDetailService wmScCanteenPoiAuditDetailService;

    @Autowired
    private WmPoiSearchAdapter wmPoiSearchAdapter;

    @Autowired
    private WmCanteenMapper canteenMapper;

    @Autowired
    private WmPoiQueryAdapter wmPoiQueryAdapter;

    @Autowired
    private WmEmployClient wmEmployClient;

    @Autowired
    private WmSchoolMapper wmSchoolMapper;

    @Autowired
    private WmScSchoolExtensionMapper wmScSchoolExtensionMapper;

    @Autowired
    private WmScLogService wmScLogService;

    @Autowired
    private SchoolyardThriftService schoolyardThriftService;

    @Autowired
    private WmScCanteenSensitiveWordsService wmScCanteenSensitiveWordsService;

    @Autowired
    private WmScSchoolSensitiveWordsService wmScSchoolSensitiveWordsService;

    @Autowired
    private WmAorServiceAdapter wmAorServiceAdapter;

    @Autowired
    private WmVirtualOrgServiceAdaptor wmVirtualOrgServiceAdaptor;

    @Autowired
    private CityCommonServiceAdapter cityCommonServiceAdapter;

    @Autowired
    private WmLabelInnerThriftService.Iface wmLabelInnerThriftService;

    @Autowired
    private WmSchoolServerService wmSchoolServerService;

    @Autowired
    private WmScTairService wmScTairService;

    @Autowired
    private WmScSchoolAuthService wmScSchoolAuthService;

    @Autowired
    private WmPoiFlowlineLabelThriftServiceAdapter wmPoiFlowlineLabelThriftServiceAdapter;

    @Autowired
    private WmScCanteenPoiAttributeMapper wmScCanteenPoiAttributeMapper;

    @Autowired
    private WmScCanteenPoiAttributeService wmScCanteenPoiAttributeService;

    @Autowired
    private WmScEnumDictMapper wmScEnumDictMapper;

    @Autowired
    private WmAgentAorServiceAdapter wmAgentAorServiceAdapter;

    /**
     * 添加学校时的加锁前缀
     */
    public static final String SCHOOL_INSERT_LOCKKEY_PREFIX = "school_clue_id_";

    private static final ImmutableSet<String> WM_CUSTOMER_CONTRACTOR_POI_FIELD_SET = ImmutableSet.of(
            WM_POI_FIELD_WM_POI_ID,
            WM_POI_FIELD_NAME,
            WM_POI_FIELD_ADDRESS,
            WM_POI_FIELD_OWNER_UID,
            WM_POI_FIELD_VALID
    );

    /**
     * 月营业渗透率低值 = 0.4(用于计算学校生命周期)
     */
    private static final double MONTH_BIZ_PENERATION_RATE_LOW = 0.4;
    /**
     * 月营业渗透率高值 = 0.75(用于计算学校生命周期)
     */
    private static final double MONTH_BIZ_PENERATION_RATE_HIGH = 0.75;
    /**
     * 自然月营业档口数 = 10(用于计算学校合作方式)
     */
    private static final int MONTH_STALL_NUM = 10;
    /**
     * 市级城市(二级城市)
     */
    public static final Integer CITY_AD_LELEL_CITY = 2;

    /**
     * 查询学校
     *
     * @param idList
     * @return
     */
    public List<WmSchoolDB> findByIdList(List<Integer> idList) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.sc.WmScSchoolService.findByIdList(java.util.List)");
        if (CollectionUtils.isEmpty(idList)) {
            return Lists.newArrayList();
        }
        WmSchoolDB wmSchoolDB = new WmSchoolDB();
        wmSchoolDB.setIdList(idList);
        List<WmSchoolDB> list= wmSchoolMapper.selectSchoolList(wmSchoolDB);
        wmScSchoolSensitiveWordsService.readWhenSelect(list);
        return list;
    }

    /**
     * 根据学校物理ID查询学校
     *
     * @param id 学校主键ID
     * @return WmSchoolDB
     */
    public WmSchoolDB findByIdList(Integer id) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.service.sc.WmScSchoolService.findByIdList(java.lang.Integer)");
        if (id == null || id <= 0) {
            return null;
        }
        List<Integer> idList = new ArrayList<>();
        idList.add(id);
        List<WmSchoolDB> list = findByIdList(idList);
        if (CollectionUtils.isNotEmpty(list)) {
            return list.get(0);
        }
        return null;
    }

    /**
     * 查询承销商关联的门店列表
     *
     * @param param
     * @return
     * @throws TException
     * @throws WmSchCantException
     */
    public WmCustomerContractorPoiQueryListBO contractorPoiList(WmCustomerContractorPoiQueryParamBO param) throws TException, WmSchCantException {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.meituan.waimai.customer.service.sc.WmScSchoolService.contractorPoiList(com.sankuai.meituan.waimai.thrift.customer.domain.customer.WmCustomerContractorPoiQueryParamBO)");
        log.info("[WmScSchoolService.contractorPoiList] input param = {}", JSON.toJSONString(param));
        WmCustomerContractorPoiQueryListBO result = new WmCustomerContractorPoiQueryListBO();
        result.setCount(0);
        result.setList(Lists.newArrayList());
        checkParam(param);
        WmCanteenDB wmCanteenDb = new WmCanteenDB();
        wmCanteenDb.setContractorId(param.getCustomerPrimaryId());
        if (param.getSchoolPrimaryId() != null) {
            wmCanteenDb.setSchoolId(param.getSchoolPrimaryId());
        }
        if (param.getCanteenPrimaryId() != null) {
            wmCanteenDb.setCanteenId(param.getCanteenPrimaryId());
        }
        wmCanteenDb.setEffective((int) EffectiveStatusEnum.EFFECTIVE.getType());
        // 查询食堂集合
        List<WmCanteenDB> canteenDBList = canteenMapper.selectCanteenList(wmCanteenDb);
        wmScCanteenSensitiveWordsService.readWhenSelect(canteenDBList);
        if (CollectionUtils.isEmpty(canteenDBList)) {
            return result;
        }
        List<Integer> canteenPrimaryIdList = wmScCanteenService.getPrimaryIdList(canteenDBList);
        List<Long> canteenPrimaryIdLongList = ListUtil.convertIntToLong(canteenPrimaryIdList);
        // 查询食堂关联的学校信息
        List<Integer> schoolPrimaryIdList = wmScCanteenService.getSchoolPrimaryIdList(canteenDBList);
        List<WmSchoolDB> schoolList = findByIdList(schoolPrimaryIdList);
        // 查询食堂关联的有效门店ID集合
        List<Long> wmPoiIdList = wmScCanteenPoiAuditDetailService.getWmPoiIdsByCanteenId(canteenPrimaryIdLongList);
        if (CollectionUtils.isEmpty(wmPoiIdList)) {
            return result;
        }
        if (param.getWmPoiPrimaryId() != null) {
            if(!wmPoiIdList.contains(param.getWmPoiPrimaryId())) {
                return result;
            } else {
                wmPoiIdList = new ArrayList<>();
                wmPoiIdList.add(param.getWmPoiPrimaryId());
            }
        }
        // 通过poisearch过滤最终的门店ID
        List<Long> wmPoiIdSearchList = wmPoiSearchAdapter.searchPoi(wmPoiIdList, param.getWmOnlineStatus());
        Collections.sort(wmPoiIdSearchList, new Comparator<Long>() {
            public int compare(Long o1, Long o2) {
                return o2 > o1 ? 1 : -1;
            }
        });
        if (CollectionUtils.isEmpty(wmPoiIdSearchList)) {
            return result;
        }
        // 门店Id内存分页
        int pageSize = param.getPageSize();
        int pageNum = param.getPageNo() - 1;
        List<List<Long>> partitionList = Lists.partition(wmPoiIdSearchList, pageSize);
        if (pageNum > partitionList.size() - 1) {
            return result;
        }
        List<Long> wmPoiIdPageList = partitionList.get(pageNum);

        // 查询门店与食堂绑定关系
        List<WmScCanteenPoiAttributeDO> attributeDOList = wmScCanteenPoiAttributeMapper.selectByWmPoiIdList(wmPoiIdPageList);
        if (CollectionUtils.isEmpty(attributeDOList)) {
            return result;
        }
        // 通过poiquery查询门店信息
        List<WmPoiAggre> list = wmPoiQueryAdapter.getWmPoiAggre(wmPoiIdPageList, WM_CUSTOMER_CONTRACTOR_POI_FIELD_SET);
        Collections.sort(list, new Comparator<WmPoiAggre>() {
            public int compare(WmPoiAggre o1, WmPoiAggre o2) {
                return o2.getWm_poi_id() > o1.getWm_poi_id() ? 1 : -1;
            }
        });
        if (CollectionUtils.isEmpty(list)) {
            return result;
        }
        result.setCount(wmPoiIdSearchList.size());
        List<WmCustomerContractorPoiQueryResultBO> resultList = makeResultNew(attributeDOList, canteenDBList, schoolList, list);
        Set<Integer> schoolNum = new HashSet<>();
        Set<Integer> canteenNum = new HashSet<>();
        for (WmCanteenDB canteenDB : canteenDBList) {
            canteenNum.add(canteenDB.getCanteenId());
            schoolNum.add(canteenDB.getSchoolId());
        }
        result.setList(resultList);
        result.setWmPoiNum(wmPoiIdList.size());
        result.setSchoolNum(schoolNum.size());
        result.setCanteenNum(canteenNum.size());
        return result;
    }

    /**
     * 组织结果
     */
    private List<WmCustomerContractorPoiQueryResultBO> makeResultNew(List<WmScCanteenPoiAttributeDO> relationList,
                                                                     List<WmCanteenDB> canteenDBList,
                                                                     List<WmSchoolDB> schoolList,
                                                                     List<WmPoiAggre> list) {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.service.sc.WmScSchoolService.makeResultNew(java.util.List,java.util.List,java.util.List,java.util.List)");
        List<WmCustomerContractorPoiQueryResultBO> result = new ArrayList<>();
        if (CollectionUtils.isEmpty(list) || CollectionUtils.isEmpty(relationList)) {
            return Lists.newArrayList();
        }
        List<String> misList = new ArrayList<>();
        for (WmSchoolDB wmSchoolDB : schoolList) {
            misList.add(wmSchoolDB.getResponsiblePerson());
        }
        for (WmCanteenDB wmCanteenDB : canteenDBList) {
            misList.add(wmCanteenDB.getResponsiblePerson());
        }
        Map<String, Emp> wmEmployMap = new HashMap<>();

        List<Emp> empList = wmEmployClient.getEmp(misList);
        if (CollectionUtils.isNotEmpty(empList)) {
            wmEmployMap = empList.stream().collect(Collectors.toMap(Emp::getMis, emp -> emp));
        }

        Map<Integer, WmCanteenDB> wmCanteenDBMap = canteenDBList.stream()
                .collect(Collectors.toMap(WmCanteenDB::getId, wmCanteenDB -> wmCanteenDB));

        for (WmPoiAggre aggre : list) {
            WmCustomerContractorPoiQueryResultBO bo = new WmCustomerContractorPoiQueryResultBO();
            bo.setWmPoiId(aggre.getWm_poi_id());
            bo.setName(aggre.getName());
            bo.setWmOnlineStatus(aggre.getValid());
            if (aggre.getOwner_uid() > 0) {
                // 查询mis号
                WmEmploy employ = wmEmployClient.getByIdWithoutException((int) aggre.getOwner_uid());
                bo.setWmPoiOwenMis(employ.getMisId());
                String wmPoiOwenName = employ.getName() + "(" + employ.getMisId() + ")";
                bo.setWmPoiOwenName(wmPoiOwenName);
            }
            // 组织食堂名字
            Integer canteenPrimaryId = wmScCanteenPoiAuditDetailService.findCanteenFromListByWmPoiIdNew(relationList, aggre.getWm_poi_id());
            bo.setScCanteenName(wmScCanteenService.findCanteenNameFromListById(canteenDBList, canteenPrimaryId));
            // 所属食堂
            WmCanteenDB wmCanteenDB = wmCanteenDBMap.get(canteenPrimaryId);
            if (wmCanteenDB != null) {
                // 食堂合作状态
                int canteenStatus = wmCanteenDB.getCanteenStatus();
                bo.setCanteenStatus(getCanteenStatus(canteenStatus));
                Emp emp = wmEmployMap.get(wmCanteenDB.getResponsiblePerson());
                if (emp == null) {
                    bo.setCanteenResponsiblePersonName(String.valueOf(wmCanteenDB.getResponsiblePerson()));;
                } else {
                    String canteenResponsiblePersonName = emp.getName() + "(" + wmCanteenDB.getResponsiblePerson() + ")";
                    bo.setCanteenResponsiblePersonName(canteenResponsiblePersonName);
                }
                bo.setCanteenId(wmCanteenDB.getId());
            }

            // 组织学校名字和学校责任人
            Integer schoolPrimaryId = wmScCanteenService.findSchoolIdFromListById(canteenDBList, canteenPrimaryId);
            WmSchoolDB school = findSchoolFromListById(schoolList, schoolPrimaryId);
            if (school != null) {
                bo.setSchoolName(school.getSchoolName());
                bo.setSchoolResponsiblePersonMis(school.getResponsiblePerson());
                bo.setSchoolWmCoStatus(getSchoolWmCoStatus(school.getWmCoStatus()));
                Emp emp = wmEmployMap.get(school.getResponsiblePerson());
                if (emp == null) {
                    bo.setSchoolResponsiblePersonName(String.valueOf(school.getResponsiblePerson()));;
                } else {
                    String schoolResponsiblePersonName = emp.getName() + "(" + school.getResponsiblePerson() + ")";
                    bo.setSchoolResponsiblePersonName(schoolResponsiblePersonName);
                }
                bo.setSchoolId(school.getId());
                bo.setSchoolWdcClueId(String.valueOf(school.getWdcClueId()));
            }
            result.add(bo);
        }
        return result;
    }


    /**
     * 获取食堂合作状态
     */
    private String getCanteenStatus(int type){
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.service.sc.WmScSchoolService.getCanteenStatus(int)");
        return CanteenStatusEnum.getByType(type).getName();
    }

    /**
     * 获取学校合作状态
     */
    private String getSchoolWmCoStatus(int type){
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.service.sc.WmScSchoolService.getSchoolWmCoStatus(int)");
        return SchoolWmCoStatusEnum.getByType(type).getName();
    }

    /**
     * 在学校集合中根据学校主键ID查找食堂对象
     *
     * @param list            学校集合
     * @param schoolPrimaryId 学校主键
     */
    public WmSchoolDB findSchoolFromListById(List<WmSchoolDB> list, Integer schoolPrimaryId) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.meituan.waimai.customer.service.sc.WmScSchoolService.findSchoolFromListById(java.util.List,java.lang.Integer)");
        if (org.apache.commons.collections.CollectionUtils.isEmpty(list) || schoolPrimaryId == null) {
            return null;
        }
        Optional<WmSchoolDB> op = list.stream()
                .filter(wmSchoolDB -> wmSchoolDB.getId() == schoolPrimaryId)
                .findFirst();
        if (op.isPresent()) {
            return op.get();
        }
        return null;
    }

    /**
     * 参数校验
     */
    private void checkParam(WmCustomerContractorPoiQueryParamBO param) throws WmSchCantException {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.meituan.waimai.customer.service.sc.WmScSchoolService.checkParam(com.sankuai.meituan.waimai.thrift.customer.domain.customer.WmCustomerContractorPoiQueryParamBO)");
        if (param == null) {
            throw new WmSchCantException(BIZ_PARA_ERROR, "参数不合法");
        }
        if (param.getCustomerPrimaryId() == null || param.getCustomerPrimaryId() <= 0) {
            throw new WmSchCantException(BIZ_PARA_ERROR, "客户ID未传");
        }
        if (param.getPageNo() == null || param.getPageNo() <= 0) {
            param.setPageNo(0);
        }
        if (param.getPageSize() == null || param.getPageSize() <= 0) {
            param.setPageSize(10);
        }
    }

    /**
     * 保存学校信息时的校验
     * @param schoolBo schoolBo
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    public void checkSaveSchoolInfo(SchoolBo schoolBo) throws WmSchCantException {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.sc.WmScSchoolService.checkSaveSchoolInfo(com.sankuai.meituan.waimai.thrift.customer.domain.sc.SchoolBo)");
        // 学校名称重复校验
        List<WmSchoolDB> wmSchoolDBIns = wmSchoolMapper.selectValidSchoolByName(schoolBo.getSchoolName());
        wmScSchoolSensitiveWordsService.readWhenSelect(wmSchoolDBIns);
        for (WmSchoolDB wmSchoolDB : wmSchoolDBIns) {
            if (wmSchoolDB.getId() != schoolBo.getId() && wmSchoolDB.getSchoolName().equals(schoolBo.getSchoolName())) {
                throw new WmSchCantException(SC_REPEAT, "已存在学校名称为“" + schoolBo.getSchoolName() + "”的学校，不可重复新建");
            }
        }
        // 合同编号重复校验
        WmSchoolDB schoolByContractNum = wmSchoolServerService.getByContractNum(schoolBo.getContractNum());
        if (schoolByContractNum != null && schoolByContractNum.getId() != schoolBo.getId()) {
            throw new WmSchCantException(SC_REPEAT, "合同编号重复：" + schoolBo.getContractNum());
        }
        // 线索ID重复校验
        WmSchoolDB schoolByWdcClueId = wmSchoolServerService.getByWdcClueId(schoolBo.getWdcClueId());
        if (schoolByWdcClueId != null && schoolByWdcClueId.getId() != schoolBo.getId()) {
            throw new WmSchCantException(SC_REPEAT, "线索ID重复：" + schoolBo.getWdcClueId());
        }
    }

    /**
     * 设置学校合作状态
     * @param schoolBo schoolBo
     */
    public void setWmCoStatus(SchoolBo schoolBo) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.service.sc.WmScSchoolService.setWmCoStatus(com.sankuai.meituan.waimai.thrift.customer.domain.sc.SchoolBo)");
        schoolBo.setWmCoStatus(getSchoolWmCoStatus(schoolBo));
    }

    /**
     * 计算学校合作状态
     * @param schoolBo schoolBo
     * @return 学校合作状态枚举值
     */
    public Integer getSchoolWmCoStatus(SchoolBo schoolBo) {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.service.sc.WmScSchoolService.getSchoolWmCoStatus(com.sankuai.meituan.waimai.thrift.customer.domain.sc.SchoolBo)");
        SchoolCooperateTypeEnum schoolCooperateTypeEnum = SchoolCooperateTypeEnum.getByType(schoolBo.getSchoolCooperateType());
        if (schoolCooperateTypeEnum == null) {
            return WmCoStatusEnum.NO.getCode();
        }
        // 若学校合作方式中有合同、授权、系统默认其中一个值, 则合作状态就是已合作
        if (schoolCooperateTypeEnum == SchoolCooperateTypeEnum.AUTH
                || schoolCooperateTypeEnum == SchoolCooperateTypeEnum.CONTRACT
                || schoolCooperateTypeEnum == SchoolCooperateTypeEnum.DEFAULT_COOPERATE) {
            return WmCoStatusEnum.YES.getCode();
        }
        // 若学校合作方式是未知, 则合作状态就是未合作
        return WmCoStatusEnum.NO.getCode();
    }

    /**
     * 查询公海线索
     */
    private ClueDto getWdcClueDetail(Long clueId) throws WmSchCantException {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.service.sc.WmScSchoolService.getWdcClueDetail(java.lang.Long)");
        if (clueId == null || clueId <= 0) {
            throw new WmSchCantException(BIZ_PARA_ERROR, "线索ID未传");
        }
        ClueDto clue = null;
        try {
            clue = schoolyardThriftService.getClue(clueId);
            log.info("clueDetail::clueId = {},clue={}", clueId, JSON.toJSONString(clue));
        } catch (HighseaThriftServiceException e) {
            log.error("clueDetail::clueId = {},code={},msg={}", clueId, e.getCode(), e.getMessage());
            throw new WmSchCantException(BIZ_PARA_ERROR, e.getMessage());
        } catch (TException e) {
            log.error("initSchoolClueId::clueId = {}", clueId, e);
            throw new WmSchCantException(WmScCodeConstants.SERVER_ERROR, "调用公私海获取线索接口异常");
        }
        if (clue == null) {
            throw new WmSchCantException(BIZ_PARA_ERROR, "查询不到线索");
        }
        return clue;
    }

    /**
     * 创建学校时查询公私海线索
     */
    public WmScSchoolClueDetailBO clueDetail(Long clueId) throws WmSchCantException {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.sc.WmScSchoolService.clueDetail(java.lang.Long)");
        log.info("clueDetail::clueId = {}", clueId);
        WmScSchoolClueDetailBO result = new WmScSchoolClueDetailBO();
        ClueDto clue = getWdcClueDetail(clueId);

        if (!WdcClueTypeEnum.SCHOOL.getCode().equals(clue.getType())) {
            throw new WmSchCantException(BIZ_PARA_ERROR, "查询不到线索");
        }
        if (StringUtils.isBlank(clue.getName()) || clue.getName().length() > 100) {
            throw new WmSchCantException(BIZ_PARA_ERROR, "线索中学校名字为空或者长度超过100");
        }
        if (StringUtils.isBlank(clue.getAddress())) {
            throw new WmSchCantException(BIZ_PARA_ERROR, "线索中的学校地址不合法");
        }
        if (clue.getPeopleNum() == null || clue.getPeopleNum() <= 0) {
            throw new WmSchCantException(BIZ_PARA_ERROR, "线索中的学校在校师生人数不合法");
        }
        if (clue.getSchoolType() == null || !SchoolTypeCrmWdcRelEnum.enumPreWdcMap.containsKey(clue.getSchoolType())) {
            throw new WmSchCantException(BIZ_PARA_ERROR, "线索中的学校类型不合法");
        }
        if (clue.getDeliveryType() != null && SchoolDeliveryStatusEnum.getByType(clue.getDeliveryType()) == null) {
            throw new WmSchCantException(BIZ_PARA_ERROR, "线索中的配送状况不合法");
        }
        List<Integer> allowDistributionEntryList = Lists.newArrayList(0, 1, 2);
        if (clue.getAllowDistributionEntry() != null && !allowDistributionEntryList.contains(clue.getAllowDistributionEntry())) {
            throw new WmSchCantException(BIZ_PARA_ERROR, "线索中的校外是否可配送进校不合法");
        }
        if (clue.getSiteId() != null && clue.getSiteId().toString().length() != 7) {
            throw new WmSchCantException(BIZ_PARA_ERROR, "线索中的配送站点ID不是7位数字");
        }
        if (StringUtils.isNotBlank(clue.getDeliveryTypeInfo()) && clue.getDeliveryTypeInfo().length() > 100) {
            throw new WmSchCantException(BIZ_PARA_ERROR, "线索中的配送状况其他最大长度不能超过100");
        }
        result.setSchoolType(SchoolTypeCrmWdcRelEnum.enumPreWdcMap.get(clue.getSchoolType()).getScmSchoolTypeId());
        result.setDeliveryStatusOther(clue.getDeliveryTypeInfo());
        result.setSiteId(clue.getSiteId());
        result.setOutDeliveryIn(clue.getAllowDistributionEntry());
        // 如果是否可配送到校内是默认值则返回null（展示层不展示默认值）
        if (OutDeliveryInEnum.DEFAULT.equals(OutDeliveryInEnum.getByType(clue.getAllowDistributionEntry()))) {
            result.setOutDeliveryIn(null);
        }
        result.setDeliveryStatus(clue.getDeliveryType() );
        result.setTeaStuNum(clue.getPeopleNum());
        result.setSchoolAddress(clue.getAddress());
        result.setSchoolName(clue.getName());
        KpDto KpDto = clue.getKpDto();
        if (KpDto != null) {
            if (StringUtils.isNotBlank(KpDto.getName())
                    && KpDto.getName().length() > 50) {
                throw new WmSchCantException(BIZ_PARA_ERROR, "线索中学校KP长度超过50");
            }
            if (StringUtils.isNotBlank(KpDto.getPhone())
                    && (KpDto.getPhone().length() != 11 || Longs.tryParse(KpDto.getPhone()) == null)) {
                throw new WmSchCantException(BIZ_PARA_ERROR, "学校KP联系电话不等于11位数字");
            }
            result.setSchoolKp(KpDto.getName());
            result.setSchoolKpNum(KpDto.getPhone());
        }
        ObjectUtil.defaultValue(result);
        log.info("clueDetail::clueId = {},result={}", clueId, JSON.toJSONString(result));
        return result;
    }

    /**
     * 创建食堂承包商时查询公私海线索
     *
     * @param clueId 线索ID
     */
    public WmCustomerContractorClueDetailBO clueDetailForContractor(Long clueId) throws WmSchCantException {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.sc.WmScSchoolService.clueDetailForContractor(java.lang.Long)");
        log.info("clueDetailForContractor::clueId = {}", clueId);
        WmCustomerContractorClueDetailBO result = new WmCustomerContractorClueDetailBO();
        ClueDto clue = getWdcClueDetail(clueId);
        if (!WdcClueTypeEnum.CONTRACTOR.getCode().equals(clue.getType())) {
            throw new WmSchCantException(BIZ_PARA_ERROR, "查询不到线索");
        }
        result.setWdcClueId(clueId.toString());
        if (CustomerType.getByCode(clue.getSubType()) == null) {
            throw new WmSchCantException(BIZ_PARA_ERROR, "线索中证件类型不合法");
        }
        result.setCustomerType(clue.getSubType());
        if (StringUtils.isBlank(clue.getName()) || clue.getName().length() > 50) {
            throw new WmSchCantException(BIZ_PARA_ERROR, "线索中承包商名字为空或长度大于50");
        }
        result.setCustomerName(clue.getName());
        if (clue.getCityId() == 0) {
            throw new WmSchCantException(BIZ_PARA_ERROR, "线索中二级物理城市ID不存在");
        }
        result.setHqSecondCityId(clue.getCityId());
        if (StringUtils.isBlank(clue.getAddress()) || clue.getAddress().length() > 200) {
            throw new WmSchCantException(BIZ_PARA_ERROR, "线索中总部详细地址为空或者长度超过200");
        }
        result.setHqDetailAddress(clue.getAddress());
        KpDto KpDto = clue.getKpDto();
        if (KpDto != null) {
            // type 1-拜访
            if (KpDto.getType() == null || KpDto.getType() != 1) {
                throw new WmSchCantException(BIZ_PARA_ERROR, "线索中kp类型不合法");
            }
            if (StringUtils.isNotBlank(KpDto.getName()) && KpDto.getName().length() > 20) {
                throw new WmSchCantException(BIZ_PARA_ERROR, "线索中kp名字的长度大于20");
            }
            if (StringUtils.isNotBlank(KpDto.getPhone()) && (KpDto.getPhone().length() != 11 || Longs.tryParse(KpDto.getPhone()) == null)) {
                throw new WmSchCantException(BIZ_PARA_ERROR, "线索中kp手机号不是11位数字");
            }
            if (StringUtils.isNotBlank(KpDto.getEmail()) && KpDto.getEmail().length() > 32) {
                throw new WmSchCantException(BIZ_PARA_ERROR, "线索中kp邮箱长度超过32");
            }
            if (StringUtils.isNotBlank(KpDto.getPost()) && KpDto.getPost().length() > 20) {
                throw new WmSchCantException(BIZ_PARA_ERROR, "线索中kp职务长度超过20");
            }
            if (StringUtils.isNotBlank(KpDto.getHobby()) && KpDto.getHobby().length() > 200) {
                throw new WmSchCantException(BIZ_PARA_ERROR, "线索中kp爱好长度超过200");
            }
            if (StringUtils.isNotBlank(KpDto.getHabit()) && KpDto.getHabit().length() > 200) {
                throw new WmSchCantException(BIZ_PARA_ERROR, "线索中kp习惯长度超过200");
            }
            if (StringUtils.isNotBlank(KpDto.getGuestRelationship()) && KpDto.getGuestRelationship().length() > 200) {
                throw new WmSchCantException(BIZ_PARA_ERROR, "线索中kp客情关系长度超过200");
            }
            if (StringUtils.isNotBlank(KpDto.getBusinessScope()) && KpDto.getBusinessScope().length() > 200) {
                throw new WmSchCantException(BIZ_PARA_ERROR, "线索中kp业务范围长度超过200");
            }
        }
        return result;
    }


    /**
     * 获取公私海承销商拜访KP信息
     */
    public WmCustomerKp getWdcContractorKp(Long clueId) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.meituan.waimai.customer.service.sc.WmScSchoolService.getWdcContractorKp(java.lang.Long)");
        WmCustomerKp kp = new WmCustomerKp();
        ObjectUtil.defaultValue(kp);
        kp.setWdcClueId(clueId);
        kp.setKpType(KpTypeEnum.VISITKP.getType());
        kp.setSignerType(KpSignerTypeEnum.LEGAL.getType());
        kp.setCertType(CertTypeEnum.ID_CARD.getType());
        kp.setState(KpSignerStateMachine.NO_DATA.getState());
        try {
            ClueDto clue = getWdcClueDetail(clueId);
            if (!WdcClueTypeEnum.CONTRACTOR.getCode().equals(clue.getType())) {
                return kp;
            }
            KpDto KpDto = clue.getKpDto();
            if (KpDto != null) {
                // type 1-拜访
                if (KpDto.getType() == null || KpDto.getType() != 1) {
                    return kp;
                }
                if (StringUtils.isNotBlank(KpDto.getName()) && KpDto.getName().length() <= 20) {
                    kp.setCompellation(KpDto.getName());
                }
                if (StringUtils.isNotBlank(KpDto.getPhone()) && KpDto.getPhone().length() == 11 && Longs.tryParse(KpDto.getPhone()) != null) {
                    kp.setPhoneNum(KpDto.getPhone());
                }
                if (StringUtils.isNotBlank(KpDto.getEmail()) && KpDto.getEmail().length() <= 32) {
                    kp.setEmail(KpDto.getEmail());
                }
                WmCustomerVisitKPProDB visitKp = new WmCustomerVisitKPProDB();
                if (StringUtils.isNotBlank(KpDto.getPost()) && KpDto.getPost().length() <= 20) {
                    visitKp.setPost(KpDto.getPost());
                }
                if (StringUtils.isNotBlank(KpDto.getHobby()) && KpDto.getHobby().length() <= 200) {
                    visitKp.setHabit(KpDto.getHobby());
                }
                if (StringUtils.isNotBlank(KpDto.getHabit()) && KpDto.getHabit().length() <= 200) {
                    visitKp.setHabit(KpDto.getHabit());
                }
                if (StringUtils.isNotBlank(KpDto.getGuestRelationship()) && KpDto.getGuestRelationship().length() <= 200) {
                    visitKp.setCustomerRelation(KpDto.getGuestRelationship());
                }
                if (StringUtils.isNotBlank(KpDto.getBusinessScope()) && KpDto.getBusinessScope().length() <= 200) {
                    visitKp.setBusinessScope(KpDto.getBusinessScope());
                }
                ObjectUtil.defaultValue(visitKp);
                kp.setVisitKPPro(JSON.toJSONString(visitKp));
            }
        } catch (WmSchCantException e) {
            log.info("getWdcContractorKp::clueId = {},code={},msg={}", clueId, e.getCode(), e.getMessage());
        }
        return kp;
    }

    public List<ScAorAndCityBo> getAorByIdOrNameV2(String aorIdOrName) throws WmSchCantException {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.service.sc.WmScSchoolService.getAorByIdOrNameV2(java.lang.String)");
        WmUniAorFindParam param = new WmUniAorFindParam();
        if (StringUtils.isNumeric(aorIdOrName)) {
            param.setIds(Lists.newArrayList(Integer.valueOf(aorIdOrName)));
        } else {
            param.setFuzzyName(aorIdOrName);
        }
        WmUniAorSearchOptions options = new WmUniAorSearchOptions();
        param.setOptions(options);

        List<WmUniAor> wmUniAorList = wmAorServiceAdapter.findAor(param);
        if (CollectionUtils.isEmpty(wmUniAorList)) {
            return new ArrayList<>();
        }

        List<ScAorAndCityBo> result = new ArrayList<>();
        for (WmUniAor wmUniAor : wmUniAorList) {
            ScAorAndCityBo bo = new ScAorAndCityBo();
            bo.setAorId(wmUniAor.getId());
            bo.setAorName(wmUniAor.getName());
            result.add(bo);
        }
        return result;
    }

    /**
     * 设置学校列表按钮权限
     * @param schoolBoList 学校对象列表
     * @param uid 用户ID
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    public void setSchoolListButtonAuth(List<SchoolBo> schoolBoList, Integer uid) throws WmSchCantException {
        if (CollectionUtils.isEmpty(schoolBoList) || uid == null || uid <= 0) {
            log.warn("[WmScSchoolService.setSchoolListButtonAuth] schoolBoList is empty or uid is null. schoolBoList = {}, uid = {}",
                    JSONObject.toJSONString(schoolBoList), uid);
            return;
        }

        List<AssertResult> assertResultList = wmScSchoolAuthService.getSchoolListOperationAssertResult(schoolBoList, uid);
        if (CollectionUtils.isEmpty(assertResultList)) {
            log.warn("[WmScSchoolService.setSchoolListButtonAuth] assertResultList is empty. schoolBoList = {}, uid = {}",
                    JSONObject.toJSONString(schoolBoList), uid);
            return;
        }

        Map<Integer, SchoolBo> schoolBoMap = schoolBoList.stream()
                .collect(Collectors.toConcurrentMap(SchoolBo::getId, schoolBo -> schoolBo));
        for (AssertResult assertResult : assertResultList) {
            if (StringUtils.isBlank(assertResult.getObjectId()) || schoolBoMap.get(Integer.valueOf(assertResult.getObjectId())) == null) {
                log.error("[WmScSchoolService.setSchoolListButtonAuth] error. assertResult = {}", JSONObject.toJSONString(assertResult));
                continue;
            }

            SchoolBo schoolBo = schoolBoMap.get(Integer.valueOf(assertResult.getObjectId()));
            wmScSchoolAuthService.setSchoolListAssertResult(assertResult, schoolBo);
        }

        // 如果学校蜂窝类型为代理，则不展示"交付管理"按钮
        for (SchoolBo schoolBo : schoolBoList) {
            if (schoolBo.getAorType().equals((int) SchoolAorTypeEnum.DAILI.getType())) {
                schoolBo.setDeliveryButtonAuth(false);
            }
        }
    }

    /**
     * 设置学校蜂窝名字
     */
    public void setSchoolListAorName(List<WmSchoolDB> wmSchoolDBList) throws WmSchCantException {
        if (CollectionUtils.isEmpty(wmSchoolDBList)) {
            return;
        }

        List<Integer> aorIdList = wmSchoolDBList.stream()
                .map(WmSchoolDB::getAorId)
                .collect(Collectors.toList());

        WmUniAorFindParam param = new WmUniAorFindParam();
        param.setIds(aorIdList);
        WmUniAorSearchOptions options = new WmUniAorSearchOptions();
        param.setOptions(options);
        List<WmUniAor> wmUniAorList = wmAorServiceAdapter.findAor(param);
        if (CollectionUtils.isEmpty(wmUniAorList)) {
            return;
        }

        for (WmSchoolDB school : wmSchoolDBList) {
            for (WmUniAor wmUniAor : wmUniAorList) {
                if (school.getAorId() != wmUniAor.getId()) {
                    continue;
                }
                school.setAorName(wmUniAor.getName());
            }
        }
    }

    /**
     * 设置学校档口总数和合作档口总数(不包括子门店)
     * @param schoolBoList schoolBo列表
     */
    public void setSchoolStallTotalAndCoStallTotal(List<SchoolBo> schoolBoList) throws WmSchCantException {
        if (CollectionUtils.isEmpty(schoolBoList)) {
            return;
        }
        for (SchoolBo schoolBo : schoolBoList) {
            // 设置学校档口总数和合作档口总数
            WmCanteenDB wmCanteenDB = canteenMapper.getStallAndStoreNumTotalBySchoolId(schoolBo.getId());
            if (wmCanteenDB != null && wmCanteenDB.getStallNumTotal() != null && wmCanteenDB.getStoreNumTotal() != null) {
                schoolBo.setStallTotal(wmCanteenDB.getStallNumTotal());
                schoolBo.setCoStallTotal(wmCanteenDB.getStoreNumTotal());
            }

            // 合作档口总数 - 不包括子门店
            if (schoolBo.getCoStallTotal() <= 0) {
                continue;
            }
            List<Long> wmPoiIdNonSubList = wmScCanteenPoiAttributeService.getNonSubWmPoiIdListBySchoolPrimaryId(schoolBo.getId());
            schoolBo.setCoStallTotal(wmPoiIdNonSubList.size());
        }
    }

    /**
     * 批量查询并设置学校标签
     *
     * @param schoolBoList 学校BO对象列表
     * @param schoolQueryBo schoolQueryBo
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    public void setSchoolLabelsForSchoolList(List<SchoolBo> schoolBoList, SchoolQueryBo schoolQueryBo) throws WmSchCantException {
        if (CollectionUtils.isEmpty(schoolBoList)) {
            return;
        }
        List<Integer> schoolIdList = new ArrayList<>();
        Map<Integer, SchoolBo> schoolBoMap = new HashMap<>();
        for (SchoolBo schoolBo : schoolBoList) {
            schoolIdList.add(schoolBo.getSchoolId());
            schoolBoMap.put(schoolBo.getSchoolId(), schoolBo);
        }
        Integer userId = 0;
        if (schoolQueryBo != null && schoolQueryBo.getOptId() != null) {
            userId = schoolQueryBo.getOptId();
        }
        try {
            Map<Integer, List<WmScSchoolLabelDTO>> schoolLabelMap = getSchoolLabelListByBatch(schoolIdList, userId);
            for (Map.Entry<Integer, List<WmScSchoolLabelDTO>> entry : schoolLabelMap.entrySet()) {
                if (schoolBoMap.get(entry.getKey()) != null) {
                    schoolBoMap.get(entry.getKey()).setSchoolLabels(entry.getValue());
                }
            }
        } catch (Exception e) {
            log.error("[setSchoolLabelsForSchoolList] 批量获取学校标签列表异常, schoolIdList = {}, userId = {}", JSONObject.toJSONString(schoolIdList), userId, e);
            throw new WmSchCantException(GET_SCHOOL_LABEL_ERROR, "批量获取学校标签列表异常");
        }
    }

    /**
     * 通过重新拉取学校蜂窝信息，刷新学校的区域，城市团队，物理城市，物理城市ID，蜂窝名字
     * @param scPrimaryId 学校主键ID
     */
    public WmScRefreshAorJobResultDTO updateSchoolAorMsgV2(int scPrimaryId) throws Exception {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.sc.WmScSchoolService.updateSchoolAorMsgV2(int)");
        WmScRefreshAorJobResultDTO wmScRefreshAorJobResultDTO = new WmScRefreshAorJobResultDTO();
        wmScRefreshAorJobResultDTO.setRefreshResult(WmScRefreshAorJobResultEnum.SUCCESS);
        wmScRefreshAorJobResultDTO.setScPrimaryId(scPrimaryId);
        WmSchoolDB wmSchoolDBOld = wmSchoolMapper.selectSchoolById(scPrimaryId);
        if (wmSchoolDBOld == null) {
            wmScRefreshAorJobResultDTO.setRefreshResult(WmScRefreshAorJobResultEnum.FAIL);
            wmScRefreshAorJobResultDTO.setErrorMsg("学校id有误，通过学校id无法查询到学校");
            return wmScRefreshAorJobResultDTO;
        }
        SchoolBo schoolBoIn = new SchoolBo();
        schoolBoIn.setId(scPrimaryId);
        schoolBoIn.setAorId(wmSchoolDBOld.getAorId());
        schoolBoIn.setAorType(wmSchoolDBOld.getAorType());
        // 组装学校蜂窝信息
        composeAorInfo(schoolBoIn);

        List<WmCustomerDiffCellBo> diffList = DiffUtil.compare(wmSchoolDBOld, schoolBoIn, schoolAorMsgField);
        if (CollectionUtils.isEmpty(diffList)) {
            wmScRefreshAorJobResultDTO.setRefreshResult(WmScRefreshAorJobResultEnum.NO_REFRESH);
            wmScRefreshAorJobResultDTO.setErrorMsg("该学校无变更信息，无需清洗");
            return wmScRefreshAorJobResultDTO;
        }
        WmSchoolDB wmSchoolDBIn = new WmSchoolDB();
        wmSchoolDBIn.setId(wmSchoolDBOld.getId());
        wmSchoolDBIn.setCityId(schoolBoIn.getCityId());
        wmSchoolDBIn.setCityName(schoolBoIn.getCityName());
        wmSchoolDBIn.setCityTeam(schoolBoIn.getCityTeam());
        wmSchoolDBIn.setArea(schoolBoIn.getArea());
        wmSchoolDBIn.setAorName(schoolBoIn.getAorName());
        wmSchoolMapper.updateSchoolAorMsg(wmSchoolDBIn);
        return wmScRefreshAorJobResultDTO;
    }

   /**
   * 从waimai_e_customer中的com.sankuai.meituan.waimai.service.sc.WmScSchoolService类中的composeAorInfo方法迁移过来的
   */
    public void composeAorInfo(SchoolBo schoolBo) throws WmSchCantException {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.sc.WmScSchoolService.composeAorInfo(com.sankuai.meituan.waimai.thrift.customer.domain.sc.SchoolBo)");
        if (schoolBo.getAorType().equals((int) SchoolAorTypeEnum.ZHIYING.getType())) {
            // 直营蜂窝
            composeAorInfoV2(schoolBo);
        } else {
            // 代理蜂窝
            composeAgentAorInfo(schoolBo);
        }
    }

    public void composeAorInfoV2(SchoolBo schoolBo) throws WmSchCantException {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.sc.WmScSchoolService.composeAorInfoV2(com.sankuai.meituan.waimai.thrift.customer.domain.sc.SchoolBo)");
        int aorId = schoolBo.getAorId();
        // 补全蜂窝信息
        WmAor wmAor = wmAorServiceAdapter.query(aorId);
        if (wmAor == null) {
            log.info("校园食堂项目:补全蜂窝信息:未找到对应的蜂窝信息:{}", JSONObject.toJSONString(schoolBo));
            throw new WmSchCantException(AORID_NOT_FOUND, "蜂窝ID不存在，不可保存");
        }
        schoolBo.setAorName(wmAor.getTitle());

        // 补全城市信息
        WmOpenCity wmOpenCity = cityCommonServiceAdapter.getByCityId(wmAor.getCityId());
        log.info("WmScSchoolService.composeAorInfo, wmOpenCityThriftService.getByCityId, wmAor.getCityId()={}, wmOpenCity={}", wmAor.getCityId(), JSON.toJSONString(wmOpenCity));
        if (wmOpenCity == null) {
            log.info("校园食堂项目:补全蜂窝信息:获取物理城市失败:{}", JSONObject.toJSONString(schoolBo));
            throw new WmSchCantException(CITY_NOT_FOUND, "获取物理城市失败 scPrimaryId={}" + schoolBo.getId());
        }
        String cityName = wmOpenCity.getCityName();
        int cityId = wmAor.getCityId();
        if (wmOpenCity.getCityAdLevel() == CITY_AD_LEVEL_COUNTY) {
            WmOpenCity parentWmOpenCity = cityCommonServiceAdapter.getParentCityByCityId(cityId);
            log.info("WmScSchoolService.composeAorInfo, wmOpenCityThriftService.getParentCityByCityId, cityId={}, parentWmOpenCity={}", cityId, JSON.toJSONString(parentWmOpenCity));
            if (parentWmOpenCity == null) {
                log.info("校园食堂项目:补全蜂窝信息:获取上级物理城市失败:{}", JSONObject.toJSONString(schoolBo));
                throw new WmSchCantException(CITY_NOT_FOUND, "获取上级物理城市失败 scPrimaryId={}" + schoolBo.getId());
            }
            cityName = parentWmOpenCity.getCityName();
            cityId = parentWmOpenCity.getCityId();
        }
        schoolBo.setCityName(cityName);
        schoolBo.setCityId(cityId);

        // 补全组织架构信息
        WmVirtualOrg wmVirtualOrg = wmVirtualOrgServiceAdaptor.getVirtualOrgByCode(WmOrgConstant.OrgType.WM_AOR, aorId);
        if (wmVirtualOrg == null) {
            log.info("校园食堂项目:补全蜂窝信息:获取虚拟节点为空:schoolBo:{}:aorId{}", JSONObject.toJSONString(schoolBo), aorId);
            throw new WmSchCantException(AORID_NOT_FOUND, "补全组织架构信息查询为空 scPrimaryId={}" + schoolBo.getId());
        }
        List<WmVirtualOrg> wmVirtualOrgs = wmVirtualOrgServiceAdaptor.getOrgsByOrgId(wmVirtualOrg.getId(), WmVirtualOrgRecursiveTypeEnum.BOTTOM_UP.getType());
        log.info("校园食堂项目:组装蜂窝参数虚拟节点:schoolBo:{}:wmVirtualOrg:{}:wmVirtualOrgs:{}", JSONObject.toJSONString(schoolBo), JSONObject.toJSONString(wmVirtualOrg), JSONObject.toJSONString(wmVirtualOrgs));
        if (CollectionUtils.isEmpty(wmVirtualOrgs)) {
            log.info("校园食堂项目:补全蜂窝信息:获取虚拟节点列表为空:schoolBo:{}:aorId{}", JSONObject.toJSONString(schoolBo), wmVirtualOrg.getId());
            throw new WmSchCantException(AORID_NOT_FOUND, "补全组织架构信息查询为空 scPrimaryId={}" + schoolBo.getId());
        }
        WmVirtualOrg wmCityVirtualOrg = Iterators.find(wmVirtualOrgs.iterator(), new Predicate<WmVirtualOrg>() {
            @Override
            public boolean apply(@Nullable WmVirtualOrg input) {
                return WmOrgConstant.OrgType.WM_ORG_CITY == input.getOrgType();
            }
        }, null);
        if (wmCityVirtualOrg == null) {
            log.info("校园食堂项目:补全蜂窝信息:获取OrgType=3(WM_ORG_CITY)的虚拟节点失败");
            throw new WmSchCantException(ORG_EXCEPTION, "补全组织架构信息失败 scPrimaryId={}" + schoolBo.getId());
        }
        schoolBo.setCityTeam(wmCityVirtualOrg.getName());
        log.info("校园食堂项目:组装蜂窝参数城市:schoolBo:{}:wmCityVirtualOrg:{}", JSONObject.toJSONString(schoolBo), JSONObject.toJSONString(wmCityVirtualOrg));

        WmVirtualOrg wmDisVirtualOrg = Iterators.find(wmVirtualOrgs.iterator(), new Predicate<WmVirtualOrg>() {
            @Override
            public boolean apply(@Nullable WmVirtualOrg input) {
                return WmOrgConstant.OrgType.WM_DISTRICT == input.getOrgType();
            }
        }, null);
        if (wmDisVirtualOrg == null) {
            log.info("校园食堂项目:补全蜂窝信息:获取OrgType=4(WM_DISTRICT)的虚拟节点失败");
            throw new WmSchCantException(ORG_EXCEPTION, "补全组织架构信息失败 scPrimaryId={}" + schoolBo.getId());
        }
        schoolBo.setArea(wmDisVirtualOrg.getName());
        log.info("校园食堂项目:组装蜂窝参数区域:schoolBo:{}:wmDisVirtualOrg:{}", JSONObject.toJSONString(schoolBo), JSONObject.toJSONString(wmDisVirtualOrg));
    }

    /**
     * 组装代理蜂窝相关信息
     */
    public void composeAgentAorInfo(SchoolBo schoolBo) throws WmSchCantException {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.sc.WmScSchoolService.composeAgentAorInfo(com.sankuai.meituan.waimai.thrift.customer.domain.sc.SchoolBo)");
        log.info("[WmScSchoolService.composeAgentAorInfo] input param: schoolBo = {}", JSONObject.toJSONString(schoolBo));
        Integer agentAorId = schoolBo.getAorId();
        // 1-补全蜂窝信息
        WmAgentAor wmAgentAor = wmAgentAorServiceAdapter.getAgentAorByAgentAorId(agentAorId);
        if (wmAgentAor == null) {
            log.error("[WmScSchoolService.composeAgentAorInfo] wmAgentAor is null. agentAorId = {}", agentAorId);
            throw new WmSchCantException(AORID_NOT_FOUND, "蜂窝ID不存在，不可保存");
        }

        schoolBo.setAorName(wmAgentAor.getName());
        // 2-补全城市信息
        String cityIdStr = wmAgentAor.getCityIds();
        List<Integer> cityIdList = new ArrayList<>();
        for (String cityId : cityIdStr.split(",")) {
            cityIdList.add(Integer.valueOf(cityId));
        }

        List<WmOpenCity> wmOpenCityList = cityCommonServiceAdapter.mgetWmOpenCityByCityIds(cityIdList);
        boolean findCityFlag = false;
        for (WmOpenCity wmOpenCity : wmOpenCityList) {
            // 1-二级物理城市，直接使用
            if (wmOpenCity.getCityAdLevel() == CITY_AD_LELEL_CITY) {
                schoolBo.setCityName(wmOpenCity.getCityName());
                schoolBo.setCityId(wmOpenCity.getCityId());
                findCityFlag = true;
                break;
            }

            // 2-三级物理城市，取parentCity
            if (wmOpenCity.getCityAdLevel() == CITY_AD_LEVEL_COUNTY) {
                WmOpenCity parentCity = cityCommonServiceAdapter.getByCityId(wmOpenCity.getParentCityId());
                if (parentCity != null) {
                    schoolBo.setCityName(parentCity.getCityName());
                    schoolBo.setCityId(parentCity.getCityId());
                    findCityFlag = true;
                    break;
                }
            }
        }

        if (!findCityFlag) {
            log.error("[WmScSchoolService.composeAgentAorInfo] wmOpenCity is null. cityId = {}", schoolBo.getCityId());
            throw new WmSchCantException(CITY_NOT_FOUND, "获取物理城市失败");
        }

        // 3-补全组织架构信息
        WmVirtualOrg wmVirtualOrg = wmVirtualOrgServiceAdaptor.getVirtualOrgByCode(WmOrgConstant.OrgType.AGENT_PERSON, agentAorId);
        if (wmVirtualOrg == null) {
            log.info("[WmScSchoolService.composeAgentAorInfo] wmVirtualOrg is null. agentAorId = {}", agentAorId);
            schoolBo.setCityTeam("-");
            schoolBo.setArea("-");
            return;
        }

        List<WmVirtualOrg> wmVirtualOrgs = wmVirtualOrgServiceAdaptor.getOrgsByOrgId(wmVirtualOrg.getId(), WmVirtualOrgRecursiveTypeEnum.BOTTOM_UP.getType());
        if (CollectionUtils.isEmpty(wmVirtualOrgs)) {
            log.info("[WmScSchoolService.composeAgentAorInfo] wmVirtualOrgs is empty. orgId = {}", wmVirtualOrg.getId());
            schoolBo.setCityTeam("-");
            schoolBo.setArea("-");
            return;
        }

        WmVirtualOrg wmCityVirtualOrg = Iterators.find(wmVirtualOrgs.iterator(), new Predicate<WmVirtualOrg>() {
            @Override
            public boolean apply(@Nullable WmVirtualOrg input) {
                return WmOrgConstant.OrgType.AGENT_CITY == input.getOrgType();
            }
        }, null);
        if (wmCityVirtualOrg == null) {
            log.info("[WmScSchoolService.composeAgentAorInfo] wmCityVirtualOrg is null. orgId = {}", wmVirtualOrg.getId());
            schoolBo.setCityTeam("-");
            schoolBo.setArea("-");
            return;
        }

        schoolBo.setCityTeam(wmCityVirtualOrg.getName());
        WmVirtualOrg wmDisVirtualOrg = Iterators.find(wmVirtualOrgs.iterator(), new Predicate<WmVirtualOrg>() {
            @Override
            public boolean apply(@Nullable WmVirtualOrg input) {
                return WmOrgConstant.OrgType.AGENT_DISTRICT == input.getOrgType();
            }
        }, null);

        if (wmDisVirtualOrg == null) {
            log.info("[WmScSchoolService.composeAgentAorInfo] wmDisVirtualOrg is null. orgId = {}", wmVirtualOrg.getId());
            schoolBo.setArea("-");
            return;
        }
        schoolBo.setArea(wmDisVirtualOrg.getName());
        log.info("[WmScSchoolService.composeAgentAorInfo] output param: schoolBo = {}", JSONObject.toJSONString(schoolBo));
    }



    /**
     * 获取单个学校标签信息列表
     * @param   schoolIdList 学校ID列表
     * @param   opUid 用户ID
     * @throws  TException org.apache.thrift.TException
     * @throws  WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    public Map<Integer, List<WmScSchoolLabelDTO>> getSchoolLabelList(List<Long> schoolIdList, Integer opUid) throws TException, WmSchCantException {
        log.info("[getSchoolLabelList] schoolIdList = {}, opUid = {}", JSONObject.toJSONString(schoolIdList), opUid);
        Map<Long, List<WmLabelClassFullFormat>> labelMap;
        Map<Integer, List<WmScSchoolLabelDTO>> resMap = new HashMap<>();
        try {
            labelMap = wmLabelInnerThriftService.getLabelFormatForCrmPage(schoolIdList, LabelSubjectTypeEnum.SCHOOL.getCode(), opUid);
            if (MapUtils.isEmpty(labelMap)) {
                return null;
            }
            for (Map.Entry<Long, List<WmLabelClassFullFormat>> entry : labelMap.entrySet()) {
                List<WmLabelClassFullFormat> wmLabelClassFullFormatList = entry.getValue();
                List<WmScSchoolLabelBO> wmScSchoolLabelBOList = new ArrayList<>();
                for (WmLabelClassFullFormat wmLabelClassFullFormat : wmLabelClassFullFormatList) {
                    List<WmPoiLabel> wmPoiLabelList = wmLabelClassFullFormat.getWmPoiLabelList();
                    if (CollectionUtils.isEmpty(wmPoiLabelList)) {
                        continue;
                    }
                    for (WmPoiLabel wmPoiLabel : wmPoiLabelList) {
                        WmScSchoolLabelBO wmScSchoolLabelBO = new WmScSchoolLabelBO();
                        wmScSchoolLabelBO.setLabelId(wmPoiLabel.getId());
                        wmScSchoolLabelBO.setLabelName(wmPoiLabel.getName());
                        wmScSchoolLabelBO.setLabelTipsName(wmPoiLabel.getContent());
                        wmScSchoolLabelBOList.add(wmScSchoolLabelBO);
                    }
                }
                resMap.put(entry.getKey().intValue(), WmScSchoolLabelBO.transWmScSchoolLabelBoToDto(wmScSchoolLabelBOList));
            }
        } catch (Exception e) {
            log.error("[getSchoolLabelList] 从标签侧获取学校标签列表异常, schoolIdList = {}, opUid = {}", JSONObject.toJSONString(schoolIdList), opUid, e);
            throw new WmSchCantException(GET_SCHOOL_LABEL_ERROR, "从标签侧获取学校标签列表异常");
        }
        return resMap;
    }

    /**
     * 批量获取学校标签信息列表
     * @param   schoolIdList 学校ID列表
     * @param   opUid 用户ID
     * @return  学校标签信息列表map, key->学校ID, value->标签列表
     * @throws  TException org.apache.thrift.TException
     * @throws  WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    public Map<Integer, List<WmScSchoolLabelDTO>> getSchoolLabelListByBatch(List<Integer> schoolIdList, Integer opUid) throws TException, WmSchCantException {
        log.info("[getSchoolLabelListByBatch] schoolIdList = {}, opUid = {}", JSONObject.toJSONString(schoolIdList), opUid);
        Map<Integer, List<WmScSchoolLabelDTO>> map = new HashMap<>();
        if (CollectionUtils.isEmpty(schoolIdList)) {
            return map;
        }
        try {
            List<Long> schoolIds = JSONArray.parseArray(schoolIdList.toString(), Long.class);
            map = getSchoolLabelList(schoolIds, opUid);
        } catch (Exception e) {
            log.error("[getSchoolLabelListByBatch] 从标签侧批量获取学校标签列表异常, schoolIdList = {}, opUid = {}", JSONObject.toJSONString(schoolIdList), opUid, e);
            throw new WmSchCantException(GET_SCHOOL_LABEL_ERROR, "批量获取学校标签列表异常");
        }
        return map;
    }

    /**
     * 获取不存在的学校ID列表
     * @param   schooIdList 待校验的学校id列表
     * @return  不存在的学校ID列表
     * @throws  TException org.apache.thrift.TException
     * @throws  WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    public List<Integer> getNonExistingSchooIdList(List<Integer> schooIdList) throws TException, WmSchCantException {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.meituan.waimai.customer.service.sc.WmScSchoolService.getNonExistingSchooIdList(java.util.List)");
        log.info("[getNonExistingSchooIdList] schooIdList = {}", JSONObject.toJSONString(schooIdList));
        List<Integer> nonExistingSchoolIdList = new ArrayList<>();
        if (CollectionUtils.isEmpty(schooIdList)) {
            return nonExistingSchoolIdList;
        }
        // 查询存在的学校ID列表
        List<Integer> existSchoolIdList = wmSchoolMapper.selectExistSchoolIdList(schooIdList);
        if (CollectionUtils.isEmpty(existSchoolIdList)) {
            return schooIdList;
        }
        if (existSchoolIdList.size() == schooIdList.size()) {
            return nonExistingSchoolIdList;
        }
        for (Integer schoolId : schooIdList) {
            if (!existSchoolIdList.contains(schoolId)) {
                nonExistingSchoolIdList.add(schoolId);
            }
        }
        log.info("[getNonExistingSchooIdList] nonExistingSchoolIdList = {}", JSONObject.toJSONString(nonExistingSchoolIdList));
        return nonExistingSchoolIdList;
    }

    /**
     * 同步学校标签变更到数据库
     * @param   schoolId 学校ID
     * @param   opUid 操作人ID
     * @throws  TException org.apache.thrift.TException
     * @throws  WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    public void syncSchoolLabelToWmScSchoolDb(Integer schoolId, Integer opUid) throws TException, WmSchCantException {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.sc.WmScSchoolService.syncSchoolLabelToWmScSchoolDb(java.lang.Integer,java.lang.Integer)");
        log.info("[syncSchoolLabelToWmScSchoolDb] 入参schoolId = {}, opUid = {}", schoolId, opUid);
        if (schoolId == null) {
            log.error("[syncSchoolLabelToWmScSchoolDb] 入参中schoolId = {}, opUid = {}为空", schoolId, opUid);
            throw new WmSchCantException(WmScCodeConstants.SYNC_SCHOOL_LABEL_ERROR, "同步学校标签变更异常");
        }
        try {
            List<WmPoiLabelRel> wmPoiLabelRelList = wmLabelInnerThriftService.selectLabelRelBySubjectIdAndLabelId(schoolId, 0, LabelSubjectTypeEnum.SCHOOL.getCode());
            log.info("[syncSchoolLabelToWmScSchoolDb] wmPoiLabelRelList = {}", JSONObject.toJSONString(wmPoiLabelRelList));
            List<String> schoolLabelList = new ArrayList<>();
            for (WmPoiLabelRel wmPoiLabelRel : wmPoiLabelRelList) {
                if (wmPoiLabelRel.getWmLabelId() > 0 && wmPoiLabelRel.getSubjectId() == schoolId) {
                    schoolLabelList.add(String.valueOf(wmPoiLabelRel.getWmLabelId()));
                }
            }
            // 同步标签信息到学校表(wm_sc_school)
            WmSchoolDB wmSchoolDB = new WmSchoolDB();
            wmSchoolDB.setSchoolLabels(Joiner.on(",").join(schoolLabelList));
            wmSchoolDB.setSchoolId(schoolId);
            if (StringUtils.isEmpty(wmSchoolDB.getSchoolLabels())) {
                wmSchoolDB.setSchoolLabels("");
            }
            wmSchoolMapper.updateSchoolLabels(wmSchoolDB);
        } catch (WmServerException e) {
            log.error("[syncSchoolLabelToWmScSchoolDb] 学校标签同步失败, schoolId = {}, opUid = {}", schoolId, opUid, e);
            throw new WmSchCantException(WmScCodeConstants.SYNC_SCHOOL_LABEL_ERROR, "同步学校标签变更异常");
        }
    }

    /**
     * 一致性校验兜底任务 - 定时执行同步标签至学校表
     * @param   schoolId 学校ID
     * @throws  TException org.apache.thrift.TException
     * @throws  WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    public void refreshSchoolLabelToWmScSchool(Integer schoolId) throws TException, WmSchCantException {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.sc.WmScSchoolService.refreshSchoolLabelToWmScSchool(java.lang.Integer)");
        log.info("[refreshSchoolLabelToWmScSchool] 入参schoolId = {}", schoolId);
        // 若学校不存在或被删除, 则直接返回
        WmSchoolDB wmSchoolDB = wmSchoolMapper.selectSchoolBySchoolId(schoolId);
        if (wmSchoolDB == null) {
            log.info("[refreshSchoolLabelToWmScSchool] schoolId = {}学校不存在或已被删除 ", schoolId);
            return;
        }
        try {
            // 根据标签侧接口查询该学校下的所有标签
            List<WmPoiLabelRel> wmPoiLabelRelList = wmLabelInnerThriftService.selectLabelRelBySubjectIdAndLabelId(schoolId, 0, LabelSubjectTypeEnum.SCHOOL.getCode());
            log.info("[refreshSchoolLabelToWmScSchool] wmPoiLabelRelList = {}", JSONObject.toJSONString(wmPoiLabelRelList));
            List<String> schoolLabelListA = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(wmPoiLabelRelList)) {
                for (WmPoiLabelRel wmPoiLabelRel : wmPoiLabelRelList) {
                    if (wmPoiLabelRel.getWmLabelId() > 0 && wmPoiLabelRel.getSubjectId() == schoolId) {
                        schoolLabelListA.add(String.valueOf(wmPoiLabelRel.getWmLabelId()));
                    }
                }
            }
            // 从学校库(wm_sc_school)中查询该学校下的所有标签
            String schoolLabelsStr = wmSchoolMapper.selectSchoolLabelsBySchoolId(schoolId);
            List<String> schoolLabelListB = new ArrayList<>();
            if (StringUtils.isNotBlank(schoolLabelsStr)) {
                schoolLabelListB = Arrays.asList(schoolLabelsStr.split(","));
            }
            // 校验学校侧和标签侧的标签数据是否一致
            if (CollectionUtils.isEmpty(schoolLabelListA) && CollectionUtils.isEmpty(schoolLabelListB)) {
                return;
            }
            if ((CollectionUtils.isEmpty(schoolLabelListA) && CollectionUtils.isNotEmpty(schoolLabelListB))
                || (CollectionUtils.isNotEmpty(schoolLabelListA) && CollectionUtils.isEmpty(schoolLabelListB))) {
                refreshSchoolLabelToWmScSchoolDetail(schoolId, schoolLabelListA, schoolLabelListB);
                return;
            }
            if (schoolLabelListA.size() != schoolLabelListB.size()) {
                refreshSchoolLabelToWmScSchoolDetail(schoolId, schoolLabelListA, schoolLabelListB);
                return;
            }
            for (String schoolLabel : schoolLabelListA) {
                if (!schoolLabelListB.contains(schoolLabel)) {
                    refreshSchoolLabelToWmScSchoolDetail(schoolId, schoolLabelListA, schoolLabelListB);
                    return;
                }
            }
            for (String schoolLabel : schoolLabelListB) {
                if (!schoolLabelListA.contains(schoolLabel)) {
                    refreshSchoolLabelToWmScSchoolDetail(schoolId, schoolLabelListA, schoolLabelListB);
                    return;
                }
            }
        } catch (WmServerException e) {
            log.error("[refreshSchoolLabelToWmScSchool] 学校标签同步失败, schoolId = {}", schoolId, e);
            throw new WmSchCantException(WmScCodeConstants.SYNC_SCHOOL_LABEL_ERROR, "同步学校标签变更异常");
        }
    }

    /**
     * 若学校标签校验不一致, 发送大象通知
     * @param schoolId 学校ID
     * @param schoolLabelListA 标签侧接口查询该学校下的所有标签
     * @param schoolLabelListB 学校库中查询该学校下的所有标签
     * @throws TException org.apache.thrift.TException
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    private void refreshSchoolLabelToWmScSchoolDetail(Integer schoolId, List<String> schoolLabelListA, List<String> schoolLabelListB) throws TException, WmSchCantException {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.sc.WmScSchoolService.refreshSchoolLabelToWmScSchoolDetail(java.lang.Integer,java.util.List,java.util.List)");
        log.error("[refreshSchoolLabelToWmScSchool] 学校标签数据不一致, schoolId = {},  标签列表(标签侧) = {}, 标签列表(学校库) = {}",
                schoolId, JSONObject.toJSONString(schoolLabelListA), JSONObject.toJSONString(schoolLabelListB));
        // 同步更新到学校数据库
        syncSchoolLabelToWmScSchoolDb(schoolId, 0);
        // 发送大象通知
        String errMsg = "[学校标签数据不一致] schoolId = " + schoolId +
                " 标签列表(标签侧) = " + JSONObject.toJSONString(schoolLabelListA) +
                " 标签列表(学校库) = " + JSONObject.toJSONString(schoolLabelListB);
        DaxiangUtilV2.push(errMsg, "houjikang", "gaoshuying");
    }

    /**
     * 定时任务-更新学校合作信息（学校合作方式、合作状态、学校生命周期）
     * @param   schoolId 学校ID
     * @throws  TException org.apache.thrift.TException
     * @throws  WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    public void refreshSchoolCooperateInfo(Integer schoolId) throws TException, WmSchCantException {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.sc.WmScSchoolService.refreshSchoolCooperateInfo(java.lang.Integer)");
        log.info("[refreshSchoolBasicInfo] input param: schoolId = {}", schoolId);
        // 若学校不存在或被删除, 则直接返回
        WmSchoolDB wmSchoolDB = wmSchoolMapper.selectSchoolBySchoolId(schoolId);
        if (wmSchoolDB == null) {
            log.info("[refreshSchoolBasicInfo] schoolId = {} 学校不存在或被删除", schoolId);
            return;
        }
        wmScSchoolSensitiveWordsService.readWhenSelect(wmSchoolDB);
        // 计算学校月营业档口数
        WmScSchoolExtensionDO wmScSchoolExtensionDO = wmScSchoolExtensionMapper.selectBySchoolId(wmSchoolDB.getSchoolId());
        int monthStallNum = 0;
        if (wmScSchoolExtensionDO != null
                && wmScSchoolExtensionDO.getMonthStallNum() != null
                && wmScSchoolExtensionDO.getMonthStallNum() >= 0) {
            monthStallNum = wmScSchoolExtensionDO.getMonthStallNum();
        }
        SchoolBo schoolBo = new SchoolBo();
        BeanUtils.copyProperties(wmSchoolDB, schoolBo);
        log.info("[refreshSchoolBasicInfo] schoolBo = {}, monthStallNum = {}", JSONObject.toJSONString(schoolBo), monthStallNum);

        WmSchoolDB wmSchoolDBToUpdate = new WmSchoolDB();
        wmSchoolDBToUpdate.setId(wmSchoolDB.getId());
        boolean isSchoolCooperateInfoChanged = false;
        // 计算学校合作方式(合作方式根据合同/授权编号及有效期计算)
        Integer schoolCooperateType = getSchoolCooperateType(schoolBo, monthStallNum);
        if (!wmSchoolDB.getSchoolCooperateType().equals(schoolCooperateType)) {
            isSchoolCooperateInfoChanged = true;
            wmSchoolDBToUpdate.setSchoolCooperateType(schoolCooperateType);
            schoolBo.setSchoolCooperateType(schoolCooperateType);
        }
        // 计算学校合作状态(合作状态根据合作方式计算)
        Integer schoolCooperateStatus = getSchoolCooperateStatus(schoolBo);
        if (!wmSchoolDB.getWmCoStatus().equals(schoolCooperateStatus)) {
            isSchoolCooperateInfoChanged = true;
            wmSchoolDBToUpdate.setWmCoStatus(schoolCooperateStatus);
            schoolBo.setWmCoStatus(schoolCooperateStatus);
        }
        // 计算学校生命周期(生命周期根据合作状态及月营业档口数计算)
        Integer schoolLifecycle = getSchoolLifecycle(schoolBo, monthStallNum);
        if (!wmSchoolDB.getSchoolLifecycle().equals(schoolLifecycle)) {
            isSchoolCooperateInfoChanged = true;
            wmSchoolDBToUpdate.setSchoolLifecycle(schoolLifecycle);
        }

        if (!isSchoolCooperateInfoChanged) {
            log.info("[refreshSchoolBasicInfo] schoolId = {} 学校合作信息未发生变化", schoolId);
            return;
        }
        log.info("[refreshSchoolBasicInfo] wmSchoolDBToUpdate = {}", JSONObject.toJSONString(wmSchoolDBToUpdate));

        int result = wmSchoolMapper.updateSchool(wmSchoolDBToUpdate);
        if (result == 0) {
            return;
        }
        try {
            SchoolBo schoolBoUpdateAfter = new SchoolBo();
            BeanUtils.copyProperties(wmSchoolDB, schoolBoUpdateAfter);
            schoolBoUpdateAfter.setSchoolCooperateType(schoolCooperateType);
            schoolBoUpdateAfter.setWmCoStatus(schoolCooperateStatus);
            schoolBoUpdateAfter.setSchoolLifecycle(schoolLifecycle);
            String logUpdate = wmScLogService.composeSchoolUpdateLog(schoolBoUpdateAfter, wmSchoolDB);
            if (StringUtils.isNotEmpty(logUpdate)) {
                wmScLogService.insertScOptLog(OptTypeEnum.UPDATE.getType(), SC_SCHOOL_LOG,
                        schoolBoUpdateAfter.getId(), 0, "系统自动更新", logUpdate, "");
            }
        } catch (Exception e) {
            log.warn("[refreshSchoolBasicInfo] Exception insertScOptLog. schoolId = {}", schoolId, e);
        }
        log.info("[refreshSchoolBasicInfo] refresh success. schoolId = {}, schoolCooperateType = {}, schoolCooperateStatus = {}, schoolLifecycle = {}",
                schoolId, schoolCooperateType, schoolCooperateStatus, schoolLifecycle);
    }

    /**
     * 计算学校合作方式
     * @param schoolBo schoolBo
     * @param schoolMonthStallNum 月营业档口数
     * @return com.sankuai.meituan.waimai.thrift.customer.constant.sc.SchoolCooperateTypeEnum
     */
    public Integer getSchoolCooperateType(SchoolBo schoolBo, int schoolMonthStallNum) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.service.sc.WmScSchoolService.getSchoolCooperateType(com.sankuai.meituan.waimai.thrift.customer.domain.sc.SchoolBo,int)");
        log.info("WmScSchoolService.getSchoolCooperateType schoolBo = {}, schoolMonthStallNum = {}", JSONObject.toJSONString(schoolBo), schoolMonthStallNum);
        // 有合同编号且在有效期内, 学校合作方式就是合同
        if (schoolBo.getAgreementType() == SchoolAgreementTypeEnum.CONTRACT.getType()
                && StringUtils.isNotBlank(schoolBo.getAgreementCode())
                && schoolBo.getAgreementTimeEnd() >= System.currentTimeMillis()/1000L
                && schoolBo.getAgreementTimeStart() <= System.currentTimeMillis()/1000L) {
                return (int) SchoolCooperateTypeEnum.CONTRACT.getType();
        }
        // 有授权编号且在有效期内, 学校合作方式就是授权
        if (schoolBo.getAgreementType() == SchoolAgreementTypeEnum.AUTH.getType()
                && StringUtils.isNotBlank(schoolBo.getAgreementCode())
                && schoolBo.getAgreementTimeEnd() >= System.currentTimeMillis()/1000L
                && schoolBo.getAgreementTimeStart() <= System.currentTimeMillis()/1000L) {
                return (int) SchoolCooperateTypeEnum.AUTH.getType();
        }
        // 自然月营业档口数>=10, 学校合作方式就是默认合作
        if (schoolMonthStallNum >= MONTH_STALL_NUM) {
            return (int) SchoolCooperateTypeEnum.DEFAULT_COOPERATE.getType();
        }
        // 不满足以上逻辑, 学校合作方式就是未知
        return (int) SchoolCooperateTypeEnum.UNKNOWN.getType();
    }

    /**
     * 计算学校合作状态
     * @param schoolBo schoolBo
     * @return com.sankuai.meituan.waimai.thrift.customer.constant.sc.SchoolWmCoStatusEnum
     */
    public Integer getSchoolCooperateStatus(SchoolBo schoolBo) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.service.sc.WmScSchoolService.getSchoolCooperateStatus(com.sankuai.meituan.waimai.thrift.customer.domain.sc.SchoolBo)");
        log.info("WmScSchoolService.getSchoolCooperateStatus schoolBo = {}", JSONObject.toJSONString(schoolBo));
        // 若学校合作方式中有合同、授权、系统默认其中一个值, 则合作状态就是已合作
        if (schoolBo.getSchoolCooperateType() == SchoolCooperateTypeEnum.CONTRACT.getType()
                || schoolBo.getSchoolCooperateType() == SchoolCooperateTypeEnum.AUTH.getType()
                || schoolBo.getSchoolCooperateType() == SchoolCooperateTypeEnum.DEFAULT_COOPERATE.getType()) {
            return (int) SchoolWmCoStatusEnum.COOPERATED.getType();
        }
        // 若学校合作方式是未知, 则合作状态就是未合作
        return (int) SchoolWmCoStatusEnum.UNCOOPERATE.getType();
    }

    /**
     * 计算学校生命周期
     * @param schoolBo schoolBo
     * @param schoolMonthStallNum 月营业档口数
     * @return com.sankuai.meituan.waimai.thrift.customer.constant.sc.SchoolLifecycleEnum
     */
    public Integer getSchoolLifecycle(SchoolBo schoolBo, Integer schoolMonthStallNum) {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.service.sc.WmScSchoolService.getSchoolLifecycle(com.sankuai.meituan.waimai.thrift.customer.domain.sc.SchoolBo,java.lang.Integer)");
        log.info("WmScSchoolService.getSchoolLifecycle schoolBo = {}, schooMonthStallNum = {}", JSONObject.toJSONString(schoolBo), schoolMonthStallNum);
        // 公海档口数 = 该学校的所有食堂的档口数总和
        Integer stallNumTotal = canteenMapper.getStallNumTotalBySchoolId(schoolBo.getId());
        if (stallNumTotal == null || stallNumTotal <= 0) {
            log.info("WmScSchoolService.getSchoolLifecycle stallNumTotal = {} not match requirement.", stallNumTotal);
            return (int) SchoolLifecycleEnum.NOT_TARGET_SCHOOL.getType();
        }
        // 月营业渗透率 = 月营业档口数 / 公海档口数
        double monthBusinessRate = schoolMonthStallNum.doubleValue() / stallNumTotal.doubleValue();
        log.info("WmScSchoolService.getSchoolLifecycle stallNumTotal = {}, monthBusinessRate = {}", stallNumTotal, monthBusinessRate);
        // 目标学校 = 合作状态是未合作, 且公海档口≥10个
        if (schoolBo.getWmCoStatus() == SchoolWmCoStatusEnum.UNCOOPERATE.getType() && stallNumTotal >= 10) {
            return (int) SchoolLifecycleEnum.TARGET_SCHOOL.getType();
        }

        if (schoolBo.getWmCoStatus() == SchoolWmCoStatusEnum.COOPERATED.getType()) {
            if (monthBusinessRate < MONTH_BIZ_PENERATION_RATE_LOW) {
                // 合作学校 = 合作状态是合作, 且月营业渗透率<40%
                return (int) SchoolLifecycleEnum.COOPERATE_SCHOOL.getType();
            } else if (monthBusinessRate < MONTH_BIZ_PENERATION_RATE_HIGH) {
                // 有效合作学校 = 合作状态是合作, 且75%>月营业渗透率≥40%
                return (int) SchoolLifecycleEnum.VALID_COOPERATE_SCHOOL.getType();
            } else {
                // 深度合作学校 = 合作状态是合作, 且月营业渗透率≥75%
                return (int) SchoolLifecycleEnum.DEEP_COOPERATE_SCHOOL.getType();
            }
        }
        // 非目标学校 = 不满足以上4个条件
        return (int) SchoolLifecycleEnum.NOT_TARGET_SCHOOL.getType();
    }

    /**
     * 设置相关查询条件, 包括学校区域、城市团队和标签列表
     * @param schoolQueryBo 查询条件bo
     * @param wmSchoolDB 查询数据库参数
     */
    public void setQueryParamInWmSchoolDB(SchoolQueryBo schoolQueryBo, WmSchoolDB wmSchoolDB) {
        // 设置查询的学校标签列表
        if (CollectionUtils.isNotEmpty(schoolQueryBo.getSchoolLabel())) {
            wmSchoolDB.setSchoolLabelQueryList(parseSchoolLabelToStrList(schoolQueryBo.getSchoolLabel()));
        }
        // 设置查询的学校区域
        if (StringUtils.isNotEmpty(schoolQueryBo.getAreaName())) {
            wmSchoolDB.setArea(schoolQueryBo.getAreaName());
        }
        // 设置查询的学校城市团队
        if (StringUtils.isNotEmpty(schoolQueryBo.getCityTeamName())) {
            wmSchoolDB.setCityTeam(schoolQueryBo.getCityTeamName());
        }
    }

    /**
     * 将学校标签从String转为List<String>
     * @param schoolLabel 学校标签列表
     * @return 学校标签列表
     */
    private List<String> parseSchoolLabelToStrList(List<Integer> schoolLabel) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.service.sc.WmScSchoolService.parseSchoolLabelToStrList(java.util.List)");
        List<String> schoolLabelList = new ArrayList<>();
        if (CollectionUtils.isEmpty(schoolLabel)) {
            return schoolLabelList;
        }
        for (Integer label : schoolLabel) {
            schoolLabelList.add(String.valueOf(label));
        }
        return schoolLabelList;
    }

    /**
     * 计算学校分级
     * @param schoolType 学校类型
     * @param teaStuNum 在校师生人数
     * @return SchoolLevelEnum
     */
    public Integer getSchoolLevel(int schoolType, Integer teaStuNum) {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.service.sc.WmScSchoolService.getSchoolLevel(int,java.lang.Integer)");
        log.info("[WmScSchoolService.getSchoolLevel] input param. schoolType = {}, teaStuNum = {}", schoolType, teaStuNum);
        if (teaStuNum == null || SchoolTypeEnum.getByType(schoolType) == null) {
            return (int) SchoolLevelEnum.UNKNOWN.getType();
        }
        // 学校类型为双一流、211、985、省重点: A类分级
        if (schoolType == SchoolTypeEnum.THELEVEN.getType() || schoolType == SchoolTypeEnum.NHFIVE.getType()
                || schoolType == SchoolTypeEnum.THELEVEN_NHFIVE.getType() || schoolType == SchoolTypeEnum.PROVINCE_FOCUS.getType()) {
            if (teaStuNum >= 20000) {
                return (int) SchoolLevelEnum.A_FIRST.getType();
            } else if (teaStuNum >= 10000) {
                return (int) SchoolLevelEnum.A_SECOND.getType();
            } else if (teaStuNum > 5000){
                return (int) SchoolLevelEnum.A_THIRD.getType();
            } else {
                return (int) SchoolLevelEnum.A_FOURTH.getType();
            }
        }
        // 学校类型为普通本科: B类分级
        if (schoolType == SchoolTypeEnum.COMMON_PUBLIC.getType()) {
            if (teaStuNum >= 20000) {
                return (int) SchoolLevelEnum.B_FIRST.getType();
            } else if (teaStuNum >= 10000) {
                return (int) SchoolLevelEnum.B_SECOND.getType();
            } else if (teaStuNum > 5000){
                return (int) SchoolLevelEnum.B_THIRD.getType();
            } else {
                return (int) SchoolLevelEnum.B_FOURTH.getType();
            }
        }
        // 学校类型为专科、私立、其他: B类分级
        if (schoolType == SchoolTypeEnum.PRIVATE.getType() || schoolType == SchoolTypeEnum.TRAINING.getType()
                || schoolType == SchoolTypeEnum.OTHER.getType()) {
            if (teaStuNum >= 20000) {
                return (int) SchoolLevelEnum.C_FIRST.getType();
            } else if (teaStuNum >= 10000) {
                return (int) SchoolLevelEnum.C_SECOND.getType();
            } else if (teaStuNum > 5000){
                return (int) SchoolLevelEnum.C_THIRD.getType();
            } else {
                return (int) SchoolLevelEnum.C_FOURTH.getType();
            }
        }
        return (int) SchoolLevelEnum.UNKNOWN.getType();
    }

    /**
     * 新增学校-加锁解锁
     * @param wmSchoolDB wmSchoolDB
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    public void insertSchoolWithLock(WmSchoolDB wmSchoolDB) throws WmSchCantException {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.service.sc.WmScSchoolService.insertSchoolWithLock(com.sankuai.meituan.waimai.customer.domain.sc.WmSchoolDB)");
        // [尝试加锁] 若加锁失败代表当前有任务进行中
        String tairLockKey = SCHOOL_INSERT_LOCKKEY_PREFIX + wmSchoolDB.getWdcClueId().toString();
        wmScTairService.tryLockWithScene(tairLockKey, WmScTairLockSceneEnum.INSERT_SCHOOL);

        int insertResult = wmSchoolMapper.insertSchool(wmSchoolDB);
        log.info("[WmScSchoolService.insertSchoolWithLock] insertResult = {}, wmSchoolDB = {}", insertResult, JSONUtil.toJSONString(wmSchoolDB));
        // [尝试解锁] 若解锁失败可三次重试
        wmScTairService.unLockWithScene(tairLockKey, WmScTairLockSceneEnum.INSERT_SCHOOL);
    }

    /**
     * 批量修改学校责任人
     * @param wmScBatchModifySchooRpDTO wmScBatchModifySchooRpDTO
     * @throws TException org.apache.thrift.TException
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    public void batchUpdateSchoolResponsiblePerson(WmScBatchModifySchooRpDTO wmScBatchModifySchooRpDTO) throws TException, WmSchCantException {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.meituan.waimai.customer.service.sc.WmScSchoolService.batchUpdateSchoolResponsiblePerson(com.sankuai.meituan.waimai.thrift.customer.domain.sc.WmScBatchModifySchooRpDTO)");
        log.info("[WmScSchoolService.batchUpdateSchoolResponsiblePerson] input param: wmScBatchModifySchooRpDTO = {}",
                JSONObject.toJSONString(wmScBatchModifySchooRpDTO));
        if (wmScBatchModifySchooRpDTO == null
                || CollectionUtils.isEmpty(wmScBatchModifySchooRpDTO.getSchoolPirmaryIdList())
                || StringUtils.isBlank(wmScBatchModifySchooRpDTO.getResponsiblePersonMis())
                || wmScBatchModifySchooRpDTO.getResponsiblePersonUid() == null
                || wmScBatchModifySchooRpDTO.getResponsiblePersonUid() <= 0) {
            throw new WmSchCantException(BIZ_PARA_ERROR, "入参校验不通过");
        }

        WmSchoolSearchCondition condition = new WmSchoolSearchCondition();
        condition.setValid(YesOrNoEnum.YES.getCode());
        condition.setIdList(wmScBatchModifySchooRpDTO.getSchoolPirmaryIdList());
        // 查询涉及到的学校信息
        List<WmSchoolDB> wmSchoolDBList = wmSchoolMapper.selectByCondition(condition);
        if (CollectionUtils.isEmpty(wmSchoolDBList)) {
            throw new WmSchCantException(BIZ_PARA_ERROR, "修改的学校不存在");
        }
        // 若学校责任人相同, 则不需要修改
        List<WmSchoolDB> wmSchoolDBListUpdate = wmSchoolDBList.stream()
                .filter(wmSchoolDB -> !wmScBatchModifySchooRpDTO.getResponsiblePersonMis().equals(wmSchoolDB.getResponsiblePerson()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(wmSchoolDBListUpdate)) {
            log.info("[WmScSchoolService.batchUpdateSchoolResponsiblePerson] wmSchoolDBListUpdate is empty, return. wmScBatchModifySchooRpDTO = {}",
                    JSONObject.toJSONString(wmScBatchModifySchooRpDTO));
            return;
        }
        // 要修改的学校主键ID列表
        List<Integer> schoolPrimaryIdListUpdate = wmSchoolDBListUpdate.stream()
                .map(WmSchoolDB::getId)
                .collect(Collectors.toList());

        WmSchoolUpdateResponsiblePersonBO wmSchoolUpdateResponsiblePersonBO = new WmSchoolUpdateResponsiblePersonBO.Builder()
                .responsiblePerson(wmScBatchModifySchooRpDTO.getResponsiblePersonMis())
                .responsibleUid(wmScBatchModifySchooRpDTO.getResponsiblePersonUid())
                .idList(schoolPrimaryIdListUpdate)
                .userId(wmScBatchModifySchooRpDTO.getUserId())
                .userName(wmScBatchModifySchooRpDTO.getUserName())
                .build();
        log.info("[WmScSchoolService.batchUpdateSchoolResponsiblePerson] wmSchoolUpdateResponsiblePersonBO = {}",
                JSONObject.toJSONString(wmSchoolUpdateResponsiblePersonBO));
        int result = wmSchoolMapper.updateSchoolResponsiblePersonByBatch(wmSchoolUpdateResponsiblePersonBO);
        // 记录操作日志
        if (result > 0) {
            Map<Integer, String> logInfoMap = wmScLogService.getBatchModifySchoolRpLogInfoList(wmSchoolDBListUpdate, wmScBatchModifySchooRpDTO.getResponsiblePersonMis());
            wmScLogService.batchInsertSchoolOptLog(OptTypeEnum.UPDATE.getType(), wmScBatchModifySchooRpDTO.getUserId(),
                    wmScBatchModifySchooRpDTO.getUserName(), logInfoMap, "");
        }
    }

    /**
     * 去掉学校下所有的标签
     * @param schoolPrimaryId 学校主键ID
     * @param userId 用户ID
     * @param userName 用户名称
     */
    public void deleteSchoolAllLabel(Integer schoolPrimaryId, Integer userId, String userName) throws WmSchCantException {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.service.sc.WmScSchoolService.deleteSchoolAllLabel(java.lang.Integer,java.lang.Integer,java.lang.String)");
        log.info("[WmScSchoolService.deleteSchoolAllLabel] input param: schoolPrimaryId = {}, userId = {}, userName = {}",
                schoolPrimaryId, userId, userName);
        String schoolLabels = wmSchoolMapper.selectSchoolLabelsBySchoolId(schoolPrimaryId + SCHOOLID_BEGIN);
        try {
            if (StringUtils.isBlank(schoolLabels)) {
                log.info("[WmScSchoolService.deleteSchoolAllLabel] school labels is empty. schoolPrimaryId = {}", schoolPrimaryId);
                return;
            }

            List<Integer> labelIdList = Arrays.stream(schoolLabels.split(",")).map(Integer::valueOf).collect(Collectors.toList());
            for (Integer labelId : labelIdList) {
                wmPoiFlowlineLabelThriftServiceAdapter.deleteSchoolLabelRel(schoolPrimaryId + SCHOOLID_BEGIN, labelId, userId, userName);
            }
        } catch (Exception e) {
            log.error("[WmScSchoolService.deleteSchoolAllLabel] Exception. schoolPrimaryId = {}, schoolLabels = {}",
                    schoolPrimaryId, schoolLabels, e);
            Cat.logEvent(WmScMetricConstant.METRIC_SCHOOL_DELETE, "deleteSchoolAllLabel");
        }
    }

    /**
     * 通过平台名称精确查询学校合作平台信息列表
     *
     * @param content 合作平台名称
     * @return 合作平台信息列表
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     * @throws TException         org.apache.thrift.TException
     */
    public List<WmScSchoolCooperationPlatformDTO> getSchoolCooperationPlatformListByPlatformName(String content)
            throws WmSchCantException, TException {
        log.info("[WmScSchoolService.getSchoolCooperationPlatformListByPlatformName] input param: content = {}", content);
        if (StringUtils.isBlank(content)) {
            throw new WmSchCantException(BIZ_PARA_ERROR, "查询内容为空");
        }

        List<WmScSchoolCooperationPlatformDTO> dtoList = new ArrayList<>();
        List<WmScEnumDictDO> doList = wmScEnumDictMapper.selectCooperationPlatformByDesc(content);
        if (CollectionUtils.isEmpty(doList)) {
            return dtoList;
        }

        for (WmScEnumDictDO wmScEnumDictDO : doList) {
            WmScSchoolCooperationPlatformDTO dto = new WmScSchoolCooperationPlatformDTO();
            dto.setCooperationPlatformId(wmScEnumDictDO.getEnumCode());
            dto.setCooperationPlatformName(wmScEnumDictDO.getEnumDesc());
            dtoList.add(dto);
        }
        return dtoList;
    }


    public void updateSchoolCurrentDeliveryStatus(Integer schoolPrimaryId, Integer currentDeliveryStatus) throws WmSchCantException {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.service.sc.WmScSchoolService.updateSchoolCurrentDeliveryStatus(java.lang.Integer,java.lang.Integer)");
        log.info("[WmScSchoolService.updateSchoolCurrentDeliveryStatus] input param: schoolPrimaryId = {}, currentDeliveryStatus = {}",
                schoolPrimaryId, currentDeliveryStatus);
        WmSchoolDB wmSchoolDB = wmSchoolMapper.selectSchoolById(schoolPrimaryId);
        if (wmSchoolDB == null) {
            log.error("[WmScSchoolService.updateSchoolCurrentDeliveryStatus] wmSchoolDB is NULL. schoolPrimaryId = {}", schoolPrimaryId);
            throw new WmSchCantException(BIZ_PARA_ERROR, "查询学校为空");
        }

        if (wmSchoolDB.getCurrentDeliveryStatus().equals(currentDeliveryStatus)) {
            log.info("[WmScSchoolService.updateSchoolCurrentDeliveryStatus] currentDeliveryStatus is the same.");
            return;
        }

        wmSchoolMapper.updateSchoolCurrentDeliveryStatus(schoolPrimaryId, currentDeliveryStatus);
    }

    /**
     * 模糊搜索操作人拥有数据权限的学校
     * @param content 模糊搜索内容
     * @param uid 用户ID
     * @return List<SchoolBo>
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     * @throws TException org.apache.thrift.TException
     */
    public List<SchoolBo> getSchoolListByFuzzySearchWithAuth(String content, Integer uid) throws WmSchCantException, TException {
        log.info("[WmScSchoolService.getCanteenListByFuzzySearchWithAuth] input param: content = {}, uid = {}", content, uid);
        if (StringUtils.isBlank(content) || uid == null || uid <= 0) {
            log.error("[WmScSchoolService.getCanteenListByFuzzySearchWithAuth] content = {}, uid = {}", content, uid);
            throw new WmSchCantException(BIZ_PARA_ERROR, "查询入参校验通过");
        }

        // 1-从数据权限侧获取DSL查询语句
        String dslQuery = wmScSchoolAuthService.getSchoolListQueryDSL(uid);

        // 2-根据条件查询用于数据权限的学校列表
        List<WmSchoolDB> wmSchoolDBList = wmSchoolMapper.selectByLikeSchoolNameWithDSL(content, dslQuery);

        List<SchoolBo> schoolBoList =  WmScTransUtil.schoolTransDbToBo(wmSchoolDBList);
        log.info("[WmScSchoolService.getCanteenListByFuzzySearchWithAuth] schoolBoList = {}", JSONObject.toJSONString(schoolBoList));
        return schoolBoList;
    }

    /**
     * 判断学校是否在"食堂档口准确性提升"需求灰度列表中
     * @param schoolPrimaryId 学校主键ID
     * @return true: 走新逻辑 / false: 走老逻辑
     */
    public Boolean isSchoolInCanteenStallAccuracyGrayList(Integer schoolPrimaryId) {
        if (schoolPrimaryId == null || schoolPrimaryId <= 0) {
            log.warn("[WmScSchoolService.isSchoolInCanteenStallAccuracyGrayList] schoolPrimaryId is null. canteenPrimaryId = {}", schoolPrimaryId);
            return false;
        }

        try {
            GrayParams grayParams = new GrayParams();
            Map<String, String> map = new HashMap<>();
            map.put(WmScGrayPlatformConstant.SCHOOL_ID, String.valueOf(WmScTransUtil.getSchoolIdBySchoolPrimaryId(schoolPrimaryId)));
            grayParams.setOthers(map);
            String grayResult = GrayConfigClient.getGrayConfig(WmScGrayPlatformConstant.CANTEEN_STALL_ACCURACY_GRAY_SCHOOL_ID_KEY, grayParams);

            log.info("[WmScSchoolService.isSchoolInCanteenStallAccuracyGrayList] schoolPrimaryId = {}, grayResult = {}", schoolPrimaryId, grayResult);
            return Objects.equals(WmScGrayPlatformConstant.SUCCESS, grayResult);
        } catch (KeyNotExistGrayException | UnexpectedGrayException | InvalidParamGrayException e) {
            log.error("[WmScSchoolService.isSchoolInCanteenStallAccuracyGrayList] KeyNotExistGrayException | UnexpectedGrayException | InvalidParamGrayException. schoolPrimaryId = {},", schoolPrimaryId, e);
        } catch (Exception e) {
            log.error("[WmScSchoolService.isSchoolInCanteenStallAccuracyGrayList] Exception. schoolPrimaryId = {},", schoolPrimaryId, e);
        }
        return false;
    }

    /**
     * 获取最大的学校ID
     * @return 最大的学校ID
     */
    public Integer getMaxSchoolId() {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.service.sc.WmScSchoolService.getMaxSchoolId()");
        return wmSchoolMapper.getMaxSchoolId();
    }

    /**
     * 设置学校食堂品类数量
     * @param wmSchoolDBS
     */
    public void setCanteenCategoryNum(List<WmSchoolDB> wmSchoolDBS, WmSchoolDB wmSchoolDB) {
        if (CollectionUtils.isNotEmpty(wmSchoolDBS) ) {
            for (WmSchoolDB schoolDB : wmSchoolDBS) {
                if (wmSchoolDB != null && wmSchoolDB.getCanteenCategory() != null && wmSchoolDB.getCanteenCategory().size() > 0) {
                    //食堂表的school_id关联的是学校表的id
                    wmSchoolDB.setSchoolId(schoolDB.getId());
                    Integer canteenCategoryNum = canteenMapper.getCanteenCategoryNum(wmSchoolDB);
                    if (canteenCategoryNum != null) {
                        schoolDB.setCanteenCategoryNum(canteenCategoryNum);
                    }
                    if (schoolDB.getCanteenCategoryNum() == null) {
                        schoolDB.setCanteenCategoryNum(0);
                    }
                }else schoolDB.setCanteenCategoryNum(0);
            }
        }
    }
}