package com.sankuai.meituan.waimai.customer.service.customer.monitor;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Sets;
import com.sankuai.meituan.util.ConfigUtilAdapter;
import com.sankuai.meituan.waimai.customer.config.MccCustomerConfig;
import com.sankuai.meituan.waimai.customer.ddd.adapter.WmPoiQueryAdapter;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerDB;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerPoiAttributeDO;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerPoiAttributeService;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerPoiRelService;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerService;
import com.sankuai.meituan.waimai.thrift.constants.WmPoiFieldQueryConstant;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.monitor.MonitorCustomerPoiAttributeAgreeDTO;
import com.sankuai.meituan.waimai.thrift.customer.domain.kp.WmCustomerKp;
import com.sankuai.meituan.waimai.thrift.customer.service.WmCustomerKpThriftService;
import com.sankuai.meituan.waimai.thrift.domain.WmPoiAggre;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import joptsimple.internal.Strings;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 客户门店属性与Agree一致性监控
 *
 * <AUTHOR>
 * @date 2022年02月09日 7:42 PM
 */
@Slf4j
@Service
public class WmCustomerPoiAttributeAgreeMonitor {

    @Autowired
    private WmCustomerPoiAttributeService wmCustomerPoiAttributeService;

    @Autowired
    private WmCustomerService wmCustomerService;

    @Autowired
    private WmCustomerKpThriftService wmCustomerKpThriftService;

    @Autowired
    private WmPoiQueryAdapter WmPoiQueryAdapter;

    @Autowired
    private WmCustomerPoiRelService wmCustomerPoiRelService;


    private final String MSG_PREFIX = "门店客户属性&客户门店关系&Agree监控:";

    public String check(MonitorCustomerPoiAttributeAgreeDTO param) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_2", "com.sankuai.meituan.waimai.customer.service.customer.monitor.WmCustomerPoiAttributeAgreeMonitor.check(MonitorCustomerPoiAttributeAgreeDTO)");
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_2", "com.sankuai.meituan.waimai.customer.service.customer.monitor.WmCustomerPoiAttributeAgreeMonitor.check(MonitorCustomerPoiAttributeAgreeDTO)");
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_2", "com.sankuai.meituan.waimai.customer.service.customer.monitor.WmCustomerPoiAttributeAgreeMonitor.check(MonitorCustomerPoiAttributeAgreeDTO)");
        try {
            Long wmPoiId = param.getWmPoiId();
            if (wmPoiId == null || wmPoiId <= 0) {
                return Strings.EMPTY;
            }
            Integer customerId = wmCustomerService.selectWmCustomerIdByWmPoiId(wmPoiId);
            List<WmCustomerPoiAttributeDO> wmCustomerPoiAttributeDoList = wmCustomerPoiAttributeService.searchEffectAttributeByWmPoiId(wmPoiId);
            if ((customerId == null || customerId <= 0) && CollectionUtils.isEmpty(wmCustomerPoiAttributeDoList)) {
                //解绑后一致
                return Strings.EMPTY;
            }
            if (CollectionUtils.isNotEmpty(wmCustomerPoiAttributeDoList) && wmCustomerPoiAttributeDoList.size() > 1) {
                return String.format("%s客户门店属性记录存在多条记录:门店ID=%s", MSG_PREFIX, wmPoiId);
            }
            WmCustomerPoiAttributeDO wmCustomerPoiAttributeDO = CollectionUtils.isNotEmpty(wmCustomerPoiAttributeDoList) ? wmCustomerPoiAttributeDoList.get(0) : null;
            if (customerId == null || customerId <= 0) {
                if (wmCustomerPoiAttributeDO != null) {
                    //客户门店不存在绑定关系，但是存在客户门店属性记录
                    return String.format("%s客户门店不存在绑定关系但是存在客户门店属性记录:门店ID=%s", MSG_PREFIX, wmPoiId);
                } else {
                    return Strings.EMPTY;
                }
            }
            if (wmCustomerPoiAttributeDO == null) {
                if (customerId != null && customerId > 0) {
                    //存在客户门店属性记录但是不存在客户门店关系记录
                    return String.format("%s客户门店存在绑定关系但是不存在客户门店属性记录:门店ID=%s", MSG_PREFIX, wmPoiId);
                } else {
                    return Strings.EMPTY;
                }
            }
            WmCustomerDB wmCustomerDB = wmCustomerService.selectCustomerById(customerId);
            if (wmCustomerDB == null) {
                return String.format("%s根据门店查询客户信息失败wmPoiId:外卖门店ID=%s", MSG_PREFIX, wmPoiId);
            }
            if (!wmCustomerPoiAttributeDO.getMtCustomerId().equals(wmCustomerDB.getMtCustomerId())) {
                return String.format("%swm_customer_poi_attribute表mt_customer_id字段:%s与wm_customer表mt_customer_id字段:%s不一致", MSG_PREFIX, wmCustomerPoiAttributeDO.getMtCustomerId(), wmCustomerDB.getMtCustomerId());
            }
            //客户门店绑定关系与客户门店属性关系数量一致性对比
            if (ConfigUtilAdapter.getBoolean("customer_poi_rel_attribute_size_match_switch", true)) {
                //命中灰度走新的对比逻辑
                int poiRelAttributeNum = wmCustomerPoiAttributeService.countCustomerPoiRelAttribute(customerId);
                int poiNum = wmCustomerPoiRelService.countCustomerPoi(customerId);
                if (poiNum != poiRelAttributeNum) {
                    return String.format("%swm_customer_poi_attribute表数量与wm_customer_poi_rel表数量不一致:客户ID=%s,poiRelAttributeNum=%s,poiNum=%s", MSG_PREFIX, wmCustomerDB.getId(), poiRelAttributeNum, poiNum);
                }
            }
            WmCustomerKp wmCustomerKp = wmCustomerKpThriftService.getEffectSignerKp(customerId);
            boolean flag = wmCustomerKp == null
                    && (StringUtils.isNotEmpty(wmCustomerPoiAttributeDO.getKpCompellation())
                    || StringUtils.isNotEmpty(wmCustomerPoiAttributeDO.getPhoneNumEncryption())
                    || StringUtils.isNotEmpty(wmCustomerPoiAttributeDO.getPhoneNumToken()));
            if (flag) {
                return String.format("%s客户不存在生效的KP信息，但是wm_customer_poi_attribute表KP信息为kpCompellation(%s)PhoneNumEncryption(%s)PhoneNumToken(%s)", MSG_PREFIX,
                        wmCustomerPoiAttributeDO.getKpCompellation(), wmCustomerPoiAttributeDO.getPhoneNumEncryption(), wmCustomerPoiAttributeDO.getPhoneNumToken());
            }
            //门店客户属性与签约人数据一致性对比
            if (ConfigUtilAdapter.getBoolean("customer_poi_attribute_signer_match_switch", true)) {
                Map<String, Object> map = wmCustomerPoiAttributeService.selectCustomerPoiAttributeKp(customerId, wmPoiId);
                if (map != null && map.size() > 1) {
                    return String.format("%s不一致数据=%s，外卖门店ID=%s", MSG_PREFIX, JSONObject.toJSONString(map), wmPoiId);
                }
            }
            //agree同步签约人KP历史存在bug，所以增加开关控制是否监控，防止一直误报
            if (MccCustomerConfig.customerPoiAttributeAgreeMonitorSwitch()) {
                WmPoiAggre wmPoiAggre = WmPoiQueryAdapter.getWmPoiAggre(wmPoiId, Sets.newHashSet(WmPoiFieldQueryConstant.WM_POI_FIELD_MT_CUSTOMER_ID,
                        WmPoiFieldQueryConstant.WM_POI_FIELD_KP_COMPELLATION,
                        WmPoiFieldQueryConstant.WM_POI_FIELD_PHONE_NUM_ENCRYPTION, WmPoiFieldQueryConstant.WM_POI_FIELD_PHONE_NUM_TOKEN));
                if (wmPoiAggre == null) {
                    return String.format("%s查询Agree未查询到门店信息:外卖门店ID=%s", MSG_PREFIX, wmPoiId);
                }
                Map<String, Object> attributeMapForAgree = getAttributeMapForAggree(wmCustomerPoiAttributeDO);
                Map<String, Object> aggreeMap = getAgreeMap(wmPoiAggre);
                Set<String> diffKey = diffMap(attributeMapForAgree, aggreeMap);
                if (CollectionUtils.isNotEmpty(diffKey)) {
                    return diffMsg("门店客户属性&Agree监控:", attributeMapForAgree, aggreeMap, diffKey) + "外卖门店ID=" + wmPoiId;
                }
            }
        } catch (WmCustomerException e) {
            log.warn("check::param = {}", JSON.toJSONString(param), e);
            return String.format("%s客户门店属性与Agree一致性监控异常：" + e.getMsg(), MSG_PREFIX);
        } catch (Exception e) {
            log.error("check::param = {}", JSON.toJSONString(param), e);
        }
        return Strings.EMPTY;
    }

    private String diffMsg(String pre, Map<String, Object> mapA, Map<String, Object> mapB, Set<String> keySets) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.customer.monitor.WmCustomerPoiAttributeAgreeMonitor.diffMsg(java.lang.String,java.util.Map,java.util.Map,java.util.Set)");
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.customer.monitor.WmCustomerPoiAttributeAgreeMonitor.diffMsg(java.lang.String,java.util.Map,java.util.Map,java.util.Set)");
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.customer.monitor.WmCustomerPoiAttributeAgreeMonitor.diffMsg(java.lang.String,java.util.Map,java.util.Map,java.util.Set)");
        if (CollectionUtils.isEmpty(keySets)) {
            return Strings.EMPTY;
        }
        StringBuffer sb = new StringBuffer();
        sb.append(pre);
        for (String key : keySets) {
            sb.append(String.format("字段key为:%s，字段值value为:%s!=%s", key, mapA.get(key), mapB.get(key)));
        }
        return sb.toString();
    }



    private Map<String, Object> getAttributeMapForAggree(WmCustomerPoiAttributeDO wmCustomerPoiAttributeDO) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.service.customer.monitor.WmCustomerPoiAttributeAgreeMonitor.getAttributeMapForAggree(com.sankuai.meituan.waimai.customer.domain.WmCustomerPoiAttributeDO)");
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.service.customer.monitor.WmCustomerPoiAttributeAgreeMonitor.getAttributeMapForAggree(com.sankuai.meituan.waimai.customer.domain.WmCustomerPoiAttributeDO)");
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.service.customer.monitor.WmCustomerPoiAttributeAgreeMonitor.getAttributeMapForAggree(com.sankuai.meituan.waimai.customer.domain.WmCustomerPoiAttributeDO)");
        Map<String, Object> map = new HashMap<>();
        map.put("mtCustomerId", wmCustomerPoiAttributeDO.getMtCustomerId());
        map.put("compellation", wmCustomerPoiAttributeDO.getKpCompellation());
        map.put("phoneNumEncryption", wmCustomerPoiAttributeDO.getPhoneNumEncryption());
        map.put("phoneNumToken", wmCustomerPoiAttributeDO.getPhoneNumToken());
        return map;
    }

    private Map<String, Object> getAgreeMap(WmPoiAggre wmPoiAggre) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.customer.monitor.WmCustomerPoiAttributeAgreeMonitor.getAgreeMap(com.sankuai.meituan.waimai.thrift.domain.WmPoiAggre)");
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.customer.monitor.WmCustomerPoiAttributeAgreeMonitor.getAgreeMap(com.sankuai.meituan.waimai.thrift.domain.WmPoiAggre)");
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.customer.monitor.WmCustomerPoiAttributeAgreeMonitor.getAgreeMap(com.sankuai.meituan.waimai.thrift.domain.WmPoiAggre)");
        Map<String, Object> map = new HashMap<>();
        map.put("mtCustomerId", wmPoiAggre.getMt_customer_id());
        map.put("compellation", wmPoiAggre.getKp_compellation() == null ? "" : wmPoiAggre.getKp_compellation());
        map.put("phoneNumEncryption", wmPoiAggre.getPhone_num_encryption() == null ? "" : wmPoiAggre.getPhone_num_encryption());
        map.put("phoneNumToken", wmPoiAggre.getPhone_num_token() == null ? "" : wmPoiAggre.getPhone_num_token());
        return map;
    }

    /**
     * diffKey
     *
     * @param mapA
     * @param mapB
     * @return
     */
    private Set<String> diffMap(Map<String, Object> mapA, Map<String, Object> mapB) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.service.customer.monitor.WmCustomerPoiAttributeAgreeMonitor.diffMap(java.util.Map,java.util.Map)");
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.service.customer.monitor.WmCustomerPoiAttributeAgreeMonitor.diffMap(java.util.Map,java.util.Map)");
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.service.customer.monitor.WmCustomerPoiAttributeAgreeMonitor.diffMap(java.util.Map,java.util.Map)");
        Set<String> diffSet = Sets.newHashSet();
        for (String key : mapA.keySet()) {
            if (!mapA.get(key).equals(mapB.get(key))) {
                diffSet.add(key);
            }
        }
        return diffSet;
    }
}
