package com.sankuai.meituan.waimai.customer.service.customer.check.customer;

import com.alibaba.fastjson.JSON;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerDB;
import com.sankuai.meituan.waimai.customer.service.customer.check.BaseWmCustomerValidator;
import com.sankuai.meituan.waimai.customer.service.customer.check.IWmCustomerValidator;
import com.sankuai.meituan.waimai.customer.service.gray.WmCustomerGrayService;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerConstants;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerRealTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerSource;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.CustomerRealTypeSpInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.ValidateResultBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.WmCustomerBasicBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 美食城客户保存参数基础校验
 * 只校验通用逻辑
 */
@Service
public class WmCustomerMscParamValidator extends BaseWmCustomerValidator implements IWmCustomerValidator {

    private static final Logger log = LoggerFactory.getLogger(WmCustomerMscParamValidator.class);
    @Autowired
    private WmCustomerGrayService wmCustomerGrayService;

    @Override
    public ValidateResultBo valid(ValidateResultBo validateResultBo, WmCustomerBasicBo wmCustomerBasicBo, Boolean force, Boolean isAudit, Integer opUid) throws WmCustomerException {
        //判断是否命中客户责任人组织架构灰度
        Integer ownerUid = opUid;
        if(wmCustomerBasicBo.getId() > 0){
            WmCustomerDB wmCustomerDB = getExistWmCustomer(wmCustomerBasicBo.getId());
            ownerUid = wmCustomerDB.getOwnerUid();
        }
        if (!wmCustomerGrayService.isGrayMscPoiCntCheckNew(ownerUid)) {
            return checkPass(validateResultBo);
        }
        log.info("命中美食城新参数校验器:{}",JSON.toJSONString(wmCustomerBasicBo));
        // 判断客户类型是否为美食城
        if (wmCustomerBasicBo.getCustomerRealType() != CustomerRealTypeEnum.MEISHICHENG.getValue()) {
            return checkPass(validateResultBo);
        }
        if (wmCustomerBasicBo.getCustomerSource() == CustomerSource.WAIMAI_BD) {
            return checkMscPoiCnt(wmCustomerBasicBo,validateResultBo);
        }
        // 非BD操作
        return notBdOperateCheck(wmCustomerBasicBo,validateResultBo);

    }

    /**
     * 非BD操作校验
     * 1、新建场景下，操作来源非先富/蜜蜂，不校验是否为空
     * 2、修改客户场景下，客户没有生效，且操作来源非先富/蜜蜂，不校验是否为空
     * @param wmCustomerBasicBo
     * @return
     */
    private ValidateResultBo notBdOperateCheck(WmCustomerBasicBo wmCustomerBasicBo,ValidateResultBo validateResultBo) throws WmCustomerException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.service.customer.check.customer.WmCustomerMscParamValidator.notBdOperateCheck(WmCustomerBasicBo,ValidateResultBo)");
        if (wmCustomerBasicBo.getId() == 0){
            return checkPass(validateResultBo);
        }
        WmCustomerDB wmCustomerDB = getExistWmCustomer(wmCustomerBasicBo.getId());
        if (wmCustomerDB == null) {
            return checkFail(validateResultBo, CustomerConstants.RESULT_CODE_ERROR, "客户不存在");
        }
        if(wmCustomerDB.isUnEffectived()){
            return checkPass(validateResultBo);
        }
        return checkMscPoiCnt(wmCustomerBasicBo,validateResultBo);

    }

    private ValidateResultBo checkMscPoiCnt(WmCustomerBasicBo wmCustomerBasicBo ,ValidateResultBo validateResultBo) {
        log.info("BD上单渠道校验美食城保存参数:{}", JSON.toJSONString(wmCustomerBasicBo));
        CustomerRealTypeSpInfoBo customerRealTypeSpInfoBo = wmCustomerBasicBo.getCustomerRealTypeSpInfoBo();
        //扩展信息不能为空
        if (customerRealTypeSpInfoBo == null) {
            return checkFail(validateResultBo, CustomerConstants.RESULT_CODE_ERROR, "美食城客户扩展信息不能为空");
        }
        //档口数量有则校验必须是正整数
        if (customerRealTypeSpInfoBo.getFoodCityPoiCount() != null && customerRealTypeSpInfoBo.getFoodCityPoiCount() <= 0) {
            return checkFail(validateResultBo, CustomerConstants.RESULT_CODE_ERROR, "档口数量必须为正整数");
        }
        //添加档口数上限要求
        if (customerRealTypeSpInfoBo.getFoodCityPoiCount() != null && customerRealTypeSpInfoBo.getFoodCityPoiCount() > 10000) {
            return checkFail(validateResultBo, CustomerConstants.RESULT_CODE_ERROR, "档口数量不能超过10000");
        }
        // 档口数和美食城视频都不能为空
        if (customerRealTypeSpInfoBo.getFoodCityPoiCount() == null || StringUtils.isBlank(customerRealTypeSpInfoBo.getFoodCityVideo())) {
            return checkFail(validateResultBo, CustomerConstants.RESULT_CODE_ERROR, "档口数量和美食城视频均不能为空");
        }
        return checkPass(validateResultBo);
    }



}
