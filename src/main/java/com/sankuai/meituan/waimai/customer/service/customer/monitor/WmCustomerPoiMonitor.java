package com.sankuai.meituan.waimai.customer.service.customer.monitor;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.sankuai.meituan.banma.thrift.deliverycrm.customer.facade.domain.dto.yitihua.monitor.BmWmIDMappingDTO;
import com.sankuai.meituan.banma.thrift.deliverycrm.customer.facade.iface.yitihua.monitor.OpenYthBmWmDataMappingQueryFacade;
import com.sankuai.meituan.banma.thrift.deliverycrm.customer.facade.open.exception.OpenBmCustomerFacadeException;
import com.sankuai.meituan.waimai.customer.adapter.BaseInfoQueryPhysicalPoiThriftServiceAdapter;
import com.sankuai.meituan.waimai.customer.adapter.WmPoiFlowlineLabelThriftServiceAdapter;
import com.sankuai.meituan.waimai.customer.adapter.WmPoiOplogCommonThriftServiceAdaptor;
import com.sankuai.meituan.waimai.customer.dao.WmCustomerDBMapper;
import com.sankuai.meituan.waimai.customer.dao.WmCustomerPoiDBMapper;
import com.sankuai.meituan.waimai.customer.dao.WmCustomerPoiRelExtensionMapper;
import com.sankuai.meituan.waimai.customer.ddd.adapter.WmPoiQueryAdapter;
import com.sankuai.meituan.waimai.customer.ddd.domain.customer.aggre.WmCustomerPoiAggre;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerDB;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerPoiDB;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerPoiRelExtension;
import com.sankuai.meituan.waimai.label.constants.LabelSubjectTypeEnum;
import com.sankuai.meituan.waimai.oplog.thrift.domain.SearchConditionVo;
import com.sankuai.meituan.waimai.oplog.thrift.domain.WmOplogNew;
import com.sankuai.meituan.waimai.poi.constants.attribute.WmPoiValidEnum;
import com.sankuai.meituan.waimai.poi.domain.WmPoiOpLog;
import com.sankuai.meituan.waimai.poibaseinfo.thrift.domain.WmPhysicalPoiBaseSimpleInfo;
import com.sankuai.meituan.waimai.poibaseinfo.thrift.domain.WmPhysicalPoiRel;
import com.sankuai.meituan.waimai.poibaseinfo.thrift.domain.result.BaseInfoLong;
import com.sankuai.meituan.waimai.poibaseinfo.thrift.exception.BaseInfoServerException;
import com.sankuai.meituan.waimai.poibaseinfo.thrift.service.BaseInfoQueryPhysicalPoiThriftService;
import com.sankuai.meituan.waimai.poibaseinfo.thrift.service.BaseInfoQueryThriftService;
import com.sankuai.meituan.waimai.thrift.constants.WmPoiFieldQueryConstant;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerRealTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.company.CompanyCustomerConstants;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.monitor.MonitorCustomerPoiDTO;
import com.sankuai.meituan.waimai.thrift.domain.WmPoiAggre;
import com.sankuai.meituan.waimai.thrift.domain.WmPoiLabelRel;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import com.sankuai.meituan.waimai.thrift.util.ConfigUtilAdapter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class WmCustomerPoiMonitor {

    @Autowired
    private WmCustomerPoiDBMapper wmCustomerPoiDBMapper;

    @Autowired
    private WmCustomerPoiRelExtensionMapper wmCustomerPoiRelExtensionMapper;

    @Autowired
    private WmCustomerDBMapper wmCustomerDBMapper;

    @Autowired
    private OpenYthBmWmDataMappingQueryFacade.Iface openYthBmWmDataMappingQueryFacade;

    @Autowired
    private WmPoiFlowlineLabelThriftServiceAdapter wmPoiFlowlineLabelThriftServiceAdapter;

    @Autowired
    private BaseInfoQueryThriftService baseInfoQueryThriftService;

    @Autowired
    private WmPoiQueryAdapter wmPoiQueryAdapter;

    @Autowired
    private BaseInfoQueryPhysicalPoiThriftService baseInfoQueryPhysicalPoiThriftService;

    @Autowired
    private BaseInfoQueryPhysicalPoiThriftServiceAdapter baseInfoQueryPhysicalPoiThriftServiceAdapter;

    private static final int IS_DELETE_NOT = 0;//非逻辑删除

    @Autowired
    private WmPoiOplogCommonThriftServiceAdaptor wmPoiOplogCommonThriftServiceAdaptor;

    public String check(MonitorCustomerPoiDTO info) {
        if (info == null || info.getCustomerId() == null || CollectionUtils.isEmpty(info.getWmPoiIdList())) {
            return String.format("参数不全:info:%s", JSONObject.toJSONString(info));
        }
        if (!checkQikeCustomer(info.getCustomerId())) {
            return "";
        }
        if (info.getEventType() == 3) {
            return checkBind(info);
        } else if (info.getEventType() == 4) {
            return checkUnBind(info);
        }
        return String.format("未知的操作类型:info:%s", JSONObject.toJSONString(info));
    }

    private boolean checkQikeCustomer(int customerId) {
        WmCustomerDB wmCustomerDB = wmCustomerDBMapper.selectCustomerById(customerId);
        if (wmCustomerDB == null) {
            return false;
        }
        long qkLabelId = ConfigUtilAdapter.getLong("config_company_label_id", 260L);

        WmPoiLabelRel customerLabel = wmPoiFlowlineLabelThriftServiceAdapter.selectLabelRelCache(wmCustomerDB.getMtCustomerId(), qkLabelId, LabelSubjectTypeEnum.CUSTOMER.getCode());
        log.info("checkQikeCustomer mtCustomerId={},customerLabel={}", wmCustomerDB.getMtCustomerId(), JSONObject.toJSONString(customerLabel));
        if (customerLabel == null || customerLabel.getId() <= 0L) {
            return false;
        }
        return true;
    }


    // 绑定 ：1、客户门店关系在 2、门店管理企客关系在 3、企客门店绑定关系在
    private String checkBind(MonitorCustomerPoiDTO info) {
        List<Long> wmPoiIdList = getExistWmPoiIdList(info.getWmPoiIdList());
        if (CollectionUtils.isEmpty(wmPoiIdList)) {
            return "";
        }
        List<WmCustomerPoiDB> wmCustomerPoiDBList = wmCustomerPoiDBMapper.selectWmCustomerPoiByWmPoiIdList(wmPoiIdList);
        if (CollectionUtils.isEmpty(wmCustomerPoiDBList)) {
            return String.format("客户门店关系未找到:wmCustomerId:%s,wmPoiIds:%s", info.getCustomerId(), JSONObject.toJSONString(info.getWmPoiIdList()));
        }
        Map<Long, Integer> poiMapCustomerId = wmCustomerPoiDBList.stream().collect(Collectors.toMap(WmCustomerPoiDB::getWmPoiId, x -> x.getCustomerId()));

        List<WmCustomerPoiRelExtension> wmCustomerPoiRelExtensionList = wmCustomerPoiRelExtensionMapper.selectByWmPoiIdListAndBizType(wmPoiIdList, CompanyCustomerConstants.COMPANY_CUSTOMER_BIZ_TYPE);
        boolean isBindFail = CollectionUtils.isEmpty(wmCustomerPoiRelExtensionList);
        if (isBindFail) {
            List<Long> validWmPoiId = filterNoWdcId(wmPoiIdList);
            if (CollectionUtils.isNotEmpty(validWmPoiId)) {
                return String.format("企客门店入驻失败:wmCustomerId:%s,wmPoiIds:%s", info.getCustomerId(), JSONObject.toJSONString(validWmPoiId));
            } else {
                return "";
            }
        }

        Map<Long, Long> poiMapQikeId = getWmPoiQikeInfo(info.getWmPoiIdList());
        if (poiMapQikeId.isEmpty()) {
            return String.format("配送侧企客门店关系未找到:wmCustomerId:%s,wmPoiIds:%s", info.getCustomerId(), JSONObject.toJSONString(info.getWmPoiIdList()));
        }

        Map<Long, Long> poiMapBizId = wmCustomerPoiRelExtensionList.stream().collect(Collectors.toMap(WmCustomerPoiRelExtension::getWmPoiId, x -> x.getBizId()));
        StringBuffer errMsg = new StringBuffer();
        for (Long wmPoiId : wmPoiIdList) {
            Integer currentCustomerId = poiMapCustomerId.get(wmPoiId);
            if (currentCustomerId == null || !currentCustomerId.equals(info.getCustomerId())) {
                errMsg.append(String.format("客户门店关系映射错误 wmPoiId:%s,currentCustomerId:%s,needCustomerId:%s", wmPoiId, currentCustomerId, info.getCustomerId()));
            }
            Long needBizId = poiMapBizId.get(wmPoiId);
            if (needBizId == null) {
                errMsg.append(String.format("企客门店关系映射错误 wmPoiId:%s,needBizId:%s", wmPoiId, needBizId));
            }

            Long qikeId = poiMapQikeId.get(wmPoiId);
            if (qikeId == null) {
                errMsg.append(String.format("配送侧企客门店关系映射错误 wmPoiId:%s,needBizId:%s", wmPoiId, needBizId));
            }
            if (needBizId != null && qikeId != null && needBizId.longValue() != qikeId.longValue()) {
                errMsg.append(String.format("企客门店关系不一致 wmPoiId:%s,currentBizId:%s,needBizId:%s", wmPoiId, qikeId, needBizId));
            }
        }
        return errMsg.toString();
    }

    // 解绑：1、客户门店关系不在 2、企客门店绑定关系解绑
    private String checkUnBind(MonitorCustomerPoiDTO info) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.service.customer.monitor.WmCustomerPoiMonitor.checkUnBind(com.sankuai.meituan.waimai.thrift.customer.domain.customer.monitor.MonitorCustomerPoiDTO)");
        List<Long> wmPoiIdList = info.getWmPoiIdList();
        List<WmCustomerPoiDB> wmCustomerPoiDBList = wmCustomerPoiDBMapper.selectWmCustomerPoiByWmPoiIdList(wmPoiIdList);
        if (CollectionUtils.isNotEmpty(wmCustomerPoiDBList)) {
            List<Long> needUnbindWmPoiIdList = wmCustomerPoiDBList.stream().map(x -> x.getWmPoiId()).collect(Collectors.toList());
            return String.format("客户门店关系未解除:wmCustomerId:%s,wmPoiIds:%s", info.getCustomerId(), JSONObject.toJSONString(needUnbindWmPoiIdList));
        }
        Map<Long, Long> poiMapQikeId = getWmPoiQikeInfo(info.getWmPoiIdList());
        if (!poiMapQikeId.isEmpty()) {
            return String.format("配送侧企客门店关系未解除:wmCustomerId:%s,wmPoiIds:%s", info.getCustomerId(), JSONObject.toJSONString(info.getWmPoiIdList()));
        }
        return "";
    }

    private Map<Long, Long> getWmPoiQikeInfo(List<Long> wmPoiIdList) {
        try {
            List<BmWmIDMappingDTO> bmWmIdList = openYthBmWmDataMappingQueryFacade.batchGetBmCustomerIdsByWmPoiIds(wmPoiIdList);
            log.info("getWmPoiQikeInfo wmPoiIdList={},bmWmIdList={}", JSONObject.toJSONString(wmPoiIdList), JSONObject.toJSONString(bmWmIdList));
            if (CollectionUtils.isEmpty(bmWmIdList)) {
                return Maps.newHashMap();
            }
            return bmWmIdList.stream().collect(Collectors.toMap(BmWmIDMappingDTO::getWmId, x -> x.getBmId()));
        } catch (OpenBmCustomerFacadeException e) {
            log.error("batchGetBmCustomerIdsByWmPoiIds error wmPoiIdList={}", JSONObject.toJSONString(wmPoiIdList), e);
        } catch (Exception e) {
            log.error("batchGetBmCustomerIdsByWmPoiIds error wmPoiIdList={}", JSONObject.toJSONString(wmPoiIdList), e);
        }
        return Maps.newHashMap();
    }


    private List<Long> filterNoWdcId(List<Long> noRelationWmPoiIds) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.service.customer.monitor.WmCustomerPoiMonitor.filterNoWdcId(java.util.List)");
        List<Long> validWmPoiId = Lists.newArrayList();
        for (Long wmPoiId : noRelationWmPoiIds) {
            try {
                BaseInfoLong baseInfoLong = baseInfoQueryThriftService.queryWdcIdFromBaseInfo(wmPoiId);
                if (baseInfoLong != null && baseInfoLong.getValue() != null && baseInfoLong.getValue().longValue() > 0l) {
                    validWmPoiId.add(wmPoiId);
                }
            } catch (BaseInfoServerException e) {
                log.error("queryWdcIdFromBaseInfo error wmPoiId={}", wmPoiId, e);
                validWmPoiId.add(wmPoiId);
            } catch (Exception e) {
                log.error("queryWdcIdFromBaseInfo error wmPoiId={}", wmPoiId, e);
                validWmPoiId.add(wmPoiId);
            }
        }
        return validWmPoiId;
    }


    private List<Long> getExistWmPoiIdList(List<Long> wmPoiIdList) {
        if (CollectionUtils.isEmpty(wmPoiIdList)) {
            return Lists.newArrayList();
        }
        List<WmPoiAggre> wmPoiAggreList = wmPoiQueryAdapter.getWmPoiAggre(wmPoiIdList, Sets.newHashSet(WmPoiFieldQueryConstant.WM_POI_FIELD_WM_POI_ID, WmPoiFieldQueryConstant.WM_POI_FIELD_IS_DELETE));
        if (CollectionUtils.isEmpty(wmPoiAggreList)) {
            return Lists.newArrayList();
        }
        return wmPoiAggreList.stream().filter(x -> x.getIs_delete() == IS_DELETE_NOT).map(x -> x.getWm_poi_id()).collect(Collectors.toList());
    }



    public String monitorUnbind(List<Long> wmPoiIdList){
        log.info("monitorUnbind wmPoiIdList={}", JSONObject.toJSONString(wmPoiIdList));
        List<WmPoiAggre> wmPoiAggreList = wmPoiQueryAdapter.getWmPoiAggre(wmPoiIdList,
                Sets.newHashSet(WmPoiFieldQueryConstant.WM_POI_FIELD_WM_POI_ID, WmPoiFieldQueryConstant.WM_POI_FIELD_VALID));
        // 非下线状态忽略
        List<Long> offlineWmPoiIdList = wmPoiAggreList.stream()
                .filter(x -> x != null && x.getValid() == WmPoiValidEnum.OFFLINE.getValue())
                .map(x -> x.getWm_poi_id())
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(offlineWmPoiIdList)) {
            return "";
        }

        Long currentTime = System.currentTimeMillis() / 1000;
        int year = 365 * 24 * 60 * 60;
        List<Long> errorWmPoiIdList = Lists.newArrayList();
        for (Long wmPoiId : offlineWmPoiIdList) {
            WmOplogNew wmOplogNew = getPoiStatus(wmPoiId, currentTime - year, currentTime);
            if (wmOplogNew != null && wmOplogNew.getOpType() == WmPoiOpLog.OpType.POI_VALID_ONLINE.getId()) {
                errorWmPoiIdList.add(wmPoiId);
            }
        }
        if (CollectionUtils.isNotEmpty(errorWmPoiIdList)) {
            return String.format("上线门店被解绑:%s", JSONObject.toJSONString(errorWmPoiIdList));
        }
        return "";
    }


    private WmOplogNew getPoiStatus(Long wmPoiId, Long startTime, Long endTime) {
        List<WmOplogNew> wmOplogNewList = Lists.newArrayList();
        SearchConditionVo searchConditionVo = new SearchConditionVo();
        searchConditionVo.setWmPoiId(wmPoiId);
        searchConditionVo.setOpType(WmPoiOpLog.OpType.POI_VALID_ONLINE.getId());
        searchConditionVo.setOffset(0L);
        searchConditionVo.setSize(5L);
        searchConditionVo.setStartUnixTime(startTime);
        searchConditionVo.setEndUnixTime(endTime);
        List<WmOplogNew> onLineList = wmPoiOplogCommonThriftServiceAdaptor.getOplogDiff(searchConditionVo);
        log.info("getPoiStatus onLineList ={}", JSONObject.toJSONString(onLineList));
        wmOplogNewList.addAll(onLineList);
        searchConditionVo.setWmPoiId(wmPoiId);
        searchConditionVo.setOpType(WmPoiOpLog.OpType.POI_VALID_OFFLINE.getId());
        List<WmOplogNew> offLineList = wmPoiOplogCommonThriftServiceAdaptor.getOplogDiff(searchConditionVo);
        log.info("getPoiStatus offLineList ={}", JSONObject.toJSONString(offLineList));
        wmOplogNewList.addAll(offLineList);
        if (CollectionUtils.isEmpty(wmOplogNewList)) {
            return null;
        }
        Collections.sort(wmOplogNewList, Comparator.comparing(WmOplogNew::getId).reversed());
        return wmOplogNewList.get(0);
    }

    public String monitorPoiBind(MonitorCustomerPoiDTO monitorCustomerPoiDTO) {
        WmCustomerDB wmCustomerDB = wmCustomerDBMapper.selectCustomerById(monitorCustomerPoiDTO.getCustomerId());

        // 非单店、单店药品、闪购单店不需要校验
        if (wmCustomerDB.getCustomerRealType() != CustomerRealTypeEnum.DANDIAN.getValue() &&
                wmCustomerDB.getCustomerRealType() != CustomerRealTypeEnum.DANDIAN_YAOPIN.getValue() &&
                wmCustomerDB.getCustomerRealType() != CustomerRealTypeEnum.DANDIAN_SG.getValue()) {
            return "";
        }
        // 根据客户ID获取门店Ids
        List<Long> wmPoiIds = WmCustomerPoiAggre.Factory.make().selectWmPoiIdsByCustomerId(monitorCustomerPoiDTO.getCustomerId());
        // 门店数量不超过1不进行校验
        if (wmPoiIds.size() <= 1) {
            return "";
        }
        try {
            // 获取经营门店集合绑定的物理门店id集合
            List<WmPhysicalPoiRel> physicalPoiRels = baseInfoQueryPhysicalPoiThriftServiceAdapter.getPhysicalPoiRelList(wmPoiIds);
            // 校验门店是否都存在物理门店
            if (!exitPhysicalPoi(wmPoiIds, physicalPoiRels)) {
                log.info("单店校验不通过，客户已绑定物理门店与当前经营门店{}绑定物理门店不一致", JSON.toJSONString(monitorCustomerPoiDTO.getWmPoiIdList()));
                return String.format("单店校验不通过，客户已绑定物理门店与当前经营门店%s绑定物理门店不一致", JSON.toJSONString(monitorCustomerPoiDTO.getWmPoiIdList()));
            }
            if (getPhysicalPoiCount(physicalPoiRels) != 1) {
                log.info("单店校验不通过，客户已绑定物理门店与当前经营门店{}绑定物理门店不一致", JSON.toJSONString(monitorCustomerPoiDTO.getWmPoiIdList()));
                return String.format("单店校验不通过，客户已绑定物理门店与当前经营门店%s绑定物理门店不一致", JSON.toJSONString(monitorCustomerPoiDTO.getWmPoiIdList()));
            }
        } catch (WmCustomerException e) {
            log.error("客户绑定门店状态监控[客户类型为单店模式下校验经营门店是否绑定同一物理门店]校验失败, 客户id={},门店id={},error={}", monitorCustomerPoiDTO.getCustomerId(), JSON.toJSON(monitorCustomerPoiDTO.getWmPoiIdList()), e.getMessage());
        }
        return "";
    }

    /**
     * 获取客户已绑定与将绑定经营门店绑定不同物理门店数量
     * @param physicalPoiRelList
     * @return
     */
    private Integer getPhysicalPoiCount(List<WmPhysicalPoiRel> physicalPoiRelList) {
        //2. 对经营门店绑定的物理门店去重
        HashSet<Long> physicalPoiIds = new HashSet<>();
        for (WmPhysicalPoiRel wmPhysicalPoiRel : physicalPoiRelList) {
            physicalPoiIds.add(wmPhysicalPoiRel.getWmPhysicalPoiId());
        }
        return physicalPoiIds.size();
    }

    /**
     * 校验门店是否都存在物理门店
     * @param wmPoiIds
     * @param physicalPoiRelList
     * @return
     */
    private boolean exitPhysicalPoi(List<Long> wmPoiIds, List<WmPhysicalPoiRel> physicalPoiRelList) {
        if (org.springframework.util.CollectionUtils.isEmpty(physicalPoiRelList)) {
            return false;
        }
        Map<Long, Long> map = new HashMap<>();
        for (WmPhysicalPoiRel wmPhysicalPoiRel : physicalPoiRelList) {
            if (wmPhysicalPoiRel.getWmPhysicalPoiId() != null) {
                map.put(wmPhysicalPoiRel.getWmPoiId(), wmPhysicalPoiRel.getWmPhysicalPoiId());
            }
        }
        for (Long wmPoiId : wmPoiIds) {
            if (!map.containsKey(wmPoiId)) {
                return false;
            }
        }
        return true;
    }
}
