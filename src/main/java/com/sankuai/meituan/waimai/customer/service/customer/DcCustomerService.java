package com.sankuai.meituan.waimai.customer.service.customer;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.sankuai.meituan.customer.thrift.common.TPage;
import com.sankuai.meituan.customer.thrift.dto.TQueryPartnerBasicReq;
import com.sankuai.meituan.waimai.customer.adapter.daocan.DcCustomerBaseAdapter;
import com.sankuai.meituan.waimai.customer.config.MccCustomerConfig;
import com.sankuai.meituan.waimai.customer.ddd.adapter.MtCustomerKpAdapter;
import com.sankuai.meituan.waimai.customer.ddd.adapter.MtCustomerThriftServiceAdapter;
import com.sankuai.meituan.waimai.customer.domain.DcCustomerKp;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerDB;
import com.sankuai.meituan.waimai.customer.service.customer.dto.DcCustomerKpQueryDto;
import com.sankuai.meituan.waimai.thrift.customer.constant.*;
import com.sankuai.meituan.waimai.thrift.customer.constant.customer.CertificateTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.customer.CustomerBizOrgEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.WmCustomerBasicBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import com.sankuai.meituan.waimai.thrift.util.ConfigUtilAdapter;
import com.sankuai.meituan.waimai.util.StringUtil;
import com.sankuai.nibcus.inf.customer.client.dto.SignerDTO;
import com.sankuai.nibcus.inf.customer.client.enums.BusinessLineEnum;
import com.sankuai.nibcus.inf.customer.client.request.QualificationNumTypeGetCustomerIdRequest;
import com.sankuai.nibcus.inf.customer.client.request.SignerGetRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;

import static com.sankuai.meituan.waimai.thrift.customer.constant.CustomerConstants.CUSTOMER_IS_LEAF_YES;

/**
 * 到餐客户业务
 */
@Service
@Slf4j
public class DcCustomerService {

    @Autowired
    private MtCustomerThriftServiceAdapter mtCustomerThriftServiceAdapter;

    @Autowired
    private WmCustomerService wmCustomerService;

    @Autowired
    private MtCustomerKpAdapter mtCustomerKpAdapter;

    @Autowired
    private DcCustomerBaseAdapter dcCustomerBaseAdapter;

    @Autowired
    private WmCustomerPlatformDataParseService wmCustomerPlatformDataParseService;


    /**
     * 保存仅到餐客户
     * 1、到餐客户绑定下沉门店
     * 2、历史下沉客户数据清洗
     */
    public void saveDcCustomer(Long mtCustomerId) throws WmCustomerException, TException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.service.customer.DcCustomerService.saveDcCustomer(java.lang.Long)");
        log.info("#saveDcCustomer-保存仅到餐客户:{}",mtCustomerId);
        if (mtCustomerId <= 0){
            return;
        }
        // 平台客户ID是否存在外卖客户
        WmCustomerBasicBo customerByIdOrMtCustomerId = wmCustomerService.getCustomerByIdOrMtCustomerId(mtCustomerId);
        if (customerByIdOrMtCustomerId != null){
            return;
        }
        //查询客户平台信息
        WmCustomerDB customerDB = mtCustomerThriftServiceAdapter.getCustomerByIdAndBusinessLine(mtCustomerId,BusinessLineEnum.NIB_FOOD.getCode());
        //按到餐客户的资质查询外卖侧有无数据，如果有则不处理
        // 消息重发，在上边按平台ID查客户的时候会被拦截
        // 一个资质在到餐侧有多个客户的情况下，在此处会新建两个仅到餐客户
        WmCustomerBasicBo currentWmCustomer = wmCustomerService.getCustomerByNumberAndTypeAndBizCode(customerDB.getCustomerNumber(), customerDB.getCustomerType(), CustomerBizOrgEnum.WAI_MAI.getCode());
        if (currentWmCustomer != null){
            log.info("到餐客户资质存在外卖业务线下客户:外卖客户ID：{}",currentWmCustomer.getId());
            return;
        }
        // 按资质查询在外卖侧是否存在仅到餐客户
        WmCustomerDB dcCustomerDb = queryDCCustomerByCustomerNumber(customerDB.getCustomerNumber());
        if (dcCustomerDb != null){
            log.info("到餐客户资质已存在仅到餐客户：客户ID:{}",dcCustomerDb.getId());
            return;
        }
        log.info("#saveDcCustomer-保存仅到餐客户开始执行:{}",mtCustomerId);
        wmCustomerService.insertCustomer(buildDefaultCustomerDB(customerDB));
        log.info("#saveDcCustomer-保存仅到餐客户-结束:{}",mtCustomerId);
    }

    private WmCustomerDB queryDCCustomerByCustomerNumber(String customerNumber) throws TException, WmCustomerException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.customer.DcCustomerService.queryDCCustomerByCustomerNumber(java.lang.String)");
        log.info("#queryDCCustomerByCustomerNumber-根据资质编码查询仅到餐客户：{}",customerNumber);
        if(StringUtils.isBlank(customerNumber)){
            return null;
        }
        // 先查询客户平台侧，此资质是否存在到餐业务线下的客户
        QualificationNumTypeGetCustomerIdRequest request = new QualificationNumTypeGetCustomerIdRequest();
        request.setBusinessLineId(BusinessLineEnum.NIB_FOOD.getCode());
        request.setQualificationNum(customerNumber);
        List<Long> customerList = mtCustomerThriftServiceAdapter.getCustomerIdByQualificationNum(request);
        if (CollectionUtils.isEmpty(customerList)){
            return null;
        }
        // 再判断资质对应的到餐业务线下的平台ID是否在外卖业务线下有客户
        List<WmCustomerDB> wmCustomerDBList = wmCustomerPlatformDataParseService.selectCustomerFromPlatformByIdOrMtCustomerId(new HashSet<>(customerList));
        if (CollectionUtils.isEmpty(wmCustomerDBList)){
            return null;
        }
        // 找出客户类型为仅到餐的客户
        return wmCustomerDBList.stream().filter(customer -> customer.getCustomerRealType() == CustomerRealTypeEnum.DAOCAN.getValue()).findFirst().orElse(null);
    }

    /**
     * 根据uid获取全部有权限的到餐客户 平台客户ID列表
     * 底层接口为分页接口，每次1页查500，查完全部之后聚合
     * @param uid
     * @return
     */
    public Set<Long> getAllMtCustomerIdByUid(Integer uid) throws TException, WmCustomerException {
        log.info("#getAllMtCustomerIdByUid-根据uid获取全部有权限的到餐客户平台客户ID列表-uid:{}",uid);
        TQueryPartnerBasicReq tQueryPartnerBasicReq = new TQueryPartnerBasicReq();
        tQueryPartnerBasicReq.setSsoId(uid);
        //默认查生效的客户
        tQueryPartnerBasicReq.setStatus(1);
        TPage tPage = new TPage();
        int currPage = 1;
        tPage.setPageNo(currPage);
        tPage.setPageSize(MccCustomerConfig.getDcCustomerPageSize());
        Set<Long> resultSet = new HashSet<>();
        while (true){
            if (resultSet.size() > MccCustomerConfig.getDcCustomerIdMaxSize()){
                log.warn("#getAllMtCustomerIdByUid-根据uid获取全部有权限的到餐客户平台客户ID列表-超出系统最大阈值，自动终止循环:{}",uid);
                break;
            }
            Set<Long> mtcustomerIdSet = dcCustomerBaseAdapter.queryPartnerBasicByBdIdAndStatus(tQueryPartnerBasicReq,tPage);
            if (mtcustomerIdSet == null || mtcustomerIdSet.isEmpty()){
                break;
            }
            resultSet.addAll(mtcustomerIdSet);
            // 翻页
            tPage.setPageNo(++currPage);
        }
        log.info("#getAllMtCustomerIdByUid-根据uid获取全部有权限的到餐客户平台客户ID列表-uid:{},customerSet:{}",uid,resultSet);
        return resultSet;
    }

    /**
     * 处理外卖系统内需要初始化的数据
     * @param wmCustomerDB
     * @return
     */
    private WmCustomerDB buildDefaultCustomerDB(WmCustomerDB wmCustomerDB){
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_2", "com.sankuai.meituan.waimai.customer.service.customer.DcCustomerService.buildDefaultCustomerDB(com.sankuai.meituan.waimai.customer.domain.WmCustomerDB)");
        wmCustomerDB.setMtCustomerId(wmCustomerDB.getMtCustomerId());
        wmCustomerDB.setIsLeaf(CUSTOMER_IS_LEAF_YES);
        wmCustomerDB.setCustomerRealType(CustomerRealTypeEnum.DAOCAN.getValue());
        //责任人待定
        wmCustomerDB.setOwnerUid(wmCustomerDB.getOwnerUid());
        wmCustomerDB.setValid(CustomerConstants.VALID);
        wmCustomerDB.setAuditStatus(CustomerAuditStatus.CUSTOMER_AUDIT_PASS.getCode());
        wmCustomerDB.setSignMode(CustomerSignMode.ELECTTRONIC.getCode());
        wmCustomerDB.setCertificateType(CertificateTypeEnum.ELECTRONIC.getType());
        wmCustomerDB.setBizOrgCode(CustomerBizOrgEnum.WAI_MAI.getCode());

        wmCustomerDB.setOwnerUid(0);
        wmCustomerDB.setSuperCustomerId(0);
        wmCustomerDB.setCustomerExtPro("");
        wmCustomerDB.setAliveIdentify(0);
        wmCustomerDB.setHqSecondCityId(0);
        wmCustomerDB.setHqDetailAddress("");
        wmCustomerDB.setContractNum("");
        wmCustomerDB.setWdcClueId(0L);
        wmCustomerDB.setCertificateOverdue(0);
        wmCustomerDB.setCertificateStatus(0);
        wmCustomerDB.setLegalPersonChange(0);
        wmCustomerDB.setCreateSource(CustomerSource.DAO_CAN.getCode());


        wmCustomerDB.setCustomerRealTypeSpInfo("");
        wmCustomerDB.setSceneInfo("");

        wmCustomerDB.setMultiplexCustomerId(0L);
        wmCustomerDB.setMultiplex(0);
        wmCustomerDB.setMultiplexBusinessLineId(0L);
        return wmCustomerDB;

    }

}
