package com.sankuai.meituan.waimai.customer.service.sc.canteenstall.clueflow;

import com.alibaba.fastjson.JSONObject;
import com.sankuai.meituan.waimai.customer.dao.sc.canteenstall.WmCanteenStallBindMapper;
import com.sankuai.meituan.waimai.customer.domain.sc.canteenstall.WmCanteenStallBindDO;
import com.sankuai.meituan.waimai.customer.domain.sc.canteenstall.WmCanteenStallClueBindBO;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.canteenstall.CanteenStallClueBindStatusEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 食堂档口线索状态食堂Service
 * <AUTHOR>
 * @date 2024/05/31
 * @email <EMAIL>
 */
@Slf4j
@Service
public class WmCanteenStallClueFlowCanteenService {

    @Autowired
    private WmCanteenStallBindMapper wmCanteenStallBindMapper;


    /**
     * 线索绑定状态更新为"绑定成功"
     * @param clueBindBO 线索绑定BO
     */
    public void updateClueBindStatusSuccess(WmCanteenStallClueBindBO clueBindBO) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.sc.canteenstall.clueflow.WmCanteenStallClueFlowCanteenService.updateClueBindStatusSuccess(WmCanteenStallClueBindBO)");
        WmCanteenStallBindDO bindDO = clueBindBO.getBindDO();
        // 将线索绑定状态更新为"绑定成功"
        bindDO.setClueBindStatus((int) CanteenStallClueBindStatusEnum.BIND_SUCCESS.getType());
        // 将线索绑定失败原因更新为""
        bindDO.setClueBindFailReason("");
        wmCanteenStallBindMapper.updateByPrimaryKeySelective(bindDO);
        log.info("[WmCanteenStallClueFlowCanteenService.updateClueBindStatusSuccess] bindDO = {}", JSONObject.toJSONString(bindDO));
    }

    /**
     * 线索绑定状态更新为"未绑定"
     * @param clueBindBO 线索绑定BO
     */
    public void updateClueBindStatusUnbind(WmCanteenStallClueBindBO clueBindBO) {
        WmCanteenStallBindDO bindDO = clueBindBO.getBindDO();
        // 将线索绑定状态更新为"未绑定"
        bindDO.setClueBindStatus((int) CanteenStallClueBindStatusEnum.UNBIND.getType());
        wmCanteenStallBindMapper.updateByPrimaryKeySelective(bindDO);
        log.info("[WmCanteenStallClueFlowCanteenService.updateClueBindStatusUnbind] bindDO = {}", JSONObject.toJSONString(bindDO));
    }


    /**
     * 线索绑定状态更新为"绑定失败"并设置绑定失败原因
     * @param clueBindBO 线索绑定BO
     */
    public void updateClueBindStatusFail(WmCanteenStallClueBindBO clueBindBO) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_1", "com.sankuai.meituan.waimai.customer.service.sc.canteenstall.clueflow.WmCanteenStallClueFlowCanteenService.updateClueBindStatusFail(WmCanteenStallClueBindBO)");
        WmCanteenStallBindDO bindDO = clueBindBO.getBindDO();
        // 将线索绑定状态更新为"绑定失败"
        bindDO.setClueBindStatus((int) CanteenStallClueBindStatusEnum.BIND_FAIL.getType());
        // 设置绑定失败原因
        bindDO.setClueBindFailReason(clueBindBO.getBindFailReason());
        wmCanteenStallBindMapper.updateByPrimaryKeySelective(bindDO);
    }




}
