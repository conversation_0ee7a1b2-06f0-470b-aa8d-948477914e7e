package com.sankuai.meituan.waimai.customer.service.sc.dbus;

import com.alibaba.fastjson.JSON;
import com.meituan.dbus.common.DbusUtils;
import com.meituan.dbus.common.StaticUtils;
import com.meituan.dbus.thriftV2.utils.ThriftV2Utils;
import com.sankuai.meituan.waimai.customer.constant.sc.WmScTableDbusEnum;
import com.sankuai.meituan.waimai.customer.service.sc.log.WmScCanteenPoiAttributeLogRecordService;
import com.sankuai.meituan.waimai.customer.service.sc.mafka.product.WmScCanteenPoiBindingUnboundSendMsgService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 门店食堂属性表
 */
@Service
@Slf4j
public class WmScCanteenPoiAttributeTableEventImpl implements ITableEvent {

    @Autowired
    private WmScCanteenPoiAttributeLogRecordService wmScCanteenPoiAttributeLogRecordService;

    @Autowired
    private WmScCanteenPoiBindingUnboundSendMsgService wmScCanteenPoiBindingUnboundSendMsgService;


    @Override
    public WmScTableDbusEnum getTable() {
        return WmScTableDbusEnum.TABLE_WM_SC_CANTEEN_POI_ATTRIBUTE;
    }

    @Override
    public String handleUpdate(TableEvent tableEvent) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.service.sc.dbus.WmScCanteenPoiAttributeTableEventImpl.handleUpdate(com.sankuai.meituan.waimai.customer.service.sc.dbus.TableEvent)");
        log.info("监听门店食堂属性表信息变更handleUpdate::tableEvent = {}", JSON.toJSONString(tableEvent));
        if (!checkParamWhenHandleInsert(tableEvent)) {
            return StaticUtils.ok;
        }

        DbusUtils utils = ThriftV2Utils.newThriftV2InstanceForUpdate(tableEvent.getMetaJsonData(), tableEvent.getDataMapJson(), tableEvent.getDiffJson());
        // 当更新的时候记录快照
        wmScCanteenPoiAttributeLogRecordService.insertLogWhenTableUpdate(utils);
        // 发送门店绑定/解绑食堂消息
        wmScCanteenPoiBindingUnboundSendMsgService.sendMsgWhenTableUpdate(utils);
        // 发送门店换绑食堂消息
        wmScCanteenPoiBindingUnboundSendMsgService.sendChangeBindMsgWhenTableUpdate(utils);
        return StaticUtils.ok;
    }

    @Override
    public String handleInsert(TableEvent tableEvent) {
        log.info("监听门店食堂属性表信息变更handleInsert::tableEvent = {}", JSON.toJSONString(tableEvent));
        if (!checkParamWhenHandleInsert(tableEvent)) {
            return StaticUtils.ok;
        }

        DbusUtils utils = ThriftV2Utils.newThriftV2InstanceForInsert(tableEvent.getMetaJsonData(), tableEvent.getDataMapJson());
        // 当新增的时候记录快照
        wmScCanteenPoiAttributeLogRecordService.insertLogWhenTableInsert(utils);
        // 发送门店绑定/解绑食堂消息
        wmScCanteenPoiBindingUnboundSendMsgService.sendMsgWhenTableInsert(utils);
        return StaticUtils.ok;
    }

    @Override
    public String handleDelete(TableEvent tableEvent) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.service.sc.dbus.WmScCanteenPoiAttributeTableEventImpl.handleDelete(com.sankuai.meituan.waimai.customer.service.sc.dbus.TableEvent)");
        // 无物理删除，不会走到这里
        return StaticUtils.ok;
    }


}
