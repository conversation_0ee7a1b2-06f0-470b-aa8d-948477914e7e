package com.sankuai.meituan.waimai.customer.service.customer;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.sankuai.meituan.waimai.customer.config.MccConfig;
import com.sankuai.meituan.waimai.customer.settle.domain.WmPoiSettleAuditedDB;
import com.sankuai.meituan.waimai.customer.settle.service.WmSettleService;
import com.sankuai.meituan.waimai.poibizflow.constant.WmPoiComplianceBasicStatusEnum;
import com.sankuai.meituan.waimai.poibizflow.constant.WmPoiComplianceModuleEnum;
import com.sankuai.meituan.waimai.poibizflow.thrift.compliance.WmComplianceThriftService;
import com.sankuai.meituan.waimai.poibizflow.thrift.compliance.bo.ComplianceBasicBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.settle.WmSettleAudited;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.mortbay.util.ajax.JSON;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;

@Service
@Slf4j
public class WmCustomerComplianceService {

    @Autowired
    private WmSettleService      wmSettleService;

    @Resource
    WmComplianceThriftService    wmComplianceThriftService;

    public void dealComplianceForCustomerUnbindPoi(Integer customerId, List<Long> wmPoiIdList) {
        if (MccConfig.oldSettleFuncOffineSwitch()) {
            return;
        }
        if (CollectionUtils.isEmpty(wmPoiIdList)) {
            return;
        }

        JSONArray wmPoiIdSettleIdJA = getSettleIdByWmPoiId(customerId, Sets.newHashSet(wmPoiIdList));
        log.info("wmPoiIdSettleIdJA = {}", JSON.toString(wmPoiIdSettleIdJA));
        for (Object object : wmPoiIdSettleIdJA) {
            try {
                JSONObject jsonObject = (JSONObject) object;
                int wmPoiId = (int)jsonObject.get("wmPoiId");
                int wmSettleId = (int)jsonObject.get("wmSettleId");
                dealSingleCompliance(wmPoiId, wmSettleId);
            } catch (Exception e) {
                log.warn("客户解绑门店,处理支付合规异常", e);
            }
        }
    }

    private JSONArray getSettleIdByWmPoiId(Integer customerId, Set<Long> wmPoiIdSet) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.service.customer.WmCustomerComplianceService.getSettleIdByWmPoiId(java.lang.Integer,java.util.Set)");
        if (CollectionUtils.isEmpty(wmPoiIdSet)) {
            return new JSONArray();
        }

        List<WmPoiSettleAuditedDB> settleAuditedList = wmSettleService.getAllByContractIdAndWmPoiIdList(customerId, new ArrayList<>(wmPoiIdSet));
        if (CollectionUtils.isEmpty(settleAuditedList)) {
            return new JSONArray();
        }

        JSONArray jsonArray = new JSONArray();
        for (WmPoiSettleAuditedDB settleAudited : settleAuditedList) {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("wmPoiId", settleAudited.getWm_poi_id());
            jsonObject.put("wmSettleId", settleAudited.getWm_settle_id());
            jsonArray.add(jsonObject);
        }
        return jsonArray;
    }

    private void dealSingleCompliance(int wmPoiId, int wmSettleId) throws Exception {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.service.customer.WmCustomerComplianceService.dealSingleCompliance(int,int)");
        List<Long> wmPoiIdListBySettleId = getWmPoiIdListBySettleId(wmSettleId);
        if (CollectionUtils.isEmpty(wmPoiIdListBySettleId)) {
            return;
        }

        //防止主从延迟
        wmPoiIdListBySettleId.remove((long) wmPoiId);
        log.info("dealSingleCompliance, wmPoiIdListBySettleId = {}", JSON.toString(wmPoiIdListBySettleId));

        // 该结算只有一个门店的情况下，直接触发合规
        if (wmPoiIdListBySettleId.size() == 1) {
            wmComplianceThriftService.noticePoiModuleEffective(wmPoiIdListBySettleId.get(0), WmPoiComplianceModuleEnum.settle, 0, "门店解绑客户");
            return;
        }
        // 多门店情况下，先找合规成功过的门店，重新合规
        boolean isCompliance = false;
        List<List<Long>> wmPoiListAll = Lists.partition(wmPoiIdListBySettleId, 50);
        for (List<Long> wmPoiIdListPart : wmPoiListAll) {
            Map<Long, ComplianceBasicBo> complianceBasicBoMap = wmComplianceThriftService.getComplianceBasicBoMap(wmPoiIdListPart);
            for (Map.Entry<Long, ComplianceBasicBo> entry : complianceBasicBoMap.entrySet()) {
                ComplianceBasicBo complianceBasicBo = entry.getValue();
                if (complianceBasicBo != null && WmPoiComplianceBasicStatusEnum.SUCCESS == complianceBasicBo.getStatus()) {
                    isCompliance = true;
                    wmComplianceThriftService.noticePoiModuleEffective(entry.getKey(), WmPoiComplianceModuleEnum.settle, 0, "门店解绑客户");
                    return;
                }
            }
        }
        // 多门店情况下，若不存在已合规成功的门店，随便取一个门店进行合规，这里取第一个
        if (!isCompliance && CollectionUtils.isNotEmpty(wmPoiIdListBySettleId)) {
            wmComplianceThriftService.noticePoiModuleEffective(wmPoiIdListBySettleId.get(0), WmPoiComplianceModuleEnum.settle, 0, "门店解绑客户");
        }
    }

    private List<Long> getWmPoiIdListBySettleId(int wmSettleId) throws WmCustomerException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.service.customer.WmCustomerComplianceService.getWmPoiIdListBySettleId(int)");
        WmSettleAudited wmSettleAudited = wmSettleService.getWmSettleAuditedByWmSettleId(wmSettleId);
        if (wmSettleAudited == null) {
            return Lists.newArrayList();
        }

        List<Integer> wmPoiIdList = wmSettleAudited.getWmPoiIdList();
        if (CollectionUtils.isEmpty(wmPoiIdList)) {
            return Lists.newArrayList();
        }

        List<Long> listRet = Lists.transform(wmPoiIdList, wmPoiId -> wmPoiId.longValue());
        return listRet;
    }
}
