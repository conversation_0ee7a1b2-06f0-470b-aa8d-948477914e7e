package com.sankuai.meituan.waimai.customer.service.kp.operatorv2.preverifierv2;

import com.sankuai.meituan.waimai.customer.domain.WmCustomerDB;
import com.sankuai.meituan.waimai.customer.util.ThrowUtil;
import com.sankuai.meituan.waimai.thrift.customer.constant.KpSignerStateMachine;
import com.sankuai.meituan.waimai.thrift.customer.domain.kp.WmCustomerKp;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Author: 窦旭蒙
 * MIS: douxumeng
 * Date: 2020-07-06 14:56
 * Email: <EMAIL>
 * Desc:
 */
@Service
public class OpmanagerStateVerify extends KpPreverify {

    @Override
    public Object verify(WmCustomerDB wmCustomer, List<WmCustomerKp> oldCustomerKpList, WmCustomerKp insertKp, WmCustomerKp updateKp, WmCustomerKp deleteKp, int uid, String uname) throws WmCustomerException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.service.kp.operatorv2.preverifierv2.OpmanagerStateVerify.verify(WmCustomerDB,List,WmCustomerKp,WmCustomerKp,WmCustomerKp,int,String)");
        if (insertKp != null) {
            // 新增空实现，后续可扩展

        }

        if (updateKp != null) {
            // 更新空实现，后续可扩展

        }

        if (deleteKp != null) {
            // 删除状态校验
            if (deleteKp.getState() == KpSignerStateMachine.AUTHORIZE_ING.getState()) {
                ThrowUtil.throwClientError("KP正在授权中，不可操作删除");
            }
            if (deleteKp.getState() == KpSignerStateMachine.CHANGE_AUTHORIZE_ING.getState()) {
                ThrowUtil.throwClientError("KP正在变更授权中，不可操作删除");
            }
        }
        return new Object();
    }
}
