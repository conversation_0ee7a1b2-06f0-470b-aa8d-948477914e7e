package com.sankuai.meituan.waimai.customer.service.kp.statemachine.action.legal;

import com.alibaba.fastjson.JSON;
import com.sankuai.meituan.waimai.customer.service.kp.CustomerKpBusinessService;
import com.sankuai.meituan.waimai.customer.service.kp.statemachine.action.KpLegalAbstractAction;
import com.sankuai.meituan.waimai.customer.service.kp.statemachine.constant.event.KpLegalEventEnum;
import com.sankuai.meituan.waimai.customer.service.kp.statemachine.context.KpLegalStatusMachineContext;
import com.sankuai.meituan.waimai.customer.service.kp.statemachine.machine.KpLegalStatusSM;
import com.sankuai.meituan.waimai.thrift.customer.constant.KpLegalStateMachine;
import com.sankuai.meituan.waimai.thrift.customer.domain.kp.WmCustomerKp;
import com.sankuai.meituan.waimai.thrift.exception.StatusMachineException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 20230828
 * @desc Kp法人信息暂存
 */
@Service
@Slf4j
public class KpLegalTempStoreAction extends KpLegalAbstractAction {

    @Autowired
    private CustomerKpBusinessService customerKpBusinessService;

    /**
     * 初始化保存执行操作
     *
     * @param from
     * @param to
     * @param eventEnum
     * @param context
     * @param kpLegalStatusSM
     */
    @Override
    public void execute(KpLegalStateMachine from, KpLegalStateMachine to, KpLegalEventEnum eventEnum,
                        KpLegalStatusMachineContext context, KpLegalStatusSM kpLegalStatusSM) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_2", "c.s.meituan.waimai.customer.service.kp.statemachine.action.legal.KpLegalTempStoreAction.execute(KpLegalStateMachine,KpLegalStateMachine,KpLegalEventEnum,KpLegalStatusMachineContext,KpLegalStatusSM)");
        try {
            customerKpBusinessService.updateKp2TempStore(context.getWmCustomerKp());
        } catch (Exception e) {
            log.error("KpLegalInitCreateAction.updateKp2TempStore,KP法人信息暂存发生异常,wmCustomerKp={}", JSON.toJSONString(context.getWmCustomerKp()), e);
            throw new StatusMachineException("KP法人你信息暂存发生异常");
        }
    }
}

