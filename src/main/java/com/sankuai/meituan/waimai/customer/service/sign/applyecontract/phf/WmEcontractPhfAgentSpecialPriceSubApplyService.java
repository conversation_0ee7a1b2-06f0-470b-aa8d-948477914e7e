package com.sankuai.meituan.waimai.customer.service.sign.applyecontract.phf;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.sankuai.meituan.waimai.customer.config.MccConfig;
import com.sankuai.meituan.waimai.customer.service.sign.batch.wrap.ca.WmEcontractCAPoiWrapperService;
import com.sankuai.meituan.waimai.customer.service.sign.batch.wrap.datawrapper.WmEcontractDateWrapperService;
import com.sankuai.meituan.waimai.customer.service.sign.batch.wrap.sms.WmEcontractSmsWrapperService;
import com.sankuai.meituan.waimai.customer.service.sign.batch.wrap.stamp.WmEcontractStampPoiWrapperService;
import com.sankuai.meituan.waimai.customer.service.sign.wrapcontext.constant.SignFlowConstant;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.EcontractBatchBo;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.StageBatchInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractDataWrapperEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractTaskApplySubTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.BatchPdfUrlContentBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractBatchContextBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.PdfUrlContentBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.thrift.TException;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * @description: 拼好饭-代理商保底金额，签约数据组装类
 * @author: zhangyuanhao02
 * @create: 2024/12/19 20:25
 */
@Slf4j
@Service
public class WmEcontractPhfAgentSpecialPriceSubApplyService extends AbstractWmEcontractPhfSubApplyAdapterService{
    @Resource
    private WmEcontractDateWrapperService wmEcontractDateWrapperService;

    @Resource
    private WmEcontractCAPoiWrapperService wmEcontractCAPoiWrapperService;

    @Resource
    private WmEcontractSmsWrapperService wmEcontractSmsWrapperService;

    @Resource
    private WmEcontractStampPoiWrapperService wmEcontractStampPoiWrapperService;


    @Override
    public EcontractTaskApplySubTypeEnum getSubTypeEnum() {
        return EcontractTaskApplySubTypeEnum.PHF_AGENT_SPECIAL_PRICE;
    }


    @Override
    public EcontractBatchBo wrapEcontractBo(EcontractBatchContextBo batchContextBo)
            throws TException, IllegalAccessException, WmCustomerException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_1", "com.sankuai.meituan.waimai.customer.service.sign.applyecontract.phf.WmEcontractPhfAgentSpecialPriceSubApplyService.wrapEcontractBo(EcontractBatchContextBo)");
        List<StageBatchInfoBo> batchInfoBoList = Lists.newArrayList();
        List<String> flowList = getFlowList(batchContextBo);

        // PDF参数
        StageBatchInfoBo createPdfStageBatchInfoBo = wmEcontractDateWrapperService.wrapMultiContractWithSingleTemplate(
                batchContextBo, flowList, EcontractDataWrapperEnum.PHF_AGENT_SPECIAL_PRICE);

        batchInfoBoList.add(createPdfStageBatchInfoBo);

        // CA认证参数
        batchInfoBoList.add(wmEcontractCAPoiWrapperService.wrap(batchContextBo));

        // 商家签章
        batchInfoBoList.add(wmEcontractStampPoiWrapperService.wrap(batchContextBo, flowList));

        // 短信参数
        batchInfoBoList.add(wmEcontractSmsWrapperService.wrap(batchContextBo));

        EcontractBatchBo econtractBatchBo =  new EcontractBatchBo.Builder()
                .token(MccConfig.getPhfEcontractToken())
                .econtractBizId(getBizId(batchContextBo))
                .econtractType(SignFlowConstant.PHF_ONLINE_CONTRACT)
                .stageInfoBoList(batchInfoBoList)
                .flowList(flowList)
                .econtractBatchSource(getSource(batchContextBo))
                .build();
        return econtractBatchBo;
    }


}
