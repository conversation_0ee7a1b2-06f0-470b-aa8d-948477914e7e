package com.sankuai.meituan.waimai.customer.service.customer.check;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.meituan.mtrace.thread.TraceRunnable;
import com.meituan.mtrace.thread.pool.TraceExecutors;
import com.meituan.pay.mwallet.thrift.req.SettleInfoBatchQueryReq;
import com.meituan.pay.mwallet.thrift.resp.SettleInfoListRes;
import com.meituan.pay.mwallet.thrift.resp.data.SettleInfoData;
import com.sankuai.meituan.waimai.customer.adapter.MerchantSettleQueryProxyServiceAdaptor;
import com.sankuai.meituan.waimai.customer.config.MccConfig;
import com.sankuai.meituan.waimai.customer.config.MccCustomerConfig;
import com.sankuai.meituan.waimai.customer.constant.customer.CustomerPoiBindTypeEnum;
import com.sankuai.meituan.waimai.customer.constant.customer.CustomerPoiUnBindDecideResultEnum;
import com.sankuai.meituan.waimai.customer.dao.WmCustomerPoiDBMapper;
import com.sankuai.meituan.waimai.customer.ddd.adapter.WmPoiQueryAdapter;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerDB;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerPoiDB;
import com.sankuai.meituan.waimai.customer.domain.cuspoirel.bind.CustomerPoiBindCheckBO;
import com.sankuai.meituan.waimai.customer.domain.poi.WmCustomerPoiCheckBo;
import com.sankuai.meituan.waimai.customer.domain.poi.WmCustomerPoiUnBindParamBo;
import com.sankuai.meituan.waimai.customer.domain.task.CustomerOperateBO;
import com.sankuai.meituan.waimai.customer.service.customer.UpmAuthCheckService;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerPoiService;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerService;
import com.sankuai.meituan.waimai.customer.service.customer.check.blackListCheck.CustomerBlackListValidator;
import com.sankuai.meituan.waimai.customer.service.customer.check.blackListCheck.PoiBlackListValidator;
import com.sankuai.meituan.waimai.customer.service.kp.WmCustomerKpService;
import com.sankuai.meituan.waimai.customer.service.version.VersionCheckUtil;
import com.sankuai.meituan.waimai.customer.settle.service.WmSettleManagerService;
import com.sankuai.meituan.waimai.customer.util.DateUtil;
import com.sankuai.meituan.waimai.poi.constants.PoiBizTypeEnum;
import com.sankuai.meituan.waimai.poi.constants.attribute.WmPoiValidEnum;
import com.sankuai.meituan.waimai.poilogistics.thrift.service.WmPoiLogisticsFeeThriftService;
import com.sankuai.meituan.waimai.poimanager.domain.WmPoiExtendStatusVo;
import com.sankuai.meituan.waimai.poimanager.service.WmPoiExtendStatusThriftService;
import com.sankuai.meituan.waimai.qualification.WmQualificationThriftService;
import com.sankuai.meituan.waimai.thrift.constant.QuaTypeEnum;
import com.sankuai.meituan.waimai.thrift.constants.WmPoiFieldQueryConstant;
import com.sankuai.meituan.waimai.thrift.customer.constant.*;
import com.sankuai.meituan.waimai.thrift.customer.constant.customer.task.CustomerTaskDetailSourceEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.customer.task.CustomerTaskOpSystemEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.customer.task.CustomerTaskSourceEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.WmCustomerBasicBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.kp.WmCustomerKp;
import com.sankuai.meituan.waimai.thrift.domain.WmPoiAggre;
import com.sankuai.meituan.waimai.thrift.domain.WmPoiControl;
import com.sankuai.meituan.waimai.thrift.domain.WmPoiQualificationInfoBo;
import com.sankuai.meituan.waimai.thrift.domain.WmPoiServiceVo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import com.sankuai.meituan.waimai.thrift.exception.WmServerException;
import com.sankuai.meituan.waimai.thrift.service.WmPoiFlowlineQueryThriftService;
import com.sankuai.meituan.waimai.thrift.service.server.WmPoiServiceCommomThriftService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.apache.commons.collections4.CollectionUtils;

import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class CustomerPoiBaseValidator {

    @Autowired
    private PoiBlackListValidator poiBlackListValidator;

    @Autowired
    private CustomerBlackListValidator customerBlackListValidator;

    @Autowired
    private WmPoiFlowlineQueryThriftService.Iface wmPoiFlowlineQueryThriftService;

    @Autowired
    private WmPoiExtendStatusThriftService.Iface wmPoiExtendStatusThriftService;

    @Autowired
    private WmCustomerPoiDBMapper wmCustomerPoiDBMapper;

    @Autowired
    protected WmPoiQueryAdapter wmPoiQueryAdapter;

    @Autowired
    public WmQualificationThriftService.Iface wmQuaThriftService;

    @Autowired
    private WmPoiServiceCommomThriftService wmPoiServiceCommomThriftService;

    @Autowired
    private WmCustomerPoiService wmCustomerPoiService;

    @Autowired
    private WmSettleManagerService wmSettleManagerService;

    @Autowired
    private WmPoiLogisticsFeeThriftService.Iface wmPoiLogisticsFeeThriftService;

    @Autowired
    private WmCustomerService wmCustomerService;

    @Autowired
    private UpmAuthCheckService upmAuthCheckService;

    @Autowired
    private WmCustomerKpService wmCustomerKpService;

    @Autowired
    private MerchantSettleQueryProxyServiceAdaptor merchantSettleQueryProxyServiceAdaptor;

    private static final List<Integer> DRUG_CUSTOMER_REAL_TYPE = Lists.newArrayList(CustomerRealTypeEnum.DANDIAN_YAOPIN.getValue(),
            CustomerRealTypeEnum.ZONGBU_YAOPIN_LIANSUO.getValue(), CustomerRealTypeEnum.B2C_DRUG.getValue());

    protected static final Set<String> WM_POI_FIELDS = Sets.newHashSet(WmPoiFieldQueryConstant.WM_POI_FIELD_WM_POI_ID,
            WmPoiFieldQueryConstant.WM_POI_FIELD_NAME,
            WmPoiFieldQueryConstant.WM_POI_FIELD_FIRST_TAG,
            WmPoiFieldQueryConstant.WM_POI_FIELD_POI_BIZ_TYPES,
            WmPoiFieldQueryConstant.WM_POI_FIELD_BIZ_ORG_CODE,
            WmPoiFieldQueryConstant.WM_POI_FIELD_LABEL_IDS,
            WmPoiFieldQueryConstant.WM_POI_FIELD_CITY_LOCATION_ID,
            WmPoiFieldQueryConstant.WM_POI_FIELD_VALID,
            WmPoiFieldQueryConstant.WM_POI_FIELD_SUB_WM_POI_TYPE
    );

    private static ExecutorService executorService = TraceExecutors.getTraceExecutorService(
            new ThreadPoolExecutor(30, 50, 30L, TimeUnit.MINUTES, new ArrayBlockingQueue<Runnable>(20000), new RejectedExecutionHandler() {
                @Override
                public void rejectedExecution(Runnable r, ThreadPoolExecutor executor) {
                    if (!executorService.isShutdown()) {
                        try {
                            executor.getQueue().put(r);
                        } catch (InterruptedException e) {
                            throw new RejectedExecutionException("Reject from " + executor.toString());
                        }
                    } else {
                        throw new RejectedExecutionException("Executor " + executor.toString() + " have shutdown.");
                    }
                }
            }));

    private void checkCustomerAndPoiBlackList(WmCustomerPoiCheckBo wmCustomerPoiCheckBo) throws WmCustomerException {
        // 先校验客户资质黑名单
        WmCustomerDB wmCustomer = wmCustomerPoiCheckBo.getWmCustomerDB();
        WmCustomerBasicBo wmCustomerBasicBo = new WmCustomerBasicBo();
        wmCustomerBasicBo.setCustomerType(wmCustomer.getCustomerType());
        wmCustomerBasicBo.setCustomerSecondType(wmCustomer.getCustomerSecondType());
        wmCustomerBasicBo.setCustomerNumber(wmCustomer.getCustomerNumber());
        wmCustomerBasicBo.setId(wmCustomer.getId());
        log.info("门店绑定客户：执行客户资质黑名单校验：CustomerType={}, CustomerSecondType={}, CustomerNumber={}",wmCustomerBasicBo.getCustomerType(), wmCustomerBasicBo.getCustomerSecondType(), wmCustomerBasicBo.getCustomerNumber());
        customerBlackListValidator.checkCustomerBlackList(wmCustomerBasicBo);
        // 再校验门店资质黑名单
        List<Long> wmPoiIdList = wmCustomerPoiCheckBo.getWmPoiIdList();
        log.info("门店绑定客户：执行门店资质黑名单校验：WmPoiIds={}", JSONObject.toJSONString(wmPoiIdList));
        poiBlackListValidator.checkPoiBlackList(wmPoiIdList, wmCustomer.getId());
    }

    public void checkBind(WmCustomerPoiCheckBo wmCustomerPoiCheckBo) throws TException, WmCustomerException {
        Integer customerId = wmCustomerPoiCheckBo.getWmCustomerDB().getId();
        Integer bizOrgCode = wmCustomerPoiCheckBo.getWmCustomerDB().getBizOrgCode();
        Integer customerRealType = wmCustomerPoiCheckBo.getWmCustomerDB().getCustomerRealType();
        // 门店绑定客户，先校验客户黑名单，再校验门店黑名单
        checkCustomerAndPoiBlackList(wmCustomerPoiCheckBo);
        Set<Long> wmPoiIdSet = Sets.newHashSet();
        List<WmPoiAggre> wmPoiAggreList = Lists.newArrayList();
        if (CustomerRealTypeEnum.DAOCAN.getValue() == customerRealType) {
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR,
                    "不允许绑定到餐客户");
        }
        if (wmCustomerPoiCheckBo.getTypeEnum() == CustomerPoiBindTypeEnum.HIGN_SEA_CHECK) {
            for (Long wmPoiId : wmCustomerPoiCheckBo.getWmPoiIdList()) {
                if (wmPoiId != null && wmPoiId.longValue() > 0) {
                    wmPoiIdSet.add(wmPoiId);
                }
            }
        } else {
            wmPoiIdSet = Sets.newHashSet(wmCustomerPoiCheckBo.getWmPoiIdList());
            if (CollectionUtils.isEmpty(wmPoiIdSet)) {
                throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR,
                        "参数不合法");
            }
            // 版本校验
            boolean checkPoiVersion = wmCustomerPoiCheckBo.isCheckPoiVersion();
            if (checkPoiVersion) {
                VersionCheckUtil.versionCheck(Lists.<Integer>newArrayList(customerId),
                        Lists.<Long>newArrayList(wmPoiIdSet));
            }

            // 校验门店是否存在
            checkNotExistWmPoiId(wmPoiIdSet);
            // 校验门店是否切换中
            validateInCustomerSwitchTask(wmPoiIdSet);
            // 校验门店是否已绑定客户
            Set<Long> existWmPoiIds = wmCustomerPoiDBMapper.selectExistPoiByWmPoiId(wmPoiIdSet);
            if (!existWmPoiIds.isEmpty()) {
                log.info("{}已有客户无法绑定", StringUtils.join(existWmPoiIds, CustomerConstants.SPLIT_SYMBOL));
                throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR,
                        String.format("%s已有客户无法绑定", StringUtils.join(existWmPoiIds, CustomerConstants.SPLIT_SYMBOL)));
            }
            // 校验门店是否预绑定客户流程中
            validatePreBind(wmPoiIdSet, wmCustomerPoiCheckBo.getTypeEnum());
        }
        if (CollectionUtils.isNotEmpty(wmPoiIdSet)) {
            // 获取待绑定门店信息
            wmPoiAggreList = wmPoiQueryAdapter.getWmPoiAggreList(Lists.newArrayList(wmPoiIdSet), WM_POI_FIELDS);
            // 从主库获取业务线并加工
            List<Integer> bizOrgCodeFromMasterSource = MccCustomerConfig.getBizOrgCodeFromMasterSource();
            if (CollectionUtils.isNotEmpty(bizOrgCodeFromMasterSource) && wmCustomerPoiCheckBo.getSourceTypeEnum() != null
                    && bizOrgCodeFromMasterSource.contains(wmCustomerPoiCheckBo.getSourceTypeEnum().getCode())) {
                buildBizOrgCodeFromMaster(Lists.newArrayList(wmPoiIdSet), wmPoiAggreList);
            }
            // 校验门店业务线
            if (!validMatchBizOrgCodeForCustomerAndPoi(wmPoiAggreList, bizOrgCode)) {
                throw new WmCustomerException(CustomerConstants.RESULT_CODE_CUSTOMER_BIZ_ORG_CODE_ERROR,
                        "门店所属业务线与客户业务线不一致，无法绑定，请修改客户类型或修改门店品类后再操作绑定，保证外卖门店绑定外卖客户、闪购门店绑定闪购客户、医药门店绑定医药客户。");
            }
            // 校验：医药门店分类限制
            checkPoiBizType(customerRealType, wmPoiAggreList);
        }
        wmCustomerPoiCheckBo.setWmPoiAggreList(wmPoiAggreList);
        wmCustomerPoiCheckBo.setRealWmPoiIdList(Lists.newArrayList(wmPoiIdSet));
    }

    public Map<CustomerPoiUnBindDecideResultEnum, Set<Long>> checkUnbind(WmCustomerPoiUnBindParamBo wmCustomerPoiUnBindParamBo) throws TException, WmCustomerException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_1", "com.sankuai.meituan.waimai.customer.service.customer.check.CustomerPoiBaseValidator.checkUnbind(com.sankuai.meituan.waimai.customer.domain.poi.WmCustomerPoiUnBindParamBo)");
        log.info("checkUnbind wmCustomerPoiUnBindParamBo={}", JSONObject.toJSONString(wmCustomerPoiUnBindParamBo));
        Map<CustomerPoiUnBindDecideResultEnum, Set<Long>> map = Maps.newHashMap();
        Integer customerId = wmCustomerPoiUnBindParamBo.getCustomerId();
        Set<Long> wmPoiIdSet = wmCustomerPoiUnBindParamBo.getWmPoiIdSet();
        // 参数校验
        if (customerId == null || customerId == 0 || CollectionUtils.isEmpty(wmPoiIdSet)) {
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "参数不合法");
        }
        // 门店版本校验
        VersionCheckUtil.versionCheck(Lists.<Integer>newArrayList(customerId), Lists.<Long>newArrayList(wmPoiIdSet));
        // 门店存在性校验
        checkNotExistWmPoiId(wmPoiIdSet);
        // 门店上线状态校验
        validWmPoiStatus(wmPoiIdSet);
        // 门店是否在切换中校验
        validateInCustomerSwitchTask(wmPoiIdSet);
        // 美食城预绑定失败解绑校验
        boolean mscPreUnbind = validPreBindFail(wmPoiIdSet, customerId);
        if (mscPreUnbind) {
            map.put(CustomerPoiUnBindDecideResultEnum.CAN_PRE_BIND_FAIL_UNBIND, wmPoiIdSet);
            return map;
        }
        // 判断是否来源于重新建店强制解绑
        boolean rebuildForceUnbind = isFromRebuildForceUnbind(wmCustomerPoiUnBindParamBo);
        // 门店是否与传入客户绑定，且不在解绑流程中
        validCustomerUnbindPoi(wmPoiIdSet, customerId, rebuildForceUnbind);
        if (rebuildForceUnbind) {
            // 重新建店强制解绑：需要直接解绑&&取消流程中签约任务
            map.put(CustomerPoiUnBindDecideResultEnum.CAN_UNBIND_AND_CANCEL_SIGN, wmPoiIdSet);
        } else {
            // 常规解绑取消：需求区分客户签约类型以及是否有生效的结算和配送
            validUnBindTypeAndMakeDecisions(wmCustomerPoiUnBindParamBo, map);
        }
        return map;
    }


    private void checkNotExistWmPoiId(Set<Long> wmPoiIdSet) throws WmCustomerException, TException {
        List<Long> notExistList = Lists.newArrayList();
        for (Long wmPoiId : wmPoiIdSet) {
            try {
                WmPoiControl wmPoiControl = wmPoiFlowlineQueryThriftService.getWmPoiControlByWmPoiIdRT(wmPoiId);
                if (wmPoiControl == null) {
                    log.info("客户绑定门店,校验门店ID{}不存在", wmPoiId);
                    notExistList.add(wmPoiId);
                } else {
                    log.info("[CustomerPoiBaseValidator.checkNotExistWmPoiId] is_delete = {}", wmPoiControl.getIs_delete());
                    if (wmPoiControl.getIs_delete() == 1) {
                        log.info("客户绑定门店,校验门店ID{}已删除", wmPoiId);
                        notExistList.add(wmPoiId);
                    }
                }
            } catch (WmServerException e) {
                log.error("查询门店是否删除异常");
                throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "查询门店是否删除异常");
            }
        }
        if (!CollectionUtils.isEmpty(notExistList)) {
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR,
                    String.format("门店ID:%s不存在", StringUtils.join(notExistList, CustomerConstants.SPLIT_SYMBOL)));
        }
    }

    /**
     * 验证是否在正进行的客户切换任务中
     */
    public void validateInCustomerSwitchTask(Set<Long> wmPoiIdSet) throws WmCustomerException {
        try {
            List<WmPoiExtendStatusVo> statusVoList = new ArrayList<>();
            List<List<Long>> wmPoiIdListAll = Lists.partition(new ArrayList<>(wmPoiIdSet), 200);
            for (List<Long> wmPoiIdPart : wmPoiIdListAll) {
                //在客户切换任务中的任务
                List<WmPoiExtendStatusVo> retList = wmPoiExtendStatusThriftService.batchGetDoingPoiStatusInfo(wmPoiIdPart);
                statusVoList.addAll(retList);
            }

            log.info("是否在客户切换的任务中：statusVoList={}", JSONObject.toJSONString(statusVoList));
            if (!CollectionUtils.isEmpty(statusVoList)) {
                List<Long> list = new ArrayList();
                for (WmPoiExtendStatusVo statusVo : statusVoList) {
                    list.add(statusVo.getWmPoiId());
                }
                throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR,
                        String.format("当前门店%s正在切换中，不可操作解绑or关联", list));
            }
        } catch (WmServerException e) {
            log.error("查询是否在客户切换任务中异常， wmPoiId={}, e={}", wmPoiIdSet, e);
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "查询是否在客户切换任务中异常");
        } catch (TException e) {
            log.error("查询是否在客户切换任务中异常， wmPoiId={}, e={}", wmPoiIdSet, e);
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "查询是否在客户切换任务中异常");
        }

    }

    /**
     * 校验门店是否在预绑定流程中
     *
     * @param wmPoiIdSet
     * @param typeEnum
     * @throws WmCustomerException
     */
    private void validatePreBind(Set<Long> wmPoiIdSet, CustomerPoiBindTypeEnum typeEnum) throws WmCustomerException {
        if (typeEnum == CustomerPoiBindTypeEnum.RE_START_BIND) {
            return;
        }
        Set<Long> existWmPoiIds = wmCustomerPoiDBMapper.selectExistPoiByWmPoiIdForPreBind(wmPoiIdSet);
        if (!existWmPoiIds.isEmpty()) {
            log.info("{}已有客户无法绑定", StringUtils.join(existWmPoiIds, CustomerConstants.SPLIT_SYMBOL));
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR,
                    String.format("%s已有客户无法绑定", StringUtils.join(existWmPoiIds, CustomerConstants.SPLIT_SYMBOL)));
        }
    }

    /**
     * 门店客户业务线匹配性校验
     *
     * @param wmPoiAggreList
     * @param bizOrgCode
     * @return
     */
    private boolean validMatchBizOrgCodeForCustomerAndPoi(List<WmPoiAggre> wmPoiAggreList, Integer bizOrgCode) {
        if (bizOrgCode == null || org.apache.commons.collections4.CollectionUtils.isEmpty(wmPoiAggreList)) {
            return true;
        }
        for (WmPoiAggre wmPoiAggre : wmPoiAggreList) {
            if (wmPoiAggre.getBiz_org_code() != bizOrgCode.intValue()) {
                return false;
            }
        }
        return true;
    }


    // 校验医药门店分类
    private void checkPoiBizType(Integer customerRealType, List<WmPoiAggre> wmPoiAggreList) throws WmCustomerException {
        List<Long> errWmPoidIds = Lists.newArrayList();
        if (CollectionUtils.isEmpty(wmPoiAggreList)) {
            return;
        }
        if (!MccCustomerConfig.getDrugPoiBindCustomerBizTypeCheckSwitch()) {
            return;
        }
        if (!DRUG_CUSTOMER_REAL_TYPE.contains(customerRealType)) {
            return;
        }
        for (WmPoiAggre wmPoiAggre : wmPoiAggreList) {
            log.info("checkPoiBizType wmPoiId:{},bizTypes={}", wmPoiAggre.getWm_poi_id(), wmPoiAggre.getPoi_biz_types());
            String bizTypeStr = wmPoiAggre.getPoi_biz_types();
            if (StringUtils.isNotBlank(bizTypeStr)) {
                int bizType = Integer.valueOf(bizTypeStr);
                if ((customerRealType == CustomerRealTypeEnum.DANDIAN_YAOPIN.getValue() || customerRealType == CustomerRealTypeEnum.ZONGBU_YAOPIN_LIANSUO.getValue())
                        && (bizType == PoiBizTypeEnum.O2O.getType() || bizType == PoiBizTypeEnum.B2C.getType())) {
                    continue;
                }
                if (customerRealType == CustomerRealTypeEnum.B2C_DRUG.getValue() && bizType == PoiBizTypeEnum.CROSS_BORDER.getType()) {
                    continue;
                }
            }
            errWmPoidIds.add(wmPoiAggre.getWm_poi_id());
        }
        if (CollectionUtils.isNotEmpty(errWmPoidIds)) {
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_CUSTOMER_REAL_TYPE_ERROR,
                    String.format("门店的医药业务类型与客户的客户类型不相符，门店id:%s", JSONObject.toJSONString(errWmPoidIds)));
        }
    }


    /**
     * 验证客户资质与门店资质是否一致
     *
     * @param wmCustomerDB
     * @param wmPoiIdList
     * @return
     * @throws TException
     * @throws WmServerException
     */
    public void validateQua(WmCustomerDB wmCustomerDB, List<Long> wmPoiIdList) throws WmCustomerException {
        if (CollectionUtils.isEmpty(wmPoiIdList)) {
            return;
        }
        String customerNumber = wmCustomerDB.getCustomerNumber();
        //资质类型
        int quaType = wmCustomerDB.getCustomerType();
        int quaSecendType = wmCustomerDB.getCustomerSecondType();
        log.info("资质编号重复校验：customerNumber={},quaType={},quaSecendType={}", customerNumber, quaType, quaSecendType);

        List<List<Long>> allPoiIds = Lists.partition(wmPoiIdList, MccConfig.checkQuaPageSize());

        Set<Long> failWmPoiIds = Sets.newHashSet();

        CountDownLatch countDownLatch = new CountDownLatch(allPoiIds.size());

        for (final List<Long> wmPoiIs : allPoiIds) {

            try {
                executorService.execute(new TraceRunnable(new Runnable() {

                    @Override
                    public void run() {
                        try {
                            Map<Long, List<WmPoiQualificationInfoBo>> resultMap = wmQuaThriftService.getWmPoiQualificationInfosByWmPoiIds(wmPoiIs);
//                            logger.info("调用门店资质接口获取门店资质:resultMap={}", JSON.toJSONString(resultMap));

                            for (Map.Entry<Long, List<WmPoiQualificationInfoBo>> map : resultMap.entrySet()) {
                                Long wmPoiId = map.getKey();
                                for (WmPoiQualificationInfoBo wmPoiAuditObjectBo : map.getValue()) {
                                    if (wmPoiAuditObjectBo.getType() == QuaTypeEnum.QUA_SUBTYPE_CID.getSubType() && wmPoiAuditObjectBo.getSecondSubType() == QuaTypeEnum.QUA_SUBTYPE_CID.getSecondSubType()
                                            &&
                                            (quaType == CustomerType.CUSTOMER_TYPE_IDCARD.getCode() && quaSecendType == CertTypeEnum.ID_CARD.getType())) {
                                        //个人身份证
                                        if (!wmPoiAuditObjectBo.getNumber().equals(customerNumber)) {
                                            failWmPoiIds.add(wmPoiId);
                                        }
                                    } else if (wmPoiAuditObjectBo.getType() == QuaTypeEnum.QUA_SUBTYPE_BUSINESSLICENSE.getSubType() && wmPoiAuditObjectBo.getSecondSubType() == QuaTypeEnum.QUA_SUBTYPE_BUSINESSLICENSE.getSecondSubType()
                                            && quaType == CustomerType.CUSTOMER_TYPE_BUSINESS.getCode()) {
                                        //营业执照
                                        if (!wmPoiAuditObjectBo.getNumber().equals(customerNumber)) {
                                            failWmPoiIds.add(wmPoiId);
                                        }
                                    }
                                }
                            }

                        } catch (WmServerException e) {
                            log.warn("wmQuaThriftService.getUpQua wmPoiIs:{} ", JSONObject.toJSONString(wmPoiIs), e);
                        } catch (TException e) {
                            log.warn("wmQuaThriftService.getUpQua wmPoiIs:{} ", JSONObject.toJSONString(wmPoiIs), e);
                        } finally {
                            countDownLatch.countDown();
                        }

                    }
                }));

            } catch (Exception e) {
                log.error("validateQua 多线程执行异常，wmPoiIdList={} ", JSONObject.toJSONString(wmPoiIdList), e);
            }


        }// end for

        try {
            countDownLatch.await();
        } catch (InterruptedException e) {
            log.error("validateQua 多线程执行超时，wmPoiIdList={} ", JSONObject.toJSONString(wmPoiIdList), e);
        } finally {
            log.info("validateQua #end");
        }

        if (CollectionUtils.isNotEmpty(failWmPoiIds)) {
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_CUSTOMER_POI_REAL_TYPE_DIFF_ERROR,
                    String.format("客户资质与门店资质不一致,无法保存。不一致门店:%s。", StringUtils.join(failWmPoiIds, "、")));
        }
    }

    /**
     * 从主库获取业务线并覆盖
     *
     * @param wmPoiIdList
     * @param wmPoiAggreList
     */
    private void buildBizOrgCodeFromMaster(List<Long> wmPoiIdList, List<WmPoiAggre> wmPoiAggreList) throws WmCustomerException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_1", "com.sankuai.meituan.waimai.customer.service.customer.check.CustomerPoiBaseValidator.buildBizOrgCodeFromMaster(java.util.List,java.util.List)");
        if (CollectionUtils.isEmpty(wmPoiIdList) || CollectionUtils.isEmpty(wmPoiAggreList)) {
            return;
        }
        List<List<Long>> allWmPoiIdList = Lists.partition(wmPoiIdList, 100);
        Map<Long, Integer> map = Maps.newHashMap();
        List<WmPoiServiceVo> wmPoiServiceVoList = Lists.newArrayList();
        try {
            for (List<Long> wmPoiIds : allWmPoiIdList) {
                List<WmPoiServiceVo> tempList = wmPoiServiceCommomThriftService.mgetWmPoiServices(wmPoiIds, true);
                log.info("buildBizOrgCodeFromMaster tempList={}", JSONObject.toJSONString(tempList));
                if (CollectionUtils.isEmpty(tempList)) {
                    continue;
                }
                wmPoiServiceVoList.addAll(tempList);
            }
            for (WmPoiServiceVo wmPoiServiceVo : wmPoiServiceVoList) {
                map.put(wmPoiServiceVo.getWmPoiId(), wmPoiServiceVo.getBizOrgCode());
            }
            for (WmPoiAggre wmPoiAggre : wmPoiAggreList) {
                Integer bizOrgCode = map.get(wmPoiAggre.getWm_poi_id());
                if (bizOrgCode != null) {
                    wmPoiAggre.setBiz_org_code(bizOrgCode);
                }
            }
        } catch (Exception e) {
            log.error("buildBizOrgCodeFromMaster error wmPoiIdList={}", JSONObject.toJSONString(wmPoiIdList), e);
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "从主库获取门店业务线异常");
        }
    }

    /**
     * 校验门店状态（上线门店不可解绑）
     *
     * @return
     */
    public void validWmPoiStatus(Set<Long> wmPoiIdSet) throws WmCustomerException {
        log.info("validWmPoiStatus wmPoiIdSet={}", JSONObject.toJSONString(wmPoiIdSet));
        List<WmPoiAggre> wmPoiAggreList = wmPoiQueryAdapter.getWmPoiAggreList(Lists.newArrayList(wmPoiIdSet), WM_POI_FIELDS);
        Map<Long, WmPoiAggre> poiMapAggre = wmPoiAggreList.stream().collect(Collectors.toMap(wmPoiAggre -> wmPoiAggre.getWm_poi_id(), wmPoiAggre -> wmPoiAggre));
        for (Long wmPoiId : wmPoiIdSet) {
            WmPoiAggre wmPoiAggre = poiMapAggre.get(wmPoiId);
            if (wmPoiAggre == null) {
                throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR,
                        String.format("门店ID:%s不存在", wmPoiId));
            } else {
                if (wmPoiAggre.getValid() == WmPoiValidEnum.ONLINE.getValue()) {
                    throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR,
                            String.format("上线门店不可解绑（门店id：%s）", wmPoiId));
                }
            }
        }
    }

    /**
     * 判断是否存在上线状态的门店
     *
     * @param wmPoiIdSet
     * @return
     * @throws WmCustomerException
     */
    public boolean checkExistsOnlineWmPoiId(Set<Long> wmPoiIdSet) throws WmCustomerException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_2", "com.sankuai.meituan.waimai.customer.service.customer.check.CustomerPoiBaseValidator.checkExistsOnlineWmPoiId(java.util.Set)");
        log.info("validWmPoiStatus wmPoiIdSet={}", JSONObject.toJSONString(wmPoiIdSet));
        List<WmPoiAggre> wmPoiAggreList = wmPoiQueryAdapter.getWmPoiAggreList(Lists.newArrayList(wmPoiIdSet), WM_POI_FIELDS);
        Map<Long, WmPoiAggre> poiMapAggre = wmPoiAggreList.stream().collect(Collectors.toMap(wmPoiAggre -> wmPoiAggre.getWm_poi_id(), wmPoiAggre -> wmPoiAggre));
        for (Long wmPoiId : wmPoiIdSet) {
            WmPoiAggre wmPoiAggre = poiMapAggre.get(wmPoiId);
            if (wmPoiAggre == null) {
                log.error("checkExistsOnlineWmPoiId,门店不存在,wmPoiId={}", wmPoiId);
                return true;
            } else {
                if (wmPoiAggre.getValid() == WmPoiValidEnum.ONLINE.getValue()) {
                    log.error("当前门店是上线状态，不能解绑,wmPoiId={}", wmPoiId);
                    return true;
                }
            }
        }
        return false;
    }


    /**
     * 批量解绑场景-暂不支持美食城门店预绑定状态的批量解绑,仅支持预绑定单门店失败解绑
     *
     * @param wmPoiIdSet
     */
    private boolean validPreBindFail(Set<Long> wmPoiIdSet, Integer customerId) throws WmCustomerException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_1", "com.sankuai.meituan.waimai.customer.service.customer.check.CustomerPoiBaseValidator.validPreBindFail(java.util.Set,java.lang.Integer)");
        log.info("validPreBindFail wmPoiIdSet={},customerId={}", JSONObject.toJSONString(wmPoiIdSet), customerId);
        //批量超过1个门店的解绑,校验不允许预绑定流程中的门店解绑
        if (wmPoiIdSet != null && wmPoiIdSet.size() > 1) {
            Set<Long> existPreBindPoi = wmCustomerPoiDBMapper.selectExistPoiByWmPoiIdForPreBind(wmPoiIdSet);
            if (!CollectionUtils.isEmpty(existPreBindPoi)) {
                throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR,
                        String.format("绑定状态非“已绑定”不支持批量解绑。（门店id：%s）", StringUtils.join(existPreBindPoi, CustomerConstants.SPLIT_SYMBOL)));
            }
        }
        //仅有1个门店解绑
        if (wmPoiIdSet != null && wmPoiIdSet.size() == 1) {
            List<WmCustomerPoiDB> wmCustomerPoiForPreBindInConfirmBinding = wmCustomerPoiDBMapper.getWmCustomerPoiForPreBind(customerId, Lists.newArrayList(wmPoiIdSet), Lists.newArrayList(CustomerRelationStatusEnum.CONFIRM_BINDING.getCode()));
            if (!CollectionUtils.isEmpty(wmCustomerPoiForPreBindInConfirmBinding)) {
                throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR,
                        String.format("绑定状态非“已绑定”不支持批量解绑。（门店id：%s）", StringUtils.join(wmPoiIdSet, CustomerConstants.SPLIT_SYMBOL)));
            }
            List<WmCustomerPoiDB> wmCustomerPoiForPreBind = wmCustomerPoiDBMapper.getWmCustomerPoiForPreBind(customerId, Lists.newArrayList(wmPoiIdSet), Lists.newArrayList(CustomerRelationStatusEnum.CONFIRM_BINDING_FAIL.getCode(), CustomerRelationStatusEnum.TO_APPLY_BIND.getCode()));
            for (WmCustomerPoiDB temp : wmCustomerPoiForPreBind) {
                if (temp.getSwitchTaskId() != null && temp.getSwitchTaskId() > 0L) {
                    throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "门店在客户切换流程中,请在切换系统中操作");
                }
            }
            if (!CollectionUtils.isEmpty(wmCustomerPoiForPreBind)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 检查门店是否已绑定，并且是绑定的这个客户,且锁定这个任务
     */
    public void validCustomerUnbindPoi(Set<Long> wmPoiIdSet, Integer customerId, boolean rebuildForceUnbind) throws WmCustomerException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.service.customer.check.CustomerPoiBaseValidator.validCustomerUnbindPoi(java.util.Set,java.lang.Integer,boolean)");
        log.info("validCustomerUnbindPoi wmPoiIdSet={},customerId={}", JSONObject.toJSONString(wmPoiIdSet), customerId);
        // 校验门店是否都存在绑定关系
        Set<Long> existWmPoiIds = wmCustomerPoiDBMapper.selectExistPoiByWmPoiId(wmPoiIdSet);
        if (existWmPoiIds != null && existWmPoiIds.size() != wmPoiIdSet.size()) {
            Set<Long> diff = Sets.difference(wmPoiIdSet, existWmPoiIds);
            log.info("{}没有绑定客户无法解绑", StringUtils.join(diff, CustomerConstants.SPLIT_SYMBOL));
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR,
                    String.format("门店ID:%s,没有绑定客户无法解绑", StringUtils.join(diff, CustomerConstants.SPLIT_SYMBOL)));
        }
        List<WmCustomerPoiDB> wmCustomerPoiDBList = wmCustomerPoiDBMapper.selectWmCustomerPoiByCustomerId(customerId);
        if (!CollectionUtils.isEmpty(wmCustomerPoiDBList)) {
            List<Long> bindList = Lists.newArrayList();
            List<Long> unbindingList = Lists.newArrayList();
            for (WmCustomerPoiDB wmCustomerPoiDB : wmCustomerPoiDBList) {
                if (wmCustomerPoiDB.getIsUnbinding() == CustomerConstants.IS_UNBINDING_YES) {
                    unbindingList.add(wmCustomerPoiDB.getWmPoiId());
                } else {
                    bindList.add(wmCustomerPoiDB.getWmPoiId());
                }
            }
            List<Long> errorPoiIds = Lists.newArrayList();
            List<Long> unBindingIds = Lists.newArrayList();
            for (Long wmPoiId : wmPoiIdSet) {
                if (unbindingList.contains(wmPoiId)) {
                    unBindingIds.add(wmPoiId);
                    continue;
                }
                if (!bindList.contains(wmPoiId)) {
                    errorPoiIds.add(wmPoiId);
                }
            }
            if (!errorPoiIds.isEmpty()) {
                log.info("门店ID:{},绑定的不是该客户", StringUtils.join(errorPoiIds, CustomerConstants.SPLIT_SYMBOL));
                throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, String.format("门店ID:%s,绑定的不是该客户",
                        StringUtils.join(errorPoiIds, CustomerConstants.SPLIT_SYMBOL)));
            }
            if (!unBindingIds.isEmpty() && !rebuildForceUnbind) {
                log.info("门店ID:{},正在解绑中", StringUtils.join(unBindingIds, CustomerConstants.SPLIT_SYMBOL));
                if (unBindingIds.size() == 1) {
                    Map<Integer, List<Long>> poiCreateTime = wmCustomerPoiService.getSmsRecordCtimeBycustomerId(customerId, unBindingIds);
                    if (poiCreateTime == null || poiCreateTime.isEmpty()) {
                        log.error("validCustomerUnbindPoi[校验客户绑定门店异常]，存在解绑中的门店找不到相应的短信签约任务，wmPoiIdSet={}, customerId={}", JSON.toJSONString(unBindingIds), customerId);
                        throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR,
                                String.format("门店ID:%s,正在解绑中", StringUtils.join(unBindingIds, CustomerConstants.SPLIT_SYMBOL)));
                    } else {
                        String dateString = DateUtil.date2StringSec(poiCreateTime.keySet().iterator().next());
                        throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR,
                                String.format("门店ID:%s,正在解绑中，解绑签约任务创建时间" + dateString, StringUtils.join(unBindingIds, CustomerConstants.SPLIT_SYMBOL)));
                    }
                } else {
                    throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR,
                            String.format("门店ID:%s,正在解绑中", StringUtils.join(unBindingIds, CustomerConstants.SPLIT_SYMBOL)));
                }
            }
        } else {
            log.info("客户ID:{},没有绑定门店", customerId);
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, String.format("客户ID:%s,没有绑定门店", customerId));
        }
    }

    /**
     * 校验客户门店解绑操作类型并按照不同操作类型将门店进行分类
     *
     * @param wmCustomerPoiUnBindParamBo
     * @param map
     * @throws WmCustomerException
     * @throws TException
     */
    private void validUnBindTypeAndMakeDecisions(WmCustomerPoiUnBindParamBo wmCustomerPoiUnBindParamBo, Map<CustomerPoiUnBindDecideResultEnum, Set<Long>> map)
            throws WmCustomerException, TException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.customer.check.CustomerPoiBaseValidator.validUnBindTypeAndMakeDecisions(WmCustomerPoiUnBindParamBo,Map)");
        log.info("wmCustomerPoiUnbindCheckBo wmCustomerPoiUnBindParamBo={},map={}", JSONObject.toJSONString(wmCustomerPoiUnBindParamBo), JSONObject.toJSONString(map));
        int customerId = wmCustomerPoiUnBindParamBo.getCustomerId();
        Set<Long> wmPoiIdSet = wmCustomerPoiUnBindParamBo.getWmPoiIdSet();
        List<Long> wmPoiIdList = Lists.newArrayList(wmCustomerPoiUnBindParamBo.getWmPoiIdSet());
        // 检查客户是否生效
        Boolean effective = wmCustomerService.checkCustomerEffect(customerId);
        if (!effective) {
            // 进行解绑
            log.info("客户未生效，直接解绑");
            map.put(CustomerPoiUnBindDecideResultEnum.CAN_UNBIND_DIRECT, wmPoiIdSet);
            return;
        }
        Set<Long> effectiveSettle = wmSettleManagerService.batchGetSettleEffectiveWmPoiIdSet(wmPoiIdList,customerId);
        Set<Long> effectiveLogisticFee = wmPoiLogisticsFeeThriftService.getValidLogisticsFeeByPoiIds(wmPoiIdList);
        // 取生效分成，结算的并集
        Set<Long> effectiveSet = Sets.union(effectiveLogisticFee, effectiveSettle);

        WmCustomerDB wmCustomerDB = wmCustomerService.selectCustomerById(customerId);
        if (wmCustomerDB.getSignMode() == CustomerSignMode.PAPER.getCode()) {
            log.info("门店ID:{}有生效的结算/分成，客户为纸质签约", effectiveSet);
            if (!effectiveSet.isEmpty()) {
                if (upmAuthCheckService.hasRolePermission(wmCustomerPoiUnBindParamBo.getOpUid(), CustomerRoleTypeEnum.PAPER_SIGNMODE_UNBINDPOI_MANAGER.getCode())) {
                    map.put(CustomerPoiUnBindDecideResultEnum.CAN_UNBIND_DIRECT, wmPoiIdSet);
                } else {
                    // 门店ID排除生效分成，结算并集
                    Set<Long> unEffectiveSet = Sets.difference(wmPoiIdSet, effectiveSet);
                    if (!unEffectiveSet.isEmpty()) {
                        log.info("门店ID:{}客户生效，但没有生效的结算/分成，直接解绑", unEffectiveSet);
                        // 进行解绑
                        map.put(CustomerPoiUnBindDecideResultEnum.CAN_UNBIND_DIRECT, unEffectiveSet);

                    }
                    map.put(CustomerPoiUnBindDecideResultEnum.PAGE_UNBIND_NO_AUTH, effectiveSet);
                }
            } else {
                map.put(CustomerPoiUnBindDecideResultEnum.CAN_UNBIND_DIRECT, wmPoiIdSet);
            }
        } else {
            WmCustomerKp wmCustomerKp = wmCustomerKpService.getCustomerKpOfEffectiveSigner(customerId);
            if (wmCustomerKp == null) {
                log.info("KP未生效，直接解绑");
                map.put(CustomerPoiUnBindDecideResultEnum.CAN_UNBIND_DIRECT, wmPoiIdSet);
                return;
            }

            if (!effectiveSet.isEmpty()) {
                log.info("门店ID:{}有生效的结算/分成，客户为电子签约，需要短信通知KP确认", effectiveSet);
                map.put(CustomerPoiUnBindDecideResultEnum.NEED_SIGN_TO_UNBIND, effectiveSet);

            }
            // 门店ID排除生效分成，结算并集
            Set<Long> unEffectiveSet = Sets.difference(wmPoiIdSet, effectiveSet);
            if (!unEffectiveSet.isEmpty()) {
                log.info("门店ID:{}客户生效，但没有生效的结算/分成，直接解绑", unEffectiveSet);
                map.put(CustomerPoiUnBindDecideResultEnum.CAN_UNBIND_DIRECT, unEffectiveSet);
            }
        }
    }

    /**
     * 判断是否来自重新建店强制解绑
     *
     * @param wmCustomerPoiUnBindParamBo
     * @return
     */
    private boolean isFromRebuildForceUnbind(WmCustomerPoiUnBindParamBo wmCustomerPoiUnBindParamBo) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_2", "com.sankuai.meituan.waimai.customer.service.customer.check.CustomerPoiBaseValidator.isFromRebuildForceUnbind(com.sankuai.meituan.waimai.customer.domain.poi.WmCustomerPoiUnBindParamBo)");
        log.info("isFromRebuildForceUnbind wmCustomerPoiUnBindParamBo={}", JSONObject.toJSONString(wmCustomerPoiUnBindParamBo));
        CustomerTaskSourceEnum customerTaskSourceEnum = wmCustomerPoiUnBindParamBo.getSourceTypeEnum();
        if (customerTaskSourceEnum == null || customerTaskSourceEnum != CustomerTaskSourceEnum.REBUILD_FORCE_UNBIND) {
            return false;
        }
        CustomerOperateBO customerOperateBO = wmCustomerPoiUnBindParamBo.getCustomerOperateBO();
        if (customerOperateBO == null || StringUtils.isBlank(customerOperateBO.getOpDetailSource()) || StringUtils.isBlank(customerOperateBO.getOpSystem())) {
            return false;
        }
        CustomerTaskDetailSourceEnum detailSourceEnum = CustomerTaskDetailSourceEnum.getByDesc(customerOperateBO.getOpDetailSource());
        List<Integer> supportOpDetailSource = MccCustomerConfig.getRebuildForceUnbindSupportOpDetailSource();
        if (detailSourceEnum == null || CollectionUtils.isEmpty(supportOpDetailSource) || !supportOpDetailSource.contains(detailSourceEnum.getCode())) {
            return false;
        }
        CustomerTaskOpSystemEnum opSystemEnum = CustomerTaskOpSystemEnum.getByDesc(customerOperateBO.getOpSystem());

        List<Integer> supportOpSystem = MccCustomerConfig.getRebuildForceUnbindSupportOpSystem();
        if (opSystemEnum == null || CollectionUtils.isEmpty(supportOpSystem) || !supportOpSystem.contains(opSystemEnum.getCode())) {
            return false;
        }
        return true;
    }

    /**
     * 校验是否处于美食城绑定流程中
     *
     * @param wmPoiIdSet
     * @return
     */
    public void validPreBindStatus(Set<Long> wmPoiIdSet) throws WmCustomerException {
        log.info("validPreBindStatus,wmPoiIdSet={}", JSONObject.toJSONString(wmPoiIdSet));
        Set<Long> inPreBindProcessList = wmCustomerPoiDBMapper.selectExistPoiByWmPoiIdForPreBind(wmPoiIdSet);
        if (CollectionUtils.isNotEmpty(inPreBindProcessList)) {
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, String.format("当前门店%s正在预绑定流程中，不可操作解绑", JSONObject.toJSONString(inPreBindProcessList)));
        }
    }


}
