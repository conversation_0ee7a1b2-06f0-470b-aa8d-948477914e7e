package com.sankuai.meituan.waimai.customer.service.customer.check;

import com.google.common.collect.Lists;
import com.sankuai.meituan.waimai.customer.util.base.SpringBeanUtil;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerConstants;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.ValidateResultBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.WmCustomerBasicBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

@Slf4j
public class CustomerCheckFilter {

    // 先富上单校验器
    private static CustomerCheckFilter bdEditCutstomerFilter = new CustomerCheckFilter();

    // 外卖自入驻，闪购自入驻校验器
    private static CustomerCheckFilter ownerEditCustomerFilter = new CustomerCheckFilter();


    // 先富上单校验器无签约模式校验
    private static CustomerCheckFilter bdEditCutstomerFilterNoSignMode = new CustomerCheckFilter();

    private List<IWmCustomerValidator> validators = Lists.newArrayList();

    static {
        // 先富上单校验规则集合
        bdEditCutstomerFilter.register((IWmCustomerValidator) SpringBeanUtil.getBean("wmCustomerStatusValidator"));
        bdEditCutstomerFilter.register((IWmCustomerValidator) SpringBeanUtil.getBean("wmCustomerBlackListValidator"));
        bdEditCutstomerFilter.register((IWmCustomerValidator) SpringBeanUtil.getBean("wmCustomerTypeValidator"));
        bdEditCutstomerFilter.register((IWmCustomerValidator) SpringBeanUtil.getBean("wmCustomerSignModeValidator"));
        bdEditCutstomerFilter.register((IWmCustomerValidator) SpringBeanUtil.getBean("wmCustomerSignModeSwitchValidator"));
        bdEditCutstomerFilter.register((IWmCustomerValidator) SpringBeanUtil.getBean("wmCustomerInputValueValidator"));
        bdEditCutstomerFilter.register((IWmCustomerValidator) SpringBeanUtil.getBean("wmCustomerNumberCharactersValidator"));
        bdEditCutstomerFilter.register((IWmCustomerValidator) SpringBeanUtil.getBean("wmCustomerRepeatabilityValidator"));
        bdEditCutstomerFilter.register((IWmCustomerValidator) SpringBeanUtil.getBean("wmCustomerBussinessValidator"));
        bdEditCutstomerFilter.register((IWmCustomerValidator) SpringBeanUtil.getBean("wmCustomerRealTypeValidator"));
        bdEditCutstomerFilter.register((IWmCustomerValidator) SpringBeanUtil.getBean("wmCustomerSuperCustomerValidator"));
        bdEditCutstomerFilter.register((IWmCustomerValidator) SpringBeanUtil.getBean("wmCustomerSuperCustomerEffectiveValidator"));
        bdEditCutstomerFilter.register((IWmCustomerValidator) SpringBeanUtil.getBean("wmCustomerRealNameVerifyValidator"));
        bdEditCutstomerFilter.register((IWmCustomerValidator) SpringBeanUtil.getBean("wmCustomerRealNameThreeEleVerifyValidator"));
        bdEditCutstomerFilter.register((IWmCustomerValidator) SpringBeanUtil.getBean("wmCustomerRealTypeSpInfoValidator"));
        bdEditCutstomerFilter.register((IWmCustomerValidator) SpringBeanUtil.getBean("wmCustomerMultiplexVerifyValidator"));
        bdEditCutstomerFilter.register((IWmCustomerValidator) SpringBeanUtil.getBean("wmCustomerSecondTypeVerifyValidator"));
        bdEditCutstomerFilter.register((IWmCustomerValidator) SpringBeanUtil.getBean("wmCustomerMscPoiCntValidator"));



        // 外卖自入驻，闪购自入驻校验规则集合
        ownerEditCustomerFilter.register((IWmCustomerValidator) SpringBeanUtil.getBean("wmCustomerStatusValidator"));
        ownerEditCustomerFilter.register((IWmCustomerValidator) SpringBeanUtil.getBean("wmCustomerBlackListValidator"));
        ownerEditCustomerFilter.register((IWmCustomerValidator) SpringBeanUtil.getBean("wmCustomerNumberEditableValidator"));
        ownerEditCustomerFilter.register((IWmCustomerValidator) SpringBeanUtil.getBean("wmCustomerTypeValidator"));
        ownerEditCustomerFilter.register((IWmCustomerValidator) SpringBeanUtil.getBean("wmCustomerSignModeValidator"));
        ownerEditCustomerFilter.register((IWmCustomerValidator) SpringBeanUtil.getBean("wmCustomerSignModeSwitchValidator"));
        ownerEditCustomerFilter.register((IWmCustomerValidator) SpringBeanUtil.getBean("wmCustomerInputValueValidator"));
        ownerEditCustomerFilter.register((IWmCustomerValidator) SpringBeanUtil.getBean("wmCustomerNumberCharactersValidator"));
        ownerEditCustomerFilter.register((IWmCustomerValidator) SpringBeanUtil.getBean("wmCustomerRepeatabilityValidator"));
        ownerEditCustomerFilter.register((IWmCustomerValidator) SpringBeanUtil.getBean("wmCustomerBussinessValidator"));
        ownerEditCustomerFilter.register((IWmCustomerValidator) SpringBeanUtil.getBean("wmCustomerRealTypeValidator"));
        ownerEditCustomerFilter.register((IWmCustomerValidator) SpringBeanUtil.getBean("wmCustomerSuperCustomerValidator"));
        ownerEditCustomerFilter.register((IWmCustomerValidator) SpringBeanUtil.getBean("wmCustomerSuperCustomerEffectiveValidator"));
        ownerEditCustomerFilter.register((IWmCustomerValidator) SpringBeanUtil.getBean("wmCustomerRealNameVerifyValidator"));
        ownerEditCustomerFilter.register((IWmCustomerValidator) SpringBeanUtil.getBean("wmCustomerRealNameThreeEleVerifyValidator"));
        ownerEditCustomerFilter.register((IWmCustomerValidator) SpringBeanUtil.getBean("wmCustomerRealTypeSpInfoValidator"));
        ownerEditCustomerFilter.register((IWmCustomerValidator) SpringBeanUtil.getBean("wmCustomerSecondTypeVerifyValidator"));
        ownerEditCustomerFilter.register((IWmCustomerValidator) SpringBeanUtil.getBean("wmCustomerMscPoiCntValidator"));
        ownerEditCustomerFilter.register((IWmCustomerValidator) SpringBeanUtil.getBean("wmCustomerMscParamValidator"));
        ownerEditCustomerFilter.register((IWmCustomerValidator) SpringBeanUtil.getBean("wmCustomerMscPoiCntV2Validator"));



        // 先富上单校验规则集合
        bdEditCutstomerFilterNoSignMode.register((IWmCustomerValidator) SpringBeanUtil.getBean("wmCustomerStatusValidator"));
        bdEditCutstomerFilterNoSignMode.register((IWmCustomerValidator) SpringBeanUtil.getBean("wmCustomerBlackListValidator"));
        bdEditCutstomerFilterNoSignMode.register((IWmCustomerValidator) SpringBeanUtil.getBean("wmCustomerTypeValidator"));
        bdEditCutstomerFilterNoSignMode.register((IWmCustomerValidator) SpringBeanUtil.getBean("wmCustomerSignModeValidator"));
        bdEditCutstomerFilterNoSignMode.register((IWmCustomerValidator) SpringBeanUtil.getBean("wmCustomerInputValueValidator"));
        bdEditCutstomerFilterNoSignMode.register((IWmCustomerValidator) SpringBeanUtil.getBean("wmCustomerNumberCharactersValidator"));
        bdEditCutstomerFilterNoSignMode.register((IWmCustomerValidator) SpringBeanUtil.getBean("wmCustomerRepeatabilityValidator"));
        bdEditCutstomerFilterNoSignMode.register((IWmCustomerValidator) SpringBeanUtil.getBean("wmCustomerBussinessValidator"));
        bdEditCutstomerFilterNoSignMode.register((IWmCustomerValidator) SpringBeanUtil.getBean("wmCustomerRealTypeValidator"));
        bdEditCutstomerFilterNoSignMode.register((IWmCustomerValidator) SpringBeanUtil.getBean("wmCustomerSuperCustomerValidator"));
        bdEditCutstomerFilterNoSignMode.register((IWmCustomerValidator) SpringBeanUtil.getBean("wmCustomerSuperCustomerEffectiveValidator"));
        bdEditCutstomerFilterNoSignMode.register((IWmCustomerValidator) SpringBeanUtil.getBean("wmCustomerRealNameVerifyValidator"));
        bdEditCutstomerFilterNoSignMode.register((IWmCustomerValidator) SpringBeanUtil.getBean("wmCustomerRealNameThreeEleVerifyValidator"));
        bdEditCutstomerFilterNoSignMode.register((IWmCustomerValidator) SpringBeanUtil.getBean("wmCustomerRealTypeSpInfoValidator"));
        bdEditCutstomerFilterNoSignMode.register((IWmCustomerValidator) SpringBeanUtil.getBean("wmCustomerMultiplexVerifyValidator"));
        bdEditCutstomerFilterNoSignMode.register((IWmCustomerValidator) SpringBeanUtil.getBean("wmCustomerSecondTypeVerifyValidator"));
        bdEditCutstomerFilterNoSignMode.register((IWmCustomerValidator) SpringBeanUtil.getBean("wmCustomerMscPoiCntValidator"));
        bdEditCutstomerFilterNoSignMode.register((IWmCustomerValidator) SpringBeanUtil.getBean("wmCustomerMscParamValidator"));
        bdEditCutstomerFilterNoSignMode.register((IWmCustomerValidator) SpringBeanUtil.getBean("wmCustomerMscPoiCntV2Validator"));
        bdEditCutstomerFilterNoSignMode.register((IWmCustomerValidator) SpringBeanUtil.getBean("wmCustomerNumberEditableValidator"));

    }


    public ValidateResultBo filter(WmCustomerBasicBo wmCustomerBasicBo, Boolean force, Boolean isAudit, Integer opUid) throws WmCustomerException {
        ValidateResultBo validateResultBo = new ValidateResultBo();
        validateResultBo.setCode(CustomerConstants.RESULT_CODE_PASS);
        try {
            for (IWmCustomerValidator validator : validators) {
                validateResultBo = validator.valid(validateResultBo, wmCustomerBasicBo, force, isAudit, opUid);
                if (CustomerConstants.RESULT_CODE_PASS != validateResultBo.getCode()) {
                    return validateResultBo;
                }
            }
        } catch (Exception e) {
            validateResultBo.setCode(CustomerConstants.RESULT_CODE_ERROR);
            log.error("客户校验异常", e);
        }
        return validateResultBo;
    }


    private void register(IWmCustomerValidator validator) {
        validators.add(validator);
    }


    public static CustomerCheckFilter getBDEditCutstomerFilter() {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.customer.check.CustomerCheckFilter.getBDEditCutstomerFilter()");
        return bdEditCutstomerFilter;
    }

    public static CustomerCheckFilter getOwnerEditCustomerFilter() {
        return ownerEditCustomerFilter;
    }

    public static CustomerCheckFilter getBDEditCutstomerFilterNoSignMode() {
        return bdEditCutstomerFilterNoSignMode;
    }


}
