package com.sankuai.meituan.waimai.customer.service.customer;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.meituan.mafka.client.bean.MafkaProducer;
import com.meituan.mtrace.Tracer;
import com.meituan.service.mobile.mtthrift.util.ClientInfoUtil;
import com.meituan.service.mobile.mtthrift.util.json.JacksonUtils;
import com.sankuai.meituan.waimai.customer.adapter.CMSThriftServiceAdapter;
import com.sankuai.meituan.waimai.customer.bo.sign.RuzhuCreateContractBo;
import com.sankuai.meituan.waimai.customer.config.MccConfig;
import com.sankuai.meituan.waimai.customer.config.MccCustomerConfig;
import com.sankuai.meituan.waimai.customer.constant.WmCustomerConstant;
import com.sankuai.meituan.waimai.customer.constant.customer.CustomerPoiBindTypeEnum;
import com.sankuai.meituan.waimai.customer.contract.dao.WmTempletContractAuditedDBMapper;
import com.sankuai.meituan.waimai.customer.contract.dao.WmTempletContractDBMapper;
import com.sankuai.meituan.waimai.customer.contract.domain.WmTempletContractDB;
import com.sankuai.meituan.waimai.customer.contract.service.impl.WmContractService;
import com.sankuai.meituan.waimai.customer.contract.service.impl.WmSubjectChangeSupplementEContractTempletService;
import com.sankuai.meituan.waimai.customer.dao.WmCustomerKpDBMapper;
import com.sankuai.meituan.waimai.customer.dao.WmCustomerKpTempDBMapper;
import com.sankuai.meituan.waimai.customer.ddd.adapter.WmPoiQueryAdapter;
import com.sankuai.meituan.waimai.customer.ddd.domain.customer.aggre.WmCustomerPoiAggre;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerDB;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerNumberRepeatVo;
import com.sankuai.meituan.waimai.customer.domain.WmPoiContact;
import com.sankuai.meituan.waimai.customer.domain.poi.WmCustomerPoiBindParamBo;
import com.sankuai.meituan.waimai.customer.domain.task.CustomerOperateBO;
import com.sankuai.meituan.waimai.customer.service.common.WmCustomerCommonWrapService;
import com.sankuai.meituan.waimai.customer.service.customer.poi.CustomerPoiBindService;
import com.sankuai.meituan.waimai.customer.service.kp.WmCustomerKpService;
import com.sankuai.meituan.waimai.customer.util.AppContext;
import com.sankuai.meituan.waimai.customer.util.DXAlertUtils;
import com.sankuai.meituan.waimai.customer.util.GrayUtil;
import com.sankuai.meituan.waimai.customer.util.MasterSlaveHelper;
import com.sankuai.meituan.waimai.customer.util.base.SpringBeanUtil;
import com.sankuai.meituan.waimai.infra.service.WmOrgService;
import com.sankuai.meituan.waimai.thrift.customer.constant.*;
import com.sankuai.meituan.waimai.thrift.customer.constant.customer.task.CustomerTaskSceneType;
import com.sankuai.meituan.waimai.thrift.customer.constant.customer.task.CustomerTaskSourceEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.customer.task.CustomerTaskTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.ModuleDetailStatus;
import com.sankuai.meituan.waimai.thrift.customer.domain.ModuleStatus;
import com.sankuai.meituan.waimai.thrift.customer.domain.base.BooleanResult;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.WmCustomerContractBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.WmPoiSignSubjectBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.WmTempletContractSignBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.*;
import com.sankuai.meituan.waimai.thrift.customer.domain.kp.WmCustomerKp;
import com.sankuai.meituan.waimai.thrift.customer.domain.kp.WmCustomerKpTemp;
import com.sankuai.meituan.waimai.thrift.customer.domain.mq.WmCustomerBizAsyncNotifyMsg;
import com.sankuai.meituan.waimai.thrift.domain.WmPoiAggre;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import org.apache.commons.lang3.StringUtils;
import org.apache.thrift.TException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Set;

@Service
public class WmCustomerCommonService {

    private static Logger logger = LoggerFactory.getLogger(WmCustomerCommonService.class);

    @Autowired
    private WmCustomerKpDBMapper wmCustomerKpDBMapper;

    @Autowired
    private WmCustomerKpTempDBMapper wmCustomerKpTempDBMapper;

    @Autowired
    private WmTempletContractDBMapper wmTempletContractDBMapper;

    @Autowired
    private WmTempletContractAuditedDBMapper wmTempletContractAuditedDBMapper;

    @Autowired
    private WmCustomerService wmCustomerService;

    @Autowired
    private WmCustomerKpService wmCustomerKpService;

    @Autowired
    WmContractService wmContractService;

    @Autowired
    private WmCustomerPoiService wmCustomerPoiService;

    @Autowired
    private WmCustomerCommonWrapService wmCustomerCommonWrapService;

    @Autowired
    private WmCustomerPlatformDataParseService wmCustomerPlatformDataParseService;

    @Autowired
    private WmCustomerSensitiveWordsService wmCustomerSensitiveWordsService;

    @Autowired
    private WmSubjectChangeSupplementEContractTempletService wmSubjectChangeSupplementEContractTempletService;

    @Resource(name = "customerAsyncNotifyMafkaProducer")
    private MafkaProducer customerAsyncNotifyMafkaProducer;

    @Autowired
    private CustomerPoiBindOrUnbindService customerPoiBindOrUnbindService;

    @Autowired
    private CustomerPoiBindService customerPoiBindService;

    @Autowired
    private CMSThriftServiceAdapter cmsThriftServiceAdapter;

    @Autowired
    private WmPoiQueryAdapter wmPoiQueryAdapter;


    private static final int CUSTOMER_ID_SIZE = 1000_0000;


    public ModuleDetailStatus getModuleDetailStatusByCustomerId(Integer customerId) throws WmCustomerException {
        logger.debug("入参 customerId = {}", customerId);
        if (customerId == null) {
            return getDefaultModuleStatus();
        }

        ModuleDetailStatus moduleTabStatus = new ModuleDetailStatus();
        WmCustomerDB wmCustomerDB = wmCustomerPlatformDataParseService.selectCustomerFromPlatformById(customerId);
        if (wmCustomerDB != null) {
            moduleTabStatus.setIsLeaf(wmCustomerDB.getIsLeaf());
        }

        moduleTabStatus.setCustomerStatus(getCustomerModuleStatus(customerId));
        moduleTabStatus.setKpStatus(getKpModuleStatus(customerId, wmCustomerDB));
        moduleTabStatus.setContractStatus(getContractModuleStatus(customerId));

        logger.debug("各模块状态 moduleTabStatus = {}", JSON.toJSONString(moduleTabStatus));
        return moduleTabStatus;
    }

    private ModuleDetailStatus getDefaultModuleStatus() {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.customer.WmCustomerCommonService.getDefaultModuleStatus()");
        ModuleDetailStatus moduleTabStatus = new ModuleDetailStatus();
        moduleTabStatus.setCustomerStatus(new ModuleStatus(ModuleDetailStatusEnum.NO_DATA.getCode(), ModuleDetailStatusEnum.NO_DATA.getDesc()));
        moduleTabStatus.setKpStatus(new ModuleStatus(ModuleDetailStatusEnum.NO_DATA.getCode(), ModuleDetailStatusEnum.NO_DATA.getDesc()));
        moduleTabStatus.setContractStatus(new ModuleStatus(ModuleDetailStatusEnum.NO_DATA.getCode(), ModuleDetailStatusEnum.NO_DATA.getDesc()));
        return moduleTabStatus;
    }

    private ModuleStatus checkStatusEnum(ModuleDetailStatusEnum statusEnum) throws WmCustomerException {
        if (statusEnum == null) {
            return new ModuleStatus(ModuleDetailStatusEnum.NO_DATA.getCode(), ModuleDetailStatusEnum.NO_DATA.getDesc());
        }
        return new ModuleStatus(statusEnum.getCode(), statusEnum.getDesc());
    }

    private ModuleStatus getCustomerModuleStatus(Integer customerId) throws WmCustomerException {
//        WmCustomerDB wmCustomerDB = wmCustomerDBMapper.selectCustomerById(customerId);
        WmCustomerDB wmCustomerDB = wmCustomerPlatformDataParseService.selectCustomerFromPlatformById(customerId);
        if (wmCustomerDB == null) {
            return new ModuleStatus(ModuleDetailStatusEnum.NO_DATA.getCode(), ModuleDetailStatusEnum.NO_DATA.getDesc());
        }
        ModuleDetailStatusEnum statusEnum = ModuleDetailStatusEnum.CUSTOMER_ENUM_MAP.get(wmCustomerDB.getAuditStatus());
        logger.debug("客户基本信息模块状态 statusEnum = {}", JSON.toJSONString(statusEnum));
        return checkStatusEnum(statusEnum);
    }

    private ModuleStatus getKpModuleStatus(Integer customerId, WmCustomerDB wmCustomerDB) throws WmCustomerException {
        if (wmCustomerDB != null
                && wmCustomerDB.getCustomerRealType() != null
                && wmCustomerDB.getCustomerRealType() == CustomerRealTypeEnum.CONTRACTOR.getValue()) {
            return new ModuleStatus();
        }
        List<WmCustomerKp> wmCustomerKps = wmCustomerKpDBMapper.selectByCustomerId(customerId, KpTypeEnum.SIGNER.getType());
        if (CollectionUtils.isEmpty(wmCustomerKps)) {
            return new ModuleStatus(ModuleDetailStatusEnum.NO_DATA.getCode(), ModuleDetailStatusEnum.NO_DATA.getDesc());
        }

        WmCustomerKp wmCustomerKp = wmCustomerKps.get(0);
        wmCustomerSensitiveWordsService.readKpWhenSelect(wmCustomerKp);
        byte kpState = wmCustomerKp.getState();
        if (KpSignerStateMachine.EFFECT.getState() == kpState) {
            WmCustomerKpTemp wmCustomerKpTemp = wmCustomerKpTempDBMapper.selectByKpId(wmCustomerKp.getId());
            wmCustomerSensitiveWordsService.readKpWhenSelect(wmCustomerKpTemp);
            if (wmCustomerKpTemp != null) {
                kpState = wmCustomerKpTemp.getState();
            }
        }
        ModuleDetailStatusEnum statusEnum = ModuleDetailStatusEnum.KP_ENUM_MAP.get((int) kpState);
        logger.debug("KP模块状态 statusEnum = {}", JSON.toJSONString(statusEnum));
        return checkStatusEnum(statusEnum);
    }

    private ModuleStatus getContractModuleStatus(Integer customerId) {
        //生效合同
        List<WmTempletContractDB> wmTempletContractDBS = wmTempletContractAuditedDBMapper.getBasicListByParentIdAndTypes(
                customerId, Lists.newArrayList(
                        WmTempletContractTypeEnum.C1_E.getCode(),
                        WmTempletContractTypeEnum.C1_PAPER.getCode()));
        if (!CollectionUtils.isEmpty(wmTempletContractDBS)) {
            WmTempletContractDB wmTempletContractDB = wmTempletContractDBMapper.selectByPrimaryKey(wmTempletContractDBS.get(0).getId());
            if (wmTempletContractDB == null || CustomerContractStatus.EFFECT.getCode() == wmTempletContractDB.getStatus()) {
                return new ModuleStatus(ModuleDetailStatusEnum.EFFECT.getCode(), ModuleDetailStatusEnum.EFFECT.getDesc());
            }
        }

        //未生效电子合同
        wmTempletContractDBS = wmTempletContractDBMapper.selectByParentIdAndTypes(customerId.longValue(), Lists.newArrayList(
                WmTempletContractTypeEnum.C1_E.getCode()));
        if (!CollectionUtils.isEmpty(wmTempletContractDBS)) {
            WmTempletContractDB wmTempletContractDB = wmTempletContractDBS.get(0);
            if (CustomerContractStatus.STAGE.getCode() == wmTempletContractDB.getStatus()) {
                return new ModuleStatus(ModuleDetailStatusEnum.SIGN_NEW.getCode(), ModuleDetailStatusEnum.SIGN_NEW.getDesc());
            }
            ModuleDetailStatusEnum statusEnum = ModuleDetailStatusEnum.CONTRACT_ENUM_MAP.get(wmTempletContractDB.getStatus());
            logger.debug("合同模块电子合同状态 statusEnum = {}", JSON.toJSONString(statusEnum));
            if (statusEnum != null) {
                return new ModuleStatus(statusEnum.getCode(), statusEnum.getDesc());
            }
        }

        //未生效纸质合同
        wmTempletContractDBS = wmTempletContractDBMapper.selectByParentIdAndTypes(customerId.longValue(), Lists.newArrayList(
                WmTempletContractTypeEnum.C1_PAPER.getCode()));
        if (!CollectionUtils.isEmpty(wmTempletContractDBS)) {
            WmTempletContractDB wmTempletContractDB = wmTempletContractDBS.get(0);
            if (CustomerContractStatus.STAGE.getCode() == wmTempletContractDB.getStatus()) {
                return new ModuleStatus(ModuleDetailStatusEnum.AUDIT_NEW.getCode(), ModuleDetailStatusEnum.AUDIT_NEW.getDesc());
            }
            ModuleDetailStatusEnum statusEnum = ModuleDetailStatusEnum.CONTRACT_ENUM_MAP.get(wmTempletContractDB.getStatus());
            logger.debug("合同模块纸质合同状态 statusEnum = {}", JSON.toJSONString(statusEnum));
            if (statusEnum != null) {
                return new ModuleStatus(statusEnum.getCode(), statusEnum.getDesc());
            }
        }
        return new ModuleStatus(ModuleDetailStatusEnum.NO_DATA.getCode(), ModuleDetailStatusEnum.NO_DATA.getDesc());
    }

    /**
     * 保存客户模块信息: 基本信息、KP、合同、客户关联门店
     *
     * @param wmCustomerInfoAggre
     * @param opUid
     * @param opName
     * @return
     * @throws TException
     * @throws WmCustomerException
     */
    @Transactional(rollbackFor = Exception.class)
    public WmCustomerCommonBo saveWmCustomerModulesInfo(WmCustomerInfoAggre wmCustomerInfoAggre, int opUid, String opName) throws TException, WmCustomerException {
        String appKey = ClientInfoUtil.getClientAppKey();
        logger.info("保存客户模块信息,参数 WmCustomerInfoAggre = {}, opUid = {}, opName = {},appKey={}",
                JSONObject.toJSONString(wmCustomerInfoAggre), opUid, opName, appKey);
        if (wmCustomerInfoAggre == null || wmCustomerInfoAggre.getWmCustomerBasicBo() == null) {
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "客户信息缺失，校验未通过");
        }
        if (wmCustomerInfoAggre.getWmCustomerKp() == null) {
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "KP信息缺失，校验未通过");
        }

        Long wmPoiId = wmCustomerInfoAggre.getWmPoiId();
        //添加操作人名称为空校验
        if (wmPoiId != null
                && StringUtils.isBlank(opName)) {
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "操作人名称不能为空，校验未通过");
        }
        WmCustomerBasicBo wmCustomerBasicBo = wmCustomerInfoAggre.getWmCustomerBasicBo();
        WmCustomerKp wmCustomerKp = wmCustomerInfoAggre.getWmCustomerKp();
        Set<Long> wmPoiIds = Sets.newHashSet(wmCustomerInfoAggre.getWmPoiId());

        WmCustomerNumberRepeatVo wmCustomerNumberRepeatVo = new WmCustomerNumberRepeatVo(wmCustomerBasicBo.getId(), wmCustomerBasicBo.getCustomerType(),
                wmCustomerBasicBo.getCustomerNumber(), wmCustomerBasicBo.getIsLeaf(), wmCustomerBasicBo.getRegistryState());
        if (wmCustomerInfoAggre.getWmPoiId() % 100 < MccCustomerConfig.getBizsettleDuplicateNumberThrowExceptionGrayPercent()) {
            wmCustomerNumberRepeatVo.setThrowException(true);
        }
        List<WmCustomerDB> wmCustomerDBList = wmCustomerPlatformDataParseService.validateCustomerNumber(wmCustomerNumberRepeatVo);
        WmCustomerDB wmCustomerDB = null;
        for (WmCustomerDB customerDB : wmCustomerDBList) {
            if (customerDB.getId() >= CUSTOMER_ID_SIZE) {
                wmCustomerDB = customerDB;
            }
        }
        if (wmCustomerDB == null) {
            WmCustomerCommonBo customerCommonBo;
            try {
                boolean lazyPushMq = true;
                AppContext.lazyProcess(lazyPushMq);
                customerCommonBo = SpringBeanUtil.getBean(WmCustomerCommonService.class)
                        .saveByCreateWmCustomer(wmCustomerBasicBo, wmCustomerKp, wmCustomerInfoAggre.getDueDate(), wmPoiIds, wmCustomerInfoAggre.getAgentId(), opUid, opName, appKey);
                AppContext.executeLazyTask();
            } finally {
                AppContext.clear();
            }
            return customerCommonBo;
        }

        logger.info("wmCustomerDB = {}", JSON.toJSONString(wmCustomerDB));
        List<Long> wmPoiIdsInDb = wmCustomerPoiService.selectWmPoiIdsByCustomerId(wmCustomerDB.getId());
        if (wmPoiIdsInDb != null && wmPoiIdsInDb.size() >= 1) {
            if (wmCustomerDB.getCustomerRealType() == CustomerRealTypeEnum.DANDIAN.getValue()
                    && wmCustomerInfoAggre.getWmCustomerBasicBo().getCustomerRealType() == CustomerRealTypeEnum.MEISHICHENG.getValue()) {
                if (wmCustomerDB.getAuditStatus() == CustomerAuditStatus.CUSTOMER_AUDIT_AUDITING.getCode()) {
                    throw new WmCustomerException(WmCustomerConstant.RESULT_CODE_CUSTOMER_VERIFY_ERROR, "已存在客户，且客户为单店类型，状态审核中，不可绑定门店。");
                }
                logger.info("更新客户类型 {} => {}  cusId:{}", wmCustomerDB.getCustomerRealType(), CustomerRealTypeEnum.MEISHICHENG.getName(),
                        wmCustomerDB.getId());
                wmCustomerPlatformDataParseService.updateCustomerRealType(wmCustomerDB.getId(), CustomerRealTypeEnum.MEISHICHENG.getValue());
                WmCustomerDB wmCustomerDBCopy = wmCustomerDB;
                return MasterSlaveHelper
                        .doInMaster(() -> bindPoiInExistingCustomer(wmCustomerInfoAggre, opUid, opName, wmCustomerKp, wmPoiIds, wmCustomerDBCopy, appKey));
            }
        }
        return bindPoiInExistingCustomer(wmCustomerInfoAggre, opUid, opName, wmCustomerKp, wmPoiIds, wmCustomerDB, appKey);
    }

    private WmCustomerCommonBo bindPoiInExistingCustomer(WmCustomerInfoAggre wmCustomerInfoAggre, int opUid, String opName, WmCustomerKp wmCustomerKp, Set<Long> wmPoiIds, WmCustomerDB wmCustomerDB, String appKey) throws WmCustomerException {
        boolean isPaperCustomer = wmCustomerDB.getSignMode() == CustomerSignMode.PAPER.getCode();
        // 有门店后置标的情况下,门店不允许关联纸质客户
        if (wmCustomerInfoAggre.getWmPoiPostMark() && isPaperCustomer) {
            throw new WmCustomerException(WmCustomerConstant.RESULT_CODE_PAPER_CUSTOMER_VERIFY_ERROR, "自入驻后置标门店不可关联纸质客户");
        }

        WmCustomerCommonBo wmCustomerCommonBo = verifyExistKP(wmCustomerDB.getId(), wmCustomerKp);
        if (CustomerConstants.RESULT_CODE_PASS != wmCustomerCommonBo.getCode()) {
            if (isPaperCustomer) {
                wmCustomerCommonBo.setCode(WmCustomerConstant.RESULT_CODE_PAPER_CUSTOMER_VERIFY_ERROR);
            }
            throw new WmCustomerException(wmCustomerCommonBo.getCode(), wmCustomerCommonBo.getMsg());
        }
        if (isPaperCustomer) {
            wmCustomerCommonBo.setPaperCustomer(true);
            wmCustomerCommonBo.setMsg("关联已有客户ID:" + wmCustomerDB.getId() + "，客户类型：纸质客户，请前往先富系统完成手动上单。");
        }

        //渠道非空则配置操作对象
        CustomerOperateBO customerOperateBO = customerPoiBindOrUnbindService.getZRZCustomerOperateDTO(appKey);
        if (customerOperateBO != null) {
            customerOperateBO.setOpUserId(opUid);
            customerOperateBO.setOpUserName(opName);
            customerOperateBO.setTaskSceneType(CustomerTaskSceneType.CUSTOMER_BIND_OR_UNBIND.getCode());
            customerOperateBO.setTaskType(CustomerTaskTypeEnum.CUSTOMER_BIND_POI.getCode());
            customerOperateBO.setBizTaskId(0);
        }

        try {
            CustomerTaskSourceEnum taskSourceEnum = CustomerTaskSourceEnum.UN_KNOWN;
            if (customerOperateBO != null && customerOperateBO.getOpSource() != null) {
                taskSourceEnum = CustomerTaskSourceEnum.of(customerOperateBO.getOpSource());
            }
            customerPoiBindService.bindOrRestart(new WmCustomerPoiBindParamBo(wmCustomerCommonBo.getCustomerId(), wmPoiIds, "", opUid, opName, true, taskSourceEnum, customerOperateBO, CustomerPoiBindTypeEnum.DIRECT_BIND));
        } catch (Exception e) {
            logger.warn("门店关联客户失败", e);
            wmCustomerCommonBo.setCode(CustomerConstants.RESULT_CODE_BINDING_ERROR);
            wmCustomerCommonBo.setMsg("门店关联客户失败,请手动关联。");
        }
        wmCustomerCommonBo.setExistOldCustomer(true);
        return wmCustomerCommonBo;
    }

    @Transactional(rollbackFor = Exception.class)
    public WmCustomerCommonBo saveByCreateWmCustomer(WmCustomerBasicBo wmCustomerBasicBo, WmCustomerKp wmCustomerKp, long dueDate, Set<Long> wmPoiIds, int agentId, int opUid, String opName,
                                                     String appKey) throws WmCustomerException, TException {
        // 计算业务线
        Integer bizOrgCode = CustomerRealTypeEnum.getBizOrgCodeByValue(wmCustomerBasicBo.getCustomerRealType());
        wmCustomerBasicBo.setBizOrgCode(bizOrgCode);

        //1、创建客户信息
        ValidateResultBo validateResultBo = saveOrUpdateCustomer(wmCustomerBasicBo, opUid, opName);

        //2、封装返回值信息
        WmCustomerCommonBo wmCustomerCommonBo = wmCustomerCommonWrapService.wrapWmCustomerCommonBo(validateResultBo.getCode(),
                validateResultBo.getMsg(), validateResultBo.getCustomerId());

        //3、插入KP信息
        wmCustomerKpService.batchSaveOrUpdateCustomerKp(wmCustomerCommonBo.getCustomerId(), Lists.newArrayList(wmCustomerKp), opUid, opName);

        //20230215:海南属地化项目针对自入驻门店在创建合同时需要判断门店类型，因此将门店绑定客户的动作前置到创建合同前
        //6、关联客户门店关系
        boolean bindPoiSuccess = false;
        try {

            //渠道非空则配置操作对象
            CustomerOperateBO customerOperateBO = customerPoiBindOrUnbindService.getZRZCustomerOperateDTO(appKey);
            if (customerOperateBO != null) {
                customerOperateBO.setOpUserId(opUid);
                customerOperateBO.setOpUserName(opName);
                customerOperateBO.setTaskSceneType(CustomerTaskSceneType.CUSTOMER_BIND_OR_UNBIND.getCode());
                customerOperateBO.setBizTaskId(0);
                customerOperateBO.setTaskType(CustomerTaskTypeEnum.CUSTOMER_BIND_POI.getCode());
            }
            CustomerTaskSourceEnum taskSourceEnum = CustomerTaskSourceEnum.UN_KNOWN;
            if (customerOperateBO != null && customerOperateBO.getOpSource() != null) {
                taskSourceEnum = CustomerTaskSourceEnum.of(customerOperateBO.getOpSource());
            }
            bindPoiSuccess = customerPoiBindService.bindOrRestart(new WmCustomerPoiBindParamBo(wmCustomerCommonBo.getCustomerId(), wmPoiIds, "", opUid, opName, true, taskSourceEnum, customerOperateBO, CustomerPoiBindTypeEnum.DIRECT_BIND));
        } catch (Exception e) {
            logger.warn("门店关联客户失败", e);
            wmCustomerCommonBo.setCode(CustomerConstants.RESULT_CODE_BINDING_ERROR);
            wmCustomerCommonBo.setMsg("门店关联客户失败,请手动关联。");
            // 推送大象消息，提示门店关联失败
            dxAlertMessageSender(wmCustomerCommonBo, wmPoiIds,
                    "【新建客户并绑定门店接口】门店wmPoiIds = {}关联customerId = {}失败，请手动关联，失败后不再进行结算主体变更。traceId:{}", opName);
        }

        //4.1、试图创建主体变更补充协议
        WmCustomerDB wmCustomerDB = wmCustomerPlatformDataParseService.selectCustomerFromPlatformById(wmCustomerCommonBo.getCustomerId());
        if (wmSubjectChangeSupplementEContractTempletService.isHainanCustomer(wmCustomerCommonBo.getCustomerId(), wmPoiIds)
                && wmSubjectChangeSupplementEContractTempletService.ziruzhuHnPoiAutoPackGray(Lists.newArrayList(wmPoiIds).get(0))) {
            createSujectChangeSupplement(wmCustomerDB, wmCustomerKp, dueDate, opUid, opName);
        }

        //4、创建C1合同
        createC1Contract(wmCustomerDB, wmCustomerKp, dueDate, opUid, opName);


        //5、创建C2合同
        createC2Contract(wmCustomerDB, wmCustomerKp, agentId, dueDate, opUid, opName);

        //7、补偿性的重试记录门店主体信息
        if (bindPoiSuccess) {
            logger.info("自入驻创建合同后，补偿写入门店主体，customerId:{}，wmPoiIds:{}", wmCustomerCommonBo.getCustomerId(), wmPoiIds);
            WmCustomerPoiAggre.Factory.make().batchChangePoiSubject(wmCustomerCommonBo.getCustomerId(), wmPoiIds, ContractSignSubjectOpTagEnum.CUSTOMER_BIND.getCode());
        }
        return wmCustomerCommonBo;
    }

    private void dxAlertMessageSender(WmCustomerCommonBo wmCustomerCommonBo, Set<Long> wmPoiIds, String originalMsg, String opName) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.service.customer.WmCustomerCommonService.dxAlertMessageSender(WmCustomerCommonBo,Set,String,String)");
        List<String> receivers = Lists.newArrayList(MccConfig.getDaXiangAlarmMisList());
        DXAlertUtils.dxAlert(originalMsg, receivers, wmPoiIds, wmCustomerCommonBo.getCustomerId(), Tracer.id());
    }

    /**
     * 创建客户信息
     *
     * @param wmCustomerBasicBo
     * @param opUid
     * @param opName
     * @return
     * @throws WmCustomerException
     * @throws TException
     */
    private ValidateResultBo saveOrUpdateCustomer(WmCustomerBasicBo wmCustomerBasicBo, int opUid, String opName) throws WmCustomerException, TException {
        ValidateResultBo validateResultBo = wmCustomerService.saveOrUpdateCustomer(wmCustomerBasicBo, false, opUid, opName, CustomerConstants.OPERATE_CUSTOMER_CHANNEL_ZIRUZHU);
        if (CustomerConstants.RESULT_CODE_PASS != validateResultBo.getCode()) {
            throw new WmCustomerException(validateResultBo.getCode(), validateResultBo.getMsg());
        }
        return validateResultBo;
    }

    /**
     * 创建C1合同
     *
     * @param wmCustomerDB
     * @param wmCustomerKp
     * @param dueDate
     * @param opUid
     * @param opName
     * @throws WmCustomerException
     * @throws TException
     */
    private void createC1Contract(WmCustomerDB wmCustomerDB, WmCustomerKp wmCustomerKp, long dueDate, int opUid, String opName) throws WmCustomerException, TException {
        RuzhuCreateContractBo ruzhuCreateContractBo = RuzhuCreateContractBo.
                builder()
                .wmCustomerDB(wmCustomerDB)
                .wmCustomerKp(wmCustomerKp)
                .dueDate(dueDate)
                .opUid(opUid)
                .opName(opName)
                .build();
        createC1Contract(ruzhuCreateContractBo);
    }

    private void createC1Contract(RuzhuCreateContractBo ruzhuCreateContractBo) throws WmCustomerException, TException {
        WmCustomerContractBo wmCustomerContractBo = initWmCustomerC1ContractBo(ruzhuCreateContractBo);
        wmContractService.saveAndStartSign(wmCustomerContractBo, ruzhuCreateContractBo.getOpUid(), ruzhuCreateContractBo.getOpName());
    }

    /**
     * 创建C2合同
     *
     * @param wmCustomerDB
     * @param wmCustomerKp
     * @param agentId
     * @param dueDate
     * @param opUid
     * @param opName
     * @throws WmCustomerException
     * @throws TException
     */
    private void createC2Contract(WmCustomerDB wmCustomerDB, WmCustomerKp wmCustomerKp, int agentId, long dueDate, int opUid, String opName) throws WmCustomerException, TException {
        //合作商ID为0，则不插入C2合同
        if (agentId <= 0) {
            return;
        }
        if (GrayUtil.isC2LongEffect()) {
            dueDate = 0;
        }
        RuzhuCreateContractBo ruzhuCreateContractBo = RuzhuCreateContractBo.
                builder()
                .wmCustomerDB(wmCustomerDB)
                .wmCustomerKp(wmCustomerKp)
                .agentId(agentId)
                .dueDate(dueDate)
                .opUid(opUid)
                .opName(opName)
                .build();
        createC2Contract(ruzhuCreateContractBo);
    }

    private void createC2Contract(RuzhuCreateContractBo ruzhuCreateContractBo) throws WmCustomerException, TException {
        if (ruzhuCreateContractBo.getAgentId() <= 0) {
            logger.warn("代理ID无实际值，不创建C2合同");
            return;
        }
        WmCustomerContractBo wmCustomerContractBo = initWmCustomerC2ContractBo(ruzhuCreateContractBo);
        wmContractService.saveAndStartSign(wmCustomerContractBo, ruzhuCreateContractBo.getOpUid(), ruzhuCreateContractBo.getOpName());
    }

    /**
     * 创建美食城承诺书
     *
     * @param ruzhuCreateContractBo
     * @throws WmCustomerException
     * @throws TException
     */
    private void createFoodcityStatementContract(RuzhuCreateContractBo ruzhuCreateContractBo) throws WmCustomerException, TException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.customer.WmCustomerCommonService.createFoodcityStatementContract(com.sankuai.meituan.waimai.customer.bo.sign.RuzhuCreateContractBo)");
        WmCustomerContractBo wmCustomerContractBo = initWmCustomerFoodcityStatementContractBo(ruzhuCreateContractBo);
        wmContractService.saveAndStartSign(wmCustomerContractBo, ruzhuCreateContractBo.getOpUid(), ruzhuCreateContractBo.getOpName());
    }


    /**
     * 创建主体变更补充协议
     *
     * @param wmCustomerDB
     * @param wmCustomerKp
     * @param dueDate
     * @param opUid
     * @param opName
     * @throws WmCustomerException
     * @throws TException
     */
    private void createSujectChangeSupplement(WmCustomerDB wmCustomerDB, WmCustomerKp wmCustomerKp, long dueDate, int opUid, String opName) throws WmCustomerException, TException {
        WmCustomerContractBo wmCustomerContractBo = initWmCustomerSubjectChangeSupplementBo(wmCustomerDB, wmCustomerKp, dueDate, opUid, opName);
        wmContractService.saveAndStartSign(wmCustomerContractBo, opUid, opName);
    }


    private WmCustomerCommonBo verifyExistKP(int customerId, WmCustomerKp wmCustomerKp) {
        List<WmCustomerKp> wmCustomerKps = wmCustomerKpDBMapper.selectByCustomerId(customerId, KpTypeEnum.SIGNER.getType());
        WmCustomerCommonBo wmCustomerCommonBo = new WmCustomerCommonBo();
        if (wmCustomerKp != null && !CollectionUtils.isEmpty(wmCustomerKps)) {
            WmCustomerKp customerKp = wmCustomerKps.get(0);
            wmCustomerSensitiveWordsService.readKpWhenSelect(customerKp);
            if (checkKP(customerKp, wmCustomerKp)) {
                wmCustomerCommonBo.setCode(CustomerConstants.RESULT_CODE_PASS);
                wmCustomerCommonBo.setCustomerId(customerId);
                return wmCustomerCommonBo;
            }
            wmCustomerCommonBo.setCode(WmCustomerConstant.RESULT_CODE_CUSTOMER_VERIFY_ERROR);
            wmCustomerCommonBo.setMsg("客户资质存在,KP签约人不一致");
            return wmCustomerCommonBo;
        }
        wmCustomerCommonBo.setCode(WmCustomerConstant.RESULT_CODE_CUSTOMER_VERIFY_ERROR);
        wmCustomerCommonBo.setMsg("客户资质存在,KP不存在");
        return wmCustomerCommonBo;
    }

    private WmCustomerContractBo initWmCustomerC1ContractBo(RuzhuCreateContractBo ruzhuCreateContractBo) throws TException, WmCustomerException {
        WmCustomerContractBo wmCustomerContractBo = new WmCustomerContractBo();
        wmCustomerContractBo.setBasicBo(wmCustomerCommonWrapService.wrapWmTempletC1ContractBasicBo(
                ruzhuCreateContractBo.getWmCustomerDB().getId(),
                ruzhuCreateContractBo.getDueDate()));
        wmCustomerContractBo.setSignBoList(wmCustomerCommonWrapService.wrapWmTempletC1ContractSignBoList(
                ruzhuCreateContractBo.getWmCustomerDB(),
                ruzhuCreateContractBo.getWmCustomerKp(),
                ruzhuCreateContractBo.getOpUid(),
                ruzhuCreateContractBo.getOpName()));
        wmCustomerContractBo.setIgnoreExistAnotherSignTypeContract(false);
        wmCustomerContractBo.setPackWay(ruzhuCreateContractBo.getPackWay());
        //自动填充乙方主体和履约服务主体
        autoFillCustomerSubject(ruzhuCreateContractBo.getWmCustomerDB(), wmCustomerContractBo);
        return wmCustomerContractBo;
    }

    //自动填充主体
    private void autoFillCustomerSubject(WmCustomerDB wmCustomerDB, WmCustomerContractBo wmCustomerContractBo) throws TException, WmCustomerException {
        WmPoiSignSubjectBo subjectBo = wmContractService.getSignSubjectBo(wmCustomerDB.getId(), WmTempletContractTypeEnum.C1_E.getCode());
        if (subjectBo == null) {
            throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "获取当前客户签约主体为空");
        }
        wmCustomerContractBo.getBasicBo().setLogisticsSubject(subjectBo.getPartlogisticsName());
        for (WmTempletContractSignBo signBo : wmCustomerContractBo.getSignBoList()) {
            if ("B".equals(signBo.getSignType())) {
                signBo.setSignName(subjectBo.getPartBName());
            }
        }
    }

    /**
     * 初始化C2合同入参
     *
     * @param ruzhuCreateContractBo
     * @return
     * @throws TException
     */
    private WmCustomerContractBo initWmCustomerC2ContractBo(RuzhuCreateContractBo ruzhuCreateContractBo) throws TException, WmCustomerException {
        WmCustomerContractBo wmCustomerContractBo = new WmCustomerContractBo();
        wmCustomerContractBo.setBasicBo(wmCustomerCommonWrapService.wrapWmTempletC2ContractBasicBo(
                ruzhuCreateContractBo.getWmCustomerDB().getId(),
                ruzhuCreateContractBo.getDueDate()));
        wmCustomerContractBo.setSignBoList(wmCustomerCommonWrapService.wrapWmTempletC2ContractSignBoList(
                ruzhuCreateContractBo.getWmCustomerDB(),
                ruzhuCreateContractBo.getWmCustomerKp(),
                ruzhuCreateContractBo.getAgentId(),
                ruzhuCreateContractBo.getOpUid(),
                ruzhuCreateContractBo.getOpName()));
        wmCustomerContractBo.setIgnoreExistAnotherSignTypeContract(false);
        wmCustomerContractBo.setPackWay(ruzhuCreateContractBo.getPackWay());
        wmCustomerContractBo.setWmPoiId(ruzhuCreateContractBo.getWmPoiId());
        return wmCustomerContractBo;
    }

    private WmCustomerContractBo initWmCustomerFoodcityStatementContractBo(RuzhuCreateContractBo ruzhuCreateContractBo) throws TException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.service.customer.WmCustomerCommonService.initWmCustomerFoodcityStatementContractBo(com.sankuai.meituan.waimai.customer.bo.sign.RuzhuCreateContractBo)");
        WmCustomerContractBo wmCustomerContractBo = new WmCustomerContractBo();
        wmCustomerContractBo.setBasicBo(wmCustomerCommonWrapService.wrapWmTempletFoodcityStatementBasicBo(
                ruzhuCreateContractBo.getWmCustomerDB().getId(),
                ruzhuCreateContractBo.getDueDate()));
        wmCustomerContractBo.setSignBoList(wmCustomerCommonWrapService.wrapWmTempletFoodcityStatementSignBoList(
                ruzhuCreateContractBo.getWmCustomerDB(),
                ruzhuCreateContractBo.getWmCustomerKp(),
                ruzhuCreateContractBo.getAgentId(),
                ruzhuCreateContractBo.getOpUid(),
                ruzhuCreateContractBo.getOpName()));
        wmCustomerContractBo.setIgnoreExistAnotherSignTypeContract(false);
        wmCustomerContractBo.setPackWay(ruzhuCreateContractBo.getPackWay());
        return wmCustomerContractBo;
    }

    private WmCustomerContractBo initWmCustomerSubjectChangeSupplementBo(WmCustomerDB wmCustomerDB, WmCustomerKp wmCustomerKp, long dueDate, int opUid, String opName) throws TException {
        WmCustomerContractBo wmCustomerContractBo = new WmCustomerContractBo();
        wmCustomerContractBo.setBasicBo(wmCustomerCommonWrapService.wrapWmTempletSubjectChangeSupplementBasicBo(wmCustomerDB.getId(), dueDate));
        wmCustomerContractBo.setSignBoList(wmCustomerCommonWrapService.wrapWmTempletSubjectChangeSupplementSignBoList(wmCustomerDB, wmCustomerKp, opUid, opName));
        wmCustomerContractBo.setIgnoreExistAnotherSignTypeContract(false);
        return wmCustomerContractBo;
    }

    /**
     * KP签约人一致性判断：姓名+手机号+身份证号
     *
     * @param defaultWmCustomerKp
     * @param verifyWmCustomerKp
     * @return
     */
    private boolean checkKP(WmCustomerKp defaultWmCustomerKp, WmCustomerKp verifyWmCustomerKp) {
        if (defaultWmCustomerKp.getCompellation().equals(verifyWmCustomerKp.getCompellation())
                && defaultWmCustomerKp.getPhoneNum().equals(verifyWmCustomerKp.getPhoneNum())
                && defaultWmCustomerKp.getCertNumber().equals(verifyWmCustomerKp.getCertNumber())) {
            return true;
        }
        return false;
    }

    public void sendCustomerAsyncNotify(String msg) throws WmCustomerException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_1", "com.sankuai.meituan.waimai.customer.service.customer.WmCustomerCommonService.sendCustomerAsyncNotify(java.lang.String)");
        WmCustomerBizAsyncNotifyMsg wmCustomerBizAsyncNotifyMsg = null;
        try {
            wmCustomerBizAsyncNotifyMsg = JSONObject.parseObject(msg, WmCustomerBizAsyncNotifyMsg.class);
        } catch (Exception e) {
            logger.error("WmCustomerBizAsyncNotifyMsg数据解析异常", e);
        }
        int notifyType = wmCustomerBizAsyncNotifyMsg.getNotifyType();
        if (notifyType == 0) {
            throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "不支持的业务类型");
        }
        try {
            customerAsyncNotifyMafkaProducer.sendMessage(msg);
        } catch (Exception e) {
            logger.error("customerAsyncNotifyMafkaProducer消息发送异常", e);
            throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "消息发送失败");
        }
    }

    //当前只支持创建C1/C2/美食城承诺书三类框架合同
    public BooleanResult saveCustomerContract(WmCustomerContractSaveParam saveParam) throws TException, WmCustomerException {
        logger.info("saveCustomerContract saveParam:{}", JSON.toJSONString(saveParam));
        try {
            List<WmTempletContractTypeEnum> contractTypeList = saveParam.getContractTypeList();
            WmCustomerDB wmCustomerDB = wmCustomerPlatformDataParseService.selectCustomerFromPlatformById(saveParam.getCustomerId());
            WmCustomerKp wmCustomerKp = new WmCustomerKp();
            wmCustomerKp.setPhoneNum(saveParam.getKpPhoneNum());
            wmCustomerKp.setCompellation(saveParam.getKpCompellation());
            for (WmTempletContractTypeEnum contractType : contractTypeList) {
                if (contractType == WmTempletContractTypeEnum.C1_E) {
                    RuzhuCreateContractBo ruzhuCreateContractBo = new RuzhuCreateContractBo();
                    ruzhuCreateContractBo.setWmCustomerDB(wmCustomerDB);
                    ruzhuCreateContractBo.setWmCustomerKp(wmCustomerKp);
                    ruzhuCreateContractBo.setDueDate(saveParam.getDueDate());
                    ruzhuCreateContractBo.setOpUid(saveParam.getOpUid());
                    ruzhuCreateContractBo.setOpName(saveParam.getOpName());
                    ruzhuCreateContractBo.setPackWay(saveParam.getPackWay());
                    createC1Contract(ruzhuCreateContractBo);
                } else if (contractType == WmTempletContractTypeEnum.C2_E) {
                    RuzhuCreateContractBo ruzhuCreateContractBo = new RuzhuCreateContractBo();
                    ruzhuCreateContractBo.setWmCustomerDB(wmCustomerDB);
                    ruzhuCreateContractBo.setWmCustomerKp(wmCustomerKp);
                    if (GrayUtil.isC2LongEffect()) {
                        ruzhuCreateContractBo.setDueDate(0);
                    } else {
                        ruzhuCreateContractBo.setDueDate(saveParam.getDueDate());
                    }
                    ruzhuCreateContractBo.setOpUid(saveParam.getOpUid());
                    ruzhuCreateContractBo.setOpName(saveParam.getOpName());
                    ruzhuCreateContractBo.setPackWay(saveParam.getPackWay());
                    ruzhuCreateContractBo.setAgentId(saveParam.getAgentId());
                    ruzhuCreateContractBo.setWmPoiId(saveParam.getWmPoiId());
                    createC2Contract(ruzhuCreateContractBo);
                } else if (contractType == WmTempletContractTypeEnum.FOODCITY_STATEMENT_E) {
                    RuzhuCreateContractBo ruzhuCreateContractBo = new RuzhuCreateContractBo();
                    ruzhuCreateContractBo.setWmCustomerDB(wmCustomerDB);
                    ruzhuCreateContractBo.setWmCustomerKp(wmCustomerKp);
                    ruzhuCreateContractBo.setDueDate(saveParam.getDueDate());
                    ruzhuCreateContractBo.setOpUid(saveParam.getOpUid());
                    ruzhuCreateContractBo.setOpName(saveParam.getOpName());
                    ruzhuCreateContractBo.setPackWay(saveParam.getPackWay());
                    ruzhuCreateContractBo.setAgentId(saveParam.getAgentId());
                    createFoodcityStatementContract(ruzhuCreateContractBo);
                }
            }
        } catch (Exception e) {
            logger.error("saveCustomerContract创建合同失败", e);
        }
        return new BooleanResult(true);
    }

    public BooleanResult sendCMSToCustomerKP(SendCmsToKpParam param) throws WmCustomerException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.service.customer.WmCustomerCommonService.sendCMSToCustomerKP(com.sankuai.meituan.waimai.thrift.customer.domain.customer.SendCmsToKpParam)");
        logger.info("sendCMSToCustomerKP param:{}",JSON.toJSONString(param));
        WmCustomerKp wmCustomerKp = wmCustomerKpService.getEffectSignerKp(param.getCustomerId());
        if(wmCustomerKp == null || StringUtils.isBlank(wmCustomerKp.getPhoneNum())){
            return new BooleanResult(false);
        }
        cmsThriftServiceAdapter.sendMessageThrowException(wmCustomerKp.getPhoneNum(), "", param.getSmsTemplateId(), param.getPair());
        return new BooleanResult(true);
    }

    public BooleanResult sendCMSToWmPoiContact(SendCmsToWmPoiContactParam param) throws WmCustomerException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_2", "com.sankuai.meituan.waimai.customer.service.customer.WmCustomerCommonService.sendCMSToWmPoiContact(com.sankuai.meituan.waimai.thrift.customer.domain.customer.SendCmsToWmPoiContactParam)");
        logger.info("sendCMSToWmPoiContact param:{}",JSON.toJSONString(param));
        WmPoiAggre wmPoiAggre = wmPoiQueryAdapter.getWmPoiAggreByWmPoiId(param.getWmPoiId());
        String contract = wmPoiAggre.getContacts();
        if(StringUtils.isBlank(contract)){
            return new BooleanResult(false);
        }
        List<WmPoiContact> contacts = JSON.parseArray(contract, WmPoiContact.class);
        if(CollectionUtils.isEmpty(contacts)){
            return new BooleanResult(false);
        }
        for(WmPoiContact contact : contacts){
            logger.info("sendCMSToWmPoiContact send cms contact:{}",JSON.toJSONString(contact));
            cmsThriftServiceAdapter.sendMessageThrowException(contact.getPhone(), "", param.getSmsTemplateId(), param.getPair());
        }
        return new BooleanResult(true);
    }
}
