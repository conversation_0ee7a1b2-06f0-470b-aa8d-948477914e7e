package com.sankuai.meituan.waimai.customer.service.customer;

import com.alibaba.fastjson.JSON;
import com.google.common.base.MoreObjects;
import com.google.common.base.Preconditions;
import com.google.common.collect.Lists;
import com.sankuai.meituan.util.ConfigUtilAdapter;
import com.sankuai.meituan.waimai.customer.contract.domain.WmTempletContractDB;
import com.sankuai.meituan.waimai.customer.contract.service.impl.WmContractService;
import com.sankuai.meituan.waimai.customer.ddd.domain.customer.aggre.WmCustomerPoiAggre;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerDB;
import com.sankuai.meituan.waimai.customer.service.AutoWireBase;
import com.sankuai.meituan.waimai.customer.service.kp.WmCustomerKpService;
import com.sankuai.meituan.waimai.customer.settle.service.WmSettleService;
import com.sankuai.meituan.waimai.poilogistics.thrift.domain.WmPoiAuditStatusInfo;
import com.sankuai.meituan.waimai.poilogistics.thrift.exception.WmPoiLogisticsException;
import com.sankuai.meituan.waimai.poilogistics.thrift.service.WmPoiLogisticsProcessInfoThriftService;
import com.sankuai.meituan.waimai.thrift.customer.constant.WmTempletContractTypeEnum;
import com.sankuai.meituan.waimai.thrift.constant.WmContractErrorCodeConstant;
import com.sankuai.meituan.waimai.thrift.customer.constant.*;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.ValidateResultBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.WmCustomerBasicBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.kp.WmCustomerKp;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import org.apache.commons.collections.CollectionUtils;
import org.apache.thrift.TException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Map;

public class SignModeSwitchChecker extends AutoWireBase {

    private static Logger logger = LoggerFactory.getLogger(SignModeSwitchChecker.class);

    private WmCustomerBasicBo wmCustomerBasicBoInput;

    private WmCustomerDB wmCustomerDB;

    @Autowired
    private WmCustomerPoiService wmCustomerPoiService;

    @Autowired
    private WmPoiLogisticsProcessInfoThriftService.Iface wmPoiLogisticsProcessInfoThriftService;

    @Autowired
    private WmSettleService wmSettleService;

    @Autowired
    private WmContractService wmContractService;

    @Autowired
    private WmCustomerKpService wmCustomerKpService;

    private ValidateResultBo validateResultBo;

    public SignModeSwitchChecker() {
        super();
    }

    public SignModeSwitchChecker(WmCustomerBasicBo wmCustomerBasicBoInput, WmCustomerDB wmCustomerDB, ValidateResultBo validateResultBo) {
        Preconditions.checkNotNull(wmCustomerBasicBoInput);
        Preconditions.checkNotNull(wmCustomerDB);
        this.wmCustomerBasicBoInput = wmCustomerBasicBoInput;
        this.wmCustomerDB = wmCustomerDB;
        this.validateResultBo = validateResultBo;
    }

    // TODO: 2022/8/1 此逻辑存在重复调用，有性能瓶颈，待优化
    public boolean check() throws WmCustomerException {
        if (wontSwitchSignMode()) {
            return true;
        }
        validateResultBo.setMsg("");
        checkAuditForDelivery();
        checkAuditForSettle();

        if (toPaperSignMode()) {
            checkConfirmForDelivery();
            checkConfirmForSettle();
            checkConfirmForContract();
            checkConfirmForUnbindingPoi();
            checkConfirmForCustomerRealType();
        } else {
            checkEffectiveForKp();
        }
        boolean isOk = validateResultBo.getCode() != CustomerConstants.RESULT_CODE_ERROR;
        if (!isOk) {
            validateResultBo.setMsg("操作失败，" + validateResultBo.getMsg() + "无法修改签约形式");
        }
        return isOk;
    }

    /**
     * 是否切换签约模式
     *
     * @return
     */
    public boolean wontSwitchSignMode() {
        return wmCustomerBasicBoInput.getSignMode() == null
                || wmCustomerBasicBoInput.getSignMode().equals(wmCustomerDB.getSignMode());
    }

    /**
     * 客户下存在解绑中门店，校验失败
     */
    private void checkConfirmForUnbindingPoi() {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_2", "com.sankuai.meituan.waimai.customer.service.customer.SignModeSwitchChecker.checkConfirmForUnbindingPoi()");
        Integer unbindingPoiCnt = WmCustomerPoiAggre.Factory.make().countUnbindingPoi(wmCustomerBasicBoInput.getId());
        if (unbindingPoiCnt != null && unbindingPoiCnt.compareTo(0) > 0) {
            validateResultBo.setCode(CustomerConstants.RESULT_CODE_ERROR);
            validateResultBo.setMsg(validateResultBo.getMsg() + "解绑确认信息正在确认中，");
        }
    }

    /**
     * 签约模式为电子签约，必须存在生效签约人KP
     */
    private void checkEffectiveForKp() {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.service.customer.SignModeSwitchChecker.checkEffectiveForKp()");
        WmCustomerKp customerSignerKp = wmCustomerKpService.getCustomerKpOfEffectiveSigner(wmCustomerBasicBoInput.getId());
        if (customerSignerKp == null) {
            validateResultBo.setCode(CustomerConstants.RESULT_CODE_ERROR);
            validateResultBo.setMsg(validateResultBo.getMsg() + "无生效状态的KP信息，");
        }
    }

    /**
     * 校验结算审核状态
     *
     * @throws WmCustomerException
     */
    private void checkAuditForSettle() throws WmCustomerException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.customer.SignModeSwitchChecker.checkAuditForSettle()");
        byte settleStatus = wmSettleService.getSettleStatusByWmCustomerId(wmCustomerBasicBoInput.getId());
        if (settleStatus == WmSettleConstant.SETTLE_STATUS_TO_AUDIT) {
            validateResultBo.setCode(CustomerConstants.RESULT_CODE_ERROR);
            validateResultBo.setMsg(validateResultBo.getMsg() + "结算信息正在审核中，");
        }
    }

    /**
     * 校验结算签约状态
     *
     * @throws WmCustomerException
     */
    private void checkConfirmForSettle() throws WmCustomerException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.service.customer.SignModeSwitchChecker.checkConfirmForSettle()");
        byte settleStatus = wmSettleService.getSettleStatusByWmCustomerId(wmCustomerBasicBoInput.getId());
        if (settleStatus == WmSettleConstant.SETTLE_STATUS_TO_CONFIRM
                || settleStatus == WmSettleConstant.SETTLE_STATUS_WAIT_SIGN) {
            validateResultBo.setCode(CustomerConstants.RESULT_CODE_ERROR);
            validateResultBo.setMsg(validateResultBo.getMsg() + "结算信息正在确认中，");
        }
    }

    /**
     * 校验C1、C2框架合同
     */
    private void checkConfirmForContract() {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.service.customer.SignModeSwitchChecker.checkConfirmForContract()");
        List<WmTempletContractDB> contractList = wmContractService.getContractByCustomerIdAndTypes(wmCustomerBasicBoInput.getId(),
                Lists.newArrayList(WmTempletContractTypeEnum.C1_E.getCode(), WmTempletContractTypeEnum.C2_E.getCode()));
        for (WmTempletContractDB contractDB : MoreObjects
                .firstNonNull(contractList, Lists.<WmTempletContractDB>newArrayList())) {
            if (contractDB.getStatus().equals(CustomerContractStatus.SIGNING.getCode())
                    || contractDB.getStatus().equals(CustomerContractStatus.WAITING_SIGN.getCode())) {
                validateResultBo.setCode(CustomerConstants.RESULT_CODE_ERROR);
                validateResultBo.setMsg(validateResultBo.getMsg() + "合同信息正在确认中，");
                break;
            }
        }
    }

    private boolean toPaperSignMode() {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_2", "com.sankuai.meituan.waimai.customer.service.customer.SignModeSwitchChecker.toPaperSignMode()");
        return wmCustomerBasicBoInput.getSignMode() == CustomerSignMode.PAPER.getCode();
    }

    /**
     * 校验客户下所有门店的配送信息审核状态
     *
     * @throws WmCustomerException
     */
    private void checkAuditForDelivery() throws WmCustomerException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.service.customer.SignModeSwitchChecker.checkAuditForDelivery()");
        List<Long> poiIds = wmCustomerPoiService.selectWmPoiIdsByCustomerId(wmCustomerBasicBoInput.getId());
        if (CollectionUtils.isEmpty(poiIds)) {
            return;
        }
        List<List<Long>> poiIdBatchList = Lists
                .partition(poiIds, ConfigUtilAdapter.getInt("check_switch_mode_poibatch_size", 200));
        for (List<Long> oneBtach : poiIdBatchList) {
            Map<Long, WmPoiAuditStatusInfo> auditStatusMap;
            try {
                auditStatusMap = wmPoiLogisticsProcessInfoThriftService.getAuditStatus(oneBtach);
                logger.info("获取配送的审核状态 auditStatusMap：{}", JSON.toJSONString(auditStatusMap));
            } catch (WmPoiLogisticsException | TException e) {
                logger.error("获取门店配送状态失败：" + e.getMessage(), e);
                throw new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR, "系统异常了");
            }
            if (auditStatusMap == null) {
                continue;
            }
            for (Map.Entry<Long, WmPoiAuditStatusInfo> entry : auditStatusMap.entrySet()) {
                if (entry.getValue().auditStatus) {
                    validateResultBo.setCode(CustomerConstants.RESULT_CODE_ERROR);
                    validateResultBo.setMsg(validateResultBo.getMsg() + "配送信息正在审核中，");
                    return;
                }
            }
        }
    }

    /**
     * 校验客户下所有门店的配送信息签约状态
     *
     * @throws WmCustomerException
     */
    private void checkConfirmForDelivery() throws WmCustomerException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.service.customer.SignModeSwitchChecker.checkConfirmForDelivery()");
        List<Long> poiIds = wmCustomerPoiService.selectWmPoiIdsByCustomerId(wmCustomerBasicBoInput.getId());
        if (CollectionUtils.isEmpty(poiIds)) {
            return;
        }
        List<List<Long>> poiIdBatchList = Lists
                .partition(poiIds, ConfigUtilAdapter.getInt("check_switch_mode_poibatch_size", 200));
        for (List<Long> oneBtach : poiIdBatchList) {
            Map<Long, WmPoiAuditStatusInfo> confirmStatusMap;
            try {
                confirmStatusMap = wmPoiLogisticsProcessInfoThriftService.getAuditStatus(oneBtach);
                logger.info("获取配送的确认状态 confirmStatusMap：{}", JSON.toJSONString(confirmStatusMap));
            } catch (WmPoiLogisticsException | TException e) {
                logger.error("获取门店配送状态失败：" + e.getMessage(), e);
                throw new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR, "系统异常了");
            }
            if (confirmStatusMap == null) {
                continue;
            }
            for (Map.Entry<Long, WmPoiAuditStatusInfo> entry : confirmStatusMap.entrySet()) {
                if (entry.getValue().confirmStatus
                        || entry.getValue().waitConfirmStatus) {
                    validateResultBo.setCode(CustomerConstants.RESULT_CODE_ERROR);
                    validateResultBo.setMsg(validateResultBo.getMsg() + "配送信息正在确认中，");
                    return;
                }
            }
        }
    }

    // 校验逻辑重复 @since WmCustomerSignModeValidator
    private void checkConfirmForCustomerRealType() {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_1", "com.sankuai.meituan.waimai.customer.service.customer.SignModeSwitchChecker.checkConfirmForCustomerRealType()");
        if (CustomerRealTypeEnum.AGGREGATE_DISTRIBUTOR.getValue() == wmCustomerBasicBoInput.getCustomerRealType()) {
            validateResultBo.setCode(CustomerConstants.RESULT_CODE_ERROR);
            validateResultBo.setMsg("客户类型为'聚合配送商'时, 不可选择纸质签约");
        }
    }

}
