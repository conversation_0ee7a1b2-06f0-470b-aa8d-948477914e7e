/*
 * Copyright (c) 2022 meituan.com. All Rights Reserved.
 */
package com.sankuai.meituan.waimai.customer.service.customer;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.meituan.pay.mwallet.proxy.thrift.ErrorTo;
import com.meituan.pay.mwallet.proxy.thrift.MwalletProxyService;
import com.meituan.pay.mwallet.proxy.thrift.entity.auth.*;
import com.meituan.pay.mwallet.proxy.util.BeanToMapUtil;
import com.meituan.pay.mwallet.util.SignAndEncUtil;
import com.meituan.service.inf.kms.client.Kms;
import com.sankuai.inf.leaf.thrift.IDGen;
import com.sankuai.meituan.waimai.customer.config.MccCustomerConfig;
import com.sankuai.meituan.waimai.poibizflow.constant.WmComplianceErrorCodeConstant;
import com.sankuai.meituan.waimai.poibizflow.thrift.exception.WmPoiBizException;
import com.sankuai.meituan.waimai.thrift.customer.constant.customer.CertificateAuthTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.customer.PreAuthResultTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.PreAuthResultBO;
import com.sankuai.meituan.waimai.thrift.exception.WmServerException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * 客户实名认证
 */
@Service
@Slf4j
public class WmCustomerRealAuthService {

    /**
     * 服务APPKEY
     */
    public static final String APP_KEY = "com.sankuai.waimai.e.customer";

    /**
     * 实名认证服务的私钥key
     */
    public static final String PRIVATE_KEY_V2 = "mcertify.rsa.private.key.v2";

    /**
     * 实名认证服务的共钥key
     */
    public static final String PUBLIC_KEY_V2 = "mcertify.rsa.public.key.v2";

    /**
     * 实名认证服务的私钥key（企业三四要素）
     */
    public static final String PRIVATE_KEY_V3 = "mcertify.rsa.private.key.v3";
    /**
     * 实名认证服务的公钥key（企业三四要素）
     */
    public static final String PUBLIC_KEY_V3 = "mcertify.rsa.public.key.v3";

    public static final String LEAF_KEY = "waimai.contract.authid";


    private static String CHARSET = "UTF-8";

    /**
     * 金融返回Error.level的值
     * =1表示系统异常
     * >1表示业务失败
     */
    private static final int ERROR_LEVEL_SYS_ERROR = 1;


    @Autowired
    private IDGen.Iface iDGen;

    @Autowired
    private MwalletProxyService.Iface mWalletProxyService;


    /**
     * 企业三要素认证
     *
     * @param companyName
     * @param licenseNo
     * @param accountName
     * @return
     * @throws Exception
     */
    public PreAuthResultBO enterpriseThreeElePreAuthForClean(String companyName, String licenseNo, String accountName) throws Exception {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.customer.WmCustomerRealAuthService.enterpriseThreeElePreAuthForClean(java.lang.String,java.lang.String,java.lang.String)");
        log.info("enterpriseThreeElePreAuthForClean:companyName={},licenseNo={},accountName={}", companyName, licenseNo, accountName);
        String privateKey = Kms.getByName(APP_KEY, PRIVATE_KEY_V3);
        String publicKey = Kms.getByName(APP_KEY, PUBLIC_KEY_V3);
        long bizSerialNo = iDGen.getSnowFlake(LEAF_KEY).getId();
        PreAuthReqTo preAuthReqTo = new PreAuthReqTo();
        preAuthReqTo.setBizSerialNo(bizSerialNo);
        preAuthReqTo.setIphPayMerchantNo(MccCustomerConfig.getMerchantNoForCleanRealAuth());
        preAuthReqTo.setType(AuthType.ENTERPRISE);
        AuthReqFieldTo authReqFieldTo = new AuthReqFieldTo();
        preAuthReqTo.setAuthInfoType(CertificateAuthTypeEnum.ENTERPRISE_THREE_ELEMENT_AUTH.getType());
        authReqFieldTo.setCertificateTypeCode(String.valueOf(CertificateTypeEnum.BUSINESS_LICENSE.getValue()));
        authReqFieldTo.setLicenseNoEncrypt(SignAndEncUtil.encrypt(licenseNo, publicKey, CHARSET));
        authReqFieldTo.setCompanyName(companyName);
        authReqFieldTo.setLegalPersonName(accountName);
        preAuthReqTo.setReqField(authReqFieldTo);
        // 将bean转成map
        Map<String, String> convertMap = null;
        try {
            convertMap = BeanToMapUtil.convertBeanForSign(preAuthReqTo);
        } catch (Exception e) {
            log.error("enterpriseThreeElePreAuthForClean:companyName={},licenseNo={},accountName={}", companyName, licenseNo, accountName, e);
            throw new WmPoiBizException(WmComplianceErrorCodeConstant.BUSINESS_ERROR, "预认证数据转化异常");
        }
        String sign = SignAndEncUtil.sign(convertMap, privateKey, CHARSET);
        preAuthReqTo.setSign(sign);
        preAuthReqTo.setSignType("RSA");
        log.info("#enterpriseThreeElePreAuthForClean,preAuthReqTo={}", JSONObject.toJSONString(preAuthReqTo));
        PreAuthSignResTo preAuthSignResTo = mWalletProxyService.preAuth(preAuthReqTo);
        log.info("#enterpriseThreeElePreAuthForClean preAuthSignResTo={}", JSON.toJSONString(preAuthSignResTo));
        PreAuthResultBO result = convertPreAuthSignRes(preAuthSignResTo);
        if (result != null) {
            result.setAuthType(CertificateAuthTypeEnum.ENTERPRISE_THREE_ELEMENT_AUTH.getType());
            if (result.getResult() == PreAuthResultTypeEnum.FAIL.getType()) {
                result.setMsg("企业三要素认证失败（执照名称、统一编码、法人姓名）；失败原因：" + result.getMsg());
            }
        }
        return result;
    }

    /**
     * 企业三要素认证
     *
     * @param companyName 执照名称
     * @param licenseNo   统一社会信用编码
     * @param accountName 法人名字
     * @return
     * @throws Exception
     */
    public PreAuthResultBO enterpriseThreeElePreAuth(String companyName, String licenseNo, String accountName) throws Exception {
        log.info("enterpriseThreeElePreAuth:companyName={},licenseNo={},accountName={}", companyName, licenseNo, accountName);
        String privateKey = Kms.getByName(APP_KEY, PRIVATE_KEY_V2);
        String publicKey = Kms.getByName(APP_KEY, PUBLIC_KEY_V2);
        long bizSerialNo = iDGen.getSnowFlake(LEAF_KEY).getId();
        PreAuthReqTo preAuthReqTo = new PreAuthReqTo();
        preAuthReqTo.setBizSerialNo(bizSerialNo);
        preAuthReqTo.setIphPayMerchantNo(MccCustomerConfig.getMerchantNoForRealAuth());
        preAuthReqTo.setType(AuthType.ENTERPRISE);
        AuthReqFieldTo authReqFieldTo = new AuthReqFieldTo();
        preAuthReqTo.setAuthInfoType(CertificateAuthTypeEnum.ENTERPRISE_THREE_ELEMENT_AUTH.getType());
        authReqFieldTo.setCertificateTypeCode(String.valueOf(CertificateTypeEnum.BUSINESS_LICENSE.getValue()));
        authReqFieldTo.setLicenseNoEncrypt(SignAndEncUtil.encrypt(licenseNo, publicKey, CHARSET));
        authReqFieldTo.setCompanyName(companyName);
        authReqFieldTo.setLegalPersonName(accountName);
        preAuthReqTo.setReqField(authReqFieldTo);
        // 将bean转成map
        Map<String, String> convertMap = null;
        try {
            convertMap = BeanToMapUtil.convertBeanForSign(preAuthReqTo);
        } catch (Exception e) {
            log.error("enterpriseThreeElePreAuth:companyName={},licenseNo={},accountName={}", companyName, licenseNo, accountName, e);
            throw new WmPoiBizException(WmComplianceErrorCodeConstant.BUSINESS_ERROR, "预认证数据转化异常");
        }
        String sign = SignAndEncUtil.sign(convertMap, privateKey, CHARSET);
        preAuthReqTo.setSign(sign);
        preAuthReqTo.setSignType("RSA");
        log.info("#enterpriseThreeElePreAuth,preAuthReqTo={}", JSONObject.toJSONString(preAuthReqTo));
        PreAuthSignResTo preAuthSignResTo = mWalletProxyService.preAuth(preAuthReqTo);
        log.info("#enterpriseThreeElePreAuth preAuthSignResTo={}", JSON.toJSONString(preAuthSignResTo));
        PreAuthResultBO result = convertPreAuthSignRes(preAuthSignResTo);
        if (result != null) {
            result.setAuthType(CertificateAuthTypeEnum.ENTERPRISE_THREE_ELEMENT_AUTH.getType());
            if (result.getResult() == PreAuthResultTypeEnum.FAIL.getType()) {
                result.setMsg("企业三要素认证失败（执照名称、统一编码、法人姓名）；失败原因：" + result.getMsg());
            }
        }
        return result;
    }

    /**
     * 企业四要素认证
     *
     * @param companyName
     * @param licenseNo
     * @param accountName
     * @param identityNo
     * @return
     * @throws Exception
     */
    public PreAuthResultBO enterpriseFourElePreAuthForClean(String companyName, String licenseNo, String accountName, String identityNo) throws Exception {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_1", "com.sankuai.meituan.waimai.customer.service.customer.WmCustomerRealAuthService.enterpriseFourElePreAuthForClean(java.lang.String,java.lang.String,java.lang.String,java.lang.String)");
        log.info("enterpriseFourElePreAuthForClean:companyName={},licenseNo={},accountName={},identityNo={}", companyName, licenseNo, accountName, identityNo);
        String privateKey = Kms.getByName(APP_KEY, PRIVATE_KEY_V3);
        String publicKey = Kms.getByName(APP_KEY, PUBLIC_KEY_V3);
        long bizSerialNo = iDGen.getSnowFlake(LEAF_KEY).getId();
        PreAuthReqTo preAuthReqTo = new PreAuthReqTo();
        preAuthReqTo.setBizSerialNo(bizSerialNo);
        preAuthReqTo.setIphPayMerchantNo(MccCustomerConfig.getMerchantNoForCleanRealAuth());
        preAuthReqTo.setType(AuthType.ENTERPRISE);
        AuthReqFieldTo authReqFieldTo = new AuthReqFieldTo();
        preAuthReqTo.setAuthInfoType(CertificateAuthTypeEnum.EXACT_ENTERPRISE_FOUR_ELEMENT_AUTH.getType());
        authReqFieldTo.setCertificateTypeCode(String.valueOf(CertificateTypeEnum.BUSINESS_LICENSE.getValue()));
        authReqFieldTo.setLicenseNoEncrypt(SignAndEncUtil.encrypt(licenseNo, publicKey, CHARSET));
        authReqFieldTo.setCompanyName(companyName);
        authReqFieldTo.setLegalPersonName(accountName);
        authReqFieldTo.setLegalPersonIdentityNoEncrypt(SignAndEncUtil.encrypt(identityNo, publicKey, CHARSET));
        authReqFieldTo.setLegalPersonCertificateTypeCode(String.valueOf(CertificateTypeEnum.IDENTITY_CARD.getValue()));
        preAuthReqTo.setReqField(authReqFieldTo);
        // 将bean转成map
        Map<String, String> convertMap = null;
        try {
            convertMap = BeanToMapUtil.convertBeanForSign(preAuthReqTo);
        } catch (Exception e) {
            log.error("enterpriseFourElePreAuthForClean:companyName={},licenseNo={},accountName={}", companyName, licenseNo, accountName, e);
            throw new WmPoiBizException(WmComplianceErrorCodeConstant.BUSINESS_ERROR, "预认证数据转化异常");
        }
        String sign = SignAndEncUtil.sign(convertMap, privateKey, CHARSET);
        preAuthReqTo.setSign(sign);
        preAuthReqTo.setSignType("RSA");
        log.info("#enterpriseFourElePreAuthForClean,preAuthReqTo={}", JSONObject.toJSONString(preAuthReqTo));
        PreAuthSignResTo preAuthSignResTo = mWalletProxyService.preAuth(preAuthReqTo);
        log.info("#enterpriseFourElePreAuthForClean.preAuthSignResTo={}", JSON.toJSONString(preAuthSignResTo));
        PreAuthResultBO result = convertPreAuthSignRes(preAuthSignResTo);
        if (result != null) {
            result.setAuthType(CertificateAuthTypeEnum.EXACT_ENTERPRISE_FOUR_ELEMENT_AUTH.getType());
            if (result.getResult() == PreAuthResultTypeEnum.FAIL.getType()) {
                result.setMsg("企业四要素认证失败（执照名称、统一编码、法人姓名、法人身份证号）；失败原因：" + result.getMsg());
            }
        }
        return result;
    }

    /**
     * 企业四要素认证
     *
     * @param companyName 执照名称
     * @param licenseNo   统一社会信用编码
     * @param accountName 法人名字
     * @param identityNo  身份证编码
     * @return
     * @throws Exception
     */
    public PreAuthResultBO enterpriseFourElePreAuth(String companyName, String licenseNo, String accountName, String identityNo) throws Exception {
        log.info("enterpriseFourElePreAuth:companyName={},licenseNo={},accountName={},identityNo={}", companyName, licenseNo, accountName, identityNo);
        String privateKey = Kms.getByName(APP_KEY, PRIVATE_KEY_V2);
        String publicKey = Kms.getByName(APP_KEY, PUBLIC_KEY_V2);
        long bizSerialNo = iDGen.getSnowFlake(LEAF_KEY).getId();
        PreAuthReqTo preAuthReqTo = new PreAuthReqTo();
        preAuthReqTo.setBizSerialNo(bizSerialNo);
        preAuthReqTo.setIphPayMerchantNo(MccCustomerConfig.getMerchantNoForRealAuth());
        preAuthReqTo.setType(AuthType.ENTERPRISE);
        AuthReqFieldTo authReqFieldTo = new AuthReqFieldTo();
        preAuthReqTo.setAuthInfoType(CertificateAuthTypeEnum.EXACT_ENTERPRISE_FOUR_ELEMENT_AUTH.getType());
        authReqFieldTo.setCertificateTypeCode(String.valueOf(CertificateTypeEnum.BUSINESS_LICENSE.getValue()));
        authReqFieldTo.setLicenseNoEncrypt(SignAndEncUtil.encrypt(licenseNo, publicKey, CHARSET));
        authReqFieldTo.setCompanyName(companyName);
        authReqFieldTo.setLegalPersonName(accountName);
        authReqFieldTo.setLegalPersonIdentityNoEncrypt(SignAndEncUtil.encrypt(identityNo, publicKey, CHARSET));
        authReqFieldTo.setLegalPersonCertificateTypeCode(String.valueOf(CertificateTypeEnum.IDENTITY_CARD.getValue()));
        preAuthReqTo.setReqField(authReqFieldTo);
        // 将bean转成map
        Map<String, String> convertMap = null;
        try {
            convertMap = BeanToMapUtil.convertBeanForSign(preAuthReqTo);
        } catch (Exception e) {
            log.error("enterpriseFourElePreAuth:companyName={},licenseNo={},accountName={}", companyName, licenseNo, accountName, e);
            throw new WmPoiBizException(WmComplianceErrorCodeConstant.BUSINESS_ERROR, "预认证数据转化异常");
        }
        String sign = SignAndEncUtil.sign(convertMap, privateKey, CHARSET);
        preAuthReqTo.setSign(sign);
        preAuthReqTo.setSignType("RSA");
        log.info("#enterpriseFourElePreAuth,preAuthReqTo={}", JSONObject.toJSONString(preAuthReqTo));
        PreAuthSignResTo preAuthSignResTo = mWalletProxyService.preAuth(preAuthReqTo);
        log.info("#enterpriseFourElePreAuth.preAuthSignResTo={}", JSON.toJSONString(preAuthSignResTo));
        PreAuthResultBO result = convertPreAuthSignRes(preAuthSignResTo);
        if (result != null) {
            result.setAuthType(CertificateAuthTypeEnum.EXACT_ENTERPRISE_FOUR_ELEMENT_AUTH.getType());
            if (result.getResult() == PreAuthResultTypeEnum.FAIL.getType()) {
                result.setMsg("企业四要素认证失败（执照名称、统一编码、法人姓名、法人身份证号）；失败原因：" + result.getMsg());
            }
        }
        return result;
    }

    /**
     * 组织认证结果信息
     *
     * @param preAuthSignResTo
     * @return
     * @throws WmServerException
     */
    private PreAuthResultBO convertPreAuthSignRes(PreAuthSignResTo preAuthSignResTo) throws WmServerException {
        PreAuthResultBO preAuthResult = new PreAuthResultBO();
        setResult(preAuthResult, PreAuthResultTypeEnum.FAIL);
        if (preAuthSignResTo == null || preAuthSignResTo.getPlain() == null) {
            return preAuthResult;
        }
        PreAuthResTo preAuthResTo = preAuthSignResTo.getPlain();
        //status=false表示系统或者业务异常统一处理为无结果
        if (preAuthResTo.getStatus().equalsIgnoreCase("fail")) {
            return makeFailErrorResult(preAuthResult, preAuthResTo);
        }
        if (preAuthResTo.getStatus().equalsIgnoreCase("success")) {
            PreAuthDataTo preAuthDataTo = preAuthResTo.getData();
            if (preAuthDataTo == null) {
                return preAuthResult;
            }
            String authStatus = preAuthDataTo.getAuthStatus();
            //authStatus=true且authStatus等于SUC表示实名认证成功，否则表示实名认证失败
            if (preAuthDataTo.isResult() && authStatus.equalsIgnoreCase("SUC")) {
                setResult(preAuthResult, PreAuthResultTypeEnum.SUCCESS);
                return preAuthResult;
            }
            return makeSuccessErrorResult(preAuthResult, preAuthResTo);
        }
        return preAuthResult;
    }

    /**
     * 设置结果对象
     *
     * @param preAuthResult
     * @param type
     */
    private void setResult(PreAuthResultBO preAuthResult, PreAuthResultTypeEnum type) {
        preAuthResult.setMsg(type.getName());
        preAuthResult.setResult(type.getType());
    }

    /**
     * 组织系统异常返回结果
     * 金融返回Error.level的值
     * =1表示系统异常
     * >1表示业务失败
     *
     * @param preAuthResult
     * @param preAuthResTo
     * @return
     */
    private PreAuthResultBO makeFailErrorResult(PreAuthResultBO preAuthResult, PreAuthResTo preAuthResTo) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.service.customer.WmCustomerRealAuthService.makeFailErrorResult(PreAuthResultBO,PreAuthResTo)");
        preAuthResult.setResult(PreAuthResultTypeEnum.FAIL.getType());
        ErrorTo error = preAuthResTo.getError();
        if (error != null && StringUtils.isNotBlank(error.getMessage())) {
            preAuthResult.setCode(error.getCode() + "");
            if (StringUtils.isNotBlank(error.getMessage())) {
                preAuthResult.setMsg(error.getMessage());
            } else {
                preAuthResult.setMsg("金融系统异常请稍后重试");
            }
        }
        return preAuthResult;
    }

    /**
     * 组织请求成功但是业务异常返回结果
     *
     * @param preAuthResult
     * @param preAuthResTo
     * @return
     */
    private PreAuthResultBO makeSuccessErrorResult(PreAuthResultBO preAuthResult, PreAuthResTo preAuthResTo) {
        preAuthResult.setResult(PreAuthResultTypeEnum.FAIL.getType());
        PreAuthDataTo preAuthDataTo = preAuthResTo.getData();
        if (preAuthDataTo != null && StringUtils.isNotBlank(preAuthDataTo.getErrorMsg())) {
            String errorCode = preAuthDataTo.getErrorCode();
            preAuthResult.setCode(errorCode);
            preAuthResult.setMsg(preAuthDataTo.getErrorMsg());
            List<String> noResultErrorCodeList = MccCustomerConfig.preAuthNoResultErrorCode();
            if (noResultErrorCodeList.contains(errorCode)) {
                preAuthResult.setResult(PreAuthResultTypeEnum.NO_RESULT.getType());
            }
        }
        return preAuthResult;
    }
}
