package com.sankuai.meituan.waimai.customer.service.customer.monitor.customerPoiList;

import com.dianping.frog.sdk.alarm.DXUtil;
import com.dianping.frog.sdk.data.*;
import com.dianping.frog.sdk.rpc.RpcService;
import com.dianping.frog.sdk.rpc.RpcServiceFactory;
import com.dianping.frog.sdk.runner.DefaultRuleRunner;
import com.dianping.lion.client.util.JsonUtils;
import com.dianping.lion.client.util.StringUtils;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.reflect.TypeToken;
import com.sankuai.meituan.waimai.customer.constant.WmCustomerPoiListESFields;

import java.util.Map;

public class WmCustomerPoiListStatusRuleRunner extends DefaultRuleRunner {

    private Gson gson = new GsonBuilder().create();


    @Override
    public String check(RawData rawData, RawData... rawData2) throws Exception {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.customer.monitor.customerPoiList.WmCustomerPoiListStatusRuleRunner.check(com.dianping.frog.sdk.data.RawData,com.dianping.frog.sdk.data.RawData[])");
        MafkaRawData triggerMsgData = (MafkaRawData) rawData;
        String body = triggerMsgData.getMafkaMessage().getBody().toString();
        Map<String, Object> triggerMsg = toMap(body);

        RpcService rpcService = RpcServiceFactory.getThriftInstance("com.sankuai.meituan.waimai.thrift.customer.service.WmCustomerPoiThriftService",
                "com.sankuai.waimai.e.customer", 10000, null, "8435");
        Map<String, Object> params = Maps.newHashMap();
        params.put("tableName", "wm_poi_status");
        params.put("operateType", DmlType.UPDATE.getCode());
        Map<String, Map<String, Object>> map = Maps.newHashMap();
        Map<String, Object> current = Maps.newHashMap();
        Integer wmPoiId = (Integer) triggerMsg.get("wmPoiId");
        current.put("oldValue", wmPoiId.toString());
        current.put("newValue", wmPoiId.toString());
        map.put("wm_poi_id", current);
        params.put("columnInfoMap", map);

        String result = rpcService.invoke("monitorCustomerPoiListEs",
                Lists.newArrayList("com.sankuai.meituan.waimai.thrift.customer.domain.customer.WmCustomerPoiListUpdateInfo"),
                Lists.newArrayList(JsonUtils.toJson(params)));

        if (!StringUtils.isBlank(result) && !"\"\"".equals(result)) {
            return result;
        }
        return null;
    }


    @Override
    public void alarm(String s, RawData rawData, RawData... rawData2) throws Exception {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.customer.monitor.customerPoiList.WmCustomerPoiListStatusRuleRunner.alarm(String,RawData,RawData[])");
        //通过大象公众号发送告警消息, 收件人为告警配置中的告警接收人。
        DXUtil.sendAlarm(s);
    }

    private Map<String, Object> toMap(String json) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.service.customer.monitor.customerPoiList.WmCustomerPoiListStatusRuleRunner.toMap(java.lang.String)");
        TypeToken<?> parameterized = TypeToken.getParameterized(Map.class, String.class, Object.class);
        return gson.fromJson(json, parameterized.getType());
    }
}
