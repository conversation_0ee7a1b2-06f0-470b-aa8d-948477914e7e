package com.sankuai.meituan.waimai.customer.service.sc.thirdworkplace.wrapper;

import com.sankuai.meituan.waimai.thrift.customer.constant.sc.WmScCodeConstants;
import com.sankuai.meituan.waimai.thrift.exception.WmSchCantException;
import org.apache.commons.lang3.StringUtils;
import com.dianping.lion.client.util.JsonUtils;
import com.meituan.waimai.common.utils.AppKeyUtils;
import com.meituan.waimai.common.utils.JacksonUtils;
import com.sankuai.djdata.readata.openapi.QueryContext;
import com.sankuai.djdata.readata.openapi.enums.BusiUnit;
import com.sankuai.djdata.readata.protocol.openapi.entity.OneServiceResponse;
import com.sankuai.djdata.readata.protocol.openapi.entity.OneServiceResult;

import com.sankuai.meituan.waimai.customer.config.MccConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.List;
import java.util.Map;


@Component
@Slf4j
public class OneServiceWrapper {

    @Autowired
    private OneServiceDataConverter oneServiceDataConverter;

    public OneServiceQueryResultBo queryDateFromOneService(QueryContext queryContext) throws WmSchCantException {
        // 初始化调用者
        initCaller(queryContext);
        try {
            log.info("queryDateFromOneService.queryContext:{}", JsonUtils.toJson(queryContext));
        } catch (IOException e) {
            log.error("queryDateFromOneService e:{}",e.getMessage());
            throw new WmSchCantException(WmScCodeConstants.SERVER_ERROR,"解析oneService查询请求失败");
        }

        OneServiceResponse response = queryContext.submit();

        return buildQueryResultByResponse(response);
    }

    public QueryContext initQueryContext() {
        return new QueryContext(24637204, "it_ka");
    }

    /**
     * 初始化调用者Caller
     *
     * @param queryContext
     */
    private void initCaller(QueryContext queryContext) {
        queryContext.setCaller(AppKeyUtils.getAppKey(),
                "学校食堂系统",
                MccConfig.getOneServiceToken());
        queryContext.setBusiUnit(BusiUnit.WAIMAI);
    }

    private OneServiceQueryResultBo buildQueryResultByResponse(OneServiceResponse response) {
        log.info("buildQueryResultByResponse OneServiceResponse={}", JacksonUtils.toJson(response));
        OneServiceResult oneServiceResult = response.getData();
        OneServiceQueryResultBo queryResult = null;
        if (response.code == 0 && oneServiceResult != null) {
            Map<String, List<String>> data = oneServiceResult.getData();
            List<Map<String, String>> resultData = oneServiceDataConverter.columnsToRows(data);
            queryResult = new OneServiceQueryResultBo(oneServiceResult.titleList, resultData,
                    oneServiceResult.queryId, oneServiceResult.queryPlan, oneServiceResult.stat, oneServiceResult.datasetMetadata);
        }
        log.info("buildQueryResultByResponse->oneServiceQueryResultBo={}", JacksonUtils.toJson(queryResult));
        return queryResult;
    }

    /**
     * 从OneService查询结果中提取总条数
     */
    public Long queryTotalCountFromOneService(QueryContext queryContext) {
        try {
            OneServiceQueryResultBo result = queryDateFromOneService(queryContext);
            return extractTotalCountFromResult(result);
        } catch (Exception e) {
            log.error("查询总条数失败", e);
            return 0L;
        }
    }

    /**
     * 从OneService查询结果中提取总条数
     */
    private Long extractTotalCountFromResult(OneServiceQueryResultBo result) {
        if (result == null || result.getDataList() == null || result.getDataList().isEmpty()) {
            log.warn("OneService查询结果为空");
            return 0L;
        }

        try {
            List<Map<String, String>> dataList = result.getDataList();
            // 对于count查询，通常只返回一行数据
            Map<String, String> firstRow = dataList.get(0);

            if (firstRow.containsKey("total") && StringUtils.isNotBlank(firstRow.get("total"))) {
                String totalValue = firstRow.get("total");
                return Long.parseLong(totalValue);
            }

            return 0L;
        } catch (Exception e) {
            log.error("提取总条数失败", e);
            return 0L;
        }
    }
}
