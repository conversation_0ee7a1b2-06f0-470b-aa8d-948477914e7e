package com.sankuai.meituan.waimai.customer.service.config;

import com.alibaba.fastjson.JSON;
import com.sankuai.meituan.waimai.customer.contract.config.WmFrameContractConfigService;
import com.sankuai.meituan.waimai.thrift.customer.domain.config.ConfigContractQueryRequestDTO;
import com.sankuai.meituan.waimai.thrift.customer.domain.config.ContractConfigInfo;
import com.sankuai.meituan.waimai.thrift.customer.domain.config.CustomerTypeInfoResponse;
import com.sankuai.meituan.waimai.thrift.customer.domain.config.QueryResponseDTO;
import com.sankuai.meituan.waimai.thrift.customer.service.WmCustomerFrameContractConfigThriftService;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

/**
 * @description:
 * @author: liuyunjie05
 * @create: 2024/5/19 15:52
 */
@Service
@Slf4j
public class WmCustomerFrameContractConfigThriftServiceImpl implements WmCustomerFrameContractConfigThriftService {

    @Resource
    private WmFrameContractConfigService wmFrameContractConfigService;

    @Override
    public List<CustomerTypeInfoResponse> getAllCustomerTypeInfo() {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_1", "com.sankuai.meituan.waimai.customer.service.config.WmCustomerFrameContractConfigThriftServiceImpl.getAllCustomerTypeInfo()");
        List<CustomerTypeInfoResponse> allCustomerTypeInfo = wmFrameContractConfigService.getAllCustomerTypeInfo();
        log.info("WmFrameContractConfigThriftServiceImpl#getAllCustomerTypeInfo, allCustomerTypeInfo: {}", JSON.toJSON(allCustomerTypeInfo));
        return allCustomerTypeInfo;
    }

    @Override
    public List<ContractConfigInfo> queryConfigFrameContract(ConfigContractQueryRequestDTO requestDTO) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.service.config.WmCustomerFrameContractConfigThriftServiceImpl.queryConfigFrameContract(ConfigContractQueryRequestDTO)");
        try {
            log.info("WmFrameContractConfigThriftServiceImpl#queryConfigFrameContract, requestDTO: {}", JSON.toJSONString(requestDTO));
            List<ContractConfigInfo> configInfoList = wmFrameContractConfigService.queryConfigFrameContract(requestDTO);
            log.info("WmFrameContractConfigThriftServiceImpl#queryConfigFrameContract, configInfoList: {}", JSON.toJSONString(configInfoList));
            return configInfoList;
        } catch (Exception e) {
            log.error("WmFrameContractConfigThriftServiceImpl#queryConfigFrameContract, error", e);
            return Collections.emptyList();
        }
    }

    @Override
    public List<ContractConfigInfo> allConfigFrameContract() {
        try {
            List<ContractConfigInfo> configInfoList = wmFrameContractConfigService.allConfigFrameContract();
            log.info("WmFrameContractConfigThriftServiceImpl#allConfigFrameContract, configInfoList: {}", JSON.toJSONString(configInfoList));
            return configInfoList;
        } catch (Exception e) {
            log.error("WmFrameContractConfigThriftServiceImpl#allConfigFrameContract, error", e);
            return Collections.emptyList();
        }
    }

    @Override
    public ContractConfigInfo queryContractConfigInfo(Integer contractId) throws WmCustomerException{
        log.info("WmFrameContractConfigThriftServiceImpl#queryContractConfigInfo, contractId: {}", contractId);
        ContractConfigInfo configInfo = wmFrameContractConfigService.queryContractConfigInfo(contractId);
        log.info("WmFrameContractConfigThriftServiceImpl#queryContractConfigInfo, configInfo: {}", JSON.toJSONString(configInfo));
        return configInfo;
    }
}
