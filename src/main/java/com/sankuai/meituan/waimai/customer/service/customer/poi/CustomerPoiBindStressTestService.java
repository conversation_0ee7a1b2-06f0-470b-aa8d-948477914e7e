package com.sankuai.meituan.waimai.customer.service.customer.poi;

import com.alibaba.fastjson.JSONObject;
import com.dianping.cat.Cat;
import com.dianping.cat.util.MetricHelper;
import com.dianping.lion.client.util.CollectionUtils;
import com.google.common.collect.Lists;
import com.sankuai.meituan.waimai.channel.beacon.lib.enums.CustomerModuleStateEnum;
import com.sankuai.meituan.waimai.customer.adapter.StateCenterAdapter;
import com.sankuai.meituan.waimai.customer.adapter.WmContractServiceAdaptor;
import com.sankuai.meituan.waimai.customer.adapter.WmPoiOplogThriftServiceAdaptor;
import com.sankuai.meituan.waimai.customer.constant.WmCustomerConstant;
import com.sankuai.meituan.waimai.customer.constant.customer.CustomerMetricEnum;
import com.sankuai.meituan.waimai.customer.constant.customer.CustomerPoiBindTypeEnum;
import com.sankuai.meituan.waimai.customer.constant.customer.WmCustomerPoiOplogSourceTypeEnum;
import com.sankuai.meituan.waimai.customer.contract.service.impl.WmContractPoiProduceService;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerDB;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerPoiOplogDB;
import com.sankuai.meituan.waimai.customer.domain.poi.WmCustomerPoiBindBo;
import com.sankuai.meituan.waimai.customer.service.customer.*;
import com.sankuai.meituan.waimai.customer.service.customer.task.CustomerTaskService;
import com.sankuai.meituan.waimai.thrift.customer.constant.ContractSignSubjectOpTagEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerConstants;
import com.sankuai.meituan.waimai.thrift.customer.domain.WmCustomerOplogBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.mq.CustomerMQEventEnum;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 20231213
 * @desc 客户门店绑定-压测专用服务
 */
@Service
@Slf4j
public class CustomerPoiBindStressTestService {

    @Autowired
    private WmCustomerService wmCustomerService;

    @Autowired
    private CustomerTaskService customerTaskService;

    @Autowired
    private WmCustomerPoiAttributeService wmCustomerPoiAttributeService;

    @Autowired
    private WmCustomerPoiOplogService wmCustomerPoiOplogService;

    @Autowired
    private WmContractPoiProduceService wmContractPoiProduceService;

    @Autowired
    private StateCenterAdapter stateCenterAdapter;

    @Autowired
    private WmPoiOplogThriftServiceAdaptor wmPoiOplogThriftServiceAdaptor;

    @Autowired
    private WmContractServiceAdaptor wmContractServiceAdaptor;

    @Autowired
    private WmCustomerPoiRelService wmCustomerPoiRelService;

    /**
     * 执行客户门店绑定-压测专用，不支持美食城客户类型，且无客户与结算标签一致判断->压测要求不限制
     *
     * @param wmCustomerPoiBindBo
     * @throws WmCustomerException
     */
    public void bindCustomerPoiOnStressTest(WmCustomerPoiBindBo wmCustomerPoiBindBo) throws WmCustomerException {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.customer.poi.CustomerPoiBindStressTestService.bindCustomerPoiOnStressTest(com.sankuai.meituan.waimai.customer.domain.poi.WmCustomerPoiBindBo)");
        log.info("bindCustomerPoiOnStressTest wmCustomerPoiBindBo={}", JSONObject.toJSONString(wmCustomerPoiBindBo));
        WmCustomerDB wmCustomerDB = wmCustomerPoiBindBo.getWmCustomerDB();
        Set<Long> wmPoiIdSet = wmCustomerPoiBindBo.getWmPoiIdSet();
        Integer opUid = wmCustomerPoiBindBo.getOpUid();
        String opName = wmCustomerPoiBindBo.getOpName();
        WmCustomerPoiOplogSourceTypeEnum oplogSourceTypeEnum = wmCustomerPoiBindBo.getOplogSourceTypeEnum();
        String status = WmCustomerConstant.SUCCESS;
        String reason = "";
        try {
            int customerId = wmCustomerDB.getId();
            // 获取客户任务
            Map<Long, Integer> poiAndTaskMaps = wmCustomerPoiBindBo.getPoiAndTaskMaps();
            // 批量绑定关系
            if (CollectionUtils.isEmpty(poiAndTaskMaps)) {
                wmCustomerPoiRelService.batchInsertCustomerPoi(customerId, wmPoiIdSet);
            } else {
                wmCustomerPoiRelService.batchInsertCustomerPoiWithBizTaskId(customerId, wmPoiIdSet, poiAndTaskMaps);
            }
            // 批量添加客户门店属性
            wmCustomerPoiAttributeService.upsertCustomerPoiAttribute(customerId, wmPoiIdSet);
            // 变更门店主体
            wmContractServiceAdaptor.batchChangePoiSubject(customerId, wmPoiIdSet, ContractSignSubjectOpTagEnum.CUSTOMER_BIND.getCode());

            // 更新任务状态-已完成
            if (!CollectionUtils.isEmpty(poiAndTaskMaps)) {
                customerTaskService.updateTaskStatus(customerId, wmPoiIdSet, Lists.newArrayList(poiAndTaskMaps.values()));
            }

            // 批量插入客户门店绑定操作日志
            wmCustomerPoiOplogService.batchBindOplog(new WmCustomerPoiOplogDB(customerId, oplogSourceTypeEnum.getCode(), opUid, opName),
                    Lists.newArrayList(wmPoiIdSet));
            // 插入客户日志
            wmCustomerService.insertCustomerOpLog(customerId, opUid, opName, WmCustomerOplogBo.OpType.INSERT,
                    String.format(CustomerConstants.CUSTOMER_LOG_TEMPLATE_POI_BIND,
                            StringUtils.join(wmPoiIdSet, CustomerConstants.SPLIT_SYMBOL)),
                    wmCustomerPoiBindBo.getRemark());
            for (Long wmPoiId : wmPoiIdSet) {
                wmPoiOplogThriftServiceAdaptor.insertPoiOpLog(wmPoiId, String.format(CustomerConstants.POI_LOG_CHANGE_CUSTOMER, "", wmCustomerDB.getMtCustomerId() + wmCustomerDB.getCustomerName()),
                        opUid, opName, "", String.valueOf(wmCustomerDB.getMtCustomerId()));
            }
            // 插入上单状态机通知，门店维度日志
            wmContractPoiProduceService.logPoiProduce(customerId, Lists.newArrayList(wmPoiIdSet), opUid, opName);
            stateCenterAdapter.batchSyncCustomerState(wmPoiIdSet, CustomerModuleStateEnum.findByCode(wmCustomerDB.getAuditStatus()), opUid, opName);
            // 发送MQ
            wmCustomerService.sendCustomerStatusNoticeMQ(customerId, CustomerMQEventEnum.CUSTOMER_BIND_POI,
                    wmPoiIdSet, opUid, opName);

        } catch (WmCustomerException e) {
            status = WmCustomerConstant.BUSINESS_EXCEPTION;
            reason = e.getMsg();
            Cat.logError(String.format("%s error errMsg=%s", CustomerMetricEnum.CUSTOMER_POI_BIND.getName(), e.getMsg()), e);
            throw e;
        } catch (Exception e) {
            status = WmCustomerConstant.SYSTEM_EXCEPTION;
            reason = WmCustomerConstant.SYSTEM_EXCEPTION_MSG;
            Cat.logError(String.format("%s error", CustomerMetricEnum.CUSTOMER_POI_BIND.getName()), e);
            throw e;
        } finally {
            Cat.logEvent(CustomerMetricEnum.CUSTOMER_POI_BIND.getName(), oplogSourceTypeEnum.getDesc(), status, reason);
            MetricHelper.build().name(CustomerMetricEnum.CUSTOMER_POI_BIND.getName()).tag(CustomerMetricEnum.CUSTOMER_POI_BIND.getTag(), oplogSourceTypeEnum.getDesc())
                    .tag(CustomerMetricEnum.CUSTOMER_POI_BIND.getStatus(), status).count(wmPoiIdSet.size());
        }
    }
}
