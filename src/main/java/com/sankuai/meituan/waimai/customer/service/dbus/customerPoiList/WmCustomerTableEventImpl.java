package com.sankuai.meituan.waimai.customer.service.dbus.customerPoiList;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import com.meituan.dbus.common.DbusUtils;
import com.meituan.dbus.thriftV2.utils.ThriftV2Utils;
import com.sankuai.meituan.waimai.customer.constant.WmCustomerConstant;
import com.sankuai.meituan.waimai.customer.constant.WmCustomerPoiListESFields;
import com.sankuai.meituan.waimai.customer.constant.YesOrNoEnum;
import com.sankuai.meituan.waimai.customer.constant.customer.WmCustomerRelTableDbusEnum;
import com.sankuai.meituan.waimai.customer.constant.sc.WmScTableColumnConstants;
import com.sankuai.meituan.waimai.customer.dao.WmCustomerPoiDBMapper;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerDB;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerPoiDB;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerPoiQueryCondtionVo;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerPoiListEsService;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.ValidEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.WmScUpdateTypeEnum;
import com.taobao.tair3.client.json.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.elasticsearch.action.bulk.BulkResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * wm_customer表变更
 */
@Slf4j
@Service
public class WmCustomerTableEventImpl implements ICustomerRelTableEvent {

    @Autowired
    private WmCustomerPoiDBMapper wmCustomerPoiDBMapper;

    @Autowired
    private WmCustomerPoiListEsService esService;

    @Override
    public WmCustomerRelTableDbusEnum getTable() {
        return WmCustomerRelTableDbusEnum.TABLE_WM_CUSTOMER;
    }

    @Override
    public String handleUpdate(Map<String, String> metaJsonData, String dataMapJson, String diffJson) {
        DbusUtils utils = ThriftV2Utils.newThriftV2InstanceForUpdate(metaJsonData, dataMapJson, diffJson);
        WmCustomerDB bean = transToBo(utils.getAftMap());

        if (bean == null) {
            return null;
        }
        boolean isUpdate = checkUpdate(utils.getDiffMap());
        if (!isUpdate) {
            return null;
        }

        List<WmCustomerPoiDB> list = getWmCustomerPoi(bean.getId());
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        Map<Object, Map<String, Object>> map = Maps.newHashMap();

        for (WmCustomerPoiDB db : list) {
            log.info("db={},mtCustomerId={}", JSON.toJSONString(db), bean.getMtCustomerId());
            map.put(db.getId(), esService.makeMap(new String[]{WmCustomerPoiListESFields.MT_CUSTOMER_ID.getField()}, new Object[]{bean.getMtCustomerId()}));
        }
        BulkResponse response = esService.bulkUpdate(map);
        if (response == null) {
            return "kp信息修改失败";
        } else {
            return null;
        }
    }

    @Override
    public String handleInsert(Map<String, String> metaJsonData, String dataMapJson) {
        return null;
    }

    @Override
    public String handleDelete(Map<String, String> metaJsonData, String dataMapJson) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.service.dbus.customerPoiList.WmCustomerTableEventImpl.handleDelete(java.util.Map,java.lang.String)");
        return null;
    }


    private boolean checkUpdate(Map<String, Object> diffMap) {
        if (MapUtils.isEmpty(diffMap) || !diffMap.containsKey(WmCustomerPoiListESFields.MT_CUSTOMER_ID.getDbField())) {
            return false;
        }
        return true;
    }

    private List<WmCustomerPoiDB> getWmCustomerPoi(Integer customerId) {
        WmCustomerPoiQueryCondtionVo vo = new WmCustomerPoiQueryCondtionVo();
        vo.setCustomerId(customerId);
        return wmCustomerPoiDBMapper.selectCustomerPoiRelByCondition(vo);
    }

    private WmCustomerDB transToBo(Map<String, Object> aftMap) {
        String jsonString = JSON.toJSONString(aftMap);
        return JSON.parseObject(jsonString, WmCustomerDB.class);
    }
}
