package com.sankuai.meituan.waimai.customer.service.sc;

import cn.hutool.core.collection.CollectionUtil;
import com.meituan.inf.xmdlog.StringUtils;
import com.sankuai.meituan.waimai.customer.util.AreaCalculatorUtils;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.*;
import com.sankuai.meituan.waimai.thrift.customer.domain.sc.*;
import com.sankuai.meituan.waimai.thrift.exception.WmSchCantException;
import com.vividsolutions.jts.geom.Polygon;
import com.vividsolutions.jts.operation.valid.IsValidOp;
import com.vividsolutions.jts.operation.valid.TopologyValidationError;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

import static com.sankuai.meituan.waimai.thrift.customer.constant.sc.WmScCodeConstants.BIZ_PARA_ERROR;

/**
 * 校验学校相关范围是否合法
 */
@Slf4j
@Service
public class CheckAreaInfoService {

    /**
     * 校验多个范围间是否存在交集
     * @param areaList 范围列表
     * @return true: 存在交集 / false: 不存在交集
     */
    public boolean checkAreaListIntersect(List<String> areaList) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.sc.CheckAreaInfoService.checkAreaListIntersect(java.util.List)");
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(areaList)) {
            return false;
        }

        for (int i = 0; i < areaList.size(); i ++) {
            for (int j = i + 1; j < areaList.size(); j ++) {
                if (intersects(areaList.get(i), areaList.get(j))) {
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * 校验宿舍楼的楼层、人数和电梯为必填项
     * @param wmScSchoolBuildingDetailDTO wmScSchoolBuildingDetailDTO
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    public void checkSchoolDormitoryBasicInfo(WmScSchoolBuildingDetailDTO wmScSchoolBuildingDetailDTO) throws WmSchCantException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_2", "com.sankuai.meituan.waimai.customer.service.sc.CheckAreaInfoService.checkSchoolDormitoryBasicInfo(com.sankuai.meituan.waimai.thrift.customer.domain.sc.WmScSchoolBuildingDetailDTO)");
        if (wmScSchoolBuildingDetailDTO.getBuildingFloor() == null || wmScSchoolBuildingDetailDTO.getBuildingFloor() < 0) {
            throw new WmSchCantException(BIZ_PARA_ERROR, "宿舍楼楼层为必填项");
        }

        if (wmScSchoolBuildingDetailDTO.getBuildingPersonNum() == null) {
            throw new WmSchCantException(BIZ_PARA_ERROR, "宿舍楼人数为必填项");
        }

        if (wmScSchoolBuildingDetailDTO.getBuildingPersonNum() < 0 || wmScSchoolBuildingDetailDTO.getBuildingPersonNum() > 20000) {
            throw new WmSchCantException(BIZ_PARA_ERROR, "宿舍楼人数范围为0～20000");
        }

        if (wmScSchoolBuildingDetailDTO.getBuildingElevator() == null
                || SchoolBuildingElevatorEnum.getByType(wmScSchoolBuildingDetailDTO.getBuildingElevator()) == null) {
            throw new WmSchCantException(BIZ_PARA_ERROR, "宿舍楼电梯为必填项");
        }
    }

    /**
     * 保存学校范围信息校验
     * @param schoolPrimaryId 学校主键ID
     * @param checkAreaInfoBoList checkAreaInfoBos列表
     * @return SchoolCheckResult
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    public SchoolCheckResult checkSchoolAoiInfo(int schoolPrimaryId, List<CheckAreaInfoBo> checkAreaInfoBoList) throws WmSchCantException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_2", "com.sankuai.meituan.waimai.customer.service.sc.CheckAreaInfoService.checkSchoolAoiInfo(int,java.util.List)");
        log.info("[CheckAreaInfoService.checkSchoolAoiInfo] input param: schoolPrimaryId = {}, checkAreaInfoBoList = {}",
                schoolPrimaryId, com.alibaba.fastjson.JSONObject.toJSONString(checkAreaInfoBoList));
        SchoolCheckResult schoolCheckResult = new SchoolCheckResult();
        if (CollectionUtil.isEmpty(checkAreaInfoBoList)) {
            schoolCheckResult.setMsgs("请输入需要校验的范围");
            return schoolCheckResult;
        }

        if (!checkLegalArea(checkAreaInfoBoList)) {
            schoolCheckResult.setMsgs("学校范围不允许出现交叉");
            return schoolCheckResult;
        }

        List<String> schoolAreaList = new ArrayList<>();
        for (CheckAreaInfoBo checkAreaInfoBo : checkAreaInfoBoList) {
            if (org.apache.commons.lang3.StringUtils.isNotBlank(checkAreaInfoBo.getArea())) {
                schoolAreaList.add(checkAreaInfoBo.getArea());
            }
        }
        // 判断学校范围是否存在交集
        if (checkAreaListIntersect(schoolAreaList)) {
            throw new WmSchCantException(BIZ_PARA_ERROR, "学校范围存在交集");
        }

        if (StringUtils.isEmpty(schoolCheckResult.getMsgs())) {
            schoolCheckResult.setCode(1);
        }
        log.info("[CheckAreaInfoService.checkSchoolAoiInfo] schoolCheckResult = {}", com.alibaba.fastjson.JSONObject.toJSONString(schoolCheckResult));
        return schoolCheckResult;
    }

    /**
     * 判断范围2与范围2是否有交集
     *
     * @param area1
     * @param area2
     * @return
     */
    public boolean intersects(String area1, String area2) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.service.sc.CheckAreaInfoService.intersects(java.lang.String,java.lang.String)");
        Polygon polygon1 = AreaCalculatorUtils.createPolygon(area1);
        Polygon polygon2 = AreaCalculatorUtils.createPolygon(area2);
        return polygon1.intersects(polygon2);
    }

    /**
     * 判断范围area是否在范围列表中
     * @param areaList 范围列表
     * @param area2 范围
     * @return true: 在 / false: 不在
     */
    public boolean areaContains(List<String> areaList, String area2) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.service.sc.CheckAreaInfoService.areaContains(java.util.List,java.lang.String)");
        Polygon polygon2 = AreaCalculatorUtils.createPolygon(area2);
        for (String area1 : areaList) {
            Polygon polygon1 = AreaCalculatorUtils.createPolygon(area1);
            if (polygon1 != null && polygon1.contains(polygon2)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 判断需要验证的地图是否为多边形
     *
     * @param checkAreaInfoBos checkAreaInfoBos
     * @return
     */
    private boolean checkLegalArea(List<CheckAreaInfoBo> checkAreaInfoBos) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.sc.CheckAreaInfoService.checkLegalArea(java.util.List)");
        for (CheckAreaInfoBo checkAreaInfoBo : checkAreaInfoBos) {
            if (!StringUtils.isEmpty(checkAreaInfoBo.getArea()) && !"[]".equals(checkAreaInfoBo.getArea())) {
                // 多边形
                Polygon polygon = AreaCalculatorUtils.createPolygon(checkAreaInfoBo.getArea());
                if (polygon == null) {
                    return false;
                }
                if(!getPolygonByInteriorPoint(polygon)){
                    return false;
                }
            }
        }
        return true;
    }

    /**
     * 校验单个范围是否出现交叉
     * @param area 范围
     * @return true: 出现交叉 / false: 没有交叉
     */
    public boolean checkAreaIsIntersect(String area) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.service.sc.CheckAreaInfoService.checkAreaIsIntersect(java.lang.String)");
        if (StringUtils.isEmpty(area) || "[]".equals(area)) {
            return true;
        }
        Polygon polygon = AreaCalculatorUtils.createPolygon(area);
        if (polygon == null) {
            return false;
        }

        if(!getPolygonByInteriorPoint(polygon)){
            return false;
        }
        return true;
    }

    /**
     * 使用jts的两线相交方法解决线的自相交问题
     * @param geometry
     * @return
     */
    private static boolean getPolygonByInteriorPoint(Polygon geometry) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.service.sc.CheckAreaInfoService.getPolygonByInteriorPoint(com.vividsolutions.jts.geom.Polygon)");
        IsValidOp isValidOp = new IsValidOp(geometry);
        TopologyValidationError validationError = isValidOp.getValidationError();
        // 如果为空，说明没有自相交的线
        if (validationError == null) {
            return true;
        }
        return false;
    }
}
