package com.sankuai.meituan.waimai.customer.contract.global;

import com.alibaba.fastjson.JSON;
import com.dianping.cat.util.StringUtils;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.meituan.mtrace.Tracer;
import com.meituan.mtrace.thread.pool.ExecutorServiceTraceWrapper;
import com.sankuai.meituan.mtcoop.thrift.dto.TFrame;
import com.sankuai.meituan.mtcoop.thrift.dto.TFrameCoop;
import com.sankuai.meituan.mtcoop.thrift.exception.TCoopException;
import com.sankuai.meituan.mtcoop.thrift.exception.TIllegalArgumentException;
import com.sankuai.meituan.waimai.customer.adapter.WmEmployClient;
import com.sankuai.meituan.waimai.customer.adapter.agent.CustomerIdMappingServiceAdapter;
import com.sankuai.meituan.waimai.customer.adapter.daocan.FrameCoopServiceAdapter;
import com.sankuai.meituan.waimai.customer.config.MccConfig;
import com.sankuai.meituan.waimai.customer.constant.customer.MetricConstant;
import com.sankuai.meituan.waimai.customer.contract.config.WmFrameContractConfigService;
import com.sankuai.meituan.waimai.customer.contract.frame.util.DcContractStatusMapUtil;
import com.sankuai.meituan.waimai.customer.contract.global.authority.WmGlobalEcontractAuthorityService;
import com.sankuai.meituan.waimai.customer.contract.global.dto.GlobalEcontractAuthorityCondition;
import com.sankuai.meituan.waimai.customer.contract.global.dto.GlobalEcontractQueryCondition;
import com.sankuai.meituan.waimai.customer.ddd.adapter.MtCustomerThriftServiceAdapter;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerDB;
import com.sankuai.meituan.waimai.customer.domain.blade.WmBladeGlobalContractInfoWidePo;
import com.sankuai.meituan.waimai.customer.service.common.MtriceService;
import com.sankuai.meituan.waimai.customer.util.Preconditions;
import com.sankuai.meituan.waimai.customer.util.SSOUtil;
import com.sankuai.meituan.waimai.econtrct.client.constants.config.FrameContractConfigStatusEnum;
import com.sankuai.meituan.waimai.infra.domain.WmEmploy;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerContractStatus;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerErrorCodeConstants;
import com.sankuai.meituan.waimai.thrift.customer.constant.global.GlobalContractCategoryEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.global.GlobalContractEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.global.GlobalContractStatusEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.config.ContractConfigInfo;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.econtractsign.dto.PageInfoDTO;
import com.sankuai.meituan.waimai.thrift.customer.domain.global.*;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import com.sankuai.meituan.waimai.util.DaxiangUtil;
import com.sankuai.nibcus.inf.customer.client.enums.BusinessLineEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.thrift.TException;
import org.elasticsearch.common.Strings;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.lang.reflect.Field;
import java.util.*;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;

/**
 * @description:
 * @author: liuyunjie05
 * @create: 2024/1/10 16:31
 */
@Service
@Slf4j
public class WmGlobalEcontractQueryService {

    @Resource
    private WmBladeGlobalContractInfoWideService wmBladeGlobalContractInfoWideService;

    @Resource
    private MtriceService mtriceService;

    @Resource
    private WmGlobalEcontractOperationService wmGlobalEcontractOperationService;

    @Resource
    private WmEmployClient wmEmployClient;

    @Resource
    private List<WmGlobalEcontractAuthorityService> wmGlobalEcontractAuthorityServiceList;

    @Resource
    private WmFrameContractConfigService wmFrameContractConfigService;

    @Resource
    private CustomerIdMappingServiceAdapter customerIdMappingServiceAdapter;

    @Resource
    private FrameCoopServiceAdapter frameCoopServiceAdapter;

    @Resource
    private MtCustomerThriftServiceAdapter mtCustomerThriftServiceAdapter;

    @PostConstruct
    private void init() {
        wmGlobalEcontractAuthorityServiceList.sort(Comparator.comparingInt(WmGlobalEcontractAuthorityService::priority));
    }

    private static final Integer DEFAULT_PAGE_NUMBER = 1;

    private static final ExecutorServiceTraceWrapper exportGlobalEcontractInfoService = new ExecutorServiceTraceWrapper(
            new ThreadPoolExecutor(10,
                    10,
                    0,
                    TimeUnit.MILLISECONDS,
                    new ArrayBlockingQueue<>(1000),
                    new ThreadFactoryBuilder().setNameFormat("export-global-econtract").build(),
                    new ThreadPoolExecutor.DiscardPolicy())
    );

    private static final Map<Integer, Integer> contractStatus2GlobalContractStatusMap = new HashMap<>();

    static {
        contractStatus2GlobalContractStatusMap.put(CustomerContractStatus.SIGNING.getCode(), GlobalContractStatusEnum.IN_PROCESSING.getCode());
        contractStatus2GlobalContractStatusMap.put(CustomerContractStatus.EFFECT.getCode(), GlobalContractStatusEnum.EFFECT.getCode());
        contractStatus2GlobalContractStatusMap.put(CustomerContractStatus.WAITING_SIGN.getCode(), GlobalContractStatusEnum.IN_PROCESSING.getCode());
        contractStatus2GlobalContractStatusMap.put(CustomerContractStatus.SIGN_FAIL.getCode(), GlobalContractStatusEnum.SIGN_FAIL.getCode());
    }

    private static final String ASYNC_EXPORT_GLOBAL_ECONTRACT_SUCCESS_MSG = "合同台账导出完成，请点击下载%s。温馨提示：此次导出行为已被系统记录，合同数据为公司机密数据，请勿用作非法用途。";
    private static final String ASYNC_EXPORT_GLOBAL_ECONTRACT_ERROR_MSG = "系统异常，合同台账导出失败，请联系管理员。traceID: ";

    private static final String NO_AUTHORITY_EXPORT_ECONTRACT_MSG = "你没有数据权限，无法查询合同";

    private static final String NO_EXPORT_DATA_MSG = "未查询到任何合同，无法导出台账";

    private static final String DEFAULT_STRING_VALUE = "";

    private static final String JAVA_COCO_FIELD = "$jacocoData";

    public Boolean exportGlobalContractInfo(GlobalContractBaseParam exportParam, SSOUtil.SsoUser user) throws WmCustomerException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_2", "com.sankuai.meituan.waimai.customer.contract.global.WmGlobalEcontractQueryService.exportGlobalContractInfo(GlobalContractBaseParam,SSOUtil$SsoUser)");
        checkExportParam(exportParam);
        tagGlobalParam(MetricConstant.GLOBAL_EXPORT_PARAM_TAG, exportParam);
        GlobalEcontractQueryCondition queryCondition = constructQueryCondition(exportParam, null);
        GlobalEcontractAuthorityCondition authorityCondition = constructAuthorityCondition(String.valueOf(user.getId()));

        completeAuthorityQueryCondition(authorityCondition, queryCondition);
        Long totalNumber = getTotalNumber(queryCondition);
        return calculateExportResult(totalNumber, getUserMisId((int) user.getId()), queryCondition);
    }

    private boolean calculateExportResult(Long totalNumber, String userMisId, GlobalEcontractQueryCondition queryCondition) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_2", "com.sankuai.meituan.waimai.customer.contract.global.WmGlobalEcontractQueryService.calculateExportResult(Long,String,GlobalEcontractQueryCondition)");
        if (totalNumber <= 0) {
            noticeOperatorResult(NO_AUTHORITY_EXPORT_ECONTRACT_MSG, Collections.singletonList(userMisId));
            return true;
        }
        if (totalNumber > MccConfig.getExportGlobalEcontractNumberUpper()) {
            return false;
        }
        exportGlobalEcontractInfoService.submit(() -> asyncExportGlobalContractInfo(queryCondition, userMisId));
        return true;
    }


    private String getUserMisId(Integer userId) throws WmCustomerException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_2", "com.sankuai.meituan.waimai.customer.contract.global.WmGlobalEcontractQueryService.getUserMisId(java.lang.Integer)");
        WmEmploy employById = wmEmployClient.getEmployById(userId);
        if (employById == null) {
            log.warn("WmGlobalEcontractQueryService#getUserMisId, user: {}, error", userId);
            throw new WmCustomerException(-1, "查询mis号异常");
        }
        return employById.getMisId();
    }

    private void asyncExportGlobalContractInfo(GlobalEcontractQueryCondition queryCondition, String userMisId) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.contract.global.WmGlobalEcontractQueryService.asyncExportGlobalContractInfo(GlobalEcontractQueryCondition,String)");
        log.info("WmGlobalEcontractQueryService#asyncExportGlobalContractInfo, queryCondition: {}, userMisId: {}", JSON.toJSONString(queryCondition), userMisId);
        List<GlobalContractInfo> globalContractInfos = null;
        try {
            if (isDcC1Contract(queryCondition)) {
                globalContractInfos = queryDcC1Contract(queryCondition);
            } else {
                globalContractInfos = query(queryCondition);
            }
        } catch (WmCustomerException e) {
            log.warn("asyncExportGlobalContractInfo query失败",e.getMessage());
            return;
        }
        if (CollectionUtils.isEmpty(globalContractInfos)) {
            noticeOperatorResult(NO_EXPORT_DATA_MSG, Collections.singletonList(userMisId));
        }

        String excelUrl = wmGlobalEcontractOperationService.exportAndUploadExcel(globalContractInfos);
        if (Strings.isEmpty(excelUrl)) {
            noticeOperatorResult(ASYNC_EXPORT_GLOBAL_ECONTRACT_ERROR_MSG + Tracer.id(), Collections.singletonList(userMisId));
        } else {
            String finalMsg = String.format(ASYNC_EXPORT_GLOBAL_ECONTRACT_SUCCESS_MSG, excelUrl);
            noticeOperatorResult(finalMsg, Collections.singletonList(userMisId));
        }
    }


    private void noticeOperatorResult(String msg, List<String> misIdList) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_1", "com.sankuai.meituan.waimai.customer.contract.global.WmGlobalEcontractQueryService.noticeOperatorResult(java.lang.String,java.util.List)");
        DaxiangUtil.push("<EMAIL>", msg, misIdList);
    }

    public GlobalContractResponse queryGlobalContractInfo(GlobalContractSearchParam searchParam) throws WmCustomerException {
        log.info("queryGlobalContractInfo searchParam:{}",JSON.toJSON(searchParam));
        fillDefaultParam(searchParam);
        checkQueryParam(searchParam);
        tagGlobalParam(MetricConstant.GLOBAL_SEARCH_PARAM_TAG, searchParam.getBaseParam());

        GlobalEcontractQueryCondition queryCondition = constructQueryCondition(searchParam.getBaseParam(), searchParam.getPageInfoParam());
        Integer uid = getUserId(searchParam.getBaseParam());
        GlobalEcontractAuthorityCondition authorityCondition = constructAuthorityCondition(String.valueOf(uid));

        completeAuthorityQueryCondition(authorityCondition, queryCondition);

        if (isDcC1Contract(searchParam)) {
            List<GlobalContractInfo> globalInfoList = queryDcC1Contract(queryCondition);
            Long totalNumber = (long) globalInfoList.size();
            return constructContractResponse(globalInfoList, constructPageInfoDTO(searchParam.getPageInfoParam(), totalNumber));
        } else {
            Long totalNumber = getTotalNumber(queryCondition);
            List<GlobalContractInfo> globalInfoList = new ArrayList<>();
            if (totalNumber > 0) {
                globalInfoList = query(queryCondition);
            }
            return constructContractResponse(globalInfoList, constructPageInfoDTO(searchParam.getPageInfoParam(), totalNumber));
        }
    }

    private Boolean isDcC1Contract(GlobalContractSearchParam searchParam) {
        GlobalContractBaseParam baseParam = searchParam.getBaseParam();
        Integer contractType = baseParam.getContractType();
        return contractType != null && contractType.equals(GlobalContractEnum.DAOCAN_SERVICE_C1_CONTRACT.getContractTypeCode());
    }

    private Boolean isDcC1Contract(GlobalEcontractQueryCondition queryCondition) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.contract.global.WmGlobalEcontractQueryService.isDcC1Contract(com.sankuai.meituan.waimai.customer.contract.global.dto.GlobalEcontractQueryCondition)");
        Integer contractType = queryCondition.getContractType();
        return contractType != null && contractType.equals(GlobalContractEnum.DAOCAN_SERVICE_C1_CONTRACT.getContractTypeCode());
    }

    private Integer getUserId(GlobalContractBaseParam baseParam) {
        if (baseParam.getOpId() == null) {
            return Math.toIntExact(SSOUtil.getUser().getId());
        } else {
            return baseParam.getOpId();
        }
    }

    private void completeAuthorityQueryCondition(GlobalEcontractAuthorityCondition authorityCondition, GlobalEcontractQueryCondition queryCondition) {
        queryCondition.setSuperAdminAuthority(authorityCondition.isSuperAdmin());
        queryCondition.setBizLineListAuthority(authorityCondition.getBizLineList());
        queryCondition.setContractTypeCodeListAuthority(authorityCondition.getContractTypeCodeList());
        queryCondition.setQueryUid(authorityCondition.getQueryUid());
    }

    private GlobalContractResponse constructContractResponse(List<GlobalContractInfo> globalInfoList, PageInfoDTO pageInfoDTO) {
        GlobalContractResponse contractResponse = new GlobalContractResponse();
        contractResponse.setGlobalInfoList(globalInfoList);
        contractResponse.setPageInfoDTO(pageInfoDTO);
        return contractResponse;
    }

    private void tagGlobalParam(String metricName, GlobalContractBaseParam baseParam) {
        try {
            for (Field field : baseParam.getClass().getDeclaredFields()) {
                if (JAVA_COCO_FIELD.equals(field.getName())) {
                    continue;
                }
                field.setAccessible(true);
                if (field.get(baseParam) != null) {
                    mtriceService.metricGlobalParam(metricName, field.getName());
                }
            }
        } catch (Exception e) {
            log.warn("WmGlobalEcontractQueryService#tagSearchParam, error", e);
        }
    }

    private void checkQueryParam(GlobalContractSearchParam searchParam) throws WmCustomerException {
        Preconditions.checkArgument(searchParam != null, -1, "查询参数异常");
        Preconditions.checkArgument(searchParam.getBaseParam() != null, -1, "查询参数异常");
        Preconditions.checkArgument(searchParam.getPageInfoParam().getPageNumber() > 0, -1, "页码小于0");
        Preconditions.checkArgument(searchParam.getPageInfoParam().getPageSize() > 0, -1, "页大小小于0");

        GlobalContractBaseParam baseParam = searchParam.getBaseParam();
        if (baseParam.getTemplateId() == null && baseParam.getTemplateVersion() != null) {
            throw new WmCustomerException(-1, "不支持只根据版本号查询合同");
        }
        Integer contractType = baseParam.getContractType();
        if (Objects.nonNull(contractType) && contractType.equals(GlobalContractEnum.DAOCAN_SERVICE_C1_CONTRACT.getContractTypeCode())) {
            checkDcC1Param(baseParam);
        }
    }

    private void checkExportParam(GlobalContractBaseParam baseParam) throws WmCustomerException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_2", "com.sankuai.meituan.waimai.customer.contract.global.WmGlobalEcontractQueryService.checkExportParam(com.sankuai.meituan.waimai.thrift.customer.domain.global.GlobalContractBaseParam)");
        Preconditions.checkArgument(baseParam != null, -1, "查询参数异常");
        if (baseParam.getTemplateId() == null && baseParam.getTemplateVersion() != null) {
            throw new WmCustomerException(-1, "不支持只根据版本号查询合同");
        }

        Integer contractType = baseParam.getContractType();
        if (Objects.nonNull(contractType) && contractType.equals(GlobalContractEnum.DAOCAN_SERVICE_C1_CONTRACT.getContractTypeCode())) {
            checkDcC1Param(baseParam);
        }
    }

    /**
     * 到餐C1合同查询条件校验
     * @param baseParam
     * @throws WmCustomerException
     */
    private void checkDcC1Param(GlobalContractBaseParam baseParam) throws WmCustomerException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_2", "com.sankuai.meituan.waimai.customer.contract.global.WmGlobalEcontractQueryService.checkDcC1Param(com.sankuai.meituan.waimai.thrift.customer.domain.global.GlobalContractBaseParam)");
        Map<Function<GlobalContractBaseParam, Boolean>, String> unsupportedConditions = new LinkedHashMap<>();
        unsupportedConditions.put(param -> param.getCustomerId() == null, "请输入客户ID进行查询");
        unsupportedConditions.put(param -> param.getGlobalContractId() != null && !param.getGlobalContractId().isEmpty(), "该合同不支持根据全局合同ID查询");
        unsupportedConditions.put(param -> param.getRecordKey() != null && !param.getRecordKey().isEmpty(), "该合同不支持根据recordKey查询");
        unsupportedConditions.put(param -> param.getSignBatchId() != null && !param.getSignBatchId().isEmpty(), "该合同不支持根据批次ID查询");
        unsupportedConditions.put(param -> param.getWmPoiId() != null && !param.getWmPoiId().isEmpty(), "该合同不支持根据门店ID查询");
        unsupportedConditions.put(param -> param.getTemplateId() != null || param.getTemplateVersion() != null, "该合同不支持根据版本号查询");
        unsupportedConditions.put(param -> param.getPhoneNumber() != null && !param.getPhoneNumber().isEmpty(), "该合同不支持根据签约手机号查询");
        unsupportedConditions.put(param -> param.getSignerName() != null && !param.getSignerName().isEmpty(), "该合同不支持根据签约人查询");
        unsupportedConditions.put(param -> (param.getSignTime1() != null && param.getSignTime1() > 0) || (param.getSignTime2() != null && param.getSignTime2() > 0), "该合同不支持根据签约时间查询");
        unsupportedConditions.put(param -> param.getCommitUid() != null && param.getCommitUid() > 0, "该合同不支持根据创建人查询");
        unsupportedConditions.put(param -> (param.getFrameContractId() != null && !param.getFrameContractId().isEmpty()) || (param.getFrameContractVersion() != null && !param.getFrameContractVersion().isEmpty()), "该合同不支持根据框架合同版本查询");
        unsupportedConditions.put(param -> param.getPartAName() != null && !param.getPartAName().isEmpty(), "该合同不支持根据甲方名称查询");
        unsupportedConditions.put(param -> param.getPartBName() != null && !param.getPartBName().isEmpty(), "该合同不支持根据乙方名称查询");
        unsupportedConditions.put(param -> (param.getDueTime1() != null && param.getDueTime1() > 0) || (param.getDueTime2() != null && param.getDueTime2() > 0), "该合同不支持根据到期时间查询");

        // 遍历不支持的查询条件，如果满足则抛出异常
        for (Map.Entry<Function<GlobalContractBaseParam, Boolean>, String> entry : unsupportedConditions.entrySet()) {
            if (entry.getKey().apply(baseParam)) {
                throw new WmCustomerException(-1, entry.getValue());
            }
        }
    }

    private void fillDefaultParam(GlobalContractSearchParam searchParam) {
        PageInfoSearchParam pageInfoParam = searchParam.getPageInfoParam();
        if (pageInfoParam == null) {
            pageInfoParam = new PageInfoSearchParam();
            searchParam.setPageInfoParam(pageInfoParam);
        }
        if (pageInfoParam.getPageNumber() == null) {
            pageInfoParam.setPageNumber(DEFAULT_PAGE_NUMBER);
        }
        if (pageInfoParam.getPageSize() == null) {
            pageInfoParam.setPageSize(MccConfig.getGlobalDefaultPageSize());
        }
    }

    public List<GlobalContractInfo> query(GlobalEcontractQueryCondition queryCondition) throws WmCustomerException {
        List<WmBladeGlobalContractInfoWidePo> globalContractInfoWidePos;
        if (queryCondition.isSuperAdminAuthority()) {
            globalContractInfoWidePos = wmBladeGlobalContractInfoWideService.queryWithAllAuthority(queryCondition);
        } else {
            globalContractInfoWidePos = wmBladeGlobalContractInfoWideService.queryWithLimitAuthority(queryCondition);
        }
        List<GlobalContractInfo> globalContractInfoList = new ArrayList<>();
        for (WmBladeGlobalContractInfoWidePo globalContractInfoWidePo : globalContractInfoWidePos) {
            globalContractInfoList.add(convertGlobalContractPoToVo(globalContractInfoWidePo));
        }
        return globalContractInfoList;
    }

    private List<GlobalContractInfo> queryDcC1Contract(GlobalEcontractQueryCondition queryCondition) throws WmCustomerException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_2", "com.sankuai.meituan.waimai.customer.contract.global.WmGlobalEcontractQueryService.queryDcC1Contract(com.sankuai.meituan.waimai.customer.contract.global.dto.GlobalEcontractQueryCondition)");
        log.info("queryDcC1Contract queryCondition:{}",JSON.toJSON(queryCondition));
        Integer mtCustomerId = queryCondition.getCustomerId();
        Long dcCustomerId = null;
        try {
            dcCustomerId = customerIdMappingServiceAdapter.getOriginCustomerIdByMtCustomerId(Long.valueOf(mtCustomerId));
            log.info("queryDcC1Contract dcCustomerId:{}",dcCustomerId);
            List<TFrameCoop> dcC1ContractList = frameCoopServiceAdapter.getDcC1ContractList(dcCustomerId.intValue());
            log.info("queryDcC1Contract size:{}",dcC1ContractList.size());
            List<GlobalContractInfo> globalContractInfoList = new ArrayList<>();
            for (TFrameCoop tFrameCoop : dcC1ContractList) {

                WmBladeGlobalContractInfoWidePo contractInfoWidePo = convertToWmBladeGlobalContractInfoWidePo(tFrameCoop);
                if (filterDcCondition(contractInfoWidePo, queryCondition)) {
                    globalContractInfoList.add(convertGlobalContractPoToVoDc(contractInfoWidePo));
                }
            }

            return globalContractInfoList;
        } catch (TException | TCoopException | TIllegalArgumentException e) {
            log.warn("查询到餐C1合同异常, mtCustomerId:{}, dcCustomerId:{}, error: {}",mtCustomerId, dcCustomerId, e.getMessage());
            throw new WmCustomerException(CustomerErrorCodeConstants.SYSTEM_ERROR, "查询到餐C1合同异常: " + e.getMessage());
        }
    }

    private GlobalContractInfo convertGlobalContractPoToVoDc(WmBladeGlobalContractInfoWidePo widePo) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.contract.global.WmGlobalEcontractQueryService.convertGlobalContractPoToVoDc(com.sankuai.meituan.waimai.customer.domain.blade.WmBladeGlobalContractInfoWidePo)");
        GlobalContractInfo globalContractInfo = new GlobalContractInfo();
        globalContractInfo.setFrameContractNumber(widePo.getFrameEcontractNumber());
        globalContractInfo.setContractType(convertContractType(widePo.getContractType()));
        GlobalContractStatusEnum statusEnum = GlobalContractStatusEnum.getByCode(widePo.getContractStatus());

        if (Objects.nonNull(statusEnum)) {
            globalContractInfo.setContractStatus(statusEnum.getName());
        }

        globalContractInfo.setCustomerInfo(widePo.getCustomerInfo());
        globalContractInfo.setCreateTime(widePo.getPdfCreateTime());
        globalContractInfo.setContractCategory(GlobalContractCategoryEnum.getContractCategory(widePo.getContractCategory()).getContractCategoryDesc());
        globalContractInfo.setDueTime(widePo.getPdfDueTime());
        return globalContractInfo;
    }

    /**
     * 过滤到餐C1合同
     * @param contractInfoWidePo
     * @param queryCondition
     * @return
     */
    private Boolean filterDcCondition(WmBladeGlobalContractInfoWidePo contractInfoWidePo, GlobalEcontractQueryCondition queryCondition) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_1", "com.sankuai.meituan.waimai.customer.contract.global.WmGlobalEcontractQueryService.filterDcCondition(WmBladeGlobalContractInfoWidePo,GlobalEcontractQueryCondition)");
        boolean filterResult = true;
        if (queryCondition.getFrameEcontractNumber() != null && !queryCondition.getFrameEcontractNumber().isEmpty()) {
            filterResult = queryCondition.getFrameEcontractNumber().equals(contractInfoWidePo.getFrameEcontractNumber());
            if (!filterResult) {
                return false;
            }
        }
        if (queryCondition.getContractType() != null && queryCondition.getContractType() > 0) {
            filterResult = queryCondition.getContractType().equals(contractInfoWidePo.getContractType());
            if (!filterResult) {
                return false;
            }
        }
        if (queryCondition.getContractStatus() != null && queryCondition.getContractStatus() > 0) {
            filterResult = queryCondition.getContractStatus().equals(contractInfoWidePo.getContractStatus());
            if (!filterResult) {
                return false;
            }
        }
        if (queryCondition.getPdfCreateTime1() != null && queryCondition.getPdfCreateTime1() > 0) {
            if (contractInfoWidePo.getPdfCreateTime() == null){
                return false;
            }
            filterResult = queryCondition.getPdfCreateTime1() <= contractInfoWidePo.getPdfCreateTime();
            if (!filterResult) {
                return false;
            }
        }
        if (queryCondition.getPdfCreateTime2() != null && queryCondition.getPdfCreateTime2() > 0) {
            if (contractInfoWidePo.getPdfCreateTime() == null){
                return false;
            }
            filterResult = queryCondition.getPdfCreateTime2() >= contractInfoWidePo.getPdfCreateTime();
            if (!filterResult) {
                return false;
            }
        }
        if (queryCondition.getContractCategory() != null && queryCondition.getContractCategory() > 0) {
            filterResult = queryCondition.getContractCategory().equals(contractInfoWidePo.getContractCategory());
            if (!filterResult) {
                return false;
            }
        }

        return filterResult;
    }

    private WmBladeGlobalContractInfoWidePo convertToWmBladeGlobalContractInfoWidePo(TFrameCoop tFrameCoop) throws WmCustomerException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.contract.global.WmGlobalEcontractQueryService.convertToWmBladeGlobalContractInfoWidePo(com.sankuai.meituan.mtcoop.thrift.dto.TFrameCoop)");
        TFrame tFrame = tFrameCoop.getTFrame();
        long addTime = tFrame.getAddTime();
        long modTime = tFrame.getModTime();
        if (addTime > Integer.MAX_VALUE) {
            addTime = addTime / 1000;
        }
        if (modTime > Integer.MAX_VALUE) {
            modTime = modTime / 1000;
        }

        String customerName = getDcCustomerName(tFrame);
        String customerInfo = generateCustomerInfo(customerName, tFrame.getPartnerId());

        return WmBladeGlobalContractInfoWidePo.builder()
                .contractType(GlobalContractEnum.DAOCAN_SERVICE_C1_CONTRACT.getContractTypeCode())
                .contractStatus(mapDc2GlobalContractStatus(tFrame.getStatus()))
                .contractCategory(GlobalContractCategoryEnum.FRAME_CONTRACT.getContractCategoryCode())
                .frameEcontractNumber(tFrame.getFrameNum())
                .pdfDueTime(0) // 长期有效
                .customerInfo(customerInfo)
                .pdfCreateTime((int) addTime)
                .ctime((int) addTime)
                .utime((int) modTime)
                .valid(1)
                .build();
    }

    private String generateCustomerInfo(String customerName, Integer partnerId) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_1", "com.sankuai.meituan.waimai.customer.contract.global.WmGlobalEcontractQueryService.generateCustomerInfo(java.lang.String,java.lang.Integer)");
        if (!StringUtils.isEmpty(customerName) && partnerId != null) {
            StringBuilder customerInfo = new StringBuilder(customerName);
            customerInfo.append("(").append(partnerId).append(")");
            return customerInfo.toString();
        }
        return "";
    }

    /**
     * 获取到餐客户名称
     * @param tFrame
     * @return
     */
    private String getDcCustomerName(TFrame tFrame) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.contract.global.WmGlobalEcontractQueryService.getDcCustomerName(com.sankuai.meituan.mtcoop.thrift.dto.TFrame)");
        try {
            if (Objects.nonNull(tFrame)) {
                int dcCustomerId = tFrame.getPartnerId();
                Long dcPlatformId = customerIdMappingServiceAdapter.getDcPlatformIdByOriginCustomerId((long) dcCustomerId);
                log.info("getDcCustomerInfo partnerId:{}, dcPlatformId", dcCustomerId);
                WmCustomerDB customerDB = mtCustomerThriftServiceAdapter.getCustomerByIdAndBusinessLine(dcPlatformId, BusinessLineEnum.NIB_FOOD.getCode());
                log.info("getDcCustomerInfo customerDB:{}", JSON.toJSON(customerDB));
                if (Objects.nonNull(customerDB) && StringUtils.isNotEmpty(customerDB.getCustomerName())) {
                    return customerDB.getCustomerName();
                }
            }
        } catch (WmCustomerException e) {
            log.error("getDcCustomerInfo error,", e);
        }

        return "";
    }

    public Integer mapDc2GlobalContractStatus(Integer dcStatus) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_1", "com.sankuai.meituan.waimai.customer.contract.global.WmGlobalEcontractQueryService.mapDc2GlobalContractStatus(java.lang.Integer)");
        Integer contractStatus = DcContractStatusMapUtil.mapDcContractStatusToWm(dcStatus);
        if (Objects.isNull(contractStatus)) {
            return GlobalContractStatusEnum.UNKNOWN_STATUS.getCode();
        }
        Integer status = contractStatus2GlobalContractStatusMap.get(contractStatus);
        if (Objects.isNull(status)) {
            return GlobalContractStatusEnum.UNKNOWN_STATUS.getCode();
        }
        return status;
    }

    public Long getTotalNumber(GlobalEcontractQueryCondition queryCondition) {
        Long totalNumber;
        if (queryCondition.isSuperAdminAuthority()) {
            totalNumber = wmBladeGlobalContractInfoWideService.getTotalNumberWithAllAuthority(queryCondition);
        } else {
            totalNumber = wmBladeGlobalContractInfoWideService.getTotalNumberWithLimitAuthority(queryCondition);
        }
        log.info("WmGlobalEcontractQueryService#getTotalNumber, totalNumber: {}", totalNumber);
        return totalNumber;
    }

    private PageInfoDTO constructPageInfoDTO(PageInfoSearchParam pageInfoSearchParam, Long totalNumber) {
        PageInfoDTO pageInfoDTO = new PageInfoDTO();
        pageInfoDTO.setPageNo(pageInfoSearchParam.getPageNumber());
        pageInfoDTO.setPageSize(pageInfoSearchParam.getPageSize());
        pageInfoDTO.setTotalCount(totalNumber);
        return pageInfoDTO;
    }

    private GlobalEcontractQueryCondition constructQueryCondition(GlobalContractBaseParam baseSearchParam, PageInfoSearchParam pageInfoSearchParam) {
        GlobalEcontractQueryCondition queryCondition = GlobalEcontractQueryCondition.builder()
                .globalContractId(convertToLongValue(baseSearchParam.getGlobalContractId()))
                .recordKey(baseSearchParam.getRecordKey())
                .frameEcontractNumber(baseSearchParam.getFrameContractNumber())
                .signBatchId(convertToLongValue(baseSearchParam.getSignBatchId()))
                .contractType(baseSearchParam.getContractType())
                .customerId(baseSearchParam.getCustomerId())
                .wmPoiId(baseSearchParam.getWmPoiId())
                .contractTemplateId(baseSearchParam.getTemplateId() == null ? null : baseSearchParam.getTemplateId().toString())
                .contractTemplateVersion(baseSearchParam.getTemplateVersion() == null ? null : baseSearchParam.getTemplateVersion().toString())
                .contractStatus(baseSearchParam.getContractStatus())
                .signPhone(convertToLongValue(baseSearchParam.getPhoneNumber()))
                .signerName(baseSearchParam.getSignerName())
                .pdfCreateTime1(convertLongTimeToInteger(baseSearchParam.getCreateTime1()))
                .pdfCreateTime2(convertLongTimeToInteger(baseSearchParam.getCreateTime2()))
                .pdfSignTime1(convertLongTimeToInteger(baseSearchParam.getSignTime1()))
                .pdfSignTime2(convertLongTimeToInteger(baseSearchParam.getSignTime2()))
                .pdfCreatorUid(baseSearchParam.getCommitUid())
                .frameEcontractId(convertToLongValue(baseSearchParam.getFrameContractId()))
                .frameEcontractVersion(baseSearchParam.getFrameContractVersion())
                .contractCategory(baseSearchParam.getContractCategory())
                .partAName(baseSearchParam.getPartAName())
                .partBName(baseSearchParam.getPartBName())
                .pdfDueTime1(convertLongTimeToInteger(baseSearchParam.getDueTime1()))
                .pdfDueTime2(convertLongTimeToInteger(baseSearchParam.getDueTime2()))
                .build();

        if (pageInfoSearchParam != null) {
            queryCondition.setPageSize(pageInfoSearchParam.getPageSize());
            queryCondition.setOffset((pageInfoSearchParam.getPageNumber() - 1) * pageInfoSearchParam.getPageSize());
        }
        return queryCondition;
    }

    private GlobalEcontractAuthorityCondition constructAuthorityCondition(String uid) {
        GlobalEcontractAuthorityCondition authorityCondition = new GlobalEcontractAuthorityCondition();
        for (WmGlobalEcontractAuthorityService authorityService : wmGlobalEcontractAuthorityServiceList) {
            authorityService.getAuthority(authorityCondition, uid);
        }
        log.info("WmGlobalEcontractQueryService#constructAuthorityCondition, authorityCondition: {}", JSON.toJSONString(authorityCondition));
        return authorityCondition;
    }

    private Long convertToLongValue(String param) {
        if (Strings.isEmpty(param)) {
            return null;
        }
        return Long.valueOf(param);
    }

    private Integer convertLongTimeToInteger(Long time) {
        if (time == null) {
            return null;
        }
        return Math.toIntExact(time);
    }

    private GlobalContractInfo convertGlobalContractPoToVo(WmBladeGlobalContractInfoWidePo widePo) {
        GlobalContractInfo globalContractInfo = new GlobalContractInfo();
        globalContractInfo.setGlobalContractId(convertNumberToString(widePo.getGlobalContractId()));
        globalContractInfo.setFrameContractNumber(widePo.getFrameEcontractNumber());
        globalContractInfo.setSignBatchId(convertNumberToString(widePo.getSignBatchId()));
        globalContractInfo.setContractType(convertContractType(widePo.getContractType()));
        globalContractInfo.setCustomerInfo(widePo.getCustomerInfo());
        globalContractInfo.setWmPoiInfo(widePo.getWmPoiInfo());
        globalContractInfo.setTemplateInfo(widePo.getContractTemplateInfo());
        globalContractInfo.setContractStatus(GlobalContractStatusEnum.getByCode(widePo.getContractStatus()).getName());
        globalContractInfo.setPhoneNumber(convertNumberToString(widePo.getSignPhone()));
        globalContractInfo.setSignerName(widePo.getSignerName());
        globalContractInfo.setCreateTime(widePo.getPdfCreateTime());
        globalContractInfo.setSignTime(widePo.getPdfSignTime());
        globalContractInfo.setCreator(widePo.getPdfCreatorInfo());
        globalContractInfo.setFrameContractId(convertNumberToString(widePo.getFrameEcontractId()));
        globalContractInfo.setFrameContractVersion(widePo.getFrameEcontractVersion());
        globalContractInfo.setContractCategory(GlobalContractCategoryEnum.getContractCategory(widePo.getContractCategory()).getContractCategoryDesc());
        globalContractInfo.setTemplateVersion(widePo.getContractTemplateVersion());
        globalContractInfo.setPartAName(widePo.getPartAName());
        globalContractInfo.setPartBName(widePo.getPartBName());
        globalContractInfo.setDueTime(widePo.getPdfDueTime());
        globalContractInfo.setPdfUrl(widePo.getPdfUrl());
        globalContractInfo.setRecordKey(widePo.getRecordKey());
        return globalContractInfo;
    }



    private String convertContractType(Integer contractType) {
        try {
            ContractConfigInfo configInfo = wmFrameContractConfigService.queryContractConfigInfo(contractType);
            if (configInfo != null) {
                return configInfo.getContractName();
            }
        } catch (Exception e) {
            log.warn("WmGlobalEcontractQueryService#convertContractType, contractType: {}, error", contractType, e);
        }
        return GlobalContractEnum.getContractDesc(contractType);
    }

    public List<ContractCategoryInfo> queryContractCategory() {
        List<ContractCategoryInfo> contractCategoryInfoList = new ArrayList<>();

        for (GlobalContractCategoryEnum categoryEnum : GlobalContractCategoryEnum.values()) {
            if (categoryEnum == GlobalContractCategoryEnum.UN_KNOWN_CONTRACT) {
                continue;
            }
            ContractCategoryInfo contractCategoryInfo = new ContractCategoryInfo();
            contractCategoryInfo.setContractCategoryCode(categoryEnum.getContractCategoryCode());
            contractCategoryInfo.setContractCategory(categoryEnum.getContractCategoryDesc());
            contractCategoryInfoList.add(contractCategoryInfo);
        }
        return contractCategoryInfoList;
    }

    public List<ContractStatusInfo> queryContractStatus() {
        List<ContractStatusInfo> contractStatusInfoList = new ArrayList<>();

        for (GlobalContractStatusEnum contractStatusEnum : GlobalContractStatusEnum.values()) {
            ContractStatusInfo contractStatusInfo = new ContractStatusInfo();
            contractStatusInfo.setContractStatusCode(contractStatusEnum.getCode());
            contractStatusInfo.setContractStatus(contractStatusEnum.getName());
            contractStatusInfoList.add(contractStatusInfo);
        }
        return contractStatusInfoList;
    }

    public List<ContractTypeInfo> queryContractType() {
        List<ContractTypeInfo> contractTypeInfoList = new ArrayList<>();

        for (GlobalContractEnum contractCategoryEnum : GlobalContractEnum.values()) {
            if (contractCategoryEnum == GlobalContractEnum.THIRD_PARTY_CONTRACT) {
                continue;
            }
            ContractTypeInfo contractTypeInfo = new ContractTypeInfo();
            contractTypeInfo.setContractTypeCode(contractCategoryEnum.getContractTypeCode());
            contractTypeInfo.setContractType(contractCategoryEnum.getContractDesc());
            contractTypeInfoList.add(contractTypeInfo);
        }
        List<ContractConfigInfo> configInfoList = wmFrameContractConfigService.allConfigFrameContract();
        for (ContractConfigInfo configInfo : configInfoList) {
            if (configInfo.getStatus() == FrameContractConfigStatusEnum.DISABLE.getCode()) {
                continue;
            }
            ContractTypeInfo contractTypeInfo = new ContractTypeInfo();
            contractTypeInfo.setContractTypeCode(configInfo.getContractId());
            contractTypeInfo.setContractType(configInfo.getContractName());
            contractTypeInfoList.add(contractTypeInfo);
        }
        return contractTypeInfoList;
    }

    /**
     * 将Long或者Integer类型的参数转为String类型 若入参为null或者<=0则返回 ""
     */
    private String convertNumberToString(Number number) {
        if (number == null || number.longValue() <= 0) {
            return DEFAULT_STRING_VALUE;
        }
        return number.toString();
    }

}
