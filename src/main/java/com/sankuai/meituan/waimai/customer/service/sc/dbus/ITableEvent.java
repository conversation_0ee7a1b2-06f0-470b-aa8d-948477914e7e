package com.sankuai.meituan.waimai.customer.service.sc.dbus;

import com.sankuai.meituan.waimai.customer.constant.sc.WmScTableDbusEnum;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @date 2021/8/31
 */
public interface ITableEvent {

    WmScTableDbusEnum getTable();

    String handleUpdate(TableEvent tableEvent);

    String handleInsert(TableEvent tableEvent);

    String handleDelete(TableEvent tableEvent);

    /**
     * 基本参数校验
     *
     * @param tableEvent
     * @return
     */
    default boolean checkParamWhenHandleUpdate(TableEvent tableEvent) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_1", "com.sankuai.meituan.waimai.customer.service.sc.dbus.ITableEvent.checkParamWhenHandleUpdate(com.sankuai.meituan.waimai.customer.service.sc.dbus.TableEvent)");
        if (tableEvent == null
                || MapUtils.isEmpty(tableEvent.getMetaJsonData())
                || StringUtils.isBlank(tableEvent.getDiffJson())) {
            return false;
        }
        return true;
    }

    /**
     * 基本参数校验
     *
     * @param tableEvent
     * @return
     */
    default boolean checkParamWhenHandleInsert(TableEvent tableEvent) {
        if (tableEvent == null || MapUtils.isEmpty(tableEvent.getMetaJsonData())) {
            return false;
        }
        return true;
    }
}
