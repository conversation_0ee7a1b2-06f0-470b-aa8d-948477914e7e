package com.sankuai.meituan.waimai.customer.service.customer;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONObject;
import com.cip.crane.netty.utils.SleepUtils;
import com.google.common.base.Function;
import com.google.common.collect.Lists;
import com.sankuai.meituan.common.security.Base64;
import com.sankuai.meituan.waimai.customer.adapter.CityCommonServiceAdapter;
import com.sankuai.meituan.waimai.customer.adapter.WmEmployClient;
import com.sankuai.meituan.waimai.customer.adapter.WmVirtualOrgServiceAdaptor;
import com.sankuai.meituan.waimai.customer.config.MccCustomerConfig;
import com.sankuai.meituan.waimai.customer.constant.customer.CertificateDaXiangTypeEnum;
import com.sankuai.meituan.waimai.customer.contract.service.impl.WmCustomerOplogService;
import com.sankuai.meituan.waimai.customer.dao.WmCustomerDBMapper;
import com.sankuai.meituan.waimai.customer.ddd.adapter.MtCustomerThriftServiceAdapter;
import com.sankuai.meituan.waimai.customer.ddd.adapter.MtCustomerThriftServiceAdapterImpl;
import com.sankuai.meituan.waimai.customer.domain.*;
import com.sankuai.meituan.waimai.customer.util.AsciiUtil;
import com.sankuai.meituan.waimai.customer.util.HttpUtil;
import com.sankuai.meituan.waimai.customer.util.diff.BeanDiffUtil;
import com.sankuai.meituan.waimai.customer.util.diff.WmLogDiffConstant;
import com.sankuai.meituan.waimai.infra.domain.WmEmploy;
import com.sankuai.meituan.waimai.infra.domain.WmVirtualOrgCity;
import com.sankuai.meituan.waimai.infra.service.WmOpenCityService;
import com.sankuai.meituan.waimai.thrift.bo.WmCustomerDiffCellBo;
import com.sankuai.meituan.waimai.thrift.customer.constant.*;
import com.sankuai.meituan.waimai.thrift.customer.constant.customer.CertificateOverdueEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.customer.CertificateStatusEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.customer.CertificateTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.customer.LegalPersonChangeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.WmCustomerOplogBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.CertificateTypeDefaultBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.WmCustomerBasicBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.WmCustomerDetailBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.WmCustomerNoticeBo;
import com.sankuai.meituan.waimai.thrift.exception.MtCustomerException;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import com.sankuai.meituan.waimai.thrift.exception.WmSchCantException;
import com.sankuai.meituan.waimai.thrift.exception.WmServerException;
import com.sankuai.meituan.waimai.thrift.util.DiffUtil;
import com.sankuai.meituan.waimai.util.DateUtil;
import com.sankuai.meituan.waimai.util.DaxiangUtilV2;
import com.sankuai.meituan.waimai.util.MtCloudFileUtil;
import com.sankuai.meituan.wbinf.sg.api.certification.business.BusinessCertification;
import com.sankuai.meituan.wbinf.sg.api.certification.business.QueryBasicInfoRequest;
import com.sankuai.meituan.wbinf.sg.api.certification.business.QueryBasicInfoResponse;
import com.sankuai.meituan.wbinf.sg.api.certification.business.RegisterBasicInfo;
import com.sankuai.meituan.zcm.elicence.thrift.TElicenceRequestDTO;
import com.sankuai.meituan.zcm.elicence.thrift.TElicenceResult;
import com.sankuai.meituan.zcm.elicence.thrift.TElicenceService;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.thrift.TException;
import org.mortbay.util.ajax.JSON;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Nullable;
import java.util.*;

import static com.sankuai.meituan.waimai.thrift.customer.constant.CustomerConstants.RESULT_CODE_VERIFY_INPUT_ERROR;

/**
 * 客户执照逻辑处理
 */
@Service
public class WmCustomerCertificateService {

    private static Logger logger = LoggerFactory.getLogger(WmCustomerCertificateService.class);

    @Autowired
    WmCustomerESService wmCustomerESService;
    @Autowired
    WmCustomerPlatformDataParseService wmCustomerPlatformDataParseService;

    @Autowired
    WmCustomerService wmCustomerService;
    @Autowired
    WmCustomerDBMapper wmCustomerDBMapper;
    @Autowired
    WmCustomerOplogService wmCustomerOplogService;
    @Autowired
    WmEmployClient wmEmployClient;

    @Autowired
    WmVirtualOrgServiceAdaptor wmVirtualOrgServiceAdaptor;

    @Autowired
    private CityCommonServiceAdapter cityCommonServiceAdapter;

    @Autowired
    TElicenceService.Iface elicenceService;

    @Autowired
    protected MtCustomerThriftServiceAdapter mtCustomerThriftServiceAdapter;

    @Autowired
    private WmOpenCityService.Iface wmOpenCityThriftService;


    @Autowired
    private BusinessCertification.Iface businessIface;

    @Autowired
    private MtCustomerThriftServiceAdapterImpl mtCustomerThriftServiceAdapterImpl;

    /**
     * 电子营业执照消息监听
     *
     * @param wmCustomerNoticeBo
     * @throws TException
     * @throws WmSchCantException
     */
    public Boolean updateCertificateInfo(WmCustomerNoticeBo wmCustomerNoticeBo) throws TException, WmCustomerException, WmServerException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_2", "com.sankuai.meituan.waimai.customer.service.customer.WmCustomerCertificateService.updateCertificateInfo(com.sankuai.meituan.waimai.thrift.customer.domain.customer.WmCustomerNoticeBo)");
        logger.info("电子营业执照消息监听回写，WmCustomerNoticeBo={}", JSONObject.toJSONString(wmCustomerNoticeBo));
        //通过注册号和统一社会信用代码查询客户信息
        List<WmCustomerDB> wmCustomerDBList = getWmCustomerInfoByNotice(wmCustomerNoticeBo);
        if (CollectionUtils.isEmpty(wmCustomerDBList)) {
            return true;
        }
        for (WmCustomerDB wmCustomerDB : wmCustomerDBList) {
            try {
                if (wmCustomerDB == null) {
                    continue;
                }
                updateCertificateInfo(wmCustomerDB, wmCustomerNoticeBo);
            } catch (WmCustomerException e) {
                logger.warn("电子营业执照消息监听回写异常，WmCustomerNoticeBo={},msg={}", JSONObject.toJSONString(wmCustomerNoticeBo), e.getMsg(), e);
            } catch (Exception e) {
                logger.error("电子营业执照消息监听回写异常，WmCustomerNoticeBo={}", JSONObject.toJSONString(wmCustomerNoticeBo), e);
            }
        }

        return true;
    }

    /**
     * 处理客户执照信息
     *
     * @param wmCustomerDB
     * @param wmCustomerNoticeBo
     * @throws TException
     * @throws WmServerException
     * @throws WmCustomerException
     */
    private void updateCertificateInfo(WmCustomerDB wmCustomerDB,WmCustomerNoticeBo wmCustomerNoticeBo) throws TException, WmServerException, WmCustomerException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.customer.WmCustomerCertificateService.updateCertificateInfo(WmCustomerDB,WmCustomerNoticeBo)");
        //判断验真消息平台数据与当前数据状态是否有变化
        if (!updateIfCertificateStatusChange(wmCustomerNoticeBo, wmCustomerDB)) {
            return;
        }
        //校验客户营业执照信息
        checkWmCustomerDBInfo(wmCustomerDB);
        //判断信息是否修改或者客户是否属于注销、吊销、过期状态
        WmCustomerDB wmCustomerDBUpdate = new WmCustomerDB();
        BeanUtils.copyProperties(wmCustomerDB, wmCustomerDBUpdate);
        copyWmCustomerNoticeBo(wmCustomerNoticeBo, wmCustomerDBUpdate);
        List<WmCustomerDiffCellBo> diffList = DiffUtil.compare(wmCustomerDB, wmCustomerDBUpdate, CustomerConstants.customerCompareDaxaing);
        filterAscii(diffList);
        if (CollectionUtil.isEmpty(diffList)) {
            return;
        }
        //判断客户营业执照是否是纸质形式
        if (wmCustomerDB.getCertificateType() == CertificateTypeEnum.PAPER.getType()) {
            String msg = CertificateDaXiangTypeEnum.getMessage(wmCustomerDB, wmCustomerDBUpdate, CertificateDaXiangTypeEnum.PAPER.getType());
            pushMsg(wmCustomerDB.getOwnerUid(), msg);
            return;
        } else {
            //电子营业执照更新
            updateCustomerInfo(wmCustomerDBUpdate, wmCustomerDB, diffList);
            try {
                String msg = "";
                if (isHasLegalChange(diffList)) {
                    msg = CertificateDaXiangTypeEnum.getMessage(wmCustomerDB, wmCustomerDBUpdate, CertificateDaXiangTypeEnum.LEGALPARSONCHANGE.getType());
                } else {
                    msg = CertificateDaXiangTypeEnum.getMessage(wmCustomerDB, wmCustomerDBUpdate, CertificateDaXiangTypeEnum.LEGALPARSONCHANGEOTHER.getType());
                }
                pushMsg(wmCustomerDB.getOwnerUid(), msg);
            } catch (Exception e) {
                logger.error("电子营业执照消息监听回写异常，WmCustomerNoticeBo={}", JSONObject.toJSONString(wmCustomerNoticeBo), e);
            }
        }
    }

    /**
     * 过滤掉因为特殊字符以及半角/全角问题引起的无效diff
     *
     * @param diffList
     */
    private void filterAscii(List<WmCustomerDiffCellBo> diffList) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.customer.WmCustomerCertificateService.filterAscii(java.util.List)");
        if (!MccCustomerConfig.asciiDealWhenCompareSwitch()) {
            return;
        }
        if (CollectionUtil.isEmpty(diffList)) {
            return;
        }
        List<String> fieldKeyList = MccCustomerConfig.asciiDealFieldWhenCompare();
        if (CollectionUtil.isEmpty(fieldKeyList)) {
            return;
        }
        Iterator<WmCustomerDiffCellBo> it = diffList.iterator();
        while (it.hasNext()) {
            WmCustomerDiffCellBo wmCustomerDiffCellBo = it.next();
            if (fieldKeyList.contains(wmCustomerDiffCellBo.getField())) {
                String pre = AsciiUtil.replaceSpecialStr(AsciiUtil.toDBCCase(wmCustomerDiffCellBo.getPre()));
                String aft = AsciiUtil.replaceSpecialStr(AsciiUtil.toDBCCase(wmCustomerDiffCellBo.getAft()));
                if (pre.equals(aft)) {
                    it.remove();
                }
            }
        }
    }


    /**
     * 发送消息
     *
     * @param uid
     * @param msg
     */
    private void pushMsg(Integer uid, String msg) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_1", "com.sankuai.meituan.waimai.customer.service.customer.WmCustomerCertificateService.pushMsg(java.lang.Integer,java.lang.String)");
        try {
            if (uid == null) {
                return;
            }
            WmEmploy originalOwner = wmEmployClient.getEmployById(uid);
            if (originalOwner != null && StringUtils.isNotEmpty(msg)) {
                DaxiangUtilV2.push(msg, String.format("%<EMAIL>", originalOwner.getMisId()));
                logger.info("给客户责任人发送执照变更消息 uid={},msg={}", uid, msg);
            }
        } catch (Exception e) {
            logger.error("pushMsg::uid = {}, msg = {}", uid, msg, e);
        }
    }

    /**
     * 判断客户状态是否有变化，如果有变化，则修改数据并发送大象消息
     *
     * @param wmCustomerNoticeBo
     * @param wmCustomerDB
     * @return
     */
    private boolean updateIfCertificateStatusChange(WmCustomerNoticeBo wmCustomerNoticeBo, WmCustomerDB wmCustomerDB) throws WmCustomerException, TException, WmServerException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_2", "com.sankuai.meituan.waimai.customer.service.customer.WmCustomerCertificateService.updateIfCertificateStatusChange(WmCustomerNoticeBo,WmCustomerDB)");
        if (CertificateStatusEnum.getByType(wmCustomerNoticeBo.getLicenseStatus()) == null) {
            //营业执照状态参数不合法
            return false;
        }
        if (wmCustomerNoticeBo.getLicenseStatus() == CertificateStatusEnum.NONE.getType()) {
            //判断验真数据状态是否为空，营业执照状态不在修改类型中
            return false;
        }
        //判断两个状态码是否相等，相等则返回true继续走接下来的流程，不想等则修改数据状态并发送大象消息
        if (wmCustomerNoticeBo.getLicenseStatus() != wmCustomerDB.getCertificateStatus()) {
            //修改数据
            WmCustomerDB wmCustomerDUpdate = new WmCustomerDB();
            BeanUtils.copyProperties(wmCustomerDB, wmCustomerDUpdate);
            wmCustomerDUpdate.setCertificateStatus(wmCustomerNoticeBo.getLicenseStatus());
            updateCustomerCertificateStatus(wmCustomerDUpdate, wmCustomerDB);
            //发送大象消息
            String msg = CertificateDaXiangTypeEnum.getMessage(wmCustomerDB, null, wmCustomerNoticeBo.getLicenseStatus());
            pushMsg(wmCustomerDB.getOwnerUid(), msg);
            return false;
        }
        return true;
    }

    /**
     * 修改数据并记录日志
     *
     * @param wmCustomerDUpdate
     * @param wmCustomerDB
     * @throws WmCustomerException
     */
    private void updateCustomerInfo(WmCustomerDB wmCustomerDUpdate, WmCustomerDB wmCustomerDB, List<WmCustomerDiffCellBo> diffList) throws WmCustomerException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.service.customer.WmCustomerCertificateService.updateCustomerInfo(WmCustomerDB,WmCustomerDB,List)");
        if (CollectionUtil.isEmpty(diffList)) {
            throw new WmCustomerException(RESULT_CODE_VERIFY_INPUT_ERROR, "未对数据进行修改");
        }
        String log = "";
        if (isHasLegalChange(diffList)) {
            if (wmCustomerDB.getLegalPersonChange().equals(wmCustomerDUpdate.getLegalPersonChange())) {
                return;
            }
            log = BeanDiffUtil.diffContentByMap(wmCustomerDB, wmCustomerDUpdate, BeanDiffUtil.PropertyDesc.clazz(WmCustomerDB.class)
                    .name(CustomerConstants.CUSTOMER_FIELD_LEGAL_PERSON_CHANGE).desc(CustomerConstants.CUSTOMER_DESC_LEGAL_PERSON_CHANGE)
                    .valueDesc(LegalPersonChangeEnum.CHANGE.getType(), LegalPersonChangeEnum.CHANGE.getName())
                    .valueDesc(LegalPersonChangeEnum.NOCHANGE.getType(), LegalPersonChangeEnum.NOCHANGE.getName())
                    .getChainMap());
            //有法人变更，则只更新客户本地存储的信息，更新法人变更标记，其他信息不更新
            wmCustomerDB.setLegalPersonChange(wmCustomerDUpdate.getLegalPersonChange());
            wmCustomerService.updateCustomer(wmCustomerDB, 0);
        } else {
            //无法人变更，则更新变更的信息，更新客户平台信息
            try {
                updateMtCustomerNoImg(wmCustomerDUpdate);
            } catch (MtCustomerException e) {
                logger.error("updateCustomerInfo更新客户信息到美团客户平台失败,customerId={},diffList={}", wmCustomerDUpdate.getId(), JSON.toString(diffList), e);
            }
            log = BeanDiffUtil.diffContentByMap(wmCustomerDB, wmCustomerDUpdate, WmLogDiffConstant.CUSTOMER_BUSINESS_MAP);
        }
        if (StringUtils.isNotBlank(log)) {
            insertCustomerOpLog(wmCustomerDB.getId(), log);
        }
    }

    /**
     * 是否有法人变更
     *
     * @param diffList
     * @return
     */
    private boolean isHasLegalChange(List<WmCustomerDiffCellBo> diffList) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.service.customer.WmCustomerCertificateService.isHasLegalChange(java.util.List)");
        if (CollectionUtils.isEmpty(diffList)) {
            return false;
        }
        for (WmCustomerDiffCellBo wmCustomerDiffCellBo : diffList) {
            if (CustomerConstants.CUSTOMER_FIELD_LEGALPERSON.equals(wmCustomerDiffCellBo.getField())) {
                return true;
            }
        }

        return false;
    }


    /**
     * 修改数据并记录日志
     *
     * @param wmCustomerDUpdate
     * @param wmCustomerDB
     * @throws WmCustomerException
     */
    private void updateCustomerCertificateStatus(WmCustomerDB wmCustomerDUpdate, WmCustomerDB wmCustomerDB) throws WmCustomerException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.service.customer.WmCustomerCertificateService.updateCustomerCertificateStatus(WmCustomerDB,WmCustomerDB)");
        wmCustomerService.updateCustomerCertificateStatus(wmCustomerDUpdate.getId(), wmCustomerDUpdate.getCertificateStatus());
        //插入修改日志
        String log = BeanDiffUtil.diffContentByMap(wmCustomerDB, wmCustomerDUpdate, WmLogDiffConstant.CUSTOMER_BUSINESS_MAP);
        insertCustomerOpLog(wmCustomerDB.getId(), log);
    }

    /**
     * 更新客户信息到美团客户平台
     */
    public long updateMtCustomerNoImg(WmCustomerDB wmCustomerDB) throws MtCustomerException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.service.customer.WmCustomerCertificateService.updateMtCustomerNoImg(com.sankuai.meituan.waimai.customer.domain.WmCustomerDB)");
        return mtCustomerThriftServiceAdapter.updateMtCustomer(wmCustomerDB, false);
    }

    /**
     * 插入客户操作日志
     */
    private void insertCustomerOpLog(Integer customerId, String log) throws WmCustomerException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.customer.WmCustomerCertificateService.insertCustomerOpLog(java.lang.Integer,java.lang.String)");
        if (StringUtils.isBlank(log)) {
            return;
        }
        WmCustomerOplogBo wmCustomerOplogBo = new WmCustomerOplogBo(customerId, WmCustomerOplogBo.OpModuleType.CUSTOMER, customerId);
        wmCustomerOplogBo.setOpType(WmCustomerOplogBo.OpType.UPDATE.type);
        wmCustomerOplogBo.setLog(log);
        wmCustomerOplogBo.setOpUid(0);
        wmCustomerOplogBo.setOpUname(MccCustomerConfig.getVerifyServerOpUname());
        wmCustomerOplogBo.setRemark("");
        wmCustomerOplogService.insert(wmCustomerOplogBo);
    }

    /**
     * 监听消息信息赋值
     *
     * @param wmCustomerNoticeBo
     * @param wmCustomerDB
     * @return
     */
    private WmCustomerDB copyWmCustomerNoticeBo(WmCustomerNoticeBo wmCustomerNoticeBo, WmCustomerDB wmCustomerDB) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.service.customer.WmCustomerCertificateService.copyWmCustomerNoticeBo(WmCustomerNoticeBo,WmCustomerDB)");
        String noticeLegalPerson = wmCustomerNoticeBo.getLegalPerson();
        String legalPerson = wmCustomerDB.getLegalPerson();
        String noticeCustomerName = wmCustomerNoticeBo.getCustomerName();
        String customerName = wmCustomerDB.getCustomerName();
        if (MccCustomerConfig.asciiDealWhenCompareSwitch()) {
            //消息中转为半角且去掉字符后的法人名字
            noticeLegalPerson = AsciiUtil.replaceSpecialStr(AsciiUtil.toDBCCase(noticeLegalPerson));
            //客户系统数据转为半角且去掉字符后的法人名字
            legalPerson = AsciiUtil.replaceSpecialStr(AsciiUtil.toDBCCase(legalPerson));
            noticeCustomerName = AsciiUtil.replaceSpecialStr(AsciiUtil.toDBCCase(noticeCustomerName));
            customerName = AsciiUtil.replaceSpecialStr(AsciiUtil.toDBCCase(customerName));
        }
        if (!noticeLegalPerson.equals(legalPerson)) {
            //变更后法人不等
            wmCustomerDB.setLegalPersonChange(LegalPersonChangeEnum.CHANGE.getType());
            wmCustomerDB.setLegalPerson(wmCustomerNoticeBo.getLegalPerson());
        }
        wmCustomerDB.setCustomerName(!noticeCustomerName.equals(customerName) ? wmCustomerNoticeBo.getCustomerName() : wmCustomerDB.getCustomerName());
        wmCustomerDB.setAddress(StringUtils.isBlank(wmCustomerNoticeBo.getAddress()) ? wmCustomerDB.getAddress() : wmCustomerNoticeBo.getAddress());
        wmCustomerDB.setValidateDate(wmCustomerNoticeBo.getValidateDate() == null ? wmCustomerDB.getValidateDate() : wmCustomerNoticeBo.getValidateDate());
        wmCustomerDB.setBusinessScope(StringUtils.isBlank(wmCustomerNoticeBo.getBusinessScope()) ? wmCustomerDB.getBusinessScope() : wmCustomerNoticeBo.getBusinessScope());
        return wmCustomerDB;
    }

    /**
     * 校验客户营业执照信息是否是审核中或者待提审
     *
     * @param wmCustomerDB
     * @throws WmCustomerException
     */
    private void checkWmCustomerDBInfo(WmCustomerDB wmCustomerDB) throws WmCustomerException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.service.customer.WmCustomerCertificateService.checkWmCustomerDBInfo(com.sankuai.meituan.waimai.customer.domain.WmCustomerDB)");
        //判断客户营业执照是否被注销或者吊销
        if (wmCustomerDB.getCertificateStatus() == CertificateStatusEnum.CANCELLATION.getType()
                || wmCustomerDB.getCertificateStatus() == CertificateStatusEnum.REVOKE.getType()) {
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "营业执照已经被注销或吊销,无法修改");
        }
        //判断客户营业执照是否过期
        if (wmCustomerDB.getCertificateOverdue() == CertificateOverdueEnum.EXPIRED.getType()) {
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "营业执照已经过期,无法修改");
        }

        //判断客户信息是否处于审核中
        if (wmCustomerDB.getAuditStatus() == CustomerAuditStatus.CUSTOMER_AUDIT_AUDITING.getCode()) {
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, wmCustomerDB.getCustomerName() + "正在审核,无法修改");
        }
        //判断客户信息是否处于待提审状态
        if (wmCustomerDB.getAuditStatus() == CustomerAuditStatus.CUSTOMER_AUDIT_NEW.getCode()) {
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, wmCustomerDB.getCustomerName() + "待提审,无法修改");
        }
    }

    /**
     * 通过注册号和统一社会信用代码查询客户信息
     *
     * @param wmCustomerNoticeBo
     * @return
     */
    private List<WmCustomerDB> getWmCustomerInfoByNotice(WmCustomerNoticeBo wmCustomerNoticeBo) throws WmCustomerException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.customer.WmCustomerCertificateService.getWmCustomerInfoByNotice(com.sankuai.meituan.waimai.thrift.customer.domain.customer.WmCustomerNoticeBo)");
        //查询ES中的数据
        List<WmCustomerListDB> wmCustomerListDBList = getCustomerInfosByES(wmCustomerNoticeBo);
        if (CollectionUtil.isEmpty(wmCustomerListDBList)) {
            return Lists.newArrayList();
        }
        //客户信息覆盖
        List<Integer> customerIdsTemp = Lists.transform(wmCustomerListDBList, new Function<WmCustomerListDB, Integer>() {
            @Nullable
            @Override
            public Integer apply(@Nullable WmCustomerListDB input) {
                return input.getId();
            }
        });
        Set<Integer> customerIdsSet = new HashSet<>(customerIdsTemp);
        //通过查询ES获得的客户Id查询客户信息
        return wmCustomerPlatformDataParseService.selectCustomerFromPlatformByIds(customerIdsSet);
    }

    /**
     * 首先通过注册号在客户端查询客户信息，如果查不到，则通过统一社会信用代码查询
     *
     * @param wmCustomerNoticeBo
     * @return
     */
    private List<WmCustomerListDB> getCustomerInfosByES(WmCustomerNoticeBo wmCustomerNoticeBo) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.service.customer.WmCustomerCertificateService.getCustomerInfosByES(com.sankuai.meituan.waimai.thrift.customer.domain.customer.WmCustomerNoticeBo)");
        List<WmCustomerListDB> listByRegisterNo = getCustomerInfosByCode(wmCustomerNoticeBo.getRegisterNo());
        if (!CollectionUtils.isEmpty(listByRegisterNo)) {
            return listByRegisterNo;
        } else {
            return getCustomerInfosByCode(wmCustomerNoticeBo.getCreditCode());
        }
    }

    /**
     * 根据营业执照编号查询客户信息
     *
     * @param code
     * @return
     */
    private List<WmCustomerListDB> getCustomerInfosByCode(String code) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_2", "com.sankuai.meituan.waimai.customer.service.customer.WmCustomerCertificateService.getCustomerInfosByCode(java.lang.String)");
        if (StringUtils.isBlank(code)) {
            return Lists.newArrayList();
        }
        WmCustomerEsQueryVo vo = new WmCustomerEsQueryVo();
        vo.setPageNo(1);
        vo.setPageSize(MccCustomerConfig.getEsPageSize());
        vo.setCustomerType(CustomerType.CUSTOMER_TYPE_BUSINESS.getCode());
        //首先通过注册号查询客户营业执照信息
        vo.setCustomerNumber(code);
        try {
            return wmCustomerESService.queryCustomerPage(vo);
        } catch (Exception e) {
            logger.error("查询es失败,code={}", code, e);
        }
        return Lists.newArrayList();
    }

    /**
     * 通过用户Id查询灰度信息
     *
     * @param userId
     * @return
     * @throws TException
     * @throws WmCustomerException
     */
    public CertificateTypeDefaultBo getCertificateDefault(int userId) throws TException, WmCustomerException {
        logger.info("getCertificateDefault(), userId={}", userId);
        CertificateTypeDefaultBo certificateTypeDefaultBo = new CertificateTypeDefaultBo();
        //1、获取灰度时间并与当前时间进行对比
        WmCustomerBusLicTypeelegrayBo wmCustomerBusLicTypeelegrayBo = new WmCustomerBusLicTypeelegrayBo().deepCopy();
        certificateTypeDefaultBo = checkCustomerBrandBatchNum(certificateTypeDefaultBo, wmCustomerBusLicTypeelegrayBo);
        if (CertificateTypeEnum.getByType(certificateTypeDefaultBo.getDefaultVal()) != null) {
            return certificateTypeDefaultBo;
        }
        certificateTypeDefaultBo.setIsEdit(false);
        certificateTypeDefaultBo.setDefaultVal(CertificateTypeEnum.PAPER.getType());
        //2、通过用户Id查询物理城市
        List<WmVirtualOrgCity> wmVirtualOrgCitie = wmVirtualOrgServiceAdaptor.getWmVirtualOrgCityByUserid(userId);
        if (CollectionUtils.isEmpty(wmVirtualOrgCitie)) {
            return certificateTypeDefaultBo;
        }
        //获取二级物理城市及物理城市下的所有区级的id的集合
        List<Integer> citys = cityCommonServiceAdapter.getCityAndGisInfo(wmCustomerBusLicTypeelegrayBo.getSecondCityId());
        //3、判断用户所属组织节点所属城市是否在灰度城市内
        CertificateTypeDefaultBo finalCertificateTypeDefaultBo = certificateTypeDefaultBo;
        wmVirtualOrgCitie.forEach(wmVirtualOrgCity -> {
            if (!Collections.disjoint(citys, wmVirtualOrgCity.getCityIds())) {
                finalCertificateTypeDefaultBo.setIsEdit(true);
                finalCertificateTypeDefaultBo.setDefaultVal(CertificateTypeEnum.ELECTRONIC.getType());
            }
        });
        return finalCertificateTypeDefaultBo;
    }

    /**
     * 校验MCC控制信息
     *
     * @param certificateTypeDefaultBo
     * @param wmCustomerBusLicTypeelegrayBo
     * @return
     */
    private CertificateTypeDefaultBo checkCustomerBrandBatchNum(CertificateTypeDefaultBo certificateTypeDefaultBo, WmCustomerBusLicTypeelegrayBo wmCustomerBusLicTypeelegrayBo) {
        //1、Mcc控制为空，则返回全量
        if (wmCustomerBusLicTypeelegrayBo == null || StringUtils.isEmpty(wmCustomerBusLicTypeelegrayBo.getDeadline())
                || CollectionUtils.isEmpty(wmCustomerBusLicTypeelegrayBo.getSecondCityId())) {
            certificateTypeDefaultBo.setIsEdit(true);
            certificateTypeDefaultBo.setDefaultVal(MccCustomerConfig.getDefaultCertificateType());
            return certificateTypeDefaultBo;
        }
        //2、判断灰度时间
        Date nowDate = DateUtil.today();
        Date validateDate = DateUtil.stirng2Date(wmCustomerBusLicTypeelegrayBo.getDeadline(), DateUtil.DefaultShortFormat);
        //当前时间在灰度时间之后
        if (nowDate.after(validateDate)) {
            //3、灰度时间之后，证件形式为电子形式可以进行操作，并通过MCC控制默认值
            certificateTypeDefaultBo.setIsEdit(true);
            certificateTypeDefaultBo.setDefaultVal(MccCustomerConfig.getDefaultCertificateType());
            return certificateTypeDefaultBo;
        }
        return certificateTypeDefaultBo;
    }

    /**
     * 查询电子营业执照信息
     *
     * @param verificationCoded
     * @param sequence
     * @param opUid
     * @return
     * @throws TException
     * @throws WmCustomerException
     */
    public WmCustomerDetailBo getElicenseByVerificationCoded(String verificationCoded, Long sequence, int opUid) throws TException, WmCustomerException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.service.customer.WmCustomerCertificateService.getElicenseByVerificationCoded(java.lang.String,java.lang.Long,int)");
        TElicenceRequestDTO elicenceRequestDTO = new TElicenceRequestDTO();
        elicenceRequestDTO.setOperator(opUid);
        elicenceRequestDTO.setSequence(sequence);
        elicenceRequestDTO.setSourceCode(MccCustomerConfig.getElicenceSourceCode());
        elicenceRequestDTO.setVerificationCode(verificationCoded);
        TElicenceResult tElicenceResult = elicenceService.getByHolderCode(elicenceRequestDTO);
        WmCustomerDetailBo wmCustomerDetailBo = new WmCustomerDetailBo();
        if (tElicenceResult != null && tElicenceResult.getData() != null) {
            wmCustomerDetailBo.setPicUrl(uploadFileImg(tElicenceResult.getData().getImageUrl(), (long) opUid));
            wmCustomerDetailBo.setCustomerNumber(tElicenceResult.getData().getUniscid());
            wmCustomerDetailBo.setCustomerName(tElicenceResult.getData().getName());
            wmCustomerDetailBo.setLegalPerson(tElicenceResult.getData().getLegalPersonName());
            wmCustomerDetailBo.setAddress(tElicenceResult.getData().getRegAddress());
            wmCustomerDetailBo.setBusinessScope(tElicenceResult.getData().getScope());
            if (tElicenceResult.getData().isPermanent == true) {
                wmCustomerDetailBo.setValidateDate(0);
            } else {
                String validateDate = tElicenceResult.getData().getParsedEffectTo();
                if (StringUtils.isNotEmpty(validateDate)) {
                    wmCustomerDetailBo.setValidateDate(DateUtil.date2Unixtime(DateUtil.string2DateSecond24(validateDate + " 23:59:59")));
                }
            }
            return wmCustomerDetailBo;
        }
        return null;
    }

    /**
     * 将电子营业执照图片上传到美团云
     *
     * @param imageUr
     * @return
     */
    private String uploadFileImg(String imageUr, Long userId) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.customer.WmCustomerCertificateService.uploadFileImg(java.lang.String,java.lang.Long)");
        if (StringUtils.isEmpty(imageUr)) {
            return "";
        }
        //电子营业执照图片处理
        byte[] fileDataOld = HttpUtil.getBytesFrommUrl(imageUr);
        String newFileName = "contract_file_" + DigestUtils.md5Hex(fileDataOld) + "_" + System.currentTimeMillis() + ".jpg";
        logger.info("newFileName = {}", newFileName);
        try {
            String picUrl= MtCloudFileUtil.uploadFileFromBytes(fileDataOld, newFileName);
            String fileContent = Base64.encodeToString(fileDataOld);
            picUrl = picUrl.replaceFirst("/", "");
            boolean flag = mtCustomerThriftServiceAdapterImpl.uploadImgToMtCustomer(picUrl, fileContent);
            if (!flag) {
                logger.error("上传文件到客户平台失败 fileName {},imageUr={},userId={}", newFileName, imageUr, userId);
                throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "上传文件到客户平台失败");
            }
            return picUrl;
        } catch (Exception e) {
            logger.warn("上传文件失败 fileName {}", newFileName, e);
        }
        return "";
    }


    /**
     * 添加营业执照新增相关字段
     *
     * @param wmCustomerBasicBo
     * @param wmCustomerDB
     */
    public void setCustomerCertificate(WmCustomerBasicBo wmCustomerBasicBo, WmCustomerDB wmCustomerDB) throws TException {
        if (wmCustomerBasicBo.getId() == 0) {
            //新增营业执照信息相关默认值
            wmCustomerBasicBo.setCertificateType(ObjectUtils.defaultIfNull(wmCustomerBasicBo.getCertificateType(), CertificateTypeEnum.PAPER.getType()));
            wmCustomerBasicBo.setCertificateStatus(ObjectUtils.defaultIfNull(wmCustomerBasicBo.getCertificateStatus(), CertificateStatusEnum.NONE.getType()));
            wmCustomerBasicBo.setCertificateOverdue(ObjectUtils.defaultIfNull(wmCustomerBasicBo.getCertificateOverdue(), CertificateOverdueEnum.NOEXPIRED.getType()));
            wmCustomerBasicBo.setLegalPersonChange(ObjectUtils.defaultIfNull(wmCustomerBasicBo.getLegalPersonChange(), LegalPersonChangeEnum.NOCHANGE.getType()));
        } else {
            if (wmCustomerDB != null) {
                wmCustomerBasicBo.setCertificateType(wmCustomerBasicBo.getCertificateType() == null ? wmCustomerDB.getCertificateType() : wmCustomerBasicBo.getCertificateType());
                wmCustomerBasicBo.setCertificateStatus(wmCustomerDB.getCertificateStatus());
                wmCustomerBasicBo.setLegalPersonChange(wmCustomerDB.getLegalPersonChange());
                //修改客户信息的时候如果修改了法人信息则法人变更标记掉标
                if (wmCustomerBasicBo.getLegalPerson() != null && !wmCustomerBasicBo.getLegalPerson().equals(wmCustomerDB.getLegalPerson())) {
                    wmCustomerBasicBo.setLegalPersonChange(LegalPersonChangeEnum.NOCHANGE.getType());
                }
            }
        }

        if (wmCustomerBasicBo.getCustomerType() == CustomerType.CUSTOMER_TYPE_BUSINESS.getCode()) {
            //资质验真（疑问：已存在校验器[WmCustomerBussinessValidator]）
            BusinessValidateResult businessValidateResult = businessCheck(wmCustomerBasicBo);
            if (businessValidateResult.getValidateStatus() == ValidateStatus.NO_DATE) {
                wmCustomerBasicBo.setCertificateStatus(CertificateStatusEnum.NONE.getType());
            } else if (businessValidateResult.getValidateStatus() == ValidateStatus.PASS) {
                wmCustomerBasicBo.setCertificateStatus(CertificateStatusEnum.DURATION.getType());
            }
        }

        //营业执照有效时间是长期有效或者小于当前时间
        if (wmCustomerBasicBo.getValidateDate() == 0l
                || wmCustomerBasicBo.getValidateDate() >= DateUtil.unixTime()) {
            wmCustomerBasicBo.setCertificateOverdue(CertificateOverdueEnum.NOEXPIRED.getType());
        } else {
            wmCustomerBasicBo.setCertificateOverdue(CertificateOverdueEnum.EXPIRED.getType());
        }
    }

    /**
     * 资质验真
     *
     * @param wmCustomerBasicBo
     * @return
     * @throws TException
     */
    private BusinessValidateResult businessCheck(WmCustomerBasicBo wmCustomerBasicBo) throws TException {
        BusinessValidateResult businessValidateResult = new BusinessValidateResult();
        try {
            QueryBasicInfoRequest request = new QueryBasicInfoRequest()
                    .setLicenseNo(wmCustomerBasicBo.getCustomerNumber());
            logger.info("营业执照判真，request = {}", request);
            QueryBasicInfoResponse response = businessIface.queryBasicInfo(
                    MccCustomerConfig.getBusinessCertificationPartnerId(),
                    MccCustomerConfig.getBusinessCertificationDigest(),
                    request);
            logger.info("营业执照判真，response = {}", response);

            if (response == null) {
                businessValidateResult.setValidateStatus(ValidateStatus.NO_DATE);
                return businessValidateResult;
            }

            if (response.getRegisterBasicInfo() == null) {
                businessValidateResult.setValidateStatus(ValidateStatus.NO_DATE);
                return businessValidateResult;
            }
            RegisterBasicInfo registerBasicInfo = response.getRegisterBasicInfo();
            if (registerBasicInfo.getLicenseStatusCode() != CustomerConstants.BUSSINESS_LINESS_REVOKE &&
                    registerBasicInfo.getLicenseStatusCode() != CustomerConstants.BUSSINESS_LINESS_WRITEOFF) {
                businessValidateResult.setValidateStatus(ValidateStatus.PASS);
            }
        } catch (Exception e) {
            logger.error("资质判真接口异常 customerNumber={}", wmCustomerBasicBo.getCustomerNumber(), e);
            businessValidateResult.setValidateStatus(ValidateStatus.ERROR);
        }
        return businessValidateResult;
    }
}
