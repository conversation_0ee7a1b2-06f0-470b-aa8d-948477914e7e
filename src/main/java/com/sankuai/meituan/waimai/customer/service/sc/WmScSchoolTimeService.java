package com.sankuai.meituan.waimai.customer.service.sc;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.meituan.waimai.customer.adapter.WmEmployClient;
import com.sankuai.meituan.waimai.customer.constant.ScFieldConstant;
import com.sankuai.meituan.waimai.customer.constant.sc.WmScTairLockSceneEnum;
import com.sankuai.meituan.waimai.customer.dao.sc.WmScSchoolTimeMapper;
import com.sankuai.meituan.waimai.customer.domain.sc.*;
import com.sankuai.meituan.waimai.customer.service.sc.check.SchoolTimeInfoCheckService;
import com.sankuai.meituan.waimai.infra.domain.WmEmploy;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.*;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.time.SchoolTermBeginPlanEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.time.SchoolTermBeginSituationEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.time.SchoolTermEndPlanEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.time.SchoolTermEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.sc.*;
import com.sankuai.meituan.waimai.thrift.customer.domain.sc.time.SchoolTermBeginDataDTO;
import com.sankuai.meituan.waimai.thrift.customer.domain.sc.time.SchoolTermEndDataDTO;
import com.sankuai.meituan.waimai.thrift.customer.domain.sc.time.SchoolTimeInfoDTO;
import com.sankuai.meituan.waimai.thrift.customer.domain.sc.time.SchoolTimeQueryConditionDTO;
import com.sankuai.meituan.waimai.thrift.exception.WmSchCantException;
import com.sankuai.meituan.waimai.util.StringUtil;
import deps.redis.clients.util.CollectionUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

import static com.sankuai.meituan.waimai.customer.config.MccScConfig.getSchoolYearInfos;
import static com.sankuai.meituan.waimai.thrift.customer.constant.sc.ScConstants.*;
import static com.sankuai.meituan.waimai.thrift.customer.constant.sc.WmScCodeConstants.*;

/**
 * 学校详情其他信息逻辑
 */
@Slf4j
@Service
public class WmScSchoolTimeService {

    @Autowired
    private WmScSchoolTimeMapper wmScSchoolTimeMapper;


    @Autowired
    private WmEmployClient wmEmployClient;

    @Autowired
    private WmScLogSchoolInfoService wmScLogSchoolInfoService;

    @Autowired
    private SchoolTimeInfoCheckService schoolTimeInfoCheckService;


    @Autowired
    private WmScTairService wmScTairService;

    /**
     * 新增学校时间信息时加锁前缀
     */
    public static final String SCHOOL_TIME_LOCKKEY_PREFIX = "school_time_";

    /**
     * 获取学年列表
     *
     * @return
     * @throws WmSchCantException
     */
    public List<SchoolTimeBeginEndBo> getSchoolYearInfo() throws WmSchCantException {
        log.info("#getSchoolYearInfo");
        String schoolYearInfo = getSchoolYearInfos();
        String[] yearStrs = schoolYearInfo.split(",");
        int startYear = Integer.parseInt(yearStrs[0]);
        int entYear = Integer.parseInt(yearStrs[1]);
        List<SchoolTimeBeginEndBo> schoolTimeBeginEndBos = new ArrayList<>();
        while (startYear <= entYear) {
            SchoolTimeBeginEndBo schoolTimeBeginEndBo = new SchoolTimeBeginEndBo();
            schoolTimeBeginEndBo.setBeginTime(String.valueOf(startYear));
            schoolTimeBeginEndBo.setEndTime(String.valueOf(startYear + 1));
            schoolTimeBeginEndBos.add(schoolTimeBeginEndBo);
            startYear++;
        }
        return schoolTimeBeginEndBos;
    }


    /**
     * 获取当前学校下的所有时间信息
     * @param schoolId 学校主键ID
     * @return List<SchoolTimeInfoDTO>
     */
    public List<SchoolTimeInfoDTO> getSchoolTimeInfoBySchoolId(int schoolId) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.service.sc.WmScSchoolTimeService.getSchoolTimeInfoBySchoolId(int)");
        log.info("[WmScSchoolTimeService.getSchoolTimeInfoBySchoolId] input param: schoolId = {}", schoolId);
        List<SchoolTimeInfoDTO> list = Lists.newArrayList();
        List<WmScSchoolTimeDO> result = wmScSchoolTimeMapper.selectBySchoolId(schoolId);
        if (CollectionUtils.isEmpty(result)) {
            return list;
        }
        return result.stream().map(this::transfer).collect(Collectors.toList());
    }

    /**
     * 根据条件获取学校时间信息
     * @param conditionDTO 学校时间信息查询dto
     * @return map
     */
    public Map<Integer, SchoolTimeInfoDTO> getSchoolTimeInfoByCondition(SchoolTimeQueryConditionDTO conditionDTO) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_1", "com.sankuai.meituan.waimai.customer.service.sc.WmScSchoolTimeService.getSchoolTimeInfoByCondition(com.sankuai.meituan.waimai.thrift.customer.domain.sc.time.SchoolTimeQueryConditionDTO)");
        if (conditionDTO == null) {
            return null;
        }
        WmScSchoolTimeCondition condition = new WmScSchoolTimeCondition();
        condition.setSchoolPrimaryId(conditionDTO.getSchoolPrimaryId());
        condition.setYearBegin(conditionDTO.getYearBegin());
        condition.setYearEnd(conditionDTO.getYearEnd());
        condition.setTerm(conditionDTO.getTerm());
        List<WmScSchoolTimeDO> list = wmScSchoolTimeMapper.selectByCondition(condition);
        if (CollectionUtil.isEmpty(list)) {
            return null;
        }
        Map<Integer, SchoolTimeInfoDTO> map = Maps.newHashMap();
        for (WmScSchoolTimeDO wmScSchoolTimeDO : list) {
            if (wmScSchoolTimeDO == null || wmScSchoolTimeDO.getSchoolPrimaryId() == null) {
                continue;
            }
            map.put(wmScSchoolTimeDO.getSchoolPrimaryId(), transfer(wmScSchoolTimeDO));
        }
        return map;
    }

    /**
     * 根据ID获取指定时间信息
     * @param id
     * @return
     */
    public SchoolTimeInfoDTO getSchoolTimeInfoById(long id) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.service.sc.WmScSchoolTimeService.getSchoolTimeInfoById(long)");
        WmScSchoolTimeDO result = wmScSchoolTimeMapper.selectByPrimaryKey(id);
        if (result == null) {
            return null;
        }
        return transfer(result);
    }

    /**
     * 逻辑删除指定时间记录
     *
     * @param id
     */
    public void deleteSchoolTimeInfoById(Long id, int opUid, String opUname) throws WmSchCantException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_1", "com.sankuai.meituan.waimai.customer.service.sc.WmScSchoolTimeService.deleteSchoolTimeInfoById(java.lang.Long,int,java.lang.String)");
        log.info("deleteSchoolTimeInfoById id={},opUid={},opUname={}", id, opUid, opUname);
        if (id == null) {
            return;
        }
        WmEmploy wmEmploy = wmEmployClient.getEmployById(opUid);
        if (wmEmploy == null) {
            log.info("[学校详情-时间信息]操作人Id不存在于系统，opUid={},opUid={},opUname={}", JSONObject.toJSONString(opUid), opUid, opUname);
            throw new WmSchCantException(BIZ_PARA_ERROR, "操作人Id不存在于系统");
        }
        WmScSchoolTimeDO wmScSchoolTimeDO = wmScSchoolTimeMapper.selectByPrimaryKey(id);
        if (wmScSchoolTimeDO == null) {
            return;
        }
        WmScTimeInfoBo wmScTimeInfoBo = transSchoolTimeDOToBO(wmScSchoolTimeDO);
        String logInfo = wmScLogSchoolInfoService.composeSchoolTimeInfoDeldateLog(wmScTimeInfoBo);
        if (StringUtil.isNotBlank(logInfo)) {
            wmScLogSchoolInfoService.insertScOptLog(OptTypeEnum.DELETE.getType(), SC_SCHOOLTIME_LOG, wmScSchoolTimeDO.getSchoolPrimaryId(),
                    opUid, opUname + "(" + wmEmploy.getMisId() + ")", logInfo, "");
            wmScSchoolTimeMapper.unValidSchoolTimeInfo(id, Long.valueOf(opUid));
        }
    }

    /**
     * 学校时间信息保存V2
     * @param schoolTimeInfoDTO 入参dto
     * @param opUid 用户id
     * @param opUname 用户名称
     * @return 学校时间表中的主键ID
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    public Integer saveSchoolTimeInfo(SchoolTimeInfoDTO schoolTimeInfoDTO, int opUid, String opUname) throws WmSchCantException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_1", "com.sankuai.meituan.waimai.customer.service.sc.WmScSchoolTimeService.saveSchoolTimeInfo(com.sankuai.meituan.waimai.thrift.customer.domain.sc.time.SchoolTimeInfoDTO,int,java.lang.String)");
        log.info("[saveSchoolTimeInfo] schoolTimeInfoDTO={}, opUid={}, opUname={}", JSONObject.toJSONString(schoolTimeInfoDTO), opUid, opUname);
        WmEmploy wmEmploy = wmEmployClient.getEmployById(opUid);
        if (wmEmploy == null) {
            log.info("[学校详情-时间信息]操作人Id不存在于系统，opUid={},opUid={},opUname={}", JSONObject.toJSONString(opUid), opUid, opUname);
            throw new WmSchCantException(BIZ_PARA_ERROR, "操作人ID不存在于系统");
        }
        // 入参check
        schoolTimeInfoCheckService.check(schoolTimeInfoDTO);
        // 对象转换
        WmScSchoolTimeDO wmScSchoolTimeDO = transSchoolTimeInfoDTOToDO(schoolTimeInfoDTO);
        // 信息保存
        int schoolTimePrimaryId = 0;
        if (schoolTimeInfoDTO.getId() == null) {
            // 学校时间信息主键ID为空-新增操作
            WmScTimeInfoBo wmScTimeInfoBo = transSchoolTimeDOToBO(wmScSchoolTimeDO);
            String logInfo = wmScLogSchoolInfoService.composeSchooTimeInfoInserting(wmScTimeInfoBo);
            if (StringUtil.isNotBlank(logInfo)) {
                wmScLogSchoolInfoService.insertScOptLog(OptTypeEnum.INSERT.getType(), SC_SCHOOLTIME_LOG, schoolTimeInfoDTO.getSchoolPrimaryId(),
                        opUid, opUname + "(" + wmEmploy.getMisId() + ")", logInfo, "");
                wmScSchoolTimeDO.setCuid((long) opUid);
                // 带并发锁新增学校时间信息
                schoolTimePrimaryId = insertSchoolTimeWithLock(wmScSchoolTimeDO);
            }
        } else {
            // 学校时间信息主键ID不为空-更新操作
            schoolTimePrimaryId = schoolTimeInfoDTO.getId().intValue();
            WmScSchoolTimeDO preDO = wmScSchoolTimeMapper.selectByPrimaryKey(schoolTimeInfoDTO.getId());
            WmScTimeInfoBo preBo = transSchoolTimeDOToBO(preDO);
            WmScTimeInfoBo aftBo = transSchoolTimeDOToBO(wmScSchoolTimeDO);
            String logInfo = wmScLogSchoolInfoService.composeSchoolTimeInfoUpdateLog(aftBo, preBo);
            if (StringUtil.isNotBlank(logInfo)) {
                wmScLogSchoolInfoService.insertScOptLog(OptTypeEnum.UPDATE.getType(), SC_SCHOOLTIME_LOG, schoolTimeInfoDTO.getSchoolPrimaryId(),
                        opUid, opUname + "(" + wmEmploy.getMisId() + ")", logInfo, "");
                wmScSchoolTimeDO.setMuid((long) opUid);
                wmScSchoolTimeMapper.updateByPrimaryKeySelective(wmScSchoolTimeDO);
            }
        }
        // 返回时间信息主键
        return schoolTimePrimaryId;
    }

    /**
     * 新增学校时间信息(wm_sc_school_time表)
     * @param wmScSchoolTimeDO wmScSchoolTimeDO
     * @return 时间信息主键ID
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    public int insertSchoolTimeWithLock(WmScSchoolTimeDO wmScSchoolTimeDO) throws WmSchCantException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.service.sc.WmScSchoolTimeService.insertSchoolTimeWithLock(com.sankuai.meituan.waimai.customer.domain.sc.WmScSchoolTimeDO)");
        String tairLockKey = SCHOOL_TIME_LOCKKEY_PREFIX
                + wmScSchoolTimeDO.getYearBegin().toString() + "_"
                + wmScSchoolTimeDO.getYearEnd().toString() + "_"
                + wmScSchoolTimeDO.getTerm().toString();
        // [尝试加锁] 若加锁失败代表当前有任务进行中]
        wmScTairService.tryLockWithScene(tairLockKey, WmScTairLockSceneEnum.INSERT_SCHOOL_TIME);

        wmScSchoolTimeMapper.insertSelective(wmScSchoolTimeDO);
        // [尝试解锁] 若解锁失败可三次重试
        wmScTairService.unLockWithScene(tairLockKey, WmScTairLockSceneEnum.INSERT_SCHOOL_TIME);

        return wmScSchoolTimeDO.getId().intValue();
    }

    /**
     * 时间信息DO转BO
     * @param wmScSchoolTimeDO wmScSchoolTimeDO
     * @return WmScTimeInfoBo
     */
    public WmScTimeInfoBo transSchoolTimeDOToBO(WmScSchoolTimeDO wmScSchoolTimeDO) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_1", "com.sankuai.meituan.waimai.customer.service.sc.WmScSchoolTimeService.transSchoolTimeDOToBO(com.sankuai.meituan.waimai.customer.domain.sc.WmScSchoolTimeDO)");
        if (wmScSchoolTimeDO == null) {
            return null;
        }
        WmScTimeInfoBo wmScTimeInfoBo = new WmScTimeInfoBo();
        wmScTimeInfoBo.setId(wmScSchoolTimeDO.getId());
        wmScTimeInfoBo.setSchoolPrimaryId(wmScSchoolTimeDO.getSchoolPrimaryId());
        wmScTimeInfoBo.setYearBegin(wmScSchoolTimeDO.getYearBegin());
        wmScTimeInfoBo.setYearEnd(wmScSchoolTimeDO.getYearEnd());
        wmScTimeInfoBo.setTerm(wmScSchoolTimeDO.getTerm());
        wmScTimeInfoBo.setTermBeginSituation(wmScSchoolTimeDO.getTermBeginSituation());

        wmScTimeInfoBo.setOpenNotPic(wmScSchoolTimeDO.getOpenNotPic());
        wmScTimeInfoBo.setOpenNotPdf(wmScSchoolTimeDO.getOpenNotPdf());
        wmScTimeInfoBo.setOpenNotRemark(wmScSchoolTimeDO.getOpenNotRemark());
        wmScTimeInfoBo.setOpenNotInfoSource(wmScSchoolTimeDO.getOpenNotInfoSource());
        // 开学信息
        if (StringUtil.isNotBlank(wmScSchoolTimeDO.getTermBeginData())) {
            JSONObject jsonObject = JSONObject.parseObject(wmScSchoolTimeDO.getTermBeginData(), JSONObject.class);
            wmScTimeInfoBo.setTermBeginPlan(jsonObject.getInteger(ScFieldConstant.TIME_FIELD_TERM_BEGIN_PLAN));
            wmScTimeInfoBo.setTermBeginTime(jsonObject.getString(ScFieldConstant.TIME_FIELD_TERM_BEGIN_TIME));
            wmScTimeInfoBo.setTermBeginWeek(jsonObject.getString(ScFieldConstant.TIME_FIELD_TERM_BEGIN_WEEK));
            wmScTimeInfoBo.setTermBeginPic(jsonObject.getString(ScFieldConstant.TIME_FIELD_TERM_BEGIN_PIC));
            wmScTimeInfoBo.setTermBeginPdf(jsonObject.getString(ScFieldConstant.TIME_FIELD_TERM_BEGIN_PDF));
            wmScTimeInfoBo.setFirstReturnTime(jsonObject.getString(ScFieldConstant.TIME_FIELD_FIRST_RETURN_TIME));
            wmScTimeInfoBo.setTermBeginRemark(jsonObject.getString(ScFieldConstant.TIME_FIELD_TERM_BEGIN_REMARK));
            wmScTimeInfoBo.setTermBeginInfoSource(jsonObject.getInteger(ScFieldConstant.TIME_FIELD_TERM_BEGIN_INFO_SOURCE));
        }
        // 放假信息
        if (StringUtil.isNotBlank(wmScSchoolTimeDO.getTermEndData())) {
            JSONObject jsonObject = JSONObject.parseObject(wmScSchoolTimeDO.getTermEndData(), JSONObject.class);
            wmScTimeInfoBo.setTermEndPlan(jsonObject.getInteger(ScFieldConstant.TIME_FIELD_TERM_END_PLAN));
            wmScTimeInfoBo.setTermEndTime(jsonObject.getString(ScFieldConstant.TIME_FIELD_TERM_END_TIME));
            wmScTimeInfoBo.setTermEndWeek(jsonObject.getString(ScFieldConstant.TIME_FIELD_TERM_END_WEEK));
            wmScTimeInfoBo.setTermEndPic(jsonObject.getString(ScFieldConstant.TIME_FIELD_TERM_END_PIC));
            wmScTimeInfoBo.setTermEndPdf(jsonObject.getString(ScFieldConstant.TIME_FIELD_TERM_END_PDF));
            wmScTimeInfoBo.setFirstLeaveTime(jsonObject.getString(ScFieldConstant.TIME_FIELD_FIRST_LEAVE_TIME));
            wmScTimeInfoBo.setTermEndRemark(jsonObject.getString(ScFieldConstant.TIME_FIELD_TERM_END_REMARK));
            wmScTimeInfoBo.setTermEndInfoSource(jsonObject.getInteger(ScFieldConstant.TIME_FIELD_TERM_END_INFO_SOURCE));
        }
        return wmScTimeInfoBo;
    }

    /**
     * 时间信息D0转DTO
     * @param wmScSchoolTimeDO 学校时间信息do
     * @return SchoolTimeInfoDTO
     */
    public SchoolTimeInfoDTO transfer(WmScSchoolTimeDO wmScSchoolTimeDO) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.sc.WmScSchoolTimeService.transfer(com.sankuai.meituan.waimai.customer.domain.sc.WmScSchoolTimeDO)");
        if (wmScSchoolTimeDO == null) {
            return null;
        }
        SchoolTimeInfoDTO schoolTimeInfoDTO = new SchoolTimeInfoDTO();
        schoolTimeInfoDTO.setId(wmScSchoolTimeDO.getId());
        schoolTimeInfoDTO.setSchoolPrimaryId(wmScSchoolTimeDO.getSchoolPrimaryId());
        schoolTimeInfoDTO.setYearBegin(wmScSchoolTimeDO.getYearBegin());
        schoolTimeInfoDTO.setYearEnd(wmScSchoolTimeDO.getYearEnd());
        schoolTimeInfoDTO.setTerm(wmScSchoolTimeDO.getTerm());
        SchoolTermEnum schoolTermEnum = SchoolTermEnum.of(wmScSchoolTimeDO.getTerm());
        schoolTimeInfoDTO.setTermDesc(schoolTermEnum.getDesc());
        schoolTimeInfoDTO.setTermBeginSituation(wmScSchoolTimeDO.getTermBeginSituation());
        SchoolTermBeginSituationEnum schoolTermBeginSituationEnum = SchoolTermBeginSituationEnum.of(wmScSchoolTimeDO.getTermBeginSituation());
        schoolTimeInfoDTO.setTermBeginSituationDesc(schoolTermBeginSituationEnum.getDesc());
        schoolTimeInfoDTO.setOpenNotPic(wmScSchoolTimeDO.getOpenNotPic());
        schoolTimeInfoDTO.setOpenNotPdf(wmScSchoolTimeDO.getOpenNotPdf());
        schoolTimeInfoDTO.setOpenNotInfoSource(wmScSchoolTimeDO.getOpenNotInfoSource());
        schoolTimeInfoDTO.setOpenNotRemark(wmScSchoolTimeDO.getOpenNotRemark());
        // 开学信息
        SchoolTermBeginDataDTO schoolTermBeginDataDTO = new SchoolTermBeginDataDTO();
        if (StringUtil.isNotBlank(wmScSchoolTimeDO.getTermBeginData())) {
            schoolTermBeginDataDTO = JSONObject.parseObject(wmScSchoolTimeDO.getTermBeginData(), SchoolTermBeginDataDTO.class);
            SchoolTermBeginPlanEnum schoolTermBeginPlanEnum = SchoolTermBeginPlanEnum.of(schoolTermBeginDataDTO.getTermBeginPlan());
            schoolTermBeginDataDTO.setTermBeginPlanDesc(schoolTermBeginPlanEnum.getDesc());
        }
        schoolTimeInfoDTO.setTermBeginData(schoolTermBeginDataDTO);
        // 放假信息
        SchoolTermEndDataDTO schoolTermEndDataDTO = new SchoolTermEndDataDTO();
        if (StringUtil.isNotBlank(wmScSchoolTimeDO.getTermEndData())) {
            schoolTermEndDataDTO = JSONObject.parseObject(wmScSchoolTimeDO.getTermEndData(), SchoolTermEndDataDTO.class);
            SchoolTermEndPlanEnum schoolTermEndPlanEnum = SchoolTermEndPlanEnum.of(schoolTermEndDataDTO.getTermEndPlan());
            schoolTermEndDataDTO.setTermEndPlanDesc(schoolTermEndPlanEnum.getDesc());
        }
        schoolTimeInfoDTO.setTermEndData(schoolTermEndDataDTO);
        return schoolTimeInfoDTO;
    }

    /**
     * 时间信息DTO转DO
     * @param schoolTimeInfoDTO schoolTimeInfoDTO
     * @return WmScSchoolTimeDO
     */
    public WmScSchoolTimeDO transSchoolTimeInfoDTOToDO(SchoolTimeInfoDTO schoolTimeInfoDTO) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_2", "com.sankuai.meituan.waimai.customer.service.sc.WmScSchoolTimeService.transSchoolTimeInfoDTOToDO(com.sankuai.meituan.waimai.thrift.customer.domain.sc.time.SchoolTimeInfoDTO)");
        if (schoolTimeInfoDTO == null) {
            return null;
        }
        WmScSchoolTimeDO wmScSchoolTimeDO = new WmScSchoolTimeDO();
        wmScSchoolTimeDO.setId(schoolTimeInfoDTO.getId());
        wmScSchoolTimeDO.setSchoolPrimaryId(schoolTimeInfoDTO.getSchoolPrimaryId());
        wmScSchoolTimeDO.setYearBegin(schoolTimeInfoDTO.getYearBegin());
        wmScSchoolTimeDO.setYearEnd(schoolTimeInfoDTO.getYearEnd());
        wmScSchoolTimeDO.setTerm(schoolTimeInfoDTO.getTerm());
        wmScSchoolTimeDO.setTermBeginSituation(schoolTimeInfoDTO.getTermBeginSituation());
        wmScSchoolTimeDO.setOpenNotPic(schoolTimeInfoDTO.getOpenNotPic());
        wmScSchoolTimeDO.setOpenNotPdf(schoolTimeInfoDTO.getOpenNotPdf());
        wmScSchoolTimeDO.setOpenNotInfoSource(schoolTimeInfoDTO.getOpenNotInfoSource());
        wmScSchoolTimeDO.setOpenNotRemark(schoolTimeInfoDTO.getOpenNotRemark());
        if (schoolTimeInfoDTO.getTermBeginSituation().equals(SchoolTermBeginSituationEnum.SCHOOL_OPEN_NOT.getCode())) {
            wmScSchoolTimeDO.setTermBeginData("");
            wmScSchoolTimeDO.setTermEndData("");
        } else {
            // 开学信息
            if (schoolTimeInfoDTO.getTermBeginData() != null) {
                wmScSchoolTimeDO.setTermBeginData(JSONObject.toJSONString(schoolTimeInfoDTO.getTermBeginData()));
            }
            // 放假信息
            if (schoolTimeInfoDTO.getTermEndData() != null) {
                wmScSchoolTimeDO.setTermEndData(JSONObject.toJSONString(schoolTimeInfoDTO.getTermEndData()));
            }
        }
        return wmScSchoolTimeDO;
    }


}
