package com.sankuai.meituan.waimai.customer.service.sc;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Function;
import com.google.common.collect.Lists;
import com.sankuai.meituan.waimai.customer.config.MccScConfig;
import com.sankuai.meituan.waimai.customer.dao.WmCustomerDBMapper;
import com.sankuai.meituan.waimai.customer.dao.sc.*;
import com.sankuai.meituan.waimai.customer.dao.sc.canteenstall.WmCanteenStallManageMapper;
import com.sankuai.meituan.waimai.customer.dao.sc.delivery.WmSchoolDeliveryMetricMapper;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerDB;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerSelectConditionFormDB;
import com.sankuai.meituan.waimai.customer.domain.sc.*;
import com.sankuai.meituan.waimai.customer.domain.sc.canteenstall.WmCanteenStallManageDO;
import com.sankuai.meituan.waimai.customer.domain.sc.canteenstall.WmCanteenStallManageQueryBO;
import com.sankuai.meituan.waimai.customer.domain.sc.delivery.WmSchoolDeliveryMetric;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerPlatformDataParseService;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerService;
import com.sankuai.meituan.waimai.customer.service.kp.WmCustomerKpService;
import com.sankuai.meituan.waimai.customer.service.sc.canteen.sensitive.WmScCanteenSensitiveWordsService;
import com.sankuai.meituan.waimai.customer.service.sc.delivery.WmSchoolDeliveryService;
import com.sankuai.meituan.waimai.customer.service.sc.poi.WmScCanteenPoiAttributeService;
import com.sankuai.meituan.waimai.customer.service.sc.school.sensitive.WmScSchoolSensitiveWordsService;
import com.sankuai.meituan.waimai.customer.util.WmScTransUtil;
import com.sankuai.meituan.waimai.thrift.customer.constant.KpTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.EffectiveStatusEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.kp.WmCustomerKp;
import com.sankuai.meituan.waimai.thrift.customer.domain.sc.*;
import com.sankuai.meituan.waimai.thrift.customer.domain.sc.canteenstall.WmCanteenStallManageDTO;
import com.sankuai.meituan.waimai.thrift.customer.domain.sc.dto.CanteenDTO;
import com.sankuai.meituan.waimai.thrift.customer.domain.sc.dto.WmContractorForWdcResultDTO;
import com.sankuai.meituan.waimai.thrift.customer.domain.sc.dto.WmScCanteenResultDTO;
import com.sankuai.meituan.waimai.thrift.customer.domain.sc.dto.WmScSchoolResultDTO;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import com.sankuai.meituan.waimai.thrift.exception.WmSchCantException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.thrift.TException;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Nullable;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.sankuai.meituan.waimai.thrift.customer.constant.sc.WmScCodeConstants.BIZ_PARA_ERROR;
import static com.sankuai.meituan.waimai.thrift.customer.constant.sc.WmScCodeConstants.SERVER_ERROR;

/**
 * @program: scm
 * @description: 处理对外暴露的方法
 * @author: jianghuimin02
 * @create: 2020-10-14 15:19
 **/
@Slf4j
@Service
public class WmScOuterService {

    @Autowired
    private WmSchoolMapper wmSchoolMapper;

    @Autowired
    private WmCanteenMapper wmCanteenMapper;

    @Autowired
    private WmScCanteenServerService wmScCanteenServerService;

    @Autowired
    private WmCustomerService wmCustomerService;

    @Autowired
    private WmCustomerPlatformDataParseService wmCustomerPlatformDataParseService;

    @Autowired
    private WmCustomerKpService wmCustomerKpService;

    @Autowired
    private WmCustomerDBMapper wmCustomerDBMapper;

    @Autowired
    private WmCanteenStallManageMapper wmCanteenStallManageMapper;

    @Autowired
    private WmScCanteenPoiAttributeMapper wmScCanteenPoiAttributeMapper;

    @Autowired
    private WmScCanteenSensitiveWordsService wmScCanteenSensitiveWordsService;

    @Autowired
    private WmScSchoolSensitiveWordsService wmScSchoolSensitiveWordsService;

    @Autowired
    private WmSchoolDeliveryService wmSchoolDeliveryService;

    @Autowired
    private WmScSchoolService wmScSchoolService;

    @Autowired
    private WmSchoolDeliveryMetricMapper wmSchoolDeliveryMetricMapper;

    @Autowired
    private WmScCanteenPoiAttributeService wmScCanteenPoiAttributeService;

    @Autowired
    private WmSchoolServerService wmSchoolServerService;

    /**
     * 查询门店列表
     * @param wmScOuterIdsBo 参数 食堂/学校/食堂承包商ID(四个参数同时只能传递一个，否则校验不通过)
     * @return 门店ID列表
     * @throws TException org.apache.thrift.TException
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    public List<Long> getWmPoiIdsByScId(WmScOuterIdsBo wmScOuterIdsBo) throws TException, WmSchCantException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.service.sc.WmScOuterService.getWmPoiIdsByScId(com.sankuai.meituan.waimai.thrift.customer.domain.sc.WmScOuterIdsBo)");
        log.info("[WmScOuterService.getWmPoiIdsByScId] input param: wmScOuterIdsBo = {}", JSONObject.toJSONString(wmScOuterIdsBo));
        List<Long> wmPoiIds = Lists.newArrayList();
        if (wmScOuterIdsBo.getCanteenId() > 0) {
            wmPoiIds = getWmPoiIdsByCanteenId(wmScOuterIdsBo.getCanteenId());
            return wmPoiIds;
        }

        if (wmScOuterIdsBo.getContractorId() > 0) {
            wmPoiIds = getWmPoiIdsByContractorId(wmScOuterIdsBo.getContractorId());
            return wmPoiIds;
        }

        if (wmScOuterIdsBo.getSchoolId() > 0) {
            wmPoiIds = getWmPoiIdsBySchoolId(wmScOuterIdsBo.getSchoolId());
            return wmPoiIds;
        }
        return wmPoiIds;
    }

    /**
     * 通过食堂承包商ID查询其下所有食堂ID
     *
     * @param wmScOuterIdsBo 参数 食堂/学校/食堂承包商ID
     *                       四个参数同时只能传递一个，否则校验不通过
     * @return 门店列表
     * @throws TException         TException
     * @throws WmSchCantException WmSchCantException
     */
    public List<Long> getCanteenIdsByContractorId(WmScOuterIdsBo wmScOuterIdsBo) throws TException, WmSchCantException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_1", "com.sankuai.meituan.waimai.customer.service.sc.WmScOuterService.getCanteenIdsByContractorId(com.sankuai.meituan.waimai.thrift.customer.domain.sc.WmScOuterIdsBo)");
        List<Long> canteenIds = Lists.newArrayList();
        if (wmScOuterIdsBo.getContractorId() > 0) {
            // 查询有效的食堂数据
            WmCanteenDB wmCanteenDB = new WmCanteenDB();
            wmCanteenDB.setContractorId(wmScOuterIdsBo.getContractorId());
            wmCanteenDB.setEffective((int) EffectiveStatusEnum.EFFECTIVE.getType());
            List<WmCanteenDB> wmCanteenDBS = wmCanteenMapper.selectCanteenList(wmCanteenDB);
            wmScCanteenSensitiveWordsService.readWhenSelect(wmCanteenDBS);
            if (CollectionUtils.isEmpty(wmCanteenDBS)) {
                return canteenIds;
            }
            canteenIds = wmCanteenDBS.stream().map(wmCanteen -> {
                return (long) wmCanteen.getCanteenId();
            }).collect(Collectors.toList());
        }
        return canteenIds;
    }

    /**
     * 通过学校ID查询其下所有食堂ID
     */
    public List<Long> getCanteenIdsBySchoolId(WmScOuterIdsBo wmScOuterIdsBo) throws TException, WmSchCantException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_2", "com.sankuai.meituan.waimai.customer.service.sc.WmScOuterService.getCanteenIdsBySchoolId(com.sankuai.meituan.waimai.thrift.customer.domain.sc.WmScOuterIdsBo)");
        List<Long> canteenIds = Lists.newArrayList();
        if (wmScOuterIdsBo.getSchoolId() > 0) {
            WmCanteenDB wmCanteenDB = new WmCanteenDB();
            wmCanteenDB.setSchoolId(wmScOuterIdsBo.getSchoolId());
            wmCanteenDB.setEffective((int) EffectiveStatusEnum.EFFECTIVE.getType());
            List<WmCanteenDB> wmCanteenDBS = wmCanteenMapper.selectCanteenList(wmCanteenDB);
            if (CollectionUtils.isEmpty(wmCanteenDBS)) {
                return canteenIds;
            }
            canteenIds = wmCanteenDBS.stream().map(wmCanteen -> {
                return (long) wmCanteen.getCanteenId();
            }).collect(Collectors.toList());
        }
        return canteenIds;
    }


    /**
     * 通过食堂/学校ID获取对应名称和物理城市
     */
    public WmScNcityBo getNameAndCityByScId(WmScOuterIdsBo wmScOuterIdsBo) throws TException, WmSchCantException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.service.sc.WmScOuterService.getNameAndCityByScId(com.sankuai.meituan.waimai.thrift.customer.domain.sc.WmScOuterIdsBo)");
        log.info("getNameAndCityByScId::wmScOuterIdsBo = {}", JSON.toJSONString(wmScOuterIdsBo));
        WmScNcityBo wmScNcityBo = new WmScNcityBo();
        List<Integer> cityIdList = new ArrayList<>();
        List<String> cityNameList = new ArrayList<>();
        //查询食堂
        if (wmScOuterIdsBo.getCanteenId() > 0) {
            WmCanteenDB wmCanteenDB = wmCanteenMapper.selectCanteenById(wmScOuterIdsBo.getCanteenId());
            wmScCanteenSensitiveWordsService.readWhenSelect(wmCanteenDB);
            if (wmCanteenDB == null || wmCanteenDB.getEffective() != EffectiveStatusEnum.EFFECTIVE.getType()) {
                return null;
            }
            wmScNcityBo.setGrade(wmCanteenDB.getGrade());
            wmScNcityBo.setCanteenName(wmCanteenDB.getCanteenName());
            int schoolId = wmCanteenDB.getSchoolId();
            WmSchoolDB wmSchoolDB = wmSchoolMapper.selectSchoolById(schoolId);
            wmScSchoolSensitiveWordsService.readWhenSelect(wmSchoolDB);
            if (wmSchoolDB == null) {
                return null;
            }
            cityIdList.add(wmSchoolDB.getCityId());
            cityNameList.add(wmSchoolDB.getCityName());
        }
        //查询学校
        if (wmScOuterIdsBo.getSchoolId() > 0) {
            WmSchoolDB wmSchoolDB = wmSchoolMapper.selectSchoolById(wmScOuterIdsBo.getSchoolId());
            wmScSchoolSensitiveWordsService.readWhenSelect(wmSchoolDB);
            if (wmSchoolDB == null) {
                return null;
            }
            cityIdList.add(wmSchoolDB.getCityId());
            cityNameList.add(wmSchoolDB.getCityName());
            wmScNcityBo.setSchoolName(wmSchoolDB.getSchoolName());
            wmScNcityBo.setGrade(wmSchoolDB.getGrade());
        }

        //查询承包商
        if (wmScOuterIdsBo.getContractorId() > 0) {
            int grade = computeContractorGrade(wmScOuterIdsBo.getContractorId());
            wmScNcityBo.setGrade(grade);
            List<WmSchoolDB> wmSchoolDBList = wmSchoolMapper.selectSchoolsByContractorId(wmScOuterIdsBo.getContractorId());
            if (CollectionUtils.isEmpty(wmSchoolDBList)) {
                return null;
            }
            try {
                WmCustomerDB wmCustomerDB = wmCustomerService.selectCustomerById(wmScOuterIdsBo.getContractorId());
                if (wmCustomerDB == null) {
                    return null;
                }
                wmScNcityBo.setContractorName(wmCustomerDB.getCustomerName());
                log.info("getNameAndCityByScId::查询的供应商信息wmCustomerDB = {}", JSON.toJSONString(wmCustomerDB));
            } catch (WmCustomerException e) {
                log.info("getNameAndCityByScId::查询客户信息失败 wmScOuterIdsBo = {},code={},err={}", wmScOuterIdsBo, e.getCode(), e.getMessage());
            }
            for (WmSchoolDB wmSchoolDB : wmSchoolDBList) {
                if (!cityIdList.contains(wmSchoolDB.getCityId())) {
                    cityIdList.add(wmSchoolDB.getCityId());
                }
                if (!cityNameList.contains(wmSchoolDB.getCityName())) {
                    cityNameList.add(wmSchoolDB.getCityName());
                }
            }
        }
        wmScNcityBo.setCityIds(cityIdList);
        wmScNcityBo.setCityNames(cityNameList);
        log.info("getNameAndCityByScId::返回结果wmScNcityBo = {}", JSON.toJSONString(wmScNcityBo));
        return wmScNcityBo;
    }

    /**
     * 通过门店ID查询所属食堂/学校/承包商信息
     * @param wmScOuterIdsBo 门店ID
     * @return WmScOuterIdsBo
     * @throws TException org.apache.thrift.TException
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    public WmScOuterIdsBo getScCantSchContIdByWmPoiId(WmScOuterIdsBo wmScOuterIdsBo) throws TException, WmSchCantException {
        log.info("[WmScOuterService.getScCantSchContIdByWmPoiId] input param: wmScOuterIdsBo = {}", JSONObject.toJSONString(wmScOuterIdsBo));
        WmScOuterIdsBo wmScOuterIdReturn = new WmScOuterIdsBo();
        try {
            if (wmScOuterIdsBo.getWmPoiId() <= 0) {
                return wmScOuterIdReturn;
            }

            wmScOuterIdReturn.setWmPoiId(wmScOuterIdsBo.getWmPoiId());
            // 1-从wm_sc_canteen_poi_attribute表查询食堂关联门店
            WmScCanteenPoiAttributeDO wmScCanteenPoiAttributeDO = wmScCanteenPoiAttributeMapper.selectByWmPoiId(wmScOuterIdsBo.getWmPoiId());
            log.info("[WmScOuterService.getScCantSchContIdByWmPoiId] wmScCanteenPoiAttributeDO = {}", JSONObject.toJSONString(wmScCanteenPoiAttributeDO));
            if (wmScCanteenPoiAttributeDO == null) {
                return wmScOuterIdReturn;
            }

            WmCanteenDB wmCanteenDB = wmCanteenMapper.selectCanteenById(wmScCanteenPoiAttributeDO.getCanteenPrimaryId());
            wmScCanteenSensitiveWordsService.readWhenSelect(wmCanteenDB);
            if (wmCanteenDB != null) {
                wmScOuterIdReturn.setCanteenId(wmCanteenDB.getCanteenId());
                WmSchoolDB wmSchoolDB = wmSchoolMapper.selectSchoolById(wmCanteenDB.getSchoolId());
                wmScSchoolSensitiveWordsService.readWhenSelect(wmSchoolDB);
                if (wmSchoolDB != null) {
                    wmScOuterIdReturn.setSchoolId(wmSchoolDB.getSchoolId());
                }
                if (wmCanteenDB.getContractorId() > 0) {
                    WmCustomerDB wmCustomerDB = wmCustomerPlatformDataParseService.getCustomerByIdOrMtCustomerId(wmCanteenDB.getContractorId());
                    if (wmCustomerDB != null) {
                        wmScOuterIdReturn.setContractorId(wmCustomerDB.getMtCustomerId().intValue());
                    }
                }
            }
        } catch (WmCustomerException e) {
            log.info("getScCantSchContIdByWmPoiId::查询客户信息失败 wmScOuterIdsBo = {},code={},errorMsg={}", JSON.toJSONString(wmScOuterIdsBo), e.getCode(), e.getCode());
        }
        log.info("[WmScOuterService.getScCantSchContIdByWmPoiId] output param: wmScOuterIdReturn = {}", JSONObject.toJSONString(wmScOuterIdReturn));
        return wmScOuterIdReturn;
    }

    /**
     * 根据ID查询学校信息
     *
     * @param schoolIdList
     * @return
     */
    public List<WmScOuterIdBatchResultDTO> getSchoolBySchoolIdList(List<Integer> schoolIdList) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_2", "com.sankuai.meituan.waimai.customer.service.sc.WmScOuterService.getSchoolBySchoolIdList(java.util.List)");
        List<WmScOuterIdBatchResultDTO> result = new ArrayList<>();
        WmSchoolDB condition = new WmSchoolDB();
        condition.setSchoolIdList(schoolIdList);
        List<WmSchoolDB> list = wmSchoolMapper.selectSchoolList(condition);
        wmScSchoolSensitiveWordsService.readWhenSelect(list);
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        for (WmSchoolDB from : list) {
            WmScOuterIdBatchResultDTO to = new WmScOuterIdBatchResultDTO();
            to.setId(from.getSchoolId());
            to.setName(from.getSchoolName());
            result.add(to);
        }
        return result;
    }


    /**
     * 根据食堂ID查询食堂信息
     *
     * @param canteenIdList
     * @return
     */
    public List<WmScOuterIdBatchResultDTO> getCanteenByCanteenIdList(List<Integer> canteenIdList) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_2", "com.sankuai.meituan.waimai.customer.service.sc.WmScOuterService.getCanteenByCanteenIdList(java.util.List)");
        List<WmScOuterIdBatchResultDTO> result = new ArrayList<>();
        WmCanteenDB condition = new WmCanteenDB();
        condition.setCanteenIdList(canteenIdList);
        condition.setEffective((int) EffectiveStatusEnum.EFFECTIVE.getType());
        List<WmCanteenDB> list = wmCanteenMapper.selectCanteenList(condition);
        wmScCanteenSensitiveWordsService.readWhenSelect(list);
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        for (WmCanteenDB from : list) {
            WmScOuterIdBatchResultDTO to = new WmScOuterIdBatchResultDTO();
            to.setId(from.getCanteenId());
            to.setName(from.getCanteenName());
            result.add(to);
        }
        return result;
    }

    /**
     * 根据客户ID查询供应商
     *
     * @param contractIdList
     * @return
     */
    public List<WmScOuterIdBatchResultDTO> getContractByContractIdList(List<Integer> contractIdList) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_1", "com.sankuai.meituan.waimai.customer.service.sc.WmScOuterService.getContractByContractIdList(java.util.List)");
        List<WmScOuterIdBatchResultDTO> result = new ArrayList<>();
        try {
            List<Long> contractIdLongList = Lists.transform(contractIdList, new Function<Integer, Long>() {
                @Nullable
                @Override
                public Long apply(@Nullable Integer input) {
                    return input.longValue();
                }
            });
            List<WmCustomerDB> list = wmCustomerPlatformDataParseService.selectCustomerFromPlatformByIdOrMtCustomerId(new HashSet<>(contractIdLongList));
            if (CollectionUtils.isEmpty(list)) {
                return null;
            }
            for (WmCustomerDB from : list) {
                WmScOuterIdBatchResultDTO to = new WmScOuterIdBatchResultDTO();
                to.setId(from.getMtCustomerId().intValue());
                to.setName(from.getCustomerName());
                result.add(to);
            }
        } catch (WmCustomerException e) {
            log.error("getContractByContractIdList::查询客户信息异常，idList = {}，code={},errMsg={}", contractIdList, e.getCode(), e.getMessage());
        }
        return result;
    }

    /**
     * 以学校ID为入口查询门店ID
     * @param schoolId 学校ID
     * @return 关联门店ID列表
     */
    public List<Long> getWmPoiIdsBySchoolId(int schoolId) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_2", "com.sankuai.meituan.waimai.customer.service.sc.WmScOuterService.getWmPoiIdsBySchoolId(int)");
        log.info("[WmScOuterService.getWmPoiIdsBySchoolId] input param: schoolId = {}", JSONObject.toJSONString(schoolId));
        // 查询有效的食堂数据
        WmCanteenDB wmCanteenDB = new WmCanteenDB();
        wmCanteenDB.setSchoolId(schoolId);
        wmCanteenDB.setEffective((int) EffectiveStatusEnum.EFFECTIVE.getType());
        List<WmCanteenDB> wmCanteenDBS = wmCanteenMapper.selectCanteenList(wmCanteenDB);
        wmScCanteenSensitiveWordsService.readWhenSelect(wmCanteenDBS);
        // 查询食堂关联门店ID
        return getWmPoiIdByCanteenList(wmCanteenDBS);
    }

    /**
     * 以承包商ID为入口查询门店ID
     * @param contractorId 承包商ID
     * @return 关联门店ID列表
     */
    public List<Long> getWmPoiIdsByContractorId(int contractorId) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.service.sc.WmScOuterService.getWmPoiIdsByContractorId(int)");
        log.info("[WmScOuterService.getWmPoiIdsByContractorId] input param: contractorId = {}", contractorId);
        // 查询有效的食堂数据
        WmCanteenDB wmCanteenDB = new WmCanteenDB();
        wmCanteenDB.setContractorId(contractorId);
        wmCanteenDB.setEffective((int) EffectiveStatusEnum.EFFECTIVE.getType());
        List<WmCanteenDB> wmCanteenDBS = wmCanteenMapper.selectCanteenList(wmCanteenDB);
        wmScCanteenSensitiveWordsService.readWhenSelect(wmCanteenDBS);
        // 查询食堂绑定的门店ID
        return getWmPoiIdByCanteenList(wmCanteenDBS);
    }

    /**
     * 以食堂ID为入口查询门店ID
     * @param canteenPrimaryId 食堂主键ID
     * @return 关联门店ID列表
     */
    public List<Long> getWmPoiIdsByCanteenId(int canteenPrimaryId) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_1", "com.sankuai.meituan.waimai.customer.service.sc.WmScOuterService.getWmPoiIdsByCanteenId(int)");
        log.info("[WmScOuterService.getWmPoiIdsByCanteenId] input param: canteenPrimaryId = {}", canteenPrimaryId);
        // 从wm_sc_canteen_poi_attribute表查询食堂关联门店
        List<WmScCanteenPoiAttributeDO> canteenPoiAttributeDOList = wmScCanteenPoiAttributeMapper.selectBycanteenPrimaryId(canteenPrimaryId);
        List<Long> wmPoiIdList = canteenPoiAttributeDOList.stream()
                .map(WmScCanteenPoiAttributeDO::getWmPoiId)
                .distinct()
                .collect(Collectors.toList());

        log.info("[WmScOuterService.getWmPoiIdsByCanteenId] wmPoiIdList = {}", JSONObject.toJSONString(wmPoiIdList));
        return wmPoiIdList;
    }

    /**
     * 根据食堂列表查询关联门店ID列表
     * @param wmCanteenDBS 食堂列表
     * @return 门店ID列表
     */
    private List<Long> getWmPoiIdByCanteenList(List<WmCanteenDB> wmCanteenDBS) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_2", "com.sankuai.meituan.waimai.customer.service.sc.WmScOuterService.getWmPoiIdByCanteenList(java.util.List)");
        log.info("[WmScOuterService.getWmPoiIdByCanteenList] input param: wmCanteenDBS = {}", JSONObject.toJSONString(wmCanteenDBS));
        List<Long> wmPoiIds = Lists.newArrayList();
        if (CollectionUtils.isEmpty(wmCanteenDBS)) {
            log.info("[WmScOuterService.getWmPoiIdByCanteenList] wmCanteenDBS is empty, return.");
            return wmPoiIds;
        }

        List<Long> canteenPrimaryIds = wmCanteenDBS.stream()
                .map(wmCanteen -> (long) wmCanteen.getId())
                .distinct()
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(canteenPrimaryIds)) {
            log.info("[WmScOuterService.getWmPoiIdByCanteenList] canteenPrimaryIds is empty, return.");
            return wmPoiIds;
        }

        // 从wm_sc_canteen_poi_attribute表查询食堂关联门店
        List<Long> wmPoiIdList = wmScCanteenPoiAttributeMapper.selectWmPoiIdListByCanteenPrimaryIdList(canteenPrimaryIds);
        log.info("[WmScOuterService.getWmPoiIdByCanteenList] wmPoiIdList = {}", JSONObject.toJSONString(wmPoiIdList));

        return wmPoiIdList;
    }

    /**
     * 计算承包商等级
     */
    private int computeContractorGrade(int contractorId) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.service.sc.WmScOuterService.computeContractorGrade(int)");
        return wmScCanteenServerService.computeContractorGrade(contractorId);
    }

    /**
     * 校验参数
     * 校园ID/学校ID/客户ID->校园主键ID/学校主键ID/客户主键ID
     */
    public void checkQueryId(WmScOuterIdsBo wmScOuterIdsBo) throws WmSchCantException {
        try {
            int zeroNum = 0;
            if (wmScOuterIdsBo.getCanteenId() > 0) {
                WmCanteenDB wmCanteenDB = new WmCanteenDB();
                wmCanteenDB.setCanteenId(wmScOuterIdsBo.getCanteenId());
                List<WmCanteenDB> list = wmCanteenMapper.selectCanteenList(wmCanteenDB);
                wmScCanteenSensitiveWordsService.readWhenSelect(list);
                if (CollectionUtils.isEmpty(list)) {
                    throw new WmSchCantException(BIZ_PARA_ERROR, "食堂ID不存在");
                }
                wmScOuterIdsBo.setCanteenId(list.get(0).getId());
                zeroNum++;
            }
            if (wmScOuterIdsBo.getSchoolId() > 0) {
                WmSchoolDB wmSchoolDB = wmSchoolMapper.selectSchoolBySchoolId(wmScOuterIdsBo.getSchoolId());
                wmScSchoolSensitiveWordsService.readWhenSelect(wmSchoolDB);
                if (wmSchoolDB == null) {
                    throw new WmSchCantException(BIZ_PARA_ERROR, "学校ID不存在");
                }
                wmScOuterIdsBo.setSchoolId(wmSchoolDB.getId());
                zeroNum++;
            }
            if (wmScOuterIdsBo.getContractorId() > 0) {
                WmCustomerDB wmCustomerDB = wmCustomerPlatformDataParseService.getCustomerByIdOrMtCustomerId(wmScOuterIdsBo.getContractorId());
                if (wmCustomerDB == null) {
                    throw new WmSchCantException(BIZ_PARA_ERROR, "客户ID不存在");
                }
                wmScOuterIdsBo.setContractorId(wmCustomerDB.getId());
                zeroNum++;
            }
            if (wmScOuterIdsBo.getWmPoiId() > 0) {
                zeroNum++;
            }

            if (zeroNum == 0) {
                throw new WmSchCantException(BIZ_PARA_ERROR, "参数至少输入一个大于0的值");
            }
            if (zeroNum > 1) {
                throw new WmSchCantException(BIZ_PARA_ERROR, "只能输入一个参数");
            }
        } catch (WmCustomerException e) {
            log.error("checkQueryId::客户信息查询失败wmScOuterIdsBo = {},code={},errorMsg={}", JSON.toJSONString(wmScOuterIdsBo), e.getCode(), e.getMessage());
        }
    }

    /**
     * 校验参数
     */
    public void checkQueryParam(WmScOuterIdBatchParamDTO dto) throws WmSchCantException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.service.sc.WmScOuterService.checkQueryParam(com.sankuai.meituan.waimai.thrift.customer.domain.sc.WmScOuterIdBatchParamDTO)");
        if (dto == null) {
            throw new WmSchCantException(BIZ_PARA_ERROR, "参数未传");

        }
        if (dto.getIdType() == null) {
            throw new WmSchCantException(BIZ_PARA_ERROR, "ID类型参数未传");
        }
        if (CollectionUtils.isEmpty(dto.getIdList())) {
            throw new WmSchCantException(BIZ_PARA_ERROR, "ID未传");
        }
        if (dto.getIdList().size() > 500) {
            throw new WmSchCantException(BIZ_PARA_ERROR, "ID最多传500个");
        }
        if (dto.getIdList().contains(null)) {
            throw new WmSchCantException(BIZ_PARA_ERROR, "ID中存在null值");
        }
        if (dto.getIdList().contains(0)) {
            throw new WmSchCantException(BIZ_PARA_ERROR, "ID中存在0值");
        }
    }


    /**
     * 根据学校ID批量查询学校信息 for wdc
     *
     * @param schoolIdList 学校业务ID集合
     * @return
     * @throws WmSchCantException
     */
    public List<WmScSchoolResultDTO> getScSchoolByIdList(List<Integer> schoolIdList, boolean isMaster) throws WmSchCantException {
        List<WmScSchoolResultDTO> result = new ArrayList<>();
        if (CollectionUtils.isEmpty(schoolIdList)) {
            return result;
        }
        if (schoolIdList.size() > 50) {
            throw new WmSchCantException(BIZ_PARA_ERROR, "ID最多传50个");
        }
        if (schoolIdList.contains(null)) {
            throw new WmSchCantException(BIZ_PARA_ERROR, "ID中存在null值");
        }
        WmSchoolSearchCondition condition = new WmSchoolSearchCondition();
        condition.setSchoolIdList(schoolIdList);
        List<WmSchoolDB> list = null;
        if (isMaster) {
            list = wmSchoolMapper.selectByConditionMaster(condition);
        } else {
            list = wmSchoolMapper.selectByCondition(condition);
        }
        wmScSchoolSensitiveWordsService.readWhenSelect(list);
        if (CollectionUtils.isEmpty(list)) {
            return result;
        }

        list.parallelStream().forEach(one -> {
            WmScSchoolResultDTO oneResult = new WmScSchoolResultDTO();
            BeanUtils.copyProperties(one, oneResult);
            //0是供应链侧的默认值，从公私海拿过来的默认值是null所以返回去保持原默认值
            if (one.getTeaStuNum() <= 0) {
                oneResult.setTeaStuNum(null);
            }
            if (one.getDeliveryStatus() <= 0) {
                oneResult.setDeliveryStatus(null);
            }
            if (one.getOutDeliveryIn() <= 0) {
                oneResult.setOutDeliveryIn(null);
            }
            if (one.getSiteId() <= 0) {
                oneResult.setSiteId(null);
            }

            if (MccScConfig.getScOutSetOtherSchoolInfo()) {
                setSchoolOtherInfo(oneResult);
            }

            result.add(oneResult);
        });

        return result;
    }

    public void setSchoolOtherInfo(WmScSchoolResultDTO oneResult) {
        log.info("[WmScOuterService.setSchoolOtherInfo] input param: oneResult = {}", JSONObject.toJSONString(oneResult));
        List<WmSchoolDeliveryMetric> wmSchoolDeliveryMetrics = wmSchoolDeliveryMetricMapper.selectBySchoolId((long) oneResult.getSchoolId());
        if (CollectionUtils.isNotEmpty(wmSchoolDeliveryMetrics)) {
            WmSchoolDeliveryMetric metric = wmSchoolDeliveryMetrics.get(0);
            oneResult.setPeakStallNumOfMonth(metric.getPeakStallNumOfMonth());
            oneResult.setPeakOrderNumOfMonth(metric.getPeakOrderNumOfMonth());
            oneResult.setOnlineStallNum(metric.getPoiNumOpen());
        }
        //档口数
        SchoolStallNumDO schoolStallNumDO = wmSchoolServerService.getSchoolStallNum(oneResult.getId());
        oneResult.setSchoolStallNum(schoolStallNumDO.getSchoolStallNum());
        oneResult.setCanyinSchoolStallNum(schoolStallNumDO.getCanyinSchoolStallNum());
        oneResult.setSchoolOfflineBizStallNum(schoolStallNumDO.getSchoolOfflineBizStallNum());
        oneResult.setCanyinOfflineBizStallNum(schoolStallNumDO.getCanyinOfflineBizStallNum());
        log.info("[WmScOuterService.setSchoolOtherInfo] output: oneResult = {}", JSONObject.toJSONString(oneResult));
    }

    /**
     * 根据学校主键ID批量查询学校信息 for CRM数据权限系统
     *
     * @param schoolPrimaryIdList 学校业务ID集合
     * @return 学校信息列表
     * @throws TException         org.apache.thrift.TException
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    public List<WmScSchoolResultDTO> getSchoolListBySchoolPrimaryIdList(List<Integer> schoolPrimaryIdList) throws TException, WmSchCantException {
        log.info("[WmScOuterService.getSchoolListBySchoolPrimaryIdList] input param: schoolPrimaryIdList = {}", JSONObject.toJSONString(schoolPrimaryIdList));
        List<WmScSchoolResultDTO> result = new ArrayList<>();
        if (CollectionUtils.isEmpty(schoolPrimaryIdList)) {
            return result;
        }

        if (schoolPrimaryIdList.size() > MccScConfig.getMaxSchoolPrimaryIdListSize()) {
            throw new WmSchCantException(BIZ_PARA_ERROR, "学校主键ID最多传" + MccScConfig.getMaxSchoolPrimaryIdListSize() + "个");
        }

        if (schoolPrimaryIdList.contains(null)) {
            throw new WmSchCantException(BIZ_PARA_ERROR, "学校主键ID中存在null值");
        }

        WmSchoolSearchCondition condition = new WmSchoolSearchCondition();
        condition.setIdList(schoolPrimaryIdList);
        List<WmSchoolDB> wmSchoolDBList = wmSchoolMapper.selectByCondition(condition);
        wmScSchoolSensitiveWordsService.readWhenSelect(wmSchoolDBList);
        if (CollectionUtils.isEmpty(wmSchoolDBList)) {
            return result;
        }

        for (WmSchoolDB wmSchoolDB : wmSchoolDBList) {
            WmScSchoolResultDTO wmScSchoolResultDTO = new WmScSchoolResultDTO();
            BeanUtils.copyProperties(wmSchoolDB, wmScSchoolResultDTO);
            result.add(wmScSchoolResultDTO);
        }
        log.info("[WmScOuterService.getSchoolListBySchoolPrimaryIdList] result = {}", JSONObject.toJSONString(result));
        return result;
    }

    /**
     * 根据档口管理ID列表查询食堂档口绑定任务列表 for CRM数据权限系统
     * @param manageIdList 档口管理任务ID列表
     * @return 食堂档口管理任务列表
     * @throws TException org.apache.thrift.TException
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    public List<WmCanteenStallManageDTO> getCanteenStallManageListByManageIdList(List<Integer> manageIdList) throws TException, WmSchCantException {
        log.info("[WmScOuterService.getCanteenStallManageListByManageIdList] input param: manageIdList = {}", JSONObject.toJSONString(manageIdList));
        List<WmCanteenStallManageDTO> manageDTOList = new ArrayList<>();
        if (CollectionUtils.isEmpty(manageIdList)) {
            return manageDTOList;
        }

        if (manageIdList.size() > MccScConfig.getMaxCanteenStallManageIdListSize()) {
            throw new WmSchCantException(BIZ_PARA_ERROR, "食堂档口管理任务ID最多传" + MccScConfig.getMaxCanteenStallManageIdListSize() + "个");
        }

        List<WmCanteenStallManageDO> manageDOList = wmCanteenStallManageMapper.selectByPrimaryIdList(manageIdList);
        manageDTOList = WmScTransUtil.transCanteenStallManageDOsToDTOs(manageDOList);

        log.info("[WmScOuterService.getCanteenStallManageListByManageIdList] manageDTOList = {}", JSONObject.toJSONString(manageDTOList));
        return manageDTOList;
    }

    /**
     * 查询承包商信息 for wdc
     *
     * @param customerIdList 客户业务ID集合
     * @return
     * @throws WmSchCantException
     * @throws WmCustomerException
     */
    public List<WmContractorForWdcResultDTO> getScContractorForWdcByIdList(List<Long> customerIdList) throws WmSchCantException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_2", "com.sankuai.meituan.waimai.customer.service.sc.WmScOuterService.getScContractorForWdcByIdList(java.util.List)");
        log.info("getScContractorForWdcByIdList::customerIdList = {}", JSON.toJSONString(customerIdList));
        List<WmContractorForWdcResultDTO> result = new ArrayList<>();
        try {
            if (CollectionUtils.isEmpty(customerIdList)) {
                return result;
            }
            if (customerIdList.size() > 50) {
                throw new WmSchCantException(BIZ_PARA_ERROR, "ID最多传50个");
            }
            if (customerIdList.contains(null)) {
                throw new WmSchCantException(BIZ_PARA_ERROR, "ID中存在null值");
            }

            List<WmCustomerDB> list = wmCustomerPlatformDataParseService.selectCustomerFromPlatformByMtCustomerId(new HashSet<>(customerIdList));
            if (CollectionUtils.isEmpty(list)) {
                return result;
            }
            for (WmCustomerDB wmCustomerDB : list) {
                WmContractorForWdcResultDTO oneResult = new WmContractorForWdcResultDTO();
                BeanUtils.copyProperties(wmCustomerDB, oneResult);
                result.add(oneResult);
            }
            List<Integer> customerPrimaryIdList = Lists.transform(list, new Function<WmCustomerDB, Integer>() {
                @Nullable
                @Override
                public Integer apply(@Nullable WmCustomerDB input) {
                    return input.getId();
                }
            });
            List<WmCustomerKp> kpList = wmCustomerKpService.getCustomerKp(customerPrimaryIdList, (int) KpTypeEnum.VISITKP.getType(), true);
            if (CollectionUtils.isEmpty(kpList)) {
                return result;
            }
            for (WmContractorForWdcResultDTO one : result) {
                for (WmCustomerKp kp : kpList) {
                    if (one.getId() != kp.getCustomerId()) {
                        continue;
                    }
                    if (kp.getWdcClueId() == null || kp.getWdcClueId() <= 0l) {
                        continue;
                    }
                    one.setKpType(kp.getKpType());
                    one.setCompellation(kp.getCompellation());
                    one.setPhoneNum(kp.getPhoneNum());
                    one.setEmail(kp.getEmail());
                    one.setVisitKPPro(kp.getVisitKPPro());
                    one.setUtimeKp(kp.getUtime());
                    break;
                }
            }
        } catch (WmCustomerException e) {
            log.error("getScContractorForWdcByIdList::customerIdList = {},code={},msg={}", customerIdList, e.getCode(), e.getMessage(), e);
        }
        return result;
    }

    /**
     * 通过线索Id（可以包含学校线索和承包商线索）查询食堂信息 for wdc
     *
     * @param wdcClueIdList 学校线索id
     * @return
     * @throws WmSchCantException
     * @throws WmCustomerException
     */
    public Map<Long, List<CanteenDTO>> getScCanteenForWdcByClueIdList(List<Long> wdcClueIdList) throws WmSchCantException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.service.sc.WmScOuterService.getScCanteenForWdcByClueIdList(java.util.List)");
        log.info("getScCanteenForWdcByClueIdList::wdcClueIdList = {}", JSON.toJSONString(wdcClueIdList));
        Map<Long, List<CanteenDTO>> result = new HashMap<>();
        if (CollectionUtils.isEmpty(wdcClueIdList)) {
            return result;
        }
        if (wdcClueIdList.size() > 50) {
            throw new WmSchCantException(BIZ_PARA_ERROR, "ID最多传50个");
        }
        if (wdcClueIdList.contains(null)) {
            throw new WmSchCantException(BIZ_PARA_ERROR, "ID中存在null值");
        }
        //通过线索id查询学校列表
        WmSchoolSearchCondition schoolCondition = new WmSchoolSearchCondition();
        schoolCondition.setValid(1);
        schoolCondition.setWdcClueIdList(wdcClueIdList);
        List<WmSchoolDB> schoollist = wmSchoolMapper.selectByCondition(schoolCondition);
        wmScSchoolSensitiveWordsService.readWhenSelect(schoollist);
        //通过线索id查询承包商列表
        WmCustomerSelectConditionFormDB customerCondition = new WmCustomerSelectConditionFormDB();
        customerCondition.setValid(1);
        customerCondition.setWdcClueIdsList(wdcClueIdList);
        List<WmCustomerDB> customerList = wmCustomerDBMapper.selectByCondition(customerCondition);

        if (CollectionUtils.isEmpty(schoollist) && CollectionUtils.isEmpty(customerList)) {
            return result;
        }
        List<WmCanteenDB> wmCanteenDBList = new ArrayList<>();
        //通过学校Id或者承包商Id查询食堂信息
        WmCanteenSearchCondition canteenCondition = new WmCanteenSearchCondition();
        List<Integer> schoolIdList = new ArrayList<>();
        for (WmSchoolDB wmSchoolDB : schoollist) {
            schoolIdList.add(wmSchoolDB.getId());
        }
        List<Integer> customerIdList = new ArrayList<>();
        for (WmCustomerDB wmCustomerDB : customerList) {
            customerIdList.add(wmCustomerDB.getId());
        }
        canteenCondition.setValid(1);
        canteenCondition.setEffective(1);
        canteenCondition.setSchoolPrimaryIdList(schoolIdList);
        List<WmCanteenDB> wmCanteenDBWithSchoolList = wmCanteenMapper.selectByCondition(canteenCondition);
        wmScCanteenSensitiveWordsService.readWhenSelect(wmCanteenDBWithSchoolList);

        canteenCondition.setSchoolPrimaryIdList(null);
        canteenCondition.setCustomerPrimaryIdList(customerIdList);
        List<WmCanteenDB> wmCanteenDBWithCustomerList = wmCanteenMapper.selectByCondition(canteenCondition);
        wmScCanteenSensitiveWordsService.readWhenSelect(wmCanteenDBWithCustomerList);

        for (WmSchoolDB wmSchoolDB : schoollist) {
            List<CanteenDTO> canteenDTOList = new ArrayList<>();
            if (CollectionUtils.isEmpty(wmCanteenDBList)) {
                result.put(wmSchoolDB.getWdcClueId(), canteenDTOList);
            }
            for (WmCanteenDB wmCanteenDB : wmCanteenDBWithSchoolList) {
                if (wmCanteenDB.getSchoolId().equals(wmSchoolDB.getId())) {
                    CanteenDTO canteenDTO = new CanteenDTO();
                    canteenDTO.setCanteenId(wmCanteenDB.getCanteenId());
                    canteenDTO.setCanteenName(wmCanteenDB.getCanteenName());
                    canteenDTO.setStallNum(wmCanteenDB.getStallNum());
                    canteenDTOList.add(canteenDTO);
                }
            }
            result.put(wmSchoolDB.getWdcClueId(), canteenDTOList);
        }
        for (WmCustomerDB wmCustomerDB : customerList) {
            List<CanteenDTO> canteenDTOList = new ArrayList<>();
            if (CollectionUtils.isEmpty(wmCanteenDBList)) {
                result.put(wmCustomerDB.getWdcClueId(), canteenDTOList);
            }
            for (WmCanteenDB wmCanteenDB : wmCanteenDBWithCustomerList) {
                if (wmCanteenDB.getContractorId().equals(wmCustomerDB.getId())) {
                    CanteenDTO canteenDTO = new CanteenDTO();
                    canteenDTO.setCanteenId(wmCanteenDB.getCanteenId());
                    canteenDTO.setCanteenName(wmCanteenDB.getCanteenName());
                    canteenDTO.setStallNum(wmCanteenDB.getStallNum());
                    canteenDTOList.add(canteenDTO);
                }
            }
            result.put(wmCustomerDB.getWdcClueId(), canteenDTOList);
        }

        return result;
    }

    /**
     * 根据批量门店ID查询关联的食堂/学校/承包商信息
     * @param wmPoiIdList 门店ID列表
     * @return 关联的食堂/学校/承包商信息
     * @throws TException org.apache.thrift.TException
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    public List<WmScOuterPoiRelatedInfoDTO> getWmPoiRelatedInfoByWmPoiIdList(List<Long> wmPoiIdList) throws TException, WmSchCantException {
        log.info("[WmScOuterService.getWmPoiRelatedInfoByWmPoiIdList] input param: wmPoiIdList = {}", JSONObject.toJSONString(wmPoiIdList));
        List<WmScOuterPoiRelatedInfoDTO> resList = new ArrayList<>();
        if (CollectionUtils.isEmpty(wmPoiIdList)) {
            log.info("[WmScOuterService.getWmPoiRelatedInfoByWmPoiIdList] wmPoiIdList is empty, return.");
            return resList;
        }

        if (wmPoiIdList.size() > MccScConfig.getMaxBatchWmPoiIdListSize()) {
            log.warn("[WmScOuterService.getWmPoiRelatedInfoByWmPoiIdList] wmPoiIdList size bigger than max. size = {}", wmPoiIdList.size());
            throw new WmSchCantException(BIZ_PARA_ERROR, "门店ID批量最大查询个数限制为" + MccScConfig.getMaxBatchWmPoiIdListSize() + "个");
        }

        try {
            // 1-食堂ID与门店ID关系(从wm_sc_canteen_poi_attribute表查询)
            List<WmScCanteenPoiAttributeDO> wmScCanteenPoiAttributeDOList = wmScCanteenPoiAttributeMapper.selectByWmPoiIdList(wmPoiIdList);
            log.info("[WmScOuterService.getWmPoiRelatedInfoByWmPoiIdList] wmScCanteenPoiAttributeDOList = {}", JSONObject.toJSONString(wmScCanteenPoiAttributeDOList));
            if (CollectionUtils.isEmpty(wmScCanteenPoiAttributeDOList)) {
                log.info("[WmScOuterService.getWmPoiRelatedInfoByWmPoiIdList] wmScCanteenPoiAttributeDOList is empty, return.");
                return resList;
            }

            // canteenMap: key->食堂主键ID, val->List<WmScOuterPoiRelatedInfoDTO>
            Map<Integer, List<WmScOuterPoiRelatedInfoDTO>> canteenMap = new HashMap<>();
            for (WmScCanteenPoiAttributeDO attributeDO : wmScCanteenPoiAttributeDOList) {
                WmScOuterPoiRelatedInfoDTO infoDTO = new WmScOuterPoiRelatedInfoDTO();
                infoDTO.setWmPoiId(attributeDO.getWmPoiId());
                canteenMap.computeIfAbsent(attributeDO.getCanteenPrimaryId(), key -> new ArrayList<>()).add(infoDTO);
                resList.add(infoDTO);
            }

            // [1] 组装门店关联食堂信息(食堂ID+食堂名称)
            List<WmCanteenDB> wmCanteenDBList = composePoiRelatedCanteenInfoNew(wmScCanteenPoiAttributeDOList, canteenMap);

            // [2] 组装门店关联学校信息(学校ID+学校名称)
            composePoiRelatedSchoolInfo(wmCanteenDBList, canteenMap);

            // [3] 组装门店关联承包商信息(承包商ID+承包商名称)
            composePoiRelatedContractorInfo(wmCanteenDBList, canteenMap);
            log.info("[WmScOuterService.getWmPoiRelatedInfoByWmPoiIdList] resList = {}", JSONObject.toJSONString(resList));
            return resList;
        } catch (WmCustomerException customerException) {
            log.error("[WmScOuterService.getWmPoiRelatedInfoByWmPoiIdList] WmCustomerException. wmPoiIdList = {}",
                    JSONObject.toJSONString(wmPoiIdList), customerException);
            throw new WmSchCantException(SERVER_ERROR, "查询食堂承包商信息失败，请稍后重试");
        }
    }

    /**
     * 组装门店关联食堂信息(食堂ID+食堂名称)
     * @param wmScCanteenPoiAttributeDOList wmScCanteenPoiAttributeDOList
     * @param canteenMap canteenMap
     * @return List<WmCanteenDB> 食堂信息列表
     */
    private List<WmCanteenDB> composePoiRelatedCanteenInfoNew(List<WmScCanteenPoiAttributeDO> wmScCanteenPoiAttributeDOList,
                                                           Map<Integer, List<WmScOuterPoiRelatedInfoDTO>> canteenMap) {
        // 食堂主键ID列表
        List<Integer> canteenPrimaryIdList = wmScCanteenPoiAttributeDOList.stream()
                .map(WmScCanteenPoiAttributeDO::getCanteenPrimaryId)
                .collect(Collectors.toList());
        List<WmCanteenDB> wmCanteenDBList = wmCanteenMapper.selectCanteensByIds(canteenPrimaryIdList);
        // 设置食堂ID与食堂名称
        for (WmCanteenDB canteenDB : wmCanteenDBList) {
            List<WmScOuterPoiRelatedInfoDTO> infoDTOList = canteenMap.get(canteenDB.getId());
            infoDTOList.forEach(infoDTO -> {
                infoDTO.setCanteenId(canteenDB.getCanteenId());
                infoDTO.setCanteenName(canteenDB.getCanteenName());
            });
        }

        return wmCanteenDBList;
    }

    /**
     * 组装门店关联学校信息(学校ID+学校名称)
     * @param wmCanteenDBList wmCanteenDBList
     * @param canteenMap      canteenMap
     */
    private void composePoiRelatedSchoolInfo(List<WmCanteenDB> wmCanteenDBList,
                                             Map<Integer, List<WmScOuterPoiRelatedInfoDTO>> canteenMap) {
        // Map: key->学校主键ID, List<WmScOuterPoiRelatedInfoDTO>
        Map<Integer, List<WmScOuterPoiRelatedInfoDTO>> schoolMap = new HashMap<>();
        wmCanteenDBList.forEach(canteenDB -> {
            List<WmScOuterPoiRelatedInfoDTO> infoDTOList = canteenMap.get(canteenDB.getId());
            schoolMap.computeIfAbsent(canteenDB.getSchoolId(), key -> new ArrayList<>()).addAll(infoDTOList);
        });
        // 学校主键ID列表
        List<Integer> schoolPrimaryIdList = wmCanteenDBList.stream()
                .map(WmCanteenDB::getSchoolId)
                .collect(Collectors.toList());
        WmSchoolSearchCondition condition = new WmSchoolSearchCondition();
        condition.setIdList(schoolPrimaryIdList);
        List<WmSchoolDB> wmSchoolDBList = wmSchoolMapper.selectByCondition(condition);
        // 设置学校ID与学校名称
        for (WmSchoolDB wmSchoolDB : wmSchoolDBList) {
            List<WmScOuterPoiRelatedInfoDTO> infoDTOList = schoolMap.get(wmSchoolDB.getId());
            infoDTOList.forEach(infoDTO -> {
                infoDTO.setSchoolId(wmSchoolDB.getSchoolId());
                infoDTO.setSchoolName(wmSchoolDB.getSchoolName());
            });
        }
    }

    /**
     * 组装门店关联承包商信息(承包商ID+承包商名称)
     * @param wmCanteenDBList wmCanteenDBList
     * @param canteenMap      canteenMap
     * @throws WmCustomerException com.sankuai.meituan.waimai.thrift.exception.WmCustomerException
     */
    public void composePoiRelatedContractorInfo(List<WmCanteenDB> wmCanteenDBList,
                                                Map<Integer, List<WmScOuterPoiRelatedInfoDTO>> canteenMap) throws WmCustomerException {
        // 食堂承包商ID列表
        List<Long> customerIdList = wmCanteenDBList.stream()
                .filter(canteenDB -> canteenDB.getContractorId() > 0)
                .map(canteenDB -> canteenDB.getContractorId().longValue())
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(customerIdList)) {
            log.info("[WmScOuterService.composePoiRelatedContractorInfo] customerIdList is empty, return. wmCanteenDBList = {}",
                    JSONObject.toJSONString(wmCanteenDBList));
            return;
        }
        // Map: key->客户ID, val->WmScOuterPoiRelatedInfoDTO
        Map<Integer, List<WmScOuterPoiRelatedInfoDTO>> customerMap = new HashMap<>();
        wmCanteenDBList.forEach(canteenDB -> {
            List<WmScOuterPoiRelatedInfoDTO> infoDTOList = canteenMap.get(canteenDB.getId());
            customerMap.computeIfAbsent(canteenDB.getContractorId(), key -> new ArrayList<>()).addAll(infoDTOList);
        });
        // 设置承包商ID与承包商名称
        List<WmCustomerDB> wmCustomerDBList = wmCustomerPlatformDataParseService.selectCustomerFromPlatformByIdOrMtCustomerId(new HashSet<>(customerIdList));
        for (WmCustomerDB wmCustomerDB : wmCustomerDBList) {
            List<WmScOuterPoiRelatedInfoDTO> infoDTOList = customerMap.get(wmCustomerDB.getId());
            infoDTOList.forEach(infoDTO -> {
                infoDTO.setContractorId(wmCustomerDB.getMtCustomerId());
                infoDTO.setContractorName(wmCustomerDB.getCustomerName());
            });
        }
    }

    /**
     * 根据用户UID列表查询作为学校责任人的学校主键ID列表 for CRM数据权限系统
     * @param uidList 用户UID列表
     * @return 作为学校责任人的学校主键ID列表
     * @throws TException         org.apache.thrift.TException
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    public List<Integer> getSchoolPrimaryIdListByUidList(List<Integer> uidList) throws TException, WmSchCantException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.service.sc.WmScOuterService.getSchoolPrimaryIdListByUidList(java.util.List)");
        log.info("[WmScOuterService.getSchoolPrimaryIdListByUidList] input param: uidList = {}", JSONObject.toJSONString(uidList));
        if (CollectionUtils.isEmpty(uidList)) {
            log.error("[WmScOuterService.getSchoolPrimaryIdListByUidList] uidList is empty, return. uidList = {}", JSONObject.toJSONString(uidList));
            throw new WmSchCantException(BIZ_PARA_ERROR, "用户UID列表为空");
        }

        List<Integer> schoolPrimaryIdList = new ArrayList<>();
        List<List<Integer>> uidListPart = Lists.partition(uidList, MccScConfig.getSchoolQueryPartitionLimit());
        for (List<Integer> listPart : uidListPart) {
            List<Integer> schoolPrimaryIdListPart = wmSchoolMapper.selectSchoolPrimaryIdListByResponsibleUidList(listPart);
            schoolPrimaryIdList.addAll(schoolPrimaryIdListPart);
        }

        log.info("[WmScOuterService.getSchoolPrimaryIdListByUidList] schoolPrimaryIdList = {}", JSONObject.toJSONString(schoolPrimaryIdList));
        return schoolPrimaryIdList;
    }

    /**
     * 根据用户UID列表查询作为食堂承包商责任人的承包商ID列表 for CRM数据权限系统
     * @param uidList 用户UID列表
     * @return 作为食堂承包商责任人的承包商ID列表
     * @throws TException         org.apache.thrift.TException
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    public List<Integer> getContractorIdListByUidList(List<Integer> uidList) throws TException, WmSchCantException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_1", "com.sankuai.meituan.waimai.customer.service.sc.WmScOuterService.getContractorIdListByUidList(java.util.List)");
        log.info("[WmScOuterService.getContractorIdListByUidList] input param: uidList = {}", JSONObject.toJSONString(uidList));
        if (CollectionUtils.isEmpty(uidList)) {
            log.error("[WmScOuterService.getContractorIdListByUidList] uidList is empty, return. uidList = {}", JSONObject.toJSONString(uidList));
            throw new WmSchCantException(BIZ_PARA_ERROR, "用户UID列表为空");
        }

        List<Integer> contractorIdList = new ArrayList<>();
        List<List<Integer>> uidListPart = Lists.partition(uidList, MccScConfig.getContractorQueryPartitionLimit());
        for (List<Integer> listPart : uidListPart) {
            List<Integer> contractorIdListPart = wmCustomerDBMapper.selectContractorIdListByOwnerUidList(listPart);
            contractorIdList.addAll(contractorIdListPart);
        }

        log.info("[WmScOuterService.getContractorIdListByUidList] contractorIdList = {}", JSONObject.toJSONString(contractorIdList));
        return contractorIdList;
    }

    /**
     * 根据食堂主键ID列表查询食堂信息列表 for CRM数据权限系统
     * @param canteenPrimaryIdList 食堂主键ID列表
     * @return 食堂信息列表
     * @throws TException         org.apache.thrift.TException
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    public List<WmScCanteenResultDTO> getCanteenListByCanteenPrimaryIdList(List<Integer> canteenPrimaryIdList) throws TException, WmSchCantException {
        log.info("[WmScOuterService.getCanteenListByCanteenPrimaryIdList] input param: canteenPrimaryIdList = {}", JSONObject.toJSONString(canteenPrimaryIdList));
        List<WmScCanteenResultDTO> result = new ArrayList<>();
        if (CollectionUtils.isEmpty(canteenPrimaryIdList)) {
            return result;
        }

        if (canteenPrimaryIdList.size() > MccScConfig.getMaxCanteenPrimaryIdListSize()) {
            throw new WmSchCantException(BIZ_PARA_ERROR, "食堂主键ID最多传" + MccScConfig.getMaxCanteenPrimaryIdListSize() + "个");
        }

        List<WmCanteenDB> wmCanteenDBList = wmCanteenMapper.selectCanteensByIds(canteenPrimaryIdList);
        result = transCanteenDBListToCanteenResultDTOList(wmCanteenDBList);

        log.info("[WmScOuterService.getCanteenListByCanteenPrimaryIdList] result = {}", JSONObject.toJSONString(result));
        return result;
    }

    private List<WmScCanteenResultDTO> transCanteenDBListToCanteenResultDTOList(List<WmCanteenDB> wmCanteenDBList) {
        List<WmScCanteenResultDTO> wmScCanteenResultDTOList = new ArrayList<>();
        if (CollectionUtils.isEmpty(wmCanteenDBList)) {
            return wmScCanteenResultDTOList;
        }

        for (WmCanteenDB wmCanteenDB : wmCanteenDBList) {
            WmSchoolDB wmSchoolDB = wmSchoolMapper.selectSchoolById(wmCanteenDB.getSchoolId());

            WmScCanteenResultDTO dto = new WmScCanteenResultDTO();
            dto.setId(wmCanteenDB.getId());
            dto.setCanteenId(wmCanteenDB.getCanteenId());
            dto.setCanteenName(wmCanteenDB.getCanteenName());
            dto.setSchoolId(wmCanteenDB.getSchoolId());
            dto.setSchoolName(wmCanteenDB.getSchoolName());
            dto.setContractorId(wmCanteenDB.getContractorId());
            dto.setContractorName(wmCanteenDB.getContractorName());
            dto.setResponsiblePerson(wmCanteenDB.getResponsiblePerson());
            dto.setCanteenAttribute(wmCanteenDB.getCanteenAttribute());
            dto.setSchoolResponsiblePerson(wmSchoolDB.getResponsiblePerson());
            wmScCanteenResultDTOList.add(dto);
        }

        return wmScCanteenResultDTOList;
    }

    /**
     * 根据本人及所有下级UID列表查询作为学校交付人员指定在“生效”状态时的或处于“审批中”的“客户成功经理”的学校主键ID for CRM数据权限系统
     * @param uidList 本人及所有下级UID列表
     * @return 学校主键ID列表
     * @throws TException org.apache.thrift.TException
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    public List<Integer> getSchoolPrimaryIdListByUidListAsDeliveryAssignmentCSM(List<Integer> uidList) throws TException, WmSchCantException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.service.sc.WmScOuterService.getSchoolPrimaryIdListByUidListAsDeliveryAssignmentCSM(java.util.List)");
        log.info("[WmScOuterService.getSchoolPrimaryIdListByUidListAsDeliveryAssignmentCSM] input param: uidList = {}", JSONObject.toJSONString(uidList));
        if (CollectionUtils.isEmpty(uidList)) {
            log.error("[WmScOuterService.getSchoolPrimaryIdListByUidListAsDeliveryAssignmentCSM] uidList is NULL.");
            throw new WmSchCantException(BIZ_PARA_ERROR, "用户UID列表为空");
        }

        // 1-查询未终结的"交付人员指定"阶段uid被指定为"客户成功经理"的学校主键ID列表
        List<Integer> effectiveSchoolPrimaryIdList = wmSchoolDeliveryService.getSchoolPrimaryIdListByUidListAsEffectiveCSM(uidList);

        // 2-查询当前处于"审批中"的"客户成功经理"的学校主键ID列表
        List<Integer> auditingSchoolPrimaryIdList = wmSchoolDeliveryService.getSchoolPrimaryIdListByUidListAsAuditingCSM(uidList);

        log.info("[WmScOuterService.getSchoolPrimaryIdListByUidListAsDeliveryAssignmentCSM] effectiveSchoolPrimaryIdList = {}, auditingSchoolPrimaryIdList = {}",
                JSONObject.toJSONString(effectiveSchoolPrimaryIdList), JSONObject.toJSONString(auditingSchoolPrimaryIdList));
        return Stream.concat(effectiveSchoolPrimaryIdList.stream(), auditingSchoolPrimaryIdList.stream())
                .collect(Collectors.toList());
    }

    /**
     * 根据本人及所有下级UID列表查询作为学校交付人员指定在“生效”状态时的或处于“审批中”的“聚合渠道经理”的学校主键ID for CRM数据权限系统
     * @param uidList 本人及所有下级UID列表
     * @return 学校主键ID列表
     * @throws TException org.apache.thrift.TException
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    public List<Integer> getSchoolPrimaryIdListByUidListAsDeliveryAssignmentACM(List<Integer> uidList) throws TException, WmSchCantException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.service.sc.WmScOuterService.getSchoolPrimaryIdListByUidListAsDeliveryAssignmentACM(java.util.List)");
        log.info("[WmScOuterService.getSchoolPrimaryIdListByUidListAsDeliveryAssignmentACM] input param: uidList = {}", JSONObject.toJSONString(uidList));
        if (CollectionUtils.isEmpty(uidList)) {
            log.error("[WmScOuterService.getSchoolPrimaryIdListByUidListAsDeliveryAssignmentACM] uidList is NULL.");
            throw new WmSchCantException(BIZ_PARA_ERROR, "用户UID列表为空");
        }

        // 1-查询未终结的"交付人员指定"阶段uid被指定为"聚合渠道经理"的学校主键ID列表
        List<Integer> effectiveSchoolPrimaryIdList = wmSchoolDeliveryService.getSchoolPrimaryIdListByUidListAsEffectiveACM(uidList);

        // 2-查询当前处于"审批中"的"聚合渠道经理"的学校主键ID列表
        List<Integer> auditingSchoolPrimaryIdList = wmSchoolDeliveryService.getSchoolPrimaryIdListByUidListAsAuditingACM(uidList);

        log.info("[WmScOuterService.getSchoolPrimaryIdListByUidListAsDeliveryAssignmentACM] effectiveSchoolPrimaryIdList = {}, auditingSchoolPrimaryIdList = {}",
                JSONObject.toJSONString(effectiveSchoolPrimaryIdList), JSONObject.toJSONString(auditingSchoolPrimaryIdList));
        return Stream.concat(effectiveSchoolPrimaryIdList.stream(), auditingSchoolPrimaryIdList.stream())
                .collect(Collectors.toList());
    }

    /**
     * 根据本人及所有下级UID列表查询作为学校交付人员指定在“生效”状态时的或处于“审批中”的“学校对应蜂窝负责人”的学校主键ID for CRM数据权限系统
     * @param uidList 本人及所有下级UID列表
     * @return 学校主键ID列表
     * @throws TException org.apache.thrift.TException
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    public List<Integer> getSchoolPrimaryIdListByUidListAsDeliveryAssignmentAORM(List<Integer> uidList) throws TException, WmSchCantException {
        log.info("[WmScOuterService.getSchoolPrimaryIdListByUidListAsDeliveryAssignmentAORM] input param: uidList = {}", JSONObject.toJSONString(uidList));
        if (CollectionUtils.isEmpty(uidList)) {
            log.error("[WmScOuterService.getSchoolPrimaryIdListByUidListAsDeliveryAssignmentAORM] uidList is NULL.");
            throw new WmSchCantException(BIZ_PARA_ERROR, "用户UID列表为空");
        }

        // 1-查询未终结的"交付人员指定"阶段 uid被指定为"学校对应蜂窝负责人"的学校主键ID列表
        List<Integer> effectiveSchoolPrimaryIdList = wmSchoolDeliveryService.getSchoolPrimaryIdListByUidListAsEffectiveAORM(uidList);

        // 2-查询当前处于"审批中"的"学校对应蜂窝负责人"的学校主键ID列表
        List<Integer> auditingSchoolPrimaryIdList = wmSchoolDeliveryService.getSchoolPrimaryIdListByUidListAsAuditingAORM(uidList);

        log.info("[WmScOuterService.getSchoolPrimaryIdListByUidListAsDeliveryAssignmentAORM] effectiveSchoolPrimaryIdList = {}, auditingSchoolPrimaryIdList = {}",
                JSONObject.toJSONString(effectiveSchoolPrimaryIdList), JSONObject.toJSONString(auditingSchoolPrimaryIdList));
        return Stream.concat(effectiveSchoolPrimaryIdList.stream(), auditingSchoolPrimaryIdList.stream())
                .collect(Collectors.toList());
    }


    /**
     * 根据本人及所有下级UID查询作为学校责任人的学校所关联的档口管理任务ID for CRM数据权限系统
     *
     * @param uidList 本人及所有下级UID列表
     * @return 档口管理任务ID列表
     * @throws TException         org.apache.thrift.TException
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    public List<Integer> getStallManageIdListByUidListAsSchoolOwner(List<Integer> uidList) throws TException, WmSchCantException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.sc.WmScOuterService.getStallManageIdListByUidListAsSchoolOwner(java.util.List)");
        log.info("[WmScOuterService.getStallManageIdListByUidListAsSchoolOwner] input param: uidList = {}", JSONObject.toJSONString(uidList));
        if (CollectionUtils.isEmpty(uidList)) {
            log.error("[WmScOuterService.getStallManageIdListByUidListAsSchoolOwner] uidList is NULL. uidList = {}", JSONObject.toJSONString(uidList));
            throw new WmSchCantException(BIZ_PARA_ERROR, "用户UID列表为空");
        }

        // 1-查询作为学校责任人的学校主键ID列表
        List<Integer> schoolPrimaryIdList = getSchoolPrimaryIdListByUidList(uidList);
        if (CollectionUtils.isEmpty(schoolPrimaryIdList)) {
            log.info("[WmScOuterService.getStallManageIdListByUidListAsSchoolOwner] schoolPrimaryIdList is empty. uidList = {}", JSONObject.toJSONString(uidList));
            return new ArrayList<>();
        }

        // 2-查询学校关联的食堂主键ID列表
        List<Integer> canteenPrimaryIdList = getCanteenPrimaryIdListBySchoolPrimaryIdList(schoolPrimaryIdList);
        if (CollectionUtils.isEmpty(canteenPrimaryIdList)) {
            log.info("[WmScOuterService.getStallManageIdListByUidListAsSchoolOwner] canteenPrimaryIdList is empty. schoolPrimaryIdList = {}", JSONObject.toJSONString(schoolPrimaryIdList));
            return new ArrayList<>();
        }

        // 3-根据食堂主键ID查询关联的档口管理任务ID列表
        List<Integer> manageIdList = getManageIdListByCanteenPrimaryIdList(canteenPrimaryIdList);

        log.info("[WmScOuterService.getStallManageIdListByUidListAsSchoolOwner] manageIdList = {}", JSONObject.toJSONString(manageIdList));
        return manageIdList;
    }

    /**
     * 根据学校主键ID列表查询关联的食堂主键ID列表
     * @param schoolPrimaryIdList 学校主键ID列表
     * @return 食堂主键ID列表
     */
    public List<Integer> getCanteenPrimaryIdListBySchoolPrimaryIdList(List<Integer> schoolPrimaryIdList) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.service.sc.WmScOuterService.getCanteenPrimaryIdListBySchoolPrimaryIdList(java.util.List)");
        if (CollectionUtils.isEmpty(schoolPrimaryIdList)) {
            return new ArrayList<>();
        }

        List<Integer> canteenPrimaryIdList = new ArrayList<>();
        List<List<Integer>> schoolPrimaryIdListPart = Lists.partition(schoolPrimaryIdList, MccScConfig.getSchoolQueryPartitionLimit());
        for (List<Integer> listPart : schoolPrimaryIdListPart) {
            List<Integer> canteenPrimaryIdListPart = wmCanteenMapper.selectCanteenPrimaryIdListBySchoolPrimaryIdList(listPart);
            canteenPrimaryIdList.addAll(canteenPrimaryIdListPart);
        }

        log.info("[WmScOuterService.getCanteenPrimaryIdListBySchoolPrimaryIdList] canteenPrimaryIdList = {}", JSONObject.toJSONString(canteenPrimaryIdList));
        return canteenPrimaryIdList;
    }

    /**
     * 根据学校主键ID查询关联的食堂主键ID列表
     * @param schoolPrimaryId 学校主键ID
     * @return 食堂主键ID列表
     */
    public List<Integer> getCanteenPrimaryIdListBySchoolPrimaryId(Integer schoolPrimaryId) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.sc.WmScOuterService.getCanteenPrimaryIdListBySchoolPrimaryId(java.lang.Integer)");
        if (schoolPrimaryId == null || schoolPrimaryId <= 0) {
            return new ArrayList<>();
        }

        List<Integer> canteenPrimaryIdList = wmCanteenMapper.selectCanteenPrimaryIdListBySchoolPrimaryId(schoolPrimaryId);
        log.info("[WmScOuterService.getCanteenPrimaryIdListBySchoolPrimaryId] canteenPrimaryIdList = {}", JSONObject.toJSONString(canteenPrimaryIdList));
        return canteenPrimaryIdList;
    }


    /**
     * 根据本人及所有下级UID查询作为客户责任人的承包商所关联的档口管理任务ID for CRM数据权限系统
     * @param uidList 本人及所有下级UID列表
     * @return 档口管理任务ID列表
     * @throws TException         org.apache.thrift.TException
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    public List<Integer> getStallManageIdListByUidListAsCustomerOwner(List<Integer> uidList) throws TException, WmSchCantException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_1", "com.sankuai.meituan.waimai.customer.service.sc.WmScOuterService.getStallManageIdListByUidListAsCustomerOwner(java.util.List)");
        log.info("[WmScOuterService.getStallManageIdListByUidListAsCustomerOwner] input param: uidList = {}", JSONObject.toJSONString(uidList));
        if (CollectionUtils.isEmpty(uidList)) {
            log.error("[WmScOuterService.getStallManageIdListByUidListAsCustomerOwner] uidList is NULL. uidList = {}", JSONObject.toJSONString(uidList));
            throw new WmSchCantException(BIZ_PARA_ERROR, "用户UID列表为空");
        }

        // 1-查询作为客户责任人的承包商ID列表
        List<Integer> customerIdList = getContractorIdListByUidList(uidList);
        if (CollectionUtils.isEmpty(customerIdList)) {
            log.info("[WmScOuterService.getStallManageIdListByUidListAsCustomerOwner] customerIdList is null. uidList = {}", JSONObject.toJSONString(uidList));
            return new ArrayList<>();
        }

        // 2-根据承包商ID查询关联的食堂主键ID列表
        List<Integer> canteenPrimaryIdList = getCanteenPrimaryIdListByCustomerIdList(customerIdList);
        if (CollectionUtils.isEmpty(canteenPrimaryIdList)) {
            log.info("[WmScOuterService.getStallManageIdListByUidListAsCustomerOwner] canteenPrimaryIdList is null. customerIdList = {}", JSONObject.toJSONString(customerIdList));
            return new ArrayList<>();
        }

        // 3-根据食堂主键ID查询关联的档口管理任务ID列表
        List<Integer> manageIdList = getManageIdListByCanteenPrimaryIdList(canteenPrimaryIdList);

        log.info("[WmScOuterService.getStallManageIdListByUidListAsCustomerOwner] manageIdList = {}", JSONObject.toJSONString(manageIdList));
        return manageIdList;
    }


    /**
     * 根据本人及所有下级MIS查询作为食堂责任人的食堂所关联的档口管理任务ID for CRM数据权限系统
     * @param misIdList 本人及所有下级MIS列表
     * @return 档口管理任务ID列表
     * @throws TException org.apache.thrift.TException
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    public List<Integer> getStallManageIdListByMisIdListAsCanteenOwner(List<String> misIdList) throws TException, WmSchCantException {
        log.info("[WmScOuterService.getStallManageIdListByMisIdListAsCanteenOwner] input param: misIdList = {}", JSONObject.toJSONString(misIdList));
        if (CollectionUtils.isEmpty(misIdList)) {
            log.error("[WmScOuterService.getStallManageIdListByMisIdListAsCanteenOwner] uidList is NULL. misIdList = {}", JSONObject.toJSONString(misIdList));
            throw new WmSchCantException(BIZ_PARA_ERROR, "用户UID列表为空");
        }

        // 1-查询作为食堂责任人的食堂主键ID列表
        List<Integer> canteenPrimaryIdList = getCanteenPrimaryIdListByMisIdList(misIdList);
        if (CollectionUtils.isEmpty(canteenPrimaryIdList)) {
            log.info("[WmScOuterService.getStallManageIdListByMisIdListAsCanteenOwner] canteenPrimaryIdList is empty. misIdList = {}", JSONObject.toJSONString(misIdList));
            return new ArrayList<>();
        }

        // 2-根据食堂主键ID查询关联的档口管理任务ID列表
        List<Integer> manageIdList = getManageIdListByCanteenPrimaryIdList(canteenPrimaryIdList);

        log.info("[WmScOuterService.getStallManageIdListByMisIdListAsCanteenOwner] manageIdList = {}", JSONObject.toJSONString(manageIdList));
        return manageIdList;
    }


    /**
     * 根据人员MIS列表查询作为食堂责任人的食堂主键ID列表
     * @param misIdList 人员MIS列表
     * @return 食堂主键ID列表
     * @throws TException org.apache.thrift.TException
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    public List<Integer> getCanteenPrimaryIdListByMisIdList(List<String> misIdList) throws TException, WmSchCantException {
        log.info("[WmScOuterService.getCanteenPrimaryIdListByMisIdList] input param: misIdList = {}", JSONObject.toJSONString(misIdList));
        if (CollectionUtils.isEmpty(misIdList)) {
            log.error("[WmScOuterService.getCanteenPrimaryIdListByMisIdList] uidList is empty, return. misIdList = {}", JSONObject.toJSONString(misIdList));
            throw new WmSchCantException(BIZ_PARA_ERROR, "用户UID列表为空");
        }

        List<Integer> canteenPrimaryIdList = new ArrayList<>();
        List<List<String>> misIdListPart = Lists.partition(misIdList, MccScConfig.getSchoolQueryPartitionLimit());
        for (List<String> listPart : misIdListPart) {
            List<Integer> canteenPrimaryIdListPart = wmCanteenMapper.selectCanteenPrimaryIdListByResponsibleMisIdList(listPart);
            canteenPrimaryIdList.addAll(canteenPrimaryIdListPart);
        }

        log.info("[WmScOuterService.getSchoolPrimaryIdListByUidList] canteenPrimaryIdList = {}", JSONObject.toJSONString(canteenPrimaryIdList));
        return canteenPrimaryIdList;
    }


    /**
     * 根据食堂主键ID查询关联的档口管理任务ID列表
     * @param canteenPrimaryIdList 食堂主键ID列表
     * @return 档口管理任务ID列表
     */
    public List<Integer> getManageIdListByCanteenPrimaryIdList(List<Integer> canteenPrimaryIdList) {
        log.info("[WmScOuterService.getManageIdListByCanteenPrimaryIdList] input param: canteenPrimaryIdList = {}", JSONObject.toJSONString(canteenPrimaryIdList));
        if (CollectionUtils.isEmpty(canteenPrimaryIdList)) {
            return new ArrayList<>();
        }

        List<Integer> manageIdList = new ArrayList<>();
        List<List<Integer>> canteenPrimaryIdListPart = Lists.partition(canteenPrimaryIdList, MccScConfig.getSchoolQueryPartitionLimit());
        for (List<Integer> listPart : canteenPrimaryIdListPart) {
            List<Integer> manageIdListPart = wmCanteenStallManageMapper.selectManageIdListByCanteenPrimaryIdList(listPart);
            manageIdList.addAll(manageIdListPart);
        }
        return manageIdList;
    }


    /**
     * 根据承包商ID列表查询关联的食堂主键ID列表
     * @param customerIdList 承包商ID列表
     * @return 食堂主键ID列表
     */
    public List<Integer> getCanteenPrimaryIdListByCustomerIdList(List<Integer> customerIdList) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.sc.WmScOuterService.getCanteenPrimaryIdListByCustomerIdList(java.util.List)");
        log.info("[WmScOuterService.getCanteenPrimaryIdListByCustomerIdList] input param: customerIdList = {}", JSONObject.toJSONString(customerIdList));
        if (CollectionUtils.isEmpty(customerIdList)) {
            return new ArrayList<>();
        }

        List<Integer> canteenPrimaryIdList = new ArrayList<>();
        List<List<Integer>> customerIdListPart = Lists.partition(customerIdList, MccScConfig.getSchoolQueryPartitionLimit());
        for (List<Integer> list : customerIdListPart) {
            List<Integer> canteenPrimaryIds = wmCanteenMapper.selectCanteenPrimaryIdListByCustomerIdList(list);
            canteenPrimaryIdList.addAll(canteenPrimaryIds);
        }

        log.info("[WmScOuterService.getCanteenPrimaryIdListByCustomerIdList] canteenPrimaryIdList = {}", JSONObject.toJSONString(canteenPrimaryIdList));
        return canteenPrimaryIdList;
    }

    /**
     * 根据门店ID判断门店是否在食堂档口管理灰度列表 for 资质系统
     * @param wmPoiId 门店ID
     * @return true: 命中灰度 / false: 未命中灰度
     * @throws TException org.apache.thrift.TException
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    public Boolean isWmPoiIdInCanteenStallGrayList(Long wmPoiId) throws TException, WmSchCantException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.service.sc.WmScOuterService.isWmPoiIdInCanteenStallGrayList(java.lang.Long)");
        log.info("[WmScOuterService.isWmPoiIdInCanteenStallGrayList] input param: wmPoiId = {}", wmPoiId);
        if (wmPoiId == null || wmPoiId <= 0) {
            throw new WmSchCantException(BIZ_PARA_ERROR, "入参为空");
        }

        // 1-从wm_sc_canteen_poi_attribute表查询门店关联的食堂
        WmScCanteenPoiAttributeDO attributeDO = wmScCanteenPoiAttributeMapper.selectByWmPoiId(wmPoiId);
        if (attributeDO == null) {
            log.info("[WmScOuterService.isWmPoiIdInCanteenStallGrayList] attributeDO is null, return. wmPoiId = {}", wmPoiId);
            return false;
        }

        // 2-根据食堂ID查询关联学校ID
        WmCanteenDB wmCanteenDB = wmCanteenMapper.selectCanteenById(attributeDO.getCanteenPrimaryId());
        if (wmCanteenDB == null) {
            log.error("[WmScOuterService.isWmPoiIdInCanteenStallGrayList] wmCanteenDB is null. canteenPrimaryId = {}", attributeDO.getCanteenPrimaryId());
            return false;
        }

        return true;
    }

}

