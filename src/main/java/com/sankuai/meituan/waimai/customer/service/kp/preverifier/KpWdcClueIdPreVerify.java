package com.sankuai.meituan.waimai.customer.service.kp.preverifier;

import com.sankuai.meituan.auth.util.CollectionUtils;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerDB;
import com.sankuai.meituan.waimai.customer.util.ThrowUtil;
import com.sankuai.meituan.waimai.thrift.customer.constant.KpTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.kp.WmCustomerKp;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/12/18
 */
@Component
public class KpWdcClueIdPreVerify implements KpPreverifier {

    private static final Logger LOGGER = LoggerFactory.getLogger(KpWdcClueIdPreVerify.class);

    @Override
    public void verify(WmCustomerDB wmCustomer, List<WmCustomerKp> oldWmCustomerKpList, List<WmCustomerKp> addKpList, List<WmCustomerKp> deleteKpList, List<WmCustomerKp> upgradeKpList) throws WmCustomerException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.kp.preverifier.KpWdcClueIdPreVerify.verify(WmCustomerDB,List,List,List,List)");
        if (deleteKpList != null) {
            for (WmCustomerKp kp : deleteKpList) {
                kpWdcClueIdVerifyWhenDelete(oldWmCustomerKpList, kp);
            }
        }
    }


    private void kpWdcClueIdVerifyWhenDelete(List<WmCustomerKp> oldCustomerKpList,WmCustomerKp deleteKp) throws WmCustomerException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.service.kp.preverifier.KpWdcClueIdPreVerify.kpWdcClueIdVerifyWhenDelete(java.util.List,com.sankuai.meituan.waimai.thrift.customer.domain.kp.WmCustomerKp)");
        if (deleteKp == null) {
            return;
        }
        if (KpTypeEnum.VISITKP.getType() != deleteKp.getKpType()) {
            return;
        }
        if (deleteKp.getWdcClueId() != null && deleteKp.getWdcClueId() > 0) {
            ThrowUtil.throwClientError("拜访KP已同步线索管理，不可删除");
        }
        if (CollectionUtils.isEmpty(oldCustomerKpList)) {
            return;
        }
        for (WmCustomerKp kp : oldCustomerKpList) {
            if (kp.getId() != deleteKp.getId()) {
                continue;
            }
            if (kp.getWdcClueId() != null && kp.getWdcClueId() > 0) {
                ThrowUtil.throwClientError("拜访KP已同步线索管理，不可删除");
            }
        }
    }

    @Override
    public int order() {
        return 5;
    }
}
