package com.sankuai.meituan.waimai.customer.service.common;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.sankuai.meituan.waimai.infra.domain.WmEmploy;
import com.sankuai.meituan.waimai.infra.service.WmEmployService;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerErrorCodeConstants;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import com.sankuai.meituan.waimai.thrift.exception.WmServerException;
import org.apache.thrift.TException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;

@Service
public class WmEmployeeService {

    @Autowired
    private WmEmployService.Iface wmEmployService;
    
    static Logger log = LoggerFactory.getLogger(WmEmployeeService.class);

    public String getMisId(int uid){
        WmEmploy wmEmploy = getWmEmployById(uid);
        return wmEmploy == null ? "" : wmEmploy.getMisId();
    }

    public int getUid(String misId){
        if(StringUtils.isEmpty(misId)){
            return 0;
        }
        WmEmploy wmEmploy = getWmEmployByMisId(misId);
        return wmEmploy == null ? 0 : wmEmploy.getUid();
    }

    public WmEmploy getWmEmployById(int uid) {
        WmEmploy wmEmploy=null;
        try {
            wmEmploy = wmEmployService.getById(uid);
        } catch (WmServerException e) {
            log.warn("获取员工信息异常:uid:{},{}",uid,e.getMsg(), e);
        } catch (TException e) {
            log.error("获取员工信息异常:uid:{},{}",uid,e.getMessage(), e);

        }
        return wmEmploy;
    }

    public WmEmploy getWmEmployByMisId(String misId) {
        WmEmploy wmEmploy=null;
        try {
            wmEmploy = wmEmployService.getByMisId(misId);
        } catch (WmServerException e) {
            log.warn("获取员工信息异常:misId:{},{}",misId,e.getMsg(), e);
        } catch (TException e) {
            log.error("获取员工信息异常:misId:{},{}",misId,e.getMessage(), e);
        }
        return wmEmploy;
    }

    public List<WmEmploy> mgetByUids(List<Integer> uidList) throws WmCustomerException {
        List<WmEmploy> employs = new ArrayList<>();
        try{
            log.info("#mgetByUids-根据uidList查询用户信息:{}", JSON.toJSONString(uidList));
            // 通过所有下级的uid获取所有下级的WmEmploy (批量获取用户信息。文档里要求uids.size()<=100)
            List<List<Integer>> uidBatchs = Lists.partition(uidList, 100);
            for (List<Integer> uidBatch : uidBatchs) {
                employs.addAll(wmEmployService.mgetByUids(uidBatch));
            }
            log.info("#mgetByUids-根据uidList查询用户信息结果:{}",JSON.toJSONString(employs));
            return employs;
        }catch (Exception e){
            log.error("#mgetByUids-根据uidList查询用户信息异常:{}",JSON.toJSONString(uidList),e);
            throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR,"查询用户信息异常");
        }
    }

    public String getUserAndId(Integer uid) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_1", "com.sankuai.meituan.waimai.customer.service.common.WmEmployeeService.getUserAndId(java.lang.Integer)");
        if (uid == null) {
            return "未知";
        }
        if (uid == 0) {
            return "system";
        }
        WmEmploy wmEmploy = getWmEmployById(uid);
        return wmEmploy == null ? "" : wmEmploy.getName() + "(" + wmEmploy.getMisId() + ")";
    }

}
