package com.sankuai.meituan.waimai.customer.service.dbus.customerPoiList;

import com.alibaba.fastjson.JSON;
import com.meituan.dbus.common.StaticUtils;
import com.meituan.dbus.thriftV2.DataBusEventServiceV2;
import com.sankuai.meituan.waimai.customer.constant.customer.WmCustomerRelTableDbusEnum;
import com.sankuai.meituan.waimai.customer.constant.customer.WmPoiRelTableDbusEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
public class PoiRelDbusEventServiceV2Impl implements DataBusEventServiceV2.Iface {

    private final String TABLE_NAME_KEY = "tableName";

    @Autowired
    private List<IPoiRelTableEvent> iTableEventHandleList;

    private Map<WmPoiRelTableDbusEnum, IPoiRelTableEvent> iTableEventHandleMap = new HashMap<>();

    @PostConstruct
    public void init() {
        if (CollectionUtils.isEmpty(iTableEventHandleList)) {
            return;
        }
        for (IPoiRelTableEvent handel : iTableEventHandleList) {
            iTableEventHandleMap.put(handel.getTable(), handel);
        }
    }

    @Override
    public String handleUpdate(Map<String, String> metaJsonData, String dataMapJson, String diffJson) throws TException {
        log.info("handleUpdate::监听门店库表变更metaJsonData={},dataMapJson={},diffJson={}", JSON.toJSONString(metaJsonData), dataMapJson, diffJson);
        if (MapUtils.isEmpty(metaJsonData) || StringUtils.isBlank(dataMapJson) || StringUtils.isBlank(diffJson)) {
            return StaticUtils.ok;
        }
        IPoiRelTableEvent tableEvent = getTableEvent(metaJsonData);
        if (tableEvent == null) {
            return StaticUtils.ok;
        }
        String result = tableEvent.handleUpdate(metaJsonData, dataMapJson, diffJson);
        if (StringUtils.isBlank(result)) {
            return StaticUtils.ok;
        } else {
            return StaticUtils.fail;
        }
    }

    @Override
    public String handleInsert(Map<String, String> metaJsonData, String dataMapJson) throws TException {
        log.info("handleInsert::监听门店库表新增metaJsonData={},dataMapJson={}", JSON.toJSONString(metaJsonData), dataMapJson);
        if (MapUtils.isEmpty(metaJsonData) || StringUtils.isBlank(dataMapJson)) {
            return StaticUtils.ok;
        }
        IPoiRelTableEvent tableEvent = getTableEvent(metaJsonData);
        if (tableEvent == null) {
            return StaticUtils.ok;
        }
        String result = tableEvent.handleInsert(metaJsonData, dataMapJson);
        if (StringUtils.isBlank(result)) {
            return StaticUtils.ok;
        } else {
            return StaticUtils.fail;
        }
    }

    @Override
    public String handleDelete(Map<String, String> metaJsonData, String dataMapJson) throws TException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_2", "com.sankuai.meituan.waimai.customer.service.dbus.customerPoiList.PoiRelDbusEventServiceV2Impl.handleDelete(java.util.Map,java.lang.String)");
        log.info("handleDelete::监听门店库表删除metaJsonData={},dataMapJson", JSON.toJSONString(metaJsonData), dataMapJson);
        if (MapUtils.isEmpty(metaJsonData) || StringUtils.isBlank(dataMapJson)) {
            return StaticUtils.ok;
        }
        IPoiRelTableEvent tableEvent = getTableEvent(metaJsonData);
        if (tableEvent == null) {
            return StaticUtils.ok;
        }
        String result = tableEvent.handleDelete(metaJsonData, dataMapJson);
        if (StringUtils.isBlank(result)) {
            return StaticUtils.ok;
        } else {
            return StaticUtils.fail;
        }
    }


    private IPoiRelTableEvent getTableEvent(Map<String, String> metaJsonData) {
        if (MapUtils.isEmpty(metaJsonData)
                || !metaJsonData.containsKey(TABLE_NAME_KEY)
                || StringUtils.isBlank(metaJsonData.get(TABLE_NAME_KEY))) {
            return null;
        }
        WmPoiRelTableDbusEnum tableDbusEnum = WmPoiRelTableDbusEnum.of(metaJsonData.get(TABLE_NAME_KEY));

        if (tableDbusEnum == null) {
            return null;
        }
        return iTableEventHandleMap.get(tableDbusEnum);
    }

}
