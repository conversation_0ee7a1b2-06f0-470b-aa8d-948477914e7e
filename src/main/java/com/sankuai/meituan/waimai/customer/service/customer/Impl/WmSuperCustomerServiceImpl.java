package com.sankuai.meituan.waimai.customer.service.customer.Impl;

import com.alibaba.fastjson.JSONObject;
import com.sankuai.meituan.waimai.customer.config.MccConfig;
import com.sankuai.meituan.waimai.customer.config.MccCustomerConfig;
import com.sankuai.meituan.waimai.customer.constant.WmCustomerConstant;
import com.sankuai.meituan.waimai.customer.contract.service.impl.WmCustomerOplogService;
import com.sankuai.meituan.waimai.customer.dao.WmCustomerDBMapper;
import com.sankuai.meituan.waimai.customer.ddd.adapter.MtCustomerThriftServiceAdapterImpl;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerDB;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerService;
import com.sankuai.meituan.waimai.customer.util.trans.WmCustomerTransUtil;
import com.sankuai.meituan.waimai.thrift.bo.WmCustomerDiffCellBo;
import com.sankuai.meituan.waimai.thrift.customer.constant.*;
import com.sankuai.meituan.waimai.thrift.customer.constant.customer.CertificateTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.WmCustomerOplogBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.ValidateResultBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.WmCustomerBasicBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import com.sankuai.meituan.waimai.thrift.util.DiffUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.thrift.TException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Service("wmSuperCustomerRealService")
public class WmSuperCustomerServiceImpl extends AbstractWmCustomerRealService {

    private static Logger logger = LoggerFactory.getLogger(WmSuperCustomerServiceImpl.class);

    @Autowired
    private WmCustomerOplogService wmCustomerOplogService;

    private static String URL_HTTP_CONSTANT = "http";

    @Autowired
    protected WmCustomerDBMapper wmCustomerDBMapper;

    @Autowired
    private WmCustomerService wmCustomerService;


    @Autowired
    private MtCustomerThriftServiceAdapterImpl mtCustomerThriftServiceAdapter;

    @Override
    protected boolean updateCustomer(WmCustomerBasicBo wmCustomerBasicBo, ValidateResultBo validateResultBo, Integer opUid, String opName, int channel) throws WmCustomerException, TException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.service.customer.Impl.WmSuperCustomerServiceImpl.updateCustomer(WmCustomerBasicBo,ValidateResultBo,Integer,String,int)");
        // 是否直接修改不进行提审
        boolean isUpdateNotAudit = false;
        WmCustomerDB wmCustomerDB = wmCustomerPlatformDataParseService.selectCustomerFromPlatformById(wmCustomerBasicBo.getId());
        if (wmCustomerDB == null) {
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "客户ID无法查到有效数据");
        }

        wmCustomerBasicBo.setMtCustomerId(wmCustomerDB.getMtCustomerId());
        boolean isUploadImg = (channel == CustomerSource.WAIMAI_BD.getCode() && !wmCustomerBasicBo.getPicUrl().contains(URL_HTTP_CONSTANT)) ? false : true;
        updateCustomerWithAudit(wmCustomerBasicBo, validateResultBo, opUid, opName, isUploadImg);
        if (wmCustomerDB.getBizOrgCode() == null || wmCustomerDB.getBizOrgCode().intValue() == 0) {
            CustomerRealTypeEnum customerRealTypeEnum = CustomerRealTypeEnum.getByValue(wmCustomerDB.getCustomerRealType());
            if (customerRealTypeEnum != null) {
                mtCustomerThriftServiceAdapter.saveMtCustomerBizOrgCode(wmCustomerDB.getMtCustomerId(), customerRealTypeEnum.getBizOrgCode());
            }
        }
        return isUpdateNotAudit;
    }

    @Override
    protected void initAuditStatus(WmCustomerDB wmCustomerDB, Integer opUid, WmCustomerBasicBo wmCustomerBasicBo) throws WmCustomerException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.service.customer.Impl.WmSuperCustomerServiceImpl.initAuditStatus(WmCustomerDB,Integer,WmCustomerBasicBo)");
        if ((wmCustomerDB.getCertificateType() == CertificateTypeEnum.PAPER.getType())) {
            wmCustomerDB.setAuditStatus(CustomerAuditStatus.CUSTOMER_AUDIT_NEW.getCode());
            //上级客户需要资质审核成功后再生效
            wmCustomerDB.setEffective(CustomerConstants.UNEFFECT);
        } else {
            wmCustomerDB.setAuditStatus(CustomerAuditStatus.CUSTOMER_AUDIT_PASS.getCode());
            wmCustomerDB.setEffective(CustomerConstants.EFFECT);
        }
    }

    @Override
    public void deleteCustomer(Integer customerId, Integer opUid, String opName) throws WmCustomerException, TException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_2", "com.sankuai.meituan.waimai.customer.service.customer.Impl.WmSuperCustomerServiceImpl.deleteCustomer(java.lang.Integer,java.lang.Integer,java.lang.String)");
        List<WmCustomerDB> customerList = selectCustomerBySuperCustomerId(customerId);
        if (CollectionUtils.isNotEmpty(customerList)) {
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "该客户下关联了下级客户,无法直接删除");
        }
//        // 数据读取
//        WmCustomerDB wmCustomerDB = wmCustomerPlatformDataParseService.getCustomerByMtCustomerId(customerId);
//        //同步删除客户平台中客户信息
//        mtCustomerThriftServiceAdapter.deleteCustomerByMtCustomerId(wmCustomerDB.getMtCustomerId());
//        wmCustomerDBMapper.deleteCustomer(customerId);

        // parseDateService适配
        wmCustomerPlatformDataParseService.deleteCustomerForSuper(customerId);
        WmCustomerOplogBo wmCustomerOplogBo = new WmCustomerOplogBo(customerId, WmCustomerOplogBo.OpModuleType.CUSTOMER, customerId);
        wmCustomerOplogBo.setOpType(WmCustomerOplogBo.OpType.DELETE.type);
        wmCustomerOplogBo.setLog(CustomerConstants.CUSTOMER_LOG_TEMPLATE_DELETE_CUSTOMER);
        wmCustomerOplogBo.setOpUid(opUid);
        wmCustomerOplogBo.setOpUname(opName);
        wmCustomerOplogBo.setRemark("");
        wmCustomerOplogService.insert(wmCustomerOplogBo);
    }

    @Override
    public long syncMtCustomerQua(long mtCustomerId) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.customer.Impl.WmSuperCustomerServiceImpl.syncMtCustomerQua(long)");
        return wmCustomerPlatformDataParseService.syncMtCustomerQua(mtCustomerId);
    }


    /**
     * 旧流程 更新上级客户无需提审
     *
     * @param wmCustomerBasicBo
     * @param validateResultBo
     * @param opUid
     * @param opName
     * @throws WmCustomerException
     * @throws TException
     */
    private void updateCustomerNoAudit(WmCustomerBasicBo wmCustomerBasicBo, ValidateResultBo validateResultBo, Integer opUid, String opName, boolean isUploadImg) throws WmCustomerException, TException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_2", "com.sankuai.meituan.waimai.customer.service.customer.Impl.WmSuperCustomerServiceImpl.updateCustomerNoAudit(WmCustomerBasicBo,ValidateResultBo,Integer,String,boolean)");

        //1、获取wmCustomeDB，主要获取mt_customer_id
        WmCustomerDB wmCustomerDB = wmCustomerPlatformDataParseService.selectCustomerFromPlatformById(wmCustomerBasicBo.getId());
        if (wmCustomerDB == null) {
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "客户ID无法查到有效数据");
        }
        wmCustomerBasicBo.setMtCustomerId(wmCustomerDB.getMtCustomerId());

        updateCustomerToPlatAndLocal(wmCustomerBasicBo, validateResultBo, opUid, opName, wmCustomerDB, isUploadImg);

        //插入修改日志
        insertCustomerOpLog(wmCustomerDB.getId(), opUid, opName, wmCustomerBasicBo, wmCustomerDB, WmCustomerOplogBo.OpType.UPDATE);
    }

    /**
     * 更新客户信息到美团平台和客户本地表
     *
     * @param wmCustomerBasicBo
     * @param validateResultBo
     * @param opUid
     * @param opName
     * @param wmCustomerDB
     * @throws WmCustomerException
     * @throws TException
     */
    private void updateCustomerToPlatAndLocal(WmCustomerBasicBo wmCustomerBasicBo, ValidateResultBo validateResultBo, Integer opUid, String opName, WmCustomerDB wmCustomerDB, boolean isUploadImg) throws WmCustomerException, TException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.service.customer.Impl.WmSuperCustomerServiceImpl.updateCustomerToPlatAndLocal(WmCustomerBasicBo,ValidateResultBo,Integer,String,WmCustomerDB,boolean)");
        if (wmCustomerBasicBo.getPicUrl().contains(WmCustomerConstant.URL_CUSTOMER_PLATFORM_PREFIX)) {
            mtCustomerThriftServiceAdapter.uploadImgFromCustomerPlatform(wmCustomerBasicBo.getId(), wmCustomerBasicBo.getPicUrl());
            String mtCustomerUrl = mtCustomerThriftServiceAdapter.transferMtCustomerPicUrl(wmCustomerBasicBo.getPicUrl());
            wmCustomerBasicBo.setPicUrl(mtCustomerUrl);
        }
        //1、更新客户信息到美团客户平台
        WmCustomerDB wmCustomerDBNew = WmCustomerTransUtil.customerBoToDB(wmCustomerBasicBo);
        //wmCustomerDBNew.setEffective(wmCustomerDB.getEffective());
        //如果营业执照形式为纸质形式按照原来的逻辑，如果是电子形式则直接生效
        if (wmCustomerDBNew.getCertificateType() == CertificateTypeEnum.PAPER.getType()) {
            wmCustomerDBNew.setEffective(wmCustomerDB.getEffective());
        } else {
            wmCustomerDBNew.setAuditStatus(CustomerAuditStatus.CUSTOMER_AUDIT_PASS.getCode());
            wmCustomerDBNew.setEffective(CustomerConstants.EFFECT);
        }
        try {
            updateMtCustomer(wmCustomerDBNew, isUploadImg);
            if (isUploadImg) {
                logger.info("updateCustomerToPlatAndLocal uploadImg wmCustomerDBNew={}", JSONObject.toJSONString(wmCustomerDBNew));
                wmCustomerBasicBo.setPicUrl(wmCustomerDBNew.getPicUrl());
            }
        } catch (Exception e) {
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "客户平台更新异常");
        }

        //2、更新客户表
        wmCustomerService.updateCustomer(wmCustomerDBNew, opUid);

        validateResultBo.setCode(CustomerConstants.RESULT_CODE_PASS);
        validateResultBo.setMsg("保存成功");

    }


    /**
     * 新流程 更新上级客户资质字段需提审
     *
     * @param wmCustomerBasicBo
     * @param validateResultBo
     * @param opUid
     * @param opName
     * @throws WmCustomerException
     * @throws TException
     */
    private void updateCustomerWithAudit(WmCustomerBasicBo wmCustomerBasicBo, ValidateResultBo validateResultBo, Integer opUid, String opName, boolean isUploadImg) throws WmCustomerException, TException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.service.customer.Impl.WmSuperCustomerServiceImpl.updateCustomerWithAudit(WmCustomerBasicBo,ValidateResultBo,Integer,String,boolean)");
        //1、获取wmCustomeDB，主要获取mt_customer_id
        WmCustomerDB wmCustomerDB = wmCustomerPlatformDataParseService.selectCustomerFromPlatformById(wmCustomerBasicBo.getId());
        wmCustomerBasicBo.setMtCustomerId(wmCustomerDB.getMtCustomerId());

        if (wmCustomerDB == null) {
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "客户ID无法查到有效数据");
        }
        if (wmCustomerDB.getCustomerRealType() != wmCustomerBasicBo.getCustomerRealType()) {
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "上级客户不允许修改类型");
        }

        if (wmCustomerDB.getAuditStatus() == CustomerAuditStatus.CUSTOMER_AUDIT_AUDITING.getCode()) {
            validateResultBo.setCode(CustomerConstants.RESULT_CODE_AUDIT_ERROR);
            validateResultBo.setMsg("正在审核,无法修改");
            return;
        }

        if (!wmCustomerBasicBo.getSignMode().equals(wmCustomerDB.getSignMode())) {
            logger.info("签约模式变更，无需审核，直接生效 signMode:{}", wmCustomerBasicBo.getSignMode());
            wmCustomerPlatformDataParseService.updateSignMode(wmCustomerBasicBo.getId(), wmCustomerBasicBo.getSignMode());
        }

        // 客户平台资质图片特殊处理
        String auditPicUrl = wmCustomerBasicBo.getPicUrl();
        if (wmCustomerBasicBo.getPicUrl().contains(WmCustomerConstant.URL_CUSTOMER_PLATFORM_PREFIX)) {
            auditPicUrl = mtCustomerThriftServiceAdapter.uploadImgFromCustomerPlatform(wmCustomerBasicBo.getId(), wmCustomerBasicBo.getPicUrl());
            String mtCustomerUrl = mtCustomerThriftServiceAdapter.transferMtCustomerPicUrl(wmCustomerBasicBo.getPicUrl());
            wmCustomerBasicBo.setPicUrl(mtCustomerUrl);
        }

        Map<String, String> compareMap = CustomerConstants.customerCompare;
        if (wmCustomerBasicBo.getCustomerType() == CustomerType.CUSTOMER_TYPE_IDCARD.getCode() &&
                wmCustomerDB.getCustomerType() != null && wmCustomerDB.getCustomerType() == CustomerType.CUSTOMER_TYPE_IDCARD.getCode()) {
            compareMap = CustomerConstants.customerIDCARDCompare;
        }
        // 获取需要提审的资质字段diff
        List<WmCustomerDiffCellBo> diffList = DiffUtil.compare(wmCustomerBasicBo, wmCustomerDB, compareMap);

        logger.info("wmSuperCustomerRealService## updateCustomer diffList ={}", diffList);
        if (CollectionUtils.isEmpty(diffList)) {
            // 无变更或变更的字段不包含资质信息
            logger.info("wmCustomerBasicBo={}", JSONObject.toJSONString(wmCustomerBasicBo));
            logger.info("updateCustomer wmCustomerBasicBo={}", JSONObject.toJSONString(WmCustomerTransUtil.customerBoToDB(wmCustomerBasicBo)));
            wmCustomerService.updateCustomer(WmCustomerTransUtil.customerBoToDB(wmCustomerBasicBo), opUid);
            //插入修改日志
            insertCustomerOpLog(wmCustomerDB.getId(), opUid, opName, wmCustomerBasicBo, wmCustomerDB, WmCustomerOplogBo.OpType.UPDATE);
            //如果审核拒绝&&生效，点击保存无修改，要消除页面展现diff
            if (wmCustomerDB.getAuditStatus() == CustomerAuditStatus.CUSTOMER_AUDIT_REJECT.getCode() && wmCustomerDB.isEffectived()) {
                wmCustomerDB.setAuditStatus(CustomerAuditStatus.CUSTOMER_AUDIT_PASS.getCode());
                wmCustomerDBMapper.updateCustomerAuditStatus(wmCustomerDB);
            }
            validateResultBo.setMsg("保存成功");
            return;
        }
        //未生效过的客户修改，需要更新正式表
        if (wmCustomerDB.isUnEffectived()) {
            //2、更新客户信息到美团客户平台
            updateCustomerToPlatAndLocal(wmCustomerBasicBo, validateResultBo, opUid, opName, wmCustomerDB, isUploadImg);
        }


        if (wmCustomerDB.isEffectived() || wmCustomerDB.getAuditStatus() == CustomerAuditStatus.CUSTOMER_AUDIT_REJECT.getCode()) {
            logger.info("客户生效后修改,需要单独提审,客户ID={}", wmCustomerBasicBo.getId());
            //生效&&审核驳回数据修改会单独提审
            //审核表主键为审核业务ID，作为定位到客户审核每条记录的唯一标识
            wmCustomerAuditDBMapper.invalidCustomerAudit(wmCustomerBasicBo.getId());
            wmCustomerBasicBo.setPicUrl(auditPicUrl);
            Integer bizId = insertCustomerAudit(wmCustomerBasicBo, wmCustomerDB.getEffective(), wmCustomerDB.getOwnerUid(), CustomerConstants.BATCH_SUBMIT_NO, opUid, opName);
            WmCustomerDB wmCustomerDBNew = WmCustomerTransUtil.customerBoToDB(wmCustomerBasicBo);
            wmCustomerDBNew.setAuditStatus(wmCustomerDB.getAuditStatus());
            commitAudit(wmCustomerDBNew, bizId, opUid, opName, true);
            //提审的时候判断其他信息是否有变更，有则直接进行处理
            if (MccCustomerConfig.getSuperCustomerOtherUpdateAuditSwitch()) {
                updateCustomerExtPro(wmCustomerBasicBo, wmCustomerDB, opUid, opName);
            }

            validateResultBo.setMsg(CustomerConstants.SUBMIT_AUDIT_SUCCESS_MSG);
            //插入修改日志
            insertCustomerOpLog(wmCustomerDB.getId(), opUid, opName, wmCustomerBasicBo, wmCustomerDB, WmCustomerOplogBo.OpType.UPDATE);
            //插入提审日志
            insertCustomerOpLog(wmCustomerDB.getId(), opUid, opName, WmCustomerOplogBo.OpType.CHANGESTATUS, CustomerConstants.CUSTOMER_LOG_TEMPLATE_COMMIT_AUDIT);
        }
    }

    /**
     * 更新上级客户的其他信息
     *
     * @param wmCustomerBasicBo
     */
    private void updateCustomerExtPro(WmCustomerBasicBo wmCustomerBasicBo, WmCustomerDB wmCustomerDB, Integer opUid, String opName) throws WmCustomerException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.service.customer.Impl.WmSuperCustomerServiceImpl.updateCustomerExtPro(WmCustomerBasicBo,WmCustomerDB,Integer,String)");
        try {
            if (wmCustomerBasicBo.getCustomerExtPro() != null
                    && wmCustomerBasicBo.getCustomerExtPro().equals(wmCustomerDB.getCustomerExtPro())) {
                logger.info("updateCustomerExtPro,上级客户的其他信息未发生变更，不需要更新,customerId={}", wmCustomerBasicBo.getId());
                return;
            }
            wmCustomerDBMapper.updateCustomerExtPro(wmCustomerBasicBo.getId(), wmCustomerBasicBo.getCustomerExtPro());
        } catch (Exception e) {
            logger.error("updateCustomerExtPro,更新上级客户的其他信息发生异常,customerId={}", wmCustomerBasicBo.getId(), e);
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "更新上级客户其他信息发生异常");
        }
    }
}
