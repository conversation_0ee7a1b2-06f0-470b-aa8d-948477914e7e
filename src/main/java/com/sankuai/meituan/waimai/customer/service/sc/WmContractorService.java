package com.sankuai.meituan.waimai.customer.service.sc;

import com.alibaba.fastjson.JSONObject;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerDB;
import com.sankuai.meituan.waimai.thrift.customer.constant.*;
import com.sankuai.meituan.waimai.customer.service.customer.Impl.IWmCustomerRealService;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerPlatformDataParseService;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerService;
import com.sankuai.meituan.waimai.thrift.customer.domain.WmCustomerOplogBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.WmCustomerBasicBo;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 承销商业务逻辑
 *
 * <AUTHOR>
 * @date 2020/11/26
 */
@Slf4j
@Service
public class WmContractorService {

    @Autowired
    private WmCustomerService wmCustomerService;

    @Autowired
    private WmCustomerPlatformDataParseService wmCustomerPlatformDataParseService;

    @Autowired
    private IWmCustomerRealService wmLeafCustomerRealService;

    /**
     * 更新客户-食堂承包商的合作状态
     * <p>
     * 已合作定义：承包商有合同编号
     * <p>
     * 未合作定义：承包商合同编号删除
     * <p>
     * 关联食堂有变动时（包括关联食堂的增加与减少，食堂状态的变更）都需要重新计算承包商的合作状态。
     *
     * @param customerPrimaryId 客户主键ID
     */
    public void updateContractorWmCoStatus(Integer customerPrimaryId) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.service.sc.WmContractorService.updateContractorWmCoStatus(java.lang.Integer)");
        log.info("updateContractorWmCoStatus::更新食堂承包商的合作状态customerPrimaryId = {}", customerPrimaryId);
        if (customerPrimaryId == null || customerPrimaryId <= 0) {
            return;
        }
        try {
            WmCustomerDB wmCustomerDB = wmCustomerPlatformDataParseService.selectCustomerFromPlatformById(customerPrimaryId);
            if (wmCustomerDB == null) {
                return;
            }
            WmCustomerDB wmCustomerUpdateDB = new WmCustomerDB();
            wmCustomerUpdateDB.setId(customerPrimaryId);
            if (StringUtils.isNotBlank(wmCustomerDB.getContractNum())) {
                wmCustomerUpdateDB.setWmCoStatus(WmCoStatusEnum.YES.getCode());
            }
            if (wmCustomerUpdateDB.getWmCoStatus() == null) {
                wmCustomerUpdateDB.setWmCoStatus(WmCoStatusEnum.NO.getCode());
            }
            if (wmCustomerUpdateDB.getWmCoStatus().equals(wmCustomerDB.getWmCoStatus())) {
                // 合作状态未发生变化
                return;
            }
            wmCustomerService.updateCustomerContractor(wmCustomerUpdateDB, 0);
            WmCustomerBasicBo wmCustomerBasicBo = new WmCustomerBasicBo();
            BeanUtils.copyProperties(wmCustomerDB, wmCustomerBasicBo);
            wmCustomerBasicBo.setWmCoStatus(wmCustomerUpdateDB.getWmCoStatus());
            wmLeafCustomerRealService.insertCustomerOpLog(wmCustomerDB.getId(), 0, "系统", wmCustomerBasicBo, wmCustomerDB, WmCustomerOplogBo.OpType.UPDATE);
        } catch (Exception e) {
            log.error("updateContractorWmCoStatus::更新承销商合作状态失败customerPrimaryId = {}", customerPrimaryId, e);
        }
    }

    /**
     * 设置承包商类型客户的合作状态
     * 已合作：承包商有合同编号
     * 未合作：承包商合同编号删除
     * @param wmCustomerDb wmCustomerDb
     */
    public void setContractorWmCoStatus(WmCustomerDB wmCustomerDb) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.service.sc.WmContractorService.setContractorWmCoStatus(com.sankuai.meituan.waimai.customer.domain.WmCustomerDB)");
        log.info("[WmContractorService.setContractorWmCoStatus] input param: wmCustomerDb = {}", JSONObject.toJSONString(wmCustomerDb));
        if (wmCustomerDb == null) {
            return;
        }
        int wmCoStatusNew = 0;
        if (StringUtils.isNotBlank(wmCustomerDb.getContractNum())) {
            wmCoStatusNew = WmCoStatusEnum.YES.getCode();
        }
        // 合作状态未发生变化
        if (wmCustomerDb.getWmCoStatus() != null && wmCustomerDb.getWmCoStatus().equals(wmCoStatusNew)) {
            return;
        }
        wmCustomerDb.setWmCoStatus(wmCoStatusNew);
        log.info("[WmContractorService.setContractorWmCoStatus] customerPrimaryId = {}, wmCoStatusNew = {}", wmCustomerDb.getId(), wmCoStatusNew);
    }

}
