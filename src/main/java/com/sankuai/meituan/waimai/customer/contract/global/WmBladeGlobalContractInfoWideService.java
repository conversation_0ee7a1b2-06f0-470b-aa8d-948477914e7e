package com.sankuai.meituan.waimai.customer.contract.global;

import com.alibaba.fastjson.JSON;
import com.sankuai.meituan.waimai.customer.dao.blade.WmBladeGlobalContractInfoWideMapper;
import com.sankuai.meituan.waimai.customer.contract.global.dto.GlobalEcontractQueryCondition;
import com.sankuai.meituan.waimai.customer.domain.blade.WmBladeGlobalContractInfoWidePo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * @description:
 * @author: liuyunjie05
 * @create: 2024/1/10 16:40
 */
@Service
@Slf4j
public class WmBladeGlobalContractInfoWideService {

    @Resource
    private WmBladeGlobalContractInfoWideMapper wmBladeGlobalContractInfoWideMapper;

    public Long getTotalNumberWithLimitAuthority(GlobalEcontractQueryCondition queryCondition) {
        log.info("WmBladeGlobalContractInfoWideService#getTotalNumberWithLimitAuthority, queryCondition: {}", JSON.toJSONString(queryCondition));
        return wmBladeGlobalContractInfoWideMapper.totalWithLimitAuthority(queryCondition);
    }

    public Long getTotalNumberWithAllAuthority(GlobalEcontractQueryCondition queryCondition) {
        log.info("WmBladeGlobalContractInfoWideService#getTotalNumberWithAllAuthority, queryCondition: {}", JSON.toJSONString(queryCondition));
        return wmBladeGlobalContractInfoWideMapper.totalWithAllAuthority(queryCondition);
    }

    public List<WmBladeGlobalContractInfoWidePo> queryWithLimitAuthority(GlobalEcontractQueryCondition queryCondition) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.contract.global.WmBladeGlobalContractInfoWideService.queryWithLimitAuthority(com.sankuai.meituan.waimai.customer.contract.global.dto.GlobalEcontractQueryCondition)");
        log.info("WmBladeGlobalContractInfoWideService#queryWithLimitAuthority, queryCondition: {}", JSON.toJSONString(queryCondition));
        return wmBladeGlobalContractInfoWideMapper.queryWithLimitAuthority(queryCondition);
    }

    public List<WmBladeGlobalContractInfoWidePo> queryWithAllAuthority(GlobalEcontractQueryCondition queryCondition) {
        log.info("WmBladeGlobalContractInfoWideService#queryWithAllAuthority, queryCondition: {}", JSON.toJSONString(queryCondition));
        return wmBladeGlobalContractInfoWideMapper.queryWithAllAuthority(queryCondition);
    }


}
