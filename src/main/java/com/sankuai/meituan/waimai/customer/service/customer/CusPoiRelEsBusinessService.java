package com.sankuai.meituan.waimai.customer.service.customer;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.meituan.waimai.customer.config.MccCustomerConfig;
import com.sankuai.meituan.waimai.customer.constant.WmCustomerConstant;
import com.sankuai.meituan.waimai.customer.constant.WmCustomerPoiListESFields;
import com.sankuai.meituan.waimai.customer.dao.WmCustomerPoiDBMapper;
import com.sankuai.meituan.waimai.customer.ddd.adapter.WmPoiQueryAdapter;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerDB;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerPoiDB;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerPoiQueryCondtionVo;
import com.sankuai.meituan.waimai.thrift.constants.WmPoiFieldQueryConstant;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerErrorCodeConstants;
import com.sankuai.meituan.waimai.thrift.customer.constant.customer.CustomerPoiChildEnum;
import com.sankuai.meituan.waimai.thrift.domain.WmPoiAggre;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.action.bulk.BulkResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @date 20230919
 * @desc 客户门店关系ES操作服务
 */
@Service
@Slf4j
public class CusPoiRelEsBusinessService {

    @Autowired
    private WmCustomerPlatformDataParseService wmCustomerPlatformDataParseService;

    @Autowired
    private WmCustomerPoiService wmCustomerPoiService;

    @Autowired
    private WmPoiQueryAdapter wmPoiQueryAdapter;

    @Autowired
    private WmCustomerPoiListEsService wmCustomerPoiListEsService;

    @Autowired
    private WmCustomerPoiDBMapper wmCustomerPoiDBMapper;

    protected static final Set<String> WM_POI_FIELDS = com.google.common.collect.Sets.newHashSet(WmPoiFieldQueryConstant.WM_POI_FIELD_WM_POI_ID,
            WmPoiFieldQueryConstant.WM_POI_FIELD_NAME,
            WmPoiFieldQueryConstant.WM_POI_FIELD_FIRST_TAG,
            WmPoiFieldQueryConstant.WM_POI_FIELD_POI_BIZ_TYPES,
            WmPoiFieldQueryConstant.WM_POI_FIELD_BIZ_ORG_CODE,
            WmPoiFieldQueryConstant.WM_POI_FIELD_LABEL_IDS,
            WmPoiFieldQueryConstant.WM_POI_FIELD_CITY_LOCATION_ID,
            WmPoiFieldQueryConstant.WM_POI_FIELD_VALID,
            WmPoiFieldQueryConstant.WM_POI_FIELD_SUB_WM_POI_TYPE
    );

    /**
     * 根据客户ID列表批量更新"是否子门店"到ES
     *
     * @param customerId
     */
    public void batchSyncChildPoi2PoiRelES(List<Integer> customerId) throws WmCustomerException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.service.customer.CusPoiRelEsBusinessService.batchSyncChildPoi2PoiRelES(java.util.List)");
        List<WmCustomerDB> wmCustomerDBList = wmCustomerPlatformDataParseService.getCustomerDBList(customerId);
        if (CollectionUtils.isEmpty(wmCustomerDBList)) {
            log.info("batchSyncChildPoi2PoiRelES,未查到有效的客户信息，customerId={}", JSON.toJSONString(customerId));
            return;
        }
        //根据客户ID逐步处理
        for (WmCustomerDB wmCustomerDB : wmCustomerDBList) {
            List<WmCustomerPoiDB> customerPoiBoList =
                    wmCustomerPoiService.listPoiRelByCustomerId(wmCustomerDB.getId());
            if (CollectionUtils.isEmpty(customerPoiBoList)) {
                continue;
            }
            //关联门店数如果超过100个需要分批次处理更新
            List<List<WmCustomerPoiDB>> partCustomerPoiLists = Lists.partition(customerPoiBoList, 200);
            for (List<WmCustomerPoiDB> customerPoiList : partCustomerPoiLists) {
                syncChildPoi2PoiRelEs(customerPoiList);
            }
        }
    }

    /**
     * 根据客户门店关系表主键ID区间同步"是否子门店"字段
     *
     * @param minId
     * @param maxId
     */
    public void syncChildPoi2EsByIdInterval(Integer minId, Integer maxId){
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_1", "com.sankuai.meituan.waimai.customer.service.customer.CusPoiRelEsBusinessService.syncChildPoi2EsByIdInterval(java.lang.Integer,java.lang.Integer)");
        WmCustomerPoiQueryCondtionVo queryConditionVo = new WmCustomerPoiQueryCondtionVo();
        queryConditionVo.setMinId(minId);
        queryConditionVo.setMaxId(maxId);
        List<WmCustomerPoiDB> poiDBList = wmCustomerPoiDBMapper.selectCustomerPoiRelByCondition(queryConditionVo);
        if (CollectionUtils.isEmpty(poiDBList)) {
            return;
        }
        //总数超过100则分批次处理
        List<List<WmCustomerPoiDB>> partCustomerPoiLists = Lists.partition(poiDBList, 100);
        for (List<WmCustomerPoiDB> customerPoiList : partCustomerPoiLists) {
            syncChildPoi2PoiRelEs(customerPoiList);
        }
        log.info("syncChildPoi2EsByIdInterval,ID区间的是否自门店字段更新到ES完成,minId={},maxId={}", minId, maxId);
    }

    /**
     * 根据客户门店关系记录同步「是否子门店字段」
     *
     * @param customerPoiList
     */
    public void syncChildPoi2PoiRelEs(List<WmCustomerPoiDB> customerPoiList) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.service.customer.CusPoiRelEsBusinessService.syncChildPoi2PoiRelEs(java.util.List)");
        List<Integer> customerPoiIds = customerPoiList.stream().map(x -> x.getId()).collect(Collectors.toList());
        try {
            List<Long> wmPoiIds = customerPoiList.stream().map(x -> x.getWmPoiId()).collect(Collectors.toList());
            Map<Integer, Map<String, Object>> map = Maps.newHashMap();
            //获取门店与是否子门店关系map
            Map<Long, Boolean> poiChildTagMaps = getWmPoiIdWithChildPoi(wmPoiIds);
            for (WmCustomerPoiDB wmCustomerPoiDB : customerPoiList) {
                Boolean isChildTag = poiChildTagMaps.get(wmCustomerPoiDB.getWmPoiId());
                map.put(wmCustomerPoiDB.getId(), WmCustomerPoiListEsService.makeMap(new String[]{WmCustomerPoiListESFields.CHILD_POI_FLAG.getField()},
                        new Object[]{isChildTag ? CustomerPoiChildEnum.IS_CHILD_POI.getCode() : CustomerPoiChildEnum.NOT_CHILD_POI.getCode()}));
            }

            BulkResponse response = wmCustomerPoiListEsService.bulkUpdate(map);
            if (response == null) {
                throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "是否子门店字段同步客户门店ES失败");
            }
        } catch (Exception e) {
            log.error("syncChildPoi2PoiRelEs,同步是否子门店字段到客户门店关系ES异常,customerPoiIds={}", JSON.toJSONString(customerPoiIds));
        }
    }


    /**
     * 组装门店与是否子门店对应关系
     *
     * @param wmPoiIds
     * @return
     */
    private Map<Long, Boolean> getWmPoiIdWithChildPoi(List<Long> wmPoiIds) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.customer.CusPoiRelEsBusinessService.getWmPoiIdWithChildPoi(java.util.List)");
        Map<Long, Boolean> poiChildTagMaps = new HashMap<>();
        List<WmPoiAggre> wmPoiAggreList = wmPoiQueryAdapter.getWmPoiAggre(wmPoiIds, WM_POI_FIELDS);
        for (WmPoiAggre wmPoiAggre : wmPoiAggreList) {
            Boolean childPoiFlag = false;
            // 判断门店是否为子门店
            if (MccCustomerConfig.getSubPoiIdentifySwitch()) {
                //  根据子门店类型判断是否为子门店
                childPoiFlag = checkChildPoiByPoiType(wmPoiAggre.getSub_wm_poi_type());
            } else {
                childPoiFlag = checkChildPoiByTagId(wmPoiAggre.getLabel_ids());
            }
            poiChildTagMaps.put(wmPoiAggre.getWm_poi_id(), childPoiFlag);
        }
        return poiChildTagMaps;
    }


    /**
     * 更新门店的子门店字段到客户门店关系ES中
     *
     * @param wmPoiId
     */
    public void updateChildPoi2Es(Integer wmPoiId) throws WmCustomerException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.service.customer.CusPoiRelEsBusinessService.updateChildPoi2Es(java.lang.Integer)");
        WmPoiAggre wmPoiAggre = wmPoiQueryAdapter.getWmPoiAggre(wmPoiId.longValue(), WM_POI_FIELDS);
        if (wmPoiAggre == null) {
            log.error("updateChildPoi2Es,门店ID不存在，未查询到有效记录,wmPoiId={}", wmPoiId);
            return;
        }
        List<WmCustomerPoiDB> poiDBList = wmCustomerPoiDBMapper.listCustomerPoiRelByWmPoiId(wmPoiId.longValue());
        if (CollectionUtils.isEmpty(poiDBList)) {
            return;
        }
        Map<Integer, Map<String, Object>> map = Maps.newHashMap();
        //获取门店与是否子门店关系map
        for (WmCustomerPoiDB wmCustomerPoiDB : poiDBList) {
            Boolean isChildTag = false;
            if (MccCustomerConfig.getSubPoiIdentifySwitch()) {
                //  根据子门店类型判断是否为子门店
                isChildTag = checkChildPoiByPoiType(wmPoiAggre.getSub_wm_poi_type());
            } else {
                //  根据标签ID判断是否为子门店
                isChildTag = checkChildPoiByTagId(wmPoiAggre.getLabel_ids());
            }
            map.put(wmCustomerPoiDB.getId(), WmCustomerPoiListEsService.makeMap(new String[]{WmCustomerPoiListESFields.CHILD_POI_FLAG.getField()},
                    new Object[]{isChildTag ? CustomerPoiChildEnum.IS_CHILD_POI.getCode() : CustomerPoiChildEnum.NOT_CHILD_POI.getCode()}));
        }
        //批量更新客户门店关系ES的是否子门店字段
        BulkResponse response = wmCustomerPoiListEsService.bulkUpdate(map);
        if (response == null) {
            throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "是否子门店字段同步客户门店ES失败");
        }
    }


    /**
     * 根据门店当前标签情况判断是否子门店
     *
     * @param poiLabels
     * @return
     */
    public Boolean checkChildPoiByTagId(String poiLabels) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_2", "com.sankuai.meituan.waimai.customer.service.customer.CusPoiRelEsBusinessService.checkChildPoiByTagId(java.lang.String)");
        if (StringUtils.isEmpty(poiLabels)) {
            return false;
        }
        List<Integer> poiRelTagIds = Arrays.stream(poiLabels.split(",")).map(Integer::valueOf).collect(Collectors.toList());
        if (!Collections.disjoint(poiRelTagIds, MccCustomerConfig.getSubPoiTagId())) {
            return true;
        }
        return false;
    }

    /**
     * 根据门店子门店类型判断是否是子门店
     *
     *
     */

    public Boolean checkChildPoiByPoiType(Integer subWmPoiType) {
        if (subWmPoiType == null) {
            return false;
        }
        if (!WmCustomerConstant.POI_NOT_SUB_POI_TYPE.equals(subWmPoiType)) {
            return true;
        }
        return false;
    }


}
