package com.sankuai.meituan.waimai.customer.service.sc.poi;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.sankuai.meituan.waimai.customer.adapter.WmApprovalRateAdapter;
import com.sankuai.meituan.waimai.customer.adapter.WmEmployClient;
import com.sankuai.meituan.waimai.customer.dao.WmCustomerDBMapper;
import com.sankuai.meituan.waimai.customer.dao.sc.*;
import com.sankuai.meituan.waimai.customer.ddd.adapter.WmPoiQueryAdapter;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerDB;
import com.sankuai.meituan.waimai.customer.domain.sc.*;
import com.sankuai.meituan.waimai.customer.service.common.WmEmployeeService;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerPlatformDataParseService;
import com.sankuai.meituan.waimai.customer.service.sc.canteen.sensitive.WmScCanteenSensitiveWordsService;
import com.sankuai.meituan.waimai.infra.domain.WmEmploy;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.CanteenAttributeEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.CanteenPoiOperateTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.CanteenPoiStatusEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.WmScCodeConstants;
import com.sankuai.meituan.waimai.thrift.customer.domain.sc.*;
import com.sankuai.meituan.waimai.thrift.domain.WmPoiAggre;
import com.sankuai.meituan.waimai.thrift.exception.WmSchCantException;
import com.sankuai.meituan.waimai.thrift.rateApproval.constant.WmApprovalFeeModeEnum;
import com.sankuai.meituan.waimai.thrift.rateApproval.vo.response.WmApprovalRateResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

import static com.sankuai.meituan.waimai.thrift.constants.WmPoiFieldQueryConstant.*;
import static com.sankuai.meituan.waimai.thrift.constants.WmPoiFieldQueryConstant.WM_POI_FIELD_FIRST_ONLINE_TIME;

@Slf4j
@Service
public class WmScCanteenInfoService {

    // 默认打标次数
    private static final int DEFAULT_ADD_TAG_NUM = 0;

    @Autowired
    private WmEmployClient wmEmployClient;

    @Autowired
    private WmPoiQueryAdapter wmPoiQueryAdapter;

    @Autowired
    private WmSchoolMapper wmSchoolMapper;

    @Autowired
    private WmCanteenMapper wmCanteenMapper;

    @Autowired
    private WmScCanteenPoiHistoryMapper wmScCanteenPoiHistoryMapper;

    @Autowired
    private WmApprovalRateAdapter wmApprovalRateAdapter;

    @Autowired
    private WmScCanteenPoiAttributeService wmScCanteenPoiAttributeService;

    @Autowired
    private WmScCanteenPoiTaskMapper wmScCanteenPoiTaskMapper;

    @Autowired
    private WmScCanteenSensitiveWordsService wmScCanteenSensitiveWordsService;

    @Autowired
    private WmScCanteenPoiTaskDetailMapper wmScCanteenPoiTaskDetailMapper;

    @Autowired
    private WmScSchoolAreaMapper wmScSchoolAreaMapper;

    @Autowired
    protected WmCustomerDBMapper wmCustomerDBMapper;

    @Autowired
    private WmEmployeeService wmEmployeeService;

    /**
     * 获取任务系统详情中的食堂，学校基本信息
     *
     * @param canteenId    食堂ID
     * @param isSpecialFee 是否加工特批费率
     * @return 任务基本详情
     */
    public WmCanPoiTaskBaseBo buildPoiTaskBaseBo(int canteenId, boolean isSpecialFee) throws WmSchCantException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.service.sc.poi.WmScCanteenInfoService.buildPoiTaskBaseBo(int,boolean)");
        log.info("buildPoiTaskBaseBo,canteenId={},isSpecialFee={}",canteenId,isSpecialFee);
        //获取食堂信息
        WmCanteenSearchCondition condition = new WmCanteenSearchCondition();
        condition.setId(canteenId > 100000 ? canteenId - 100000 : canteenId);
        List<WmCanteenDB> wmCanteenDBS = wmCanteenMapper.selectByCondition(condition);
        WmCanteenDB wmCanteenDB = wmCanteenDBS.get(0);
        if (wmCanteenDB == null) {
            return null;
        }
        //获取学校信息
        WmSchoolDB wmSchoolDB = wmSchoolMapper.selectSchoolById(wmCanteenDB.getSchoolId());
        if (wmSchoolDB == null) {
            return null;
        }
        WmCanPoiTaskBaseBo baseBo = new WmCanPoiTaskBaseBo();

        // 加工食堂信息
        WmCanPoiTaskCanInfoBo canInfoBo = new WmCanPoiTaskCanInfoBo();
        canInfoBo.setCanteenId(wmCanteenDB.getCanteenId());
        canInfoBo.setCanteenName(wmCanteenDB.getCanteenName());
        canInfoBo.setCanteenAttribute(wmCanteenDB.getCanteenAttribute());
        canInfoBo.setCanteenAttributeDesc(CanteenAttributeEnum.getByType(wmCanteenDB.getCanteenAttribute()).getName());
        canInfoBo.setCanPoiNum(wmCanteenDB.getStallNum());
        canInfoBo.setContractorId(wmCanteenDB.getContractorId());
        canInfoBo.setContractorName(wmCanteenDB.getContractorName());
        canInfoBo.setOfflineBizStallNum(wmCanteenDB.getOfflineBizStallNum());
        // 食堂责任人设置
        if(wmCanteenDB.getResponsiblePerson() != null){
            WmEmploy ResponsiblePerson = wmEmployClient.getByMisId(wmCanteenDB.getResponsiblePerson());
            if (ResponsiblePerson != null) {
                canInfoBo.setResponsiblePersonUid(String.valueOf(ResponsiblePerson.getUid()));
                canInfoBo.setResponsiblePersonName(String.format("%s（%s）", ResponsiblePerson.getName(), ResponsiblePerson.getMisId()));
            }
        }
        // 客户责任人设置
        if (wmCanteenDB.getContractorId() != null && wmCanteenDB.getContractorId() > 0) {
            WmCustomerDB wmCustomerDB = wmCustomerDBMapper.selectCustomerById(wmCanteenDB.getContractorId());
            if (null != wmCustomerDB && wmCustomerDB.getOwnerUid() > 0) {
                WmEmploy customerOwner = wmEmployeeService.getWmEmployById(wmCustomerDB.getOwnerUid());
                if (customerOwner != null) {
                    canInfoBo.setCustomerOwnerUid(customerOwner.getUid());
                    canInfoBo.setCustomerOwnerName(String.format("%s（%s）", customerOwner.getName(), customerOwner.getMisId()));
                }
            }
        }

        //获取特批费率信息
        if (isSpecialFee) {
            List<WmApprovalRateResponse> wmApprovalRateResponseList = wmApprovalRateAdapter.queryApprovalRateListByCanteenId(wmCanteenDB.getCanteenId(), WmApprovalFeeModeEnum.WAIMAI_V3);
            if (CollectionUtils.isNotEmpty(wmApprovalRateResponseList)) {
                Long startTime = Long.MAX_VALUE;
                Long endTime = Long.MIN_VALUE;
                for (WmApprovalRateResponse wmApprovalRateResponse : wmApprovalRateResponseList) {
                    //获取最早的开始时间
                    if (wmApprovalRateResponse.getStartTime() != null) {
                        startTime = Math.min(wmApprovalRateResponse.getStartTime(), startTime);
                    }
                    //获取最晚的结束时间
                    if (wmApprovalRateResponse.getEndTime() != null) {
                        endTime = Math.max(wmApprovalRateResponse.getEndTime(), endTime);
                    }
                }
                if (startTime <= endTime) {
                    canInfoBo.setSpecialFeeStartTime(startTime);
                    canInfoBo.setSpecialFeeEndTime(endTime);
                }
            }
        }

        baseBo.setCanteenInfo(canInfoBo);


        // 加工学校信息
        WmCanPoiTaskScInfoBo scInfoBo = new WmCanPoiTaskScInfoBo();
        scInfoBo.setSchoolId(wmSchoolDB.getSchoolId());
        scInfoBo.setSchoolName(wmSchoolDB.getSchoolName());
        scInfoBo.setSchoolAddress(wmSchoolDB.getSchoolAddress());
        scInfoBo.setSchoolOwnerUid(0);
        scInfoBo.setSchoolOwnerName(String.format("%s（%s）", "未知", wmSchoolDB.getResponsiblePerson()));
        WmEmploy schoolOwner = wmEmployClient.getByMisId(wmSchoolDB.getResponsiblePerson());
        if (schoolOwner != null) {
            scInfoBo.setSchoolOwnerUid(schoolOwner.getUid());
            scInfoBo.setSchoolOwnerName(String.format("%s（%s）", schoolOwner.getName(), schoolOwner.getMisId()));
        }
        scInfoBo.setWdcClueId(wmSchoolDB.getWdcClueId());
        baseBo.setSchoolInfo(scInfoBo);

        // 加工蜂窝信息
        WmCanPoiTaskAorInfoBo aorInfoBo = new WmCanPoiTaskAorInfoBo();
        aorInfoBo.setAorId(wmSchoolDB.getAorId());
        aorInfoBo.setAorName(wmSchoolDB.getAorName());
        baseBo.setAorInfo(aorInfoBo);

        log.info("buildPoiTaskBaseBo = {}", JSONObject.toJSONString(baseBo));

        return baseBo;
    }

    /**
     * 加工任务系统详情中的门店基本信息
     *
     * @param wmPoiIdList 门店ID列表
     * @return 任务基本详情中门店信息
     */
    public List<WmCanPoiTaskPoiInfoBo> buildPoiInfoBos(List<Long> wmPoiIdList) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_2", "com.sankuai.meituan.waimai.customer.service.sc.poi.WmScCanteenInfoService.buildPoiInfoBos(java.util.List)");
        if (CollectionUtils.isEmpty(wmPoiIdList)) {
            return Lists.newArrayList();
        }
        //获取门店信息
        List<WmPoiAggre> wmPoiAggreList = wmPoiQueryAdapter.getWmPoiAggre(wmPoiIdList, Sets.newHashSet(WM_POI_FIELD_WM_POI_ID, WM_POI_FIELD_NAME,
                WM_POI_FIELD_VALID, WM_POI_FIELD_FIRST_ONLINE_TIME));
        List<WmScCanteenPoiHistoryBO> wmScCanteenPoiHistoryBOS = wmScCanteenPoiHistoryMapper.getAddTagNum(wmPoiIdList);
        Map<Long, Integer> wmPoiIdMapAddTagNum = new HashMap<Long, Integer>();
        if(CollectionUtils.isNotEmpty(wmScCanteenPoiHistoryBOS)){
            wmPoiIdMapAddTagNum = wmScCanteenPoiHistoryBOS.stream().collect(Collectors.toMap(WmScCanteenPoiHistoryBO::getWmPoiId, item -> item.getAddTagNum()));
        }
        Map<Long, Integer> finalWmPoiIdMapAddTagNum = wmPoiIdMapAddTagNum;
        return wmPoiAggreList.stream().map((item) -> {
            WmCanPoiTaskPoiInfoBo poiInfo = new WmCanPoiTaskPoiInfoBo();
            poiInfo.setWmPoiId(item.getWm_poi_id());
            poiInfo.setWmPoiName(item.getName());
            poiInfo.setWmPoiStatus(item.getValid());
            poiInfo.setWmPoiStatusDes(CanteenPoiStatusEnum.getName(item.getValid()));
            poiInfo.setFirstOnlineTime(item.getFirst_online_time());
            if (finalWmPoiIdMapAddTagNum.get(item.getWm_poi_id()) != null) {
                poiInfo.setAddTagNum(finalWmPoiIdMapAddTagNum.get(item.getWm_poi_id()));
            } else {
                poiInfo.setAddTagNum(DEFAULT_ADD_TAG_NUM);
            }
            return poiInfo;
        }).collect(Collectors.toList());
    }

    /**
     * 获取食堂绑定门店数量
     * @param canteenPrimaryKey 食堂主键ID
     * @return 食堂绑定门店数量
     */
    public int getCurrentCanteenPoiNum(int canteenPrimaryKey) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.sc.poi.WmScCanteenInfoService.getCurrentCanteenPoiNum(int)");
        List<WmScCanteenPoiAttributeDO> list = wmScCanteenPoiAttributeService.selectEffectByCanteenPrimaryId(canteenPrimaryKey);
        if (CollectionUtils.isEmpty(list)) {
            log.info("[getCurrentCanteenPoiNum] canteenPrimaryKey = {}, storeNum = 0", canteenPrimaryKey);
            return 0;
        }
        Set<Long> wmPoiIdSet = list.stream().map(x -> x.getWmPoiId()).collect(Collectors.toSet());
        log.info("[getCurrentCanteenPoiNum] canteenPrimaryKey = {}, storeNum = {}", canteenPrimaryKey, wmPoiIdSet.size());
        return wmPoiIdSet.size();
    }


    public WmCanteenPoiTaskBO buildTaskBO(long taskId) throws WmSchCantException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.sc.poi.WmScCanteenInfoService.buildTaskBO(long)");
        WmCanteenPoiTaskBO wmCanteenPoiTaskBO = new WmCanteenPoiTaskBO();
        WmScCanteenPoiTaskDO wmScCanteenPoiTaskDO = wmScCanteenPoiTaskMapper.selectByPrimaryKey(taskId);
        wmCanteenPoiTaskBO.setId(wmScCanteenPoiTaskDO.getId());
        wmCanteenPoiTaskBO.setUserId(wmScCanteenPoiTaskDO.getUserId());
        wmCanteenPoiTaskBO.setUserName(wmScCanteenPoiTaskDO.getUserName());
        if (wmScCanteenPoiTaskDO.getCanteenIdFrom() != null && wmScCanteenPoiTaskDO.getCanteenIdFrom() > 0) {
            WmCanteenDB canteenDBFrom = wmCanteenMapper.selectCanteenById(wmScCanteenPoiTaskDO.getCanteenIdFrom());
            wmCanteenPoiTaskBO.setCanteenFrom(canteenDBFrom);
        }

        int canteenPrimaryKey = wmScCanteenPoiTaskDO.getCanteenIdTo();
        WmCanteenDB wmCanteenDB = wmCanteenMapper.selectCanteenById(canteenPrimaryKey);
        if (wmCanteenDB == null) {
            throw new WmSchCantException(WmScCodeConstants.BIZ_PARA_ERROR, "加工任务参数异常:待绑食堂未找到");
        }
        wmScCanteenSensitiveWordsService.readWhenSelect(wmCanteenDB);
        wmCanteenPoiTaskBO.setCanteenTo(wmCanteenDB);
        WmSchoolDB wmSchoolDB = wmSchoolMapper.selectSchoolById(wmCanteenDB.getSchoolId());
        if (wmSchoolDB == null) {
            throw new WmSchCantException(WmScCodeConstants.BIZ_PARA_ERROR, "加工任务参数异常:待绑食堂学校未找到");
        }
        wmCanteenPoiTaskBO.setSchoolTo(wmSchoolDB);
        List<WmScSchoolAreaDO> wmScSchoolAreaDOList = wmScSchoolAreaMapper.selectBySchoolId(wmSchoolDB.getId());
        if (CollectionUtils.isNotEmpty(wmScSchoolAreaDOList)) {
            List<String> schoolAreaList = new ArrayList<>();
            for (WmScSchoolAreaDO wmScSchoolAreaDO : wmScSchoolAreaDOList) {
                if (StringUtils.isNotBlank(wmScSchoolAreaDO.getArea())) {
                    schoolAreaList.add(wmScSchoolAreaDO.getArea());
                }
            }
            wmCanteenPoiTaskBO.setSchoolAreaList(schoolAreaList);
        }

        List<WmScCanteenPoiTaskDetailDO> detailDOList = wmScCanteenPoiTaskDetailMapper.selectByCanteenPoiTaskId(taskId);
        if (CollectionUtils.isEmpty(detailDOList)) {
            return wmCanteenPoiTaskBO;
        }
        List<Long> wmPoiIdList = detailDOList.stream()
                .map(WmScCanteenPoiTaskDetailDO::getWmPoiId)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(wmPoiIdList)) {
            return wmCanteenPoiTaskBO;
        }

        wmCanteenPoiTaskBO.setWmPoiIdList(wmPoiIdList);
        List<WmPoiAggre> wmPoiAggreList = wmPoiQueryAdapter.getWmPoiAggre(wmPoiIdList, Sets.newHashSet(
                WM_POI_FIELD_WM_POI_ID,
                WM_POI_FIELD_NAME,
                WM_POI_FIELD_NAME,
                WM_POI_FIELD_VALID,
                WM_POI_FIELD_IS_DELETE,
                WM_POI_FIELD_AOR_ID,
                WM_POI_FIELD_OWNER_UID,
                WM_POI_FIELD_FIRST_TAG,
                WM_POI_FIELD_LATITUDE,
                WM_POI_FIELD_LONGITUDE));
        if (CollectionUtils.isNotEmpty(wmPoiAggreList)) {
            wmCanteenPoiTaskBO.setWmPoiAggreList(wmPoiAggreList);
        }
        return wmCanteenPoiTaskBO;
    }
}
