package com.sankuai.meituan.waimai.customer.service.sc.thirdworkplace;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.sankuai.meituan.waimai.poi.annotation.ExcelExportField;
import lombok.Data;

@Data
@ExcelIgnoreUnannotated
public class WmScThirdWorkplaceQueryMetricsExcelModel {

    @ExcelExportField(title = "日期范围", priority = 10)
    private String dateRange;

    @ExcelExportField(title = "学校数量", priority = 20)
    private String schoolNum;

    @ExcelExportField(title = "在校师生数", priority = 30)
    private String teaStuNum;

    @ExcelExportField(title = "在校师生数环比", priority = 40)
    private String teaStuNumMom;

    @ExcelExportField(title = "食堂数量", priority = 50)
    private String canteenNum;

    @ExcelExportField(title = "档口数量", priority = 60)
    private String stallNum;

    @ExcelExportField(title = "档口数量环比", priority = 70)
    private String stallNumMom;

    @ExcelExportField(title = "线下营业档口数", priority = 80)
    private String cafeteriaOfflineOpenStallNum;

    @ExcelExportField(title = "线下营业档口数环比", priority = 90)
    private String cafeteriaOfflineOpenStallNumMom;

    @ExcelExportField(title = "可上线档口数", priority = 100)
    private String canteenPreOnlineStallNum;

    @ExcelExportField(title = "可上线档口数环比", priority = 110)
    private String canteenPreOnlineStallNumMom;

    @ExcelExportField(title = "在线档口数", priority = 120)
    private String onlinePoiNum;

    @ExcelExportField(title = "在线档口数环比", priority = 130)
    private String onlinePoiNumMom;

    @ExcelExportField(title = "在线档口渗透率", priority = 140)
    private String cafeteriaOnlineStallInfiltrationRate;

    @ExcelExportField(title = "在线档口渗透率环比", priority = 150)
    private String cafeteriaOnlineStallInfiltrationRateMom;

    @ExcelExportField(title = "营业档口数", priority = 160)
    private String openPoiNum;

    @ExcelExportField(title = "营业档口数环比", priority = 170)
    private String openPoiNumMom;

    @ExcelExportField(title = "营业档口渗透率", priority = 180)
    private String cafeteriaOpenStallInfiltrationRate;

    @ExcelExportField(title = "营业档口渗透率环比", priority = 190)
    private String cafeteriaOpenStallInfiltrationRateMom;

    @ExcelExportField(title = "交易档口数", priority = 200)
    private String txnPoiNum;

    @ExcelExportField(title = "交易档口数环比", priority = 210)
    private String txnPoiNumMom;

    @ExcelExportField(title = "交易档口渗透率", priority = 220)
    private String cafeteriaTxnStallInfiltrationRate;

    @ExcelExportField(title = "交易档口渗透率环比", priority = 230)
    private String cafeteriaTxnStallInfiltrationRateMom;

    @ExcelExportField(title = "档口动销率", priority = 240)
    private String txnrate;

    @ExcelExportField(title = "档口动销率环比", priority = 250)
    private String txnrateMom;

    @ExcelExportField(title = "新增档口数", priority = 260)
    private String newPoiNum;

    @ExcelExportField(title = "新增档口数环比", priority = 270)
    private String newPoiNumMom;

    @ExcelExportField(title = "实付交易额", priority = 280)
    private String finActualAmt;

    @ExcelExportField(title = "实付交易额环比", priority = 290)
    private String finActualAmtMom;

    @ExcelExportField(title = "订单量", priority = 300)
    private String finOrdNum;

    @ExcelExportField(title = "订单量环比", priority = 310)
    private String finOrdNumMom;

    @ExcelExportField(title = "客单价", priority = 320)
    private String ordAvgFinActualAmt;

    @ExcelExportField(title = "客单价环比", priority = 330)
    private String ordAvgFinActualAmtMom;

    @ExcelExportField(title = "单产", priority = 340)
    private String poiAvgSettleOrdNum;

    @ExcelExportField(title = "单产环比", priority = 350)
    private String poiAvgSettleOrdNumMom;

    @ExcelExportField(title = "交易用户数", priority = 360)
    private String finUsrNum;

    @ExcelExportField(title = "交易用户数环比", priority = 370)
    private String finUsrNumMom;

    @ExcelExportField(title = "食堂新增用户数", priority = 380)
    private String cafeteriaNewUserNum;

    @ExcelExportField(title = "食堂新增用户数环比", priority = 390)
    private String cafeteriaNewUserNumMom;

    @ExcelExportField(title = "人均交易频次", priority = 400)
    private String usrAvgFinOrdNum;

    @ExcelExportField(title = "人均交易频次环比", priority = 410)
    private String usrAvgFinOrdNumMom;
}