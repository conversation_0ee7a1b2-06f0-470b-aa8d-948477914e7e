package com.sankuai.meituan.waimai.customer.service.sc.dbus;

import com.alibaba.fastjson.JSON;
import com.meituan.dbus.common.DbusUtils;
import com.meituan.dbus.common.StaticUtils;
import com.meituan.dbus.thriftV2.utils.ThriftV2Utils;
import com.sankuai.meituan.waimai.customer.constant.sc.WmScTableDbusEnum;
import com.sankuai.meituan.waimai.customer.service.sc.mafka.product.WmScCanteenSendMsgService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 食堂表
 */
@Service
@Slf4j
public class WmScCanteenTableEventImpl implements ITableEvent {

    @Autowired
    private WmScCanteenSendMsgService wmScCanteenSendMsgService;


    @Override
    public WmScTableDbusEnum getTable() {
        return WmScTableDbusEnum.TABLE_WM_SC_CANTEEN;
    }

    @Override
    public String handleUpdate(TableEvent tableEvent) {
        log.info("监听食堂表信息变更handleUpdate::tableEvent = {}", JSON.toJSONString(tableEvent));
        if (!checkParamWhenHandleInsert(tableEvent)) {
            return StaticUtils.ok;
        }
        DbusUtils utils = ThriftV2Utils.newThriftV2InstanceForUpdate(tableEvent.getMetaJsonData(), tableEvent.getDataMapJson(), tableEvent.getDiffJson());
        wmScCanteenSendMsgService.sendMsgWhenTableUpdate(utils);
        return StaticUtils.ok;
    }

    @Override
    public String handleInsert(TableEvent tableEvent) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_1", "com.sankuai.meituan.waimai.customer.service.sc.dbus.WmScCanteenTableEventImpl.handleInsert(com.sankuai.meituan.waimai.customer.service.sc.dbus.TableEvent)");
        log.info("监听食堂表信息变更handleInsert::tableEvent = {}", JSON.toJSONString(tableEvent));
        if (!checkParamWhenHandleInsert(tableEvent)) {
            return StaticUtils.ok;
        }
        DbusUtils utils = ThriftV2Utils.newThriftV2InstanceForInsert(tableEvent.getMetaJsonData(), tableEvent.getDataMapJson());
        return StaticUtils.ok;
    }

    @Override
    public String handleDelete(TableEvent tableEvent) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.service.sc.dbus.WmScCanteenTableEventImpl.handleDelete(com.sankuai.meituan.waimai.customer.service.sc.dbus.TableEvent)");
        //无物理删除，不会走到这里
        return StaticUtils.ok;
    }


}
