package com.sankuai.meituan.waimai.customer.contract.partner.impl;

import com.sankuai.meituan.waimai.customer.config.MccConfig;
import com.sankuai.meituan.waimai.customer.contract.partner.WmPartnerCustomerContractService;
import com.sankuai.meituan.waimai.customer.contract.partner.ability.cancelsign.WmPartnerCustomerContractCancelAbilityService;
import com.sankuai.meituan.waimai.customer.contract.partner.ability.check.WmPartnerCustomerContractCheckAbilityService;
import com.sankuai.meituan.waimai.customer.contract.partner.ability.query.WmPartnerCustomerContractQueryAbilityService;
import com.sankuai.meituan.waimai.customer.contract.partner.ability.save.WmPartnerCustomerContractSaveAbilityService;
import com.sankuai.meituan.waimai.customer.contract.partner.ability.sms.WmPartnerCustomerContractSmsAbilityService;
import com.sankuai.meituan.waimai.customer.domain.WmEcontractSignBatchDB;
import com.sankuai.meituan.waimai.customer.service.sign.biz.WmEcontractBigBatchParseService;
import com.sankuai.meituan.waimai.customer.service.sign.signpack.WmEcontractSignPackService;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerErrorCodeConstants;
import com.sankuai.meituan.waimai.thrift.customer.constant.partner.C2SignStatusCheckEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.partner.ContractSaveSceneTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.partnercontract.*;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.partner.DaoCanContractContext;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @description:
 * @author: liuyunjie05
 * @create: 2024/8/2 17:09
 */
@Slf4j
@Service
public class WmPartnerCustomerContractServiceImpl implements WmPartnerCustomerContractService {

    @Resource
    private List<WmPartnerCustomerContractSaveAbilityService> wmPartnerCustomerContractSaveAbilityServiceList;

    @Resource
    private List<WmPartnerCustomerContractCheckAbilityService> wmPartnerCustomerContractCheckAbilityServiceList;

    @Resource
    private WmPartnerCustomerContractCancelAbilityService wmPartnerCustomerContractCancelAbilityService;

    @Resource
    private WmPartnerCustomerContractQueryAbilityService wmPartnerCustomerContractQueryAbilityService;

    @Resource
    private WmEcontractSignPackService wmEcontractSignPackService;

    @Resource
    private WmPartnerCustomerContractSmsAbilityService wmPartnerCustomerContractSmsAbilityService;

    private final Map<ContractSaveSceneTypeEnum, WmPartnerCustomerContractSaveAbilityService> sceneTypeMap = new HashMap<>();

    private final Map<C2SignStatusCheckEnum, WmPartnerCustomerContractCheckAbilityService> checkTypeMap = new HashMap<>();

    @PostConstruct
    private void init() {
        for (WmPartnerCustomerContractSaveAbilityService saveAbilityService : wmPartnerCustomerContractSaveAbilityServiceList) {
            sceneTypeMap.put(saveAbilityService.getSupportSceneType(), saveAbilityService);
        }
        for (WmPartnerCustomerContractCheckAbilityService checkAbilityService : wmPartnerCustomerContractCheckAbilityServiceList) {
            checkTypeMap.put(checkAbilityService.getCheckType(), checkAbilityService);
        }
    }

    @Override
    public CustomerContractSaveResponseDTO saveCustomerContract(CustomerContractSaveRequestDTO requestDTO) throws WmCustomerException {
        if (!MccConfig.isSupportDaoCanCustomerContract()) {
            log.warn("WmPartnerCustomerContractServiceImpl#saveCustomerContract, 不支持到餐侧创建合同");
            CustomerContractSaveResponseDTO responseDTO = new CustomerContractSaveResponseDTO();
            responseDTO.setSuccess(false);
            responseDTO.setFailMsg("不支持到餐侧创建合同");
            return responseDTO;
        }
        ContractSaveSceneTypeEnum sceneType = requestDTO.getSceneType();
        WmPartnerCustomerContractSaveAbilityService abilityService = sceneTypeMap.get(sceneType);
        if (abilityService == null) {
            throw new WmCustomerException(CustomerErrorCodeConstants.PARAM_ERROR, "渠道异常");
        }
        log.info("WmPartnerCustomerContractServiceImpl#saveCustomerContract, abilityService: {}", abilityService.getClass().getSimpleName());
        return abilityService.saveCustomerContract(requestDTO);
    }

    @Override
    public C2SignStatusCheckResponseDTO checkC2ContractSignStatus(C2SignStatusCheckRequestDTO requestDTO) throws WmCustomerException {
        C2SignStatusCheckEnum checkType = requestDTO.getCheckType();
        WmPartnerCustomerContractCheckAbilityService checkAbilityService = checkTypeMap.get(checkType);
        if (checkAbilityService == null) {
            throw new WmCustomerException(CustomerErrorCodeConstants.PARAM_ERROR, "校验类型异常");
        }
        log.info("WmPartnerCustomerContractServiceImpl#saveCustomerContract, checkAbilityService: {}", checkAbilityService.getClass().getSimpleName());
        return checkAbilityService.checkC2ContractSignStatus(requestDTO);
    }

    @Override
    public CustomerContractCancelSignResponseDTO cancelCustomerContractSign(CustomerContractCancelSignRequestDTO requestDTO) throws WmCustomerException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.contract.partner.impl.WmPartnerCustomerContractServiceImpl.cancelCustomerContractSign(CustomerContractCancelSignRequestDTO)");
        if (MccConfig.notSupportCancelCustomerContract()) {
            log.warn("WmPartnerCustomerContractServiceImpl#cancelCustomerContractSign, 不支持取消签约");
            throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "不支持取消签约");
        }
        return wmPartnerCustomerContractCancelAbilityService.cancelCustomerContractSign(requestDTO);
    }

    @Override
    public List<CustomerContractQueryResponseDTO> queryCustomerContract(CustomerContractQueryRequestDTO requestDTO) throws WmCustomerException {
        return wmPartnerCustomerContractQueryAbilityService.queryCustomerContract(requestDTO);
    }

    @Override
    public List<C2ContractCheckResponseDTO> hasC2Contract(C2ContractCheckRequestDTO requestDTO) throws WmCustomerException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_2", "com.sankuai.meituan.waimai.customer.contract.partner.impl.WmPartnerCustomerContractServiceImpl.hasC2Contract(C2ContractCheckRequestDTO)");
        return wmPartnerCustomerContractQueryAbilityService.hasC2Contract(requestDTO);
    }

    @Override
    public List<DaoCanContractContext> queryDcContractInfo(List<String> recordKeyList) {
        try {
            return wmEcontractSignPackService.queryDcContractInfo(recordKeyList);
        } catch (Exception e) {
            log.error("WmPartnerCustomerContractServiceImpl#queryDcContractInfo, error", e);
            return Collections.emptyList();
        }
    }

    @Override
    public PushMsgSendResponseDTO sendBatchTaskPushMessage(PushMsgSendRequestDTO requestDTO) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.contract.partner.impl.WmPartnerCustomerContractServiceImpl.sendBatchTaskPushMessage(PushMsgSendRequestDTO)");
        return wmPartnerCustomerContractSmsAbilityService.sendBatchTaskPushMessage(requestDTO);
    }

    @Override
    public ContractSignTypeResponseDTO queryContractSignType(ContractSignTypeRequestDTO requestDTO) throws WmCustomerException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.contract.partner.impl.WmPartnerCustomerContractServiceImpl.queryContractSignType(ContractSignTypeRequestDTO)");
        return wmPartnerCustomerContractQueryAbilityService.queryContractSignType(requestDTO);
    }
}
