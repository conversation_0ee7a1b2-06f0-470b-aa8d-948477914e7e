package com.sankuai.meituan.waimai.customer.service.sc.canteenstall.clueflow;

import com.alibaba.fastjson.JSONObject;
import com.sankuai.meituan.waimai.customer.adapter.WdcPoiLabelChangeThriftServiceAdapter;
import com.sankuai.meituan.waimai.customer.domain.sc.canteenstall.WmCanteenStallBindDO;
import com.sankuai.meituan.waimai.customer.domain.sc.canteenstall.WmCanteenStallClueBindBO;
import com.sankuai.meituan.waimai.thrift.exception.WmSchCantException;
import com.sankuai.meituan.wdcn.api.enums.WdcPoiLabelEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 食堂档口线索标签系统相关Service
 * <AUTHOR>
 * @date 2024/05/30
 * @email <EMAIL>
 */
@Slf4j
@Service
public class WmCanteenStallClueFlowLabelService {

    @Autowired
    private WdcPoiLabelChangeThriftServiceAdapter wdcPoiLabelChangeThriftServiceAdapter;

    /**
     * 给线索删除校园食堂标签
     * @param clueBindBO 档口绑定BO
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    public void deleteWdcClueLabel(WmCanteenStallClueBindBO clueBindBO) throws WmSchCantException {
        log.info("[WmCanteenStallClueFlowLabelService.deleteWdcClueLabel] clueBindBO = {}", JSONObject.toJSONString(clueBindBO));
        WmCanteenStallBindDO bindDO = clueBindBO.getBindDO();
        // 给线索删除校园食堂标签
        wdcPoiLabelChangeThriftServiceAdapter.deleteWdcClueLabel(bindDO.getWdcClueId(), WdcPoiLabelEnum.SCHOOL_RESTAURANT.getCode());
    }


    /**
     * 给线索打上校园食堂标签
     * @param clueBindBO 档口绑定BO
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    public void addWdcClueLabel(WmCanteenStallClueBindBO clueBindBO) throws WmSchCantException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.service.sc.canteenstall.clueflow.WmCanteenStallClueFlowLabelService.addWdcClueLabel(WmCanteenStallClueBindBO)");
        log.info("[WmCanteenStallClueFlowLabelService.addWdcClueLabel] clueBindBO = {}", JSONObject.toJSONString(clueBindBO));
        WmCanteenStallBindDO bindDO = clueBindBO.getBindDO();
        // 给线索打上校园食堂标签
        wdcPoiLabelChangeThriftServiceAdapter.addWdcClueLabel(bindDO.getWdcClueId(), WdcPoiLabelEnum.SCHOOL_RESTAURANT.getCode());
    }

}
