package com.sankuai.meituan.waimai.customer.service.sc.auth;

import com.alibaba.fastjson.JSONObject;
import com.dianping.cat.Cat;
import com.google.common.collect.Lists;
import com.sankuai.meituan.waimai.customer.adapter.AuthenticateServiceAdapter;
import com.sankuai.meituan.waimai.customer.adapter.TeamServiceAdapter;
import com.sankuai.meituan.waimai.customer.config.MccScConfig;
import com.sankuai.meituan.waimai.customer.constant.sc.WmScMetricConstant;
import com.sankuai.meituan.waimai.infra.service.WmEmployService;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.WmScCodeConstants;
import com.sankuai.meituan.waimai.thrift.customer.domain.sc.SchoolBo;
import com.sankuai.meituan.waimai.thrift.exception.WmSchCantException;
import com.sankuai.waimai.crm.authenticate.client.constant.DSLTypeEnum;
import com.sankuai.waimai.crm.authenticate.client.service.request.AuthenticateAssertBatchOperationRequest;
import com.sankuai.waimai.crm.authenticate.client.service.request.AuthenticateAssertRequest;
import com.sankuai.waimai.crm.authenticate.client.service.request.AuthenticateQueryRequest;
import com.sankuai.waimai.crm.authenticate.client.service.response.AssertResult;
import com.sankuai.waimai.crm.authenticate.client.service.response.AuthenticateAssertBatchOperationResponse;
import com.sankuai.waimai.crm.authenticate.client.service.response.AuthenticateAssertResponse;
import com.sankuai.waimai.crm.authenticate.client.service.response.AuthenticateQueryResponse;
import com.sankuai.waimai.crm.team.client.dto.AddTeamNodeUserDto;
import com.sankuai.waimai.crm.team.client.request.AddTeamNodeUserRequest;
import com.sankuai.waimai.crm.team.client.response.TeamBaseResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

import static com.sankuai.meituan.waimai.thrift.customer.constant.sc.WmScCodeConstants.BIZ_AUTH_ERROR;

/**
 * 学校权限服务相关
 * <AUTHOR>
 * @date 2023.08.09
 * @email <EMAIL>
 */
@Slf4j
@Service
public class WmScSchoolAuthService {

    @Autowired
    private TeamServiceAdapter teamServiceAdapter;

    @Autowired
    private WmEmployService.Iface wmEmployService;

    @Autowired
    private AuthenticateServiceAdapter authenticateServiceAdapter;

    /**
     * 外卖租户ID
     */
    private static final Integer WM_TENANT_ID = 1000008;
    /**
     * 学校业务对象CODE
     */
    private static final String SCHOOL_OBJECT_CODE = "waimai_school";
    /**
     * 学校列表查询操作CODE
     */
    private static final String SCHOOL_LIST_QUERY_OPERATION_CODE = "school_list_query";
    /**
     * 学校列表修改按钮操作CODE
     */
    private static final String SCHOOL_LIST_EDIT_BUTTON_OPERATION_CODE = "school_list_edit_button";
    /**
     * 学校列表操作日志按钮操作CODE
     */
    private static final String SCHOOL_LIST_LOG_BUTTON_OPERATION_CODE = "school_list_log_button";
    /**
     * 学校列表分配责任人按钮操作CODE
     */
    private static final String SCHOOL_LIST_RESPONSIBLE_BUTTON_OPERATION_CODE = "school_list_responsible_button";
    /**
     * 学校列表-交付管理按钮操作CODE
     */
    private static final String SCHOOL_LIST_DELIVERY_BUTTON_OPERATION_CODE = "school_list_delivery_button";
    /**
     * 学校责任人团队CODE
     */
    private static final String SCHOOL_RESPONSIBLE_PERSON_TEAM_CODE = "school_responsible_person";
    /**
     * 客户成功经理团队CODE
     */
    private static final String SCHOOL_CUSTOMER_SUCCESS_MANAGER_TEAM_CODE = "school_customer_success_manager";
    /**
     * 聚合渠道经理团队CODE
     */
    private static final String SCHOOL_AGGRE_CHANNLE_MANAGER_TEAM_CODE = "school_aggre_channel_manager";
    /**
     * 学校责任人岗位CODE
     */
    private static final String SCHOOL_RESPONSIBLE_PERSON_POSITION_CODE = "school_responsible_person";
    /**
     * 客户成功经理岗位CODE
     */
    private static final String SCHOOL_CUSTOMER_SUCCESS_MANAGER_POSITION_CODE = "school_customer_success_manager";
    /**
     * 聚合渠道经理岗位CODE
     */
    private static final String SCHOOL_AGGRE_CHANNLE_MANAGER_POSITION_CODE = "school_aggre_channel_manager";
    /**
     * 学校交付管理页面查看
     */
    private static final String SCHOOL_DELIVERY_PAGE_VIEW = "school_delivery_page_view";
    /**
     * 学校交付人员指定撤回审批
     */
    public static final String SCHOOL_DELIVERY_ASSIGNMENT_CANCEL_AUDIT = "school_delivery_assignment_cancel_audit";
    /**
     * 学校交付目标制定撤回审批
     */
    public static final String SCHOOL_DELIVERY_GOALSET_CANCEL_AUDIT = "school_delivery_goalset_cancel_audit";
    /**
     * 学校交付跟进撤回审批
     */
    public static final String SCHOOL_DELIVERY_FOLLOWUP_CANCEL_AUDIT = "school_delivery_followup_cancel_audit";


    /**
     * 根据用户UID获取"学校列表查询"操作的DSL查询语句
     * @param uid 用户ID
     * @return DSL查询语句
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    public String getSchoolListQueryDSL(Integer uid) throws WmSchCantException {
        log.info("[WmScSchoolAuthService.getSchoolListQueryDSL] input param: uid = {}", uid);
        if (uid == null || uid <= 0) {
            log.warn("[WmScSchoolAuthService.getSchoolListQueryDSL] uid is null. uid = {}", uid);
            return null;
        }

        AuthenticateQueryRequest queryRequest = new AuthenticateQueryRequest();
        queryRequest.setDslType(DSLTypeEnum.MY_SQL_DSL.code());
        queryRequest.setTenantId(WM_TENANT_ID);
        queryRequest.setUid(uid);
        queryRequest.setObjectCode(SCHOOL_OBJECT_CODE);
        queryRequest.setOperation(SCHOOL_LIST_QUERY_OPERATION_CODE);
        AuthenticateQueryResponse authenticateQueryResponse = authenticateServiceAdapter.getAuthQueryResult(queryRequest);
        log.info("[WmScSchoolAuthService.getSchoolListQueryDSL] authenticateQueryResponse = {}", JSONObject.toJSONString(authenticateQueryResponse));
        if (authenticateQueryResponse == null
                || authenticateQueryResponse.getCode() != 0
                || StringUtils.isBlank(authenticateQueryResponse.getDsl())) {
            log.error("[WmScSchoolAuthService.getSchoolListQueryDSL] query error. uid = {}, authenticateQueryResponse = {}",
                    uid, JSONObject.toJSONString(authenticateQueryResponse));
            Cat.logEvent(WmScMetricConstant.METRIC_AUTH_QUERY, "getSchoolListQueryDSL");
            throw new WmSchCantException(WmScCodeConstants.SERVER_ERROR, "权限获取异常, 请稍后刷新后再试");
        }
        return authenticateQueryResponse.getDsl();
    }

    /**
     * 学校列表操作鉴权查询
     * @param schoolBoList 学校对象列表
     * @param uid 用户ID
     * @return 鉴权结果列表
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    public List<AssertResult> getSchoolListOperationAssertResult(List<SchoolBo> schoolBoList, Integer uid) throws WmSchCantException {
        if (CollectionUtils.isEmpty(schoolBoList) || uid == null || uid <= 0) {
            return new ArrayList<>();
        }

        AuthenticateAssertBatchOperationRequest assertRequest = new AuthenticateAssertBatchOperationRequest();
        assertRequest.setTenantId(WM_TENANT_ID);
        assertRequest.setUid(uid);
        assertRequest.setObjectCode(SCHOOL_OBJECT_CODE);
        // 操作类型code列表
        Set<String> operationList = new HashSet<>();
        operationList.add(SCHOOL_LIST_EDIT_BUTTON_OPERATION_CODE);
        operationList.add(SCHOOL_LIST_LOG_BUTTON_OPERATION_CODE);
        operationList.add(SCHOOL_LIST_RESPONSIBLE_BUTTON_OPERATION_CODE);
        operationList.add(SCHOOL_LIST_DELIVERY_BUTTON_OPERATION_CODE);

        assertRequest.setOperations(operationList);
        // 业务对象ID列表
        Set<String> schoolPrimaryIdList = schoolBoList.stream()
                .map(x -> String.valueOf(x.getId()))
                .collect(Collectors.toSet());
        assertRequest.setObjectIds(schoolPrimaryIdList);

        AuthenticateAssertBatchOperationResponse assertResponse = authenticateServiceAdapter.getAuthAssertResultByBatch(assertRequest);
        if (assertResponse == null || assertResponse.getCode() != 0) {
            log.error("[WmScSchoolAuthService.getSchoolListOperationAssertResult] error. schoolBoList = {}, uid = {}, getSchoolListOperationAssertResult = {}",
                    JSONObject.toJSONString(schoolBoList), uid, JSONObject.toJSONString(assertResponse));
            throw new WmSchCantException(WmScCodeConstants.SERVER_ERROR, "权限获取异常, 请稍后刷新后再试");
        }
        return assertResponse.getAssertResults();
    }

    /**
     * 查询单个学校批量操作的鉴权结果
     * @param operationList 操作列表
     * @param schoolPrimaryId 学校主键ID
     * @param uid 用户ID
     * @return 鉴权结果Map: key->operationCode, value->assert result
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    public Map<String, Boolean> batchGetOperationAuthAssertResult(Set<String> operationList, Integer schoolPrimaryId, Integer uid)
            throws WmSchCantException, TException {
        log.info("[WmScSchoolAuthService.batchGetOperationAuthAssertResult] input param: operationList = {}, schoolPrimaryId = {}, uid = {}",
                JSONObject.toJSONString(operationList), schoolPrimaryId, uid);
        if (CollectionUtils.isEmpty(operationList)) {
            log.warn("[WmScSchoolAuthService.batchGetOperationAuthAssertResult] operationList is empty, return.");
            return new HashMap<>();
        }

        AuthenticateAssertBatchOperationRequest assertRequest = new AuthenticateAssertBatchOperationRequest();
        assertRequest.setTenantId(WM_TENANT_ID);
        assertRequest.setUid(uid);
        assertRequest.setObjectCode(SCHOOL_OBJECT_CODE);
        // 操作类型code列表
        assertRequest.setOperations(operationList);
        // 业务对象ID列表
        Set<String> schoolPrimaryIdList = new HashSet<>();
        schoolPrimaryIdList.add(schoolPrimaryId.toString());
        assertRequest.setObjectIds(schoolPrimaryIdList);

        AuthenticateAssertBatchOperationResponse assertResponse = authenticateServiceAdapter.getAuthAssertResultByBatch(assertRequest);
        if (assertResponse == null
                || assertResponse.getCode() != 0
                || CollectionUtils.isEmpty(assertResponse.getAssertResults())) {
            log.error("[WmScSchoolAuthService.batchGetOperationAuthAssertResult] error. assertRequest = {}, assertResponse = {}",
                    JSONObject.toJSONString(assertRequest), JSONObject.toJSONString(assertResponse));
            throw new WmSchCantException(WmScCodeConstants.SERVER_ERROR, "权限获取异常, 请稍后刷新后再试");
        }

        List<AssertResult> assertResultList = assertResponse.getAssertResults();
        return assertResultList.stream()
                .collect(Collectors.toMap(AssertResult::getOperation, AssertResult::getResult));
    }

    /**
     * 设置学校操作鉴权结果
     * @param assertResult 鉴权结果
     * @param schoolBo 学校对象
     * @return 学校对象
     */
    public void setSchoolListAssertResult(AssertResult assertResult, SchoolBo schoolBo) {
        if (assertResult.getOperation().equals(SCHOOL_LIST_EDIT_BUTTON_OPERATION_CODE)) {
            // 学校列表修改按钮权限
            schoolBo.setEditButtonAuth(assertResult.getResult());
        } else if (assertResult.getOperation().equals(SCHOOL_LIST_LOG_BUTTON_OPERATION_CODE)) {
            // 学校列表操作日志按钮权限
            schoolBo.setLogButtonAuth(assertResult.getResult());
        } else if (assertResult.getOperation().equals(SCHOOL_LIST_RESPONSIBLE_BUTTON_OPERATION_CODE)) {
            // 学校列表分配责任人按钮权限
            schoolBo.setBindRpButtonAuth(assertResult.getResult());
        } else if (assertResult.getOperation().equals(SCHOOL_LIST_DELIVERY_BUTTON_OPERATION_CODE)) {
            // 交付管理按钮权限
            schoolBo.setDeliveryButtonAuth(assertResult.getResult());
        }
    }

    /**
     * 根据用户MISID同步学校责任人至团队
     * @param misId 用户mis
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    public void syncSchoolResponsiblePersonToTeamByMisId(String misId) throws WmSchCantException {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.meituan.waimai.customer.service.sc.auth.WmScSchoolAuthService.syncSchoolResponsiblePersonToTeamByMisId(java.lang.String)");
        log.info("[WmScSchoolAuthService.syncSchoolResponsiblePersonToTeamByMisId] input param: misId = {}", misId);
        if (StringUtils.isBlank(misId)) {
            log.warn("[WmScSchoolAuthService.syncSchoolResponsiblePersonToTeamByMisId] misId is blank.");
            return;
        }

        try {
            int uid = wmEmployService.getUidByMisId(misId);
            syncSchoolResponsiblePersonToTeamByUid(uid);
        } catch (Exception e) {
            throw new WmSchCantException(WmScCodeConstants.SERVER_ERROR, "同步学校责任人至团队失败");
        }
    }

    /**
     * 根据用户UID同步聚合渠道经理至团队
     * @param uid 用户ID
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    public void syncAggreChannelManagerToTeamByUid(Integer uid) throws WmSchCantException {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.meituan.waimai.customer.service.sc.auth.WmScSchoolAuthService.syncAggreChannelManagerToTeamByUid(java.lang.Integer)");
        log.info("[WmScSchoolAuthService.syncAggreChannelManagerToTeamByUid] input param: uid = {}", uid);
        if (uid == null || uid <= 0) {
            log.warn("[WmScSchoolAuthService.syncAggreChannelManagerToTeamByUid] uid is null.");
            return;
        }

        // 用户和岗位信息信息列表
        AddTeamNodeUserDto addTeamNodeUserDto = new AddTeamNodeUserDto();
        addTeamNodeUserDto.setUid(uid);
        addTeamNodeUserDto.setTeamPositionCodes(Collections.singletonList(SCHOOL_AGGRE_CHANNLE_MANAGER_POSITION_CODE));
        // 请求入参
        AddTeamNodeUserRequest addTeamNodeUserRequest = new AddTeamNodeUserRequest();
        addTeamNodeUserRequest.setTenantId(WM_TENANT_ID);
        addTeamNodeUserRequest.setTeamCode(SCHOOL_AGGRE_CHANNLE_MANAGER_TEAM_CODE);
        addTeamNodeUserRequest.setTeamNodeId(MccScConfig.getAggreChannelManagerTeamNodeId());
        addTeamNodeUserRequest.setAddTeamNodeUserDtoList(Collections.singletonList(addTeamNodeUserDto));

        TeamBaseResponse teamBaseResponse = teamServiceAdapter.addTeamNodeUser(addTeamNodeUserRequest);
        log.info("[WmScSchoolAuthService.syncAggreChannelManagerToTeamByUid] teamBaseResponse = {}", JSONObject.toJSONString(teamBaseResponse));
        if (teamBaseResponse == null || teamBaseResponse.getResponseStatus() == null) {
            log.error("[WmScSchoolAuthService.syncAggreChannelManagerToTeamByUid] error. addTeamNodeUserRequest = {}",
                    JSONObject.toJSONString(addTeamNodeUserRequest));
            Cat.logEvent(WmScMetricConstant.METIRC_TEAM_ADD_USER, "syncAggreChannelManagerToTeamByUid");
        }
    }


    /**
     * 根据用户UID同步客户成功经理至团队
     * @param uid 用户ID
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    public void syncCustomerSuccessManagerToTeamByUid(Integer uid) throws WmSchCantException {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.meituan.waimai.customer.service.sc.auth.WmScSchoolAuthService.syncCustomerSuccessManagerToTeamByUid(java.lang.Integer)");
        log.info("[WmScSchoolAuthService.syncCustomerSuccessManagerToTeamByUid] input param: uid = {}", uid);
        if (uid == null || uid <= 0) {
            log.warn("[WmScSchoolAuthService.syncCustomerSuccessManagerToTeamByUid] uid is null.");
            return;
        }

        // 用户和岗位信息信息列表
        AddTeamNodeUserDto addTeamNodeUserDto = new AddTeamNodeUserDto();
        addTeamNodeUserDto.setUid(uid);
        addTeamNodeUserDto.setTeamPositionCodes(Collections.singletonList(SCHOOL_CUSTOMER_SUCCESS_MANAGER_POSITION_CODE));
        // 请求入参
        AddTeamNodeUserRequest addTeamNodeUserRequest = new AddTeamNodeUserRequest();
        addTeamNodeUserRequest.setTenantId(WM_TENANT_ID);
        addTeamNodeUserRequest.setTeamCode(SCHOOL_CUSTOMER_SUCCESS_MANAGER_TEAM_CODE);
        addTeamNodeUserRequest.setTeamNodeId(MccScConfig.getCustomerSuccessManagerTeamNodeId());
        addTeamNodeUserRequest.setAddTeamNodeUserDtoList(Collections.singletonList(addTeamNodeUserDto));

        TeamBaseResponse teamBaseResponse = teamServiceAdapter.addTeamNodeUser(addTeamNodeUserRequest);
        log.info("[WmScSchoolAuthService.syncCustomerSuccessManagerToTeamByUid] teamBaseResponse = {}", JSONObject.toJSONString(teamBaseResponse));
        if (teamBaseResponse == null || teamBaseResponse.getResponseStatus() == null) {
            log.error("[WmScSchoolAuthService.syncCustomerSuccessManagerToTeamByUid] error. addTeamNodeUserRequest = {}",
                    JSONObject.toJSONString(addTeamNodeUserRequest));
            Cat.logEvent(WmScMetricConstant.METIRC_TEAM_ADD_USER, "syncCustomerSuccessManagerToTeamByUid");
        }
    }

    /**
     * 根据用户UID同步学校责任人至团队
     * @param uid 用户ID
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    public void syncSchoolResponsiblePersonToTeamByUid(Integer uid) throws WmSchCantException {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.service.sc.auth.WmScSchoolAuthService.syncSchoolResponsiblePersonToTeamByUid(java.lang.Integer)");
        log.info("[WmScSchoolAuthService.syncSchoolResponsiblePersonToTeamByUid] input param: uid = {}", uid);
        if (uid == null || uid <= 0) {
            log.warn("[WmScSchoolAuthService.syncSchoolResponsiblePersonToTeamByUid] uid is null.");
            return;
        }
        try {
            AddTeamNodeUserRequest addTeamNodeUserRequest = new AddTeamNodeUserRequest();
            addTeamNodeUserRequest.setTenantId(WM_TENANT_ID);
            addTeamNodeUserRequest.setTeamCode(SCHOOL_RESPONSIBLE_PERSON_TEAM_CODE);
            addTeamNodeUserRequest.setTeamNodeId(MccScConfig.getSchoolResponsiblePersonTeamNodeId());
            // 用户信息列表
            List<AddTeamNodeUserDto> addTeamNodeUserDtoList = new ArrayList<>();
            AddTeamNodeUserDto addTeamNodeUserDto = new AddTeamNodeUserDto();
            addTeamNodeUserDto.setUid(uid);
            // 岗位信息
            List<String> positionCodes = new ArrayList<>();
            positionCodes.add(SCHOOL_RESPONSIBLE_PERSON_POSITION_CODE);
            addTeamNodeUserDto.setTeamPositionCodes(positionCodes);
            addTeamNodeUserDtoList.add(addTeamNodeUserDto);
            addTeamNodeUserRequest.setAddTeamNodeUserDtoList(addTeamNodeUserDtoList);
            TeamBaseResponse teamBaseResponse = teamServiceAdapter.addTeamNodeUser(addTeamNodeUserRequest);
            log.info("[WmScSchoolAuthService.syncSchoolResponsiblePersonToTeamByUid] teamBaseResponse = {}", JSONObject.toJSONString(teamBaseResponse));
            if (teamBaseResponse == null || teamBaseResponse.getResponseStatus() == null) {
                log.error("[WmScSchoolAuthService.syncSchoolResponsiblePersonToTeamByUid] error. addTeamNodeUserRequest = {}",
                        JSONObject.toJSONString(addTeamNodeUserRequest));
                Cat.logEvent(WmScMetricConstant.METIRC_TEAM_ADD_USER, "syncSchoolResponsiblePersonToTeamByUid");
            }
        } catch (Exception e) {
            log.error("[WmScSchoolAuthService.syncSchoolResponsiblePersonToTeamByUid] Exception. uid = {}", uid, e);
            throw new WmSchCantException(WmScCodeConstants.SERVER_ERROR, "同步学校责任人至团队失败");
        }
    }

    /**
     * 获取用户对某一操作的鉴权结果
     * @param uid 用户ID
     * @param operationCode 操作code
     * @param schoolPrimaryId 学校主建ID
     * @return true: 有权限 / false: 无权限
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     * @throws TException org.apache.thrift.TException
     */
    public Boolean getSchoolAuthAssertResult(Integer uid, String operationCode, Integer schoolPrimaryId) throws WmSchCantException, TException {
        log.info("[WmScSchoolAuthService.getAuthAssertResult] input param: uid = {}, operationCode = {}, schoolPrimaryId = {}",
                uid, operationCode, schoolPrimaryId);
        if (uid == null || uid <= 0 || StringUtils.isBlank(operationCode) || schoolPrimaryId == null || schoolPrimaryId <= 0) {
            log.error("[WmScSchoolAuthService.getAuthAssertResult] input param invalid. uid = {}, operationCode = {}, schoolPrimaryId = {}",
                    uid, operationCode, schoolPrimaryId);
            return false;
        }
        AuthenticateAssertRequest assertRequest = new AuthenticateAssertRequest();
        assertRequest.setTenantId(WM_TENANT_ID);
        assertRequest.setUid(uid);
        assertRequest.setObjectCode(SCHOOL_OBJECT_CODE);
        // 操作类型code
        assertRequest.setOperation(operationCode);
        // 业务对象ID列表
        List<String> schoolPrimaryIdList = new ArrayList<>();
        schoolPrimaryIdList.add(String.valueOf(schoolPrimaryId));
        assertRequest.setObjectIds(schoolPrimaryIdList);
        AuthenticateAssertResponse assertResponse = authenticateServiceAdapter.getAuthAssertResult(assertRequest);
        log.info("[WmScAuthService.getAuthAssertResult] assertResponse = {}", JSONObject.toJSONString(assertResponse));
        if (assertResponse == null) {
            log.error("[WmScAuthService.getAuthAssertResult] auth assert failed. schoolPriaryId = {}, uid = {}", schoolPrimaryId, uid);
            throw new WmSchCantException(BIZ_AUTH_ERROR, "权限获取异常, 请稍后刷新后再试");
        }
        return assertResponse.getResult(String.valueOf(schoolPrimaryId));
    }

    /**
     * 获取用户对学校列表某一操作的鉴权结果
     * @param uid 用户ID
     * @param operationCode 操作code
     * @param schoolPrimaryIdList 学校主键ID列表
     * @return Map<String, Boolean> key->学校主键ID、val->true: 有权限 / false: 无权限
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     * @throws TException org.apache.thrift.TException
     */
    public Map<String, Boolean> batchGetSchoolAuthAssertResult(Integer uid, String operationCode, List<Integer> schoolPrimaryIdList) throws WmSchCantException, TException {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.service.sc.auth.WmScSchoolAuthService.batchGetSchoolAuthAssertResult(java.lang.Integer,java.lang.String,java.util.List)");
        log.info("[WmScSchoolAuthService.batchGetSchoolAuthAssertResult] input param: uid = {}, operationCode = {}, schoolPrimaryIdList = {}",
                uid, operationCode, JSONObject.toJSONString(schoolPrimaryIdList));
        if (uid == null || uid <= 0 || StringUtils.isBlank(operationCode) || CollectionUtils.isEmpty(schoolPrimaryIdList)) {
            log.error("[WmScSchoolAuthService.batchGetSchoolAuthAssertResult] input param invalid. uid = {}, operationCode = {}, schoolPrimaryIdList = {}",
                    uid, operationCode, JSONObject.toJSONString(schoolPrimaryIdList));
            return null;
        }
        AuthenticateAssertRequest assertRequest = new AuthenticateAssertRequest();
        assertRequest.setTenantId(WM_TENANT_ID);
        assertRequest.setUid(uid);
        assertRequest.setObjectCode(SCHOOL_OBJECT_CODE);
        // 操作类型code
        assertRequest.setOperation(operationCode);
        // 业务对象ID列表
        List<String> schoolPrimaryIdListStr = schoolPrimaryIdList.stream()
                .map(Object::toString)
                .collect(Collectors.toList());
        // 将List进行拆分
        List<List<String>> schoolPrimaryIdListPartition = Lists.partition(schoolPrimaryIdListStr, MccScConfig.getMaxSchoolPrimaryIdListSize());
        Map<String, Boolean> resMap = new HashMap<>();
        for (List<String> objectIds : schoolPrimaryIdListPartition) {
            assertRequest.setObjectIds(objectIds);
            AuthenticateAssertResponse assertResponse = authenticateServiceAdapter.getAuthAssertResult(assertRequest);
            log.info("[WmScAuthService.batchGetSchoolAuthAssertResult] assertResponse = {}", JSONObject.toJSONString(assertResponse));
            if (assertResponse == null) {
                log.error("[WmScAuthService.batchGetSchoolAuthAssertResult] auth assert failed. schoolPrimaryIdList = {}, uid = {}", JSONObject.toJSONString(schoolPrimaryIdList), uid);
                throw new WmSchCantException(BIZ_AUTH_ERROR, "权限获取异常, 请稍后刷新后再试");
            }
            resMap.putAll(assertResponse.getAssertResults());
        }
        return resMap;
    }

    /**
     * 校验学校交付页面访问权限
     * @param schoolPrimaryId 学校主键ID
     * @param uid 用户UID
     * @throws TException org.apache.thrift.TException
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    public void checkSchoolDeliveryPageViewAuth(Integer schoolPrimaryId, Integer uid) throws TException, WmSchCantException {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.sc.auth.WmScSchoolAuthService.checkSchoolDeliveryPageViewAuth(java.lang.Integer,java.lang.Integer)");
        log.info("[WmScSchoolAuthService.checkSchoolDeliveryFollowUpPageViewAuth] input param: schoolPrimaryId = {}, uid = {}",
                schoolPrimaryId, uid);
        Boolean authResult = getSchoolAuthAssertResult(uid, SCHOOL_DELIVERY_PAGE_VIEW, schoolPrimaryId);
        if (!authResult) {
            throw new WmSchCantException(BIZ_AUTH_ERROR, "抱歉，您暂无权限");
        }
    }

    /**
     * 交付人员指定-撤回审批鉴权
     * @param schoolPrimaryId 学校主键ID
     * @param uid 用户UID
     * @throws TException org.apache.thrift.TException
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    public void checkDeliveryAssignmentCancelAuditTaskAuth(Integer schoolPrimaryId, Integer uid) throws TException, WmSchCantException {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.meituan.waimai.customer.service.sc.auth.WmScSchoolAuthService.checkDeliveryAssignmentCancelAuditTaskAuth(java.lang.Integer,java.lang.Integer)");
        log.info("[WmScSchoolAuthService.checkDeliveryAssignmentCancelAuditTaskAuth] schoolPrimaryId = {}, uid = {}", schoolPrimaryId, uid);
        Boolean authResult = getSchoolAuthAssertResult(uid, SCHOOL_DELIVERY_ASSIGNMENT_CANCEL_AUDIT, schoolPrimaryId);
        if (!authResult) {
            throw new WmSchCantException(BIZ_AUTH_ERROR, "抱歉，您暂无权限");
        }
    }

    /**
     * 交付目标制定-撤回审批鉴权
     * @param schoolPrimaryId 学校主键ID
     * @param uid 用户UID
     * @throws TException org.apache.thrift.TException
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    public void checkDeliveryGoalSetCancelAuditTaskAuth(Integer schoolPrimaryId, Integer uid) throws TException, WmSchCantException {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.service.sc.auth.WmScSchoolAuthService.checkDeliveryGoalSetCancelAuditTaskAuth(java.lang.Integer,java.lang.Integer)");
        log.info("[WmScSchoolAuthService.checkDeliveryGoalSetCancelAuditTaskAuth] schoolPrimaryId = {}, uid = {}", schoolPrimaryId, uid);
        Boolean authResult = getSchoolAuthAssertResult(uid, SCHOOL_DELIVERY_GOALSET_CANCEL_AUDIT, schoolPrimaryId);
        if (!authResult) {
            throw new WmSchCantException(BIZ_AUTH_ERROR, "抱歉，您暂无权限");
        }
    }

    /**
     * 交付跟进-撤回审批鉴权
     * @param schoolPrimaryId 学校主键ID
     * @param uid 用户UID
     * @throws TException org.apache.thrift.TException
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    public void checkDeliveryFollowUpCancelAuditTaskAuth(Integer schoolPrimaryId, Integer uid) throws TException, WmSchCantException {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.meituan.waimai.customer.service.sc.auth.WmScSchoolAuthService.checkDeliveryFollowUpCancelAuditTaskAuth(java.lang.Integer,java.lang.Integer)");
        log.info("[WmScSchoolAuthService.checkDeliveryFollowUpCancelAuditTaskAuth] schoolPrimaryId = {}, uid = {}", schoolPrimaryId, uid);
        Boolean authResult = getSchoolAuthAssertResult(uid, SCHOOL_DELIVERY_FOLLOWUP_CANCEL_AUDIT, schoolPrimaryId);
        if (!authResult) {
            throw new WmSchCantException(BIZ_AUTH_ERROR, "抱歉，您暂无权限");
        }
    }

}
