package com.sankuai.meituan.waimai.customer.service.customer.monitor;

import com.dianping.frog.sdk.alarm.DXUtil;
import com.dianping.frog.sdk.data.BinlogRawData;
import com.dianping.frog.sdk.data.ColumnInfo;
import com.dianping.frog.sdk.data.RawData;
import com.dianping.frog.sdk.rpc.RpcService;
import com.dianping.frog.sdk.rpc.RpcServiceFactory;
import com.dianping.frog.sdk.runner.DefaultRuleRunner;
import com.dianping.lion.client.util.JsonUtils;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.lang.StringUtils;

import java.util.Map;

/**
 * 客户门店属性与Agree一致性监控
 *
 * <AUTHOR>
 * @date 2022年02月09日 7:42 PM
 */
public class WmCustomerPoiAttributeAgreeRuleRunner extends DefaultRuleRunner {

	@Override
	public String check(RawData triggerData, RawData... targetData) throws Exception {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.service.customer.monitor.WmCustomerPoiAttributeAgreeRuleRunner.check(com.dianping.frog.sdk.data.RawData,com.dianping.frog.sdk.data.RawData[])");
        BinlogRawData binlogRawData = (BinlogRawData) triggerData;
		String tableName = binlogRawData.getRealTableName();
		Map<String, ColumnInfo> columnInfoMap = binlogRawData.getColumnInfoMap();

		RpcService rpcService = RpcServiceFactory.getThriftInstance("com.sankuai.meituan.waimai.thrift.customer.service.WmCustomerPoiThriftService",
				"com.sankuai.waimai.e.customer", 10000, null, "8435");
		Map<String, Object> params = Maps.newHashMap();

		if ("wm_customer_poi_attribute".equals(tableName) && columnInfoMap.containsKey("wm_poi_id")) {
			params.put("wmPoiId", columnInfoMap.get("wm_poi_id").getNewValue());
		}
		if ("wm_customer_poi_rel".equals(tableName) && columnInfoMap.containsKey("wm_poi_id")) {
			params.put("wmPoiId", columnInfoMap.get("wm_poi_id").getNewValue());

		}

		String result = rpcService.invoke("monitorCustomerPoiAttributeAgree",
				Lists.newArrayList("com.sankuai.meituan.waimai.thrift.customer.domain.customer.monitor.MonitorCustomerPoiAttributeAgreeDTO"),
				Lists.newArrayList(JsonUtils.toJson(params)));
		if (!StringUtils.isBlank(result) && !result.equals("\"\"")) {
			return result;
		}
		return null;
	}

	/**
	 * 默认情况下alarm方法会自动发送大象告警(不要覆盖该方法)，如需通过mafka或者泛化调用等方式请复写alarm方法
	 *
	 * @param checkResult 是check方法的返回结果，传入alarm方便进行告警
	 * @param triggerData 触发数据：触发数据源的数据
	 * @param targetData  目标数据：根据触发数据源的设置的key对应的其他源数据源的数据；单流校验为空，多流校验则为所有数据源数据除去触发数据
	 */
	@Override
	public void alarm(String checkResult, RawData triggerData, RawData... targetData) throws Exception {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.customer.monitor.WmCustomerPoiAttributeAgreeRuleRunner.alarm(java.lang.String,com.dianping.frog.sdk.data.RawData,com.dianping.frog.sdk.data.RawData[])");
        DXUtil.sendAlarm(checkResult);
	}
}
