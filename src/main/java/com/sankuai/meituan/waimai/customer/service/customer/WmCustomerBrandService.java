package com.sankuai.meituan.waimai.customer.service.customer;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.google.common.primitives.Ints;
import com.sankuai.meituan.waimai.customer.adapter.WmPoiClient;
import com.sankuai.meituan.waimai.customer.bo.WmPoiDomain;
import com.sankuai.meituan.waimai.customer.config.MccConfig;
import com.sankuai.meituan.waimai.customer.dao.WmCustomerBrandDBMapper;
import com.sankuai.meituan.waimai.customer.ddd.domain.customer.aggre.WmCustomerPoiAggre;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerBrandCondition;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerBrandDB;
import com.sankuai.meituan.waimai.customer.service.WmCustomerBrandThriftServiceImpl;
import com.sankuai.meituan.waimai.customer.service.version.VersionCheckUtil;
import com.sankuai.meituan.waimai.customer.util.PageUtil;
import com.sankuai.meituan.waimai.poibrand.domain.WmPoiOriginBrand;
import com.sankuai.meituan.waimai.poibrand.domain.WmPoiServiceBrand;
import com.sankuai.meituan.waimai.poibrand.service.WmPoiBrandQueryThriftService;
import com.sankuai.meituan.waimai.poibrand.vo.QueryCountResponseVo;
import com.sankuai.meituan.waimai.poisearch.thrift.domain.WmPoiSearchParam;
import com.sankuai.meituan.waimai.poisearch.thrift.domain.WmPoiSearchResult;
import com.sankuai.meituan.waimai.poisearch.thrift.execption.WmPoiSearchException;
import com.sankuai.meituan.waimai.poisearch.thrift.service.WmPoiSearchThriftService;
import com.sankuai.meituan.waimai.thrift.constant.WmContractErrorCodeConstant;
import com.sankuai.meituan.waimai.thrift.constants.WmPoiFieldQueryConstant;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerConstants;
import com.sankuai.meituan.waimai.thrift.customer.domain.PageData;
import com.sankuai.meituan.waimai.thrift.customer.domain.WmCustomerOplogBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.base.IntResult;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.WmCustomerBrandBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.WmCustomerBrandListBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.WmCustomerBrandListPageData;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import com.sankuai.meituan.waimai.thrift.exception.WmServerException;
import org.apache.commons.collections.MultiMap;
import org.apache.commons.lang3.StringUtils;
import org.apache.thrift.TException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Service
public class WmCustomerBrandService {

    private static final Logger logger = LoggerFactory.getLogger(WmCustomerBrandService.class);

    @Autowired
    private WmCustomerBrandDBMapper wmCustomerBrandDBMapper;

    @Autowired
    private WmCustomerService wmCustomerService;

    @Autowired
    private WmPoiBrandQueryThriftService.Iface wmPoiBrandQueryThriftService;

    @Autowired
    private WmPoiSearchThriftService.Iface wmPoiSearchThriftService;

    @Autowired
    private WmCustomerPoiService wmCustomerPoiService;

    @Autowired
    private WmPoiClient wmPoiClient;

    private static final int BATCH_QUERY_NUM = 100;

    private static final String QUERY_COUNT_SQL = "select count(wmPoiId) from poi.poi_index_rw where customerId = %d and brandId = %d and isDelete = 0";

    /**
     * 批量关联客户品牌
     * @param customerId
     * @param brandIds
     * @param opUid
     * @param opName
     * @throws WmCustomerException
     */
    @Transactional(rollbackFor = Exception.class)
    public void batchSaveCustomerBrandRel(int customerId, List<Integer> brandIds, int opUid, String opName) throws WmCustomerException, TException {
        logger.info("批量关联客户品牌 customerId = {}, brandIds = {}, opUid = {}, opName = {}", customerId, JSON.toJSONString(brandIds), opUid, opName);
        VersionCheckUtil.versionCheck(customerId, 0);
        validateParam(customerId, brandIds);

        List<Integer> brandIdList = filterNotExistBrand(brandIds);
        logger.info("批量关联客户品牌 brandIdList", JSON.toJSONString(brandIdList));
        if (CollectionUtils.isEmpty(brandIdList)) {
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "客户关联品牌失败, 品牌ID不存在");
        }

        List<Integer> existBrandIdList = selectBrandIdListByCustomerId(customerId);
        brandIdList.removeAll(existBrandIdList);
        if (CollectionUtils.isEmpty(brandIdList)) {
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "客户关联品牌失败, 品牌已存在");
        }

        List<WmCustomerBrandDB> wmCustomerBrandDBList = Lists.newArrayList();
        for (Integer brandId : brandIdList) {
            WmCustomerBrandDB wmCustomerBrandDB = new WmCustomerBrandDB();
            wmCustomerBrandDB.setBrandId(brandId);
            wmCustomerBrandDB.setCustomerId(customerId);
            wmCustomerBrandDB.setValid(CustomerConstants.VALID);
            wmCustomerBrandDBList.add(wmCustomerBrandDB);
        }
        wmCustomerBrandDBMapper.batchInsert(wmCustomerBrandDBList);
        wmCustomerService.insertCustomerOpLog(customerId, opUid, opName, WmCustomerOplogBo.OpType.INSERT, String.format(CustomerConstants.CUSTOMER_LOG_TEMPLATE_BRAND_INSERT, StringUtils.join(brandIdList, ",")));
    }

    private List<Integer> filterNotExistBrand(List<Integer> allBrandIdList) throws WmCustomerException {
        try {
            List<WmPoiServiceBrand> wmPoiServiceBrandList = wmPoiBrandQueryThriftService.mgetWmPoiServiceBrand(allBrandIdList);
            if (!CollectionUtils.isEmpty(wmPoiServiceBrandList)) {
                List<Integer> existBrandIdList = wmPoiServiceBrandList.stream().map(WmPoiServiceBrand::getId).distinct().collect(Collectors.toList());
                return existBrandIdList;
            }
        } catch (TException | WmServerException e) {
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "批量关联客户品牌异常");
        }
        return Lists.newArrayList();
    }


    /**
     * 批量解绑客户品牌关系
     * @param customerId
     * @param brandIds
     * @param opUid
     * @param opName
     */
    @Transactional(rollbackFor = Exception.class)
    public void deleteCustomerBrandRel(int customerId, List<Integer> brandIds, int opUid, String opName) throws WmCustomerException, TException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.service.customer.WmCustomerBrandService.deleteCustomerBrandRel(int,java.util.List,int,java.lang.String)");
        logger.info("批量解绑客户品牌关系 customerId = {}, brandId = {}, opUid = {}, opName = {}", customerId, brandIds, opUid, opName);
        VersionCheckUtil.versionCheck(customerId, 0);
        validateParam(customerId, brandIds);
        int deleteCount = wmCustomerBrandDBMapper.batchDelete(customerId, brandIds);
        if (deleteCount > 0) {
            wmCustomerService.insertCustomerOpLog(customerId, opUid, opName, WmCustomerOplogBo.OpType.DELETE,
                    String.format(CustomerConstants.CUSTOMER_LOG_TEMPLATE_BRAND_DELETE, StringUtils.join(brandIds, ",")));
        }
    }

    /**
     * 查询客户品牌关系列表
     * @param customerId
     * @return
     */
    public List<Integer> selectBrandIdListByCustomerId(Integer customerId) throws WmCustomerException{
        if(customerId==null||customerId==0){
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR,"参数缺失");
        }
        return wmCustomerBrandDBMapper.selectBrandIdListByCustomerId(customerId);
    }


    /**
     * 校验入参
     * @param customerId
     * @param brandIds
     * @throws WmCustomerException
     */
    private void validateParam(int customerId, List<Integer> brandIds) throws WmCustomerException {
        if (customerId == 0 || CollectionUtils.isEmpty(brandIds)) {
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "参数缺失");
        }
        if (brandIds.size() > MccConfig.getCustomerBrandBatchNum()) {
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "一次性操作不能超过" + MccConfig.getCustomerBrandBatchNum());
        }
    }

    public WmCustomerBrandListPageData getCustomerBrandList(Integer customerId, String content, Integer pageNo, Integer pageSize) throws TException, WmCustomerException {
        logger.info("getCustomerBrandList customerId = {}, content = {}, pageNo = {}, pageSize = {}", customerId, content, pageNo, pageSize);
        if (StringUtils.isNotBlank(content)) {
            return getCustomerBrandListByCusIdAndContent(customerId, content, pageNo, pageSize);
        }

        PageHelper.startPage(pageNo, pageSize);
        List<Integer> customerBrandIdList = wmCustomerBrandDBMapper.selectBrandIdListByCustomerId(customerId);
        List<WmCustomerBrandListBo>  wmCustomerBrandListBoList = getWmCustomerBrandListBoList(customerId, customerBrandIdList);
        wmCustomerBrandListBoList.stream().forEach(wmCustomerBrandListBo -> wmCustomerBrandListBo.setCustomerId(customerId));

        PageData<WmCustomerBrandListBo> pageData = PageUtil.page(customerBrandIdList, wmCustomerBrandListBoList);
        return new WmCustomerBrandListPageData(pageData.getPageInfo(), pageData.getList());
    }

    private WmCustomerBrandListPageData getCustomerBrandListByCusIdAndContent(Integer customerId, String content, Integer pageNo, Integer pageSize) throws WmCustomerException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.service.customer.WmCustomerBrandService.getCustomerBrandListByCusIdAndContent(java.lang.Integer,java.lang.String,java.lang.Integer,java.lang.Integer)");
        Integer brandId = Ints.tryParse(content);
        if (null != brandId) {
            PageHelper.startPage(pageNo, pageSize);
            List<Integer> brandIdList = wmCustomerBrandDBMapper.selectListByCustomerIdAndBrandIdList(customerId, Lists.newArrayList(brandId));
            List<WmCustomerBrandListBo> wmCustomerBrandListBoList = getWmCustomerBrandListBoList(customerId, brandIdList);
            wmCustomerBrandListBoList.stream().forEach(wmCustomerBrandListBo -> wmCustomerBrandListBo.setCustomerId(customerId));
            PageData<WmCustomerBrandListBo> pageData = PageUtil.page(brandIdList, wmCustomerBrandListBoList);
            return new WmCustomerBrandListPageData(pageData.getPageInfo(), pageData.getList());
        }

        List<Integer> customerBrandIdList = wmCustomerBrandDBMapper.selectBrandIdListByCustomerId(customerId);
        List<List<Integer>> customerBrandIdLists = Lists.partition(customerBrandIdList, BATCH_QUERY_NUM);
        List<WmCustomerBrandListBo> wmCustomerBrandListBoList = Lists.newArrayList();
        for (List<Integer> list : customerBrandIdLists) {
            wmCustomerBrandListBoList.addAll(getWmCustomerBrandListBoList(customerId, list));
        }

        Page<WmCustomerBrandListBo> pageResult = new Page<>(pageNo, pageSize, true);
        wmCustomerBrandListBoList.stream().forEach(wmCustomerBrandListBo -> {
            if (wmCustomerBrandListBo.getBrandName().contains(content)) {
                pageResult.add(wmCustomerBrandListBo);
            }
        });
        pageResult.setTotal(pageResult.size());

        List<WmCustomerBrandListBo> viewList = pageResult.stream().skip((pageNo - 1) * pageSize).limit(pageSize).collect(Collectors.toList());
        viewList.stream().forEach(wmCustomerBrandListBo -> wmCustomerBrandListBo.setCustomerId(customerId));
        PageData<WmCustomerBrandListBo> pageData = PageUtil.page(pageResult, viewList);
        return new WmCustomerBrandListPageData(pageData.getPageInfo(), pageData.getList());
    }

    private List<WmCustomerBrandListBo> getWmCustomerBrandListBoList(Integer customerId, List<Integer> customerBrandIdList) throws WmCustomerException {
        try {
            if (CollectionUtils.isEmpty(customerBrandIdList)) {
                return Lists.newArrayList();
            }

            List<WmPoiServiceBrand> wmPoiServiceBrandList = wmPoiBrandQueryThriftService.mgetWmPoiServiceBrand(customerBrandIdList);
            if (CollectionUtils.isEmpty(wmPoiServiceBrandList)) {
                return Lists.newArrayList();
            }
            List<Integer> originBrandIdList = wmPoiServiceBrandList.stream().map(WmPoiServiceBrand::getOrigin_brand_id).distinct().collect(Collectors.toList());
            if (CollectionUtils.isEmpty(originBrandIdList)) {
                return Lists.newArrayList();
            }
            List<WmPoiOriginBrand> wmPoiOriginBrandList = wmPoiBrandQueryThriftService.getOriginBrandByBrandList(originBrandIdList);
            if (CollectionUtils.isEmpty(wmPoiOriginBrandList)) {
                return Lists.newArrayList();
            }
            Map<Integer, Integer> originBrandTypeMap = wmPoiOriginBrandList.stream().collect(Collectors.toMap(WmPoiOriginBrand::getOrigin_brand_id, WmPoiOriginBrand::getBrand_type));
            if (CollectionUtils.isEmpty(originBrandTypeMap)) {
                return Lists.newArrayList();
            }
            List<WmCustomerBrandListBo> wmCustomerBrandListBoList = Lists.newArrayList();
            for (WmPoiServiceBrand wmPoiServiceBrand : wmPoiServiceBrandList) {
                WmCustomerBrandListBo wmCustomerBrandListBo = new WmCustomerBrandListBo();
                wmCustomerBrandListBo.setBrandId(wmPoiServiceBrand.getId());
                wmCustomerBrandListBo.setBrandName(wmPoiServiceBrand.getBranch_name());
                wmCustomerBrandListBo.setBrandLogo(wmPoiServiceBrand.getService_brand_logo_url43());
                // 该值先不查询, 0是有意义的数字, 所以-1让前端展示"-"
                wmCustomerBrandListBo.setPhysicalBrandPoiNum(-1);

                Integer brandType = originBrandTypeMap.get(wmPoiServiceBrand.getOrigin_brand_id());
                if (brandType != null) {
                    wmCustomerBrandListBo.setBrandType(brandType);
                }

                WmPoiSearchParam wmPoiSearchParam = new WmPoiSearchParam();
                wmPoiSearchParam.setSql(String.format(QUERY_COUNT_SQL, customerId, wmPoiServiceBrand.getId()));
                WmPoiSearchResult searchResult = wmPoiSearchThriftService.search(wmPoiSearchParam);
                wmCustomerBrandListBo.setCusBrandPoiNum((int) searchResult.getTotal());

                QueryCountResponseVo queryCountResponseVo = wmPoiBrandQueryThriftService.getBrandRelCountByServiceBrandId(wmPoiServiceBrand.getId(), 0, "客户品牌列表页查询");
                wmCustomerBrandListBo.setWmBrandPoiNum(queryCountResponseVo.getQueryCount());

                wmCustomerBrandListBoList.add(wmCustomerBrandListBo);
            }
            return wmCustomerBrandListBoList;
        } catch (WmServerException e) {
            logger.error("查询客户品牌信息列表异常 code = {}, msg = {}", e.getCode(), e.getMsg(), e);
            throw new WmCustomerException(e.getCode(), e.getMsg());
        } catch (WmPoiSearchException e) {
            logger.error("查询客户品牌信息列表异常 code = {}, msg = {}", e.getCode(), e.getMsg(), e);
            throw new WmCustomerException(e.getCode(), e.getMsg());
        } catch (TException e) {
            logger.error("查询客户品牌信息列表异常", e);
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "查询客户品牌信息列表异常");
        }
    }

    public List<WmCustomerBrandBo> getCusPoiBrandByCusId(Integer customerId) {
        try {
            List<Long> wmPoiIdList = WmCustomerPoiAggre.Factory.make().selectWmPoiIdsByCustomerId(customerId);
            if (CollectionUtils.isEmpty(wmPoiIdList)) {
                return Lists.newArrayList();
            }

            List<WmPoiDomain> wmPoiDomainList = wmPoiClient.pageGetWmPoiByWmPoiIdList(wmPoiIdList, Sets.newHashSet(WmPoiFieldQueryConstant.WM_POI_FIELD_BRAND_ID));
            List<Integer> wmBrandIdList = wmPoiDomainList.stream().map(WmPoiDomain::getBrandId).distinct().map(Long::intValue).collect(Collectors.toList());
            List<WmPoiServiceBrand> wmPoiServiceBrandList = wmPoiBrandQueryThriftService.mgetWmPoiServiceBrand(wmBrandIdList);
            List<WmCustomerBrandBo> wmCustomerBrandBoList = Lists.newArrayList();
            wmPoiServiceBrandList.stream().forEach(wmPoiServiceBrand -> {
                WmCustomerBrandBo wmCustomerBrandBo = new WmCustomerBrandBo();
                wmCustomerBrandBo.setBrandId(wmPoiServiceBrand.getId());
                wmCustomerBrandBo.setBrandName(wmPoiServiceBrand.getBranch_name());
                wmCustomerBrandBoList.add(wmCustomerBrandBo);
            });
            return wmCustomerBrandBoList;
        } catch (WmCustomerException e) {
            logger.error("查询客户下门店品牌异常 code = {}, msg = {}", e.getCode(), e.getMsg(), e);
        }catch (WmServerException e) {
            logger.error("查询客户下门店品牌异常 code = {}, msg = {}", e.getCode(), e.getMsg(), e);
        } catch (TException e) {
            logger.error("查询客户下门店品牌异常", e);
        }
        return Lists.newArrayList();
    }

    public Map<Integer, List<Integer>> getBrandCusIdListMapByBrandIdList(List<Integer> brandIdList) {
        logger.info("getCustomerIdListByBrandIdList brandIdList = {}", JSON.toJSONString(brandIdList));
        if (CollectionUtils.isEmpty(brandIdList)) {
            return Maps.newHashMap();
        }

        List<WmCustomerBrandDB> wmCustomerBrandDBList = wmCustomerBrandDBMapper.selectCustomerIdListByBrandIdList(brandIdList);
        Map<Integer, List<Integer>> brandCusIdListMap = Maps.newHashMap();
        wmCustomerBrandDBList.stream().forEach(wmCustomerBrandDB -> {
            List<Integer> cusIdList = brandCusIdListMap.getOrDefault(wmCustomerBrandDB.getBrandId(), Lists.newArrayList());
            cusIdList.add(wmCustomerBrandDB.getCustomerId());
            brandCusIdListMap.put(wmCustomerBrandDB.getBrandId(), cusIdList);
        });
        return brandCusIdListMap;
    }

    /**
     * 根据业务品牌id和客户id列表查询客户id
     *
     * @param brandIds
     * @return
     */
    public Set<Integer> selectCustomerIdByBrandIdsAndCustomerIds(List<Integer> brandIds, Set<Integer> customerIds) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.service.customer.WmCustomerBrandService.selectCustomerIdByBrandIdsAndCustomerIds(java.util.List,java.util.Set)");
        List<WmCustomerBrandDB> wmCustomerBrandDBList = wmCustomerBrandDBMapper.selectCustomerIdListByBrandIdList(brandIds);
        Set<Integer> customerIdSet = Sets.newHashSet();
        if (!CollectionUtils.isEmpty(wmCustomerBrandDBList)) {
            for (WmCustomerBrandDB wmCustomerBrandDB : wmCustomerBrandDBList) {
                if (customerIds.contains(wmCustomerBrandDB.getCustomerId())) {
                    customerIdSet.add(wmCustomerBrandDB.getCustomerId());
                }
            }
        }
        return customerIdSet;
    }

    /**
     * 根据业务品牌id查询客户id
     *
     * @param brandIds
     * @return
     */
    public Set<Integer> selectCustomerIdByBrandIds(List<Integer> brandIds) {
        List<WmCustomerBrandDB> wmCustomerBrandDBList = wmCustomerBrandDBMapper.selectCustomerIdListByBrandIdList(brandIds);
        Set<Integer> customerIdSet = Sets.newHashSet();
        if (!CollectionUtils.isEmpty(wmCustomerBrandDBList)) {
            for (WmCustomerBrandDB wmCustomerBrandDB : wmCustomerBrandDBList) {
                customerIdSet.add(wmCustomerBrandDB.getCustomerId());

            }
        }
        return customerIdSet;
    }

    public List<WmCustomerBrandDB> listByCondition(WmCustomerBrandCondition condition) {
        if (condition == null) {
            return Lists.newArrayList();
        }
        return wmCustomerBrandDBMapper.listByCondition(condition);
    }
}
