package com.sankuai.meituan.waimai.customer.service.customer.monitor;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.meituan.banma.thrift.deliverycrm.customer.facade.domain.dto.yitihua.monitor.BmWmIDMappingDTO;
import com.sankuai.meituan.banma.thrift.deliverycrm.customer.facade.iface.yitihua.monitor.OpenYthBmWmDataMappingQueryFacade;
import com.sankuai.meituan.banma.thrift.deliverycrm.customer.facade.open.exception.OpenBmCustomerFacadeException;
import com.sankuai.meituan.waimai.customer.config.MccCustomerConfig;
import com.sankuai.meituan.waimai.customer.dao.WmCustomerDBMapper;
import com.sankuai.meituan.waimai.customer.dao.WmCustomerPoiRelExtensionMapper;
import com.sankuai.meituan.waimai.customer.dao.WmCustomerRelMapper;
import com.sankuai.meituan.waimai.customer.ddd.domain.customer.aggre.WmCustomerPoiAggre;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerDB;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerEsQueryVo;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerListDB;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerRelDB;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerESService;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerService;
import com.sankuai.meituan.waimai.poibaseinfo.thrift.domain.result.BaseInfoLong;
import com.sankuai.meituan.waimai.poibaseinfo.thrift.exception.BaseInfoServerException;
import com.sankuai.meituan.waimai.poibaseinfo.thrift.service.BaseInfoQueryThriftService;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerConstants;
import com.sankuai.meituan.waimai.thrift.customer.constant.company.CompanyCustomerConstants;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.WmCustomerPoiLabelInfo;
import com.sankuai.meituan.waimai.thrift.util.ConfigUtilAdapter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


@Slf4j
@Service
public class WmCustomerPoiLabelMonitor {

    private static final String INSERT_TYPE = "insert";

    private static final String DELETE_TYPE = "delete";


    @Autowired
    private WmCustomerESService wmCustomerESService;

    @Autowired
    private WmCustomerService wmCustomerService;

    @Autowired
    private WmCustomerRelMapper wmCustomerRelMapper;

    @Autowired
    private WmCustomerPoiRelExtensionMapper wmCustomerPoiRelExtensionMapper;

    @Autowired
    private WmCustomerDBMapper wmCustomerDBMapper;

    @Autowired
    private OpenYthBmWmDataMappingQueryFacade.Iface openYthBmWmDataMappingQueryFacade;

    @Autowired
    private BaseInfoQueryThriftService baseInfoQueryThriftService;

    public String check(WmCustomerPoiLabelInfo info) {
        Long mtCustomerId = info.getMtCustomerId();
        Long labelId = info.getWmLabelId();
        String operateType = info.getOperateType();
        if (mtCustomerId == null || labelId == null || operateType == null) {
            return "";
        }
        try {
            WmCustomerDB wmCustomerDB = wmCustomerDBMapper.selectCustomerByIdOrMtCustomerId(mtCustomerId);
            if (wmCustomerDB == null) {
                return "";
            }
            if (!MccCustomerConfig.getESDataLabelIdList().contains(labelId)) {
                return "";
            }
            WmCustomerEsQueryVo vo = new WmCustomerEsQueryVo();
            vo.setMtCustomerId(mtCustomerId);
            vo.setIsLeaf(CustomerConstants.CUSTOMER_IS_LEAF_ALL);
            vo.setLabelIds(labelId.toString());
            vo.setPageNo(1);
            vo.setPageSize(10);
            List<WmCustomerListDB> list = wmCustomerESService.queryCustomerPage(vo);
            boolean isInsertFail = INSERT_TYPE.equals(operateType) && CollectionUtils.isEmpty(list);
            boolean isDeleteFail = DELETE_TYPE.equals(operateType) && CollectionUtils.isNotEmpty(list);

            if (isInsertFail || isDeleteFail) {
                return String.format("客户标签同步失败:mtCustomerId:%s,labelId:%s", mtCustomerId, labelId);
            }

            long qkLabelId = ConfigUtilAdapter.getLong("config_company_label_id", 260L);
            if (qkLabelId == labelId && !DELETE_TYPE.equals(operateType)) {
                long wmCustomerId = wmCustomerService.getWmCustomerIdByMtCustomerId(mtCustomerId);
                if (wmCustomerId == 0) {
                    return String.format("未找到客户:mtCustomerId:%s,labelId:%s", mtCustomerId, labelId);
                }

                WmCustomerRelDB wmCustomerRelDB = wmCustomerRelMapper.selectByWmCustomerIdAndBizType(wmCustomerId, CompanyCustomerConstants.COMPANY_CUSTOMER_BIZ_TYPE);
                if (wmCustomerRelDB == null || wmCustomerRelDB.getCustomer_biz_id() == null) {
                    return String.format("客户同步企客失败:mtCustomerId:%s,customerId:%s", mtCustomerId, wmCustomerId);
                }

                Long qikeId = getWmCustomerQikeInfo(wmCustomerId);
                if (qikeId == null) {
                    return String.format("配送侧客户同步企客失败:mtCustomerId:%s,customerId:%s", mtCustomerId, wmCustomerId);
                }
                if (wmCustomerRelDB.getCustomer_biz_id().longValue() != qikeId.longValue()) {
                    return String.format("企客客户关系不一致 customerId:%s,currentBizId:%s,needBizId:%s", wmCustomerId, qikeId, wmCustomerRelDB.getCustomer_biz_id());
                }

                List<Long> wmPoiIds = WmCustomerPoiAggre.Factory.make().selectWmPoiIdsByCustomerId((int) wmCustomerId);
                if (CollectionUtils.isEmpty(wmPoiIds)) {
                    return "";
                }
                List<Long> existRelWmPoiIdList = wmCustomerPoiRelExtensionMapper.getExistRelWmPoiIdListMaster(wmPoiIds, CompanyCustomerConstants.COMPANY_CUSTOMER_BIZ_TYPE);
                boolean isBindFail = CollectionUtils.isEmpty(existRelWmPoiIdList);
                if (isBindFail) {
                    List<Long> validWmPoiId = filterNoWdcId(wmPoiIds);
                    if (CollectionUtils.isNotEmpty(validWmPoiId)) {
                        return String.format("企客门店入驻失败:mtCustomerId:%s,customerId:%s", mtCustomerId, wmCustomerId);
                    } else {
                        return "";
                    }
                }
                boolean isMatchFail = CollectionUtils.isNotEmpty(existRelWmPoiIdList) && wmPoiIds.size() > existRelWmPoiIdList.size();
                if (isMatchFail) {
                    List<Long> noRelationWmPoiIds = wmPoiIds.stream().filter(wmPoiId -> !existRelWmPoiIdList.contains(wmPoiId)).collect(Collectors.toList());
                    List<Long> validWmPoiId = filterNoWdcId(noRelationWmPoiIds);
                    if (CollectionUtils.isNotEmpty(validWmPoiId)) {
                        return String.format("企客门店入驻失败:mtCustomerId:%s,customerId:%s", mtCustomerId, wmCustomerId);
                    }
                }
            }
        } catch (Exception e) {
            log.error("监控客户门店标签失败 info={}", JSONObject.toJSONString(info), e);
        }

        return "";
    }

    private Long getWmCustomerQikeInfo(long wmCustomerId) {
        try {
            List<BmWmIDMappingDTO> bmCustomerIdList = openYthBmWmDataMappingQueryFacade.batchGetBmCustomerIdsByWmCustomerIds(Lists.newArrayList(wmCustomerId));
            if (CollectionUtils.isEmpty(bmCustomerIdList)) {
                return null;
            }
            if (bmCustomerIdList.get(0) == null) {
                return null;
            }
            return bmCustomerIdList.get(0).getBmId();
        } catch (OpenBmCustomerFacadeException e) {
            log.error("batchGetBmCustomerIdsByWmCustomerIds error wmCustomerId={}", wmCustomerId, e);
        } catch (Exception e) {
            log.error("batchGetBmCustomerIdsByWmCustomerIds error wmCustomerId={}", wmCustomerId, e);
        }
        return null;
    }

    private List<Long> filterNoWdcId(List<Long> noRelationWmPoiIds) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.service.customer.monitor.WmCustomerPoiLabelMonitor.filterNoWdcId(java.util.List)");
        List<Long> validWmPoiId = Lists.newArrayList();
        for (Long wmPoiId : noRelationWmPoiIds) {
            try {
                BaseInfoLong baseInfoLong = baseInfoQueryThriftService.queryWdcIdFromBaseInfo(wmPoiId);
                if (baseInfoLong != null && baseInfoLong.getValue() != null && baseInfoLong.getValue().longValue() > 0l) {
                    validWmPoiId.add(wmPoiId);
                }
            } catch (BaseInfoServerException e) {
                log.error("queryWdcIdFromBaseInfo error wmPoiId={}", wmPoiId, e);
                validWmPoiId.add(wmPoiId);
            } catch (Exception e) {
                log.error("queryWdcIdFromBaseInfo error wmPoiId={}", wmPoiId, e);
                validWmPoiId.add(wmPoiId);
            }
        }
        return validWmPoiId;
    }
}
