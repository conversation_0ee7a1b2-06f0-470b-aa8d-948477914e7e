package com.sankuai.meituan.waimai.customer.service.customer.monitor;

import com.dianping.frog.sdk.data.MafkaRawData;
import com.dianping.frog.sdk.data.RawData;
import com.dianping.frog.sdk.rpc.RpcService;
import com.dianping.frog.sdk.rpc.RpcServiceFactory;
import com.dianping.frog.sdk.runner.DefaultRuleRunner;
import com.dianping.frog.sdk.util.BcpLoggerUtils;
import com.google.common.collect.Lists;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.reflect.TypeToken;
import org.apache.commons.lang3.StringUtils;
import com.dianping.frog.sdk.util.JsonUtils;


import java.util.Arrays;
import java.util.List;
import java.util.Map;

public class WmCustomerPoiUnbindRunner extends DefaultRuleRunner {

    private Gson gson = new GsonBuilder().create();


    @Override
    public boolean filter(RawData rawData, RawData... targetData) throws Exception {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.service.customer.monitor.WmCustomerPoiUnbindRunner.filter(com.dianping.frog.sdk.data.RawData,com.dianping.frog.sdk.data.RawData[])");
        MafkaRawData triggerMsgData = (MafkaRawData) rawData;
        Object body = triggerMsgData.getMafkaMessage().getBody();

        if (null == body || StringUtils.isBlank(body.toString())) {
            return false;
        }
        Map<String, Object> triggerMsg = toMap(body.toString());
        if (triggerMsg.isEmpty()) {
            return false;
        }
        Integer eventType = (Integer) triggerMsg.get("eventType");
        // 客户删除、解绑事件
        if (eventType == null || (eventType.intValue() != 2 && eventType.intValue() != 4)) {
            return false;
        }
        // 参数校验
        if (triggerMsg.get("extraData") == null) {
            return false;
        }
        Map<String, Object> extraData = toMap(triggerMsg.get("extraData").toString());
        if (extraData.isEmpty() || extraData.get("wmPoiIds") == null) {
            return false;
        }
        String wmPoiIds = (String) extraData.get("wmPoiIds");
        if (StringUtils.isBlank(wmPoiIds)) {
            return false;
        }
        return true;
    }


    @Override
    public String check(RawData rawData, RawData... rawData2) throws Exception {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.service.customer.monitor.WmCustomerPoiUnbindRunner.check(com.dianping.frog.sdk.data.RawData,com.dianping.frog.sdk.data.RawData[])");
        MafkaRawData triggerMsgData = (MafkaRawData) rawData;
        Object body = triggerMsgData.getMafkaMessage().getBody();

        try {
            if (body == null) {
                return null;
            }
            Map<String, Object> triggerMsg = toMap(body.toString());
            if (triggerMsg.isEmpty()) {
                return null;
            }
            Integer eventType = (Integer) triggerMsg.get("eventType");
            if (eventType == null || (eventType.intValue() != 2 && eventType.intValue() != 4)) {
                return null;
            }
            if (triggerMsg.get("extraData") == null) {
                return null;
            }
            Map<String, Object> extraData = toMap(triggerMsg.get("extraData").toString());
            if (extraData.isEmpty() || extraData.get("wmPoiIds") == null) {
                return null;
            }
            String wmPoiIds = (String) extraData.get("wmPoiIds");
            if (StringUtils.isBlank(wmPoiIds)) {
                return null;
            }

            RpcService rpcService = RpcServiceFactory.getThriftInstance("com.sankuai.meituan.waimai.thrift.customer.service.WmCustomerPoiThriftService",
                    "com.sankuai.waimai.e.customer", 10000, null, "8435");
            String result = rpcService.invoke("monitorCustomerPoiUnbind",
                    Lists.newArrayList("java.util.List"),
                    Lists.newArrayList(JsonUtils.toJson(Arrays.asList(wmPoiIds.split(",")))));

            if (!StringUtils.isBlank(result) && !"\"\"".equals(result)) {
                return result;
            }
        } catch (Exception e) {
            BcpLoggerUtils.info("类型转换失败:msg={}" + body.toString() + ";异常日志:" + e.getStackTrace());    // 打印日志
        }
        return null;
    }


    private Map<String, Object> toMap(String json) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.service.customer.monitor.WmCustomerPoiUnbindRunner.toMap(java.lang.String)");
        TypeToken<?> parameterized = TypeToken.getParameterized(Map.class, String.class, Object.class);
        return gson.fromJson(json, parameterized.getType());
    }
}
