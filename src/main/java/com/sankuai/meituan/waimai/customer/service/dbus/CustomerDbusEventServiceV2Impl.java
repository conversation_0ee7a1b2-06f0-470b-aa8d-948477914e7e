package com.sankuai.meituan.waimai.customer.service.dbus;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.util.concurrent.Uninterruptibles;
import com.meituan.dbus.common.DbusUtils;
import com.meituan.dbus.common.StaticUtils;
import com.meituan.dbus.thriftV2.DataBusEventServiceV2;
import com.meituan.dbus.thriftV2.utils.ThriftV2Utils;
import com.meituan.mafka.client.bean.MafkaProducer;
import com.sankuai.meituan.util.ConfigUtilAdapter;
import com.sankuai.meituan.waimai.customer.config.MccCustomerConfig;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerDB;
import com.sankuai.meituan.waimai.customer.mq.service.MafkaMessageSendManager;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerESService;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerLabelService;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerPlatformDataParseService;
import com.sankuai.meituan.waimai.customer.service.customer.ownerapply.WmCustomerOwnerApplyService;
import com.sankuai.meituan.waimai.customer.service.kp.WmCustomerKpService;
import com.sankuai.meituan.waimai.customer.util.ExecutorUtil;
import com.sankuai.meituan.waimai.poibizflow.constant.datatransfer.WmDataSubModuleEnum;
import com.sankuai.meituan.waimai.poibizflow.thrift.datatransfer.WmSupplyChainDataSyncThriftService;
import com.sankuai.meituan.waimai.poibizflow.thrift.exception.WmPoiBizException;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerConstants;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;

import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerRealTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.mq.CustomerMQBody;
import com.sankuai.meituan.waimai.thrift.customer.domain.mq.CustomerMQEventEnum;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import org.apache.thrift.TException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
public class CustomerDbusEventServiceV2Impl implements DataBusEventServiceV2.Iface {

    static Logger logger = LoggerFactory.getLogger(CustomerDbusEventServiceV2Impl.class);

    private static final String CUSTOMER_REAL_TYPE = "customer_real_type";

    private static final String OWNER_UID = "owner_uid";
    private static final String SIGN_MODE = "sign_mode";

    private static final String EFFECTIVE = "effective";

    private static final int UPDATE_ES_FAIL_RETRY_TIME = 3;


    @Autowired
    WmCustomerESService wmCustomerESService;

    @Autowired
    private WmCustomerPlatformDataParseService wmCustomerPlatformDataParseService;

    @Autowired
    private MafkaMessageSendManager mafkaMessageSendManager;

    @Autowired
    private WmSupplyChainDataSyncThriftService wmSupplyChainDataSyncThriftService;

    @Autowired
    private WmCustomerKpService wmCustomerKpService;

    @Autowired
    private WmCustomerLabelService wmCustomerLabelService;

    @Resource(name = "wmCustomerChangeProducer")
    private MafkaProducer wmCustomerChangeProducer;
    
    @Autowired
    private WmCustomerOwnerApplyService wmCustomerOwnerApplyService;

    @Override
    public String handleUpdate(Map<String, String> metaJsonData, String dataMapJson, String diffJson) throws TException {
        logger.info("#databus监听客户表变更# 更新数据");
        logger.info("#databus监听客户表变更#, metaJsonData={}, dataMapJson={}, diffJson={}", JSONObject.toJSONString(metaJsonData), JSONObject.toJSONString(dataMapJson), JSONObject.toJSON(diffJson));
        DbusUtils utils = ThriftV2Utils.newThriftV2InstanceForUpdate(metaJsonData, dataMapJson, diffJson);

        WmCustomerDB db = transToDb(utils);
        //企客监听CUSTOMER_REAL_TYPE、SIGN_MODE字段变更
        Map<String, Object> diffMap = utils.getDiffMap();
        if (ConfigUtilAdapter.getBoolean("CustomerDbus_sendChangeDelayMsg_open", true) && db.getId() != null && db.getId() > 0 && (diffMap.containsKey(CUSTOMER_REAL_TYPE) || diffMap.containsKey(SIGN_MODE))) {
            try {
                wmSupplyChainDataSyncThriftService.sendChangeDelayMsg(db.getId().toString(), WmDataSubModuleEnum.WM_CUSTOMER_BASE.getSubModuleName());
            } catch (WmPoiBizException e) {
                logger.warn("sendChangeDelayMsg exception", e);
            }
        }

        // 客户类型变更需同步客户责任申请中客户类型
        updateCustomerRealType2OwnerApply(diffMap, db);

        // 客户责任人变更有进行中客户责任人申请，需处理驳回
        rejectOwnerApplyOnOwnerChange(diffMap, db);

        customerEffectDrivenKpSigner(db.getId(), diffMap, db);
        if (db.getValid() == CustomerConstants.UNVALID) {
            logger.info("#databus监听客户表变更# 逻辑删除数据");
            if (db.getId() % 100 < MccCustomerConfig.customerSyncEsByMQGrayPercent()) {
                return sendCustomerChangeNotify(db, "delete");
            } else {
                return delete(db);
            }
        }

        // 计算业务线
        Integer bizOrgCode = CustomerRealTypeEnum.getBizOrgCodeByValue(db.getCustomerRealType());
        db.setBizOrgCode(bizOrgCode);
        if (db.getId() % 100 < MccCustomerConfig.customerSyncEsByMQGrayPercent()) {
            return sendCustomerChangeNotify(db, "update");
        } else {
            return upsert(db);
        }
    }

    /**
     * 更新客户类型到客户归属申请记录
     * 
     * @param diffMap
     * @param db
     */
    private void updateCustomerRealType2OwnerApply(Map<String, Object> diffMap, WmCustomerDB db) {
        try {
            // 开关不开启不需要同步
            if (!MccCustomerConfig.getSyncCustomerChangeInfo2CustomerOwnerApplySwitch()) {
                return;
            }
            // 客户ID小于等于0或者客户类型未变更不需要同步
            if (db.getId() == null || db.getId() <= 0 || !diffMap.containsKey(CUSTOMER_REAL_TYPE)) {
                return;
            }
            logger.info("updateCustomerRealType2OwnerApply,客户类型变更同步新客户类型到申请记录,customerId={},newCustomerRealType={}", db.getId(), db.getCustomerRealType());
            Integer customerId = db.getId();
            Integer newCustomerRealType = db.getCustomerRealType();
            wmCustomerOwnerApplyService.updateCustomerRealTypeInChange(customerId, newCustomerRealType);
        } catch (Exception e) {
            logger.error(
                    "updateCustomerRealType2OwnerApply,客户类型变更同步新客户类型到申请记录发生异常,customerId={},newCustomerRealType={}",
                    db.getId(), db.getCustomerRealType(), e);
        }
    }

    /**
     * 客户责任人变更驳回客户责任人修改申请
     * 
     * @param diffMap
     * @param db
     */
    private void rejectOwnerApplyOnOwnerChange(Map<String, Object> diffMap, WmCustomerDB db) {

        try {
            // 开关不开启不需要同步
            if (!MccCustomerConfig.getSyncCustomerChangeInfo2CustomerOwnerApplySwitch()) {
                return;
            }
            // 客户ID小于等于0或者客户类型未变更不需要同步
            if (db.getId() == null || db.getId() <= 0 || !diffMap.containsKey(OWNER_UID)) {
                return;
            }
            logger.info("rejectOwnerApplyOnOwnerChange,客户责任人变更自动驳回进行中客户责任人申请,customerId={}", db.getId());
            Integer customerId = db.getId();
            wmCustomerOwnerApplyService.rejectOwnerApplyWhenOwnerChange(customerId);
        } catch (Exception e) {
            logger.error("rejectOwnerApplyOnOwnerChange,客户责任人变更自动驳回进行中客户责任人申请发生异常,customerId={}", db.getId(), e);
        }

    }


    private String sendCustomerChangeNotify(WmCustomerDB db, String type) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("id", db.getId());
        jsonObject.put("data", db);
        jsonObject.put("type", type);
        try {
            wmCustomerChangeProducer.sendMessage(jsonObject.toJSONString(), db.getId());
            return StaticUtils.ok;
        } catch (Exception e) {
            logger.error("wmCustomerChangeProducer 异常：data={}", jsonObject.toJSONString(), e);
            return StaticUtils.fail;
        }
    }

    /**
     * 客户生效驱动原kp签约人流程
     *
     * @param id
     * @param diffMap
     */
    private void customerEffectDrivenKpSigner(Integer id, Map<String, Object> diffMap, WmCustomerDB db) {
        if (id == null || id <= 0) {
            return;
        }
        if (diffMap.containsKey(EFFECTIVE) || db.isEffectived()) {
            ExecutorUtil.execAsyncWithTraceId(new ExecutorUtil.Executor() {
                @Override
                public Object exec() {
                    try {
                        logger.info("#databus监听客户表变更 customerEffectDrivenKpSigner:id={}", id);
                        WmCustomerDB customer = wmCustomerPlatformDataParseService.selectCustomerFromPlatformById(id);
                        if (customer != null && customer.isEffectived() && customer.getValid() != null && customer.getValid() == CustomerConstants.VALID) {
                            //设置签约人KP
                            wmCustomerKpService.customerEffectDrivenKpSigner(customer);
                        }
                    } catch (WmCustomerException e) {
                        logger.warn("customerEffectDrivenKpSigner selectCustomerById 失败 id={}", id, e);
                    }
                    return null;
                }

                @Override
                public boolean shouldRetry(Object o) {
                    return false;
                }
            }, MccCustomerConfig.getCustomerEffectDrivenKpSignerDelayTime());
        }
    }

    private String upsert(WmCustomerDB db) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.service.dbus.CustomerDbusEventServiceV2Impl.upsert(com.sankuai.meituan.waimai.customer.domain.WmCustomerDB)");
        long afterMillis = MccCustomerConfig.getCustomerSyncESDelayTime();
        ExecutorUtil.execAsyncWithTraceId(new ExecutorUtil.Executor() {
            @Override
            public Object exec() {
                try {
                    logger.info("#databus监听客户表变更 刷新客户ES db={}", JSONObject.toJSONString(db));
                    //更新es时
                    wmCustomerESService.refreshToUpsertEs(db);
                } catch (Exception e) {
                    logger.error("同步更新客户es数据失败 db={}", JSONObject.toJSONString(db), e);
                } finally {
                    sendCustomerChangeMsg(db);
                }
                return null;
            }

            @Override
            public boolean shouldRetry(Object o) {
                return false;
            }
        }, afterMillis);
        return StaticUtils.ok;
    }

    private WmCustomerDB transToDb(DbusUtils utils) {
        Map<String, Object> aftMap = utils.getAftMap();
        String jsonString = JSON.toJSONString(aftMap);
        return JSON.parseObject(jsonString, WmCustomerDB.class);
    }

    private void sendCustomerChangeMsg(WmCustomerDB db) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_1", "com.sankuai.meituan.waimai.customer.service.dbus.CustomerDbusEventServiceV2Impl.sendCustomerChangeMsg(com.sankuai.meituan.waimai.customer.domain.WmCustomerDB)");
        try {
            JSONObject extraData = new JSONObject();
            extraData.put("customerRealType", db.getCustomerRealType());
            CustomerMQBody customerMQBody = new CustomerMQBody(db.getId(), CustomerMQEventEnum.CUSTOMER_CHANGE, extraData.toJSONString());
            logger.info("sendCustomerChangeMsg customerMQBody = {}", JSON.toJSONString(customerMQBody));
            mafkaMessageSendManager.send(customerMQBody);
        } catch (Exception e) {
            logger.warn("发送【客户变更】消息异常 customerId = {}", db.getId(), e);
        }
    }

    @Override
    public String handleInsert(Map<String, String> metaJsonData, String dataMapJson) throws TException {
        logger.info("#databus监听客户表变更# 新增数据");
        DbusUtils utils = ThriftV2Utils.newThriftV2InstanceForInsert(metaJsonData, dataMapJson);
        WmCustomerDB db = transToDb(utils);
        if (db.getValid() == CustomerConstants.UNVALID) {
            return StaticUtils.ok;
        }
        // 计算业务线
        Integer bizOrgCode = CustomerRealTypeEnum.getBizOrgCodeByValue(db.getCustomerRealType());
        db.setBizOrgCode(bizOrgCode);
        if (db.getId() % 100 < MccCustomerConfig.customerSyncEsByMQGrayPercent()) {
            return sendCustomerChangeNotify(db, "insert");
        } else {
            return insert(db);
        }
    }

    private String insert(WmCustomerDB db) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.dbus.CustomerDbusEventServiceV2Impl.insert(com.sankuai.meituan.waimai.customer.domain.WmCustomerDB)");
        ExecutorUtil.execAsyncWithTraceId(new ExecutorUtil.Executor() {
            @Override
            public Object exec() {
                try {
                    logger.info("#databus监听客户表变更 插入客户ES db={}", JSONObject.toJSONString(db));
                    // 更新es时
                    wmCustomerESService.refreshToUpsertEs(db);
                    // 更新标签

                    boolean isSucc = false;
                    for (int i = 0; i < UPDATE_ES_FAIL_RETRY_TIME; i++) {
                        try {
                            wmCustomerLabelService.customerLabelSync(db.getMtCustomerId(), Long.valueOf(db.getId()));
                            isSucc = true;
                            logger.info("customerLabelSync customerId={},mtCustomerId={} update", db.getId(), db.getMtCustomerId());
                            break;
                        } catch (Exception e) {
                            logger.warn("customerLabelSync error mtCustomerId={}", db.getId(), db.getMtCustomerId(), e);
                        }
                        Uninterruptibles.sleepUninterruptibly(500, TimeUnit.MILLISECONDS);
                    }
                    if (!isSucc) {
                        logger.error("customerLabelSync error mtCustomerId={}",  db.getMtCustomerId());
                    }
                } catch (Exception e) {
                    logger.error("同步插入客户es数据失败 db={}", JSONObject.toJSONString(db), e);
                } finally {
                    sendCustomerChangeMsg(db);
                }
                return null;
            }

            @Override
            public boolean shouldRetry(Object o) {
                return false;
            }
        }, MccCustomerConfig.getCustomerSyncESDelayTime());
        return StaticUtils.ok;
    }

    @Override
    public String handleDelete(Map<String, String> metaJsonData, String dataMapJson) throws TException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_1", "com.sankuai.meituan.waimai.customer.service.dbus.CustomerDbusEventServiceV2Impl.handleDelete(java.util.Map,java.lang.String)");
        logger.info("#databus监听客户表变更# 物理删除数据");
        DbusUtils utils = ThriftV2Utils.newThriftV2InstanceForDelete(metaJsonData, dataMapJson);
        WmCustomerDB db = transToDbForDelete(utils);
        if (db.getId() % 100 < MccCustomerConfig.customerSyncEsByMQGrayPercent()) {
            return sendCustomerChangeNotify(db, "delete");
        } else {
            return delete(db);
        }
    }

    private WmCustomerDB transToDbForDelete(DbusUtils utils){
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_1", "com.sankuai.meituan.waimai.customer.service.dbus.CustomerDbusEventServiceV2Impl.transToDbForDelete(com.meituan.dbus.common.DbusUtils)");
        Map<String, Object> preMap = utils.getPreMap();
        String jsonString = JSON.toJSONString(preMap);
        return JSON.parseObject(jsonString, WmCustomerDB.class);
    }

    private String delete(WmCustomerDB db) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_2", "com.sankuai.meituan.waimai.customer.service.dbus.CustomerDbusEventServiceV2Impl.delete(com.sankuai.meituan.waimai.customer.domain.WmCustomerDB)");
        try {
            wmCustomerESService.syncToDelEs(db.getId());
        } catch (TimeoutException e) {
            logger.warn("同步删除客户es数据失败，超时了", e);
            return StaticUtils.fail;
        } catch (Exception e) {
            logger.error("同步删除客户es数据失败", e);
        }
        return StaticUtils.ok;
    }
}
