package com.sankuai.meituan.waimai.customer.contract.partner.ability.save.impl;

import com.meituan.mtrace.Tracer;
import com.sankuai.inf.octo.mns.util.ProcessInfoUtil;
import com.sankuai.meituan.util.DateUtil;
import com.sankuai.meituan.waimai.customer.config.MccConfig;
import com.sankuai.meituan.waimai.customer.constant.sign.WmSignConstant;
import com.sankuai.meituan.waimai.customer.contract.partner.ability.save.AbstractWmPartnerCustomerContractSaveAbilityService;
import com.sankuai.meituan.waimai.customer.contract.service.impl.WmContractService;
import com.sankuai.meituan.waimai.customer.service.common.EmpServiceAdaptor;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerService;
import com.sankuai.meituan.waimai.customer.service.sign.WmEcontractSignBzService;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerErrorCodeConstants;
import com.sankuai.meituan.waimai.thrift.customer.constant.SignPackWay;
import com.sankuai.meituan.waimai.thrift.customer.constant.SignSubjectEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.WmTempletContractTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.partner.ContractSaveSceneTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.WmCustomerContractBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.WmTempletContractBasicBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.WmTempletContractSignBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.framecontract.DcC1ExchangeInfo;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.partnercontract.ContractOperatorDTO;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.partnercontract.CustomerContractSaveRequestDTO;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.partnercontract.CustomerContractSaveResponseDTO;
import com.sankuai.meituan.waimai.thrift.customer.domain.partner.DaoCanContractInfo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import com.sankuai.meituan.waimai.util.DaxiangUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.thrift.TException;
import org.mapstruct.ap.internal.util.Strings;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @description: 到餐-开店宝-商家自入驻, 无需签约, 直接生效
 * @author: liuyunjie05
 * @create: 2024/8/6 11:51
 */
@Slf4j
@Service
public class DaoCanSelfSettleOpenServiceImpl extends AbstractWmPartnerCustomerContractSaveAbilityService {
    @Resource
    private WmContractService wmContractService;

    @Resource
    private EmpServiceAdaptor empServiceAdaptor;

    @Resource
    private WmCustomerService wmCustomerService;

    @Resource
    private WmEcontractSignBzService wmEcontractSignBzService;

    @Override
    public ContractSaveSceneTypeEnum getSupportSceneType() {
        return ContractSaveSceneTypeEnum.DAOCAN_KDB_SELF_SETTLE_OPEN;
    }

    private DaoCanContractInfo initDcC2ContractInfo(Long mtCustomerId) {
        DaoCanContractInfo daoCanContractInfo = new DaoCanContractInfo();
        daoCanContractInfo.setNeedCheckC1Renew(false);
        daoCanContractInfo.setContractType(WmTempletContractTypeEnum.DAOCAN_SERVICE_C2_CONTRACT.getCode());
        daoCanContractInfo.setMtCustomerId(mtCustomerId);
        return daoCanContractInfo;
    }

    @Override
    public CustomerContractSaveResponseDTO saveCustomerContract(CustomerContractSaveRequestDTO requestDTO) throws WmCustomerException {
        try {
            validateRequestParam(requestDTO);
            List<DcC1ExchangeInfo> needResinContractList = getNeedResinContractList(requestDTO.getMtCustomerId());
            List<Integer> signTaskIdList = saveDcContract(requestDTO.getMtCustomerId(), requestDTO.getOperatorDTO(), needResinContractList, requestDTO.getAgentId());
            return buildResponse(signTaskIdList, requestDTO.getMtCustomerId());
        } catch (WmCustomerException e) {
            log.warn("DaoCanSelfSettleOpenServiceImpl#saveCustomerContract, warn", e);
            return fail(e.getMsg(), requestDTO.getMtCustomerId());
        } catch (Exception e) {
            log.error("DaoCanSelfSettleOpenServiceImpl#saveCustomerContract, error", e);
            return fail("保存失败", requestDTO.getMtCustomerId());
        }
    }

    private void validateRequestParam(CustomerContractSaveRequestDTO requestDTO) throws WmCustomerException {
        checkCustomerContractParam(requestDTO);
        if (requestDTO.getAgentId() == null) {
            throw new WmCustomerException(CustomerErrorCodeConstants.PARAM_ERROR, "参数异常: 缺少代理商ID");
        }
        if (requestDTO.getAgentId() <= 0) {
            throw new WmCustomerException(CustomerErrorCodeConstants.PARAM_ERROR, "参数异常: 代理商ID不合法");
        }
        Integer contractType = requestDTO.getContractType();
        if (WmTempletContractTypeEnum.DAOCAN_SERVICE_C2_CONTRACT.getCode() != contractType) {
            throw new WmCustomerException(CustomerErrorCodeConstants.PARAM_ERROR, "参数异常: 该渠道暂时只支持发起到餐C2合同");
        }
    }


    private List<Integer> saveDcContract(Long mtCustomerId, ContractOperatorDTO operatorDTO, List<DcC1ExchangeInfo> needResinContractList, Integer agentId) throws WmCustomerException, TException {
        List<Integer> signTaskList = new ArrayList<>();
        signTaskList.add(saveDaoCanC2Contract(mtCustomerId, agentId, operatorDTO, SignPackWay.DO_SIGN));
        if (CollectionUtils.isNotEmpty(needResinContractList)) {
            List<Integer> dcC1ContractList = saveDaoCanC1Contract(mtCustomerId, operatorDTO, needResinContractList);
            signTaskList.addAll(dcC1ContractList);
        }
        return signTaskList;
    }

    private CustomerContractSaveResponseDTO buildResponse(List<Integer> signTaskIdList, Long mtCustomerId) throws WmCustomerException {
        CustomerContractSaveResponseDTO responseDTO = success(mtCustomerId);
        responseDTO.setSignTaskIdList(signTaskIdList);
        return responseDTO;
    }

    private List<Integer> saveDaoCanC1Contract(Long mtCustomerId, ContractOperatorDTO operatorDTO, List<DcC1ExchangeInfo> needResinContractList) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.contract.partner.ability.save.impl.DaoCanSelfSettleOpenServiceImpl.saveDaoCanC1Contract(Long,ContractOperatorDTO,List)");
        return needResinContractList.stream()
                .map(exchangeInfo -> saveSingleDaoCanC1Contract(mtCustomerId, operatorDTO, exchangeInfo))
                .filter(signTaskId -> signTaskId != null && signTaskId > 0)
                .collect(Collectors.toList());
    }

    private Integer saveSingleDaoCanC1Contract(Long mtCustomerId, ContractOperatorDTO operatorDTO, DcC1ExchangeInfo exchangeInfo) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.contract.partner.ability.save.impl.DaoCanSelfSettleOpenServiceImpl.saveSingleDaoCanC1Contract(Long,ContractOperatorDTO,DcC1ExchangeInfo)");
        try {
            WmCustomerContractBo contractBo = wrapDcC1CustomerContractBo(mtCustomerId, operatorDTO, exchangeInfo);
            return wmContractService.saveAndStartSign(contractBo, Math.toIntExact(operatorDTO.getOpId()), operatorDTO.getOpName());
        } catch (Exception e) {
            log.error("DaoCanSelfSettleOpenServiceImpl#saveSingleDaoCanC1Contract, error", e);
            metricAndAlertMsg(ContractSaveSceneTypeEnum.DAOCAN_KDB_SELF_SETTLE_OPEN.getDesc(), "发起到餐C1合同的待打包任务失败", e);
            return null;
        }
    }

    private WmCustomerContractBo wrapDcC1CustomerContractBo(Long mtCustomerId, ContractOperatorDTO operatorDTO, DcC1ExchangeInfo exchangeInfo) throws WmCustomerException, TException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.contract.partner.ability.save.impl.DaoCanSelfSettleOpenServiceImpl.wrapDcC1CustomerContractBo(Long,ContractOperatorDTO,DcC1ExchangeInfo)");
        WmCustomerContractBo contractBo = new WmCustomerContractBo();

        long wmCustomerId = getWmCustomerByMtCustomerId(mtCustomerId);

        WmTempletContractBasicBo basicBo = initBasicBo(WmTempletContractTypeEnum.DAOCAN_SERVICE_C1_CONTRACT.getCode(), (int) wmCustomerId);
        basicBo.setDaoCanContractInfo(initDcRenewC1ContractInfo(exchangeInfo, mtCustomerId));
        contractBo.setBasicBo(basicBo);

        List<WmTempletContractSignBo> signBoList = new ArrayList<>();
        signBoList.add(initDaoCanPartA((int) wmCustomerId, mtCustomerId));
        signBoList.add(initDaoCanC1PartB(operatorDTO));
        contractBo.setSignBoList(signBoList);

        contractBo.setIgnoreExistAnotherSignTypeContract(false);
        contractBo.setPackWay(SignPackWay.DO_SIGN.getCode());
        contractBo.setManualBatchId(0);
        return contractBo;
    }

    private WmTempletContractSignBo initDaoCanC1PartB(ContractOperatorDTO operatorDTO) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.contract.partner.ability.save.impl.DaoCanSelfSettleOpenServiceImpl.initDaoCanC1PartB(ContractOperatorDTO)");
        String today = DateUtil.secondsToString(DateUtil.unixTime());
        WmTempletContractSignBo signBo = new WmTempletContractSignBo();
        signBo.setSignId(0);
        signBo.setTempletContractId(0);
        signBo.setSignName(SignSubjectEnum.BJ_SANKUAI.getDesc());
        signBo.setSignPeople(operatorDTO.getOpName());
        signBo.setSignPhone(empServiceAdaptor.getPhone(Math.toIntExact(operatorDTO.getOpId())));

        signBo.setSignTime(today);
        signBo.setSignType("B");
        return signBo;
    }

    private Integer saveDaoCanC2Contract(Long mtCustomerId, int agentId, ContractOperatorDTO operatorDTO, SignPackWay signPackWay) {
        try {
            log.info("DaoCanSelfSettleOpenServiceImpl#saveDaoCanC2Contract, mtCustomerId: {}, agentId: {}, signPackWay: {}",
                    mtCustomerId, agentId, signPackWay);
            WmCustomerContractBo contractBo = wrapDcC2CustomerContractBo(mtCustomerId, agentId, signPackWay);
            Integer contractId = wmContractService.saveAndStartSign(contractBo, 0, operatorDTO.getOpName());
            log.info("DaoCanSelfSettleOpenServiceImpl#saveDaoCanC2Contract, contractId: {}", contractId);
            return contractId;
        } catch (Exception e) {
            log.error("DaoCanSelfSettleOpenServiceImpl#saveDaoCanC2Contract, error", e);
            metricAndAlertMsg(ContractSaveSceneTypeEnum.DAOCAN_KDB_SELF_SETTLE_OPEN.getDesc(), "开店宝自入驻发起到餐C2合同失败", e);
            return null;
        }
    }

    private WmCustomerContractBo wrapDcC2CustomerContractBo(Long mtCustomerId, int agentId, SignPackWay signPackWay) throws WmCustomerException {
        WmCustomerContractBo contractBo = new WmCustomerContractBo();

        long wmCustomerId = getWmCustomerByMtCustomerId(mtCustomerId);
        WmTempletContractBasicBo basicBo = initBasicBo(WmTempletContractTypeEnum.DAOCAN_SERVICE_C2_CONTRACT.getCode(), (int) wmCustomerId);
        basicBo.setDaoCanContractInfo(initDcC2ContractInfo(mtCustomerId));
        basicBo.setSenselessEffect(true);
        contractBo.setBasicBo(basicBo);

        List<WmTempletContractSignBo> signBoList = new ArrayList<>();
        signBoList.add(initDaoCanPartA((int) wmCustomerId, mtCustomerId));
        signBoList.add(initPartB(agentId));
        contractBo.setSignBoList(signBoList);

        contractBo.setIgnoreExistAnotherSignTypeContract(false);
        contractBo.setPackWay(signPackWay.getCode());
        contractBo.setManualBatchId(0);
        return contractBo;
    }


    private WmTempletContractSignBo initPartB(int agentId) throws WmCustomerException {
        WmTempletContractSignBo signBo = initAgentSignBo(agentId);
        signBo.setSignType("B");
        return signBo;
    }

}
