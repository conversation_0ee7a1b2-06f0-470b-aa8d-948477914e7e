package com.sankuai.meituan.waimai.customer.service.customer.Impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.sankuai.meituan.waimai.customer.adapter.WmPoiSwitchServiceAdapter;
import com.sankuai.meituan.waimai.customer.config.MccConfig;
import com.sankuai.meituan.waimai.customer.config.MccCustomerConfig;
import com.sankuai.meituan.waimai.customer.constant.WmCustomerConstant;
import com.sankuai.meituan.waimai.customer.contract.service.impl.WmContractService;
import com.sankuai.meituan.waimai.customer.contract.service.impl.WmCustomerOplogService;
import com.sankuai.meituan.waimai.customer.ddd.adapter.MtCustomerThriftServiceAdapterImpl;
import com.sankuai.meituan.waimai.customer.ddd.domain.customer.aggre.WmCustomerPoiAggre;
import com.sankuai.meituan.waimai.customer.domain.CustomerSceneInfoBODiff;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerDB;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerPoiDB;
import com.sankuai.meituan.waimai.customer.service.customer.*;
import com.sankuai.meituan.waimai.customer.service.gray.WmCustomerGrayService;
import com.sankuai.meituan.waimai.customer.service.version.VersionCheckUtil;
import com.sankuai.meituan.waimai.customer.util.business.WmCustomerUtil;
import com.sankuai.meituan.waimai.customer.util.trans.WmCustomerTransUtil;
import com.sankuai.meituan.waimai.label.constants.LabelSubjectTypeEnum;
import com.sankuai.meituan.waimai.thrift.bo.WmCustomerDiffCellBo;
import com.sankuai.meituan.waimai.thrift.customer.constant.*;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerAuditStatus;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerConstants;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerRelationStatusEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerSource;
import com.sankuai.meituan.waimai.thrift.customer.constant.customer.CertificateTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.WmCustomerOplogBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.CustomerRealTypeSpInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.ValidateResultBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.WmCustomerBasicBo;
import com.sankuai.meituan.waimai.thrift.domain.WmPoiLabelRel;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import com.sankuai.meituan.waimai.thrift.util.DiffUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.thrift.TException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 *
 */
@Service("wmLeafCustomerRealService")
public class WmLeafCustomerServiceImpl extends AbstractWmCustomerRealService {

    private static Logger logger = LoggerFactory.getLogger(WmLeafCustomerServiceImpl.class);

    @Autowired
    private WmCustomerPlatformDataParseService wmCustomerPlatformDataParseService;

    @Autowired
    private WmContractService wmContractService;

    @Autowired
    private WmCustomerOplogService wmCustomerOplogService;

    @Autowired
    private WmCustomerService wmCustomerService;

    private static String URL_HTTP_CONSTANT = "http";

    @Autowired
    protected MtCustomerThriftServiceAdapterImpl mtCustomerThriftServiceAdapter;

    @Autowired
    private WmPoiSwitchServiceAdapter wmPoiSwitchServiceAdapter;

    @Autowired
    private WmCustomerMSCLabelService wmCustomerMSCLabelService;

    @Autowired
    private UpmAuthCheckService upmAuthCheckService;

    @Autowired
    private WmCustomerLabelService wmCustomerLabelService;

    @Autowired
    private WmCustomerGrayService wmCustomerGrayService;

    @Autowired
    private SpecialApprovalService specialApprovalService;

    @Autowired
    private WmCustomerAuditService wmCustomerAuditService;

    @Override
    protected boolean updateCustomer(WmCustomerBasicBo wmCustomerBasicBo, ValidateResultBo validateResultBo, Integer opUid, String opName, int channel) throws WmCustomerException, TException {
        // 是否直接修改不进行提审
        boolean isUpdateNotAudit = false;
        boolean isUploadImg = (channel == CustomerSource.WAIMAI_BD.getCode() && !wmCustomerBasicBo.getPicUrl().contains(URL_HTTP_CONSTANT)) ? false : true;
        VersionCheckUtil.versionCheck(wmCustomerBasicBo.getId(), 0);
        WmCustomerDB wmCustomerDB = wmCustomerPlatformDataParseService.selectCustomerFromPlatformById(wmCustomerBasicBo.getId());
        wmCustomerBasicBo.setMtCustomerId(wmCustomerDB.getMtCustomerId());

        //校验是否可以修改：1客户ID有效存在 2子客户与客户不能切换 3审核中不能修改
        checkUpdateCustomer(wmCustomerDB, wmCustomerBasicBo);

        //美食城客户校验(不可有进行中的切换任务&客户下门店不可在预绑定流程中)
        checkMSCCustomerModify(wmCustomerBasicBo,wmCustomerDB);

        //签约模式变更，直接修改
        if (!wmCustomerBasicBo.getSignMode().equals(wmCustomerDB.getSignMode())) {
            logger.info("签约模式变更，无需审核，直接生效 signMode:{}", wmCustomerBasicBo.getSignMode());
            wmCustomerPlatformDataParseService.updateSignMode(wmCustomerBasicBo.getId(), wmCustomerBasicBo.getSignMode());
        }

        //如果营业执照类型为电子形式，不需要提审,直接生效（美食城客户场景仍需要提审）
        if (checkCertifyTypeElectAndNotMsc(wmCustomerBasicBo)) {
            updateCustomerCertificateType(wmCustomerBasicBo, wmCustomerDB, opUid, opName, isUploadImg);
            validateResultBo.setMsg("保存成功");
            isUpdateNotAudit = true;
            return isUpdateNotAudit;
        }

        // 客户平台资质图片特殊处理
        String auditPicUrl = wmCustomerBasicBo.getPicUrl();
        if (wmCustomerBasicBo.getPicUrl().contains(WmCustomerConstant.URL_CUSTOMER_PLATFORM_PREFIX)) {
            auditPicUrl = mtCustomerThriftServiceAdapter.uploadImgFromCustomerPlatform(wmCustomerBasicBo.getId(), wmCustomerBasicBo.getPicUrl());
            String mtCustomerUrl = mtCustomerThriftServiceAdapter.transferMtCustomerPicUrl(wmCustomerBasicBo.getPicUrl());
            wmCustomerBasicBo.setPicUrl(mtCustomerUrl);
        }

        //获取前后diff信息
        List<WmCustomerDiffCellBo> diffList = getCustomerDiff(wmCustomerBasicBo, wmCustomerDB);

        //是否命中特批流程
        boolean hitSpecialFlow = hitSinglePerCertifyFlow(opUid, wmCustomerBasicBo, wmCustomerDB.getAuditStatus()) && !diffList.isEmpty();

        //仅仅客户类型进行了变更，并且不是单店切换为[美食城/食堂]，则不需要提审
        boolean notAuditByCustomerRealType = notAuditByCustomerRealType(wmCustomerBasicBo, diffList);

        // 客户类型切换处理
        customerRealTypeSwitch(wmCustomerDB, wmCustomerBasicBo, notAuditByCustomerRealType);
        logger.info("updateCustomer,hitSpecialFlow={},notAuditByCustomerRealType={}", hitSpecialFlow, notAuditByCustomerRealType);

        //如果本次提交无任何修改，或者客户类型变化但是不需要提审，直接修改线上表信息
        if (diffList.isEmpty() || notAuditByCustomerRealType) {
            logger.info("wmCustomerBasicBo={}", JSONObject.toJSONString(wmCustomerBasicBo));

            //信息修改&当前状态为特批驳回-直接修改不提审则重置审核状态
            if (notAuditByCustomerRealType
                    && wmCustomerDB.getAuditStatus() == CustomerAuditStatus.CUSTOMER_SPECIAL_AUDIT_REJECT.getCode()
                    && !diffList.isEmpty()) {
                //特批驳回直接修改到生效状态-平台客户信息
                specialReject2Effective(wmCustomerDB, wmCustomerBasicBo);
                //特批驳回拒绝更新，先将审核驳回记录修改为无效，继续下一个流程
                wmCustomerAuditService.updateCustomerAudit2InValid(wmCustomerDB.getId());
            }

            WmCustomerDB newWmCustomerDB = WmCustomerTransUtil.customerBoToDB(wmCustomerBasicBo);
            logger.info("updateCustomer newWmCustomerDB={}", JSONObject.toJSONString(newWmCustomerDB));

            wmCustomerService.updateCustomer(newWmCustomerDB, opUid);
            if (notAuditByCustomerRealType || checkCustomerBizCodeUnValid(wmCustomerDB)) {
                logger.info("updateCustomer updateBizOrgCode wmCustomerBasicBo={}", JSONObject.toJSONString(newWmCustomerDB));
                mtCustomerThriftServiceAdapter.saveMtCustomerBizOrgCode(newWmCustomerDB.getMtCustomerId(), newWmCustomerDB.getBizOrgCode());
            }
            if (isUploadImg) {
                logger.info("updateCustomer uploadImg wmCustomerBasicBo={}", JSONObject.toJSONString(newWmCustomerDB));
                mtCustomerThriftServiceAdapter.updateQualification(newWmCustomerDB, true);
                wmCustomerBasicBo.setPicUrl(newWmCustomerDB.getPicUrl());
            }
            //删除客户与门店的美食城标签
            deleteCustomerAndPoiMscLabel(wmCustomerDB, newWmCustomerDB, opUid, opName);
            //删除客户场景标签
            deleteCustomerSceneTag(wmCustomerDB, wmCustomerBasicBo);
            //插入修改日志
            insertCustomerOpLog(wmCustomerDB.getId(), opUid, opName, wmCustomerBasicBo, wmCustomerDB, WmCustomerOplogBo.OpType.UPDATE);
            //如果审核拒绝&&生效，点击保存无修改，要消除页面展现diff
            if (checkEffectAuditRejectCustomer(wmCustomerDB)) {
                wmCustomerDB.setAuditStatus(CustomerAuditStatus.CUSTOMER_AUDIT_PASS.getCode());
                wmCustomerService.updateCustomerAuditStatus(wmCustomerDB);
            }
            validateResultBo.setCode(CustomerConstants.RESULT_CODE_PASS);
            //只有未生效且待提审状态的客户,不进行任何修改点击保存，才会提示已经提交审核
            if (checkUnEffectiveAuditNew2Audit(wmCustomerDB, opUid, opName)) {
                validateResultBo.setMsg("保存成功,已提交审核");
            } else {
                validateResultBo.setMsg("保存成功");
            }
            return isUpdateNotAudit;
        }
        //未生效过的客户修改，需要更新正式表
        if (wmCustomerDB.isUnEffectived()) {
            //命中特批流程
            if (hitSpecialFlow) {
                logger.info("未生效客户继续执行特批流程,customerId={}", wmCustomerDB.getId());
                //特批驳回拒绝更新，先将审核驳回记录修改为无效，继续下一个流程
                wmCustomerAuditService.updateCustomerAudit2InValid(wmCustomerDB.getId());
                wmCustomerBasicBo.setAuditStatus(CustomerAuditStatus.CUSTOMER_SPECIAL_AUDITING.getCode());
                //发起特批&更新状态为特批中
                specialApprovalService.commitSpecialApproval(wmCustomerBasicBo, opUid, opName);
                //客户信息修改操作记录
                addCustomerChangeOpLog(wmCustomerDB, wmCustomerBasicBo, opUid, opName);
            }

            //未命中特批流程&&特批驳回->待提审
            if (!hitSpecialFlow
                    && wmCustomerDB.getAuditStatus() == CustomerAuditStatus.CUSTOMER_SPECIAL_AUDIT_REJECT.getCode()) {
                logger.info("未生效特批驳回客户执行待提审流程,customerId={}", wmCustomerDB.getId());
                //未生效客户审核状态更新为待提审
                wmCustomerBasicBo.setAuditStatus(CustomerAuditStatus.CUSTOMER_AUDIT_NEW.getCode());
                //将客户状态更新为待提审
                wmCustomerService.updateAuditStatusByCustomerId(wmCustomerDB.getId(), CustomerAuditStatus.CUSTOMER_AUDIT_NEW.getCode());
                addCustomerAuditStatusChangeLog(wmCustomerDB.getId(), opUid, opName, String.format("审核状态修改：特批驳回 => 待提审"));
            }
            //同步客户平台&更新本地客户信息
            WmCustomerDB updateCustomerDB = updateMtCustomerAndWmCustomer(wmCustomerBasicBo, wmCustomerDB, opUid);

            //删除客户场景标签
            deleteCustomerSceneTag(wmCustomerDB, wmCustomerBasicBo);

            //美食城客户无资质且未录入视频或档口数，则直接保存信息不提审
            if (!checkMscNotAudit(wmCustomerDB, wmCustomerBasicBo, opUid)) {
                validateResultBo.setMsg("保存成功,客户状态未更新");
                insertCustomerOpLog(wmCustomerDB.getId(), opUid, opName, wmCustomerBasicBo, wmCustomerDB, WmCustomerOplogBo.OpType.UPDATE);
                return isUpdateNotAudit;
            }
            //当前状态为特批中，则直接返回保存成功
            if (checkUpdateAndSpecialApproval(wmCustomerBasicBo)) {
                validateResultBo.setMsg("保存成功");
                return isUpdateNotAudit;
            }

            //只有未生效&&待审核的数据不会提审，需要和门店资质一块提审
            if (wmCustomerDB.getAuditStatus() == CustomerAuditStatus.CUSTOMER_AUDIT_NEW.getCode()
                    || checkUpdateAndAudiNew(updateCustomerDB)) {
                logger.info("客户未提审,需要等待资质统一提审,客户ID={}", wmCustomerBasicBo.getId());
                if (checkPoiQuaForCustomerAudit(wmCustomerDB.getId(), opUid, opName)) {
                    validateResultBo.setMsg("保存成功,已提交审核");
                } else {
                    validateResultBo.setMsg("保存成功,将与门店资质一同提审");
                }
                insertCustomerOpLog(wmCustomerDB.getId(), opUid, opName, wmCustomerBasicBo, wmCustomerDB, WmCustomerOplogBo.OpType.UPDATE);
            }
        }
        // 已经生效过或者当前是驳回状态需要提审或者当前客户类型属于直接提审类型
        if (checkEffectiveOrUnEffective2Audit(wmCustomerDB, wmCustomerBasicBo)) {
            logger.info("客户生效或审核驳回或修改信息,需要单独提审,客户ID={}", wmCustomerBasicBo.getId());

            //审核驳回无资质共用标不录入视频和档口数不允许提审
            if (!checkMscNotAudit(wmCustomerDB, wmCustomerBasicBo, opUid)) {
                validateResultBo.setMsg("保存成功,客户状态未更新");
                return isUpdateNotAudit;
            }
            //生效&&审核驳回数据修改会单独提审
            //审核表主键为审核业务ID，作为定位到客户审核每条记录的唯一标识
            wmCustomerAuditDBMapper.invalidCustomerAudit(wmCustomerBasicBo.getId());
            wmCustomerBasicBo.setPicUrl(auditPicUrl);
            WmCustomerDB wmCustomerDBNew = WmCustomerTransUtil.customerBoToDB(wmCustomerBasicBo);
            if (isUploadImg) {
                logger.info("updateCustomer uploadImg wmCustomerBasicBo={}", JSONObject.toJSONString(wmCustomerBasicBo));
                mtCustomerThriftServiceAdapter.getAttachmentAndUploadImg(wmCustomerDBNew);
                wmCustomerBasicBo.setPicUrl(wmCustomerDBNew.getPicUrl());
            }

            //修改的时候更新平台客户业务线字段
            updateCustomerBizType(wmCustomerDB);

            Integer bizId = insertCustomerAudit(wmCustomerBasicBo, wmCustomerDB.getEffective(), wmCustomerDB.getOwnerUid(), CustomerConstants.BATCH_SUBMIT_NO, opUid, opName);
            wmCustomerDBNew.setAuditStatus(wmCustomerDB.getAuditStatus());
            commitAudit(wmCustomerDBNew, bizId, opUid, opName, true);
            validateResultBo.setMsg(CustomerConstants.SUBMIT_AUDIT_SUCCESS_MSG);
            //组织日志之前处理资质图片url（外卖客户系统资质图片url格式转换为客户平台资质如片url格式）
            WmCustomerBasicBo wmCustomerBasicBoForLog = new WmCustomerBasicBo();
            BeanUtils.copyProperties(wmCustomerBasicBo, wmCustomerBasicBoForLog);
            if (wmCustomerBasicBo.getPicUrl().contains(WmCustomerConstant.URL_TO_CUSTOMER_PREFIX)) {
                String mtCustomerUrl = mtCustomerThriftServiceAdapter.transferMtCustomerPicUrl(wmCustomerBasicBo.getPicUrl());
                wmCustomerBasicBoForLog.setPicUrl(mtCustomerUrl);
            }
            //插入修改日志
            insertCustomerOpLog(wmCustomerDB.getId(), opUid, opName, wmCustomerBasicBoForLog, wmCustomerDB, WmCustomerOplogBo.OpType.UPDATE);
            //插入提审日志
            insertCustomerOpLog(wmCustomerDB.getId(), opUid, opName, WmCustomerOplogBo.OpType.CHANGESTATUS, CustomerConstants.CUSTOMER_LOG_TEMPLATE_COMMIT_AUDIT);
        }
        return isUpdateNotAudit;
    }

    /**
     * 修改后为特批审核中
     *
     * @param wmCustomerBasicBo
     * @return
     */
    private boolean checkUpdateAndSpecialApproval(WmCustomerBasicBo wmCustomerBasicBo) {
        if (wmCustomerBasicBo.getAuditStatus() != null
                && wmCustomerBasicBo.getAuditStatus() == CustomerAuditStatus.CUSTOMER_SPECIAL_AUDITING.getCode()) {
            return true;
        }
        return false;
    }

    /**
     * 修改后的状态为待提审
     *
     * @param updateCustomerDB
     * @return
     */
    private boolean checkUpdateAndAudiNew(WmCustomerDB updateCustomerDB) {
        if (updateCustomerDB.getAuditStatus() != null
                && updateCustomerDB.getAuditStatus() == CustomerAuditStatus.CUSTOMER_AUDIT_NEW.getCode()) {
            return true;
        }
        return false;
    }

    /**
     * 添加客户信息修改操作记录
     *
     * @param oldWmCustomerDB
     * @param wmCustomerBasicBo
     * @param opUid
     * @param opName
     */
    private void addCustomerChangeOpLog(WmCustomerDB oldWmCustomerDB, WmCustomerBasicBo wmCustomerBasicBo, Integer opUid, String opName) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.service.customer.Impl.WmLeafCustomerServiceImpl.addCustomerChangeOpLog(WmCustomerDB,WmCustomerBasicBo,Integer,String)");
        Integer customerId = oldWmCustomerDB.getId();
        try {
            insertCustomerOpLog(oldWmCustomerDB.getId(), opUid, opName, wmCustomerBasicBo, oldWmCustomerDB, WmCustomerOplogBo.OpType.UPDATE);
        } catch (Exception e) {
            logger.error("addCustomerChangeOpLog,添加客户信息修改操作记录发生异常,customerId = {}", customerId, e);
        }
    }

    /**
     * 新增审核状态变更操作记录
     *
     * @param customerId
     * @param opUid
     * @param opName
     * @param opLog
     */
    private void addCustomerAuditStatusChangeLog(Integer customerId, Integer opUid, String opName, String opLog) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.service.customer.Impl.WmLeafCustomerServiceImpl.addCustomerAuditStatusChangeLog(java.lang.Integer,java.lang.Integer,java.lang.String,java.lang.String)");
        try {
            //添加操作记录
            insertCustomerOpLog(customerId, opUid,
                    opName, WmCustomerOplogBo.OpType.CHANGESTATUS, opLog);
        } catch (Exception e) {
            logger.error("addCustomerAuditStatusChangeLog,新增特批审核结果操作记录发生异常,customerId={},opLog={}", customerId, opLog, e);
        }
    }


    /**
     * 删除客户场景标签
     *
     * @param wmCustomerDB
     * @param wmCustomerBasicBo
     */
    private void deleteCustomerSceneTag(WmCustomerDB wmCustomerDB, WmCustomerBasicBo wmCustomerBasicBo) {
        //修改前客户类型非外卖单店或个人资质则直接返回
        if (wmCustomerDB.getCustomerRealType() != CustomerRealTypeEnum.DANDIAN.getValue()
                || wmCustomerDB.getCustomerType() != CustomerType.CUSTOMER_TYPE_IDCARD.getCode()) {
            return;
        }
        //客户类型与资质类型都不变
        if (wmCustomerDB.getCustomerRealType() == wmCustomerBasicBo.getCustomerRealType()
                && wmCustomerDB.getCustomerType() == wmCustomerBasicBo.getCustomerType()) {
            return;
        }

        wmCustomerLabelService.deleteWmSingleCustomerSceneTag(wmCustomerDB.getMtCustomerId(), 0, "系统");
    }



    /**
     * 校验 生效或者未生效需要提审的场景
     *
     * @param wmCustomerDB
     * @param wmCustomerBasicBo
     * @return
     */
    private boolean checkEffectiveOrUnEffective2Audit(WmCustomerDB wmCustomerDB, WmCustomerBasicBo wmCustomerBasicBo) {
        return wmCustomerDB.isEffectived() || wmCustomerDB.getAuditStatus() == CustomerAuditStatus.CUSTOMER_AUDIT_REJECT.getCode()
                || wmCustomerService.isCommitAuditWhenUpdate(wmCustomerBasicBo.getIsLeaf(), wmCustomerBasicBo.getCustomerRealType());
    }


    /**
     * 校验未生效待提审客户是否流转到提审
     *
     * @param wmCustomerDB
     * @param opUid
     * @param opName
     * @return
     * @throws WmCustomerException
     * @throws TException
     */
    private boolean checkUnEffectiveAuditNew2Audit(WmCustomerDB wmCustomerDB, Integer opUid, String opName) throws WmCustomerException, TException {
        return wmCustomerDB.isUnEffectived() && wmCustomerDB.getAuditStatus() == CustomerAuditStatus.CUSTOMER_AUDIT_NEW.getCode()
                && checkPoiQuaForCustomerAudit(wmCustomerDB.getId(), opUid, opName);

    }

    /**
     * 特批驳回修改至生效
     *
     * @param wmCustomerDB
     * @param wmCustomerBasicBo
     * @throws WmCustomerException
     */
    private void specialReject2Effective(WmCustomerDB wmCustomerDB, WmCustomerBasicBo wmCustomerBasicBo) throws WmCustomerException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.service.customer.Impl.WmLeafCustomerServiceImpl.specialReject2Effective(WmCustomerDB,WmCustomerBasicBo)");
        try {
            Integer channel = wmCustomerBasicBo.getCustomerSource() == null ? CustomerSource.UNKNOWN.getCode() : wmCustomerBasicBo.getCustomerSource().getCode();
            boolean isUploadImg = (channel == CustomerSource.WAIMAI_BD.getCode() && !wmCustomerBasicBo.getPicUrl().contains(URL_HTTP_CONSTANT)) ? false : true;
            //更新到美团客户平台
            WmCustomerDB mtCustomerForUpdate = WmCustomerTransUtil.customerBoToDB(wmCustomerBasicBo);
            //前端没有带入mtCustomerId，所以需要设置
            mtCustomerForUpdate.setMtCustomerId(wmCustomerDB.getMtCustomerId());
            //只有存在mtCustomerId的才进行更新
            mtCustomerForUpdate.setEffective(CustomerConstants.EFFECT);

            updateMtCustomer(mtCustomerForUpdate, isUploadImg);
            wmCustomerBasicBo.setAuditStatus(CustomerAuditStatus.CUSTOMER_AUDIT_PASS.getCode());
            wmCustomerBasicBo.setEffective(CustomerConstants.EFFECT);
            //特批驳回拒绝更新，先将审核驳回记录修改为无效，继续下一个流程
            wmCustomerAuditService.updateCustomerAudit2InValid(wmCustomerDB.getId());
        } catch (Exception e) {
            logger.error("specialReject2Effective,特批驳回修改直接到生效发生异常,customerId={},wmCustomerBasicBo={}", wmCustomerDB.getId(), JSON.toJSONString(wmCustomerBasicBo), e);
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "系统异常");
        }
    }

    /**
     * 同步平台客户信息并更新本地客户信息
     *
     * @param wmCustomerBasicBo
     * @throws WmCustomerException
     */
    private WmCustomerDB updateMtCustomerAndWmCustomer(WmCustomerBasicBo wmCustomerBasicBo, WmCustomerDB oldWmCustomerDB, Integer opUid) throws WmCustomerException {
        //定义渠道
        Integer channel = wmCustomerBasicBo.getCustomerSource() == null ? CustomerSource.UNKNOWN.getCode() : wmCustomerBasicBo.getCustomerSource().getCode();
        boolean isUploadImg = (channel == CustomerSource.WAIMAI_BD.getCode() && !wmCustomerBasicBo.getPicUrl().contains(URL_HTTP_CONSTANT)) ? false : true;

        //更新到美团客户平台
        WmCustomerDB mtCustomerForUpdate = WmCustomerTransUtil.customerBoToDB(wmCustomerBasicBo);
        //前端没有带入mtCustomerId，所以需要设置
        mtCustomerForUpdate.setMtCustomerId(oldWmCustomerDB.getMtCustomerId());
        //只有存在mtCustomerId的才进行更新
        mtCustomerForUpdate.setEffective(oldWmCustomerDB.getEffective());
        try {
            long mtCustomerId = updateMtCustomer(mtCustomerForUpdate, isUploadImg);
            wmCustomerBasicBo.setMtCustomerId(mtCustomerId);
            if (checkCustomerBizCodeUnValid(oldWmCustomerDB)) {
                logger.info("updateCustomer updateBizOrgCode wmCustomerBasicBo={}", JSONObject.toJSONString(mtCustomerForUpdate));
                mtCustomerThriftServiceAdapter.saveMtCustomerBizOrgCode(mtCustomerForUpdate.getMtCustomerId(), mtCustomerForUpdate.getBizOrgCode());
            }
        } catch (Exception e) {
            logger.error("美团客户平台更新客户信息失败, wmCustomerId={}，e={}", oldWmCustomerDB.getId(), e);
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "客户平台更新异常");
        }
        logger.info("平台更新客户信息执行，客户ID:{}", wmCustomerBasicBo.getMtCustomerId());
        if (oldWmCustomerDB.getBizOrgCode().intValue() != wmCustomerBasicBo.getBizOrgCode().intValue()) {
            logger.info("updateCustomer updateBizOrgCode wmCustomerBasicBo={}", JSONObject.toJSONString(wmCustomerBasicBo));
            mtCustomerThriftServiceAdapter.saveMtCustomerBizOrgCode(wmCustomerBasicBo.getMtCustomerId(), wmCustomerBasicBo.getBizOrgCode());
        }
        // 更新本地
        wmCustomerService.updateCustomer(mtCustomerForUpdate, opUid);
        return mtCustomerForUpdate;
    }

    /**
     * 是否命中外卖单店个人资质修改场景灰度
     *
     * @param opUid
     * @param wmCustomerBasicBo
     * @return
     */
    private boolean hitSinglePerCertifyFlow(Integer opUid, WmCustomerBasicBo wmCustomerBasicBo, Integer oldAuditStatus) throws WmCustomerException {
        boolean hitSinglePerCertifyFlow = false;
        if (wmCustomerBasicBo.getCustomerSource() != null
                && wmCustomerBasicBo.getCustomerSource() == CustomerSource.WAIMAI_BD
                && wmCustomerGrayService.hitWmSinglePerCertifyGray(opUid, wmCustomerBasicBo)
                && wmCustomerBasicBo.getCustomerRealType() == CustomerRealTypeEnum.DANDIAN.getValue()
                && wmCustomerBasicBo.getCustomerType() == CustomerType.CUSTOMER_TYPE_IDCARD.getCode()
                && oldAuditStatus == CustomerAuditStatus.CUSTOMER_SPECIAL_AUDIT_REJECT.getCode()
                && !upmAuthCheckService.checkUpmRoleByIdAndUserId(opUid, MccCustomerConfig.getPersonCertifyAdminRoleId())) {
            hitSinglePerCertifyFlow = true;
        }
        return hitSinglePerCertifyFlow;
    }

    /**
     * 美食城客户校验(不可有进行中的切换任务&客户下门店不可在预绑定流程中)
     * @param newCustomerInfo
     * @param oldCustomerInfo
     */
    private void checkMSCCustomerModify(WmCustomerBasicBo newCustomerInfo, WmCustomerDB oldCustomerInfo) throws WmCustomerException {
        if(newCustomerInfo == null || oldCustomerInfo == null){
            return;
        }
        int newCustomerRealType = newCustomerInfo.getCustomerRealType();
        int oldCustomerRealType = oldCustomerInfo.getCustomerRealType();
        //美食城->非美食城
        if(oldCustomerRealType == CustomerRealTypeEnum.MEISHICHENG.getValue() && newCustomerRealType != CustomerRealTypeEnum.MEISHICHENG.getValue()){
            if(wmPoiSwitchServiceAdapter.wrapToHasCustomerSwitchingPoi(newCustomerInfo.getId())){
                throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "该客户"+newCustomerInfo.getMtCustomerId()+"有作为目标客户进行中的切换客户任务");
            }
            List<WmCustomerPoiDB> preBindPoiByCustomerId = wmCustomerPoiDBMapper.getPreBindPoiByCustomerId(newCustomerInfo.getId(), Lists.newArrayList(CustomerRelationStatusEnum.CONFIRM_BINDING_FAIL.getCode(), CustomerRelationStatusEnum.CONFIRM_BINDING.getCode(), CustomerRelationStatusEnum.TO_APPLY_BIND.getCode()));
            if(CollectionUtils.isNotEmpty(preBindPoiByCustomerId)){
                throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "该客户下有[确认绑定中]、[确认绑定失败]的门店");
            }
        }
        //非美食城->美食城
        else if(oldCustomerRealType != CustomerRealTypeEnum.MEISHICHENG.getValue() && newCustomerRealType == CustomerRealTypeEnum.MEISHICHENG.getValue()){
            if(wmPoiSwitchServiceAdapter.wrapToHasCustomerSwitchingPoi(newCustomerInfo.getId())){
                throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "该客户"+newCustomerInfo.getMtCustomerId()+"有作为目标客户进行中的切换客户任务");
            }
        }
    }

    private void customerRealTypeSwitch(WmCustomerDB wmCustomerDB, WmCustomerBasicBo wmCustomerBasicBo, boolean notAuditByCustomerRealType) throws WmCustomerException, TException {
        boolean customerRealTypeSwitch = WmCustomerUtil.checkSwitch(wmCustomerDB.getCustomerRealType(), wmCustomerBasicBo.getCustomerRealType());
        if (customerRealTypeSwitch && !MccCustomerConfig.getCheckRuleSwitch()) {
            logger.info("客户类型在医药和非医药之间切换 ：customer_id = {}", wmCustomerDB.getId());
            List<Long> wmPoiIds = wmCustomerPoiService.selectWmPoiIdsByCustomerIdFromDBAndSwitchCentre(wmCustomerDB.getId());
            if (CollectionUtils.isNotEmpty(wmPoiIds)) {
                throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "客户类型在医药与非医药间切换，需要解绑客户下门店");
            }
        }
        //如果只修改客户类型，并且客户类型是在医药和非医药切换的场景，合同取消签约
        if (notAuditByCustomerRealType && customerRealTypeSwitch) {
            //切换逻辑
            wmContractService.invalidContractAndCancleSign(wmCustomerDB.getId(), -99, "系统");
        }
    }


    /**
     * 修改客户数据并直接生效
     *
     * @param wmCustomerBasicBo
     * @param wmCustomerDB
     * @param opUid
     * @param opName
     * @throws WmCustomerException
     * @throws TException
     */
    private void updateCustomerCertificateType(WmCustomerBasicBo wmCustomerBasicBo, WmCustomerDB wmCustomerDB, Integer opUid, String opName, boolean isUploadImg) throws WmCustomerException, TException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_1", "com.sankuai.meituan.waimai.customer.service.customer.Impl.WmLeafCustomerServiceImpl.updateCustomerCertificateType(WmCustomerBasicBo,WmCustomerDB,Integer,String,boolean)");
        //更新到美团客户平台
        WmCustomerDB mtCustomerForUpdate = WmCustomerTransUtil.customerBoToDB(wmCustomerBasicBo);
        //前端没有带入mtCustomerId，所以需要设置
        mtCustomerForUpdate.setMtCustomerId(wmCustomerDB.getMtCustomerId());
        //只有存在mtCustomerId的才进行更新
        //如果营业执照形式为纸质形式按照原来的逻辑，如果是电子形式则直接生效
        wmCustomerBasicBo.setAuditStatus(CustomerAuditStatus.CUSTOMER_AUDIT_PASS.getCode());
        wmCustomerBasicBo.setEffective(CustomerConstants.EFFECT);
        try {
            mtCustomerForUpdate.setAuditStatus(CustomerAuditStatus.CUSTOMER_AUDIT_PASS.getCode());
            mtCustomerForUpdate.setEffective(CustomerConstants.EFFECT);
            long mtCustomerId = updateMtCustomer(mtCustomerForUpdate, isUploadImg);
            wmCustomerBasicBo.setMtCustomerId(mtCustomerId);
            // 资质地址有修改（http->s3）
            if (isUploadImg) {
                logger.info("updateCustomerCertificateType uploadImg mtCustomerForUpdate={}", JSONObject.toJSONString(mtCustomerForUpdate));
                wmCustomerBasicBo.setPicUrl(mtCustomerForUpdate.getPicUrl());
            }
        } catch (Exception e) {
            logger.error("美团客户平台更新客户信息失败, wmCustomerId={}，e={}", wmCustomerDB.getId(), e);
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "客户平台更新异常");
        }
        logger.info("平台更新客户信息执行，客户ID:{}", wmCustomerBasicBo.getMtCustomerId());
        // 更新本地
        wmCustomerService.updateCustomer(WmCustomerTransUtil.customerBoToDB(wmCustomerBasicBo), opUid);
        if (wmCustomerDB.getBizOrgCode().intValue() != wmCustomerBasicBo.getBizOrgCode().intValue()) {
            logger.info("updateCustomer updateBizOrgCode wmCustomerBasicBo={}", JSONObject.toJSONString(wmCustomerBasicBo));
            mtCustomerThriftServiceAdapter.saveMtCustomerBizOrgCode(wmCustomerBasicBo.getMtCustomerId(), wmCustomerBasicBo.getBizOrgCode());
        }
        //插入修改日志
        insertCustomerOpLog(wmCustomerDB.getId(), opUid, opName, wmCustomerBasicBo, wmCustomerDB, WmCustomerOplogBo.OpType.UPDATE);
    }

    @Override
    protected void initAuditStatus(WmCustomerDB wmCustomerDB, Integer opUid, WmCustomerBasicBo wmCustomerBasicBo) throws WmCustomerException {
        if (wmCustomerDB.getCertificateType() == CertificateTypeEnum.PAPER.getType()) {
            //外卖单店&个人证件&无个人资质管理员角色，重置审核状态->待发起特批
            if (hitSinglePerCertifyInsertGray(opUid, wmCustomerDB, wmCustomerBasicBo)) {
                wmCustomerDB.setAuditStatus(CustomerAuditStatus.CUSTOMER_TO_APPLY_SPECIAL_AUDIT.getCode());
            } else {
                wmCustomerDB.setAuditStatus(CustomerAuditStatus.CUSTOMER_AUDIT_NEW.getCode());
            }
            wmCustomerDB.setEffective(CustomerConstants.UNEFFECT);
        } else if (wmCustomerDB.getCertificateType() == CertificateTypeEnum.ELECTRONIC.getType()) {
            //美食城客户仍然需要提审
            if (wmCustomerGrayService.isGrayForNewMscInitStatus(wmCustomerDB)) {
                wmCustomerDB.setAuditStatus(CustomerAuditStatus.CUSTOMER_AUDIT_NEW.getCode());
                wmCustomerDB.setEffective(CustomerConstants.UNEFFECT);
            } else {
                wmCustomerDB.setAuditStatus(CustomerAuditStatus.CUSTOMER_AUDIT_PASS.getCode());
                wmCustomerDB.setEffective(CustomerConstants.EFFECT);
            }
        }
    }

    @Override
    public void deleteCustomer(Integer customerId, Integer opUid, String opName) throws WmCustomerException, TException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.service.customer.Impl.WmLeafCustomerServiceImpl.deleteCustomer(java.lang.Integer,java.lang.Integer,java.lang.String)");
        List<Long> wmPoiIds = WmCustomerPoiAggre.Factory.make().selectWmPoiIdsByCustomerId(customerId);
        if (wmPoiIds != null && wmPoiIds.size() > MccConfig.getCustomerDeletePoiBatchNum()) {
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "客户所关联的门店大于" + MccConfig.getCustomerDeletePoiBatchNum() + ",请先解绑关联门店");
        }

        // 校验是否有有效父客户
        WmCustomerDB wmCustomerDB = wmCustomerPlatformDataParseService.selectCustomerFromPlatformById(customerId);
        if (wmCustomerDB != null && wmCustomerDB.getSuperCustomerId() > 0) {
            WmCustomerDB parent = wmCustomerPlatformDataParseService.selectCustomerFromPlatformById((int) wmCustomerDB.getSuperCustomerId());
            if (parent != null && parent.getValid() != null && parent.getValid().intValue() == CustomerConstants.VALID) {
                throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "当前客户存在有效的与其他客户的关联关系，不能删除");
            }
        }

//        WmCustomerDB wmCustomerDB = wmCustomerDBMapper.selectCustomerById(customerId);
//        //同步删除客户平台中客户信息
//        mtCustomerThriftServiceAdapter.deleteCustomerByMtCustomerId(wmCustomerDB.getMtCustomerId());
//        wmCustomerDBMapper.deleteCustomer(customerId);
//        wmCustomerPoiService.unbindCustomerPoi(Sets.newHashSet(wmPoiIds), customerId, opUid, opName, WmCustomerOplogBo.OpType.DELETE, CustomerConstants.CUSTOMER_LOG_TEMPLATE_DELETE_CUSTOMER, CustomerMQEventEnum.CUSTOMER_DELETE);

        // parseDateService适配
        wmCustomerPlatformDataParseService.deleteCustomerForLeaf(customerId, opUid, opName);

        WmCustomerOplogBo wmCustomerOplogBo = new WmCustomerOplogBo(customerId, WmCustomerOplogBo.OpModuleType.CUSTOMER, customerId);
        wmCustomerOplogBo.setOpType(WmCustomerOplogBo.OpType.DELETE.type);
        wmCustomerOplogBo.setLog(CustomerConstants.CUSTOMER_LOG_TEMPLATE_DELETE_CUSTOMER);
        wmCustomerOplogBo.setOpUid(opUid);
        wmCustomerOplogBo.setOpUname(opName);
        wmCustomerOplogBo.setRemark("");
        wmCustomerOplogService.insert(wmCustomerOplogBo);

        //客户删除需要掉个人资质场景标签-外卖单店个人资质
        if (wmCustomerDB.getCustomerRealType() == CustomerRealTypeEnum.DANDIAN.getValue()
                && wmCustomerDB.getCustomerType() == CustomerType.CUSTOMER_TYPE_IDCARD.getCode()
                && StringUtils.isNotEmpty(wmCustomerDB.getSceneInfo())) {
            //删除客户场景标签
            wmCustomerLabelService.deleteWmSingleCustomerSceneTag(wmCustomerDB.getMtCustomerId(), opUid, opName);
        }
    }

    @Override
    public long syncMtCustomerQua(long mtCustomerId) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.service.customer.Impl.WmLeafCustomerServiceImpl.syncMtCustomerQua(long)");
        return wmCustomerPlatformDataParseService.syncMtCustomerQua(mtCustomerId);
    }


    /**
     * 校验是否可以修改客户信息
     *
     * @param wmCustomerDB
     * @param wmCustomerBasicBo
     * @throws WmCustomerException
     */
    private void checkUpdateCustomer(WmCustomerDB wmCustomerDB, WmCustomerBasicBo wmCustomerBasicBo)
            throws WmCustomerException {
        if (wmCustomerDB == null) {
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "客户ID无法查到有效数据");
        }

        if (wmCustomerDB.getIsLeaf() != wmCustomerBasicBo.getIsLeaf()) {
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "上级客户类型不允许切换为叶子客户");
        }

        if (wmCustomerDB.getAuditStatus() == CustomerAuditStatus.CUSTOMER_AUDIT_AUDITING.getCode()) {
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "正在审核,无法修改");
        }
    }

    /**
     * 修改场景校验美食城客户保存后是否提审
     * 未生效过且命中灰度+无资质共用标签+无档口数和视频=》不允许提审
     *
     * @param opUid
     * @param wmCustomerBasicBo
     */
    private boolean checkMscNotAudit(WmCustomerDB wmCustomerDB, WmCustomerBasicBo wmCustomerBasicBo, Integer opUid) {
        Long mtCustomerId = wmCustomerBasicBo.getMtCustomerId();
        Integer wmCustomerId = wmCustomerBasicBo.getId();
        try {
            //审核状态只关注 待提审/审核拒绝
            if (wmCustomerDB.getAuditStatus() != CustomerAuditStatus.CUSTOMER_AUDIT_NEW.getCode()
                    && wmCustomerDB.getAuditStatus() != CustomerAuditStatus.CUSTOMER_AUDIT_REJECT.getCode()) {
                return true;
            }
            //美食城客户+命中灰度+无资质共用标+美食城视频或档口数为空
            if (wmCustomerBasicBo.getCustomerRealType() == CustomerRealTypeEnum.MEISHICHENG.getValue()
                    && !wmCustomerMSCLabelService.checkHasQuaComCustomerLabel(mtCustomerId)
                    && !checkMscVideoAndPoiCnt(wmCustomerId, wmCustomerBasicBo.getCustomerRealTypeSpInfoBo())) {
                logger.info("checkUnEffMscNotAudit,未生效的美食城客户无资质共用标，且美食城视频或档口数不符合规范，不继续提审,customerId={}", wmCustomerId);
                return false;
            }
        } catch (Exception e) {
            logger.error("checkMscNotAudit,校验美食城客户未生效保存后是否直接提审发生异常,customerId={}", wmCustomerId);
        }
        return true;
    }

    /**
     * 校验美食城视频和档口数
     *
     * @param customerId
     * @param customerRealTypeSpInfoBo
     * @return
     */
    private boolean checkMscVideoAndPoiCnt(Integer customerId, CustomerRealTypeSpInfoBo customerRealTypeSpInfoBo) {
        if (customerRealTypeSpInfoBo == null
                || StringUtils.isBlank(customerRealTypeSpInfoBo.getFoodCityVideo())
                || customerRealTypeSpInfoBo.getFoodCityPoiCount() == null
                || customerRealTypeSpInfoBo.getFoodCityPoiCount() <= 0) {
            logger.info("checkMscVideoAndPoiCnt,美食城客户未录入合法视频或档口数信息，校验未通过,customerId={}", customerId);
            return false;
        }
        return true;
    }

    /**
     * 修改设置业务线字段
     *
     * @param wmCustomerDB
     */
    private void updateCustomerBizType(WmCustomerDB wmCustomerDB) {
        if (wmCustomerDB.getBizOrgCode() == null || wmCustomerDB.getBizOrgCode().intValue() == 0) {
            logger.info("updateCustomer updateBizOrgCode wmCustomerDB={}", JSONObject.toJSONString(wmCustomerDB));
            CustomerRealTypeEnum customerRealTypeEnum = CustomerRealTypeEnum.getByValue(wmCustomerDB.getCustomerRealType());
            if (customerRealTypeEnum != null) {
                mtCustomerThriftServiceAdapter.saveMtCustomerBizOrgCode(wmCustomerDB.getMtCustomerId(), customerRealTypeEnum.getBizOrgCode());
            }
        }
    }

    /**
     * 客户证件为电子且非美食城客户
     *
     * @param wmCustomerBasicBo
     * @return
     */
    private boolean checkCertifyTypeElectAndNotMsc(WmCustomerBasicBo wmCustomerBasicBo) {
        return (wmCustomerBasicBo.getCertificateType() == CertificateTypeEnum.ELECTRONIC.getType() &&
                (wmCustomerBasicBo.getCustomerRealType() != CustomerRealTypeEnum.MEISHICHENG.getValue() || !wmCustomerGrayService.isGrayForNewMsc(wmCustomerBasicBo)));
    }

    /**
     * 获取客户修改前后的diff信息
     *
     * @param wmCustomerBasicBo
     * @param wmCustomerDB
     * @return
     */
    private List<WmCustomerDiffCellBo> getCustomerDiff(WmCustomerBasicBo wmCustomerBasicBo, WmCustomerDB wmCustomerDB) throws WmCustomerException {
        Map<String, String> compareMap = CustomerConstants.customerCompare;
        if (wmCustomerBasicBo.getCustomerType() == CustomerType.CUSTOMER_TYPE_IDCARD.getCode() &&
                wmCustomerDB.getCustomerType() != null && wmCustomerDB.getCustomerType() == CustomerType.CUSTOMER_TYPE_IDCARD.getCode()) {
            compareMap = CustomerConstants.customerIDCARDCompare;
        }
        List<WmCustomerDiffCellBo> diffList = DiffUtil.compare(wmCustomerBasicBo, wmCustomerDB, compareMap);
        //更新目标为美食城客户时,比对美食城特殊字段
        if (wmCustomerBasicBo.getCustomerRealType() == CustomerRealTypeEnum.MEISHICHENG.getValue()) {
            List<WmCustomerDiffCellBo> customerRealTypeSpInfoDiffList = DiffUtil.compare(genCustomerRealTypeSpInfoBoDiff(wmCustomerDB), genCustomerRealTypeSpInfoBoDiff(wmCustomerBasicBo), CustomerConstants.customerCompareCustomerRealTypeSpInfo);
            diffList.addAll(customerRealTypeSpInfoDiffList);
        }

        //更新成外卖单店个人证件
        if (wmCustomerBasicBo.getCustomerRealType() == CustomerRealTypeEnum.DANDIAN.getValue()
                && wmCustomerBasicBo.getCustomerType() == CustomerType.CUSTOMER_TYPE_IDCARD.getCode()) {
            List<WmCustomerDiffCellBo> customerSceneInfoDiff = DiffUtil.compare(StringUtils.isBlank(wmCustomerDB.getSceneInfo()) ? new CustomerSceneInfoBODiff() : JSON.parseObject(wmCustomerDB.getSceneInfo(), CustomerSceneInfoBODiff.class),
                    wmCustomerBasicBo.getSceneInfoBO() == null ? new CustomerSceneInfoBODiff() : JSON.parseObject(JSON.toJSONString(wmCustomerBasicBo.getSceneInfoBO()), CustomerSceneInfoBODiff.class), CustomerConstants.customerSceneInfoCompare);
            diffList.addAll(customerSceneInfoDiff);
        }
        logger.info("diffList ={}", JSON.toJSONString(diffList));
        return diffList;
    }

    /**
     * 删除客户与门店的美食城标签
     *
     * @param wmCustomerDB
     * @param opUid
     * @param opName
     */
    private void deleteCustomerAndPoiMscLabel(WmCustomerDB wmCustomerDB, WmCustomerDB newCustomerDB, Integer opUid, String opName) {
        //非（客户类型由美食城修改为非美食城）
        if (!(wmCustomerDB.getCustomerRealType() == CustomerRealTypeEnum.MEISHICHENG.getValue() &&
                newCustomerDB.getCustomerRealType() != CustomerRealTypeEnum.MEISHICHENG.getValue())) {
            return;
        }
        //去除客户及门店标签
        WmPoiLabelRel customerLabel = wmPoiFlowlineLabelThriftServiceAdapter.selectLabelRelCache(wmCustomerDB.getMtCustomerId(), MccCustomerConfig.getAuditedMSCCustomerLabel(), LabelSubjectTypeEnum.CUSTOMER.getCode());
        if (customerLabel != null && customerLabel.getId() > 0L) {
            //客户去标
            wmPoiFlowlineLabelThriftServiceAdapter.delCustomerLabelRel(MccCustomerConfig.getAuditedMSCCustomerLabel(), wmCustomerDB.getMtCustomerId(), opUid, opName);
            List<WmCustomerPoiDB> wmCustomerPoiDBList = wmCustomerPoiDBMapper.selectWmCustomerPoiByCustomerId(wmCustomerDB.getId());
            List<Long> wmPoiIdList = wmCustomerPoiDBList.stream().map(x -> x.getWmPoiId()).collect(Collectors.toList());
            //门店去标
            wmPoiFlowlineLabelThriftServiceAdapter.batchDeleteLabelToPois(MccCustomerConfig.getMSCWmPoiLabel(), wmPoiIdList, opUid, opName);
        }
    }

    /**
     * 已生效客户审核拒绝
     *
     * @param wmCustomerDB
     * @return
     */
    private boolean checkEffectAuditRejectCustomer(WmCustomerDB wmCustomerDB) {
        return wmCustomerDB.getAuditStatus() == CustomerAuditStatus.CUSTOMER_AUDIT_REJECT.getCode() && wmCustomerDB.isEffectived();
    }

    /**
     * 校验客户的业务线字段无效值：为空或0
     *
     * @param wmCustomerDB
     * @return
     */
    private boolean checkCustomerBizCodeUnValid(WmCustomerDB wmCustomerDB) {
        return wmCustomerDB.getBizOrgCode() == null || wmCustomerDB.getBizOrgCode().intValue() == 0;
    }

    /**
     * 是否命中外卖单店个人资质新增场景灰度
     *
     * @param opUid
     * @param wmCustomerDB
     * @return
     */
    private boolean hitSinglePerCertifyInsertGray(Integer opUid, WmCustomerDB wmCustomerDB, WmCustomerBasicBo wmCustomerBasicBo) throws WmCustomerException {
        if (wmCustomerBasicBo.getCustomerSource() != null
                && wmCustomerBasicBo.getCustomerSource() == CustomerSource.WAIMAI_BD
                && wmCustomerGrayService.hitWmSinglePerCertifyGray(opUid, wmCustomerBasicBo)
                && wmCustomerDB.getCustomerRealType() == CustomerRealTypeEnum.DANDIAN.getValue()
                && wmCustomerDB.getCustomerType() == CustomerType.CUSTOMER_TYPE_IDCARD.getCode()
                && !upmAuthCheckService.checkUpmRoleByIdAndUserId(opUid, MccCustomerConfig.getPersonCertifyAdminRoleId())) {
            return true;
        }
        return false;
    }


}
