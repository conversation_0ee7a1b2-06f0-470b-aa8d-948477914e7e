package com.sankuai.meituan.waimai.customer.service.customer.poi;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.dianping.cat.Cat;
import com.dianping.cat.util.MetricHelper;
import com.google.common.base.Preconditions;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.meituan.service.mobile.mtthrift.util.ClientInfoUtil;
import com.sankuai.meituan.waimai.channel.beacon.lib.enums.CustomerModuleStateEnum;
import com.sankuai.meituan.waimai.customer.adapter.StateCenterAdapter;
import com.sankuai.meituan.waimai.customer.adapter.WmEmployClient;
import com.sankuai.meituan.waimai.customer.adapter.WmPoiOplogThriftServiceAdaptor;
import com.sankuai.meituan.waimai.customer.config.MccConfig;
import com.sankuai.meituan.waimai.customer.config.MccCustomerConfig;
import com.sankuai.meituan.waimai.customer.constant.WmCustomerConstant;
import com.sankuai.meituan.waimai.customer.constant.customer.*;
import com.sankuai.meituan.waimai.customer.contract.service.impl.WmContractService;
import com.sankuai.meituan.waimai.customer.dao.WmCustomerPoiSmsRecordMapper;
import com.sankuai.meituan.waimai.customer.ddd.adapter.WmPoiQueryAdapter;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerDB;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerPoiDB;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerPoiOplogDB;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerPoiSmsRecordDB;
import com.sankuai.meituan.waimai.customer.domain.poi.*;
import com.sankuai.meituan.waimai.customer.domain.task.CustomerOperateBO;
import com.sankuai.meituan.waimai.customer.domain.task.CustomerPoiAppKeySourceConfig;
import com.sankuai.meituan.waimai.customer.domain.task.WmCustomerTask;
import com.sankuai.meituan.waimai.customer.mq.service.CustomerPoiSendService;
import com.sankuai.meituan.waimai.customer.service.companycustomer.CompanyCustomerSyncService;
import com.sankuai.meituan.waimai.customer.service.customer.*;
import com.sankuai.meituan.waimai.customer.service.customer.check.CustomerPoiBaseValidator;
import com.sankuai.meituan.waimai.customer.service.customer.task.CustomerTaskService;
import com.sankuai.meituan.waimai.customer.service.sign.WmEcontractSignBzService;
import com.sankuai.meituan.waimai.thrift.constants.WmPoiFieldQueryConstant;
import com.sankuai.meituan.waimai.thrift.customer.constant.*;
import com.sankuai.meituan.waimai.thrift.customer.constant.customer.task.*;
import com.sankuai.meituan.waimai.thrift.customer.domain.WmCustomerOplogBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.base.LongResult;
import com.sankuai.meituan.waimai.thrift.customer.domain.mq.CustomerMQEventEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.mq.CustomerPoiReleaseMsg;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractCancelAuthInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractTaskApplyBo;
import com.sankuai.meituan.waimai.thrift.domain.WmPoiAggre;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import com.sankuai.meituan.waimai.util.DaxiangUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;

@Slf4j
@Service
public class CustomerPoiUnBindService {

    private static final int MAX_POI_NAME_NUM = 3;

    // 签约任务取消原因
    public static final String CANCEL_SMS_REASON = "%s流程已强制解绑门店，此签约任务取消";


    @Autowired
    private CustomerPoiBaseValidator customerPoiBaseValidator;

    @Autowired
    private WmCustomerPoiRelService wmCustomerPoiRelService;

    @Autowired
    private WmCustomerPoiAttributeService wmCustomerPoiAttributeService;

    @Autowired
    private CustomerTaskService customerTaskService;

    @Autowired
    private WmCustomerService wmCustomerService;

    @Autowired
    private WmCustomerPoiSmsRecordMapper wmCustomerPoiSmsRecordMapper;

    @Autowired
    private WmPoiQueryAdapter wmPoiQueryAdapter;

    @Autowired
    private WmEcontractSignBzService wmEcontractSignBzService;

    @Autowired
    private WmCustomerMSCLabelService wmCustomerMSCLabelService;

    @Autowired
    private StateCenterAdapter stateCenterAdapter;

    @Autowired
    private WmContractService wmContractService;

    @Autowired
    private WmCustomerPoiOplogService wmCustomerPoiOplogService;

    @Autowired
    private CompanyCustomerSyncService companyCustomerSyncService;

    @Autowired
    private CustomerPoiSendService customerPoiSendService;

    @Autowired
    private WmPoiOplogThriftServiceAdaptor wmPoiOplogThriftServiceAdaptor;

    @Autowired
    private WmCustomerPoiListEsService wmCustomerPoiListEsService;

    @Autowired
    private WmEmployClient wmEmployClient;

    @Autowired
    private WmCustomerPlatformDataParseService wmCustomerPlatformDataParseService;

    /**
     * 标准解绑
     *
     * @param wmCustomerPoiUnBindParamBo
     * @return
     * @throws TException
     * @throws WmCustomerException
     */
    public String unBind(WmCustomerPoiUnBindParamBo wmCustomerPoiUnBindParamBo) throws TException, WmCustomerException {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.service.customer.poi.CustomerPoiUnBindService.unBind(com.sankuai.meituan.waimai.customer.domain.poi.WmCustomerPoiUnBindParamBo)");
        log.info("unBind wmCustomerPoiUnBindParamBo={}", JSONObject.toJSONString(wmCustomerPoiUnBindParamBo));
        // 基础参数校验
        checkUnBindParam(wmCustomerPoiUnBindParamBo);
        // 业务校验
        Map<CustomerPoiUnBindDecideResultEnum, Set<Long>> map = customerPoiBaseValidator.checkUnbind(wmCustomerPoiUnBindParamBo);
        log.info("unBind map={}", JSONObject.toJSONString(map));
        if (CollectionUtils.isEmpty(map)) {
            log.error("unBind error wmCustomerPoiUnBindParamBo={},map={}", JSONObject.toJSONString(wmCustomerPoiUnBindParamBo), JSONObject.toJSONString(map));
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "系统异常");
        }
        String result = CustomerConstants.CUSTOMER_POI_UNBIND_SUCCESS;
        // 构造门店解绑对象
        WmCustomerPoiUnBindBo wmCustomerPoiUnBindBo = buildPoiUnBindBo(wmCustomerPoiUnBindParamBo, map);
        // 预解绑
        if (!CollectionUtils.isEmpty(map.get(CustomerPoiUnBindDecideResultEnum.CAN_PRE_BIND_FAIL_UNBIND))) {
            wmCustomerPoiUnBindBo.setWmPoiIdSet(map.get(CustomerPoiUnBindDecideResultEnum.CAN_PRE_BIND_FAIL_UNBIND));
            preBindUnBind(wmCustomerPoiUnBindBo);
        } else if (!CollectionUtils.isEmpty(map.get(CustomerPoiUnBindDecideResultEnum.CAN_UNBIND_AND_CANCEL_SIGN))) {
            // 解绑并取消流程中任务
            wmCustomerPoiUnBindBo.setWmPoiIdSet(map.get(CustomerPoiUnBindDecideResultEnum.CAN_UNBIND_AND_CANCEL_SIGN));
            wmCustomerPoiUnBindBo.setOpType(WmCustomerOplogBo.OpType.CHANGESTATUS);
            directUnBind(wmCustomerPoiUnBindBo);
            List<WmCustomerPoiSmsRecordDB> wmCustomerPoiSmsRecordDBList = wmCustomerPoiSmsRecordMapper.selectSmsRecordListByCustomerId(wmCustomerPoiUnBindParamBo.getCustomerId());
            cancelDoingCustomerTaskAndSignTaskBatch(new WmCustomerPoiUnBindCancelDoingTaskBo(wmCustomerPoiSmsRecordDBList,
                    map.get(CustomerPoiUnBindDecideResultEnum.CAN_UNBIND_AND_CANCEL_SIGN),
                    wmCustomerPoiUnBindParamBo.getOpUid(), wmCustomerPoiUnBindParamBo.getOpName(),
                    CustomerTaskDetailSourceEnum.REBUILD_POI.getDesc()));
        } else {
            // 直接解绑
            if (!CollectionUtils.isEmpty(map.get(CustomerPoiUnBindDecideResultEnum.CAN_UNBIND_DIRECT))) {
                wmCustomerPoiUnBindBo.setWmPoiIdSet(map.get(CustomerPoiUnBindDecideResultEnum.CAN_UNBIND_DIRECT));
                directUnBind(wmCustomerPoiUnBindBo);
            }

            // 发起电子签约
            if (!CollectionUtils.isEmpty(map.get(CustomerPoiUnBindDecideResultEnum.NEED_SIGN_TO_UNBIND))) {
                WmCustomerPoiUnBindToSignBo wmCustomerPoiUnBindToSignBo = buildPoiUnBindToSignBo(wmCustomerPoiUnBindBo,
                        map.get(CustomerPoiUnBindDecideResultEnum.NEED_SIGN_TO_UNBIND), wmCustomerPoiUnBindParamBo.getSourceTypeEnum());
                applyUnBindCustomerPoiConfirm(wmCustomerPoiUnBindToSignBo);
                result = CustomerConstants.CUSTOMER_POI_UNBIND_NEED_CHECK;
            }

            // 纸质客户操作无权限
            if (!CollectionUtils.isEmpty(map.get(CustomerPoiUnBindDecideResultEnum.PAGE_UNBIND_NO_AUTH))) {
                result = String.format(CustomerConstants.CUSTOMER_POI_UNBIND_NEED_AUTH,
                        JSONObject.toJSONString(map.get(CustomerPoiUnBindDecideResultEnum.PAGE_UNBIND_NO_AUTH)));
            }
        }
        return result;
    }

    /**
     * 门店释放解绑
     *
     * @param wmPoiIdSet
     * @param opUid
     * @param opName
     * @throws WmCustomerException
     * @throws TException
     */
    public void poiReleaseUnBind(Set<Long> wmPoiIdSet, int opUid, String opName) throws WmCustomerException, TException {
        log.info("poiReleaseUnBind wmPoiIdSet={},opUid={},opName={}", JSONObject.toJSONString(wmPoiIdSet), opUid, opName);
        if (CollectionUtils.isEmpty(wmPoiIdSet)) {
            return;
        }
        // 业务校验
        customerPoiBaseValidator.validateInCustomerSwitchTask(wmPoiIdSet);
        customerPoiBaseValidator.validPreBindStatus(wmPoiIdSet);
        List<WmCustomerPoiSmsRecordDB> smsRecordDBList = Lists.newArrayList();
        // for 每个门店解绑
        for (Long wmPoiId : wmPoiIdSet) {
            Integer customerId = unBindForPoiRelease(wmPoiId, opUid, opName);
            if (customerId == null) {
                continue;
            }
            List<WmCustomerPoiSmsRecordDB> currentSmsList = wmCustomerPoiSmsRecordMapper.selectUnBindSmsRecordByCustomerIdWithPoiId(customerId, wmPoiId);
            if (CollectionUtils.isEmpty(currentSmsList)) {
                continue;
            }
            smsRecordDBList.addAll(currentSmsList);
        }
        if (CollectionUtils.isEmpty(smsRecordDBList)) {
            return;
        }
        // 取消签约任务
        cancelDoingCustomerTaskAndSignTaskBatch(new WmCustomerPoiUnBindCancelDoingTaskBo(smsRecordDBList,
                wmPoiIdSet, opUid, opName, CustomerTaskDetailSourceEnum.POI_RELEASE.getDesc()));
    }

    /**
     * 客户删除解绑
     *
     * @param customerId
     * @param opUid
     * @param opName
     * @throws WmCustomerException
     */
    public void customerDeleteUnBind(int customerId, Integer opUid, String opName) throws WmCustomerException {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.meituan.waimai.customer.service.customer.poi.CustomerPoiUnBindService.customerDeleteUnBind(int,java.lang.Integer,java.lang.String)");
        log.info("customerDeleteUnBind customerId={},opUid={},opName={}", customerId, opUid, opName);
        List<Long> wmPoiIds = wmCustomerPoiRelService.selectWmPoiIdsByCustomerId(customerId);
        if (wmPoiIds != null && wmPoiIds.size() > MccConfig.getCustomerDeletePoiBatchNum()) {
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "客户所关联的门店大于" + MccConfig.getCustomerDeletePoiBatchNum() + ",请先解绑关联门店");
        }
        Set<Long> wmPoiIdSet = Sets.newHashSet(wmPoiIds);
        if (!CollectionUtils.isEmpty(wmPoiIds)) {
            customerPoiBaseValidator.validWmPoiStatus(wmPoiIdSet);
            customerPoiBaseValidator.validateInCustomerSwitchTask(wmPoiIdSet);
        }
        WmCustomerDB wmCustomerDB = getWmCustomerDB(customerId);

        CustomerOperateBO customerOperateBO = new CustomerOperateBO.Builder()
                .opSource(CustomerTaskSourceEnum.CUSTOMER_DELETE.getCode())
                .opDetailSource(CustomerTaskDetailSourceEnum.CUSTOMER_DELETE.getDesc())
                .opSystem(CustomerTaskOpSystemEnum.CUSTOMER_WEB.getDesc())
                .opUserId(opUid)
                .opUserName(opName)
                .taskSceneType(CustomerTaskSceneType.CUSTOMER_BIND_OR_UNBIND.getCode())
                .taskType(CustomerTaskTypeEnum.CUSTOMER_UNBIND_POI.getCode())
                .bizTaskId(null)
                .build();

        //创建客户门店解绑任务
        Map<Long, Integer> poiAndTaskMaps = customerTaskService.batchAddCustomerUnBindTask(customerId,
                wmPoiIdSet, customerOperateBO);

        directUnBind(new WmCustomerPoiUnBindBo(
                wmCustomerDB, wmPoiIdSet, "客户删除门店解绑",
                opUid, opName, poiAndTaskMaps,
                CustomerPoiUnBindTypeEnum.DELETE_CUSTOMER_UNBIND, WmCustomerPoiOplogSourceTypeEnum.DELETE_CUSTOMER,
                CustomerMQEventEnum.CUSTOMER_DELETE, WmCustomerOplogBo.OpType.DELETE));
    }

    /**
     * 确认解绑
     *
     * @param wmCustomerPoiUnbindConfirmBo
     * @throws WmCustomerException
     */
    public void confirmUnBind(WmCustomerPoiUnbindConfirmBo wmCustomerPoiUnbindConfirmBo) throws WmCustomerException {
        log.info("确认解绑开始,wmCustomerPoiUnbindConfirmBo={}", JSONObject.toJSONString(wmCustomerPoiUnbindConfirmBo));
        // 参数校验
        checkUnBindConfirmParam(wmCustomerPoiUnbindConfirmBo);
        int customerId = wmCustomerPoiUnbindConfirmBo.getCustomerId();
        Set<Long> wmPoiIdSet = wmCustomerPoiUnbindConfirmBo.getWmPoiIdSet();
        Integer opUid = wmCustomerPoiUnbindConfirmBo.getOpUid();
        String opName = wmCustomerPoiUnbindConfirmBo.getOpName();
        CustomerPoiUnBindTypeEnum unBindTypeEnum = wmCustomerPoiUnbindConfirmBo.getUnBindTypeEnum();
        WmCustomerPoiOplogSourceTypeEnum oplogSourceTypeEnum = wmCustomerPoiUnbindConfirmBo.getOplogSourceTypeEnum();
        try {
            // 批量解绑不允许操作线上门店
            customerPoiBaseValidator.validWmPoiStatus(wmPoiIdSet);
        } catch (WmCustomerException e) {
            log.error("已确认解绑，但门店是上线状态，不执行解绑客户。门店id：{}", JSONObject.toJSONString(wmPoiIdSet), e);
            wmCustomerPoiUnbindConfirmBo.setUnBindTypeEnum(CustomerPoiUnBindTypeEnum.POI_ONLINE_CANCEL_UNBIND);
            cancelUnBind(wmCustomerPoiUnbindConfirmBo);
            return;
        }

        // 校验门店当前绑定的客户非传入的客户
        List<WmCustomerPoiDB> notBindCustomerIdList = wmCustomerPoiRelService.selectNotCustomerIdByWmPoiIdsRT(wmPoiIdSet, customerId);
        if (!CollectionUtils.isEmpty(notBindCustomerIdList)) {
            log.error("已确认解绑，但门店绑定的客户非传入客户，不执行解绑客户。门店id：{}，notBindCustomerId={}", JSONObject.toJSONString(wmPoiIdSet),
                    JSONObject.toJSONString(notBindCustomerIdList));
            wmCustomerPoiUnbindConfirmBo.setUnBindTypeEnum(CustomerPoiUnBindTypeEnum.CUSTOMER_DIFFERENT_CANCEL_UNBIND);
            cancelUnBind(wmCustomerPoiUnbindConfirmBo);
            return;
        }

        // 过滤已经解绑客户的门店
        Set<Long> needUnBindWmPoiIdSet = wmCustomerPoiRelService.selectExistPoiByWmPoiId(wmPoiIdSet);
        if (CollectionUtils.isEmpty(needUnBindWmPoiIdSet)) {
            log.info("confirmUnBind no need poi wmCustomerPoiUnbindConfirmBo={}", JSONObject.toJSONString(wmCustomerPoiUnbindConfirmBo));
            String logMsg = String.format(CustomerConstants.CUSTOMER_LOG_TEMPLATE_UNBIND_CUSTOMER_NO_POI, StringUtils.join(wmPoiIdSet, "、"));
            wmCustomerService.insertCustomerOpLog(customerId, opUid, opName, WmCustomerOplogBo.OpType.CHANGESTATUS, logMsg);
            return;
        }
        // 获取处理中解绑任务ID
        Map<Long, Integer> poiAndTaskIds = new HashMap<>();
        List<WmCustomerTask> taskList = customerTaskService.listDoingTaskByCusIdAndSignIdAndTaskType(customerId,
                wmCustomerPoiUnbindConfirmBo.getSmsId(), CustomerTaskTypeEnum.CUSTOMER_UNBIND_POI.getCode());
        if (!CollectionUtils.isEmpty(taskList)) {
            for (WmCustomerTask wmCustomerTask : taskList) {
                if (needUnBindWmPoiIdSet.contains(wmCustomerTask.getBizId().longValue())) {
                    poiAndTaskIds.put(wmCustomerTask.getBizId().longValue(), wmCustomerTask.getId());
                }
            }
        }

        WmCustomerDB wmCustomerDB = getWmCustomerDB(customerId);
        directUnBind(new WmCustomerPoiUnBindBo(
                wmCustomerDB, needUnBindWmPoiIdSet, "", opUid, opName, poiAndTaskIds, unBindTypeEnum,
                oplogSourceTypeEnum, CustomerMQEventEnum.CUSTOMER_UNBIND_POI, WmCustomerOplogBo.OpType.CHANGESTATUS));
        log.info("确认解绑结束,wmCustomerPoiUnbindConfirmBo={}", JSONObject.toJSONString(wmCustomerPoiUnbindConfirmBo));

    }

    /**
     * 取消解绑
     *
     * @param wmCustomerPoiUnbindConfirmBo
     * @throws WmCustomerException
     */
    public void cancelUnBind(WmCustomerPoiUnbindConfirmBo wmCustomerPoiUnbindConfirmBo) throws WmCustomerException {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.service.customer.poi.CustomerPoiUnBindService.cancelUnBind(com.sankuai.meituan.waimai.customer.domain.poi.WmCustomerPoiUnbindConfirmBo)");
        log.info("取消解绑开始,wmCustomerPoiUnbindConfirmBo={}", JSONObject.toJSONString(wmCustomerPoiUnbindConfirmBo));
        // 参数校验
        checkUnBindCancelParam(wmCustomerPoiUnbindConfirmBo);
        int customerId = wmCustomerPoiUnbindConfirmBo.getCustomerId();
        Set<Long> wmPoiIdSet = wmCustomerPoiUnbindConfirmBo.getWmPoiIdSet();
        Integer opUid = wmCustomerPoiUnbindConfirmBo.getOpUid();
        String opName = wmCustomerPoiUnbindConfirmBo.getOpName();
        CustomerPoiUnBindTypeEnum unBindTypeEnum = wmCustomerPoiUnbindConfirmBo.getUnBindTypeEnum();
        // 过滤已经解绑客户的门店
        Set<Long> needUnBindWmPoiIdSet = wmCustomerPoiRelService.selectExistPoiByWmPoiId(wmPoiIdSet);
        if (CollectionUtils.isEmpty(needUnBindWmPoiIdSet)) {
            log.info("cancelUnBind no need poi wmCustomerPoiUnbindConfirmBo={}", JSONObject.toJSONString(wmCustomerPoiUnbindConfirmBo));
            String logMsg = String.format(CustomerConstants.CUSTOMER_LOG_TEMPLATE_CANCEL_UNBIND_CUSTOMER_NO_POI, StringUtils.join(wmPoiIdSet, "、"));
            wmCustomerService.insertCustomerOpLog(customerId, opUid, opName, WmCustomerOplogBo.OpType.CHANGESTATUS, logMsg);
            return;
        }
        wmCustomerPoiRelService.rejectUnBind(needUnBindWmPoiIdSet, customerId, null);
        String logMsg = null;
        if (wmCustomerPoiUnbindConfirmBo.getUnBindTypeEnum() != null &&
                wmCustomerPoiUnbindConfirmBo.getUnBindTypeEnum() == CustomerPoiUnBindTypeEnum.CUSTOMER_DIFFERENT_CANCEL_UNBIND) {
            logMsg = String.format(unBindTypeEnum.getLogMsg(), wmCustomerPoiUnbindConfirmBo.getCustomerId(), StringUtils.join(needUnBindWmPoiIdSet, ","));
        } else {
            logMsg = String.format(unBindTypeEnum.getLogMsg(), StringUtils.join(needUnBindWmPoiIdSet, ","));
        }
        wmCustomerService.insertCustomerOpLog(customerId, opUid, opName, WmCustomerOplogBo.OpType.CHANGESTATUS, logMsg);
        log.info("取消解绑结束,wmCustomerPoiUnbindConfirmBo={}", JSONObject.toJSONString(wmCustomerPoiUnbindConfirmBo));
    }

    /**
     * 代理商切换解绑
     *
     * @param customerId
     * @param wmPoiIdSet
     * @param opUid
     * @param opName
     * @throws WmCustomerException
     */
    public void agentSwitchUnBind(int customerId, Set<Long> wmPoiIdSet, Integer opUid, String opName) throws WmCustomerException {
        log.info("agentSwitchUnBind customerId={},wmPoiIdSet={},opUid={},opName={}", customerId, JSONObject.toJSONString(wmPoiIdSet), opUid, opName);
        if (CollectionUtils.isEmpty(wmPoiIdSet)) {
            return;
        }
        WmCustomerDB wmCustomerDB = getWmCustomerDB(customerId);
        CustomerOperateBO customerOperateBO = getUnbindCustomerOperateBO(ClientInfoUtil.getClientAppKey(), opUid, opName);
        Map<Long, Integer> poiAndTaskMaps = null;
        if (customerOperateBO != null) {
            poiAndTaskMaps = customerTaskService.batchAddCustomerUnBindTask(customerId, wmPoiIdSet, customerOperateBO);
        }
        directUnBind(new WmCustomerPoiUnBindBo(
                wmCustomerDB, wmPoiIdSet, "", opUid, opName, poiAndTaskMaps, CustomerPoiUnBindTypeEnum.AGENT_SWITCH_CUSTOMER_UNBIND,
                WmCustomerPoiOplogSourceTypeEnum.AGENT_AND_CONTRACT_START, CustomerMQEventEnum.CUSTOMER_UNBIND_POI,
                WmCustomerOplogBo.OpType.UPDATE));
    }


    /**
     * 门店解绑入参校验
     *
     * @param unBindParamBo
     * @throws WmCustomerException
     */
    private void checkUnBindParam(WmCustomerPoiUnBindParamBo unBindParamBo) throws WmCustomerException {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.meituan.waimai.customer.service.customer.poi.CustomerPoiUnBindService.checkUnBindParam(com.sankuai.meituan.waimai.customer.domain.poi.WmCustomerPoiUnBindParamBo)");
        try {
            Preconditions.checkNotNull(unBindParamBo);
            Preconditions.checkArgument(unBindParamBo.getCustomerId() > 0);
            Preconditions.checkArgument(!CollectionUtils.isEmpty(unBindParamBo.getWmPoiIdSet()));
            Preconditions.checkNotNull(unBindParamBo.getSourceTypeEnum());
            Preconditions.checkNotNull(unBindParamBo.getTypeEnum());
        } catch (Exception e) {
            log.error("checkUnBindParam 门店解绑参数错误 unBindParamBo={}", JSONObject.toJSONString(unBindParamBo), e);
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "参数不合法");
        }
    }

    /**
     * 门店确认解绑入参校验
     *
     * @param wmCustomerPoiUnbindConfirmBo
     * @throws WmCustomerException
     */
    private void checkUnBindConfirmParam(WmCustomerPoiUnbindConfirmBo wmCustomerPoiUnbindConfirmBo) throws WmCustomerException {
        try {
            Preconditions.checkNotNull(wmCustomerPoiUnbindConfirmBo);
            Preconditions.checkArgument(wmCustomerPoiUnbindConfirmBo.getCustomerId() > 0);
            Preconditions.checkArgument(!CollectionUtils.isEmpty(wmCustomerPoiUnbindConfirmBo.getWmPoiIdSet()));
            WmCustomerPoiOplogSourceTypeEnum poiOplogSourceTypeEnum = wmCustomerPoiUnbindConfirmBo.getOplogSourceTypeEnum();
            Preconditions.checkNotNull(poiOplogSourceTypeEnum);
            Preconditions.checkArgument(poiOplogSourceTypeEnum == WmCustomerPoiOplogSourceTypeEnum.FORCE_UNBIND
                    || poiOplogSourceTypeEnum == WmCustomerPoiOplogSourceTypeEnum.CONFIRM_UNBIND);
            CustomerPoiUnBindTypeEnum unBindTypeEnum = wmCustomerPoiUnbindConfirmBo.getUnBindTypeEnum();
            Preconditions.checkNotNull(unBindTypeEnum);
            Preconditions.checkArgument(unBindTypeEnum == CustomerPoiUnBindTypeEnum.BD_FORCE_UNBIND
                    || unBindTypeEnum == CustomerPoiUnBindTypeEnum.CONFIRM_UNBIND);
        } catch (Exception e) {
            log.error("checkUnBindConfirmParam 门店解绑参数错误 wmCustomerPoiUnbindConfirmBo={}", JSONObject.toJSONString(wmCustomerPoiUnbindConfirmBo), e);
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "参数不合法");
        }
    }

    /**
     * 门店取消解绑入参校验
     *
     * @param wmCustomerPoiUnbindConfirmBo
     * @throws WmCustomerException
     */
    private void checkUnBindCancelParam(WmCustomerPoiUnbindConfirmBo wmCustomerPoiUnbindConfirmBo) throws WmCustomerException {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.service.customer.poi.CustomerPoiUnBindService.checkUnBindCancelParam(com.sankuai.meituan.waimai.customer.domain.poi.WmCustomerPoiUnbindConfirmBo)");
        try {
            Preconditions.checkNotNull(wmCustomerPoiUnbindConfirmBo);
            Preconditions.checkArgument(wmCustomerPoiUnbindConfirmBo.getCustomerId() > 0);
            Preconditions.checkArgument(!CollectionUtils.isEmpty(wmCustomerPoiUnbindConfirmBo.getWmPoiIdSet()));
            CustomerPoiUnBindTypeEnum unBindTypeEnum = wmCustomerPoiUnbindConfirmBo.getUnBindTypeEnum();
            Preconditions.checkNotNull(unBindTypeEnum);
            Preconditions.checkArgument(unBindTypeEnum == CustomerPoiUnBindTypeEnum.CONFITM_CANCEL_UNBIND
                    || unBindTypeEnum == CustomerPoiUnBindTypeEnum.BD_CANCEL_UNBIND
                    || unBindTypeEnum == CustomerPoiUnBindTypeEnum.POI_ONLINE_CANCEL_UNBIND
                    || unBindTypeEnum == CustomerPoiUnBindTypeEnum.CUSTOMER_DIFFERENT_CANCEL_UNBIND);
        } catch (Exception e) {
            log.error("checkUnBindCancelParam 门店解绑参数错误 wmCustomerPoiUnbindConfirmBo={}", JSONObject.toJSONString(wmCustomerPoiUnbindConfirmBo), e);
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "参数不合法");
        }
    }

    private WmCustomerDB getWmCustomerDB(int customerId) throws WmCustomerException {
        WmCustomerDB wmCustomerDB = wmCustomerService.selectCustomerById(customerId);
        if (wmCustomerDB == null) {
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "客户ID无法查到有效数据");
        }
        return wmCustomerDB;
    }

    /**
     * 加工门店解绑对象
     *
     * @param wmCustomerPoiUnBindParamBo
     * @param map
     * @return
     * @throws WmCustomerException
     */
    private WmCustomerPoiUnBindBo buildPoiUnBindBo(WmCustomerPoiUnBindParamBo wmCustomerPoiUnBindParamBo, Map<CustomerPoiUnBindDecideResultEnum, Set<Long>> map) throws WmCustomerException {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.customer.poi.CustomerPoiUnBindService.buildPoiUnBindBo(com.sankuai.meituan.waimai.customer.domain.poi.WmCustomerPoiUnBindParamBo,java.util.Map)");
        WmCustomerDB wmCustomerDB = getWmCustomerDB(wmCustomerPoiUnBindParamBo.getCustomerId());
        WmCustomerPoiUnBindBo wmCustomerPoiUnBindBo = new WmCustomerPoiUnBindBo();
        wmCustomerPoiUnBindBo.setWmCustomerDB(wmCustomerDB);
        wmCustomerPoiUnBindBo.setWmPoiIdSet(wmCustomerPoiUnBindParamBo.getWmPoiIdSet());
        wmCustomerPoiUnBindBo.setRemark(wmCustomerPoiUnBindParamBo.getRemark());
        wmCustomerPoiUnBindBo.setOpUid(wmCustomerPoiUnBindParamBo.getOpUid());
        wmCustomerPoiUnBindBo.setOpName(wmCustomerPoiUnBindParamBo.getOpName());
        CustomerTaskSourceEnum sourceTypeEnum = wmCustomerPoiUnBindParamBo.getSourceTypeEnum();
        WmCustomerPoiOplogSourceTypeEnum oplogSourceTypeEnum = wmCustomerPoiOplogService.getOpSourceType(sourceTypeEnum, wmCustomerPoiUnBindParamBo.getCustomerOperateBO());
        wmCustomerPoiUnBindBo.setOplogSourceTypeEnum(oplogSourceTypeEnum);
        if (sourceTypeEnum == CustomerTaskSourceEnum.CUSTOMER_DELETE) {
            wmCustomerPoiUnBindBo.setMqEventEnum(CustomerMQEventEnum.CUSTOMER_DELETE);
        } else {
            wmCustomerPoiUnBindBo.setMqEventEnum(CustomerMQEventEnum.CUSTOMER_UNBIND_POI);
        }
        if (!CollectionUtils.isEmpty(map.get(CustomerPoiUnBindDecideResultEnum.CAN_PRE_BIND_FAIL_UNBIND))) {
            wmCustomerPoiUnBindBo.setTypeEnum(CustomerPoiUnBindTypeEnum.PRE_BIND_FAIL_UNBIND);
        } else if (!CollectionUtils.isEmpty(map.get(CustomerPoiUnBindDecideResultEnum.CAN_UNBIND_AND_CANCEL_SIGN))) {
            wmCustomerPoiUnBindBo.setTypeEnum(CustomerPoiUnBindTypeEnum.REBUILD_FORCE_UNBIND);
        } else {
            wmCustomerPoiUnBindBo.setTypeEnum(CustomerPoiUnBindTypeEnum.DIRECT_UNBIND);
        }
        Set<Long> needCreateCustomerTask = Sets.newHashSet();
        for (Map.Entry<CustomerPoiUnBindDecideResultEnum, Set<Long>> data : map.entrySet()) {
            CustomerPoiUnBindDecideResultEnum resultEnum = data.getKey();
            Set<Long> wmPoiIdSet = data.getValue();
            if (resultEnum != CustomerPoiUnBindDecideResultEnum.PAGE_UNBIND_NO_AUTH && !CollectionUtils.isEmpty(wmPoiIdSet)) {
                needCreateCustomerTask.addAll(wmPoiIdSet);
            }
        }
        Map<Long, Integer> poiAndTaskMaps = customerTaskService.batchAddCustomerUnBindTask(wmCustomerPoiUnBindParamBo.getCustomerId(),
                needCreateCustomerTask, wmCustomerPoiUnBindParamBo.getCustomerOperateBO());
        wmCustomerPoiUnBindBo.setPoiAndTaskMaps(poiAndTaskMaps);
        wmCustomerPoiUnBindBo.setOpType(WmCustomerOplogBo.OpType.UPDATE);
        return wmCustomerPoiUnBindBo;
    }

    /**
     * 加工门店解绑签约对象
     *
     * @param wmCustomerPoiUnBindBo
     * @param wmPoiIdSet
     * @return
     */
    private WmCustomerPoiUnBindToSignBo buildPoiUnBindToSignBo(WmCustomerPoiUnBindBo wmCustomerPoiUnBindBo, Set<Long> wmPoiIdSet, CustomerTaskSourceEnum sourceTypeEnum) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.meituan.waimai.customer.service.customer.poi.CustomerPoiUnBindService.buildPoiUnBindToSignBo(WmCustomerPoiUnBindBo,Set,CustomerTaskSourceEnum)");
        WmCustomerPoiUnBindToSignBo wmCustomerPoiUnBindToSignBo = new WmCustomerPoiUnBindToSignBo();
        wmCustomerPoiUnBindToSignBo.setWmCustomerDB(wmCustomerPoiUnBindBo.getWmCustomerDB());
        wmCustomerPoiUnBindToSignBo.setWmPoiIdSet(wmPoiIdSet);
        wmCustomerPoiUnBindToSignBo.setOpUid(wmCustomerPoiUnBindBo.getOpUid());
        wmCustomerPoiUnBindToSignBo.setOpName(wmCustomerPoiUnBindBo.getOpName());
        wmCustomerPoiUnBindToSignBo.setRemark(wmCustomerPoiUnBindBo.getRemark());
        wmCustomerPoiUnBindToSignBo.setPoiAndTaskMaps(wmCustomerPoiUnBindBo.getPoiAndTaskMaps());
        wmCustomerPoiUnBindToSignBo.setCustomerPoiUnBindTypeEnum(wmCustomerPoiUnBindBo.getTypeEnum());
        wmCustomerPoiUnBindToSignBo.setSourceTypeEnum(sourceTypeEnum);
        return wmCustomerPoiUnBindToSignBo;
    }

    /**
     * 发起解绑签约
     *
     * @param wmCustomerPoiUnBindToSignBo
     * @throws WmCustomerException
     */
    private void applyUnBindCustomerPoiConfirm(WmCustomerPoiUnBindToSignBo wmCustomerPoiUnBindToSignBo) throws WmCustomerException {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.service.customer.poi.CustomerPoiUnBindService.applyUnBindCustomerPoiConfirm(com.sankuai.meituan.waimai.customer.domain.poi.WmCustomerPoiUnBindToSignBo)");
        log.info("applyUnBindCustomerPoiConfirm wmCustomerPoiUnBindToSignBo={}", JSONObject.toJSONString(wmCustomerPoiUnBindToSignBo));
        WmCustomerDB wmCustomerDB = wmCustomerPoiUnBindToSignBo.getWmCustomerDB();
        int customerId = wmCustomerDB.getId();
        Set<Long> wmPoiIdSet = wmCustomerPoiUnBindToSignBo.getWmPoiIdSet();
        Integer opUid = wmCustomerPoiUnBindToSignBo.getOpUid();
        String opName = wmCustomerPoiUnBindToSignBo.getOpName();
        Map<Long, Integer> poiAndTaskMaps = wmCustomerPoiUnBindToSignBo.getPoiAndTaskMaps();
        CustomerTaskSourceEnum sourceTypeEnum = wmCustomerPoiUnBindToSignBo.getSourceTypeEnum();
        LongResult result = null;
        try {
            EcontractTaskApplyBo econtractTaskApplyBo = new EcontractTaskApplyBo();
            econtractTaskApplyBo.setBizId(String.valueOf(customerId));
            econtractTaskApplyBo.setBizTypeEnum(EcontractTaskApplyBizTypeEnum.CUSTOMER_ID);
            econtractTaskApplyBo.setApplyTypeEnum(EcontractTaskApplyTypeEnum.CUSTOMER);

            EcontractCancelAuthInfoBo econtractCancelAuthInfoBo = new EcontractCancelAuthInfoBo();
            econtractCancelAuthInfoBo.setCustomerName(wmCustomerDB.getCustomerName());
            econtractCancelAuthInfoBo.setCustomerNumber(wmCustomerDB.getCustomerNumber());
            // 批量获取门店名称
            List<String> wmPoiNames = Lists.newArrayList();
            List<Long> wmPoiIds = Lists.newArrayList(wmPoiIdSet);
            econtractCancelAuthInfoBo.setWmPoiIdList(wmPoiIds);
            // 如果超过批量查询限制,则查询最大限制即可，因为只会取头几个
            if (wmPoiIds.size() > CustomerConstants.POI_QUERY_BATCH_NUM) {
                wmPoiIds = Lists.partition(wmPoiIds, CustomerConstants.POI_QUERY_BATCH_NUM).get(0);
            }
            List<WmPoiAggre> wmPoiAggreList = wmPoiQueryAdapter.getWmPoiAggre(wmPoiIds,
                    Sets.newHashSet(WmPoiFieldQueryConstant.WM_POI_FIELD_NAME));
            int count = 0;
            for (WmPoiAggre wmPoiAggre : wmPoiAggreList) {
                if (count > MAX_POI_NAME_NUM) {
                    break;
                }
                wmPoiNames.add(wmPoiAggre.getName());
                count++;
            }
            if (wmPoiAggreList != null && wmPoiAggreList.size() > MAX_POI_NAME_NUM) {
                econtractCancelAuthInfoBo.setWmPoiName(StringUtils.join(wmPoiNames, "、") + "等");
            } else {
                econtractCancelAuthInfoBo.setWmPoiName(StringUtils.join(wmPoiNames, "、"));
            }
            econtractTaskApplyBo.setApplyInfoBo(JSONObject.toJSONString(econtractCancelAuthInfoBo));
            econtractTaskApplyBo.setCommitUid(opUid);
            // 发送短信通知生效KP
            result = wmEcontractSignBzService.applyTask(econtractTaskApplyBo);
            // 记录短信记录
            WmCustomerPoiSmsRecordDB wmCustomerPoiSmsRecordDB = new WmCustomerPoiSmsRecordDB();
            wmCustomerPoiSmsRecordDB.setCustomerId(customerId);
            wmCustomerPoiSmsRecordDB.setTaskId(result.getValue());
            wmCustomerPoiSmsRecordDB.setWmPoiIds(StringUtils.join(wmPoiIdSet, CustomerConstants.SPLIT_SYMBOL));
            wmCustomerPoiSmsRecordDB.setTaskStatus(EcontractTaskStateEnum.TO_COMMIT.getType());
            wmCustomerPoiSmsRecordDB.setValid(CustomerConstants.VALID);
            wmCustomerPoiSmsRecordDB.setType((byte) 0);
            wmCustomerPoiSmsRecordMapper.insertSmsRecord(wmCustomerPoiSmsRecordDB);
            // 更新任务的签约任务ID
            customerTaskService.updateTaskSignTaskId(customerId, Lists.newArrayList(poiAndTaskMaps.values()), wmCustomerPoiSmsRecordDB.getId());
            // 更新客户门店关系为确认解绑中
            wmCustomerPoiRelService.confirmUnBind(wmPoiIdSet, null, customerId, poiAndTaskMaps);
            // 插入操作日志
            wmCustomerService.insertCustomerOpLog(customerId, opUid, opName, WmCustomerOplogBo.OpType.CHANGESTATUS,
                    String.format(CustomerConstants.CUSTOMER_LOG_TEMPLATE_UNBIND_TO_CONFIRM,
                            StringUtils.join(wmPoiIdSet, CustomerConstants.SPLIT_SYMBOL)),
                    wmCustomerPoiUnBindToSignBo.getRemark());
        } catch (Exception e) {
            log.error("发送客户门店解绑短信异常 wmCustomerPoiUnBindToSignBo={}", JSONObject.toJSONString(wmCustomerPoiUnBindToSignBo), e);
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "发送客户门店解绑短信异常");
        } finally {
            MetricHelper.build()
                    .name(MetricConstant.METRIC_SEND_CUSTOMER_POI_UNBIND_SMS_COUNT)
                    .tag("source", sourceTypeEnum.name())
                    .tag("success", String.valueOf(result.getValue() > 0))
                    .count();
        }

    }

    /**
     * 预绑定失败解绑
     *
     * @param wmCustomerPoiUnBindBo
     * @throws WmCustomerException
     */
    private void preBindUnBind(WmCustomerPoiUnBindBo wmCustomerPoiUnBindBo) throws WmCustomerException {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.meituan.waimai.customer.service.customer.poi.CustomerPoiUnBindService.preBindUnBind(com.sankuai.meituan.waimai.customer.domain.poi.WmCustomerPoiUnBindBo)");
        log.info("preBindUnBind wmCustomerPoiUnBindBo={}", JSONObject.toJSONString(wmCustomerPoiUnBindBo));
        int customerId = wmCustomerPoiUnBindBo.getWmCustomerDB().getId();
        Set<Long> wmPoiIdSet = wmCustomerPoiUnBindBo.getWmPoiIdSet();
        Integer opUid = wmCustomerPoiUnBindBo.getOpUid();
        String opName = wmCustomerPoiUnBindBo.getOpName();
        Map<Long, Integer> poiAndTaskMaps = wmCustomerPoiUnBindBo.getPoiAndTaskMaps();

        for (Long wmPoiId : wmPoiIdSet) {
            log.info("#preBindUnBind,delete wmPoiId={}", wmPoiId);
            wmCustomerPoiRelService.unBindPreBindFail(wmPoiId, customerId);
            // 插入客户日志
            wmCustomerService.insertCustomerOpLog(customerId, opUid, opName, WmCustomerOplogBo.OpType.UPDATE,
                    String.format(CustomerConstants.CUSTOMER_LOG_TEMPLATE_POI_UNBIND_FROM_PREBIND, StringUtils.join(wmPoiIdSet, CustomerConstants.SPLIT_SYMBOL)), "");
        }
        //需要查询出来数据更新到关系
        if (!CollectionUtils.isEmpty(poiAndTaskMaps) && poiAndTaskMaps.values() != null) {
            List<Integer> taskIds = Lists.newArrayList(poiAndTaskMaps.values());
            customerTaskService.updateTaskStatus(customerId, wmPoiIdSet, taskIds);
        }
    }

    /**
     * 客户门店关系解绑
     *
     * @param wmCustomerPoiUnBindBo
     * @throws WmCustomerException
     */
    private void directUnBind(WmCustomerPoiUnBindBo wmCustomerPoiUnBindBo) throws WmCustomerException {
        log.info("directUnBind wmCustomerPoiUnBindBo={}", JSONObject.toJSONString(wmCustomerPoiUnBindBo));
        Set<Long> wmPoiIdSet = wmCustomerPoiUnBindBo.getWmPoiIdSet();
        Integer opUid = wmCustomerPoiUnBindBo.getOpUid();
        String opName = wmCustomerPoiUnBindBo.getOpName();
        WmCustomerDB wmCustomerDB = wmCustomerPoiUnBindBo.getWmCustomerDB();
        int customerId = wmCustomerDB.getId();
        WmCustomerPoiOplogSourceTypeEnum oplogSourceTypeEnum = wmCustomerPoiUnBindBo.getOplogSourceTypeEnum();
        CustomerPoiUnBindTypeEnum typeEnum = wmCustomerPoiUnBindBo.getTypeEnum();
        CustomerMQEventEnum mqEventEnum = wmCustomerPoiUnBindBo.getMqEventEnum();
        String status = WmCustomerConstant.SUCCESS;
        String failReason = "";
        try {
            if (!CollectionUtils.isEmpty(wmPoiIdSet)) {
                Map<Long, Integer> poiAndTaskMaps = wmCustomerPoiUnBindBo.getPoiAndTaskMaps();
                Set<Long> errorWmPoiIds = Sets.newHashSet();
                for (Long wmPoiId : wmPoiIdSet) {
                    try {
                        Integer taskId = null;
                        if (MapUtils.isNotEmpty(poiAndTaskMaps) && poiAndTaskMaps.get(wmPoiId) != null) {
                            taskId = poiAndTaskMaps.get(wmPoiId);
                        }
                        // 批量删除门店和客户的关系
                        wmCustomerPoiRelService.unBindCustomerPoi(wmPoiId, customerId, taskId);
                        //删除客户门店属性记录
                        wmCustomerPoiAttributeService.deleteCustomerPoiAttribute(customerId, wmPoiId);
                        // 写入门店维护日志
                        wmPoiOplogThriftServiceAdaptor.insertPoiOpLog(wmPoiId, String.format(CustomerConstants.POI_LOG_CHANGE_CUSTOMER, wmCustomerDB.getMtCustomerId() + wmCustomerDB.getCustomerName(), ""),
                                opUid, opName, String.valueOf(wmCustomerDB.getMtCustomerId()), "");
                    } catch (Exception e) {
                        log.error("解绑门店异常,wmPoiId:{}", wmPoiId, e);
                        errorWmPoiIds.add(wmPoiId);
                    }
                }

                // 美食城客户解绑门店,删除门店美食城标签
                if (wmCustomerDB.getCustomerRealType() == CustomerRealTypeEnum.MEISHICHENG.getValue()
                        && wmCustomerMSCLabelService.checkCustomerHasMSCLabel(wmCustomerDB.getMtCustomerId())) {
                    wmCustomerMSCLabelService.deleteMSCPoiTag(wmPoiIdSet, opUid, opName);
                }

                //门店如果有资质共用-商家标签，则删除标签
                wmCustomerMSCLabelService.deleteMscQuaCommonPoiTag(wmPoiIdSet, opUid, opName);

                // 通知上单状态机
                stateCenterAdapter.batchSyncCustomerState(wmPoiIdSet, CustomerModuleStateEnum.DELETE, opUid, opName);
                // 解绑失败的门店需要排除这部分ID，且通知操作人
                if (!CollectionUtils.isEmpty(errorWmPoiIds)) {
                    wmPoiIdSet = Sets.difference(wmPoiIdSet, errorWmPoiIds);
                    String email = wmEmployClient.getEmail(opUid);
                    log.info("门店ID{},解绑异常,通知email={}", StringUtils.join(errorWmPoiIds, ","), email);
                    // 发大象消息
                    DaxiangUtil.push("<EMAIL>",
                            String.format("门店ID:%s,解绑异常", StringUtils.join(errorWmPoiIds, ",")), email);
                }

                //门店主体置为空
                wmContractService.saveCustomerPoiSubject(customerId, Lists.newArrayList(wmPoiIdSet), ContractSignSubjectOpTagEnum.CUSTOMER_UNBIND.getCode(), Strings.EMPTY, Strings.EMPTY);
                // 插入客户日志
                wmCustomerService.insertCustomerOpLog(customerId, opUid, opName, wmCustomerPoiUnBindBo.getOpType(),
                        String.format(typeEnum.getLogMsg(), StringUtils.join(wmPoiIdSet, CustomerConstants.SPLIT_SYMBOL)), wmCustomerPoiUnBindBo.getRemark());
                if (oplogSourceTypeEnum == WmCustomerPoiOplogSourceTypeEnum.CUSTOMER_POI_LIST) {
                    // 批量刷新es
                    wmCustomerPoiListEsService.batchUpdateEsSync(customerId, wmPoiIdSet);
                }
                // 记录解绑操作日志
                wmCustomerPoiOplogService.batchUnbindOplog(new WmCustomerPoiOplogDB(Long.valueOf(customerId), oplogSourceTypeEnum.getCode(), opUid, opName),
                        Lists.newArrayList(wmPoiIdSet));
                // 发送MQ
                wmCustomerService.sendCustomerStatusNoticeMQ(customerId, mqEventEnum,
                        wmPoiIdSet, opUid, opName);
                //需要查询出来数据更新到关系
                if (!CollectionUtils.isEmpty(poiAndTaskMaps) && poiAndTaskMaps.values() != null) {
                    List<Integer> taskIds = Lists.newArrayList(poiAndTaskMaps.values());
                    customerTaskService.updateTaskStatus(customerId, wmPoiIdSet, taskIds);
                }
            } else {
                // 删除解绑时，没有关联门店也需要发送MQ消息
                if (mqEventEnum.getCode() == CustomerMQEventEnum.CUSTOMER_DELETE.getCode()) {
                    wmCustomerService.sendCustomerStatusNoticeMQ(customerId, mqEventEnum, new HashSet(), opUid, opName);
                }
            }
        } catch (WmCustomerException e) {
            status = WmCustomerConstant.BUSINESS_EXCEPTION;
            failReason = e.getMsg();
            throw e;
        } catch (Exception e) {
            status = WmCustomerConstant.SYSTEM_EXCEPTION;
            failReason = WmCustomerConstant.SYSTEM_EXCEPTION_MSG;
            throw e;
        } finally {
            Cat.logEvent(CustomerMetricEnum.CUSTOMER_POI_UNBIND.getName(), oplogSourceTypeEnum.name(), status, failReason);
            MetricHelper.build().name(CustomerMetricEnum.CUSTOMER_POI_UNBIND.getName()).tag(CustomerMetricEnum.CUSTOMER_POI_UNBIND.getTag(), oplogSourceTypeEnum.name())
                    .tag(CustomerMetricEnum.CUSTOMER_POI_UNBIND.getStatus(), status).count();
        }
    }

    /**
     * 门店释放解绑
     *
     * @param wmPoiId
     * @param opUid
     * @param opName
     * @throws WmCustomerException
     * @throws TException
     */
    private Integer unBindForPoiRelease(long wmPoiId, int opUid, String opName) throws WmCustomerException, TException {
        log.info("unBindForPoiRelease wmPoiId={},opUid={},opName={}", wmPoiId, opUid, opName);
        String status = WmCustomerConstant.SUCCESS;
        String reason = "";
        try {
            WmCustomerDB wmCustomerDB = wmCustomerService.selectCustomerByWmPoiId(wmPoiId);
            if (null == wmCustomerDB) {
                //从库查询为空查询主库
                log.info("根据门店id:{}查询从库客户信息为空,降级查询主库",wmPoiId);
                Cat.logEvent("selectCustomerByWmPoiId","notFoundFromSlave");
                wmCustomerDB = wmCustomerPlatformDataParseService.selectCustomerFromPlatformByWmPoiIdByRT(wmPoiId);
                if(wmCustomerDB == null){
                    Cat.logEvent("selectCustomerByWmPoiId","notFoundFromRT");
                    log.warn("根据门店id:{}查询客户信息为空,请及时关注是否为异常数据",wmPoiId);
                    return null;
                }
            }
            //门店释放-直接创建新解绑任务
            CustomerOperateBO customerOperateBO = new CustomerOperateBO.Builder()
                    .opUserId(opUid).opUserName(opName).remark("")
                    .opSource(CustomerTaskSourceEnum.POI_RELEASE.getCode())
                    .taskType(CustomerTaskTypeEnum.CUSTOMER_UNBIND_POI.getCode())
                    .opDetailSource(CustomerTaskDetailSourceEnum.POI_RELEASE.getDesc())
                    .taskSceneType(CustomerTaskSceneType.CUSTOMER_BIND_OR_UNBIND.getCode())
                    .opSystem(CustomerTaskOpSystemEnum.POI_MANAGER_SYS.getDesc())
                    .build();
            int customerId = wmCustomerDB.getId();

            // 创建客户门店解绑任务
            Integer taskId = customerTaskService.addCustomerUnBindTask(customerId, wmPoiId, customerOperateBO);
            log.info("根据客户ID和门店ID查询客户切换发起的解绑任务,taskId={}", taskId);

            //解绑
            wmCustomerPoiRelService.unBindCustomerPoi(wmPoiId, customerId, taskId);
            //删除客户门店属性记录
            wmCustomerPoiAttributeService.deleteCustomerPoiAttribute(customerId, wmPoiId);
            //解绑企客门店
            companyCustomerSyncService.handleUnbindInSwitchCustomer(customerId, wmPoiId);

            //门店主体置为空
            wmContractService.saveCustomerPoiSubject(customerId, Lists.newArrayList(wmPoiId), ContractSignSubjectOpTagEnum.CUSTOMER_UNBIND.getCode(), Strings.EMPTY, Strings.EMPTY);
            //从"已审核美食城"标签的客户解绑门店
            if (wmCustomerDB.getCustomerRealType() == CustomerRealTypeEnum.MEISHICHENG.getValue() &&
                    wmCustomerMSCLabelService.checkCustomerHasMSCLabel(wmCustomerDB.getMtCustomerId())) {
                wmCustomerMSCLabelService.deleteMSCPoiTag(Sets.newHashSet(wmPoiId), opUid, opName);
            }
            //门店如果有资质共用-商家标签，则删除标签
            wmCustomerMSCLabelService.deleteMscQuaCommonPoiTag(Sets.newHashSet(wmPoiId), opUid, opName);

            //插入客户日志
            wmCustomerService.insertCustomerOpLog(customerId, opUid, opName, WmCustomerOplogBo.OpType.UPDATE, String.format(CustomerConstants.CUSTOMER_LOG_TEMPLATE_POI_UNBIND, wmPoiId), "");
            //插入门店日志
            wmPoiOplogThriftServiceAdaptor.insertPoiOpLog(wmPoiId, String.format(CustomerConstants.POI_LOG_CHANGE_CUSTOMER, wmCustomerDB.getMtCustomerId() + wmCustomerDB.getCustomerName(), ""),
                    opUid, opName, String.valueOf(wmCustomerDB.getMtCustomerId()), "");
            // 记录客户门店解绑日志
            wmCustomerPoiOplogService.unbindOplog(new WmCustomerPoiOplogDB(wmCustomerDB.getId().longValue(), wmPoiId, WmCustomerPoiOplogSourceTypeEnum.POI_RELEASE.getCode(), opUid, opName));
            // 发送门店释放消息
            customerPoiSendService.sendCustomerPoiRelaseNotify(new CustomerPoiReleaseMsg(customerId, wmPoiId, opUid, opName));
            //更新任务状态-已完成
            if (taskId != null && taskId > 0) {
                customerTaskService.updateTaskStatus(customerId, Sets.newHashSet(wmPoiId), Lists.newArrayList(taskId));
            }
            return customerId;
        } catch (WmCustomerException e) {
            status = WmCustomerConstant.BUSINESS_EXCEPTION;
            reason = e.getMsg();
            throw e;
        } catch (Exception e) {
            status = WmCustomerConstant.SYSTEM_EXCEPTION;
            reason = WmCustomerConstant.SYSTEM_EXCEPTION_MSG;
            throw e;
        } finally {
            Cat.logEvent(CustomerMetricEnum.CUSTOMER_POI_UNBIND.getName(), WmCustomerPoiOplogSourceTypeEnum.POI_RELEASE.name(), status, reason);
            MetricHelper.build().name(CustomerMetricEnum.CUSTOMER_POI_UNBIND.getName()).tag(CustomerMetricEnum.CUSTOMER_POI_UNBIND.getTag(), WmCustomerPoiOplogSourceTypeEnum.POI_RELEASE.name())
                    .tag(CustomerMetricEnum.CUSTOMER_POI_UNBIND.getStatus(), status).count();
        }
    }

    /**
     * 取消进行中客户任务和签约任务-指定短信任务
     *
     * @param wmCustomerPoiUnBindCanCelDoingTaskBo
     * @throws TException
     * @throws WmCustomerException
     */
    public void cancelDoingCustomerTaskAndSignTaskBatch(WmCustomerPoiUnBindCancelDoingTaskBo wmCustomerPoiUnBindCanCelDoingTaskBo) throws TException, WmCustomerException {
        log.info("cancelDoingCustomerTaskAndSignTaskBatch WmCustomerPoiUnBindCanCelDoingTaskBo={}", JSONObject.toJSONString(wmCustomerPoiUnBindCanCelDoingTaskBo));
        List<WmCustomerPoiSmsRecordDB> wmCustomerPoiSmsRecordDBList = wmCustomerPoiUnBindCanCelDoingTaskBo.getSmsRecordDBList();
        if (CollectionUtils.isEmpty(wmCustomerPoiSmsRecordDBList)) {
            log.info("cancelDoingCustomerTaskAndSignTaskBatch no need cancel WmCustomerPoiUnBindCanCelDoingTaskBo={}", JSONObject.toJSONString(wmCustomerPoiUnBindCanCelDoingTaskBo));
            return;
        }
        Integer opUid = wmCustomerPoiUnBindCanCelDoingTaskBo.getOpUid();
        String opName = wmCustomerPoiUnBindCanCelDoingTaskBo.getOpName();
        String detailSource = wmCustomerPoiUnBindCanCelDoingTaskBo.getDetailSource();
        Set<Long> wmPoiIdSet = wmCustomerPoiUnBindCanCelDoingTaskBo.getWmPoiIdSet();
        for (WmCustomerPoiSmsRecordDB wmCustomerPoiSmsRecordDB : wmCustomerPoiSmsRecordDBList) {
            Set<Long> smsWmPoiIdSet = convertWmPoiIds(wmCustomerPoiSmsRecordDB.getWmPoiIds());
            if (CollectionUtils.isEmpty(smsWmPoiIdSet)) {
                log.error("cancelDoingCustomerTaskAndSignTaskBatch error smsWmPoiIdSet is empty wmCustomerPoiSmsRecordDB={}", JSONObject.toJSONString(wmCustomerPoiSmsRecordDB));
                return;
            }
            if (smsWmPoiIdSet.size() == 1) {
                log.info("cancelDoingCustomerTaskAndSignTaskBatch only one poi wmCustomerPoiSmsRecordDB={}", JSONObject.toJSONString(wmCustomerPoiSmsRecordDB));
                Long wmPoiId = Long.valueOf(wmCustomerPoiSmsRecordDB.getWmPoiIds());
                if (wmPoiIdSet.contains(wmPoiId)) {
                    // 取消电子签约
                    wmEcontractSignBzService.cancelSignWithReason(wmCustomerPoiSmsRecordDB.getTaskId(), WmEcontractBatchConstant.FORCE_UNBIND,
                            String.format(CANCEL_SMS_REASON, detailSource), false);
                    // 取消短信任务
                    wmCustomerPoiSmsRecordMapper.updateSmsRecordByTaskStatus(EcontractTaskStateEnum.FAIL.getType(), wmCustomerPoiSmsRecordDB.getTaskId());
                    // 取消客户任务
                    customerTaskService.cancelDoingUnBindTask(wmCustomerPoiSmsRecordDB.getCustomerId(), wmPoiId, detailSource);
                    //插入客户日志
                    wmCustomerService.insertCustomerOpLog(wmCustomerPoiSmsRecordDB.getCustomerId(), opUid, opName, WmCustomerOplogBo.OpType.CHANGESTATUS,
                            String.format(CustomerConstants.CUSTOMER_LOG_TEMPLATE_POI_UNBIND_AND_CANCEL_SMS, wmPoiId, detailSource), "");
                }
            } else {
                log.info("cancelDoingCustomerTaskAndSignTaskBatch contain poi wmCustomerPoiSmsRecordDB={}", JSONObject.toJSONString(wmCustomerPoiSmsRecordDB));
                smsWmPoiIdSet.retainAll(wmPoiIdSet);
                if (CollectionUtils.isEmpty(smsWmPoiIdSet)) {
                    continue;
                }
                for (Long wmPoiId : smsWmPoiIdSet) {
                    // 取消客户任务
                    customerTaskService.cancelDoingUnBindTask(wmCustomerPoiSmsRecordDB.getCustomerId(), wmPoiId, detailSource);
                }
            }
        }
    }

    /**
     * 获取老接口解绑操作的appKey来源
     *
     * @param appKey
     * @return
     */
    private CustomerOperateBO getUnbindCustomerOperateBO(String appKey, Integer opUid, String opName) {
        try {
            CustomerOperateBO customerOperateBO = null;
            Map<String, Object> customerUnBIndPoiAppKeyConfigs = MccCustomerConfig.getCustomerUnbindPoiAppKeySourceConfig();
            if (StringUtils.isNotBlank(appKey)
                    && !CollectionUtils.isEmpty(customerUnBIndPoiAppKeyConfigs)
                    && customerUnBIndPoiAppKeyConfigs.get(appKey) != null) {
                customerOperateBO = new CustomerOperateBO();
                CustomerPoiAppKeySourceConfig customerPoiAppKeySourceConfig =
                        JSON.parseObject(customerUnBIndPoiAppKeyConfigs.get(appKey).toString(), CustomerPoiAppKeySourceConfig.class);
                if (customerPoiAppKeySourceConfig == null || customerPoiAppKeySourceConfig.getOpSource() == null || customerPoiAppKeySourceConfig.getOpSource() < 0) {
                    return null;
                }
                customerOperateBO.setOpSource(customerPoiAppKeySourceConfig.getOpSource());
                customerOperateBO.setOpDetailSource(customerPoiAppKeySourceConfig.getOpDetailSource());
                customerOperateBO.setOpSystem(customerPoiAppKeySourceConfig.getOpSystem());
                customerOperateBO.setTaskType(CustomerTaskTypeEnum.CUSTOMER_UNBIND_POI.getCode());
                customerOperateBO.setTaskSceneType(CustomerTaskSceneType.CUSTOMER_BIND_OR_UNBIND.getCode());
                customerOperateBO.setOpUserId(opUid);
                customerOperateBO.setOpUserName(opName);
            }
            return customerOperateBO;
        } catch (Exception e) {
            log.error("getUnbindCustomerOperateBO,根据appKey获取调用端配置发生异常,appKey={}", appKey, e);
        }
        return null;
    }

    /**
     * 字符串形式的wmPoiIds转成Set
     *
     * @param wmPoiIds
     * @return
     * @throws WmCustomerException
     */
    private Set<Long> convertWmPoiIds(String wmPoiIds) throws WmCustomerException {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.meituan.waimai.customer.service.customer.poi.CustomerPoiUnBindService.convertWmPoiIds(java.lang.String)");
        if (StringUtils.isEmpty(wmPoiIds)) {
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "当前任务ID的门店为空");
        }
        String[] wmPoiIdArray = wmPoiIds.split(CustomerConstants.SPLIT_SYMBOL);
        Set<Long> wmPoiIdSet = Sets.newHashSet();
        for (String wmPoiId : wmPoiIdArray) {
            wmPoiIdSet.add(Long.parseLong(wmPoiId));
        }
        return wmPoiIdSet;
    }

}
