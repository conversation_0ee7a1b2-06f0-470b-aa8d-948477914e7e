package com.sankuai.meituan.waimai.customer.service.sign.applytask;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.dianping.cat.Cat;
import com.google.common.base.Function;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.meituan.mtrace.Tracer;
import com.meituan.mtrace.thread.pool.TraceExecutors;
import com.sankuai.meituan.waimai.customer.adapter.WmCustomerGrayServiceAdapter;
import com.sankuai.meituan.waimai.customer.adapter.WmLogisticsContractThriftServiceAdapter;
import com.sankuai.meituan.waimai.customer.adapter.WmLogisticsGatewayThriftServiceAdapter;
import com.sankuai.meituan.waimai.customer.adapter.WmPoiLogisticsClient;
import com.sankuai.meituan.waimai.customer.config.MccConfig;
import com.sankuai.meituan.waimai.customer.config.MccSignConfig;
import com.sankuai.meituan.waimai.customer.constant.EcontractUserToken;
import com.sankuai.meituan.waimai.customer.constant.WmEcontractConstant;
import com.sankuai.meituan.waimai.customer.contract.config.WmFrameContractConfigService;
import com.sankuai.meituan.waimai.customer.contract.service.impl.WmContractService;
import com.sankuai.meituan.waimai.customer.domain.WmEcontractSignManualTaskDB;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerPoiService;
import com.sankuai.meituan.waimai.customer.service.kp.WmCustomerKpGroupService;
import com.sankuai.meituan.waimai.customer.service.sign.applytask.context.DcContractContext;
import com.sankuai.meituan.waimai.customer.service.sign.applytask.context.ManualPackNoticeContext;
import com.sankuai.meituan.waimai.customer.service.sign.biz.WmEcontractManualBatchBizService;
import com.sankuai.meituan.waimai.customer.service.sign.biz.WmEcontractManualTaskBizService;
import com.sankuai.meituan.waimai.customer.service.sign.lock.ManualTaskLockService;
import com.sankuai.meituan.waimai.customer.service.sign.manualpack.strategy.noticetask.NoticeTaskTypeFactory;
import com.sankuai.meituan.waimai.customer.service.sign.pack.WmEcontractCustomerPackService;
import com.sankuai.meituan.waimai.customer.service.sign.signpack.WmEcontractSignPackService;
import com.sankuai.meituan.waimai.customer.settle.ddd.domain.input.dservice.impl.WmSettleInputCommitDomainServiceImpl;
import com.sankuai.meituan.waimai.customer.settle.service.WmSettleManagerService;
import com.sankuai.meituan.waimai.customer.util.ServiceEnvUtils;
import com.sankuai.meituan.waimai.econtrct.client.constants.BatchSignBizLineEnum;
import com.sankuai.meituan.waimai.econtrct.client.constants.EcontractAPIResponseConstant;
import com.sankuai.meituan.waimai.econtrct.client.domain.api.ApplyBatchNumBo;
import com.sankuai.meituan.waimai.econtrct.client.service.EcontractAPIResponse;
import com.sankuai.meituan.waimai.econtrct.client.service.EcontractAPIService;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.param.HeronContractOperator;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.param.flow.sign.HeronContractManualBatchSignParam;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.param.flow.sign.HeronContractManualSignItem;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.param.flow.sign.HeronContractSignGrayParam;
import com.sankuai.meituan.waimai.poilogistics.thrift.constant.OperateSource;
import com.sankuai.meituan.waimai.poilogistics.thrift.domain.OperateInfo;
import com.sankuai.meituan.waimai.poilogistics.thrift.domain.sign.BatchManualSignInfo;
import com.sankuai.meituan.waimai.poilogistics.thrift.domain.sign.ManualSignInfo;
import com.sankuai.meituan.waimai.poilogistics.thrift.exception.WmPoiLogisticsException;
import com.sankuai.meituan.waimai.poilogistics.thrift.service.WmLogisticsFeeThriftService;
import com.sankuai.meituan.waimai.poilogistics.thrift.service.WmPoiLogisticsSignThriftService;
import com.sankuai.meituan.waimai.thrift.constant.WmContractErrorCodeConstant;
import com.sankuai.meituan.waimai.thrift.customer.constant.*;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractTaskConstants.ManualTaskCancelSource;
import com.sankuai.meituan.waimai.thrift.customer.domain.base.BooleanResult;
import com.sankuai.meituan.waimai.thrift.customer.domain.base.LongResult;
import com.sankuai.meituan.waimai.thrift.customer.domain.config.ContractConfigInfo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.C1ExpireAfterRenewalResultBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.ManualTaskApplyBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.ManualTaskSettleContextBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.WmEcontractSignManualTaskBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import com.sankuai.meituan.waimai.thrift.util.ConfigUtilAdapter;
import com.sankuai.meituan.waimai.util.DaxiangUtil;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.concurrent.BasicThreadFactory;
import org.apache.thrift.TException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Nullable;
import javax.annotation.Resource;
import java.util.*;
import java.util.Map.Entry;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Service
public class WmEcontractManualTaskApplyService {
    private static final Logger LOGGER = LoggerFactory.getLogger(WmEcontractManualTaskApplyService.class);
    private static final ScheduledExecutorService scheduledthreadpool = TraceExecutors.getTraceScheduledExecutorService(new ScheduledThreadPoolExecutor(2,
            new BasicThreadFactory.Builder().namingPattern("manualTaskApply-schedule-pool-%d").daemon(true).build()));

    private static final Set<String> DAOCAN_CONTRACT_TASK_TYPE_LIST = Sets.newHashSet(
            EcontractTaskApplyTypeEnum.DAOCAN_SERVICE_C1_CONTRACT.getName(),
            EcontractTaskApplyTypeEnum.DAOCAN_SERVICE_C2_CONTRACT.getName()
    );

    @Autowired
    private WmEcontractManualTaskBizService wmEcontractManualTaskBizService;
    @Autowired
    private WmEcontractManualBatchBizService wmEcontractManualBatchBizService;
    @Autowired
    private WmCustomerPoiService wmCustomerPoiService;
    @Autowired
    private ManualTaskLockService manualTaskLockService;
    @Autowired
    private WmContractService wmContractService;
    @Autowired
    private WmLogisticsFeeThriftService.Iface wmLogisticsFeeThriftService;
    @Autowired
    private WmSettleManagerService wmSettleManagerService;
    @Autowired
    private WmSettleInputCommitDomainServiceImpl wmSettleInputCommitDomainService;
    @Autowired
    private WmPoiLogisticsSignThriftService.Iface wmPoiLogisticsSignThriftService;
    @Autowired
    private WmPoiLogisticsClient wmPoiLogisticsClient;
    @Autowired
    private WmCustomerKpGroupService wmCustomerKpGroupService;
    @Autowired
    private WmCustomerGrayServiceAdapter wmCustomerGrayServiceAdapter;
    @Autowired
    private EcontractAPIService econtractAPIService;
    @Autowired
    private WmEcontractSignPackService wmEcontractSignPackService;
    @Autowired
    private NoticeTaskTypeFactory noticeTaskTypeFactory;
    @Autowired
    private WmEcontractCustomerPackService wmEcontractCustomerPackService;
    @Autowired
    private WmLogisticsGatewayThriftServiceAdapter wmLogisticsGatewayThriftServiceAdapter;
    @Autowired
    private WmLogisticsContractThriftServiceAdapter wmLogisticsContractThriftServiceAdapter;

    @Autowired
    private WmEcontractC1AutoRenewalService wmEcontractC1AutoRenewalService;

    @Resource
    private WmFrameContractConfigService wmFrameContractConfigService;

    public LongResult applyManualTask(ManualTaskApplyBo manualTaskApplyBo) throws WmCustomerException {
        LOGGER.info("#applyManualTask,manualTaskApplyBo={}",JSONObject.toJSONString(manualTaskApplyBo));
        //业务校验
        checkApplyTask(manualTaskApplyBo);
        //1.数据校验-待发起列表不重复
        //2.插入manualTask表
        wmEcontractManualTaskBizService.checkDuplicate(manualTaskApplyBo);
        long manualTaskId = wmEcontractManualTaskBizService.insertManualTask(manualTaskApplyBo);
        /*if(EcontractTaskApplyTypeEnum.POIFEE.equals(manualTaskApplyBo.getApplyTypeEnum())
                && StringUtils.isNotBlank(manualTaskApplyBo.getApplyContext())){
            wmEcontractC1AutoRenewalService.autoReviewalForPoiFee(manualTaskApplyBo,manualTaskId);
        }*/
        return new LongResult(manualTaskId);
    }

    public LongResult applyTaskForAutoRenewal(ManualTaskApplyBo manualTaskApplyBo) throws WmCustomerException, TException {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.service.sign.applytask.WmEcontractManualTaskApplyService.applyTaskForAutoRenewal(com.sankuai.meituan.waimai.thrift.customer.domain.sign.ManualTaskApplyBo)");
        try {
            return wmEcontractC1AutoRenewalService.applyAutoRenewalTask(manualTaskApplyBo);
        } catch (Exception e) {
            LOGGER.error("WmEcontractC1AutoRenewalService#autoReviewalForPoiFee 发起自动续签打包签约任务失败，manualTaskApplyBo:{}", JSON.toJSONString(manualTaskApplyBo), e);
            String message ="环境："+ ServiceEnvUtils.getEnv() +",wmPoiId = " + manualTaskApplyBo.getBizId() + " C1到期自动续签配送合同打包签约任务发起失败,tracdId:"+ Tracer.id();
            DaxiangUtil.push("<EMAIL>",message, MccConfig.getContractAlarmMisIdList());
            throw new WmCustomerException(500,"发起自动续签打包签约任务失败");
        }
    }

    public BooleanResult cancelManualTask(long manualTaskId, int opUid, String source) throws WmCustomerException, TException {
        LOGGER.info("#cancelManualTask,manualTaskId={},opUid={},source={}",manualTaskId,opUid,source);
        //1.校验任务ID是否被锁住
        //2.manualTask置为无效
        manualTaskLockService.checkLock(Lists.newArrayList(manualTaskId));
        List<WmEcontractSignManualTaskDB> taskDBList = wmEcontractManualTaskBizService.batchGetByManualTaskIds(Lists.newArrayList(manualTaskId));
        if (taskDBList.size() == 1) {
            wmEcontractManualTaskBizService.deleteManualTask(manualTaskId);
            if(ManualTaskCancelSource.TASK_MANAGE_MODULE.getSource().equals(source)){
                cancelManualTaskFromTaskManagerModule(opUid, taskDBList);
            }
        }
        return new BooleanResult(true);
    }


    private void checkApplyTask(ManualTaskApplyBo applyBo) throws WmCustomerException {
        if (wmCustomerGrayServiceAdapter.isGrayCustomer(applyBo.getCustomerId()) && EcontractTaskApplyTypeEnum.SETTLE == applyBo.getApplyTypeEnum()) {
            throw new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR, "女娲二期灰度客户不能发起结算签约！");
        }
    }

    private void cancelManualTaskFromTaskManagerModule(int opUid, List<WmEcontractSignManualTaskDB> taskDBList) throws WmCustomerException, TException {
        WmEcontractSignManualTaskDB taskDB = taskDBList.get(0);
        String module = taskDB.getModule();
        if (module.equals(EcontractTaskApplyTypeEnum.C1CONTRACT.getName())) {
            wmContractService.cancelC1SignByWaitingSign(taskDB.getCustomerId(), "任务列表取消", opUid, "");
        } else if (module.equals(EcontractTaskApplyTypeEnum.SETTLE.getName())) {
            wmSettleManagerService.cancelConfirm(taskDB.getCustomerId());
        } else if (module.equals(EcontractTaskApplyTypeEnum.POIFEE.getName())) {
            try {
                wmPoiLogisticsClient.cancelWmPoiAllFeeManualConfirmInfo(taskDB.getId(),taskDB.getWmPoiId(),"", opUid, OperateSource.CUSTOMER.getCode());
            } catch (WmPoiLogisticsException e) {
                LOGGER.error("cancelWmPoiAllFeeManualConfirmInfo", e);
                throw new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR, e.getMsg());
            }
        } else if (module.equals(EcontractTaskApplyTypeEnum.AGENT_SQS_STANDARD.getName())) {
            wmLogisticsContractThriftServiceAdapter.cancelManualWaitSignByChannel(taskDB, opUid);
        } else if (EcontractTaskApplyTypeEnum.DRONE_DELIVERY.getName().equals(module)
                || EcontractTaskApplyTypeEnum.FRUIT_TOGETHER_DELIVERY.getName().equals(module)
                || EcontractTaskApplyTypeEnum.VIP_CARD_CONTRACT_AGREEMENT.getName().equals(module)
                || EcontractTaskApplyTypeEnum.isNationalSubsidyDeliveryTaskType(EcontractTaskApplyTypeEnum.getByName(module))) {
            wmPoiLogisticsClient.cancelWmPoiAllFeeManualConfirmInfoByGateWay(taskDB, StringUtils.EMPTY, opUid, OperateSource.CUSTOMER.getCode(), module);
        } else if (module.equals(EcontractTaskApplyTypeEnum.C2CONTRACT.getName()) ||
                module.equals(EcontractTaskApplyTypeEnum.QUA_REAL_LETTER.getName())||
                module.equals(EcontractTaskApplyTypeEnum.POI_PROMOTION_SERVICE.getName())||
                module.equals(EcontractTaskApplyTypeEnum.BUSINESS_CUSTOMER_E_CONTRACT.getName())||
                module.equals(EcontractTaskApplyTypeEnum.GROUP_MEAL.getName()) ||
                module.equals(EcontractTaskApplyTypeEnum.FOODCITY_STATEMENT.getName()) ||
                module.equals(EcontractTaskApplyTypeEnum.SUBJECT_CHANGE_SUPPLEMENT.getName()) ||
                module.equals(EcontractTaskApplyTypeEnum.FOUR_WHEEL_PERFORMANCE_SUPPLEMENT_AGREEMENT.getName()) ||
                module.equals(EcontractTaskApplyTypeEnum.SPEEDY_DELIVERY_COOPERATION_AGREEMENT.getName()) ||
                module.equals(EcontractTaskApplyTypeEnum.DAOCAN_SERVICE_C1_CONTRACT.getName()) ||
                module.equals(EcontractTaskApplyTypeEnum.DAOCAN_SERVICE_C2_CONTRACT.getName()) ||
                module.equals(EcontractTaskApplyTypeEnum.NATIONAL_SUBSIDY_PURCHASE.getName()) ||
                module.equals(EcontractTaskApplyTypeEnum.COMMON_CONFIG_FRAME_CONTRACT_AGREEMENT.getName())
        ) {
            // 注：WmEcontractSignManualTaskDB这个对象的wmpoiid字段其实是发起签约时传的bizId
            wmContractService.cancelSignByWaitingSign(taskDB.getCustomerId(), taskDB.getWmPoiId(), "任务列表取消", opUid, "");
        } else {
            ContractConfigInfo configInfo = wmFrameContractConfigService.queryContractConfigInfo(module);
            if (configInfo != null) {
                wmContractService.cancelSignByWaitingSign(taskDB.getCustomerId(), taskDB.getWmPoiId(), "任务列表取消", opUid, "");
            }
        }
    }

    public BooleanResult cancelManualTask(int wmCustomerId, EcontractTaskApplyTypeEnum applyType) throws WmCustomerException {
        List<Long> toCancelTaskIdList = wmEcontractManualTaskBizService.getManualTaskByWmCustomerIdAndModule(wmCustomerId, applyType.getName());
        manualTaskLockService.checkLock(toCancelTaskIdList);
        wmEcontractManualTaskBizService.deleteManualTask(wmCustomerId, applyType.getName());
        return new BooleanResult(true);
    }

    public BooleanResult cancelManualTaskWithBizId(int wmCustomerId, long bizId, EcontractTaskApplyTypeEnum applyType) throws WmCustomerException {
        Long toCancelTaskId = wmEcontractManualTaskBizService.getManualTaskByWmCustomerIdAndModuleAndBizId(wmCustomerId, bizId, applyType.getName());
        if (toCancelTaskId == null) {
            return new BooleanResult(true);
        }
        manualTaskLockService.checkLock(Arrays.asList(toCancelTaskId));
        wmEcontractManualTaskBizService.deleteManualTaskWithBizId(wmCustomerId, bizId, applyType.getName());
        return new BooleanResult(true);
    }

    public BooleanResult cancelManualTaskWithBizId4Config(int wmCustomerId, long bizId, String module) throws WmCustomerException {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.service.sign.applytask.WmEcontractManualTaskApplyService.cancelManualTaskWithBizId4Config(int,long,java.lang.String)");
        Long toCancelTaskId = wmEcontractManualTaskBizService.getManualTaskByWmCustomerIdAndModuleAndBizId(wmCustomerId, bizId, module);
        if (toCancelTaskId == null) {
            return new BooleanResult(true);
        }
        manualTaskLockService.checkLock(Collections.singletonList(toCancelTaskId));
        wmEcontractManualTaskBizService.deleteManualTaskWithBizId(wmCustomerId, bizId, module);
        return new BooleanResult(true);
    }

    private ManualPackNoticeContext genManualPackNoticeContext(Map<String,List<WmEcontractSignManualTaskDB>> taskMap, String source) {
        int wmCustomerId = 0;
        Map<String, List<Long>> taskInfo = Maps.newHashMap();
        List<Long> taskList = null;
        List<Long> c1ContractTaskInfo = null;
        List<Long> settleTaskInfo = null;
        List<Long> deliveryTaskInfo = null;
        Map<Long, Long> deliveryTaskWmPoiIdMap = null;
        List<Long> droneTaskInfo = null;
        Map<Long, Long> droneTaskWmPoiIdMap = null;
        Map<Long, Long> fruitTogetherTaskWmPoiIdMap = null;
        Map<Long, Long> vipCardTaskWmPoiIdMap = null;
        Map<Long, Long> nationalSubsidyDistributorWmPoiIdMap = null;
        Map<Long, Long> nationalSubsidyHeadquartersWmPoiIdMap = null;
        List<Long> allTaskInfo = Lists.newArrayList();
        boolean onlyHaveDeliveryTask = true;
        for (Entry<String, List<WmEcontractSignManualTaskDB>> entry : taskMap.entrySet()) {
            if (wmCustomerId == 0) {
                wmCustomerId = entry.getValue().get(0).getCustomerId();
            }
            taskList = Lists.newArrayList(Lists.transform(entry.getValue(), new Function<WmEcontractSignManualTaskDB, Long>() {
                @Nullable
                @Override
                public Long apply(@Nullable WmEcontractSignManualTaskDB input) {
                    return input.getId();
                }
            }));
            taskInfo.put(entry.getKey(), taskList);
            allTaskInfo.addAll(taskList);
            if (EcontractTaskApplyTypeEnum.C1CONTRACT.getName().equals(entry.getKey())) {
                onlyHaveDeliveryTask = false;
                c1ContractTaskInfo = Lists.newArrayList(taskList);
            } else if (EcontractTaskApplyTypeEnum.SETTLE.getName().equals(entry.getKey())) {
                onlyHaveDeliveryTask = false;
                settleTaskInfo = Lists.newArrayList(taskList);
            } else if (EcontractTaskApplyTypeEnum.POIFEE.getName().equals(entry.getKey())) {
                deliveryTaskInfo = Lists.newArrayList(taskList);
                deliveryTaskWmPoiIdMap = entry.getValue().stream()
                        .collect(Collectors.toMap(x -> x.getId(), x -> x.getWmPoiId()));
            } else if (EcontractTaskApplyTypeEnum.DRONE_DELIVERY.getName().equals(entry.getKey())) {
                droneTaskInfo = Lists.newArrayList(taskList);
                droneTaskWmPoiIdMap = entry.getValue().stream()
                        .collect(Collectors.toMap(x -> x.getId(), x -> x.getWmPoiId()));
            } else if (EcontractTaskApplyTypeEnum.FRUIT_TOGETHER_DELIVERY.getName().equals(entry.getKey())) {
                fruitTogetherTaskWmPoiIdMap = entry.getValue().stream()
                        .collect(Collectors.toMap(x -> x.getId(), x -> x.getWmPoiId()));
            } else if (EcontractTaskApplyTypeEnum.VIP_CARD_CONTRACT_AGREEMENT.getName().equals(entry.getKey())) {
                vipCardTaskWmPoiIdMap = entry.getValue().stream()
                        .collect(Collectors.toMap(WmEcontractSignManualTaskDB::getId, WmEcontractSignManualTaskDB::getWmPoiId));
            } else if (EcontractTaskApplyTypeEnum.NATIONAL_SUBSIDY_DISTRIBUTOR_DELIVERY.getName().equals(entry.getKey())) {
                nationalSubsidyDistributorWmPoiIdMap = entry.getValue().stream()
                        .collect(Collectors.toMap(WmEcontractSignManualTaskDB::getId, WmEcontractSignManualTaskDB::getWmPoiId));
            } else if (EcontractTaskApplyTypeEnum.NATIONAL_SUBSIDY_HEADQUARTERS_DELIVERY.getName().equals(entry.getKey())) {
                nationalSubsidyHeadquartersWmPoiIdMap = entry.getValue().stream()
                        .collect(Collectors.toMap(WmEcontractSignManualTaskDB::getId, WmEcontractSignManualTaskDB::getWmPoiId));
            }
        }
        Integer relWmPoiIdCount = wmCustomerPoiService.countCustomerPoi(wmCustomerId);
        return ManualPackNoticeContext.builder().customerId(wmCustomerId)
                .customerRelWmPoiIdCount(relWmPoiIdCount).taskInfo(taskInfo)
                .allTaskInfo(allTaskInfo)
                .c1ContractTaskInfo(c1ContractTaskInfo)
                .settleTaskInfo(settleTaskInfo)
                .droneTaskInfo(droneTaskInfo)
                .droneTaskWmPoiIdMap(droneTaskWmPoiIdMap)
                .fruitTogetherTaskWmPoiIdMap(fruitTogetherTaskWmPoiIdMap)
                .vipCardTaskWmPoiIdMap(vipCardTaskWmPoiIdMap)
                .nationalSubsidyDistributorWmPoiIdMap(nationalSubsidyDistributorWmPoiIdMap)
                .nationalSubsidyHeadquartersWmPoiIdMap(nationalSubsidyHeadquartersWmPoiIdMap)
                .deliveryTaskInfo(deliveryTaskInfo)
                .deliveryTaskWmPoiIdMap(deliveryTaskWmPoiIdMap)
                .onlyHaveDeliveryTask(onlyHaveDeliveryTask)
                .source(source)
                .build();
    }

    private ManualPackNoticeContext genManualPackNoticeContextNew(Map<String, List<WmEcontractSignManualTaskDB>> taskMap, int commitUid, String source) {
        LOGGER.info("genManualPackNoticeContextNew, taskMap: {}",JSONObject.toJSONString(taskMap));
        int wmCustomerId = 0;
        Map<String, List<Long>> taskInfo = Maps.newHashMap();
        List<Long> taskList;
        List<Long> droneTaskInfo = null;
        Map<Long, Long> droneTaskWmPoiIdMap = null;
        Map<Long, Long> fruitTogetherTaskWmPoiIdMap = null;
        Map<Long, Long> vipTaskTaskWmPoiIdMap = null;
        Map<Long, Long> deliveryTaskWmPoiIdMap = null;
        Map<Long, Long> depositTaskWmPoiIdMap = null;
        Map<Long, Long> nationalSubsidyDistributorWmPoiIdMap = null;
        Map<Long, Long> nationalSubsidyHeadquartersWmPoiIdMap = null;
        List<Long> allTaskInfo = Lists.newArrayList();
        StringBuilder sb = new StringBuilder();
        boolean onlyHaveDeliveryTask = true;
        List<DcContractContext> dcContractContextList = new ArrayList<>();
        for (Entry<String, List<WmEcontractSignManualTaskDB>> entry : taskMap.entrySet()) {
            if (wmCustomerId == 0) {
                wmCustomerId = entry.getValue().get(0).getCustomerId();
            }
            for(WmEcontractSignManualTaskDB manualTaskDB : entry.getValue()){
                sb.append(entry.getKey()).append(",");
            }
            taskList = Lists.newArrayList(Lists.transform(entry.getValue(), new Function<WmEcontractSignManualTaskDB, Long>() {
                @Nullable
                @Override
                public Long apply(@Nullable WmEcontractSignManualTaskDB input) {
                    return input.getId();
                }
            }));
            taskInfo.put(entry.getKey(), taskList);
            allTaskInfo.addAll(taskList);
            //组装医药保证金门店和任务id
            if (EcontractTaskApplyTypeEnum.MED_DEPOSIT.getName().equals(entry.getKey())) {
                depositTaskWmPoiIdMap = entry.getValue().stream().collect(Collectors.toMap(x -> x.getId(), x -> x.getWmPoiId()));
            }
            if (EcontractTaskApplyTypeEnum.DRONE_DELIVERY.getName().equals(entry.getKey())) {
                droneTaskWmPoiIdMap = entry.getValue().stream().collect(Collectors.toMap(WmEcontractSignManualTaskDB::getId, WmEcontractSignManualTaskDB::getWmPoiId));
            }
            if (EcontractTaskApplyTypeEnum.FRUIT_TOGETHER_DELIVERY.getName().equals(entry.getKey())) {
                fruitTogetherTaskWmPoiIdMap = entry.getValue().stream().collect(Collectors.toMap(WmEcontractSignManualTaskDB::getId, WmEcontractSignManualTaskDB::getWmPoiId));
            }
            if (EcontractTaskApplyTypeEnum.VIP_CARD_CONTRACT_AGREEMENT.getName().equals(entry.getKey())) {
                vipTaskTaskWmPoiIdMap = entry.getValue().stream().collect(Collectors.toMap(WmEcontractSignManualTaskDB::getId, WmEcontractSignManualTaskDB::getWmPoiId));
            }
            if (EcontractTaskApplyTypeEnum.NATIONAL_SUBSIDY_DISTRIBUTOR_DELIVERY.getName().equals(entry.getKey())) {
                nationalSubsidyDistributorWmPoiIdMap = entry.getValue().stream()
                        .collect(Collectors.toMap(WmEcontractSignManualTaskDB::getId, WmEcontractSignManualTaskDB::getWmPoiId));
            }
            if (EcontractTaskApplyTypeEnum.NATIONAL_SUBSIDY_HEADQUARTERS_DELIVERY.getName().equals(entry.getKey())) {
                nationalSubsidyHeadquartersWmPoiIdMap = entry.getValue().stream()
                        .collect(Collectors.toMap(WmEcontractSignManualTaskDB::getId, WmEcontractSignManualTaskDB::getWmPoiId));
            }
            if (EcontractTaskApplyTypeEnum.POIFEE.getName().equals(entry.getKey())) {
                deliveryTaskWmPoiIdMap=entry.getValue().stream().collect(Collectors.toMap(WmEcontractSignManualTaskDB::getId, WmEcontractSignManualTaskDB::getWmPoiId));
            } else {
                onlyHaveDeliveryTask = false;
            }
            if (DAOCAN_CONTRACT_TASK_TYPE_LIST.contains(entry.getKey())) {
                List<WmEcontractSignManualTaskDB> signManualTaskDbList = entry.getValue();
                dcContractContextList.addAll(transDcContractContext(signManualTaskDbList, entry.getKey()));
            }
        }
        Integer relWmPoiIdCount = wmCustomerPoiService.countCustomerPoi(wmCustomerId);
        String taskMoudleInfo = sb.substring(0,sb.toString().length()-1);
        return ManualPackNoticeContext.builder()
                .taskInfo(taskInfo)
                .deliveryTaskWmPoiIdMap(deliveryTaskWmPoiIdMap)
                .droneTaskWmPoiIdMap(droneTaskWmPoiIdMap)
                .fruitTogetherTaskWmPoiIdMap(fruitTogetherTaskWmPoiIdMap)
                .vipCardTaskWmPoiIdMap(vipTaskTaskWmPoiIdMap)
                .nationalSubsidyDistributorWmPoiIdMap(nationalSubsidyDistributorWmPoiIdMap)
                .nationalSubsidyHeadquartersWmPoiIdMap(nationalSubsidyHeadquartersWmPoiIdMap)
                .allTaskInfo(allTaskInfo)
                .customerId(wmCustomerId)
                .customerRelWmPoiIdCount(relWmPoiIdCount)
                .commitUid(commitUid)
                .onlyHaveDeliveryTask(onlyHaveDeliveryTask)
                .taskMoudleInfo(taskMoudleInfo)
                .depositTaskWmPoiIdMap(depositTaskWmPoiIdMap)
                .source(source)
                .dcContractContextList(dcContractContextList)
                .build();
    }

    private List<DcContractContext> transDcContractContext(List<WmEcontractSignManualTaskDB> signManualTaskDBList, String contractType) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.sign.applytask.WmEcontractManualTaskApplyService.transDcContractContext(java.util.List,java.lang.String)");
        return signManualTaskDBList.stream()
                .map(signManualTaskDB -> extractDcContractContext(signManualTaskDB, contractType))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    private DcContractContext extractDcContractContext(WmEcontractSignManualTaskDB wmEcontractSignManualTaskDB, String contractType) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.meituan.waimai.customer.service.sign.applytask.WmEcontractManualTaskApplyService.extractDcContractContext(WmEcontractSignManualTaskDB,String)");
        ManualTaskSettleContextBo contextBo = JSON.parseObject(wmEcontractSignManualTaskDB.getApplyContext(), ManualTaskSettleContextBo.class);
        if (contextBo.getDaoCanContractContext() == null) {
            LOGGER.warn("WmEcontractManualTaskApplyService#transDcContractContext, 没有存储到餐信息, wmEcontractSignManualTaskDB: {}", JSON.toJSONString(wmEcontractSignManualTaskDB));
            return null;
        } else {
            DcContractContext dcContractContext = new DcContractContext();
            dcContractContext.setContractProof(contextBo.getDaoCanContractContext().getContractProof());
            dcContractContext.setNewSignContract(contextBo.getDaoCanContractContext().isNewSignContract());
            dcContractContext.setCoopType(contextBo.getDaoCanContractContext().getCoopType());
            dcContractContext.setContractType(contractType);
            return dcContractContext;
        }
    }

    //通知业务发起实际确认申请
    public LongResult applyManualPack(Map<String, List<WmEcontractSignManualTaskDB>> taskMap,
                                      int commitUid, String source) throws WmCustomerException,TException{
        //1.任务ID加锁
        //2.生成batch
        //3.异步提交:->延时预估->创建延时任务->执行回调业务接口->重置manual_task表状态->
        ManualPackNoticeContext context = genManualPackNoticeContext(taskMap, source);
        LOGGER.info("#applyManualPack,context={}",JSONObject.toJSONString(context));
        context.setCommitUid(commitUid);
        long batchId = initBatch(context);
        LOGGER.info("#applyManualPack,batchId={}",batchId);
        addSchedule(context);
        return new LongResult(batchId);
    }

    //通知业务发起实际确认申请
    public LongResult applyManualPackNew(Map<String, List<WmEcontractSignManualTaskDB>> taskMap,
                                         int commitUid, String source) throws WmCustomerException,TException{
        //构造manual_batch信息
        ManualPackNoticeContext context = genManualPackNoticeContextNew(taskMap, commitUid, source);
        LOGGER.info("#applyManualPackNew,context={}",JSONObject.toJSONString(context));
        //申请生成sign_pack
        Long signPackId = wmEcontractSignPackService.applySignPack(context);
        context.setSignPackId(signPackId);
        //申请生成record_batch
        Long econtractRecordBatchId = applyEcontractRecordBatch(context, signPackId);
        //关联sign_pack与record_batch
        wmEcontractSignPackService.updateRecordBatchById(signPackId, econtractRecordBatchId);
        //生成manual_batch
        long batchId = initBatch(context);
        LOGGER.info("#applyManualPackNew,batchId={}",batchId);
        //递归回调子任务系统
        addScheduleNew(context);
        return new LongResult(batchId);

    }

    public LongResult applyManualPackWithGroup(Map<String, List<WmEcontractSignManualTaskDB>> taskMap,
                                               int commitUid, List<Long> manualTaskIds, String source)  throws WmCustomerException,TException{
        try {
            // 命中运营经理分组逻辑
            if (MccSignConfig.signKpGroupOpen() && checkOnlyDeliveryManualTask(taskMap)) {
                return applyManualPackByGroup(taskMap, commitUid, KpSignTaskTypeEnum.DELIVERY, source);
            } else {
                Integer wmCustomerId = taskMap.get(taskMap.keySet().iterator().next()).get(0).getCustomerId();
                boolean isNewManualPack = wmEcontractCustomerPackService.manualSignUpdateGray(wmCustomerId);
                boolean isContainsNewTaskType = wmEcontractCustomerPackService.manualSignTaskTypeGray(taskMap.keySet());
                if (isNewManualPack && isContainsNewTaskType) {
                    return applyManualPackNew(taskMap, commitUid, source);
                } else {
                    return applyManualPack(taskMap, commitUid, source);
                }
            }
        } catch (WmCustomerException e) {
            LOGGER.error("#applyManualPackWithGroup打包签约发起异常,manualTaskIds={}", JSON.toJSONString(manualTaskIds), e);
            Cat.logMetricForCount("apply_manual_pack_fail");
            throw new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR, StringUtils.isEmpty(e.getMsg()) ? "发起打包签约失败" : e.getMsg());
        } catch (Exception e) {
            LOGGER.error("#applyManualPackWithGroup打包签约发起异常,manualTaskIds={}", JSON.toJSONString(manualTaskIds), e);
            Cat.logMetricForCount("apply_manual_pack_fail");
            throw new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR, "发起打包签约失败");
        } finally {
            manualTaskLockService.unLockManualTaskId(manualTaskIds);
        }
    }

    /**
     * 支持配送模块的手动打包分组
     * @param taskMap
     * @param commitUid
     * @param kpSignTaskTypeEnum
     * @return
     */
    private LongResult applyManualPackByGroup(Map<String, List<WmEcontractSignManualTaskDB>> taskMap,
            int commitUid, KpSignTaskTypeEnum kpSignTaskTypeEnum, String source) throws WmCustomerException,TException{
        Integer wmCustomerId = taskMap.get(taskMap.keySet().iterator().next()).get(0).getCustomerId();
        boolean isNewManualPack = wmEcontractCustomerPackService.manualSignUpdateGray(wmCustomerId);
        boolean isContainsNewTaskType = wmEcontractCustomerPackService.manualSignTaskTypeGray(taskMap.keySet());
        LongResult manualBatchId = new LongResult(0L);
        for(Entry<String, List<WmEcontractSignManualTaskDB>> temp : taskMap.entrySet()){
            if(!EcontractTaskApplyTypeEnum.POIFEE.getName().equals(temp.getKey())){
                LOGGER.error("无法支持的手动打包分组,temp={}",JSONObject.toJSONString(temp));
                continue;
            }
            List<WmEcontractSignManualTaskDB> value = temp.getValue();
            WmEcontractSignManualTaskDB wmEcontractSignManualTaskDB = value.get(0);
            Integer customerId = wmEcontractSignManualTaskDB.getCustomerId();
            //获取手动打包任务中的门店集合
            List<Long> wmPoiIdList = value.stream().map(x -> x.getWmPoiId()).collect(Collectors.toList());
            //对门店集合进行打包分组
            List<Set<Long>> groupByKp = wmCustomerKpGroupService.getGroupByKp(customerId, Sets.newHashSet(wmPoiIdList), KpTypeEnum.OPMANAGER,kpSignTaskTypeEnum);
            Map<Long, WmEcontractSignManualTaskDB> totalMap = value.stream().collect(Collectors.toMap(x -> x.getWmPoiId(), x -> x));
            Map<String, List<WmEcontractSignManualTaskDB>> subMap = null;
            List<WmEcontractSignManualTaskDB> subTaskDBList = null;
            //对分组分别提交手动打包任务
            for(Set<Long> group : groupByKp){
                subMap = Maps.newHashMap();
                subTaskDBList = Lists.newArrayList();
                for(Long wmPoiId : group){
                    subTaskDBList.add(totalMap.get(wmPoiId));
                }
                subMap.put(EcontractTaskApplyTypeEnum.POIFEE.getName(),subTaskDBList);
                LOGGER.info("#applyManualPackByGroup,subMap={}",JSONObject.toJSONString(subMap));
                if(isNewManualPack && isContainsNewTaskType){
                    manualBatchId = applyManualPackNew(subMap,commitUid,source);
                } else {
                    manualBatchId = applyManualPack(subMap,commitUid,source);
                }
            }
        }
        return manualBatchId;
    }

    private boolean checkOnlyDeliveryManualTask(
            Map<String, List<WmEcontractSignManualTaskDB>> taskMap) {
        for(Entry<String, List<WmEcontractSignManualTaskDB>> temp : taskMap.entrySet()){
            if(!EcontractTaskApplyTypeEnum.POIFEE.getName().equals(temp.getKey())){
                return false;
            }
        }
        return true;
    }

    private long initBatch(ManualPackNoticeContext context) {
        wmEcontractManualBatchBizService.initBatch(context);
        wmEcontractManualTaskBizService.updateManualTaskBatchId(context.getAllTaskInfo(),context.getManualBatchId());
        return context.getManualBatchId();
    }

    private void addSchedule(final ManualPackNoticeContext context) throws WmCustomerException,TException{
        if(CollectionUtils.isEmpty(context.getTaskInfo())){
            return;
        }
        LOGGER.info("#addSchedule,context={}",JSONObject.toJSONString(context));
        Map<String,List<Long>> taskInfo = context.getTaskInfo();
        Iterator<Entry<String, List<Long>>> iterator = taskInfo.entrySet().iterator();
        Entry<String, List<Long>> one = iterator.next();
        noticeModule(one,context);
        iterator.remove();
        if(iterator.hasNext()){
            scheduledthreadpool.schedule(new Runnable(){
                @Override
                public void run() {
                    try {
                        addSchedule(context);
                    } catch (WmCustomerException | TException e) {
                        LOGGER.error("noticeModule异常",e);
                    }
                }
            },estimateApplyInterval(one.getKey(),one.getValue(),context),TimeUnit.MILLISECONDS);
        }
    }

    private void addScheduleNew(final ManualPackNoticeContext context) throws WmCustomerException, TException {
        if (CollectionUtils.isEmpty(context.getTaskInfo())) {
            return;
        }
        LOGGER.info("#addScheduleNew,context={}", JSONObject.toJSONString(context));
        Map<String, List<Long>> taskInfo = context.getTaskInfo();
        for (Map.Entry<String, List<Long>> entry : taskInfo.entrySet()) {
            noticeModuleNew(entry.getKey(), entry.getValue(), context);
        }
    }

    private long estimateApplyInterval(String module, List<Long> taskIds, ManualPackNoticeContext context) {
        int deliveryApplyIntervalConfig = ConfigUtilAdapter.getInt("delivery_applyInterval", 60);
        int otherApplyIntervalConfig = ConfigUtilAdapter.getInt("other_applyInterval", 60);
        if (EcontractTaskApplyTypeEnum.POIFEE.getName().equals(module)) {
            return Math.max(taskIds.size() * deliveryApplyIntervalConfig, 1000);
        } else {
            return Math.max(context.getCustomerRelWmPoiIdCount() * otherApplyIntervalConfig, 1000);
        }
    }

    private void noticeModule(Entry<String, List<Long>> one, ManualPackNoticeContext context) throws WmCustomerException {
        LOGGER.info("#noticeModule,one={},context={}", JSONObject.toJSONString(one), JSONObject.toJSONString(context));
        //batch模块状态转为to_commit
        String status = WmEcontractConstant.TO_COMMIT;
        wmEcontractManualBatchBizService.retryUpdateModuleStatus(context.getManualBatchId(), one.getKey(), status);
        //调用业务接口
        try {
            //失效manual_task
            wmEcontractManualTaskBizService.batchDeleteManualTask(one.getValue());
            if (one.getKey().equals(EcontractTaskApplyTypeEnum.C1CONTRACT.getName())) {
                wmContractService.startC1SignByWaitingSign(context.getCustomerId(), context.getCommitUid(), "", context.getManualBatchId());
            } else if (one.getKey().equals(EcontractTaskApplyTypeEnum.SETTLE.getName())) {
                wmSettleInputCommitDomainService.commitSettleInfo(context.getCustomerId(), SignPackWay.DO_SIGN.getCode(), context.getCommitUid(), "",
                        context.getManualBatchId(),false);
            } else if (one.getKey().equals(EcontractTaskApplyTypeEnum.POIFEE.getName())) {
                if (!MccSignConfig.configPoifeeSignUseWmPoiLogisticsSignThriftService()) {
                    wmLogisticsFeeThriftService.signWmPoiAllFeeManualPackageInfo(one.getValue(), context.getManualBatchId(),
                            new OperateInfo().setOpId(context.getCommitUid()));
                } else {
                    Map<Long, Long> deliveryTaskWmPoiIdMap = context.getDeliveryTaskWmPoiIdMap();
                    if (CollectionUtils.isEmpty(deliveryTaskWmPoiIdMap)) {
                        LOGGER.error("deliveryTaskWmPoiIdMap为空");
                        throw new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR, "任务数据组装异常");
                    }
                    List<HeronContractManualSignItem> manualSignItemList = deliveryTaskWmPoiIdMap.entrySet().stream()
                            .map(x -> {
                                HeronContractManualSignItem signItem = new HeronContractManualSignItem();
                                signItem.setWmPoiId(x.getValue());
                                signItem.setManualConfirmId(x.getKey());
                                return signItem;
                            }).collect(Collectors.toList());
                    HeronContractSignGrayParam signGrayParam = new HeronContractSignGrayParam();
                    signGrayParam.setBatchManualConfirmId(context.getManualBatchId());
                    signGrayParam.setSignItemList(manualSignItemList);
                    if (wmLogisticsGatewayThriftServiceAdapter.isDeliverySignOperateUseNewIface(signGrayParam)) {
                        HeronContractOperator operator = HeronContractOperator.builder()
                                .opId((long) context.getCommitUid())
                                .build();
                        HeronContractManualBatchSignParam manualBatchSignParam = HeronContractManualBatchSignParam.builder()
                                .batchManualConfirmId(context.getManualBatchId())
                                .signItemList(manualSignItemList)
                                .operator(operator).build();
                        wmLogisticsGatewayThriftServiceAdapter.deliveryBatchApplySignUseNewIface(manualBatchSignParam);
                    } else {
                        List<ManualSignInfo> signInfoList = deliveryTaskWmPoiIdMap.entrySet().stream()
                                .map(x -> new ManualSignInfo(x.getValue(), x.getKey())).collect(Collectors.toList());
                        wmPoiLogisticsSignThriftService.signWmPoiAllFeeManualPackageInfo(
                                new BatchManualSignInfo(signInfoList, context.getManualBatchId()), new OperateInfo().setOpId(context.getCommitUid()));
                    }
                }
            }
        } catch (WmPoiLogisticsException e) {
            LOGGER.error("signWmPoiAllFeeManualPackageInfo异常", e);
            throw new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR, e.getMsg());
        } catch (TException e) {
            LOGGER.error("noticeModule#TException", e);
        } finally {
            manualTaskLockService.unLockManualTaskId(one.getValue());
        }
        // 异常-状态回退
    }

    private void noticeModuleNew(String module, List<Long> taskIds, ManualPackNoticeContext context) throws WmCustomerException {
        LOGGER.info("#noticeModuleNew,module={},taskIds={},context={}", module, taskIds, JSONObject.toJSONString(context));
        //获取任务业务模块的bizid
        List<WmEcontractSignManualTaskDB> manualTaskDBList = wmEcontractManualTaskBizService.batchGetByManualTaskIds(taskIds);
        List<Long> bizIdList = manualTaskDBList.stream().map(WmEcontractSignManualTaskDB::getWmPoiId).collect(Collectors.toList());
        LOGGER.info("#noticeModuleNew,bizIdList={}", JSONObject.toJSONString(bizIdList));
        //失效manual_task
        wmEcontractManualTaskBizService.batchDeleteManualTask(taskIds);
        //调用子模块接口
        try{
            noticeTaskTypeFactory.getNoticeTaskByMoudle(module).notice(module, bizIdList, context, taskIds);
        }catch(Exception e){
            LOGGER.error("notice子模块异常", e);
            throw new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR, "打包签约任务触发异常");
        }finally {
            manualTaskLockService.unLockManualTaskId(taskIds);
        }
    }


    public BooleanResult cancelManualTaskByCustomerIdAndModule(int wmCustomerId, String module) {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.service.sign.applytask.WmEcontractManualTaskApplyService.cancelManualTaskByCustomerIdAndModule(int,java.lang.String)");
        List<Long> task = wmEcontractManualTaskBizService
                .getManualTaskByWmCustomerIdAndModule(wmCustomerId, module);
        if (CollectionUtils.isEmpty(task)) {
            return new BooleanResult(true);
        }
        wmEcontractManualTaskBizService.deleteManualTask(wmCustomerId, module);
        return new BooleanResult(true);
    }

    public List<LongResult> getManualTaskIdByCustomerId(int wmCustomerId) {
        List<Long> taskIdList = wmEcontractManualTaskBizService
            .getManualTaskIdByCustomerId(wmCustomerId);
        List<LongResult> result = Lists.newArrayList();
        for (Long temp : taskIdList) {
            result.add(new LongResult(temp));
        }
        return result;
    }

    public List<LongResult> getManualTaskIdList(int wmCustomerId, int startTime) {
        List<Long> taskIdList = wmEcontractManualTaskBizService
            .getManualTaskIdList(wmCustomerId, startTime);
        List<LongResult> result = Lists.newArrayList();
        for (Long temp : taskIdList) {
            result.add(new LongResult(temp));
        }
        return result;
    }

    public List<WmEcontractSignManualTaskBo> getManualTaskInfoByCustomerId(int wmCustomerId) {
        List<WmEcontractSignManualTaskDB> taskInfoList = wmEcontractManualTaskBizService.getManualTaskInfoByCustomerId(
            wmCustomerId);
        List<WmEcontractSignManualTaskBo> result = Lists.newArrayList();
        if (CollectionUtils.isEmpty(taskInfoList)) {
            return result;
        }
        taskInfoList.stream().forEach(task -> {
            WmEcontractSignManualTaskBo taskBo = new WmEcontractSignManualTaskBo();
            taskBo.setId(task.getId());
            taskBo.setWmCustomerId(task.getCustomerId());
            taskBo.setWmPoiId(task.getWmPoiId());
            taskBo.setModule(task.getModule());
            taskBo.setCommitUid(task.getCommitUid());
            taskBo.setManualBatchId(task.getManualBatchId());
            taskBo.setCtime(task.getCtime());
            taskBo.setUtime(task.getUtime());
            result.add(taskBo);
        });
        LOGGER.info("getManualTaskInfoByCustomerId#result:{}", JSONObject.toJSONString(result));
        return result;
    }

    public Map<Long, List<Long>> getManualTaskIdByCustomerIdList(List<Long> wmCustomerIdList) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.meituan.waimai.customer.service.sign.applytask.WmEcontractManualTaskApplyService.getManualTaskIdByCustomerIdList(java.util.List)");
        if (CollectionUtils.isEmpty(wmCustomerIdList)) {
            return Maps.newHashMap();
        }

        List<Integer> idList = wmCustomerIdList.stream().map(Long::intValue).collect(Collectors.toList());
        List<WmEcontractSignManualTaskDB> wmEcontractSignManualTaskDBList = wmEcontractManualTaskBizService.getManualTaskIdByCustomerIdList(idList);
        Map<Long, List<Long>> resultMap = Maps.newHashMap();

        for (WmEcontractSignManualTaskDB wmEcontractSignManualTaskDB : wmEcontractSignManualTaskDBList) {
            List<Long> taskIdList = resultMap.get(Long.valueOf(wmEcontractSignManualTaskDB.getCustomerId()));
            if (CollectionUtils.isEmpty(taskIdList)) {
                List<Long> result = Lists.newArrayList();
                result.add(wmEcontractSignManualTaskDB.getId());
                resultMap.put(Long.valueOf(wmEcontractSignManualTaskDB.getCustomerId()), result);
            } else {
                taskIdList.add(wmEcontractSignManualTaskDB.getId());
                resultMap.put(Long.valueOf(wmEcontractSignManualTaskDB.getCustomerId()), taskIdList);
            }
        }
        return resultMap;
    }

    private Long applyEcontractRecordBatch(ManualPackNoticeContext context, Long bizId){
        ApplyBatchNumBo applyBatchNumBo = new ApplyBatchNumBo();
        applyBatchNumBo.setToken(EcontractUserToken.WAIMAI_CONTRACT);
        applyBatchNumBo.setBizId(bizId);
        applyBatchNumBo.setBizLine(getBizLine(!CollectionUtils.isEmpty(context.getDcContractContextList())));
        applyBatchNumBo.setCommitUid(context.getCommitUid());
        applyBatchNumBo.setCustomerId(context.getCustomerId());
        applyBatchNumBo.setForceAllOp(1);//0:部分签约，1:全签
        EcontractAPIResponse resp = econtractAPIService.applyEcontractBatchNum(applyBatchNumBo);
        String batchNum = resp.getReturnData().get(EcontractAPIResponseConstant.BATCH_NUM);
        return Long.valueOf(batchNum);
    }

    private Integer getBizLine(boolean dcContract) {
        if (dcContract) {
            return BatchSignBizLineEnum.DINE_IN_SERVICE.getCode();
        }
        return BatchSignBizLineEnum.WM.getCode();
    }

    /**
     * 根据manualtaskId获取applyType
     * @param manualTaskId
     * @return
     */
    public String getMoudleById(Integer manualTaskId){
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.meituan.waimai.customer.service.sign.applytask.WmEcontractManualTaskApplyService.getMoudleById(java.lang.Integer)");
        return wmEcontractManualTaskBizService.getMoudleByManualTaskId(Long.valueOf(manualTaskId));
    }

    public WmEcontractSignManualTaskDB getManualTaskByManualTaskId(Long manualTaskId){
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.meituan.waimai.customer.service.sign.applytask.WmEcontractManualTaskApplyService.getManualTaskByManualTaskId(java.lang.Long)");
        return wmEcontractManualTaskBizService.getManualTaskByManualTaskId(manualTaskId);
    }
    
    public C1ExpireAfterRenewalResultBo applyTaskForRenewalC1ExpireAfter(ManualTaskApplyBo manualTaskApplyBo) throws WmCustomerException {
        try {
            return wmEcontractC1AutoRenewalService.applyTaskForRenewalC1ExpireAfter(manualTaskApplyBo);
        } catch (Exception e) {
            LOGGER.error("WmEcontractC1AutoRenewalService#applyTaskForRenewalC1ExpireAfter 发起C1到期后违约金续签打包签约任务失败，manualTaskApplyBo:{}", JSON.toJSONString(manualTaskApplyBo), e);
            throw new WmCustomerException(CustomerErrorCodeConstants.BUSINESS_ERROR, "发起C1到期后违约金续签打包签约任务失败");
        }
        
    }
}
