package com.sankuai.meituan.waimai.customer.service.sc;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.meituan.mtrace.thread.TraceRunnable;
import com.sankuai.meituan.waimai.customer.adapter.BmLbsAoiExtThriftServiceAdapter;
import com.sankuai.meituan.waimai.customer.adapter.WmEmployClient;
import com.sankuai.meituan.waimai.customer.adapter.WmHighseasThriftServiceAdapter;
import com.sankuai.meituan.waimai.customer.constant.sc.ScCommonConstant;
import com.sankuai.meituan.waimai.customer.dao.sc.*;
import com.sankuai.meituan.waimai.customer.ddd.adapter.WmPoiQueryAdapter;
import com.sankuai.meituan.waimai.customer.domain.sc.*;
import com.sankuai.meituan.waimai.customer.domain.sc.area.WmScPoint;
import com.sankuai.meituan.waimai.customer.domain.sc.canteenstall.WmCanteenStallBindDO;
import com.sankuai.meituan.waimai.customer.service.sc.area.WmRtreeUtil;
import com.sankuai.meituan.waimai.customer.service.sc.canteenstall.WmCanteenStallBindService;
import com.sankuai.meituan.waimai.infra.domain.WmEmploy;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.*;
import com.sankuai.meituan.waimai.thrift.customer.domain.sc.*;
import com.sankuai.meituan.waimai.thrift.customer.domain.sc.dto.WmScSchoolAoiDTO;
import com.sankuai.meituan.waimai.thrift.domain.WmPoiAggre;
import com.sankuai.meituan.waimai.thrift.domain.WmPoiHighSeasPoiInfo;
import com.sankuai.meituan.waimai.thrift.exception.WmSchCantException;
import deps.redis.clients.util.CollectionUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.thrift.TException;
import org.json.JSONArray;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;

import static com.sankuai.meituan.waimai.thrift.constants.WmPoiFieldQueryConstant.*;
import static com.sankuai.meituan.waimai.thrift.customer.constant.sc.ScConstants.SC_SCHOOLAREA_LOG;
import static com.sankuai.meituan.waimai.thrift.customer.constant.sc.WmScCodeConstants.*;

/**
 * 学校范围逻辑
 */
@Slf4j
@Service
public class WmScSchoolAreaService {

    @Autowired
    private WmSchoolServerService wmSchoolServerService;

    @Autowired
    private WmScSchoolAreaMapper wmScSchoolAreaMapper;

    @Autowired
    private WmSchoolMapper wmSchoolMapper;

    @Autowired
    private WmCanteenMapper wmCanteenMapper;

    @Autowired
    private CheckAreaInfoService checkAreaInfoService;

    @Autowired
    private WmScLogSchoolInfoService wmScLogSchoolInfoService;

    @Autowired
    private WmEmployClient wmEmployClient;

    @Autowired
    private WmPoiQueryAdapter wmPoiQueryAdapter;

    @Autowired
    private WmScSchoolBuildingService wmScSchoolBuildingService;

    @Autowired
    private WmScSchoolBuildingMapper wmScSchoolBuildingMapper;

    @Autowired
    private BmLbsAoiExtThriftServiceAdapter bmLbsAoiExtThriftServiceAdapter;

    @Autowired
    private WmCanteenStallBindService wmCanteenStallBindService;

    @Autowired
    private WmHighseasThriftServiceAdapter wmHighseasThriftServiceAdapter;


    private static final int SUBMIT_SC_AREA_SUCCESS_CODE = 0;

    /**
     * 门店删除
     */
    private static final int WM_POI_ID_IS_DELETE = 1;

    private final ExecutorService executorService = new ThreadPoolExecutor(10, 30, 5L, TimeUnit.SECONDS,
            new LinkedBlockingQueue<Runnable>(200),
            new ThreadFactoryBuilder().setNameFormat("WmScSchoolAreaService" + "-pool-%d").build(),
            new RejectedExecutionHandler() {
                @Override
                public void rejectedExecution(Runnable r, ThreadPoolExecutor executor) {
                    if (!executorService.isShutdown()) {
                        try {
                            executor.getQueue().put(r);
                        } catch (InterruptedException e) {
                            throw new RejectedExecutionException("Reject from " + executor.toString());
                        }
                    } else {
                        throw new RejectedExecutionException("Executor " + executor.toString() + " have shutdown.");
                    }
                }
    });

    /**
     * 根据学校主键id查询学校范围V2
     * @param schoolPrimaryId 学校主键Id
     * @param type 查看标签 0->查看界面 1->编辑界面
     * @return SchoolAreaBo
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    public SchoolAreaBo getSchoolAreaByIdV2(int schoolPrimaryId, int type) throws WmSchCantException {
        log.info("[WmScSchoolAreaService.getSchoolAreaByIdV2] input param: schoolPrimaryId = {}, type = {}", schoolPrimaryId, type);
        if (SchoolInfoTypeEnum.getByType(type) == null) {
            throw new WmSchCantException(BIZ_PARA_ERROR, "查看标签类型错误");
        }
        // 根据学校主键盘ID查询学校信息
        SchoolBo schoolBo = wmSchoolServerService.selectSchoolById(schoolPrimaryId);
        if (schoolBo == null || schoolBo.getId() != schoolPrimaryId) {
            throw new WmSchCantException(SCHOOL_ID_ERROR, "未查询到学校信息");
        }
        // 将数据赋值给schoolAreaBo
        SchoolAreaBo schoolAreaBo = new SchoolAreaBo();
        schoolAreaBo.setCityId(schoolBo.getCityId());
        schoolAreaBo.setSchoolId(schoolPrimaryId);
        schoolAreaBo.setSchoolName(schoolBo.getSchoolName());
        schoolAreaBo.setCityName(schoolBo.getCityName());
        schoolAreaBo.setSchoolAddress(schoolBo.getSchoolAddress());
        schoolAreaBo.setResponsiblePerson(schoolBo.getResponsiblePerson());
        schoolAreaBo.setArea(Arrays.toString(new String[]{}));
        schoolAreaBo.setAggreOrderAllowDelivery(schoolBo.getAggreOrderAllowDelivery());
        // 设置学校范围AOI信息
        schoolAreaBo = setSchoolAoiListInfo(schoolAreaBo, schoolPrimaryId);
        return schoolAreaBo;
    }

    /**
     * 设置学校范中的AOI信息
     * @param schoolAreaBo schoolAreaBo
     * @param schoolPrimaryId 学校主键ID
     */
    public SchoolAreaBo setSchoolAoiListInfo(SchoolAreaBo schoolAreaBo, int schoolPrimaryId) {
        List<WmScSchoolAreaDO> wmScSchoolAreaDOList = wmScSchoolAreaMapper.selectBySchoolId(schoolPrimaryId);
        // 该学校无范围信息-状态设置为待录入
        if (CollectionUtils.isEmpty(wmScSchoolAreaDOList)) {
            schoolAreaBo.setAuditStatus((int) SchoolAuditStatusEnum.FORENTRY.getType());
            schoolAreaBo.setAuditStatusDesc(SchoolAuditStatusEnum.FORENTRY.getName());
            schoolAreaBo.setWmScSchoolAoiDTOList(new ArrayList<>());
            return schoolAreaBo;
        }
        schoolAreaBo.setWmScSchoolAoiDTOList(WmScSchoolAoiBO.transWmScSchoolAoiBoToDto(getWmScSchoolAoiBOList(wmScSchoolAreaDOList)));
        schoolAreaBo.setAuditStatus((int) SchoolAuditStatusEnum.PASS.getType());
        schoolAreaBo.setAuditStatusDesc(SchoolAuditStatusEnum.PASS.getName());
        return schoolAreaBo;
    }

    /**
     * 根据wmScSchoolAreaDOList转换得到WmScSchoolAoiBO列表
     * @param wmScSchoolAreaDOList wmScSchoolAreaDOList
     * @return List<WmScSchoolAoiBO>
     */
    public List<WmScSchoolAoiBO> getWmScSchoolAoiBOList(List<WmScSchoolAreaDO> wmScSchoolAreaDOList) {
        log.info("[getWmScSchoolAoiBOList] wmScSchoolAreaDOList = {}", JSONObject.toJSONString(wmScSchoolAreaDOList));
        List<WmScSchoolAoiBO> wmScSchoolAoiBOList = new ArrayList<>();
        if (CollectionUtils.isEmpty(wmScSchoolAreaDOList)) {
            return wmScSchoolAoiBOList;
        }
        for (WmScSchoolAreaDO wmScSchoolAreaDO : wmScSchoolAreaDOList) {
            WmScSchoolAoiBO wmScSchoolAoiBO = new WmScSchoolAoiBO();
            wmScSchoolAoiBO.setAoiId(wmScSchoolAreaDO.getAoiId());
            wmScSchoolAoiBO.setAoiName(wmScSchoolAreaDO.getAoiName());
            if (StringUtils.isBlank(wmScSchoolAreaDO.getArea())) {
                wmScSchoolAoiBO.setAoiArea("[]");
            } else {
                wmScSchoolAoiBO.setAoiArea(wmScSchoolAreaDO.getArea());
            }
            wmScSchoolAoiBO.setAoiMode(wmScSchoolAreaDO.getAoiMode());
            // 设置通行属性枚举值(若未获取到, 则展示"-")
            SchoolAoiModeEnum schoolAoiModeEnum = SchoolAoiModeEnum.getByType(wmScSchoolAreaDO.getAoiMode());
            wmScSchoolAoiBO.setAoiModeDesc(schoolAoiModeEnum == null ? "-" : schoolAoiModeEnum.getName());
            wmScSchoolAoiBOList.add(wmScSchoolAoiBO);
        }
        return wmScSchoolAoiBOList;
    }

    /**
     * 保存学校范围信息-前置校验
     * @param schoolAreaBo 学校范围信息BO
     * @return 提示语
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     * @throws TException org.apache.thrift.TException
     */
    public List<String> checkSchoolAreaList(SchoolAreaBo schoolAreaBo) throws WmSchCantException, TException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_1", "com.sankuai.meituan.waimai.customer.service.sc.WmScSchoolAreaService.checkSchoolAreaList(com.sankuai.meituan.waimai.thrift.customer.domain.sc.SchoolAreaBo)");
        log.info("[WmScSchoolAreaService.checkSchoolAreaList] input param: schoolAreaBo = {}",
                JSONObject.toJSONString(schoolAreaBo));
        // 学校范围信息基础校验
        checkSchoolAoiInfo(schoolAreaBo);

        List<String> resInfoList = new ArrayList<>();
        // 不在学校范围内的门店列表
        List<WmCanteenStallBindDO> outOfScPoiList = getPoiListNotInSchoolAreasV2(schoolAreaBo);

        // 不在学校范围内的线索列表
        List<WmCanteenStallBindDO> outOfScClueList = getClueListNotInSchoolAreas(schoolAreaBo);
        if (CollectionUtils.isNotEmpty(outOfScPoiList) || CollectionUtils.isNotEmpty(outOfScClueList)) {
            String info = makePoiAndClueUnbindTips(outOfScPoiList, outOfScClueList);
            info = info + "\\n\\n" + "请确认流程是否继续？";
            resInfoList.add(info);
        }

        // 查询不在学校范围内/外的楼宇列表
        List<WmScSchoolBuildingDO> wmScSchoolBuildingDOListNotMatch = wmScSchoolBuildingService.getSchoolBuildingWithLocationNotMatch(
                schoolAreaBo.getSchoolId(), schoolAreaBo.getWmScSchoolAoiDTOList());
        if (CollectionUtils.isEmpty(wmScSchoolBuildingDOListNotMatch)) {
            log.info("[WmScSchoolAreaService.checkSchoolAreaList] wmScSchoolBuildingDOListNotMatch is empty, return. schoolAreaBo = {}",
                    JSONObject.toJSONString(schoolAreaBo));
            return resInfoList;
        }

        String buidlingInfo = composeBuildingInfoTips(wmScSchoolBuildingDOListNotMatch);
        resInfoList.add(buidlingInfo);
        return resInfoList;
    }

    /**
     * 学校范围修改导致学校楼宇位置变更提示
     * @param wmScSchoolBuildingDOListNotMatch wmScSchoolBuildingDOListNotMatch
     * @return 提示Tips
     */
    private String composeBuildingInfoTips(List<WmScSchoolBuildingDO> wmScSchoolBuildingDOListNotMatch) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.service.sc.WmScSchoolAreaService.composeBuildingInfoTips(java.util.List)");
        // 调整前范围内-调整后范围外的楼宇列表
        List<WmScSchoolBuildingDO> schoolBuildingListIn = wmScSchoolBuildingDOListNotMatch.stream()
                .filter(buildingDO -> buildingDO.getBuildingLocation() == SchoolBuildingLocationEnum.INSCHOOL.getType())
                .collect(Collectors.toList());
        // 需展示的楼宇名称列表
        List<String> buildingNameListIn = schoolBuildingListIn.stream()
                .map(WmScSchoolBuildingDO::getBuildingName)
                .limit(3)
                .collect(Collectors.toList());
        // 调整前范围外-调整后范围内的楼宇列表
        List<WmScSchoolBuildingDO> schoolBuildingListOut = wmScSchoolBuildingDOListNotMatch.stream()
                .filter(buildingDO -> buildingDO.getBuildingLocation() == SchoolBuildingLocationEnum.OUTOFSCHOOL.getType())
                .collect(Collectors.toList());
        // 需展示的楼宇名称列表
        List<String> buildingNameListOut = schoolBuildingListOut.stream()
                .map(WmScSchoolBuildingDO::getBuildingName)
                .limit(3)
                .collect(Collectors.toList());

        String buidlingInfo = "按照此调整区域范围，将会使【";
        if (CollectionUtils.isNotEmpty(schoolBuildingListIn)) {
            buidlingInfo = buidlingInfo + StringUtils.join(buildingNameListIn, "、")
                    + "】等" + schoolBuildingListIn.size() + "个楼宇位置由校内变更为校外；";
        }

        if (CollectionUtils.isNotEmpty(schoolBuildingListOut)) {
            buidlingInfo = buidlingInfo + "【" + StringUtils.join(buildingNameListOut, "、")
                    + "】等" + schoolBuildingListOut.size() + "个楼宇位置由校外变更为校内；";
        }

        buidlingInfo = buidlingInfo + "\\n请确认流程是否继续？";
        return buidlingInfo;
    }

    /**
     * 保存学校范围信息V4
     * @param schoolAreaBo schoolAreaBo
     * @return WmModifyScAreaCallDo
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     * @throws TException org.apache.thrift.TException
     */
    public WmModifyScAreaCallDo submitSchoolAreaV4(SchoolAreaBo schoolAreaBo) throws WmSchCantException, TException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.sc.WmScSchoolAreaService.submitSchoolAreaV4(com.sankuai.meituan.waimai.thrift.customer.domain.sc.SchoolAreaBo)");
        log.info("[WmScSchoolAreaService.submitSchoolAreaV4] input param: schoolAreaBo = {},", JSONObject.toJSONString(schoolAreaBo));
        WmModifyScAreaCallDo wmModifyScAreaCallDo = new WmModifyScAreaCallDo();
        wmModifyScAreaCallDo.setCode(SUBMIT_SC_AREA_SUCCESS_CODE);
        // 校验学校范围信息
        checkSchoolAoiInfo(schoolAreaBo);
        WmEmploy wmEmploy = wmEmployClient.getEmployById(schoolAreaBo.getCuid().intValue());
        if (wmEmploy == null) {
            throw new WmSchCantException(BIZ_PARA_ERROR, "当前登录人信息不存在");
        }

        // 不在学校范围内的门店列表
        List<WmCanteenStallBindDO> outOfScPoiList = getPoiListNotInSchoolAreasV2(schoolAreaBo);

        // 不在学校范围内的线索列表
        List<WmCanteenStallBindDO> outOfScClueList = getClueListNotInSchoolAreas(schoolAreaBo);
        if (CollectionUtils.isNotEmpty(outOfScPoiList) || CollectionUtils.isNotEmpty(outOfScClueList)) {
            List<WmCanteenStallBindDO> bindDOList = new ArrayList<>(outOfScPoiList);
            bindDOList.addAll(outOfScClueList);
            // 去重的解绑档口绑定任务
            Set<Integer> hashSet = new HashSet<>();
            List<WmCanteenStallBindDO> distinctBindDOList = bindDOList.stream()
                    .filter(bindDO -> hashSet.add(bindDO.getId()))
                    .collect(Collectors.toList());

            // 解绑档口绑定任务和线索门店(异步)
            unbindClueAndWmPoiBySchoolAreaAsync(distinctBindDOList, wmEmploy.getUid(), wmEmploy.getName() + "(" + wmEmploy.getMisId() + ")");

            // 记录操作日志(学校)
            recordUnbindClueAndWmPoiBySchoolAreaLog(outOfScPoiList, outOfScClueList, distinctBindDOList, wmEmploy, schoolAreaBo.getSchoolId());
        }

        // 查询不在学校范围内/外的楼宇列表
        List<WmScSchoolBuildingDO> wmScSchoolBuildingDOListNotMatch = wmScSchoolBuildingService.getSchoolBuildingWithLocationNotMatch(
                schoolAreaBo.getSchoolId(), schoolAreaBo.getWmScSchoolAoiDTOList());
        // 将楼宇位置校内->校外; 校外->校内
        if (CollectionUtils.isNotEmpty(wmScSchoolBuildingDOListNotMatch)) {
            wmScSchoolBuildingService.updateSchoolBuildingLocation(wmScSchoolBuildingDOListNotMatch,
                    wmEmploy.getUid(), wmEmploy.getName() + "(" + wmEmploy.getMisId() + ")");
        }
        // 保存学校范围信息
        wmModifyScAreaCallDo.setDataOrMsg(String.valueOf(saveSchoolArea(schoolAreaBo)));
        // 重新计算并更新学校楼宇状态(异步)
        updateSchoolBuildingStatusBySchoolAreaAsync(schoolAreaBo);

        return wmModifyScAreaCallDo;
    }

    /**
     * 不在学校范围内的档口绑定任务解绑(异步)
     * @param bindDOList 档口绑定任务列表
     * @param userId 用户ID
     * @param userName 用户名称
     */
    private void unbindClueAndWmPoiBySchoolAreaAsync(List<WmCanteenStallBindDO> bindDOList, Integer userId, String userName) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_1", "com.sankuai.meituan.waimai.customer.service.sc.WmScSchoolAreaService.unbindClueAndWmPoiBySchoolAreaAsync(java.util.List,java.lang.Integer,java.lang.String)");
        for (WmCanteenStallBindDO bindDO : bindDOList) {
            executorService.execute(new TraceRunnable(new Runnable() {
                @Override
                public void run() {
                    try {
                        wmCanteenStallBindService.unbindClueAndWmPoi(bindDO, userId, userName, "学校范围变更解绑");
                    } catch (Exception e) {
                        log.error("[WmScSchoolAreaService.unbindClueAndWmPoiBySchoolAreaAsync] Exception. bindDOList = {}, userId = {}, userName = {}",
                                JSONObject.toJSONString(bindDOList), userId, userName, e);
                    }
                }
            }));
        }
    }

    /**
     * 重新计算并更新学校楼宇状态(异步)
     * @param schoolAreaBo schoolAreaBo
     */
    private void updateSchoolBuildingStatusBySchoolAreaAsync(SchoolAreaBo schoolAreaBo) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.service.sc.WmScSchoolAreaService.updateSchoolBuildingStatusBySchoolAreaAsync(com.sankuai.meituan.waimai.thrift.customer.domain.sc.SchoolAreaBo)");
        executorService.execute(new TraceRunnable(new Runnable() {
            @Override
            public void run() {
                try {
                    wmScSchoolBuildingService.updateSchoolBuildingStatusBySchoolArea(schoolAreaBo);
                } catch (Exception e) {
                    log.error("[WmScSchoolAreaService.submitSchoolAreaV4] updateSchoolBuildingStatus failed. schoolAreaBo = {}",
                            JSONObject.toJSONString(schoolAreaBo), e);
                }
            }
        }));
    }

    /**
     * 记录学校范围变更引起档口绑定任务解绑操作日志
     * @param outOfScPoiList 不在学校范围内的门店列表
     * @param outOfScClueList 不在学校范围内的线索列表
     * @param bindDOList 去重的解绑档口绑定任务
     * @param wmEmploy wmEmploy
     * @param schoolPrimaryId 学校主键ID
     */
    private void recordUnbindClueAndWmPoiBySchoolAreaLog(List<WmCanteenStallBindDO> outOfScPoiList,
                                                         List<WmCanteenStallBindDO> outOfScClueList,
                                                         List<WmCanteenStallBindDO> bindDOList,
                                                         WmEmploy wmEmploy,
                                                         Integer schoolPrimaryId) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.service.sc.WmScSchoolAreaService.recordUnbindClueAndWmPoiBySchoolAreaLog(List,List,List,WmEmploy,Integer)");
        if (CollectionUtils.isEmpty(bindDOList)) {
            return;
        }

        String logInfo = wmScLogSchoolInfoService.composeUnbindClueAndWmPoiBySchoolAreaLog(outOfScPoiList, outOfScClueList, bindDOList);
        wmScLogSchoolInfoService.insertScOptLog(OptTypeEnum.UNBAND.getType(), SC_SCHOOLAREA_LOG, schoolPrimaryId,
                wmEmploy.getUid(), wmEmploy.getName() + "(" + wmEmploy.getMisId() + ")", logInfo, "");
    }

    /**
     * 组装门店解绑食堂提示文案
     * @param outOfScPoiList 不在学校范围内的门店列表
     * @param outOfScClueList 不在学校范围内的线索列表
     * @return 门店解绑食堂提示文案
     */
    private String makePoiAndClueUnbindTips(List<WmCanteenStallBindDO> outOfScPoiList, List<WmCanteenStallBindDO> outOfScClueList)
            throws WmSchCantException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_1", "com.sankuai.meituan.waimai.customer.service.sc.WmScSchoolAreaService.makePoiAndClueUnbindTips(java.util.List,java.util.List)");
        String msg = "学校范围更改后有" + outOfScPoiList.size() + "个档口、" + outOfScClueList.size() +
                "条线索坐标计算在校外，会触发线索、门店的解绑，门店会掉标" + "\\n\\n";

        return msg + composeWmPoiUnbindTips(outOfScPoiList) +
                composeWdcClueUnbindTips(outOfScClueList) +
                composeStallBindUnbindTips(outOfScPoiList, outOfScClueList);
    }


    private String composeStallBindUnbindTips(List<WmCanteenStallBindDO> outOfScPoiList, List<WmCanteenStallBindDO> outOfScClueList) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.sc.WmScSchoolAreaService.composeStallBindUnbindTips(java.util.List,java.util.List)");
        List<WmCanteenStallBindDO> bindDOList = new ArrayList<>();
        bindDOList.addAll(outOfScPoiList);
        bindDOList.addAll(outOfScClueList);

        // 去重的解绑档口绑定任务
        Set<Integer> hashSet = new HashSet<>();
        List<WmCanteenStallBindDO> distinctBindDOList = bindDOList.stream()
                .filter(bindDO -> hashSet.add(bindDO.getId()))
                .collect(Collectors.toList());

        List<Integer> bindIdList = distinctBindDOList.stream()
                .sorted(Comparator.comparing(WmCanteenStallBindDO::getId))
                .map(WmCanteenStallBindDO::getId)
                .collect(Collectors.toList());
        String msg = "需解绑档口绑定任务" + bindIdList.size() + "条：" + StringUtils.join(bindIdList, "、") + "；\\n";

        List<Long> wmPoiIdList = distinctBindDOList.stream()
                .map(WmCanteenStallBindDO::getWmPoiId)
                .filter(wmPoiId -> wmPoiId > 0)
                .collect(Collectors.toList());
        msg = msg + "需解绑外卖门店" + wmPoiIdList.size() + "个；";

        List<Long> wdcClueIdList = distinctBindDOList.stream()
                .map(WmCanteenStallBindDO::getWdcClueId)
                .filter(wdcClueId -> wdcClueId > 0)
                .collect(Collectors.toList());
        msg = msg + "需解绑线索" + wdcClueIdList.size() + "个";
        return msg;
    }


    private String composeWmPoiUnbindTips(List<WmCanteenStallBindDO> outOfScPoiList) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_1", "com.sankuai.meituan.waimai.customer.service.sc.WmScSchoolAreaService.composeWmPoiUnbindTips(java.util.List)");
        List<Long> wmPoiIdList = outOfScPoiList.stream()
                .map(WmCanteenStallBindDO::getWmPoiId)
                .collect(Collectors.toList());

        List<WmPoiAggre> wmPoiAggreList = wmPoiQueryAdapter.getWmPoiAggre(wmPoiIdList,
                Sets.newHashSet(WM_POI_FIELD_WM_POI_ID, WM_POI_FIELD_NAME));

        String msg = "范围外的外卖门店（" + wmPoiAggreList.size() + "个）：";
        List<String> outPoiNameList = wmPoiAggreList.stream()
                .map(wmPoiAggre -> wmPoiAggre.getName() + "（" + wmPoiAggre.getWm_poi_id() + "）")
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(outPoiNameList)) {
            msg = msg + StringUtils.join(outPoiNameList, "、");
        }

        return msg + "\\n";
    }

    private String composeWdcClueUnbindTips(List<WmCanteenStallBindDO> outOfScClueList) throws WmSchCantException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.sc.WmScSchoolAreaService.composeWdcClueUnbindTips(java.util.List)");
        List<Long> wdcClueIdList = outOfScClueList.stream()
                .map(WmCanteenStallBindDO::getWdcClueId)
                .collect(Collectors.toList());

        List<WmPoiHighSeasPoiInfo> infoList = wmHighseasThriftServiceAdapter.getWdcClueListByWdcCludIdList(wdcClueIdList);

        String msg = "范围外的线索（" + infoList.size() + "个）：";
        List<String> outClueNameList = infoList.stream()
                .map(info -> info.getPointName() + "（" + info.getPoiId() + "）")
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(outClueNameList)) {
            msg = msg + StringUtils.join(outClueNameList, "、");
        }

        return msg + "\\n";
    }

    /**
     * 查询不在学校范围内的门店列表V2
     * @param schoolAreaBo schoolAreaBo
     * @return List<WmPoiAggre> 不在学校范围内的门店列表
     */
    private List<WmCanteenStallBindDO> getPoiListNotInSchoolAreasV2(SchoolAreaBo schoolAreaBo) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.service.sc.WmScSchoolAreaService.getPoiListNotInSchoolAreasV2(com.sankuai.meituan.waimai.thrift.customer.domain.sc.SchoolAreaBo)");
        log.info("[WmScSchoolAreaService.getPoiListNotInSchoolAreasV2] input param: schoolAreaBo = {}", JSONObject.toJSONString(schoolAreaBo));
        // 1-根据学校主键ID查询关联的门店绑定状态为"绑定成功"的档口绑定任务
        List<WmCanteenStallBindDO> bindDOList = wmCanteenStallBindService.getStallBindListBySchoolPrimaryIdWithWmPoiBindSuccess(schoolAreaBo.getSchoolId());
        if (CollectionUtils.isEmpty(bindDOList)) {
            return new ArrayList<>();
        }

        // 2-组装门店ID列表
        List<Long> wmPoiIdList = bindDOList.stream()
                .map(WmCanteenStallBindDO::getWmPoiId)
                .collect(Collectors.toList());

        log.info("[WmScSchoolAreaService.getPoiListNotInSchoolAreasV2] schoolPrimaryId = {}, wmPoiIdList = {}", schoolAreaBo.getSchoolId(), JSONObject.toJSONString(wmPoiIdList));
        List<WmPoiAggre> wmPoiAggreList = wmPoiQueryAdapter.getWmPoiAggre(wmPoiIdList,
                Sets.newHashSet(
                        WM_POI_FIELD_WM_POI_ID,
                        WM_POI_FIELD_NAME,
                        WM_POI_FIELD_LATITUDE,
                        WM_POI_FIELD_LONGITUDE,
                        WM_POI_FIELD_IS_DELETE));

        List<String> schoolAreaList = schoolAreaBo.getWmScSchoolAoiDTOList().stream()
                .map(WmScSchoolAoiDTO::getAoiArea)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toList());

        List<Long> outOfScPoiIdList = Lists.newArrayList();
        for (WmPoiAggre wmPoiAggre : wmPoiAggreList) {
            // 外卖门店如已释放，则不参与校验
            if (wmPoiAggre.getIs_delete() == WM_POI_ID_IS_DELETE) {
                continue;
            }
            if (!WmRtreeUtil.withinAreaList((int) wmPoiAggre.getLatitude(), (int) wmPoiAggre.getLongitude(), schoolAreaList)) {
                outOfScPoiIdList.add(wmPoiAggre.getWm_poi_id());
            }
        }

        return bindDOList.stream()
                .filter(bindDO -> outOfScPoiIdList.contains(bindDO.getWmPoiId()))
                .collect(Collectors.toList());
    }

    /**
     * 查询不在学校范围内的线索列表
     * @param schoolAreaBo schoolAreaBo
     * @return List<WmPoiHighSeasPoiInfo> 不在学校范围内的线索列表
     */
    private List<WmCanteenStallBindDO> getClueListNotInSchoolAreas(SchoolAreaBo schoolAreaBo) throws WmSchCantException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.service.sc.WmScSchoolAreaService.getClueListNotInSchoolAreas(com.sankuai.meituan.waimai.thrift.customer.domain.sc.SchoolAreaBo)");
        log.info("[WmScSchoolAreaService.getClueListNotInSchoolAreas] input param: schoolAreaBo = {}", JSONObject.toJSONString(schoolAreaBo));
        // 1-根据学校主键ID查询关联的线索绑定状态为"绑定成功"/"绑定中"的档口绑定任务
        List<WmCanteenStallBindDO> bindDOList = wmCanteenStallBindService.getStallBindListBySchoolPrimaryIdWithClueBindingOrSuccess(schoolAreaBo.getSchoolId());
        if (CollectionUtils.isEmpty(bindDOList)) {
            log.info("[WmScSchoolAreaService.getClueListNotInSchoolAreas] bindDOList = {}", JSONObject.toJSONString(bindDOList));
            return new ArrayList<>();
        }

        // 2-组装线索ID列表和学校范围列表
        List<Long> wdcClueIdList = bindDOList.stream()
                .map(WmCanteenStallBindDO::getWdcClueId)
                .collect(Collectors.toList());

        List<String> schoolAreaList = schoolAreaBo.getWmScSchoolAoiDTOList().stream()
                .map(WmScSchoolAoiDTO::getAoiArea)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toList());

        // 3-根据线索ID列表批量查询线索信息
        List<WmPoiHighSeasPoiInfo> infoList = wmHighseasThriftServiceAdapter.getWdcClueListByWdcCludIdList(wdcClueIdList);
        List<Long> outOfScClueIdList = new ArrayList<>();
        for (WmPoiHighSeasPoiInfo poiInfo : infoList) {
            if (!WmRtreeUtil.withinAreaList((int) poiInfo.getLatitude(), (int) poiInfo.getLongitude(), schoolAreaList)) {
                outOfScClueIdList.add(poiInfo.getPoiId());
            }
        }

        return bindDOList.stream()
                .filter(bindDO -> outOfScClueIdList.contains(bindDO.getWdcClueId()))
                .collect(Collectors.toList());
    }

    /**
     * 校验学校范围数据
     * @param schoolAreaBo schoolAreaBo
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    private void checkSchoolAoiInfo(SchoolAreaBo schoolAreaBo) throws WmSchCantException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_1", "com.sankuai.meituan.waimai.customer.service.sc.WmScSchoolAreaService.checkSchoolAoiInfo(com.sankuai.meituan.waimai.thrift.customer.domain.sc.SchoolAreaBo)");
        log.info("WmScSchoolAreaService.checkSchoolAoiInfo input param: schoolAreaBo = {}", JSONObject.toJSONString(schoolAreaBo));
        if (schoolAreaBo == null || schoolAreaBo.getSchoolId() <= 0) {
            throw new WmSchCantException(BIZ_PARA_ERROR, "学校ID必填");
        }

        List<WmScSchoolAoiDTO> wmScSchoolAoiDTOList = schoolAreaBo.getWmScSchoolAoiDTOList();
        if (CollectionUtils.isEmpty(wmScSchoolAoiDTOList)) {
            throw new WmSchCantException(BIZ_PARA_ERROR, "学校范围不能为空");
        }

        List<CheckAreaInfoBo> checkAreaInfoBos = new ArrayList<>();
        for (WmScSchoolAoiDTO wmScSchoolAoiDTO : wmScSchoolAoiDTOList) {
            JSONArray jsonArray = new JSONArray(wmScSchoolAoiDTO.getAoiArea());
            if (jsonArray.length() < 3) {
                throw new WmSchCantException(BIZ_PARA_ERROR, "学校范围地图点必须大于2个");
            }
            CheckAreaInfoBo checkAreaInfoBo = new CheckAreaInfoBo();
            checkAreaInfoBo.setArea(wmScSchoolAoiDTO.getAoiArea());
            checkAreaInfoBos.add(checkAreaInfoBo);
        }
        // 校验学校范围是否合法
        SchoolCheckResult schoolCheckResult = checkAreaInfoService.checkSchoolAoiInfo(schoolAreaBo.getSchoolId(), checkAreaInfoBos);
        log.info("WmScSchoolAreaService.checkSchoolAoiInfo schoolCheckResult = {}", JSONObject.toJSONString(schoolCheckResult));
        if (schoolCheckResult != null && StringUtils.isNotEmpty(schoolCheckResult.getMsgs())) {
            // 校验失败
            throw new WmSchCantException(BIZ_PARA_ERROR, String.valueOf(schoolCheckResult.getMsgs()));
        }
    }

    /**
     * 保存学校范围信息-学校基建升级
     * @param schoolAreaBo schoolAreaBo
     * @return 修改行数
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    public int saveSchoolArea(SchoolAreaBo schoolAreaBo) throws WmSchCantException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.service.sc.WmScSchoolAreaService.saveSchoolArea(com.sankuai.meituan.waimai.thrift.customer.domain.sc.SchoolAreaBo)");
        log.info("[WmScSchoolAreaService.saveSchoolArea] input param: schoolAreaBo = {}", JSONObject.toJSONString(schoolAreaBo));
        WmEmploy wmEmploy = wmEmployClient.getEmployById(schoolAreaBo.getCuid().intValue());
        if (wmEmploy == null) {
            throw new WmSchCantException(BIZ_PARA_ERROR, "当前登录人信息不存在");
        }
        List<WmScSchoolAreaDO> wmScSchoolAreaDOList = wmScSchoolAreaMapper.selectBySchoolId(schoolAreaBo.getSchoolId());
        int result = 0;
        // 若范围信息为空, 为手动编辑保存
        if (CollectionUtils.isEmpty(wmScSchoolAreaDOList)) {
            return insertSchoolAoiInfoByManual(schoolAreaBo, wmEmploy);
        }

        List<String> schoolAreaExistList = new ArrayList<>();
        for (WmScSchoolAreaDO wmScSchoolAreaDO : wmScSchoolAreaDOList) {
            schoolAreaExistList.add(wmScSchoolAreaDO.getArea());
        }

        boolean isSchoolAreaChanged = false;
        for (WmScSchoolAoiDTO wmScSchoolAoiDTO : schoolAreaBo.getWmScSchoolAoiDTOList()) {
            // 该范围没有变化
            if (schoolAreaExistList.contains(wmScSchoolAoiDTO.getAoiArea())) {
                continue;
            }

            if (wmScSchoolAoiDTO.getAoiId() == null) {
                wmScSchoolAoiDTO.setAoiId(0L);
            }
            isSchoolAreaChanged = true;
            WmScSchoolAreaDO wmScSchoolAreaDO = wmScSchoolAreaMapper.selectBySchoolPrimaryIdAndAoiId(schoolAreaBo.getSchoolId(), wmScSchoolAoiDTO.getAoiId());
            if (wmScSchoolAreaDO == null) {
                throw new WmSchCantException(BIZ_PARA_ERROR, "不存在该AOI信息(" + wmScSchoolAoiDTO.getAoiName() + ")");
            }
            result += updateSchoolAoiInfoByManual(wmScSchoolAreaDO, wmScSchoolAoiDTO.getAoiArea(), wmEmploy);
        }

        if (!isSchoolAreaChanged) {
            log.info("[WmScSchoolAreaService.saveSchoolArea] school area is not changed. schoolId = {}", schoolAreaBo.getSchoolId());
        }
        return result;
    }

    /**
     * 查询学校范围通用方法
     */
    public List<WmScSchoolAreaDO> selectByCondition(WmScSchoolAreaSearchCondition condition) {
        if (condition == null) {
            return Lists.newArrayList();
        }
        if (condition.getPageFrom() == null) {
            condition.setPageFrom(ScCommonConstant.initPageFrom);
        }
        if (condition.getPageSize() == null || condition.getPageSize() > ScCommonConstant.initPageSize) {
            condition.setPageSize(ScCommonConstant.initPageSize);
        }
        return wmScSchoolAreaMapper.selectByCondition(condition);
    }

    /**
     * 学校范围页面-同步学校AOI信息
     * @param wmScSchoolAoiInfoSyncDTO 学校AOI信息同步DTO对象
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     * @throws Exception java.lang.Exception
     */
    public void syncSchoolAoiInfoByButton(WmScSchoolAoiInfoSyncDTO wmScSchoolAoiInfoSyncDTO) throws WmSchCantException, Exception {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_1", "com.sankuai.meituan.waimai.customer.service.sc.WmScSchoolAreaService.syncSchoolAoiInfoByButton(com.sankuai.meituan.waimai.thrift.customer.domain.sc.WmScSchoolAoiInfoSyncDTO)");
        log.info("[WmScSchoolService.syncSchoolAoiInfo] input param: wmScSchoolAoiInfoSyncDTO = {}", wmScSchoolAoiInfoSyncDTO.toString());
        if (wmScSchoolAoiInfoSyncDTO.getSchoolPrimaryId() == null) {
            log.warn("[WmScSchoolService.syncSchoolAoiInfo] input param is null. wmScSchoolAoiInfoSyncDTO = {}", wmScSchoolAoiInfoSyncDTO.toString());
            throw new WmSchCantException(BIZ_PARA_ERROR, "学校范围信息有误");
        }

        List<WmScSchoolAreaDO> wmScSchoolAreaDOList = wmScSchoolAreaMapper.selectBySchoolId(wmScSchoolAoiInfoSyncDTO.getSchoolPrimaryId());
        if (CollectionUtils.isEmpty(wmScSchoolAreaDOList)) {
            throw new WmSchCantException(BIZ_PARA_ERROR, "学校无有效范围信息");
        }

        for (WmScSchoolAreaDO wmScSchoolAreaDO : wmScSchoolAreaDOList) {
            // 手动编辑的范围信息
            if (wmScSchoolAreaDO.getAoiId() == null || wmScSchoolAreaDO.getAoiId() <= 0) {
                continue;
            }
            // 更新学校范围和通行属性, 并设置AOI信息自动同步属性为"是"
            updateSchoolAoiInfo(wmScSchoolAreaDO, wmScSchoolAoiInfoSyncDTO.getUserId(), wmScSchoolAoiInfoSyncDTO.getUserName());
        }

        // 更新学校下所有楼宇的状态
        Integer schoolPrimaryId = wmScSchoolAoiInfoSyncDTO.getSchoolPrimaryId();
        Integer userId = wmScSchoolAoiInfoSyncDTO.getUserId();
        String userName = wmScSchoolAoiInfoSyncDTO.getUserName();

        executorService.execute(new TraceRunnable(new Runnable() {
            @Override
            public void run() {
                try {
                    wmScSchoolBuildingService.updateSchoolBuildingStatusBySchoolPrimaryId(schoolPrimaryId, userId, userName);
                } catch (Exception e) {
                    log.error("[WmScSchoolAreaService.syncSchoolAoiInfoByButton] updateSchoolBuildingStatusBySchoolPrimaryId failed. schoolPrimaryId = {}",
                            schoolPrimaryId, e);
                }
            }
        }));

    }

    /**
     * 更新学校AOI信息, 包括AOI范围和通行属性和AOI名称
     * @param wmScSchoolAreaDO wmScSchoolAreaDO
     * @param userId 用户ID
     * @param userName 用户名称
     */
    public void updateSchoolAoiInfo(WmScSchoolAreaDO wmScSchoolAreaDO, Integer userId, String userName) throws TException, WmSchCantException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_1", "com.sankuai.meituan.waimai.customer.service.sc.WmScSchoolAreaService.updateSchoolAoiInfo(com.sankuai.meituan.waimai.customer.domain.sc.WmScSchoolAreaDO,java.lang.Integer,java.lang.String)");
        log.info("[WmScSchoolAreaService.updateSchoolAoiInfo] input param: wmScSchoolAreaDO = {}, userId = {}, userName = {}",
                JSONObject.toJSONString(wmScSchoolAreaDO), userId, userName);
        if (wmScSchoolAreaDO == null || wmScSchoolAreaDO.getAoiId() == null || wmScSchoolAreaDO.getAoiId() < 0) {
            log.warn("[WmScSchoolAreaService.updateSchoolAoiInfo] illegal input param. wmScSchoolAreaDO = {}", JSONObject.toJSONString(wmScSchoolAreaDO));
            throw new WmSchCantException(BIZ_PARA_ERROR, "学校范围信息有误");
        }

        if (wmScSchoolAreaDO.getAoiId() == 0) {
            log.info("[WmScSchoolAreaService.updateSchoolAoiInfo] aoi id = 0, wmScSchoolAreaDO = {}", wmScSchoolAreaDO.toString());
            return;
        }

        WmScSchoolAoiBO wmScSchoolAoiBO = bmLbsAoiExtThriftServiceAdapter.getSchoolAoiInfoByAoiId(wmScSchoolAreaDO.getAoiId());
        // AOI相关信息一致, 无需更新
        if (wmScSchoolAoiBO.getAoiArea().equals(wmScSchoolAreaDO.getArea())
                && wmScSchoolAoiBO.getAoiMode().equals(wmScSchoolAreaDO.getAoiMode())
                && wmScSchoolAoiBO.getAoiName().equals(wmScSchoolAreaDO.getAoiName())) {
            return;
        }

        WmScSchoolAreaDO wmScSchoolAreaDoUpdate = new WmScSchoolAreaDO();
        wmScSchoolAreaDoUpdate.setId(wmScSchoolAreaDO.getId());
        // 设置变更的学校区域AOI信息
        wmScSchoolAreaDoUpdate = setUpdateSchoolAreaAoiInfo(wmScSchoolAreaDO, wmScSchoolAreaDoUpdate, wmScSchoolAoiBO);
        wmScSchoolAreaDoUpdate.setMuid(userId.longValue());
        // 设置同步属性为自动同步
        if (!wmScSchoolAreaDO.getAutoSync().equals((int) SchoolAreaAutoSyncEnum.AUTO_SYNC.getType())) {
            wmScSchoolAreaDoUpdate.setAutoSync((int) SchoolAreaAutoSyncEnum.AUTO_SYNC.getType());
        }

        int result = wmScSchoolAreaMapper.updateByPrimaryKeySelective(wmScSchoolAreaDoUpdate);
        if (result > 0) {
            BeanUtils.copyProperties(wmScSchoolAreaDO, wmScSchoolAreaDoUpdate);
            wmScSchoolAreaDoUpdate = setUpdateSchoolAreaAoiInfo(wmScSchoolAreaDO, wmScSchoolAreaDoUpdate, wmScSchoolAoiBO);
            // 记录到操作日志
            WmScSchoolAreaLogBO wmScSchoolAreaLogBO = new WmScSchoolAreaLogBO();
            wmScSchoolAreaLogBO.setWmScSchoolAreaDoBefore(wmScSchoolAreaDO);
            wmScSchoolAreaLogBO.setWmScSchoolAreaDoAfter(wmScSchoolAreaDoUpdate);
            wmScSchoolAreaLogBO.setUserId(userId);
            wmScSchoolAreaLogBO.setUserName(userName);
            wmScLogSchoolInfoService.recordSchoolAreaUpdateLog(wmScSchoolAreaLogBO);
        }
        log.info("[WmScSchoolAreaService.updateSchoolAoiInfo] update area success. schoolPrimaryId = {}, area = {}, aoiMode = {}",
                wmScSchoolAreaDO.getSchoolPrimaryId(), wmScSchoolAoiBO.getAoiArea(), wmScSchoolAoiBO.getAoiMode());
    }

    /**
     * 设置变更的学校区域AOI信息
     * @param wmScSchoolAreaDoBefore 变更前学校区域DO
     * @param wmScSchoolAreaDoUpdate 变更后学校区域DO
     * @param wmScSchoolAoiBO 学校AOI信息
     */
    public WmScSchoolAreaDO setUpdateSchoolAreaAoiInfo(WmScSchoolAreaDO wmScSchoolAreaDoBefore,
                                           WmScSchoolAreaDO wmScSchoolAreaDoUpdate,
                                           WmScSchoolAoiBO wmScSchoolAoiBO) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_1", "com.sankuai.meituan.waimai.customer.service.sc.WmScSchoolAreaService.setUpdateSchoolAreaAoiInfo(WmScSchoolAreaDO,WmScSchoolAreaDO,WmScSchoolAoiBO)");
        if (!wmScSchoolAoiBO.getAoiArea().equals(wmScSchoolAreaDoBefore.getArea())) {
            wmScSchoolAreaDoUpdate.setArea(wmScSchoolAoiBO.getAoiArea());
        }

        if (!wmScSchoolAoiBO.getAoiName().equals(wmScSchoolAreaDoBefore.getAoiName())) {
            wmScSchoolAreaDoUpdate.setAoiName(wmScSchoolAoiBO.getAoiName());
        }

        if (!wmScSchoolAoiBO.getAoiMode().equals(wmScSchoolAreaDoBefore.getAoiMode())) {
            wmScSchoolAreaDoUpdate.setAoiMode(wmScSchoolAoiBO.getAoiMode());
        }

        return wmScSchoolAreaDoUpdate;
    }

    /**
     * 更新学校AOI范围信息-手动编辑了范围
     * @param wmScSchoolAreaDoStored wmScSchoolAreaDoStored
     * @param aoiArea 范围信息
     * @param wmEmploy 用户
     * @return 更新行数
     */
    public int updateSchoolAoiInfoByManual(WmScSchoolAreaDO wmScSchoolAreaDoStored, String aoiArea, WmEmploy wmEmploy) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.service.sc.WmScSchoolAreaService.updateSchoolAoiInfoByManual(WmScSchoolAreaDO,String,WmEmploy)");
        log.info("[WmScSchoolAreaService.updateSchoolAoiInfoByManual] input param: wmScSchoolAreaDO = {}, aoiArea = {}, wmEmploy = {}",
                    JSONObject.toJSONString(wmScSchoolAreaDoStored), aoiArea, JSONObject.toJSONString(wmEmploy));
        Integer userId = wmEmploy.getUid();
        String userName = wmEmploy.getName() + "(" + wmEmploy.getMisId() + ")";

        WmScSchoolAreaDO wmScSchoolAreaDoUpdate = new WmScSchoolAreaDO();
        wmScSchoolAreaDoUpdate.setId(wmScSchoolAreaDoStored.getId());
        wmScSchoolAreaDoUpdate.setArea(aoiArea);
        wmScSchoolAreaDoUpdate.setMuid(userId.longValue());
        // 若手动修改过范围, 则设置为「不自动同步」
        wmScSchoolAreaDoUpdate.setAutoSync((int) SchoolAreaAutoSyncEnum.NOT_AUTO_SYNC.getType());
        int result = wmScSchoolAreaMapper.updateByPrimaryKeySelective(wmScSchoolAreaDoUpdate);
        if (result > 0) {
            BeanUtils.copyProperties(wmScSchoolAreaDoStored, wmScSchoolAreaDoUpdate);
            wmScSchoolAreaDoUpdate.setArea(aoiArea);
            wmScSchoolAreaDoUpdate.setAutoSync((int) SchoolAreaAutoSyncEnum.NOT_AUTO_SYNC.getType());
            // 记录到操作日志
            String logInfo = wmScLogSchoolInfoService.composeSchoolAreaUpdateLog(wmScSchoolAreaDoStored, wmScSchoolAreaDoUpdate);
            if (StringUtils.isNotBlank(logInfo)) {
                wmScLogSchoolInfoService.insertScOptLog(OptTypeEnum.UPDATE.getType(), SC_SCHOOLAREA_LOG, wmScSchoolAreaDoStored.getSchoolPrimaryId(),
                        userId, userName, logInfo, "");
            }
        }
        log.info("[WmScSchoolAreaService.updateSchoolAoiInfoByManual] update school area by manual success. wmScSchoolAreaDoUpdate = {}, result = {}",
                JSONObject.toJSONString(wmScSchoolAreaDoUpdate), result);
        return result;
    }

    /**
     * 新增学校aoi相关信息
     * @param schoolPrimaryId 学校主键ID
     * @param aoiId aoiId
     * @param userId 用户ID
     * @param userName 用户名称
     */
    public void insertSchoolAoiInfo(Integer schoolPrimaryId, Long aoiId, Integer userId, String userName) throws TException, WmSchCantException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.service.sc.WmScSchoolAreaService.insertSchoolAoiInfo(java.lang.Integer,java.lang.Long,java.lang.Integer,java.lang.String)");
        log.info("[WmScSchoolAreaService.insertSchoolAoiInfo] input param: schoolPrimaryId = {}, aoiId = {}, userId = {}, userName = {}",
                schoolPrimaryId, aoiId, userId, userName);
        // 查询履约侧的AOI信息
        WmScSchoolAoiBO wmScSchoolAoiBO = bmLbsAoiExtThriftServiceAdapter.getSchoolAoiInfoByAoiId(aoiId);
        if (wmScSchoolAoiBO == null) {
            // 若AOI ID不存在则提示"AOI ID不存在"
            throw new WmSchCantException(BIZ_PARA_ERROR, "AOI ID不存在");
        }

        WmScSchoolAreaDO wmScSchoolAreaDoInsert = new WmScSchoolAreaDO();
        wmScSchoolAreaDoInsert.setAoiId(aoiId);
        wmScSchoolAreaDoInsert.setSchoolPrimaryId(schoolPrimaryId);
        wmScSchoolAreaDoInsert.setArea(wmScSchoolAoiBO.getAoiArea());
        wmScSchoolAreaDoInsert.setAoiMode(wmScSchoolAoiBO.getAoiMode());
        wmScSchoolAreaDoInsert.setAoiName(wmScSchoolAoiBO.getAoiName());
        wmScSchoolAreaDoInsert.setCuid(userId.longValue());
        wmScSchoolAreaDoInsert.setMuid(userId.longValue());
        wmScSchoolAreaDoInsert.setAutoSync((int) SchoolAreaAutoSyncEnum.AUTO_SYNC.getType());
        wmScSchoolAreaDoInsert.setValid(ScConstants.SC_IS_VALID);

        int result = wmScSchoolAreaMapper.insertSelective(wmScSchoolAreaDoInsert);
        if (result > 0) {
            // 记录操作日志
            String logInfo = wmScLogSchoolInfoService.composeSchoolAreaInserting(wmScSchoolAreaDoInsert);
            if (StringUtils.isNotBlank(logInfo)) {
                wmScLogSchoolInfoService.insertScOptLog(OptTypeEnum.INSERT.getType(), SC_SCHOOLAREA_LOG, wmScSchoolAreaDoInsert.getSchoolPrimaryId(),
                        userId, userName, logInfo, "");
            }
        }
        log.info("[WmScSchoolAreaService.insertSchoolAoiInfo] insert aoi info success. wmScSchoolAreaDoInsert = {}, result = {}",
                JSONObject.toJSONString(wmScSchoolAreaDoInsert), result);
    }

    /**
     * 新增学校aoi相关信息(手动编辑的范围信息)
     * @param schoolAreaBo schoolAreaBo
     * @param wmEmploy 用户
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    public int insertSchoolAoiInfoByManual(SchoolAreaBo schoolAreaBo, WmEmploy wmEmploy) throws WmSchCantException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.service.sc.WmScSchoolAreaService.insertSchoolAoiInfoByManual(SchoolAreaBo,WmEmploy)");
        log.info("[WmScSchoolAreaService.insertSchoolAoiInfoByManual] input param: schoolAreaBo = {}", JSONObject.toJSONString(schoolAreaBo));
        if (CollectionUtils.isEmpty(schoolAreaBo.getWmScSchoolAoiDTOList())) {
            throw new WmSchCantException(BIZ_PARA_ERROR, "保存学校范围不能为空");
        }

        if (schoolAreaBo.getWmScSchoolAoiDTOList().size() > 1) {
            throw new WmSchCantException(BIZ_PARA_ERROR, "只允许手动新建一个范围");
        }
        // 手动新建编辑范围, 只能新建一个范围
        WmScSchoolAoiDTO wmScSchoolAoiDTO = schoolAreaBo.getWmScSchoolAoiDTOList().get(0);
        if (StringUtils.isBlank(wmScSchoolAoiDTO.getAoiArea())) {
            throw new WmSchCantException(BIZ_PARA_ERROR, "保存学校范围不能为空");
        }

        WmScSchoolAreaDO wmScSchoolAreaDoInsert = new WmScSchoolAreaDO();
        wmScSchoolAreaDoInsert.setAoiId(wmScSchoolAoiDTO.getAoiId());
        wmScSchoolAreaDoInsert.setSchoolPrimaryId(schoolAreaBo.getSchoolId());
        wmScSchoolAreaDoInsert.setArea(wmScSchoolAoiDTO.getAoiArea());
        wmScSchoolAreaDoInsert.setAoiMode(wmScSchoolAoiDTO.getAoiMode());
        wmScSchoolAreaDoInsert.setAoiName(wmScSchoolAoiDTO.getAoiName());
        wmScSchoolAreaDoInsert.setCuid(schoolAreaBo.getCuid());
        wmScSchoolAreaDoInsert.setMuid(schoolAreaBo.getCuid());
        wmScSchoolAreaDoInsert.setAutoSync((int) SchoolAreaAutoSyncEnum.AUTO_SYNC.getType());
        wmScSchoolAreaDoInsert.setValid(ScConstants.SC_IS_VALID);

        Integer userId = wmEmploy.getUid();
        String userName = wmEmploy.getName() + "(" + wmEmploy.getMisId() + ")";
        int result = wmScSchoolAreaMapper.insertSelective(wmScSchoolAreaDoInsert);
        if (result > 0) {
            // 记录操作日志
            String logInfo = wmScLogSchoolInfoService.composeSchoolAreaInsertingManual(wmScSchoolAreaDoInsert);
            if (StringUtils.isNotBlank(logInfo)) {
                wmScLogSchoolInfoService.insertScOptLog(OptTypeEnum.INSERT.getType(), SC_SCHOOLAREA_LOG, wmScSchoolAreaDoInsert.getSchoolPrimaryId(),
                        userId, userName, logInfo, "");
            }
        }
        log.info("[WmScSchoolAreaService.insertSchoolAoiInfoByManual] insert aoi info by manual success. wmScSchoolAreaDoInsert = {}, result = {}",
                JSONObject.toJSONString(wmScSchoolAreaDoInsert), result);
        return result;
    }

    /**
     * AOI弹窗-保存学校AOI信息前置校验
     * @param schoolPrimaryId 学校主键ID
     * @param aoiIdList AOI ID列表
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     * @throws Exception java.lang.Exception
     */
    public void checkSchoolAoiIdList(Integer schoolPrimaryId, List<Long> aoiIdList) throws WmSchCantException, Exception {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.sc.WmScSchoolAreaService.checkSchoolAoiIdList(java.lang.Integer,java.util.List)");
        log.info("[WmScSchoolAreaService.checkSchoolAoiIdList] input param: schoolPrimaryId = {}, aoiIdList = {}",
                schoolPrimaryId, JSONObject.toJSONString(aoiIdList));
        if (CollectionUtils.isEmpty(aoiIdList)) {
            // 删除所有的AOI信息-前置校验
            checkSchoolAoiInfoWhenDeleteAll(schoolPrimaryId);
            return;
        }
        // 删除部分的AOI信息-前置校验
        deletePartAoiInfoPreCheck(schoolPrimaryId, aoiIdList);
        WmScSchoolAreaDO wmScSchoolAreaDO = wmScSchoolAreaMapper.selectBySchoolPrimaryIdAndAoiId(schoolPrimaryId, 0L);
        if (wmScSchoolAreaDO != null) {
            // 新增AOI删除手动标注的范围及全部楼宇-前置校验
            checkSchoolAoiInfoWhenInsert(schoolPrimaryId, aoiIdList);
        }
    }

    /**
     * 删除所有AOI ID-前置校验
     * @param schoolPrimaryId 学校主键ID
     * @throws WmSchCantException  com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     * @throws Exception java.lang.Exception
     */
    public void checkSchoolAoiInfoWhenDeleteAll(Integer schoolPrimaryId) throws WmSchCantException, Exception {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.sc.WmScSchoolAreaService.checkSchoolAoiInfoWhenDeleteAll(java.lang.Integer)");
        log.info("[WmScSchoolAreaService.deleteAllAoiInfoPreCheck] schoolPrimaryId = {}", schoolPrimaryId);
        List<WmScSchoolAreaDO> wmScSchoolAreaDOList = wmScSchoolAreaMapper.selectBySchoolId(schoolPrimaryId);
        // 当前已有的AOI ID列表
        List<Long> aoiIdListExist = wmScSchoolAreaDOList.stream()
                .map(WmScSchoolAreaDO::getAoiId)
                .filter(aoiId -> aoiId > 0)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(aoiIdListExist)) {
            log.info("[WmScSchoolAreaService.deleteAllAoiInfoPreCheck] aoiIdListExist is empty, return. schoolPrimaryId {}", schoolPrimaryId);
            return;
        }
        List<WmScSchoolBuildingDO> wmScSchoolBuildingDOList = wmScSchoolBuildingMapper.selectBySchoolPrimaryId(schoolPrimaryId);
        // 学校没有楼宇信息, 校验通过
        if (CollectionUtils.isEmpty(wmScSchoolBuildingDOList)) {
            log.info("[WmScSchoolAreaService.deleteAllAoiInfoPreCheck] wmScSchoolBuildingDOList is empty, return. schoolPrimaryId = {}", schoolPrimaryId);
            return;
        }
        // 学校有楼宇信息, 返回二次确认提示
        List<String> aoiNameList = wmScSchoolAreaDOList.stream()
                .filter(wmScSchoolAreaDO -> wmScSchoolAreaDO.getAoiId() > 0)
                .map(WmScSchoolAreaDO::getAoiName)
                .collect(Collectors.toList());

        List<String> buildingNameList = wmScSchoolBuildingDOList.stream()
                .map(WmScSchoolBuildingDO::getBuildingName)
                .limit(3)
                .collect(Collectors.toList());

        String str = "删除AOI【" + StringUtils.join(aoiNameList, "、") +
                "】将触发【" + StringUtils.join(buildingNameList, "、") +
                "】等" + wmScSchoolBuildingDOList.size() + "个楼宇的删除。\\n" +
                "请确认是否继续？";
        log.info("[WmScSchoolAreaService.deleteAllAoiInfoPreCheck] precheck result = {}", str);
        throw new WmSchCantException(SVAE_SCHOOL_AOI_PRE_CHECK_ERROR, str);
    }

    /**
     * 删除部分AOI ID-前置校验
     * @param schoolPrimaryId 学校主键ID
     * @param aoiIdList AOI ID列表
     * @throws WmSchCantException  com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     * @throws Exception java.lang.Exception
     */
    public void deletePartAoiInfoPreCheck(Integer schoolPrimaryId, List<Long> aoiIdList) throws WmSchCantException, Exception {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_2", "com.sankuai.meituan.waimai.customer.service.sc.WmScSchoolAreaService.deletePartAoiInfoPreCheck(java.lang.Integer,java.util.List)");
        List<WmScSchoolAreaDO> wmScSchoolAreaDOList = wmScSchoolAreaMapper.selectBySchoolId(schoolPrimaryId);
        if (CollectionUtils.isEmpty(wmScSchoolAreaDOList)) {
            log.info("[WmScSchoolAreaService.deletePartAoiInfoPreCheck] wmScSchoolAreaDOList is empty, return. schoolPrimaryId = {}, aoiIdList = {}",
                    schoolPrimaryId, JSONObject.toJSONString(aoiIdList));
            return;
        }
        // 当前已有的AOI ID列表
        List<Long> aoiIdListExist = wmScSchoolAreaDOList.stream()
                .map(WmScSchoolAreaDO::getAoiId)
                .filter(aoiId -> aoiId > 0)
                .collect(Collectors.toList());
        // 被删除的AOI ID列表
        List<Long> aoiIdDeleteList = aoiIdListExist.stream()
                .filter(aoiId -> !aoiIdList.contains(aoiId))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(aoiIdDeleteList)) {
            log.info("[WmScSchoolAreaService.deletePartAoiInfoPreCheck] aoiIdDeleterList is empty, return. schoolPrimaryId = {}, aoiIdList = {}",
                    schoolPrimaryId, JSONObject.toJSONString(aoiIdList));
            return;
        }

        List<WmScSchoolBuildingDO> wmScSchoolBuildingDOList = wmScSchoolBuildingMapper.selectBySchoolPrimaryId(schoolPrimaryId);
        // 学校没有楼宇信息, 校验通过
        if (CollectionUtils.isEmpty(wmScSchoolBuildingDOList)) {
            log.info("[WmScSchoolAreaService.deletePartAoiInfoPreCheck] wmScSchoolBuildingDOList is empty, return. schoolPrimaryId = {}, aoiIdList = {}",
                    schoolPrimaryId, JSONObject.toJSONString(aoiIdList));
            return;
        }

        List<WmScSchoolAreaDO> wmScSchoolAreaDODeleteList = wmScSchoolAreaDOList.stream()
                .filter(wmScSchoolAreaDO -> aoiIdDeleteList.contains(wmScSchoolAreaDO.getAoiId()))
                .collect(Collectors.toList());

        List<String> schoolAreaDeleteList = wmScSchoolAreaDODeleteList.stream()
                .filter(wmScSchoolAreaDO -> StringUtils.isNotBlank(wmScSchoolAreaDO.getArea()) && !"[]".equals(wmScSchoolAreaDO.getArea()))
                .map(WmScSchoolAreaDO::getArea)
                .collect(Collectors.toList());

        List<WmScSchoolBuildingDO> wmScSchoolBuildingDODeleteList = new ArrayList<>();
        for (WmScSchoolBuildingDO wmScSchoolBuildingDO : wmScSchoolBuildingDOList) {
            if (StringUtils.isBlank(wmScSchoolBuildingDO.getBuildingCoordinate())
                    || "[]".equals(wmScSchoolBuildingDO.getBuildingCoordinate())) {
                continue;
            }
            // 楼宇坐标经纬度
            List<WmScPoint> wmScPoints = JSONObject.parseArray(wmScSchoolBuildingDO.getBuildingCoordinate(), WmScPoint.class);
            int buildingLatitude = wmScPoints.get(0).getX();
            int buidlingLongitude = wmScPoints.get(0).getY();
            // 楼宇坐标在被删除的AOI范围内
            if (WmRtreeUtil.withinAreaList(buildingLatitude, buidlingLongitude, schoolAreaDeleteList)) {
                wmScSchoolBuildingDODeleteList.add(wmScSchoolBuildingDO);
            }
        }

        if (CollectionUtils.isEmpty(wmScSchoolBuildingDODeleteList)) {
            log.info("[WmScSchoolAreaService.deletePartAoiInfoPreCheck] wmScSchoolBuildingDODeleteList is empty, return. schoolPrimaryId = {}, aoiIdList = {}",
                    schoolPrimaryId, JSONObject.toJSONString(aoiIdList));
            return;
        }

        List<String> deleteAoiNameList = wmScSchoolAreaDODeleteList.stream()
                .filter(wmScSchoolAreaDO -> wmScSchoolAreaDO.getAoiId() > 0)
                .map(WmScSchoolAreaDO::getAoiName)
                .collect(Collectors.toList());

        List<String> deleteBuildingNameList = wmScSchoolBuildingDODeleteList.stream()
                .map(WmScSchoolBuildingDO::getBuildingName)
                .limit(3)
                .collect(Collectors.toList());

        String str = "删除AOI【" + StringUtils.join(deleteAoiNameList, "、") +
                "】将触发【" + StringUtils.join(deleteBuildingNameList, "、") +
                "】等" + wmScSchoolBuildingDODeleteList.size() + "个校内楼宇的删除。\\n" +
                "请确认是否继续？";
        log.info("[WmScSchoolAreaService.deletePartAoiInfoPreCheck] precheck result = {}", str);
        throw new WmSchCantException(SVAE_SCHOOL_AOI_PRE_CHECK_ERROR, str);
    }

    /**
     * 新增AOI删除手动标注的范围及全部楼宇-前置校验
     * @param schoolPrimaryId 学校主键ID
     * @param aoiIdList AOI ID列表
     * @throws WmSchCantException  com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     * @throws Exception java.lang.Exception
     */
    public void checkSchoolAoiInfoWhenInsert(Integer schoolPrimaryId, List<Long> aoiIdList) throws WmSchCantException, Exception {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_2", "com.sankuai.meituan.waimai.customer.service.sc.WmScSchoolAreaService.checkSchoolAoiInfoWhenInsert(java.lang.Integer,java.util.List)");
        log.info("[WmScSchoolAreaService.addAoiInfoWithAutoAreaPreCheck] input param: schoolPrimaryId = {}, aoiIdList = {}",
                schoolPrimaryId, JSONObject.toJSONString(aoiIdList));
        List<WmScSchoolBuildingDO> wmScSchoolBuildingDOList = wmScSchoolBuildingMapper.selectBySchoolPrimaryId(schoolPrimaryId);
        if (CollectionUtils.isEmpty(wmScSchoolBuildingDOList)) {
            log.info("[WmScSchoolAreaService.addAoiInfoWithAutoAreaPreCheck] wmScSchoolBuildingDOList is empty, return. schoolPrimaryId = {}, aoiIdList = {}",
                    schoolPrimaryId, JSONObject.toJSONString(aoiIdList));
            return;
        }

        List<String> deleteBuildingNameList = wmScSchoolBuildingDOList.stream()
                .map(WmScSchoolBuildingDO::getBuildingName)
                .limit(3)
                .collect(Collectors.toList());
        // 从履约侧查询AOI名称
        Map<Long, WmScSchoolAoiBO> aoiInfoMap = bmLbsAoiExtThriftServiceAdapter.getSchoolAoiListByAoiIdList(aoiIdList);
        List<String> aoiNameList = aoiInfoMap.values().stream()
                .map(WmScSchoolAoiBO::getAoiName)
                .collect(Collectors.toList());
        String str = "增加AOI【" + StringUtils.join(aoiNameList, "、") +
                "】将删除手动标注范围，将触发【" + StringUtils.join(deleteBuildingNameList, "、") +
                "】等" + wmScSchoolBuildingDOList.size() + "个楼宇的删除。\\n" +
                "请确认是否继续？";
        log.info("[WmScSchoolAreaService.addAoiInfoWithAutoAreaPreCheck] precheck result = {}", str);
        throw new WmSchCantException(SVAE_SCHOOL_AOI_PRE_CHECK_ERROR, str);
    }

    /**
     * AOI弹窗-保存学校AOI信息V2
     * @param wmScSchoolAoiInfoSaveDTO 学校AOI信息保存DTO对象
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     * @throws Exception java.lang.Exception
     */
    public void saveSchoolAoiInfoV2(WmScSchoolAoiInfoSaveDTO wmScSchoolAoiInfoSaveDTO) throws WmSchCantException, Exception {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_2", "com.sankuai.meituan.waimai.customer.service.sc.WmScSchoolAreaService.saveSchoolAoiInfoV2(com.sankuai.meituan.waimai.thrift.customer.domain.sc.WmScSchoolAoiInfoSaveDTO)");
        log.info("[WmScSchoolService.saveSchoolAoiInfoV2] input param: wmScSchoolAoiInfoSaveDTO = {}", wmScSchoolAoiInfoSaveDTO.toString());
        List<Long> aoiIdList = wmScSchoolAoiInfoSaveDTO.getAoiIdList();
        Integer schoolPrimaryId = wmScSchoolAoiInfoSaveDTO.getSchoolPrimaryId();
        Integer userId = wmScSchoolAoiInfoSaveDTO.getUserId();
        String userName = wmScSchoolAoiInfoSaveDTO.getUserName();

        // 要保存的AOI ID列表为空
        if (CollectionUtils.isEmpty(aoiIdList)) {
            WmScSchoolAreaDO wmScSchoolAreaDO = wmScSchoolAreaMapper.selectBySchoolPrimaryIdAndAoiId(schoolPrimaryId, 0L);
            // 存在手动编辑的范围, 则直接返回
            if (wmScSchoolAreaDO != null) {
                return;
            }
            // 清空已保存的学校AOI信息
            deleteAllSchoolAoiInfo(schoolPrimaryId, userId, userName);
            // 删除学校下的所有楼宇信息
            wmScSchoolBuildingService.deleteAllSchoolBuilding(schoolPrimaryId, userId, userName);
            return;
        }

        // 若存在手动标注的范围, 则先删除该范围和该学校下的所有楼宇
        WmScSchoolAreaDO wmScSchoolAreaDO = wmScSchoolAreaMapper.selectBySchoolPrimaryIdAndAoiId(schoolPrimaryId, 0L);
        if (wmScSchoolAreaDO != null) {
            deleteSchoolAreaById(wmScSchoolAreaDO.getId(), userId, userName);
            wmScSchoolBuildingService.deleteAllSchoolBuilding(schoolPrimaryId, userId, userName);
        }

        for (Long aoiId : aoiIdList) {
            WmScSchoolAreaDO wmScSchoolAreaDoByAoiId = wmScSchoolAreaMapper.selectBySchoolPrimaryIdAndAoiId(schoolPrimaryId, aoiId);
            if (wmScSchoolAreaDoByAoiId != null) {
                // 学校已存在AOI信息, 则进行更新操作
                updateSchoolAoiInfo(wmScSchoolAreaDoByAoiId, userId, userName);
            } else {
                // 学校不存在AOI信息, 则进行新增操作
                insertSchoolAoiInfo(schoolPrimaryId, aoiId, userId, userName);
            }
        }

        // 删除不在保存AOI列表范围内但已有的AOI信息, 以及在该AOI范围内的所有楼宇信息
        List<WmScSchoolAreaDO> wmScSchoolAreaDODeleteList = getSchoolAoiInfoListNotInAoiIdList(schoolPrimaryId, aoiIdList);
        log.info("[WmScSchoolService.saveSchoolAoiInfoV2] wmScSchoolAreaDODeleteList = {}", JSONObject.toJSONString(wmScSchoolAreaDODeleteList));
        if (CollectionUtils.isNotEmpty(wmScSchoolAreaDODeleteList)) {
            deleteSchoolAoiInfoAndBuildingInfoInAoi(schoolPrimaryId, wmScSchoolAreaDODeleteList, userId, userName);
        }

        // 更新学校下所有楼宇的状态
        executorService.execute(new TraceRunnable(new Runnable() {
            @Override
            public void run() {
                try {
                    wmScSchoolBuildingService.updateSchoolBuildingStatusBySchoolPrimaryId(schoolPrimaryId, userId, userName);
                } catch (Exception e) {
                    log.error("[WmScSchoolAreaService.saveSchoolAoiInfoV2] updateSchoolBuildingStatusBySchoolPrimaryId failed. schoolPrimaryId = {}",
                            schoolPrimaryId, e);
                }
            }
        }));
    }

    /**
     * 根据学校范围主键ID逻辑删除学校范围
     * @param id 学校范围主键ID
     * @param userId 用户ID
     * @param userName 用户名称
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    public void deleteSchoolAreaById(Long id, Integer userId, String userName) throws WmSchCantException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_2", "com.sankuai.meituan.waimai.customer.service.sc.WmScSchoolAreaService.deleteSchoolAreaById(java.lang.Long,java.lang.Integer,java.lang.String)");
        log.info("[WmScSchoolAreaService.deleteSchoolAreaById] input param: id = {}, userId = {}, userName = {}",
                id, userId, userName);

        WmScSchoolAreaDO wmScSchoolAreaDO = wmScSchoolAreaMapper.selectByPrimaryKey(id);
        if (wmScSchoolAreaDO == null) {
            log.error("[WmScSchoolAreaService.deleteSchoolAreaById] wmScSchoolBuildingDO is null. id = {}", id);
            throw new WmSchCantException(BIZ_PARA_ERROR, "被删除的学校范围不存在");
        }

        // 执行逻辑删除
        int result = wmScSchoolAreaMapper.invalidByPrimaryKey(id, userId.longValue());
        if (result > 0) {
            String logInfo = wmScLogSchoolInfoService.composeSchoolAreaDeleteLog(wmScSchoolAreaDO);
            if (StringUtils.isNotBlank(logInfo)) {
                wmScLogSchoolInfoService.insertScOptLog(OptTypeEnum.DELETE.getType(), SC_SCHOOLAREA_LOG, wmScSchoolAreaDO.getSchoolPrimaryId(),
                        userId, userName, logInfo, "");
            }
        }
        log.info("[WmScSchoolBuildingService.deleteSchoolAreaById] delete school building success. id = {}", id);
    }

    /**
     * 获取某学校不在AOI ID列表内的AOI信息
     * @param schoolPrimaryId 学校主键ID
     * @param aoiIdList AOI信息列表
     * @return 不在AOI ID列表内的AOI信息
     */
    public List<WmScSchoolAreaDO> getSchoolAoiInfoListNotInAoiIdList(Integer schoolPrimaryId, List<Long> aoiIdList) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_1", "com.sankuai.meituan.waimai.customer.service.sc.WmScSchoolAreaService.getSchoolAoiInfoListNotInAoiIdList(java.lang.Integer,java.util.List)");
        List<WmScSchoolAreaDO> wmScSchoolAreaDOList = wmScSchoolAreaMapper.selectBySchoolId(schoolPrimaryId);
        if (CollectionUtils.isEmpty(wmScSchoolAreaDOList)) {
            log.info("[WmScSchoolAreaService.getSchoolAoiInfoListNotInAoiIdList] wmScSchoolAreaDOList is empty, return. schoolPrimaryId = {}, aoiIdList = {}",
                    schoolPrimaryId, JSONObject.toJSONString(aoiIdList));
            return new ArrayList<>();
        }
        // 被删除的AOI列表
        return wmScSchoolAreaDOList.stream()
                .filter(wmScSchoolAreaDO -> !aoiIdList.contains(wmScSchoolAreaDO.getAoiId()))
                .collect(Collectors.toList());
    }

    /**
     * 定时任务-更新学校AOI信息和学校楼宇POI信息
     * @param schoolId 学校ID
     * @throws TException org.apache.thrift.TException
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    public void refreshSchoolAoiAndBuildingPoiInfo(Integer schoolId) throws TException, WmSchCantException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_1", "com.sankuai.meituan.waimai.customer.service.sc.WmScSchoolAreaService.refreshSchoolAoiAndBuildingPoiInfo(java.lang.Integer)");
        log.info("[WmScSchoolAreaService.refreshSchoolAoiAndBuildingPoiInfo] input param: schoolId = {}", schoolId);
        // 若学校不存在或被删除, 则直接返回
        WmSchoolDB wmSchoolDB = wmSchoolMapper.selectSchoolBySchoolId(schoolId);
        if (wmSchoolDB == null) {
            log.info("[refreshSchoolAoiAndBuildingPoiInfo] wmSchoolDB is empty. schoolId = {}", schoolId);
            return;
        }

        int schoolPrimaryId = wmSchoolDB.getId();
        List<WmScSchoolAreaDO> wmScSchoolAreaDOList = wmScSchoolAreaMapper.selectBySchoolId(wmSchoolDB.getId());
        // 若学校没有范围信息, 则直接返回
        if (CollectionUtils.isEmpty(wmScSchoolAreaDOList)) {
            log.info("[refreshSchoolAoiAndBuildingPoiInfo] wmScSchoolAreaDOList is empty. schoolPrimaryId = {}", schoolPrimaryId);
            return;
        }
        // 更新学校AOI信息
        for (WmScSchoolAreaDO wmScSchoolAreaDO : wmScSchoolAreaDOList) {
            if (wmScSchoolAreaDO.getAoiId() == null || wmScSchoolAreaDO.getAoiId() <= 0) {
                continue;
            }
            updateSchoolAoiInfoWithAutoSyncCheck(wmScSchoolAreaDO);
        }
        // 更新学校楼宇POI信息
        wmScSchoolBuildingService.syncSchoolBuildingExistingPoiInfo(schoolPrimaryId, 0, "系统自动更新");
    }

    /**
     * 更新学校AOI信息(若无需自动同步, 则只更新通行属性)
     * @param wmScSchoolAreaDO wmScSchoolAreaDO
     */
    public void updateSchoolAoiInfoWithAutoSyncCheck(WmScSchoolAreaDO wmScSchoolAreaDO) throws TException, WmSchCantException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_1", "com.sankuai.meituan.waimai.customer.service.sc.WmScSchoolAreaService.updateSchoolAoiInfoWithAutoSyncCheck(com.sankuai.meituan.waimai.customer.domain.sc.WmScSchoolAreaDO)");
        log.info("[WmScSchoolAreaService.updateSchoolAoiInfoWithAutoSyncCheck] input param: wmScSchoolAreaDO = {}", JSONObject.toJSONString(wmScSchoolAreaDO));
        if (wmScSchoolAreaDO == null || wmScSchoolAreaDO.getAoiId() == null || wmScSchoolAreaDO.getAoiId() < 0) {
            throw new WmSchCantException(BIZ_PARA_ERROR, "学校范围信息有误");
        }

        if (wmScSchoolAreaDO.getAoiId() == 0) {
            log.info("[WmScSchoolAreaService.updateSchoolAoiInfoWithAutoSyncCheck] aoi id = 0, wmScSchoolAreaDO = {}", wmScSchoolAreaDO.toString());
            return;
        }

        try {
            if (wmScSchoolAreaDO.getAutoSync() != null && wmScSchoolAreaDO.getAutoSync().equals((int) SchoolAreaAutoSyncEnum.AUTO_SYNC.getType())) {
                // 同步属性为「自动同步」, 更新AOI范围和通行属性和AOI名称
                updateSchoolAoiInfo(wmScSchoolAreaDO, 0, "系统自动更新");
            } else {
                // 同步属性为「不自动同步」, 仅更新通行属性和AOI名称
                updateSchoolAoiModeAndAoiName(wmScSchoolAreaDO, 0, "系统自动更新");
            }
        } catch (Exception e) {
            //此处catch住异常，job服务目前会因为这里抛出异常导致部分学校未更新
            log.error("[WmScSchoolAreaService.updateSchoolAoiInfoWithAutoSyncCheck] AOI更新执行异常, param: {}, e: ", JSONObject.toJSONString(wmScSchoolAreaDO), e);
        }

        // 更新学校下所有楼宇状态
        executorService.execute(new TraceRunnable(new Runnable() {
            @Override
            public void run() {
                try {
                    wmScSchoolBuildingService.updateSchoolBuildingStatusBySchoolPrimaryId(wmScSchoolAreaDO.getSchoolPrimaryId(), 0, "系统自动更新");
                } catch (Exception e) {
                    log.error("[WmScSchoolAreaService.updateSchoolAoiInfoWithAutoSyncCheck] updateSchoolBuildingStatusBySchoolPrimaryId failed. schoolPrimaryId = {}",
                            wmScSchoolAreaDO.getSchoolPrimaryId(), e);
                }
            }
        }));

    }

    /**
     * 更新学校AOI通行属性和AOI名称
     * @param wmScSchoolAreaDO wmScSchoolAreaDO
     * @param userId 用户ID
     * @param userName 用户名称
     */
    public void updateSchoolAoiModeAndAoiName(WmScSchoolAreaDO wmScSchoolAreaDO, Integer userId, String userName) throws TException, WmSchCantException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.service.sc.WmScSchoolAreaService.updateSchoolAoiModeAndAoiName(com.sankuai.meituan.waimai.customer.domain.sc.WmScSchoolAreaDO,java.lang.Integer,java.lang.String)");
        log.info("[WmScSchoolAreaService.updateSchoolAoiModeAndAoiName] wmScSchoolAreaDO = {}, userId = {}, userName = {}",
                JSONObject.toJSONString(wmScSchoolAreaDO), userId, userName);
        if (wmScSchoolAreaDO == null || wmScSchoolAreaDO.getAoiId() == null || wmScSchoolAreaDO.getAoiId() < 0) {
            throw new WmSchCantException(BIZ_PARA_ERROR, "学校范围信息有误");
        }

        if (wmScSchoolAreaDO.getAoiId() == 0) {
            log.info("[WmScSchoolAreaService.updateSchoolAoiModeAndAoiName] aoi id = 0, wmScSchoolAreaDO = {}", wmScSchoolAreaDO.toString());
            return;
        }

        Long aoiId = wmScSchoolAreaDO.getAoiId();
        // 查询最新的学校AOI信息
        WmScSchoolAoiBO wmScSchoolAoiBO = bmLbsAoiExtThriftServiceAdapter.getSchoolAoiInfoByAoiId(aoiId);
        // 学校AOI通行属性和AOI名称无变化
        if (wmScSchoolAoiBO.getAoiName().equals(wmScSchoolAreaDO.getAoiName())
            && wmScSchoolAoiBO.getAoiMode().equals(wmScSchoolAreaDO.getAoiMode())) {
            log.info("[WmScSchoolAreaService.updateSchoolAoiModeAndAoiName] aoiName and aoiMode is not changed. wmScSchoolAreaDO = {}", JSONObject.toJSONString(wmScSchoolAreaDO));
            return;
        }
        // 更新学校AOI通行属性
        WmScSchoolAreaDO wmScSchoolAreaDoUpdate = new WmScSchoolAreaDO();
        wmScSchoolAreaDoUpdate.setId(wmScSchoolAreaDO.getId());
        wmScSchoolAreaDoUpdate.setAoiName(wmScSchoolAoiBO.getAoiName());
        wmScSchoolAreaDoUpdate.setAoiMode(wmScSchoolAoiBO.getAoiMode());
        wmScSchoolAreaDoUpdate.setMuid(userId.longValue());

        int result = wmScSchoolAreaMapper.updateByPrimaryKeySelective(wmScSchoolAreaDoUpdate);
        if (result > 0) {
            BeanUtils.copyProperties(wmScSchoolAreaDO, wmScSchoolAreaDoUpdate);
            wmScSchoolAreaDoUpdate.setAoiName(wmScSchoolAoiBO.getAoiName());
            wmScSchoolAreaDoUpdate.setAoiMode(wmScSchoolAoiBO.getAoiMode());
            // 记录到操作日志
            WmScSchoolAreaLogBO wmScSchoolAreaLogBO = new WmScSchoolAreaLogBO();
            wmScSchoolAreaLogBO.setWmScSchoolAreaDoBefore(wmScSchoolAreaDO);
            wmScSchoolAreaLogBO.setWmScSchoolAreaDoAfter(wmScSchoolAreaDoUpdate);
            wmScSchoolAreaLogBO.setUserId(userId);
            wmScSchoolAreaLogBO.setUserName(userName);
            wmScLogSchoolInfoService.recordSchoolAreaUpdateLog(wmScSchoolAreaLogBO);
        }
        log.info("[WmScSchoolAreaService.updateSchoolAoiModeAndAoiName] update aoiMode and aoiName success. schoolPrimaryId = {}, aoiMode = {}, aoiName = {}",
                wmScSchoolAreaDO.getSchoolPrimaryId(), wmScSchoolAoiBO.getAoiMode(), wmScSchoolAoiBO.getAoiName());
    }

    /**
     * 清空学校下的所有AOI信息
     * @param schoolPrimaryId 学校主键ID
     * @param userId 用户ID
     * @param userName 用户名称
     */
    public void deleteAllSchoolAoiInfo(Integer schoolPrimaryId, Integer userId, String userName) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.sc.WmScSchoolAreaService.deleteAllSchoolAoiInfo(java.lang.Integer,java.lang.Integer,java.lang.String)");
        log.info("[WmScSchoolAreaService.deleteSchoolAoiInfo] input param: schoolPrimaryId = {}, userId = {}, userName = {}",
                schoolPrimaryId, userId, userName);
        List<WmScSchoolAreaDO> wmScSchoolAreaDOList = wmScSchoolAreaMapper.selectBySchoolId(schoolPrimaryId);
        if (CollectionUtils.isEmpty(wmScSchoolAreaDOList)) {
            return;
        }

        for (WmScSchoolAreaDO wmScSchoolAreaDO : wmScSchoolAreaDOList) {
            // 手动编辑的范围信息不清空
            if (wmScSchoolAreaDO.getAoiId() != null && wmScSchoolAreaDO.getAoiId() <= 0) {
                continue;
            }
            // 从履约侧获取的AOI信息逻辑删除
            deleteSchoolAoiInfo(wmScSchoolAreaDO, userId, userName);
        }
    }

    /**
     * 删除学校AOI信息并记录日志
     * @param wmScSchoolAreaDO wmScSchoolAreaDO
     * @param userId 用户ID
     * @param userName 用户名称
     */
    public void deleteSchoolAoiInfo(WmScSchoolAreaDO wmScSchoolAreaDO, Integer userId, String userName) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_1", "com.sankuai.meituan.waimai.customer.service.sc.WmScSchoolAreaService.deleteSchoolAoiInfo(com.sankuai.meituan.waimai.customer.domain.sc.WmScSchoolAreaDO,java.lang.Integer,java.lang.String)");
        log.info("[WmScSchoolAreaService.deleteSchoolAoiInfo] wmScSchoolAreaDO = {}, userId = {}, userName = {}",
                JSONObject.toJSONString(wmScSchoolAreaDO), userId, userName);
        WmScSchoolAreaDO wmScSchoolAreaDoUpdate = new WmScSchoolAreaDO();
        wmScSchoolAreaDoUpdate.setId(wmScSchoolAreaDO.getId());
        wmScSchoolAreaDoUpdate.setValid(ScConstants.SC_IS_DELETE);
        wmScSchoolAreaDoUpdate.setMuid(userId.longValue());

        int result = wmScSchoolAreaMapper.updateByPrimaryKeySelective(wmScSchoolAreaDoUpdate);
        if (result > 0) {
            // 记录到操作日志
            String logInfo = wmScLogSchoolInfoService.composeSchoolAreaDeleteLog(wmScSchoolAreaDO);
            if (StringUtils.isNotBlank(logInfo)) {
                wmScLogSchoolInfoService.insertScOptLog(OptTypeEnum.DELETE.getType(), SC_SCHOOLAREA_LOG, wmScSchoolAreaDO.getSchoolPrimaryId(),
                        userId, userName, logInfo, "");
            }
        }
        log.info("[WmScSchoolAreaService.deleteSchoolAoiInfo] delete aoi success. schoolPrimaryId = {}", wmScSchoolAreaDO.getSchoolPrimaryId());
    }

    /**
     * 删除学校AOI列表, 以及在AOI范围内的所有楼宇
     * @param schoolPrimaryId 学校主键ID
     * @param wmScSchoolAreaDODeleteList 要删除的学校AOI列表
     * @param userId 操作人ID
     * @param userName 操作人名称
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     * @throws TException org.apache.thrift.TException
     */
    public void deleteSchoolAoiInfoAndBuildingInfoInAoi(Integer schoolPrimaryId, List<WmScSchoolAreaDO> wmScSchoolAreaDODeleteList,
                                                        Integer userId, String userName) throws WmSchCantException, TException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.sc.WmScSchoolAreaService.deleteSchoolAoiInfoAndBuildingInfoInAoi(java.lang.Integer,java.util.List,java.lang.Integer,java.lang.String)");
        log.info("[WmScSchoolAreaService.deleteSchoolAoiInfoAndBuildingInfoInAoi] schoolPrimaryId = {}, wmScSchoolAreaDODeleteList = {}, userId = {}, userName = {}",
                schoolPrimaryId, JSONObject.toJSONString(wmScSchoolAreaDODeleteList), userId, userName);
        if (CollectionUtils.isEmpty(wmScSchoolAreaDODeleteList)) {
            return;
        }
        // 删除学校AOI信息并记录日志
        for (WmScSchoolAreaDO wmScSchoolAreaDO : wmScSchoolAreaDODeleteList) {
            deleteSchoolAoiInfo(wmScSchoolAreaDO, userId, userName);
        }

        List<WmScSchoolBuildingDO> wmScSchoolBuildingDOList = wmScSchoolBuildingMapper.selectBySchoolPrimaryId(schoolPrimaryId);
        if (CollectionUtils.isEmpty(wmScSchoolBuildingDOList)) {
            log.info("[WmScSchoolAreaService.deleteSchoolAoiInfoAndBuildingInfoInAoi] wmScSchoolBuildingDOList is empty, return.");
            return;
        }

        List<String> schoolAreaDeleteList = wmScSchoolAreaDODeleteList.stream()
                .filter(wmScSchoolAreaDO -> StringUtils.isNotBlank(wmScSchoolAreaDO.getArea()) && !"[]".equals(wmScSchoolAreaDO.getArea()))
                .map(WmScSchoolAreaDO::getArea)
                .collect(Collectors.toList());
        // 判断在被删除AOI范围内的楼宇信息
        List<WmScSchoolBuildingDO> wmScSchoolBuildingDODeleteList = new ArrayList<>();
        for (WmScSchoolBuildingDO wmScSchoolBuildingDO : wmScSchoolBuildingDOList) {
            if (StringUtils.isBlank(wmScSchoolBuildingDO.getBuildingCoordinate())
                    || "[]".equals(wmScSchoolBuildingDO.getBuildingCoordinate())) {
                continue;
            }
            // 楼宇坐标经纬度
            List<WmScPoint> wmScPoints = JSONObject.parseArray(wmScSchoolBuildingDO.getBuildingCoordinate(), WmScPoint.class);
            int buildingLatitude = wmScPoints.get(0).getX();
            int buidlingLongitude = wmScPoints.get(0).getY();
            // 楼宇坐标在被删除的AOI范围内
            if (WmRtreeUtil.withinAreaList(buildingLatitude, buidlingLongitude, schoolAreaDeleteList)) {
                wmScSchoolBuildingDODeleteList.add(wmScSchoolBuildingDO);
            }
        }

        if (CollectionUtils.isEmpty(wmScSchoolAreaDODeleteList)) {
            log.info("[WmScSchoolAreaService.deleteSchoolAoiInfoAndBuildingInfoInAoi] wmScSchoolAreaDODeleteList is empty, return.");
            return;
        }

        for (WmScSchoolBuildingDO wmScSchoolBuildingDO : wmScSchoolBuildingDODeleteList) {
            wmScSchoolBuildingService.deleteSchoolBuilding(wmScSchoolBuildingDO, userId, userName);
        }
    }

    /**
     * 根据学校主键ID查询AOI ID列表
     * @param schoolPrimaryId 学校主键ID
     * @return AOI ID列表
     */
    public List<Long> getSchoolAoiIdListBySchoolPrimaryId(Integer schoolPrimaryId) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.service.sc.WmScSchoolAreaService.getSchoolAoiIdListBySchoolPrimaryId(java.lang.Integer)");
        log.info("[WmScSchoolAreaService.getSchoolAoiIdList] input param: schoolPrimaryId = {}", schoolPrimaryId);
        List<Long> schoolAoiIdList = new ArrayList<>();
        // 查询该学校的所有AOI信息
        List<WmScSchoolAreaDO> wmScSchoolAreaDOList = wmScSchoolAreaMapper.selectBySchoolId(schoolPrimaryId);
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(wmScSchoolAreaDOList)) {
            return schoolAoiIdList;
        }
        // 组装学校AOI ID列表
        for (WmScSchoolAreaDO wmScSchoolAreaDO : wmScSchoolAreaDOList) {
            if (wmScSchoolAreaDO.getAoiId() != null && wmScSchoolAreaDO.getAoiId() > 0) {
                schoolAoiIdList.add(wmScSchoolAreaDO.getAoiId());
            }
        }
        log.info("[WmScSchoolAreaService.getSchoolAoiIdList] schoolAoiIdList = {}", JSONObject.toJSONString(schoolAoiIdList));
        return schoolAoiIdList;
    }

    /**
     * 根据学校主键ID查询学校范围列表
     * @param schoolPrimaryId 学校主键ID
     * @return 学校范围列表
     */
    public List<String> getSchoolAreaListBySchoolPrimaryId(Integer schoolPrimaryId) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.sc.WmScSchoolAreaService.getSchoolAreaListBySchoolPrimaryId(java.lang.Integer)");
        List<WmScSchoolAreaDO> wmScSchoolAreaDOList = wmScSchoolAreaMapper.selectBySchoolId(schoolPrimaryId);
        return wmScSchoolAreaDOList.stream()
                .filter(area -> StringUtils.isNotBlank(area.getArea()) && !"[]".equals(area.getArea()))
                .map(WmScSchoolAreaDO::getArea)
                .collect(Collectors.toList());
    }

    /**
     * 根据食堂主键ID查询学校范围列表
     * @param canteenPrimaryId 食堂主键ID
     * @return 学校范围列表
     */
    public List<String> getSchoolAreaListByCanteenPrimaryId(Integer canteenPrimaryId) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.service.sc.WmScSchoolAreaService.getSchoolAreaListByCanteenPrimaryId(java.lang.Integer)");
        WmCanteenDB wmCanteenDB = wmCanteenMapper.selectCanteenById(canteenPrimaryId);
        List<WmScSchoolAreaDO> schoolAreaDOList = wmScSchoolAreaMapper.selectBySchoolId(wmCanteenDB.getSchoolId());
        return schoolAreaDOList.stream()
                .filter(area -> StringUtils.isNotBlank(area.getArea()) && !"[]".equals(area.getArea()))
                .map(WmScSchoolAreaDO::getArea)
                .collect(Collectors.toList());
    }

    public Boolean confirmSchoolArea(Integer schoolPrimaryId, Integer uid, String operatorMis) throws WmSchCantException {
        if (wmScSchoolAreaMapper.confirmSchoolArea(schoolPrimaryId, operatorMis) > 0) {
            // 记录操作日志
            wmScLogSchoolInfoService.recordConfirmSchoolAreaLog(schoolPrimaryId, uid, operatorMis);
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }

}
