package com.sankuai.meituan.waimai.customer.service.dbus.customerPoiList;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import com.meituan.dbus.common.DbusUtils;
import com.meituan.dbus.thriftV2.utils.ThriftV2Utils;
import com.sankuai.meituan.waimai.customer.config.MccCustomerConfig;
import com.sankuai.meituan.waimai.customer.constant.WmCustomerPoiListESFields;
import com.sankuai.meituan.waimai.customer.constant.customer.WmPoiRelTableDbusEnum;
import com.sankuai.meituan.waimai.customer.dao.WmCustomerPoiDBMapper;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerPoiDB;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerPoiQueryCondtionVo;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerPoiListEsService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.elasticsearch.action.bulk.BulkResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Service
@Slf4j
public class WmPoiControlTableEventImpl implements IPoiRelTableEvent {

    @Autowired
    private WmCustomerPoiDBMapper wmCustomerPoiDBMapper;


    @Autowired
    private WmCustomerPoiListEsService esService;


    @Override
    public WmPoiRelTableDbusEnum getTable() {
        return WmPoiRelTableDbusEnum.TABLE_WM_POI_CONTROL;
    }

    @Override
    public String handleUpdate(Map<String, String> metaJsonData, String dataMapJson, String diffJson) {
        DbusUtils utils = ThriftV2Utils.newThriftV2InstanceForUpdate(metaJsonData, dataMapJson, diffJson);

        boolean isUpdate = checkUpdate(utils.getDiffMap());
        if (!isUpdate) {
            return null;
        }
        if(MccCustomerConfig.filterPHFPoiForDTSFlag()){
            Map<String, Object> dataMap = utils.getDataMap();
            //is_visible = 0 证明是拼好饭的门店直接过滤
            if(dataMap.get("is_visible") !=null && (Integer)dataMap.get("is_visible") == 0 ){
                return null;
            }
        }

        Map<String, Object> after = utils.getAftMap();

        List<WmCustomerPoiDB> list = getWmCustomerPoi((Integer) after.get(WmCustomerPoiListESFields.WM_POI_ID.getDbField()));
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        return update(list, after);
    }

    @Override
    public String handleInsert(Map<String, String> metaJsonData, String dataMapJson) {
        DbusUtils utils = ThriftV2Utils.newThriftV2InstanceForInsert(metaJsonData, dataMapJson);
        Map<String, Object> after = utils.getAftMap();

        List<WmCustomerPoiDB> list = getWmCustomerPoi((Integer) after.get(WmCustomerPoiListESFields.WM_POI_ID.getDbField()));
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        return update(list, after);
    }

    @Override
    public String handleDelete(Map<String, String> metaJsonData, String dataMapJson) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.service.dbus.customerPoiList.WmPoiControlTableEventImpl.handleDelete(java.util.Map,java.lang.String)");
        log.warn("门店控制信息删除,不应走到这里:metaJsonData={},dataMapJson={}", JSONObject.toJSONString(dataMapJson), dataMapJson);
        return null;
    }

    private boolean checkUpdate(Map<String, Object> diffMap) {
        if (MapUtils.isEmpty(diffMap) || !(diffMap.containsKey(WmCustomerPoiListESFields.POI_STATUS.getDbField()) || diffMap.containsKey(WmCustomerPoiListESFields.IS_DELETE.getDbField()))) {
            //修改，判断门店上单状态是否发生了变化，如果未发生变化则不更新
            return false;
        }
        return true;
    }

    private List<WmCustomerPoiDB> getWmCustomerPoi(Integer wmPoiId) {
        WmCustomerPoiQueryCondtionVo vo = new WmCustomerPoiQueryCondtionVo();
        vo.setWmPoiId(wmPoiId.longValue());
        return wmCustomerPoiDBMapper.selectCustomerPoiRelByCondition(vo);
    }

    private String update(List<WmCustomerPoiDB> list, Map<String, Object> after) {
        Integer poiStatus = (Integer) after.get(WmCustomerPoiListESFields.VALID.getDbField());
        Integer isDelete = (Integer) after.get(WmCustomerPoiListESFields.IS_DELETE.getDbField());

        Map<Object, Map<String, Object>> map = Maps.newHashMap();
        for (WmCustomerPoiDB db : list) {
            log.info("db={},poiStatus={},isDelete={}", poiStatus, isDelete);
            map.put(db.getId(), esService.makeMap(new String[]{WmCustomerPoiListESFields.POI_STATUS.getField(), WmCustomerPoiListESFields.IS_DELETE.getField()}, new Object[]{poiStatus, isDelete}));
        }
        BulkResponse response = esService.bulkUpdate(map);
        if (response == null) {
            return "门店状态更新失败";
        } else {
            return null;
        }
    }
}
