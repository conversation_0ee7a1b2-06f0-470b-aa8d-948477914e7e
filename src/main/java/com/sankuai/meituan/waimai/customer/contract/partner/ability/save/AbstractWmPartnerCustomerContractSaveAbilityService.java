package com.sankuai.meituan.waimai.customer.contract.partner.ability.save;

import com.alibaba.fastjson.JSON;
import com.google.common.base.MoreObjects;
import com.meituan.mtrace.Tracer;
import com.sankuai.inf.octo.mns.util.ProcessInfoUtil;
import com.sankuai.meituan.mtcoop.thrift.dto.TNeedResignCoopRequest;
import com.sankuai.meituan.util.DateUtil;
import com.sankuai.meituan.waimai.customer.adapter.WmCommercialAgentAdapter;
import com.sankuai.meituan.waimai.customer.adapter.agent.CustomerIdMappingServiceAdapter;
import com.sankuai.meituan.waimai.customer.adapter.daocan.CoopQueryServiceAdapter;
import com.sankuai.meituan.waimai.customer.config.MccConfig;
import com.sankuai.meituan.waimai.customer.contract.service.impl.WmContractAgentService;
import com.sankuai.meituan.waimai.customer.ddd.adapter.MtCustomerThriftServiceAdapter;
import com.sankuai.meituan.waimai.customer.domain.QueryCustomerKpDTO;
import com.sankuai.meituan.waimai.customer.domain.WmContractAgentInfo;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerDB;
import com.sankuai.meituan.waimai.customer.service.common.MtriceService;
import com.sankuai.meituan.waimai.customer.service.customer.WmAndDcCustomerRelService;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerService;
import com.sankuai.meituan.waimai.customer.service.kp.WmCustomerKpService;
import com.sankuai.meituan.waimai.domain.MultiFileJsonVo;
import com.sankuai.meituan.waimai.thrift.agent.domain.WmCommercialAgentInfo;
import com.sankuai.meituan.waimai.thrift.constant.WmContractErrorCodeConstant;
import com.sankuai.meituan.waimai.thrift.customer.constant.BusinessTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerConstants;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerErrorCodeConstants;
import com.sankuai.meituan.waimai.thrift.customer.constant.WmTempletContractTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.config.ContractSourceEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.CustomerPaperContractRemarkBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.MultiFileJsonBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.WmTempletContractBasicBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.WmTempletContractSignBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.framecontract.DcC1ExchangeInfo;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.partnercontract.CustomerContractSaveRequestDTO;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.partnercontract.CustomerContractSaveResponseDTO;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.WmCustomerBasicBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.kp.WmCustomerKp;
import com.sankuai.meituan.waimai.thrift.customer.domain.partner.DaoCanContractInfo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import com.sankuai.meituan.waimai.util.DaxiangUtil;
import com.sankuai.nibcus.inf.customer.client.enums.BusinessLineEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

/**
 * @description:
 * @author: liuyunjie05
 * @create: 2024/8/6 11:20
 */
@Slf4j
public abstract class AbstractWmPartnerCustomerContractSaveAbilityService implements WmPartnerCustomerContractSaveAbilityService {

    protected static final String DEFAULT_CONTACT_NUMBER = "电子合同保存后自动生成编号";

    @Resource
    private WmCommercialAgentAdapter wmCommercialAgentAdapter;

    @Resource
    private WmCustomerService wmCustomerService;

    @Resource
    private WmCustomerKpService wmCustomerKpService;

    @Resource
    private CustomerIdMappingServiceAdapter customerIdMappingServiceAdapter;

    @Resource
    private CoopQueryServiceAdapter coopQueryServiceAdapter;

    @Resource
    private MtCustomerThriftServiceAdapter mtCustomerThriftServiceAdapter;

    @Resource
    private WmAndDcCustomerRelService wmAndDcCustomerRelService;

    @Resource
    private MtriceService mtriceService;

    @Resource
    private WmContractAgentService wmContractAgentService;

    protected CustomerContractSaveResponseDTO fail(String failMsg, Long mtCustomerId) throws WmCustomerException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.contract.partner.ability.save.AbstractWmPartnerCustomerContractSaveAbilityService.fail(java.lang.String,java.lang.Long)");
        mtriceService.metricFailSaveDaoCanContract("保存失败");
        CustomerContractSaveResponseDTO responseDTO = new CustomerContractSaveResponseDTO();
        responseDTO.setSuccess(false);
        responseDTO.setFailMsg(failMsg);
        responseDTO.setWmCustomerId(getWmCustomerByMtCustomerId(mtCustomerId).longValue());
        return responseDTO;
    }

    protected CustomerContractSaveResponseDTO success(Long mtCustomerId) throws WmCustomerException {
        CustomerContractSaveResponseDTO responseDTO = new CustomerContractSaveResponseDTO();
        responseDTO.setSuccess(true);
        responseDTO.setWmCustomerId(getWmCustomerByMtCustomerId(mtCustomerId).longValue());
        return responseDTO;
    }

    protected void metricAndAlertMsg(String scene, String alertMsg, Exception e) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_2", "com.sankuai.meituan.waimai.customer.contract.partner.ability.save.AbstractWmPartnerCustomerContractSaveAbilityService.metricAndAlertMsg(java.lang.String,java.lang.String,java.lang.Exception)");
        mtriceService.metricFailSaveDaoCanContract(scene);

        if (MccConfig.isNeedAlertFailByDaXiang() && shouldSendAlert(e)) {
            String environment = String.valueOf(ProcessInfoUtil.getHostEnv());
            String message = String.format("%s环境, %s, trace: %s", environment, alertMsg, Tracer.id());
            DaxiangUtil.push("<EMAIL>", message, MccConfig.getDaXiangAlarmMisList());
        }
    }

    private boolean shouldSendAlert(Exception e) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_2", "com.sankuai.meituan.waimai.customer.contract.partner.ability.save.AbstractWmPartnerCustomerContractSaveAbilityService.shouldSendAlert(java.lang.Exception)");
        if (!(e instanceof WmCustomerException)) {
            return true;
        }

        String errorMessage = ((WmCustomerException) e).getMsg();
        return !containsNoAlertReason(errorMessage);
    }

    private boolean containsNoAlertReason(String errorMessage) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_2", "com.sankuai.meituan.waimai.customer.contract.partner.ability.save.AbstractWmPartnerCustomerContractSaveAbilityService.containsNoAlertReason(java.lang.String)");
        return MccConfig.noNeedAlertDcContractFailReason().stream()
                .anyMatch(errorMessage::contains);
    }

    protected void checkCustomerContractParam(CustomerContractSaveRequestDTO requestDTO) throws WmCustomerException {
        if (requestDTO.getMtCustomerId() == null) {
            throw new WmCustomerException(CustomerErrorCodeConstants.PARAM_ERROR, "参数异常: 缺少平台客户ID");
        }
        if (requestDTO.getContractType() == null) {
            throw new WmCustomerException(CustomerErrorCodeConstants.PARAM_ERROR, "参数异常: 缺少合同类型");
        }
        long wmCustomerId = getWmCustomerByMtCustomerId(requestDTO.getMtCustomerId());
        if (wmCustomerId <= 0) {
            throw new WmCustomerException(CustomerErrorCodeConstants.BUSINESS_ERROR, "没有匹配到外卖客户ID");
        }
        if (!isSingleCustomerKp(requestDTO.getMtCustomerId())) {
            throw new WmCustomerException(CustomerErrorCodeConstants.PARAM_ERROR, "该客户匹配到多个KP信息");
        }
        if (requestDTO.getOperatorDTO() == null || requestDTO.getOperatorDTO().getOpId() == null) {
            throw new WmCustomerException(CustomerErrorCodeConstants.PARAM_ERROR, "参数异常: 操作人信息异常");
        }
    }

    private boolean isSingleCustomerKp(long mtCustomerId) throws WmCustomerException {
        try {
            List<WmCustomerKp> customerKpList = wmCustomerKpService.getCustomerSignerEffectiveByBusiness(buildQueryCustomerKpDTO(mtCustomerId));
            return !CollectionUtils.isEmpty(customerKpList) && customerKpList.size() == 1;
        } catch (Exception e) {
            log.error("WaimaiBatchSaveC2ContractServiceImpl#isSingleCustomerKp, error", e);
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "查询到餐KP信息异常");
        }
    }

    /**
     * 初始化到餐甲方信息
     *
     * @param wmCustomerId 外卖客户ID
     * @return 到餐甲方信息
     * @throws WmCustomerException 异常
     */
    protected WmTempletContractSignBo initDaoCanPartA(int wmCustomerId, long mtCustomerId) throws WmCustomerException {
        WmTempletContractSignBo signBo = new WmTempletContractSignBo();

        WmCustomerKp signerKp = getDaoCanCustomerSignerKp(mtCustomerId);
        WmCustomerDB wmCustomerDB = mtCustomerThriftServiceAdapter.getCustomerByIdAndBusinessLine(mtCustomerId, BusinessLineEnum.NIB_FOOD.getCode());
        signBo.setSignId(wmCustomerId);
        signBo.setTempletContractId(0);
        signBo.setSignPeople(signerKp.getCompellation());
        signBo.setSignPhone(signerKp.getPhoneNum());
        signBo.setSignName(StringUtils.defaultIfEmpty(wmCustomerDB.getCustomerName(), StringUtils.EMPTY));
        signBo.setSignTime(DateUtil.secondsToString(DateUtil.unixTime()));
        signBo.setSignType("A");
        return signBo;
    }

    protected WmCustomerBasicBo getWmCustomerById(int wmCustomerId) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.contract.partner.ability.save.AbstractWmPartnerCustomerContractSaveAbilityService.getWmCustomerById(int)");
        WmCustomerBasicBo customerBasicBo;
        try {
            customerBasicBo = wmCustomerService.getCustomerById(wmCustomerId);
        } catch (Exception e) {
            log.error("AbstractWmPartnerCustomerContractSaveAbilityService#getWmCustomerById, error", e);
            return new WmCustomerBasicBo();
        }
        if (customerBasicBo == null) {
            new WmCustomerBasicBo();
        }
        return customerBasicBo;
    }

    private WmCustomerKp getDaoCanCustomerSignerKp(long mtCustomerID) throws WmCustomerException {
        List<WmCustomerKp> customerKpList;
        try {
            customerKpList = wmCustomerKpService.getCustomerSignerEffectiveByBusiness(buildQueryCustomerKpDTO(mtCustomerID));
        } catch (Exception e) {
            log.error("AbstractWmPartnerCustomerContractSaveAbilityService#getCustomerSignerKp, error", e);
            throw new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR, "查询到餐签约人信息异常");
        }
        if (CollectionUtils.isEmpty(customerKpList) || customerKpList.size() > 1) {
            throw new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR, "查询到餐签约人信息异常");
        }
        return customerKpList.get(0);
    }

    protected Integer getWmCustomerByMtCustomerId(long mtCustomerId) throws WmCustomerException {
        int retryTimes = MccConfig.getRetryTimesToGetWmCustomerByMtCustomerId();
        for (int i = 0; i < retryTimes; ++i) {
            try {
                List<Integer> wmCustoomerIdList = wmAndDcCustomerRelService.getWmCustomerIdByDcPlatformId(mtCustomerId);
                if (CollectionUtils.isEmpty(wmCustoomerIdList)) {
                    log.warn("AbstractWmPartnerCustomerContractSaveAbilityService#getWmCustomerId, 没有查询到对应的外卖客户ID, 尝试次数: {}", i + 1);
                    Thread.sleep(1000);
                    continue;
                }
                if (wmCustoomerIdList.size() > 1) {
                    throw new WmCustomerException(CustomerErrorCodeConstants.BUSINESS_ERROR, "查询到多个外卖客户ID");
                }
                return wmCustoomerIdList.get(0);
            } catch (WmCustomerException e) {
                log.error("AbstractWmPartnerCustomerContractSaveAbilityService#getWmCustomerId, warn", e);
                throw e;
            } catch (Exception e) {
                log.error("AbstractWmPartnerCustomerContractSaveAbilityService#getWmCustomerId, error", e);
                throw new WmCustomerException(CustomerErrorCodeConstants.BUSINESS_ERROR, "查询外卖客户ID异常");
            }
        }
        throw new WmCustomerException(CustomerErrorCodeConstants.BUSINESS_ERROR, "没有查询到对应的外卖客户ID");
    }

    protected QueryCustomerKpDTO buildQueryCustomerKpDTO(long mtCustomerId) {
        QueryCustomerKpDTO queryCustomerKpDTO = new QueryCustomerKpDTO();
        queryCustomerKpDTO.setMtCustomerId(mtCustomerId);
        queryCustomerKpDTO.setBusinessTypeEnum(BusinessTypeEnum.DAOCAN);
        return queryCustomerKpDTO;
    }

    protected WmTempletContractBasicBo initBasicBo(Integer contractType, Integer wmCustomerId) {
        WmTempletContractBasicBo basicBo = new WmTempletContractBasicBo();
        basicBo.setContractNum(DEFAULT_CONTACT_NUMBER);
        basicBo.setContractSource(ContractSourceEnum.CODE.getCode());
        basicBo.setDueDate(0L);
        basicBo.setExpectEffectiveDate(0);
        basicBo.setType(contractType);
        basicBo.setExtStr(buildExtStr());
        basicBo.setParentId(wmCustomerId);
        return basicBo;
    }

    private String buildExtStr() {
        CustomerPaperContractRemarkBo paperContractRemarkBo = new CustomerPaperContractRemarkBo();
        paperContractRemarkBo.setContractScan(MultiFileJsonBo.toMultiFileJsonBo(JSON.toJSONString(new MultiFileJsonVo())));
        paperContractRemarkBo.setOtherContractScan(MultiFileJsonBo.toMultiFileJsonBo(JSON.toJSONString(new MultiFileJsonVo())));
        return JSON.toJSONString(paperContractRemarkBo);
    }

    protected WmTempletContractSignBo initAgentSignBo(int agentId) throws WmCustomerException {
        WmTempletContractSignBo signBo = new WmTempletContractSignBo();
        WmContractAgentInfo agentInfo = wmContractAgentService.queryAgentInfoById(agentId);
        signBo.setSignId(agentId);
        signBo.setSignName(agentInfo.getName());
        signBo.setSignPeople(agentInfo.getLegalPerson());
        signBo.setTempletContractId(0);
        signBo.setSignPhone(MoreObjects.firstNonNull(agentInfo.getLegalPersonPhone(), ""));
        signBo.setSignTime(DateUtil.secondsToString(DateUtil.unixTime()));
        return signBo;
    }

    /**
     * 判断是否需要换签到餐C1合同
     *
     * @param mtCustomerId 平台客户ID
     * @return 需要换签的到餐C1合同信息
     */
    protected List<DcC1ExchangeInfo> getNeedResinContractList(long mtCustomerId) {
        try {
            Long dcCustomerId = customerIdMappingServiceAdapter.getOriginCustomerIdByMtCustomerId(mtCustomerId);
            return coopQueryServiceAdapter.getNeedResignContractInfo(buildTNeedResignCoopRequest(dcCustomerId));
        } catch (Exception e) {
            log.error("AbstractWmPartnerCustomerContractSaveAbilityService#getNeedResinContractList, error", e);
            return Collections.emptyList();
        }
    }

    private TNeedResignCoopRequest buildTNeedResignCoopRequest(Long dcCustomerId) {
        TNeedResignCoopRequest request = new TNeedResignCoopRequest();
        request.setPartnerId(Math.toIntExact(dcCustomerId));
        return request;
    }

    /**
     * 初始化换签到餐C1的合同信息
     *
     * @param exchangeInfo 换签C1的信息
     * @return 到餐C1合同信息
     */
    protected DaoCanContractInfo initDcRenewC1ContractInfo(DcC1ExchangeInfo exchangeInfo, Long mtCustomerId) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.contract.partner.ability.save.AbstractWmPartnerCustomerContractSaveAbilityService.initDcRenewC1ContractInfo(DcC1ExchangeInfo,Long)");
        DaoCanContractInfo daoCanContractInfo = new DaoCanContractInfo();
        daoCanContractInfo.setContractProof(exchangeInfo.getContractProof());
        daoCanContractInfo.setContractType(WmTempletContractTypeEnum.DAOCAN_SERVICE_C1_CONTRACT.getCode());
        daoCanContractInfo.setNewSignContract(false);
        daoCanContractInfo.setCoopType(exchangeInfo.getCoopType());
        daoCanContractInfo.setNeedCheckC1Renew(false);
        daoCanContractInfo.setMtCustomerId(mtCustomerId);
        return daoCanContractInfo;
    }

    /**
     * 初始化到餐C2的合同信息
     *
     * @param needCheckC1Renew 是否需要校验换签C1
     * @return 到餐C2合同信息
     */
    protected DaoCanContractInfo initDaoCanC2ContractInfo(boolean needCheckC1Renew, Long mtCustomerId) {
        DaoCanContractInfo daoCanContractInfo = new DaoCanContractInfo();
        daoCanContractInfo.setNeedCheckC1Renew(needCheckC1Renew);
        daoCanContractInfo.setContractType(WmTempletContractTypeEnum.DAOCAN_SERVICE_C2_CONTRACT.getCode());
        daoCanContractInfo.setMtCustomerId(mtCustomerId);
        return daoCanContractInfo;
    }

}
