package com.sankuai.meituan.waimai.customer.service.sign.base;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.dianping.cat.util.MetricHelper;
import com.dianping.squirrel.client.StoreKey;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.meituan.waimai.customer.constant.customer.MetricConstant;
import com.sankuai.meituan.waimai.customer.bo.sign.TaskSearchForSwitchCancelWmPoiParam;
import com.sankuai.meituan.waimai.customer.contract.domain.WmTempletContractDB;
import com.sankuai.meituan.waimai.customer.contract.service.impl.WmContractService;
import com.sankuai.meituan.waimai.customer.dao.WmCustomerPoiDBMapper;
import com.sankuai.meituan.waimai.customer.domain.WmEContractSignBaseDB;
import com.sankuai.meituan.waimai.customer.domain.WmEcontractPoiSignStateDB;
import com.sankuai.meituan.waimai.customer.service.cache.RedisKvService;
import com.sankuai.meituan.waimai.customer.service.common.MtriceService;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerPoiService;
import com.sankuai.meituan.waimai.customer.service.sign.applyecontract.WmEcontractBatchApplyService;
import com.sankuai.meituan.waimai.customer.service.sign.applytask.WmEcontractApplyBizService;
import com.sankuai.meituan.waimai.customer.service.sign.batch.WmEcontractBatchService;
import com.sankuai.meituan.waimai.customer.service.sign.biz.WmEcontractBaseBizService;
import com.sankuai.meituan.waimai.customer.service.sign.biz.WmEcontractBatchBizService;
import com.sankuai.meituan.waimai.customer.service.sign.biz.WmEcontractManualBatchBizService;
import com.sankuai.meituan.waimai.customer.service.sign.biz.WmEcontractManualTaskBizService;
import com.sankuai.meituan.waimai.customer.service.sign.biz.WmEcontractPoiStateBizService;
import com.sankuai.meituan.waimai.customer.service.sign.biz.WmEcontractTaskBizService;
import com.sankuai.meituan.waimai.customer.service.sign.callback.handler.WmEcontractCallbackFailHandler;
import com.sankuai.meituan.waimai.customer.service.sign.callback.handler.WmEcontractCallbackSuccessHandler;
import com.sankuai.meituan.waimai.customer.service.sign.cancel.WmEcontractCancelBzService;
import com.sankuai.meituan.waimai.customer.service.sign.cancel.WmEcontractCancelManualTaskService;
import com.sankuai.meituan.waimai.customer.service.sign.cancel.WmEcontractCancelService;
import com.sankuai.meituan.waimai.customer.service.sign.pack.WmEcontractBasePackService;
import com.sankuai.meituan.waimai.customer.util.business.WmContractUtil;
import com.sankuai.meituan.waimai.thrift.config.ConfigUtilAdapter;
import com.sankuai.meituan.waimai.thrift.constant.WmContractErrorCodeConstant;
import com.sankuai.meituan.waimai.thrift.customer.constant.WmTempletContractTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractBaseApplyTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractTaskApplyTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractTaskStateEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.WmEcontractBatchConstant;
import com.sankuai.meituan.waimai.thrift.customer.domain.base.BooleanResult;
import com.sankuai.meituan.waimai.thrift.customer.domain.base.LongResult;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.*;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.thrift.TException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

@Service
public class WmEcontractBaseApplyService {

    private static Logger LOGGER = LoggerFactory.getLogger(WmEcontractCallbackSuccessHandler.class);

    public static final String FINISH = "econtract_finish";

    @Autowired
    private WmCustomerPoiDBMapper wmCustomerPoiDBMapper;

    @Resource
    private WmContractService wmContractService;

    @Resource
    private WmEcontractBaseInitService wmEcontractBaseInitService;

    @Resource
    private WmEcontractBasePackService wmEcontractBasePackService;

    @Resource
    private WmEcontractBatchService wmEcontractBatchService;

    @Resource
    private WmEcontractApplyBizService wmEcontractApplyBizService;

    @Resource
    private WmEcontractBaseBizService wmEcontractBaseBizService;

    @Resource
    private WmEcontractTaskBizService wmEcontractTaskBizService;

    @Resource
    private WmEcontractPoiStateBizService wmEcontractPoiStateBizService;

    @Resource
    private WmEcontractBatchBizService wmEcontractBatchBizService;

    @Resource
    private WmEcontractBatchApplyService wmEcontractBatchApplyService;

    @Resource
    private WmEcontractCallbackFailHandler wmEcontractCallbackFailHandler;

    @Resource
    private WmEcontractCancelBzService wmEcontractCancelBzService;

    @Resource
    private WmEcontractCancelService wmEcontractCancelService;

    @Autowired
    private WmEcontractManualBatchBizService wmEcontractManualBatchBizService;

    @Autowired
    private WmEcontractManualTaskBizService wmEcontractManualTaskBizService;

    @Autowired
    private WmEcontractCancelManualTaskService wmEcontractCancelManualTaskService;

    @Autowired
    private WmCustomerPoiService wmCustomerPoiService;

    @Autowired
    private RedisKvService redisKvService;

    @Autowired
    private MtriceService mtriceService;

    private static final String APPLY_BASE_KEY_REPFIX = "applybase_lock_key";

    private final String AUTOPACK = "autopack";

    private final String MANUALPACK = "manualpack";

    private final String SINGLE = "single";

    /**
     * 申请基本信息
     *
     * @param applyBo 申请信息
     */
    public LongResult applyBase(EcontractBaseApplyBo applyBo)
            throws TException, WmCustomerException, IllegalAccessException {
        LOGGER.info("#applyBase,applyBo={}",JSONObject.toJSONString(applyBo));
        if(ConfigUtilAdapter.getBoolean("applybase_try_lock",true)){
            LOGGER.info("#applyBase with lock，customer:{}，applyType:{}",applyBo.getCustomerId(), applyBo.getApplyTypeEnum());
            try{
                boolean isAutoPackType = EcontractBaseApplyTypeEnum.CUSTOMER.equals(applyBo.getApplyTypeEnum())
                        || EcontractBaseApplyTypeEnum.KP.equals(applyBo.getApplyTypeEnum());
                //CUSTOMER与KP类型需要获取锁才能执行
                if(isAutoPackType){
                    LOGGER.info("#applyBase客户KP类型，尝试获取锁，customer:{}，applyType:{}",applyBo.getCustomerId(), applyBo.getApplyTypeEnum());
                    if(redisKvService.tryLock(genApplyBaseKey(applyBo), 60)){
                        return applyBaseCommon(applyBo);
                    }else{
                        LOGGER.info("#applyBase未获取到锁，customer:{}，applyType:{}",applyBo.getCustomerId(), applyBo.getApplyTypeEnum());
                    }
                }else{
                    //其他类型的任务维持原流程
                    LOGGER.info("#applyBase非客户KP类型，无需获取锁，customer:{}，applyType:{}",applyBo.getCustomerId(), applyBo.getApplyTypeEnum());
                    return applyBaseCommon(applyBo);
                }
            }finally {
                redisKvService.unLock(genApplyBaseKey(applyBo));
            }
        }else{
            LOGGER.info("#applyBase without lock，customer:{}，applyType:{}",applyBo.getCustomerId(), applyBo.getApplyTypeEnum());
            return applyBaseCommon(applyBo);
        }
        return new LongResult(applyBo.getCustomerId());
    }

    private LongResult applyBaseCommon(EcontractBaseApplyBo applyBo)
            throws TException, WmCustomerException, IllegalAccessException{
        try{
            //初始化base信息，base信息落库
            WmEContractSignBaseDB baseDB = wmEcontractBaseInitService.init(applyBo);
            //检查poistate信息生成后是否有要触发的任务
            EcontractBaseStateContextBo baseContextBo = wmEcontractBasePackService.pack(baseDB);
            //校验基本信息是否完备
            if (!wmEcontractApplyBizService.initCheck(baseContextBo.getCustomerInfoBo(), baseContextBo.getKpBo())) {
                return new LongResult(applyBo.getCustomerId());
            }
            //Base信息申请
            List<EcontractBatchContextBo> batchContextBoList = wmEcontractBatchService.batch(baseContextBo);
            //更新task信息和poiState信息,产生回调
            wmEcontractApplyBizService.handleBatchCallback(batchContextBoList);
        }catch(Exception e){
            LOGGER.error("applyBaseCommon发起签约失败,customerId:{}", applyBo.getCustomerId(), e);
            mtriceService.metricContractApplySignFail(AUTOPACK);
        }
        return new LongResult(applyBo.getCustomerId());
    }

    private StoreKey genApplyBaseKey(EcontractBaseApplyBo applyBo){
        return new StoreKey(APPLY_BASE_KEY_REPFIX, applyBo.getCustomerId());
    }

    /**
     * 删除客户: 导致客户base信息废除, 客户关联门店poistate信息废除, 任务撤回(撤回task, )
     *
     * @param customerId 客户ID
     * @return 取消base信息
     */
    public BooleanResult cancelBase(Integer customerId) throws TException, WmCustomerException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.service.sign.base.WmEcontractBaseApplyService.cancelBase(java.lang.Integer)");
        LOGGER.info("cancelBase customerId = {}", customerId);
        List<EcontractTaskBo> taskBoList = wmEcontractTaskBizService
                .getByCustomerIdAndTypeList(customerId,
                        Lists.newArrayList(EcontractTaskStateEnum.HOLDING.getName(),
                                EcontractTaskStateEnum.IN_PROCESSING.getName()));
        if (CollectionUtils.isEmpty(taskBoList)) {
            wmEcontractPoiStateBizService.deleteByCustomerId(customerId);
            return new BooleanResult(Boolean.TRUE);
        }
        for (EcontractTaskBo taskBo : taskBoList) {
            wmEcontractCancelService
                    .cancel(taskBo, "客户" + customerId + "删除", WmEcontractBatchConstant.CANCEL_BASE);
        }
        wmEcontractPoiStateBizService.deleteByCustomerId(customerId);

        //待手动打包任务删除
        wmEcontractManualBatchBizService.deleteByCustomerId(customerId);
        wmEcontractManualTaskBizService.deleteByCustomerId(customerId);
        return new BooleanResult(Boolean.TRUE);
    }

    /**
     * 解绑门店，删除门店任务(门店配送分成) 删除客户门店PoiState 撤回正在流程中的batch
     *
     * @param customerId 客户ID
     * @param wmPoiId 门店ID
     */
    public BooleanResult unbingPoi(Integer customerId, Long wmPoiId)
            throws WmCustomerException, TException {
        LOGGER.info("unbingPoi customerId = {}, wmPoiId = {}", customerId, wmPoiId);

        // 手动打包
        wmEcontractCancelManualTaskService.cancelByUnbindingPoi(customerId, wmPoiId);

        // 删除门店任务
        Map<String, EcontractTaskBo> taskBoMap = wmEcontractTaskBizService.getByCustomerIdAndWmPoiId(customerId,
                wmPoiId, Lists.newArrayList(EcontractTaskStateEnum.HOLDING.getName(),
                        EcontractTaskStateEnum.IN_PROCESSING.getName()));
        if (MapUtils.isEmpty(taskBoMap)) {
            wmEcontractPoiStateBizService.deleteByCustomerIdAndWmPoiIdList(customerId, Lists.newArrayList(wmPoiId));
            return new BooleanResult(Boolean.TRUE);
        }
        for (Map.Entry<String, EcontractTaskBo> entry : taskBoMap.entrySet()) {
            wmEcontractCancelService.cancel(entry.getValue(), "门店" + wmPoiId + "与客户解绑",
                    WmEcontractBatchConstant.UNBIND_POI);
        }

        // 删除客户与门店的状态
        wmEcontractPoiStateBizService.deleteByCustomerIdAndWmPoiIdList(customerId, Lists.newArrayList(wmPoiId));
        return new BooleanResult(Boolean.TRUE);
    }

    /**
     * 客户绑定门店-主要影响C1合同 生成poistate，生成时注意查客户状态更新到poistate中
     *
     * @param customerId 客户ID
     * @param wmPoiIdList 门店ID列表
     */
    public BooleanResult bindPoi(Integer customerId, List<Long> wmPoiIdList)
            throws WmCustomerException, TException, IllegalAccessException {
        EcontractSignStateBo contractSignStateBo = new EcontractSignStateBo();

        //兼容绑定门店处理在其他模块处理之后,wm_econtract_poi_sign_state不可被重置
        List<WmEcontractPoiSignStateDB> customerIdAndWmPoiIdListMaster = wmEcontractPoiStateBizService
                .getByCustomerIdAndWmPoiIdListMaster(customerId, wmPoiIdList);

        Map<Long, WmEcontractPoiSignStateDB> poiSignStateDBMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(customerIdAndWmPoiIdListMaster)) {
            LOGGER.warn("#bindPoi#into#customerIdAndWmPoiIdListMaster={}",
                    JSONObject.toJSONString(customerIdAndWmPoiIdListMaster));
            for (WmEcontractPoiSignStateDB temp : customerIdAndWmPoiIdListMaster) {
                poiSignStateDBMap.put(temp.getWmPoiId(), temp);
            }
        }
        boolean hasIsAgentWmPoiIdBoolean=wmCustomerPoiService.isSinglePoiAgentBoolean(customerId);
        contractSignStateBo = calContractState(contractSignStateBo, customerId);
        if(hasIsAgentWmPoiIdBoolean){
            contractSignStateBo = calC2ContractState(contractSignStateBo, customerId);
        }
        LOGGER.info("contractSignStateBocontractSignStateBo:[{}]",JSON.toJSONString(contractSignStateBo));
        EcontractSignStateBo signStateBoTemp = null;

        //生成poiState信息
        List<WmEcontractPoiSignStateDB> poiStateDBList = Lists.newArrayList();
        for (Long wmPoiId : wmPoiIdList) {
            WmEcontractPoiSignStateDB poiStateDB = new WmEcontractPoiSignStateDB();
            poiStateDB.setCustomerId(customerId);
            poiStateDB.setWmPoiId(wmPoiId);
            signStateBoTemp = new EcontractSignStateBo();
            if (poiSignStateDBMap.containsKey(wmPoiId)) {
                signStateBoTemp = JSONObject
                        .parseObject(poiSignStateDBMap.get(wmPoiId).getSignState(),
                                EcontractSignStateBo.class);
            }
            signStateBoTemp.setContractTaskId(contractSignStateBo.getContractTaskId());
            signStateBoTemp.setContractState(contractSignStateBo.getContractState());

            signStateBoTemp.setContractC2TaskId(contractSignStateBo.getContractC2TaskId());
            signStateBoTemp.setContractC2State(contractSignStateBo.getContractC2State());

            poiStateDB.setSignState(JSON.toJSONString(signStateBoTemp));
            poiStateDBList.add(poiStateDB);
        }

        wmEcontractPoiStateBizService.batchSave(poiStateDBList,hasIsAgentWmPoiIdBoolean);

        //申请基本类型
        EcontractBaseApplyBo applyBo = new EcontractBaseApplyBo.Builder()
                .customerId(customerId)
                .applyTypeEnum(EcontractBaseApplyTypeEnum.POI)
                .applyInfoBo(StringUtils.EMPTY)
                .build();
        this.applyBase(applyBo);
        return new BooleanResult(Boolean.TRUE);
    }

    /**
     * 计算合同状态
     */
    private EcontractSignStateBo calContractState(EcontractSignStateBo signStateBo,
            Integer customerId) {
        List<WmTempletContractDB> templetContractDBList =
                wmContractService.getEffectContractByCustomerIdAndType(customerId,
                        Lists.newArrayList(
                                WmTempletContractTypeEnum.C1_PAPER.getCode(),
                                WmTempletContractTypeEnum.C1_E.getCode()));
        if (CollectionUtils.isEmpty(templetContractDBList)) {
            EcontractTaskBo taskBo = wmEcontractTaskBizService
                    .getLastByCustomerIdAndType(customerId,
                            EcontractTaskApplyTypeEnum.C1CONTRACT.getName());
            if (taskBo == null) {
                signStateBo.setContractTaskId(0L);
                signStateBo.setContractState(EcontractTaskStateEnum.TO_COMMIT.getName());
            } else {
                signStateBo.setContractTaskId(taskBo.getId());
                signStateBo.setContractState(taskBo.getApplyState());
            }
        } else {
            EcontractTaskBo taskBo = wmEcontractTaskBizService
                    .getLastEffectContractByCustomerIdAndType(customerId);
            if (WmContractUtil.hasEContract(templetContractDBList)) {
                signStateBo.setContractTaskId(taskBo != null ? taskBo.getId() : 0L);
                signStateBo.setContractState(EcontractTaskStateEnum.SUCCESS.getName());
            } else {
                signStateBo.setContractTaskId(0L);
                signStateBo.setContractState(EcontractTaskStateEnum.NOT_SIGN.getName());
            }
        }
        return signStateBo;
    }
    /**
     * 计算合同状态
     */
    private EcontractSignStateBo calC2ContractState(EcontractSignStateBo signStateBo,
                                                    Integer customerId) {
        List<WmTempletContractDB> templetContractDBList =
                wmContractService.getEffectContractByCustomerIdAndType(customerId,
                        Lists.newArrayList(
                                WmTempletContractTypeEnum.C2_PAPER.getCode(),
                                WmTempletContractTypeEnum.C2_E.getCode()));
        if (CollectionUtils.isEmpty(templetContractDBList)) {
            EcontractTaskBo taskBo = wmEcontractTaskBizService
                    .getLastByCustomerIdAndType(customerId,
                            EcontractTaskApplyTypeEnum.C2CONTRACT.getName());
            if (taskBo == null) {
                signStateBo.setContractC2TaskId(0L);
                signStateBo.setContractC2State(EcontractTaskStateEnum.TO_COMMIT.getName());
            } else {
                signStateBo.setContractC2TaskId(taskBo.getId());
                signStateBo.setContractC2State(taskBo.getApplyState());
            }
        } else {
            EcontractTaskBo taskBo = wmEcontractTaskBizService
                    .getLastEffectC2ContractByCustomerIdAndType(customerId);
            if (WmContractUtil.hasEContract(templetContractDBList)) {
                signStateBo.setContractC2TaskId(taskBo != null ? taskBo.getId() : 0L);
                signStateBo.setContractC2State(EcontractTaskStateEnum.SUCCESS.getName());
            } else {
                signStateBo.setContractC2TaskId(0L);
                signStateBo.setContractC2State(EcontractTaskStateEnum.NOT_SIGN.getName());
            }
        }
        return signStateBo;
    }

    public BooleanResult cancelWmCustomerSwitch(Integer targetCustomerId, List<Long> wmPoiIdList, int opUid, String opUname)
            throws WmCustomerException, TException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.sign.base.WmEcontractBaseApplyService.cancelWmCustomerSwitch(java.lang.Integer,java.util.List,int,java.lang.String)");
        //删除手动打包
        wmEcontractCancelManualTaskService.batchCancelWmCustomerSwitch(targetCustomerId, wmPoiIdList);
        //删除门店任务
        Map<String, EcontractTaskBo> taskBoMap = wmEcontractTaskBizService.getByCustomerIdAndWmPoiIdList(targetCustomerId, wmPoiIdList,
                Lists.newArrayList(EcontractTaskStateEnum.HOLDING.getName(), EcontractTaskStateEnum.IN_PROCESSING.getName()));
        if (MapUtils.isEmpty(taskBoMap)) {
            wmEcontractPoiStateBizService.deleteByCustomerIdAndWmPoiIdList(targetCustomerId, wmPoiIdList);
            return new BooleanResult(Boolean.TRUE);
        }
        for (Map.Entry<String, EcontractTaskBo> entry : taskBoMap.entrySet()) {
            wmEcontractCancelService.cancel(entry.getValue(), "客户:" + targetCustomerId + "取消切换", WmEcontractBatchConstant.CANCEL_CUSTOMER_SWITCH);
        }
        //删除客户与门店状态
        wmEcontractPoiStateBizService
                .deleteByCustomerIdAndWmPoiIdList(targetCustomerId, wmPoiIdList);
        return new BooleanResult(true);
    }

    public BooleanResult cancelWmCustomerSwitchForWmPoi(CancelSignForSwitchParam param)
            throws WmCustomerException, TException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.sign.base.WmEcontractBaseApplyService.cancelWmCustomerSwitchForWmPoi(com.sankuai.meituan.waimai.thrift.customer.domain.sign.CancelSignForSwitchParam)");
        LOGGER.info("#cancelWmCustomerSwitchForWmPoi,param={}",JSONObject.toJSONString(param));
        Long taskId = param.getTaskId();
        Integer targetCustomerId = param.getTargetCustomerId();
        List<Long> wmPoiIdList = param.getWmPoiIdList();
        //删除手动打包
        wmEcontractCancelManualTaskService.batchCancelWmCustomerSwitchForDelivery(targetCustomerId, wmPoiIdList);
        //删除门店任务
        TaskSearchForSwitchCancelWmPoiParam taskSearchParam = new TaskSearchForSwitchCancelWmPoiParam();
        taskSearchParam.setTaskId(taskId);
        taskSearchParam.setCustomerId(targetCustomerId);
        taskSearchParam.setWmPoiIdList(wmPoiIdList);
        taskSearchParam.setStateList(Lists.newArrayList(EcontractTaskStateEnum.HOLDING.getName(), EcontractTaskStateEnum.IN_PROCESSING.getName()));
        Map<String, EcontractTaskBo> taskBoMap = wmEcontractTaskBizService.getMatchedDeliveryTaskForSwitchCancelWmPoi(taskSearchParam);
        LOGGER.info("计算待取消任务结果:taskBoMap ={}",JSONObject.toJSONString(taskBoMap));
        for (Map.Entry<String, EcontractTaskBo> entry : taskBoMap.entrySet()) {
            wmEcontractCancelService.cancel(entry.getValue(), "客户:" + targetCustomerId + "取消切换", WmEcontractBatchConstant.CANCEL_CUSTOMER_SWITCH);
        }
        //删除客户与门店状态
        wmEcontractPoiStateBizService.deleteByCustomerIdAndWmPoiIdList(targetCustomerId, wmPoiIdList);
        return new BooleanResult(Boolean.TRUE);
    }

}
