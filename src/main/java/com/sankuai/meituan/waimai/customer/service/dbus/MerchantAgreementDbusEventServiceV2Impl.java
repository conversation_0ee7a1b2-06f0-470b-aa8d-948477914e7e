package com.sankuai.meituan.waimai.customer.service.dbus;

import com.alibaba.fastjson.JSONObject;
import com.sankuai.meituan.waimai.customer.service.agreement.WmAgreementSyncService;
import com.sankuai.meituan.waimai.thrift.customer.constant.agreement.AgreementConstants;
import com.sankuai.meituan.waimai.thrift.util.ConfigUtilAdapter;
import java.util.Map;

import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.meituan.dbus.common.DbusUtils;
import com.meituan.dbus.common.StaticUtils;
import com.meituan.dbus.thriftV2.DataBusEventServiceV2;
import com.meituan.dbus.thriftV2.utils.ThriftV2Utils;
import com.sankuai.meituan.waimai.customer.constant.agreement.WmFoodSafetyPromiseConstant;
import com.sankuai.meituan.waimai.customer.domain.agreement.WmFoodSafetyPromiseDB;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class MerchantAgreementDbusEventServiceV2Impl implements DataBusEventServiceV2.Iface{

    @Autowired
    private WmAgreementSyncService wmAgreementSyncService;

    @Override
    public String handleUpdate(Map<String, String> metaJsonData, String dataMapJson,
            String diffJson) throws TException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.service.dbus.MerchantAgreementDbusEventServiceV2Impl.handleUpdate(java.util.Map,java.lang.String,java.lang.String)");
//        log.info("#MerchantAgreementDbusEvent#handleUpdate,diffJson={}",JSONObject.toJSON(diffJson));
        if (switchConsume()) {
            return StaticUtils.ok;
        }
        DbusUtils utils = ThriftV2Utils.newThriftV2InstanceForUpdate(metaJsonData, dataMapJson, diffJson);
        return handleDataChange(utils);
    }

    private boolean switchConsume() {
        return ConfigUtilAdapter.getBoolean("consume_MerchantAgreementDbus_close",true);
    }

    @Override
    public String handleInsert(Map<String, String> metaJsonData, String dataMapJson)
            throws TException {
//        log.info("#MerchantAgreementDbusEvent#handleInsert,dataMapJson={}",JSONObject.toJSONString(dataMapJson));
        if (switchConsume()) {
            return StaticUtils.ok;
        }
        DbusUtils utils = ThriftV2Utils.newThriftV2InstanceForInsert(metaJsonData, dataMapJson);
        return handleDataChange(utils);
    }

    @Override
    public String handleDelete(Map<String, String> metaJsonData, String dataMapJson)
            throws TException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_2", "com.sankuai.meituan.waimai.customer.service.dbus.MerchantAgreementDbusEventServiceV2Impl.handleDelete(java.util.Map,java.lang.String)");
        return StaticUtils.ok;
    }

    private String handleDataChange(DbusUtils utils){
        WmFoodSafetyPromiseDB db = transToDb(utils);
        if(ConfigUtilAdapter.getBoolean("MerchantAgreementDbus_changelog_open",false)){
            log.info("#WmFoodSafetyPromiseDB={}",JSONObject.toJSONString(db));
        }
        int type = db.getType();
        //数据变更来源自供应链侧-无需处理
        if(WmFoodSafetyPromiseConstant.SOURCE_SUPPLY_CHAIN.equals(db.getDesc())){
            return StaticUtils.ok;
        }
        if (AgreementConstants.AGREEMENT_TYPE_LIST.contains(type)) {
            return upsert(db);
        }else{
            return StaticUtils.ok;
        }
    }

    private WmFoodSafetyPromiseDB transToDb(DbusUtils utils){
        Map<String, Object> aftMap = utils.getAftMap();
        String jsonString = JSON.toJSONString(aftMap);
        return JSON.parseObject(jsonString, WmFoodSafetyPromiseDB.class);
    }

    private String upsert(WmFoodSafetyPromiseDB db) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_1", "com.sankuai.meituan.waimai.customer.service.dbus.MerchantAgreementDbusEventServiceV2Impl.upsert(com.sankuai.meituan.waimai.customer.domain.agreement.WmFoodSafetyPromiseDB)");
        boolean syncResult = false;
        try{
            syncResult = wmAgreementSyncService.syncOldDataChangeToNewData(db);
        }catch(Exception e){
            log.error("syncOldDataChangeToNewData出错了,db={}",JSONObject.toJSONString(db),e);
        }
        return syncResult ? StaticUtils.ok : StaticUtils.fail;
    }
}
