package com.sankuai.meituan.waimai.customer.service.customer.monitor.dataConsistence;
import com.dianping.frog.sdk.alarm.DXUtil;
import com.dianping.frog.sdk.data.MafkaRawData;
import com.dianping.frog.sdk.data.RawData;
import com.dianping.frog.sdk.rpc.RpcService;
import com.dianping.frog.sdk.rpc.RpcServiceFactory;
import com.dianping.frog.sdk.runner.DefaultRuleRunner;
import com.dianping.frog.sdk.util.BcpLoggerUtils;
import com.google.common.collect.Lists;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.reflect.TypeToken;
import com.google.common.collect.Maps;
import org.apache.commons.lang3.StringUtils;
import com.dianping.lion.client.util.JsonUtils;

import java.util.Map;
public class WmKpAuditRuleRunner extends DefaultRuleRunner {
    private Gson gson = new GsonBuilder().create();

    @Override
    public boolean filter(RawData rawData, RawData... targetData) throws Exception {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.service.customer.monitor.dataConsistence.WmKpAuditRuleRunner.filter(com.dianping.frog.sdk.data.RawData,com.dianping.frog.sdk.data.RawData[])");
        MafkaRawData triggerMsgData = (MafkaRawData) rawData;
        Object body = triggerMsgData.getMafkaMessage().getBody();

        if (null == body || StringUtils.isBlank(body.toString())) {
            return false;
        }
        Map<String, Object> triggerMsg = toMap(body.toString());
        if (triggerMsg.isEmpty()) {
            return false;
        }
        Integer bizType = (Integer) triggerMsg.get("bizType");
        // kp审核事件
        if (bizType == null || (bizType.intValue() != 43 && bizType.intValue() != 45)) {
            return false;
        }
        // 参数校验
        Integer bizId = (Integer) triggerMsg.get("bizId");
        return bizId != null && bizId > 0;
    }

    @Override
    public String check(RawData rawData, RawData... rawData2) throws Exception {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.service.customer.monitor.dataConsistence.WmKpAuditRuleRunner.check(com.dianping.frog.sdk.data.RawData,com.dianping.frog.sdk.data.RawData[])");
        MafkaRawData triggerMsgData = (MafkaRawData) rawData;
        Object body = triggerMsgData.getMafkaMessage().getBody();

        if (body == null) {
            return null;
        }
        Map<String, Object> triggerMsg = toMap(body.toString());
        Map<String, Object> params = Maps.newHashMap();
        if (triggerMsg.isEmpty()) {
            return null;
        }
        Integer bizType = (Integer) triggerMsg.get("bizType");
        // kp审核事件
        if (bizType == null || (bizType.intValue() != 43 && bizType.intValue() != 45)) {
            return null;
        }
        params.put("bizType", bizType);
        // 参数校验
        Integer bizId = (Integer) triggerMsg.get("bizId");
        if (bizId == null || bizId <= 0) {
            return null;
        }
        params.put("bizId", bizId);

        Integer state = (Integer) triggerMsg.get("auditResult");
        // 审核驳回、审核通过
        if (state == null || (state!=0 && state!=1)) {
            return null;
        }
        int auditStatus = state!=0 ? 2 : 3;
        params.put("auditStatus", auditStatus);

        RpcService rpcService = RpcServiceFactory.getThriftInstance("com.sankuai.meituan.waimai.thrift.customer.service.WmCustomerKpThriftService",
                "com.sankuai.waimai.e.customer", 1000, null, "8430");
        String result = rpcService.invoke("monitorKpAudit",
                Lists.newArrayList("com.sankuai.meituan.waimai.thrift.customer.domain.customer.monitor.MonitorCustomerKpDTO"),
                Lists.newArrayList(JsonUtils.toJson(params)));

        if (!StringUtils.isBlank(result) && !"\"\"".equals(result)) {
            return result;
        }
        return null;
    }

    private Map<String, Object> toMap(String json) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.service.customer.monitor.dataConsistence.WmKpAuditRuleRunner.toMap(java.lang.String)");
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.service.customer.monitor.dataConsistence.WmKpAuditRuleRunner.toMap(java.lang.String)");
        TypeToken<?> parameterized = TypeToken.getParameterized(Map.class, String.class, Object.class);
        return gson.fromJson(json, parameterized.getType());
    }

    @Override
    public void alarm(String s, RawData rawData, RawData... rawData2) throws Exception {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.service.customer.monitor.dataConsistence.WmKpAuditRuleRunner.alarm(java.lang.String,com.dianping.frog.sdk.data.RawData,com.dianping.frog.sdk.data.RawData[])");
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.service.customer.monitor.dataConsistence.WmKpAuditRuleRunner.alarm(java.lang.String,com.dianping.frog.sdk.data.RawData,com.dianping.frog.sdk.data.RawData[])");
        //通过大象公众号发送告警消息, 收件人为告警配置中的告警接收人。
        DXUtil.sendAlarm(s);
    }
}
