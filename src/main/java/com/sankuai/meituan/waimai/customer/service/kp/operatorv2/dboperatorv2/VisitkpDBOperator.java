package com.sankuai.meituan.waimai.customer.service.kp.operatorv2.dboperatorv2;

import com.sankuai.meituan.waimai.customer.domain.WmCustomerDB;
import com.sankuai.meituan.waimai.thrift.customer.domain.kp.WmCustomerKp;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import com.sankuai.meituan.waimai.thrift.exception.WmServerException;
import org.apache.thrift.TException;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Author: 窦旭蒙
 * MIS: douxumeng
 * Date: 2020-07-06 17:17
 * Email: <EMAIL>
 * Desc:
 */
@Service
public class VisitkpDBOperator extends KpDBOperator {

    @Override
    public Object insert(WmCustomerDB wmCustomer, List<WmCustomerKp> oldCustomerKpList, List<WmCustomerKp> insertKpList, int uid, String uname) throws WmCustomerException, TException, WmServerException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_1", "com.sankuai.meituan.waimai.customer.service.kp.operatorv2.dboperatorv2.VisitkpDBOperator.insert(WmCustomerDB,List,List,int,String)");
        WmCustomerKp insertKp = getOperateKp(insertKpList);
        if (null != insertKp) {
            insertKp(insertKp, uid, uname);
        }
        return null;
    }

    @Override
    public Object update(WmCustomerDB wmCustomer, List<WmCustomerKp> oldCustomerKpList, List<WmCustomerKp> updateKpList, int uid, String uname) throws WmCustomerException, TException, WmServerException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_1", "com.sankuai.meituan.waimai.customer.service.kp.operatorv2.dboperatorv2.VisitkpDBOperator.update(WmCustomerDB,List,List,int,String)");
        WmCustomerKp updateKp = getOperateKp(updateKpList);
        if (null != updateKp) {
            updateKp(oldCustomerKpList, updateKp, uid, uname);
        }
        return null;
    }

    @Override
    public Object delete(WmCustomerDB wmCustomer, List<WmCustomerKp> oldCustomerKpList, List<WmCustomerKp> deleteKpList, int uid, String uname) throws WmCustomerException, TException, WmServerException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.kp.operatorv2.dboperatorv2.VisitkpDBOperator.delete(WmCustomerDB,List,List,int,String)");
        WmCustomerKp deleteKp = getOperateKp(deleteKpList);
        if (null != deleteKp) {
            deleteKp(wmCustomer, deleteKpList.get(0), uid, uname);
        }
        return null;
    }
}
