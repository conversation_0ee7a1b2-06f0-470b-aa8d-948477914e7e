package com.sankuai.meituan.waimai.customer.service.sc.auth;

import com.alibaba.fastjson.JSONObject;
import com.dianping.cat.Cat;
import com.sankuai.meituan.waimai.customer.adapter.AuthenticateServiceAdapter;
import com.sankuai.meituan.waimai.customer.config.MccConfig;
import com.sankuai.meituan.waimai.customer.constant.sc.WmScMetricConstant;
import com.sankuai.meituan.waimai.customer.service.sc.check.WmCanteenPoiCheckService;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.ScGrayEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.WmScCodeConstants;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.canteenstall.CanteenStallClueBindStatusEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.canteenstall.CanteenStallWmPoiBindStatusEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.sc.SchoolBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sc.canteenstall.WmCanteenStallBindDTO;
import com.sankuai.meituan.waimai.thrift.customer.domain.sc.canteenstall.WmCanteenStallManageDTO;
import com.sankuai.meituan.waimai.thrift.domain.WmPoiHighSeasPoiInfo;
import com.sankuai.meituan.waimai.thrift.exception.WmSchCantException;
import com.sankuai.waimai.crm.authenticate.client.constant.DSLTypeEnum;
import com.sankuai.waimai.crm.authenticate.client.service.request.AuthenticateAssertBatchOperationRequest;
import com.sankuai.waimai.crm.authenticate.client.service.request.AuthenticateAssertRequest;
import com.sankuai.waimai.crm.authenticate.client.service.request.AuthenticateQueryRequest;
import com.sankuai.waimai.crm.authenticate.client.service.response.AssertResult;
import com.sankuai.waimai.crm.authenticate.client.service.response.AuthenticateAssertBatchOperationResponse;
import com.sankuai.waimai.crm.authenticate.client.service.response.AuthenticateAssertResponse;
import com.sankuai.waimai.crm.authenticate.client.service.response.AuthenticateQueryResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

import static com.sankuai.meituan.waimai.thrift.customer.constant.sc.WmScCodeConstants.BIZ_AUTH_ERROR;

/**
 * 食堂档口管理权限服务相关
 * <AUTHOR>
 * @date 2024/06/04
 * @email <EMAIL>
 */
@Slf4j
@Service
public class WmCanteenStallAuthService {

    @Autowired
    private AuthenticateServiceAdapter authenticateServiceAdapter;
    @Autowired
    private WmCanteenPoiCheckService wmCanteenPoiCheckService;

    /**
     * 外卖租户ID
     */
    private static final Integer WM_TENANT_ID = 1000008;
    /**
     * 食堂档口业务对象CODE
     */
    private static final String CANTEEN_STALL_OBJECT_CODE = "waimai_canteen_stall_manage";
    /**
     * 档口管理列表查询操作CODE
     */
    private static final String MANAGE_LIST_QUERY_OPERATION_CODE = "manage_list_query";
    /**
     * 档口绑定列表-解绑按钮
     */
    private static final String STALL_BIND_LIST_UNBIND_BUTTON = "stall_bind_list_unbind_button";
    /**
     * 档口绑定列表-去上单按钮
     */
    private static final String STALL_BIND_LIST_PLACE_ORDER_BUTTON = "stall_bind_list_place_order_button";
    /**
     * 档口绑定列表-标记正常按钮
     */
    private static final String STALL_BIND_LIST_NORMAL_BUTTON = "stall_bind_list_normal_button";
    /**
     * 档口绑定列表-标记异常按钮
     */
    private static final String STALL_BIND_LIST_ABNORMAL_BUTTON = "stall_bind_list_abnormal_button";
    /**
     * 档口绑定列表-跟进状态审批中按钮
     */
    private static final String STALL_BIND_LIST_AUDITING_BUTTON = "stall_bind_list_auditing_button";
    /**
     * 档口绑定列表-操作记录按钮
     */
    private static final String STALL_BIND_LIST_LOG_BUTTON = "stall_bind_list_log_button";
    /**
     * 档口管理列表-查看详情按钮
     */
    private static final String MANAGE_LIST_VIEW_DETAIL_BUTTON = "manage_list_view_detail_button";
    /**
     * 档口管理列表-去扎点按钮
     */
    private static final String MANAGE_LIST_EDIT_COORDINATE_BUTTON = "manage_list_edit_coordinate_button";

    private static final String STALL_BIND_LIST_CHANGEBIND_BUTTON = "stall_bind_list_changebind_button";

    private static final String STALL_BIND_LIST_BATCH_CHANGEBIND_BUTTON = "stall_bind_list_batch_changebind_button";

    private static final String STALL_BIND_LIST_BATCH_UNBIND_BUTTON = "stall_bind_list_batch_unbind_button";

    /**
     * 获取用户对食堂档口管理某一操作的鉴权结果
     * @param uid 用户ID
     * @param operationCode 操作CODE
     * @param manageId 档口管理ID
     * @return true: 有权限 / false: 无权限
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     * @throws TException org.apache.thrift.TException
     */
    public Boolean getOperationAuthAssertResult(Integer uid, String operationCode, Integer manageId) throws WmSchCantException, TException {
        log.info("[WmCanteenStallAuthService.getOperationAuthAssertResult] input param: uid = {}, operationCode = {}, manageId = {}",
                uid, operationCode, manageId);
        if (uid == null || StringUtils.isBlank(operationCode) || manageId == null) {
            log.error("[WmCanteenStallAuthService.getOperationAuthAssertResult] input param invalid. uid = {}, operationCode = {}, manageId = {}",
                    uid, operationCode, manageId);
            return false;
        }

        AuthenticateAssertRequest assertRequest = new AuthenticateAssertRequest();
        // 业务对象ID列表
        List<String> manageIdList = new ArrayList<>();
        manageIdList.add(String.valueOf(manageId));
        assertRequest.setObjectIds(manageIdList);
        // 租户ID
        assertRequest.setTenantId(WM_TENANT_ID);
        assertRequest.setObjectCode(CANTEEN_STALL_OBJECT_CODE);
        assertRequest.setUid(uid);
        // 操作类型code
        assertRequest.setOperation(operationCode);

        AuthenticateAssertResponse assertResponse = authenticateServiceAdapter.getAuthAssertResult(assertRequest);
        log.info("[WmCanteenStallAuthService.getOperationAuthAssertResult] assertResponse = {}", JSONObject.toJSONString(assertResponse));

        if (assertResponse == null) {
            log.error("[WmCanteenStallAuthService.getOperationAuthAssertResult] auth assert failed. manageId = {}, uid = {}", manageId, uid);
            throw new WmSchCantException(BIZ_AUTH_ERROR, "权限获取异常, 请稍后刷新后再试");
        }
        return assertResponse.getResult(String.valueOf(manageId));
    }


    /**
     * 查询用户对单个档口管理多个操作的鉴权结果
     * @param operationList 操作code列表
     * @param manageId 档口管理ID
     * @param uid 用户ID
     * @return Map<String, Boolean>
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     * @throws TException org.apache.thrift.TException
     */
    public Map<String, Boolean> batchGetOperationAuthAssertResult(Set<String> operationList, Integer manageId, Integer uid)
            throws WmSchCantException, TException {
        log.info("[WmCanteenStallAuthService.batchGetOperationAuthAssertResult] input param: operationList = {}, manageId = {}, uid = {}",
                JSONObject.toJSONString(operationList), manageId, uid);
        if (CollectionUtils.isEmpty(operationList)) {
            log.warn("[WmCanteenStallAuthService.batchGetOperationAuthAssertResult] operationList is empty, return.");
            return new HashMap<>();
        }

        AuthenticateAssertBatchOperationRequest assertRequest = new AuthenticateAssertBatchOperationRequest();
        assertRequest.setObjectCode(CANTEEN_STALL_OBJECT_CODE);
        assertRequest.setUid(uid);
        assertRequest.setTenantId(WM_TENANT_ID);
        // 操作类型code列表
        assertRequest.setOperations(operationList);
        // 业务对象ID列表
        Set<String> manageIdList = new HashSet<>();
        manageIdList.add(manageId.toString());
        assertRequest.setObjectIds(manageIdList);
        AuthenticateAssertBatchOperationResponse assertResponse = authenticateServiceAdapter.getAuthAssertResultByBatch(assertRequest);

        if (assertResponse == null
                || assertResponse.getCode() != 0
                || CollectionUtils.isEmpty(assertResponse.getAssertResults())) {
            log.error("[WmCanteenStallAuthService.batchGetOperationAuthAssertResult] error. assertRequest = {}, assertResponse = {}",
                    JSONObject.toJSONString(assertRequest), JSONObject.toJSONString(assertResponse));
            throw new WmSchCantException(WmScCodeConstants.SERVER_ERROR, "权限获取异常, 请稍后刷新后再试");
        }

        List<AssertResult> assertResultList = assertResponse.getAssertResults();
        return assertResultList.stream().collect(Collectors.toMap(AssertResult::getOperation, AssertResult::getResult));
    }


    /**
     * 根据用户UID获取"食堂档口管理列表查询"操作的DSL查询语句
     * @param uid 用户ID
     * @return DSL查询语句
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    public String getManageListQueryDSL(Integer uid) throws WmSchCantException {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.sc.auth.WmCanteenStallAuthService.getManageListQueryDSL(java.lang.Integer)");
        log.info("[WmCanteenStallAuthService.getManageListQueryDSL] input param: uid = {}", uid);
        if (uid == null || uid <= 0) {
            log.warn("[WmCanteenStallAuthService.getManageListQueryDSL] uid is null. uid = {}", uid);
            return null;
        }

        AuthenticateQueryRequest queryRequest = new AuthenticateQueryRequest();
        queryRequest.setDslType(DSLTypeEnum.MY_SQL_DSL.code());
        queryRequest.setTenantId(WM_TENANT_ID);
        queryRequest.setObjectCode(CANTEEN_STALL_OBJECT_CODE);
        queryRequest.setOperation(MANAGE_LIST_QUERY_OPERATION_CODE);
        queryRequest.setUid(uid);

        AuthenticateQueryResponse authenticateQueryResponse = authenticateServiceAdapter.getAuthQueryResult(queryRequest);
        if (authenticateQueryResponse == null
                || authenticateQueryResponse.getCode() != 0
                || StringUtils.isBlank(authenticateQueryResponse.getDsl())) {
            log.error("[WmCanteenStallAuthService.getManageListQueryDSL] query error. uid = {}, authenticateQueryResponse = {}",
                    uid, JSONObject.toJSONString(authenticateQueryResponse));
            throw new WmSchCantException(WmScCodeConstants.SERVER_ERROR, "权限获取异常, 请稍后刷新后再试");
        }
        return authenticateQueryResponse.getDsl();
    }

    /**
     * 档口绑定列表操作鉴权查询
     * @param manageId 档口管理任务ID
     * @param uid 用户ID
     * @return 鉴权结果列表
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    public List<AssertResult> getBindListOperationAssertResult(Integer manageId, Integer uid) throws WmSchCantException {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.service.sc.auth.WmCanteenStallAuthService.getBindListOperationAssertResult(java.lang.Integer,java.lang.Integer)");
        if (manageId == null || manageId <= 0 || uid == null || uid <= 0) {
            return new ArrayList<>();
        }
        log.info("[WmCanteenStallAuthService.getBindListOperationAssertResult] manageId = {}, uid = {}", manageId, uid);

        AuthenticateAssertBatchOperationRequest assertRequest = new AuthenticateAssertBatchOperationRequest();
        assertRequest.setUid(uid);
        assertRequest.setTenantId(WM_TENANT_ID);
        assertRequest.setObjectCode(CANTEEN_STALL_OBJECT_CODE);
        // 操作类型code列表
        Set<String> operationList = new HashSet<>();
        operationList.add(STALL_BIND_LIST_PLACE_ORDER_BUTTON);
        operationList.add(STALL_BIND_LIST_NORMAL_BUTTON);
        operationList.add(STALL_BIND_LIST_ABNORMAL_BUTTON);
        operationList.add(STALL_BIND_LIST_AUDITING_BUTTON);
        operationList.add(STALL_BIND_LIST_LOG_BUTTON);
        operationList.add(STALL_BIND_LIST_UNBIND_BUTTON);
        operationList.add(STALL_BIND_LIST_CHANGEBIND_BUTTON);
        operationList.add(STALL_BIND_LIST_BATCH_CHANGEBIND_BUTTON);
        operationList.add(STALL_BIND_LIST_BATCH_UNBIND_BUTTON);
        assertRequest.setOperations(operationList);
        // 业务对象ID列表
        Set<String> manageIdSet = new HashSet<>();
        manageIdSet.add(String.valueOf(manageId));
        assertRequest.setObjectIds(manageIdSet);

        AuthenticateAssertBatchOperationResponse assertResponse = authenticateServiceAdapter.getAuthAssertResultByBatch(assertRequest);
        if (assertResponse == null || assertResponse.getCode() != 0) {
            log.error("[WmCanteenStallAuthService.getManageListOperationAssertResult] error. manageId = {}, uid = {}, assertResponse = {}",
                    manageId, uid, JSONObject.toJSONString(assertResponse));
            throw new WmSchCantException(WmScCodeConstants.SERVER_ERROR, "权限获取异常, 请稍后刷新后再试");
        }
        log.info("[WmCanteenStallAuthService.getBindListOperationAssertResult] result = {}", JSONObject.toJSONString(assertResponse.getAssertResults()));
        return assertResponse.getAssertResults();
    }


    public void setCanteenStallBindDTOAssertResult(WmCanteenStallBindDTO bindDTO, AssertResult assertResult) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.sc.auth.WmCanteenStallAuthService.setCanteenStallBindDTOAssertResult(WmCanteenStallBindDTO,AssertResult)");
        switch (assertResult.getOperation()) {
            case STALL_BIND_LIST_UNBIND_BUTTON:
                bindDTO.setUnbindButtonAuth(assertResult.getResult() ? ScGrayEnum.CAN_EDIT.getCode() : ScGrayEnum.NOT_DISPLAY.getCode());
                break;
            case STALL_BIND_LIST_PLACE_ORDER_BUTTON:
                bindDTO.setPlaceOrderButtonAuth(assertResult.getResult() ? ScGrayEnum.CAN_EDIT.getCode() : ScGrayEnum.NOT_DISPLAY.getCode());
                break;
            case STALL_BIND_LIST_NORMAL_BUTTON:
                bindDTO.setNormalButtonAuth(assertResult.getResult() ? ScGrayEnum.CAN_EDIT.getCode() : ScGrayEnum.NOT_DISPLAY.getCode());
                break;
            case STALL_BIND_LIST_ABNORMAL_BUTTON:
                bindDTO.setAbnormalButtonAuth(assertResult.getResult() ? ScGrayEnum.CAN_EDIT.getCode() : ScGrayEnum.NOT_DISPLAY.getCode());
                break;
            case STALL_BIND_LIST_AUDITING_BUTTON:
                bindDTO.setAuditingButtonAuth(assertResult.getResult() ? ScGrayEnum.CAN_EDIT.getCode() : ScGrayEnum.NOT_DISPLAY.getCode());
                break;
            case STALL_BIND_LIST_LOG_BUTTON:
                bindDTO.setLogButtonAuth(assertResult.getResult() ? ScGrayEnum.CAN_EDIT.getCode() : ScGrayEnum.NOT_DISPLAY.getCode());
                break;
            case STALL_BIND_LIST_CHANGEBIND_BUTTON:
                bindDTO.setChangebindButtonAuth(assertResult.getResult() ? ScGrayEnum.CAN_EDIT.getCode() : ScGrayEnum.NOT_DISPLAY.getCode());
                break;
            case STALL_BIND_LIST_BATCH_UNBIND_BUTTON:
                bindDTO.setBatchUnbindButtonAuth(assertResult.getResult() ? ScGrayEnum.CAN_EDIT.getCode() : ScGrayEnum.NOT_DISPLAY.getCode());
                break;
            case STALL_BIND_LIST_BATCH_CHANGEBIND_BUTTON:
                bindDTO.setBatchChangebindButtonAuth(assertResult.getResult() ? ScGrayEnum.CAN_EDIT.getCode() : ScGrayEnum.NOT_DISPLAY.getCode());
                break;
            default:
                break;
        }
    }

    /**
     * 设置"去上单"按钮权限
     * @param bindDTOList bindDTOList
     * @param userId 用户ID
     * @param wdcClueMap 线索信息Map
     */
    public void resetBindDTOPlaceOrderAuthByWdcClueInfo(List<WmCanteenStallBindDTO> bindDTOList, Integer userId, Map<Long, WmPoiHighSeasPoiInfo> wdcClueMap) {
        // 该档口绑定任务关联的线索（WDC ID)的认领上单状态为“已认领”且认领人为当前页面登录人时 才展示
        for (WmCanteenStallBindDTO bindDTO : bindDTOList) {
            WmPoiHighSeasPoiInfo clueInfo = wdcClueMap.get(bindDTO.getWdcClueId());
            if (clueInfo == null || clueInfo.getClaimStatus() != 1 || clueInfo.getClaimUid() != userId) {
                bindDTO.setPlaceOrderButtonAuth(ScGrayEnum.NOT_DISPLAY.getCode());
            }
        }
    }

    /**
     * 设置档口管理列表按钮权限
     * @param manageDTOList manageDTOList
     * @param uid 用户ID
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    public void setManageDTOListButtonAuth(List<WmCanteenStallManageDTO> manageDTOList, Integer uid) throws WmSchCantException {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.meituan.waimai.customer.service.sc.auth.WmCanteenStallAuthService.setManageDTOListButtonAuth(java.util.List,java.lang.Integer)");
        if (CollectionUtils.isEmpty(manageDTOList)) {
            return;
        }
        // 1-查询列表鉴权结果
        List<AssertResult> assertResultList = getManageListOperationAssertResult(manageDTOList, uid);
        if (CollectionUtils.isEmpty(assertResultList)) {
            log.error("[WmCanteenStallAuthService.setManageDTOListButtonAuth] assertResultList is empty, return. manageDTOList = {}, uid = {}",
                    JSONObject.toJSONString(manageDTOList), uid);
            return;
        }

        // 2-权限组装
        Map<Integer, WmCanteenStallManageDTO> manageDTOMap = manageDTOList.stream()
                .collect(Collectors.toConcurrentMap(WmCanteenStallManageDTO::getId, manageDTO -> manageDTO));
        for (AssertResult assertResult : assertResultList) {
            if (StringUtils.isBlank(assertResult.getObjectId()) || manageDTOMap.get(Integer.valueOf(assertResult.getObjectId())) == null) {
                log.error("[WmCanteenStallAuthService.setManageDTOListButtonAuth] error. assertResult = {}", JSONObject.toJSONString(assertResult));
                continue;
            }

            WmCanteenStallManageDTO manageDTO = manageDTOMap.get(Integer.valueOf(assertResult.getObjectId()));
            setManageListAssertResult(assertResult, manageDTO);
        }
    }


    public void setManageListAssertResult(AssertResult assertResult, WmCanteenStallManageDTO manageDTO) {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.service.sc.auth.WmCanteenStallAuthService.setManageListAssertResult(AssertResult,WmCanteenStallManageDTO)");
        switch (assertResult.getOperation()) {
            // 查看详情按钮权限
            case MANAGE_LIST_VIEW_DETAIL_BUTTON:
                manageDTO.setViewDetailButtonAuth(assertResult.getResult() ? ScGrayEnum.CAN_EDIT.getCode() : ScGrayEnum.NOT_DISPLAY.getCode());
                break;
            // 去扎点按钮权限
            case MANAGE_LIST_EDIT_COORDINATE_BUTTON:
                manageDTO.setEditCoordinateButtonAuth(assertResult.getResult() ? ScGrayEnum.CAN_EDIT.getCode() : ScGrayEnum.NOT_DISPLAY.getCode());
                break;
            default:
                break;
        }
    }

    /**
     * 校验解绑权限
     */
    public Map<Integer, CheckResult> checkStallUnbindAuth(Integer manageId, Integer uid, List<WmCanteenStallBindDTO> bindDTOList) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.service.sc.auth.WmCanteenStallAuthService.checkStallUnbindAuth(java.lang.Integer,java.lang.Integer,java.util.List)");
        //统一走鉴权
        HashMap<Integer, CheckResult> resultMap = new HashMap<>();
        Boolean operationAuth = getOperationAuth(manageId, uid, STALL_BIND_LIST_CHANGEBIND_BUTTON);
        List<Long> wmPoiIds = bindDTOList.stream().map(WmCanteenStallBindDTO::getWmPoiId).collect(Collectors.toList());
        Map<Long, Integer> remainingTransferCountMap = wmCanteenPoiCheckService.getRemainingTransferCount(wmPoiIds);

        for (WmCanteenStallBindDTO bindDTO : bindDTOList) {
            CheckResult checkResult = new CheckResult();
            try {
                if (!operationAuth) {
                    checkResult.setScGrayEnum(ScGrayEnum.NOT_DISPLAY);
                    checkResult.setReason("操作人无解绑按钮权限");
                    resultMap.put(bindDTO.getId(), checkResult);
                    continue;
                }
                //解绑门店已在绑定/解绑审批流程中，置灰
                if (Objects.equals(bindDTO.getWmPoiBindStatus().byteValue(), CanteenStallWmPoiBindStatusEnum.UNBINDING.getType())
                        || Objects.equals(bindDTO.getWmPoiBindStatus().byteValue(), CanteenStallWmPoiBindStatusEnum.REBINDING.getType())
                ) {
                    checkResult.setScGrayEnum(ScGrayEnum.READ_ONLY);
                    checkResult.setReason("该门店已在解绑/换绑审批流程中，不支持再次操作");
                    resultMap.put(bindDTO.getId(), checkResult);
                    continue;
                }
                if (!bindDTO.getClueBindStatus().equals((int) CanteenStallClueBindStatusEnum.BIND_SUCCESS.getType())
                        && !bindDTO.getClueBindStatus().equals((int) CanteenStallClueBindStatusEnum.BINDING.getType())
                        && !bindDTO.getWmPoiBindStatus().equals((int) CanteenStallWmPoiBindStatusEnum.BIND_SUCCESS.getType())
                ) {
                    checkResult.setScGrayEnum(ScGrayEnum.NOT_DISPLAY);
                    checkResult.setReason("当前档口绑定任务未绑定");
                    resultMap.put(bindDTO.getId(), checkResult);
                    continue;
                }
                //解绑门店【解换绑操作次数】>=3，置灰
                if (remainingTransferCountMap.get(bindDTO.getWmPoiId()) >= MccConfig.getMaxUnbindRebind0perations()) {
                    checkResult.setScGrayEnum(ScGrayEnum.READ_ONLY);
                    checkResult.setReason("该门店解换绑操作次数已达最大次数，不支持再次操作");
                    resultMap.put(bindDTO.getId(), checkResult);
                    continue;
                }
                checkResult.setScGrayEnum(ScGrayEnum.CAN_EDIT);
                resultMap.put(bindDTO.getId(), checkResult);

            } catch (Exception e) {
                log.error("bindId:{}， 校验解绑权限异常", bindDTO.getId(), e);
                checkResult.setScGrayEnum(ScGrayEnum.NOT_DISPLAY);
                checkResult.setReason("系统权限判断异常");
                resultMap.put(bindDTO.getId(), checkResult);
            }

        }
        return resultMap;
    }

    /**
     * 校验换绑权限
     */
    public Map<Integer, CheckResult> checkStallChangebindAuth(Integer manageId, Integer uid, List<WmCanteenStallBindDTO> bindDTOList) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.meituan.waimai.customer.service.sc.auth.WmCanteenStallAuthService.checkStallChangebindAuth(java.lang.Integer,java.lang.Integer,java.util.List)");
        //统一走鉴权
        HashMap<Integer, CheckResult> resultMap = new HashMap<>();
        Boolean operationAuth = getOperationAuth(manageId, uid, STALL_BIND_LIST_CHANGEBIND_BUTTON);
        List<Long> wmPoiIds = bindDTOList.stream().map(WmCanteenStallBindDTO::getWmPoiId).collect(Collectors.toList());
        Map<Long, Integer> remainingTransferCountMap = wmCanteenPoiCheckService.getRemainingTransferCount(wmPoiIds);

        for (WmCanteenStallBindDTO bindDTO : bindDTOList) {
            CheckResult checkResult = new CheckResult();
            try {
                if (!MccConfig.getIsNewFLowSwitch()) {
                    checkResult.setScGrayEnum(ScGrayEnum.NOT_DISPLAY);
                    checkResult.setReason("降级隐藏换绑按钮");
                    resultMap.put(bindDTO.getId(), checkResult);
                    continue;
                }
                if (!operationAuth) {
                    checkResult.setScGrayEnum(ScGrayEnum.NOT_DISPLAY);
                    checkResult.setReason("操作人无换绑按钮权限");
                    resultMap.put(bindDTO.getId(), checkResult);
                    continue;
                }
                //解绑门店已在绑定/解绑审批流程中，置灰
                if (Objects.equals(bindDTO.getWmPoiBindStatus().byteValue(), CanteenStallWmPoiBindStatusEnum.UNBINDING.getType())
                        || Objects.equals(bindDTO.getWmPoiBindStatus().byteValue(), CanteenStallWmPoiBindStatusEnum.REBINDING.getType())
                ) {
                    checkResult.setScGrayEnum(ScGrayEnum.READ_ONLY);
                    checkResult.setReason("该门店已在解绑/换绑审批流程中，不支持再次操作");
                    resultMap.put(bindDTO.getId(), checkResult);
                    continue;
                }
                //未绑定时，不展示按钮
                if (!bindDTO.getWmPoiBindStatus().equals((int) CanteenStallWmPoiBindStatusEnum.BIND_SUCCESS.getType())) {
                    checkResult.setScGrayEnum(ScGrayEnum.NOT_DISPLAY);
                    checkResult.setReason("当前档口绑定任务未绑定");
                    resultMap.put(bindDTO.getId(), checkResult);
                    continue;
                }
                //解绑门店【解换绑操作次数】>=3，置灰
                if (remainingTransferCountMap.get(bindDTO.getWmPoiId()) >= MccConfig.getMaxUnbindRebind0perations()) {
                    checkResult.setScGrayEnum(ScGrayEnum.READ_ONLY);
                    checkResult.setReason("该门店解换绑操作次数已达最大次数，不支持再次操作");
                    resultMap.put(bindDTO.getId(), checkResult);
                    continue;
                }
                checkResult.setScGrayEnum(ScGrayEnum.CAN_EDIT);
                resultMap.put(bindDTO.getId(), checkResult);

            } catch (Exception e) {
                log.error("bindId:{}， 校验换绑权限异常", bindDTO.getId(), e);
                checkResult.setScGrayEnum(ScGrayEnum.NOT_DISPLAY);
                checkResult.setReason("系统权限判断异常");
                resultMap.put(bindDTO.getId(), checkResult);
            }

        }
        return resultMap;
    }

    private Boolean getOperationAuth (Integer manageId, Integer uid, String operation) {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.service.sc.auth.WmCanteenStallAuthService.getOperationAuth(java.lang.Integer,java.lang.Integer,java.lang.String)");
        try {
            AuthenticateAssertBatchOperationRequest assertRequest = new AuthenticateAssertBatchOperationRequest();
            assertRequest.setObjectCode(CANTEEN_STALL_OBJECT_CODE);
            assertRequest.setUid(uid);
            assertRequest.setTenantId(WM_TENANT_ID);
            // 操作类型code列表
            assertRequest.setOperations(Collections.singleton(operation));
            // 业务对象ID列表
            Set<String> manageIdList = new HashSet<>();
            manageIdList.add(String.valueOf(manageId));
            assertRequest.setObjectIds(manageIdList);
            AuthenticateAssertBatchOperationResponse assertResponse = authenticateServiceAdapter.getAuthAssertResultByBatch(assertRequest);

            if (assertResponse == null
                    || assertResponse.getCode() != 0
                    || CollectionUtils.isEmpty(assertResponse.getAssertResults())) {
                log.error("[WmCanteenStallAuthService.batchGetOperationAuthAssertResult] error. assertRequest = {}, assertResponse = {}",
                        JSONObject.toJSONString(assertRequest), JSONObject.toJSONString(assertResponse));
                throw new WmSchCantException(WmScCodeConstants.SERVER_ERROR, "权限获取异常, 请稍后刷新后再试");
            }
            return assertResponse.getAssertResults().get(0).getResult();
        } catch (Exception e) {
            log.error("manegeId:{}, uid:{}, 执行鉴权{}异常", manageId, uid, operation, e);
            return false;
        }

    }

    /**
     * 档口管理列表操作鉴权查询
     * @param manageDTOList manageDTOList
     * @param uid 用户ID
     * @return 鉴权结果列表
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    public List<AssertResult> getManageListOperationAssertResult(List<WmCanteenStallManageDTO> manageDTOList, Integer uid) throws WmSchCantException {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.meituan.waimai.customer.service.sc.auth.WmCanteenStallAuthService.getManageListOperationAssertResult(java.util.List,java.lang.Integer)");
        if (CollectionUtils.isEmpty(manageDTOList) || uid == null || uid <= 0) {
            return new ArrayList<>();
        }

        AuthenticateAssertBatchOperationRequest assertRequest = new AuthenticateAssertBatchOperationRequest();
        assertRequest.setTenantId(WM_TENANT_ID);
        assertRequest.setObjectCode(CANTEEN_STALL_OBJECT_CODE);
        assertRequest.setUid(uid);
        // 操作类型code列表
        Set<String> operationList = new HashSet<>();
        operationList.add(MANAGE_LIST_VIEW_DETAIL_BUTTON);
        operationList.add(MANAGE_LIST_EDIT_COORDINATE_BUTTON);

        assertRequest.setOperations(operationList);
        // 业务对象ID列表
        Set<String> manageIdList = manageDTOList.stream()
                .map(x -> String.valueOf(x.getId()))
                .collect(Collectors.toSet());
        assertRequest.setObjectIds(manageIdList);

        AuthenticateAssertBatchOperationResponse assertResponse = authenticateServiceAdapter.getAuthAssertResultByBatch(assertRequest);
        if (assertResponse == null || assertResponse.getCode() != 0) {
            log.error("[WmCanteenStallAuthService.getManageListOperationAssertResult] error. schoolBoList = {}, uid = {}, assertResponse = {}",
                    JSONObject.toJSONString(manageDTOList), uid, JSONObject.toJSONString(assertResponse));
            throw new WmSchCantException(WmScCodeConstants.SERVER_ERROR, "权限获取异常, 请稍后刷新后再试");
        }
        return assertResponse.getAssertResults();
    }
}
