package com.sankuai.meituan.waimai.customer.service.common;

import org.springframework.stereotype.Service;

@Service
public class SettleProduceNotifyService extends ProduceNotifyService {

  private static final int SETTLE_BASE_CODE = 70;

  @Override
  public int getBaseCode() {
      com.dianping.cat.Cat.logEvent("INVALID_METHOD_1", "com.sankuai.meituan.waimai.customer.service.common.SettleProduceNotifyService.getBaseCode()");
      return SETTLE_BASE_CODE;
  }
}