package com.sankuai.meituan.waimai.customer.service.customer;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.dianping.cat.Cat;
import com.dianping.cat.util.MetricHelper;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.meituan.mtrace.thread.TraceRunnable;
import com.meituan.mtrace.thread.pool.TraceExecutors;
import com.meituan.service.mobile.mtthrift.util.ClientInfoUtil;
import com.sankuai.meituan.scmbrand.thrift.constant.OwnerTypeEnum;
import com.sankuai.meituan.scmbrand.thrift.domain.servicebrand.WmServiceBrandAggreDTO;
import com.sankuai.meituan.scmbrand.thrift.domain.servicebrand.WmServiceBrandAggreSearchConditionDTO;
import com.sankuai.meituan.waimai.customer.adapter.*;
import com.sankuai.meituan.waimai.customer.adapter.dto.QueryRepeatQuaWmPoiIdDTO;
import com.sankuai.meituan.waimai.customer.config.MccConfig;
import com.sankuai.meituan.waimai.customer.config.MccCustomerConfig;
import com.sankuai.meituan.waimai.customer.constant.WmCustomerConstant;
import com.sankuai.meituan.waimai.customer.constant.customer.*;
import com.sankuai.meituan.waimai.customer.contract.domain.WmTempletContractSignDB;
import com.sankuai.meituan.waimai.customer.contract.service.impl.WmCustomerOplogService;
import com.sankuai.meituan.waimai.customer.dao.WmCustomerDBMapper;
import com.sankuai.meituan.waimai.customer.dao.WmCustomerKpDBMapper;
import com.sankuai.meituan.waimai.customer.ddd.adapter.WmPoiQueryAdapter;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerCleanQuery;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerDB;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerNumberRepeatVo;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerQueryVo;
import com.sankuai.meituan.waimai.customer.domain.customer.WmCustomerOwnerApply;
import com.sankuai.meituan.waimai.customer.service.common.WmEmployeeService;
import com.sankuai.meituan.waimai.customer.service.customer.Impl.IWmCustomerRealService;
import com.sankuai.meituan.waimai.customer.service.customer.dto.CustomerMscUsedPoiDetailDTO;
import com.sankuai.meituan.waimai.customer.service.customer.dto.MscPoiInfoDTO;
import com.sankuai.meituan.waimai.customer.service.gray.WmCustomerGrayService;
import com.sankuai.meituan.waimai.customer.service.kp.WmCustomerKpService;
import com.sankuai.meituan.waimai.customer.util.base.AssertUtil;
import com.sankuai.meituan.waimai.customer.util.trans.WmCustomerTransUtil;
import com.sankuai.meituan.waimai.infra.domain.WmEmploy;
import com.sankuai.meituan.waimai.label.constants.LabelSubjectTypeEnum;
import com.sankuai.meituan.waimai.poi.constants.PoiOrgEnum;
import com.sankuai.meituan.waimai.poiaudit.thrift.constants.WmPoiValidEnum;
import com.sankuai.meituan.waimai.poibizflow.thrift.switcher.bo.SwitchTaskAndPoiDTO;
import com.sankuai.meituan.waimai.qualification.constants.QuaNumberUsedStatusEnum;
import com.sankuai.meituan.waimai.thrift.constants.WmPoiFieldQueryConstant;
import com.sankuai.meituan.waimai.thrift.customer.constant.*;
import com.sankuai.meituan.waimai.thrift.customer.constant.customer.CustomerBizOrgEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.customer.CustomerBusinessEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.WmCustomerOplogBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.*;
import com.sankuai.meituan.waimai.thrift.customer.domain.kp.WmCustomerKp;
import com.sankuai.meituan.waimai.thrift.customer.domain.mq.CustomerMQEventEnum;
import com.sankuai.meituan.waimai.thrift.domain.WmPoiAggre;
import com.sankuai.meituan.waimai.thrift.domain.WmPoiLabelRel;
import com.sankuai.meituan.waimai.thrift.domain.WmQuaRepeatRequest;
import com.sankuai.meituan.waimai.thrift.domain.WmQuaRepeatResponse;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import com.sankuai.meituan.waimai.thrift.exception.WmServerException;
import com.sankuai.nibcus.inf.customer.client.enums.BusinessLineEnum;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.thrift.TException;
import org.elasticsearch.action.search.SearchResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.sankuai.meituan.waimai.thrift.constants.WmPoiFieldQueryConstant.*;

/**
 * Desc: 单一职责原则：只提供客户DB先关能力
 * 区分客户平台WmCustomerPlatformDataParseService服务
 */
@Service
public class WmCustomerService {

    private static Logger logger = LoggerFactory.getLogger(WmCustomerService.class);

    @Autowired
    private IWmCustomerRealService wmLeafCustomerRealService;

    @Autowired
    private IWmCustomerRealService wmSuperCustomerRealService;

    @Autowired
    private WmCustomerCommonService customerCommonService;

    @Autowired
    private WmCustomerBrandService wmCustomerBrandService;

    @Autowired
    private WmCustomerKpService wmCustomerKpService;

    @Autowired
    private WmEmployeeService wmEmployeeService;

    @Autowired
    private WmCustomerDBMapper wmCustomerDBMapper;

    @Autowired
    private WmCustomerKpDBMapper wmCustomerKpDBMapper;

    @Autowired
    private WmCustomerPoiListEsService poiListEsService;

    @Autowired
    private WmCustomerGrayService wmCustomerGrayService;

    @Autowired
    private WmCustomerMSCLabelService wmCustomerMSCLabelService;

    @Autowired
    private WmPoiSwitchServiceAdapter wmPoiSwitchServiceAdapter;

    @Autowired
    private WmPoiClient wmPoiClient;

    @Autowired
    private WmCustomerPoiRelService wmCustomerPoiRelService;

    @Autowired
    private WmPoiQueryAdapter wmPoiQueryAdapter;

    @Autowired
    private WmQuaAdapter wmQuaAdapter;

    @Autowired
    private WmBrandAdapter wmBrandAdapter;

    @Autowired
    private WmEmployeeService employeeService;

    @Autowired
    private WmCustomerPlatformDataParseService wmCustomerPlatformDataParseService;
    @Autowired
    private WmCustomerOplogService wmCustomerOplogService;

    @Resource
    WmPoiFlowlineLabelThriftServiceAdapter wmPoiFlowlineLabelThriftServiceAdapter;


    private static ExecutorService EXECUTOR_SERVICE = TraceExecutors.getTraceExecutorService(
            new ThreadPoolExecutor(5, 10, 60L, TimeUnit.SECONDS, new ArrayBlockingQueue<Runnable>(100), Executors.defaultThreadFactory()));

    /**
     * 校验一级类型下客户编号唯一
     *
     * @param wmCustomerNumberRepeatVo
     * @return
     */
    public WmCustomerDB validateCustomerNumber(WmCustomerNumberRepeatVo wmCustomerNumberRepeatVo) throws WmCustomerException {
        return wmLeafCustomerRealService.validateCustomerNumber(wmCustomerNumberRepeatVo);
    }


    /**
     * 校验一级类型下客户编号唯一
     *
     * @param wmCustomerNumberRepeatVo
     * @return
     */
    public List<WmCustomerDB> validateCustomerNumberNew(WmCustomerNumberRepeatVo wmCustomerNumberRepeatVo) throws WmCustomerException {
        return wmLeafCustomerRealService.validateCustomerNumberNew(wmCustomerNumberRepeatVo);
    }

    /**
     * 获取具体的wmCustomerService处理类
     *
     * @param isLeaf
     * @return
     */
    private IWmCustomerRealService getWmCustomerRealServiceByIsLeaf(int isLeaf) {
        if (isLeaf == CustomerConstants.CUSTOMER_IS_LEAF_NO) {
            return wmSuperCustomerRealService;
        }
        return wmLeafCustomerRealService;
    }

    private IWmCustomerRealService getWmCustomerRealServiceByCustomerId(int customerId) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.meituan.waimai.customer.service.customer.WmCustomerService.getWmCustomerRealServiceByCustomerId(int)");
        try {
            WmCustomerBasicBo wmCustomerBasicBo = wmLeafCustomerRealService.getCustomerByIdOrMtCustomerId(customerId);
            if (wmCustomerBasicBo.getIsLeaf() == CustomerConstants.CUSTOMER_IS_LEAF_NO) {
                return wmSuperCustomerRealService;
            }
        } catch (TException | WmCustomerException e) {
            logger.error("根据Id获取客户信息异常");
        }

        return wmLeafCustomerRealService;
    }

    /**
     * 保存或修改客户
     *
     * @param wmCustomerBasicBo
     * @param force
     * @param opUid
     * @param opName
     * @throws WmCustomerException
     * @throws TException
     */
    public ValidateResultBo saveOrUpdateCustomer(WmCustomerBasicBo wmCustomerBasicBo, Boolean force, Integer opUid, String opName, int channel) throws WmCustomerException, TException {
        String appKey = ClientInfoUtil.getClientAppKey();
        logger.info("保存客户,参数wmCustomerBasicBo={},force={},opUid={},opName={},appKey={}",
                JSONObject.toJSONString(wmCustomerBasicBo), force, opUid, opName, appKey);
        String status = WmCustomerConstant.SUCCESS;
        String reason = "";
        try {
            // 计算业务线
            Integer bizOrgCode = CustomerRealTypeEnum.getBizOrgCodeByValue(wmCustomerBasicBo.getCustomerRealType());
            wmCustomerBasicBo.setBizOrgCode(bizOrgCode);
            IWmCustomerRealService wmCustomerRealService = getWmCustomerRealServiceByIsLeaf(wmCustomerBasicBo.getIsLeaf());
            //新增字段默认值
            wmCustomerBasicBo.setHqSecondCityId(ObjectUtils.defaultIfNull(wmCustomerBasicBo.getHqSecondCityId(), 0));
            wmCustomerBasicBo.setHqDetailAddress(ObjectUtils.defaultIfNull(wmCustomerBasicBo.getHqDetailAddress(), ""));
            wmCustomerBasicBo.setWdcClueId(ObjectUtils.defaultIfNull(wmCustomerBasicBo.getWdcClueId(), 0L));
            wmCustomerBasicBo.setContractNum(ObjectUtils.defaultIfNull(wmCustomerBasicBo.getContractNum(), ""));

            //场景信息非空&资质类型非个人证件，需要将场景信息置空以免外部系统传错导致脏数据
            if (wmCustomerBasicBo.getSceneInfoBO() != null
                    && wmCustomerBasicBo.getCustomerType() != CustomerType.CUSTOMER_TYPE_IDCARD.getCode()) {
                logger.info("saveOrUpdateCustomer,客户资质非个人证件但场景信息非空，重置场景信息,wmCustomerBasicBo={}", JSON.toJSONString(wmCustomerBasicBo));
                wmCustomerBasicBo.setSceneInfoBO(null);
            }
            ValidateResultBo validateResultBo = wmCustomerRealService.saveOrUpdateCustomer(wmCustomerBasicBo, force, opUid, opName, channel);
            //如果校验不通过，打不通过原因埋点
            if (validateResultBo.getCode() != CustomerConstants.RESULT_CODE_PASS) {
                status = String.valueOf(validateResultBo.getCode());
                reason = validateResultBo.getMsg();
            }
            return validateResultBo;
        } catch (WmCustomerException e) {
            status = WmCustomerConstant.BUSINESS_EXCEPTION;
            reason = e.getMsg();
            throw e;
        } catch (Exception e) {
            status = WmCustomerConstant.SYSTEM_EXCEPTION;
            reason = WmCustomerConstant.SYSTEM_EXCEPTION_MSG;
            throw e;
        } finally {
            CustomerSource customerSource = CustomerSource.of(channel);
            Cat.logEvent(CustomerMetricEnum.CUSTOMER_SAVE.getName(), customerSource.name(), status, reason);
            MetricHelper.build().name(CustomerMetricEnum.CUSTOMER_SAVE.getName()).tag(CustomerMetricEnum.CUSTOMER_SAVE.getTag(), customerSource.name())
                    .tag(CustomerMetricEnum.CUSTOMER_SAVE.getStatus(), status).count();
        }
    }

    /**
     * 获取客户详细信息，包括审核diff
     *
     * @param customerId
     * @return
     * @throws TException
     * @throws WmCustomerException
     */
    public WmCustomerDetailBo getCustomerWithAuditById(Integer customerId) throws TException, WmCustomerException {
        return wmLeafCustomerRealService.getCustomerWithAuditById(customerId);
    }

    /**
     * 获取客户标签信息
     *
     * @param customerId
     * @param userId
     * @return
     * @throws TException
     * @throws WmCustomerException
     */
    public List<WmCustomerLabelBo> getCustomerLabelInfo(int customerId, int userId) throws TException, WmCustomerException {
        return wmLeafCustomerRealService.getCustomerLabelInfo(customerId, userId);
    }

    /**
     * 获取客户标签信息，批量
     *
     * @param customerIdList
     * @param userId
     * @return
     * @throws TException
     * @throws WmCustomerException
     */
    public Map<Integer, List<WmCustomerLabelBo>> getCustomerLabelInfoBatch(List<Integer> customerIdList, int userId) throws TException, WmCustomerException {
        return wmLeafCustomerRealService.getCustomerLabelInfoBatch(customerIdList, userId);
    }

    /**
     * 解绑该客户的上级客户信息
     *
     * @param customerId
     * @return
     * @throws TException
     * @throws WmCustomerException
     */
    public boolean unBindSuperCustomer(Integer customerId, int opUid, String opName) throws TException, WmCustomerException {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.meituan.waimai.customer.service.customer.WmCustomerService.unBindSuperCustomer(java.lang.Integer,int,java.lang.String)");
        WmCustomerDB wmCustomerDB = selectCustomerById(customerId);
        if (wmCustomerDB == null) {
            logger.warn("unBindSuperCustomer,未查询到客户:{}", customerId);
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "解绑失败,客户不存在");
        }
        boolean res = wmLeafCustomerRealService.unBindSuperCustomer(customerId, opUid, opName);

        sendUnBindMessageAndInsertOpAsyn(customerId, (int) wmCustomerDB.getSuperCustomerId(), opUid, opName);
        return res;
    }

    /**
     * 异步发送解绑消息
     *
     * @param customerId
     * @param opUid
     * @param opName
     */
    private void sendUnBindMessageAndInsertOpAsyn(Integer customerId, Integer superCustomerId, int opUid, String opName) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.meituan.waimai.customer.service.customer.WmCustomerService.sendUnBindMessageAndInsertOpAsyn(java.lang.Integer,java.lang.Integer,int,java.lang.String)");
        EXECUTOR_SERVICE.execute(new TraceRunnable(() -> {
            logger.info("unBindSuperCustomer(), customerId={}, opUid:{},opUname:{}", customerId, opUid, opName);
            try {
                WmEmploy operator = wmEmployeeService.getWmEmployById(opUid);
                wmLeafCustomerRealService.sendBindOrUnBindMessageToOwnersAndInsertOpLog(operator, customerId, 0, superCustomerId);
            } catch (Exception e) {
                logger.error("给客户责任人发送解绑消息异常, wmCustomerId:{}", customerId);
            }
        }));
    }


    /**
     * 获取客户列表
     *
     * @param wmCustomerFormBo
     * @return
     */
    public WmCustomerPageDate getCustomerList(WmCustomerFormBo wmCustomerFormBo) throws WmCustomerException, TException {
        if (wmCustomerFormBo.getBusiness() == null){
            // 默认查到家数据，理论上所有查客户列表的入口都已经显示的传客户经营类型了
            wmCustomerFormBo.setBusiness(CustomerBusinessEnum.DAOJIA.getCode());
        }
        if (wmCustomerFormBo.getBusiness() == CustomerBusinessEnum.DAOJIA.getCode()){
            return wmLeafCustomerRealService.getCustomerList(wmCustomerFormBo);
        }
        return wmLeafCustomerRealService.getDcCustomerList(wmCustomerFormBo);
    }

    public boolean checkPoiQuaForCustomerAudit(int customerId, int opUid, String opName) throws WmCustomerException, TException {
        return wmLeafCustomerRealService.checkPoiQuaForCustomerAudit(customerId, opUid, opName);
    }

    /**
     * 审核回调处理
     *
     * @param wmCustomerAuditBo
     * @throws WmCustomerException
     */
    public void auditCustomerCallBack(WmCustomerAuditBo wmCustomerAuditBo) throws WmCustomerException {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.service.customer.WmCustomerService.auditCustomerCallBack(com.sankuai.meituan.waimai.thrift.customer.domain.customer.WmCustomerAuditBo)");
        wmLeafCustomerRealService.auditCustomerCallBack(wmCustomerAuditBo);
    }

    /**
     * 检查客户有效性
     *
     * @param id
     * @return
     * @throws WmCustomerException
     */
    public Boolean checkCustomerEffect(Integer id) throws WmCustomerException {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.customer.WmCustomerService.checkCustomerEffect(java.lang.Integer)");
        return wmLeafCustomerRealService.checkCustomerEffect(id);
    }


    /**
     * 删除客户信息
     *
     * @param customerId
     */
    public void deleteCustomer(Integer customerId, Integer opUid, String opName) throws WmCustomerException, TException {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.service.customer.WmCustomerService.deleteCustomer(java.lang.Integer,java.lang.Integer,java.lang.String)");
        //到餐客户不能删除
        WmCustomerDB wmCustomerDB = selectCustomerById(customerId);
        if (CustomerRealTypeEnum.DAOCAN.getValue() == wmCustomerDB.getCustomerRealType()){
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "到餐客户不可删除");
        }
        IWmCustomerRealService wmCustomerRealService = getWmCustomerRealServiceByCustomerId(customerId);
        wmCustomerRealService.deleteCustomer(customerId, opUid, opName);
    }


    /**
     * 根据客户ID查询客户信息
     *
     * @param id
     * @return
     */
    public WmCustomerDB selectCustomerById(Integer id) throws WmCustomerException {
        return wmLeafCustomerRealService.selectCustomerById(id);
    }

    /**
     * 根据客户ID查询客户信息（slave节点）
     *
     * @param id
     * @return
     */
    public WmCustomerDB selectPlatformCustomerByIdFromSlave(Integer id) throws WmCustomerException {
        return wmLeafCustomerRealService.selectPlatformCustomerByIdFromSlave(id);
    }

    public Integer selectCustomerById4Grey(Integer id) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.customer.WmCustomerService.selectCustomerById4Grey(java.lang.Integer)");
        return wmLeafCustomerRealService.selectCustomerById4Grey(id);
    }

    public WmCustomerAddressBo getCustomerAddressForAutoBringIn(int customerId, int wmPoiId) throws TException, WmCustomerException {
        return wmLeafCustomerRealService.getCustomerAddressForAutoBringIn(customerId, wmPoiId);
    }


    /**
     * 根据id批量查询客户信息
     *
     * @param customerIdSet
     * @return
     */
    public List<WmCustomerDB> selectCustomerByIds(Set<Integer> customerIdSet) throws WmCustomerException {
        return wmLeafCustomerRealService.selectCustomerByIds(customerIdSet);
    }

    /**
     * 根据客户ID查询客户信息(查主库)
     *
     * @param id
     * @return
     */
    public WmCustomerDB selectCustomerByIdRT(Integer id) throws WmCustomerException {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.service.customer.WmCustomerService.selectCustomerByIdRT(java.lang.Integer)");
        return wmLeafCustomerRealService.selectCustomerByIdRT(id);
    }


    /**
     * 根据客户ID查询生效客户数据
     *
     * @param id
     * @return
     */
    public WmCustomerDB selectEffectCustomerById(Integer id) throws WmCustomerException {
        return wmLeafCustomerRealService.selectEffectCustomerById(id);
    }

    /**
     * 根据门店ID查询客户信息
     *
     * @param wmPoiId
     * @return
     */
    public WmCustomerDB selectCustomerByWmPoiId(Long wmPoiId) throws WmCustomerException {
        return wmLeafCustomerRealService.selectCustomerByWmPoiId(wmPoiId);
    }

    public Integer selectWmCustomerIdByWmPoiId(Long wmPoiId) {
        return wmLeafCustomerRealService.selectWmCustomerIdByWmPoiId(wmPoiId);
    }

    /**
     * 更新提审信息为已打包提审
     *
     * @param customerId
     * @throws TException
     * @throws WmCustomerException
     */
    public void packageCommitAudited(int customerId) throws WmCustomerException {
        wmLeafCustomerRealService.packageCommitAudited(customerId);
    }

    public WmCustomerBatchAuditBo commitAudit(Integer customerId, Integer opUid, String opName, Boolean force) throws WmCustomerException, TException {
        return wmLeafCustomerRealService.commitAudit(customerId, opUid, opName, force);
    }

    public Integer commitAuditHeron(Integer customerId, Integer opUid, String opName, Boolean force) throws WmCustomerException, TException {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.meituan.waimai.customer.service.customer.WmCustomerService.commitAuditHeron(java.lang.Integer,java.lang.Integer,java.lang.String,java.lang.Boolean)");
        return wmLeafCustomerRealService.commitAuditHeron(customerId, opUid, opName, force);
    }


    /**
     * 插入客户操作日志
     *
     * @param customerId
     * @param userId
     * @param userName
     * @param opType
     * @param log
     * @throws WmCustomerException
     * @throws TException
     */
    public void insertCustomerOpLog(Integer customerId, Integer userId, String userName, WmCustomerOplogBo.OpType opType, String log) throws WmCustomerException {
        wmLeafCustomerRealService.insertCustomerOpLog(customerId, userId, userName, opType, log);
    }

    /**
     * 插入客户操作日志
     *
     * @param customerId
     * @param userId
     * @param userName
     * @param opType
     * @param log
     * @param remark
     * @throws WmCustomerException
     * @throws TException
     */
    public void insertCustomerOpLog(Integer customerId, Integer userId, String userName, WmCustomerOplogBo.OpType opType, String log, String remark) throws WmCustomerException {
        wmLeafCustomerRealService.insertCustomerOpLog(customerId, userId, userName, opType, log, remark);

    }


    /**
     * 分配责任人
     *
     * @param customerIdList
     * @param userId
     */
    public void distributeCustomer(List<Integer> customerIdList, int userId, Integer opUid, String opName) throws WmCustomerException {
        wmLeafCustomerRealService.distributeCustomer(customerIdList, userId, opUid, opName);
    }

    /**
     * 变更客户责任人(员工离职)
     *
     * @param oldOwnerUid
     * @param newOwnerUid
     */
    public void changeCustomerOwner(Integer oldOwnerUid, Integer newOwnerUid) throws WmCustomerException {
        wmLeafCustomerRealService.changeCustomerOwner(oldOwnerUid, newOwnerUid);
    }


    public void changeCustomerOwnerUidByWmPoi(Long wmPoiId, int ownerUid, int opUid, String opUname) throws WmCustomerException {
        wmLeafCustomerRealService.changeCustomerOwnerUidByWmPoi(wmPoiId, ownerUid, opUid, opUname);
    }

    /**
     * 通过关键字查询客户列表(用于下拉提示)
     *
     * @param keyword
     * @param searchType
     */
    public List<WmCustomerDB> selectCustomerListByKeyword(String keyword, Integer searchType, int isLeaf) throws WmCustomerException {
        return wmLeafCustomerRealService.selectCustomerListByKeyword(keyword, searchType, isLeaf);
    }

    /**
     * 通过关键字查询客户列表(用于下拉提示)
     * @param customerConditionBO
     */
    public List<WmCustomerDB> selectCustomerListByCondition(SearchCustomerConditionBO customerConditionBO) throws WmCustomerException {
        return wmLeafCustomerRealService.searchCustomerListByCondition(customerConditionBO);
    }

    /**
     * 通过客户Ids查询客户责任人
     *
     * @param customerIdList
     * @return
     */
    public List<WmCustomerDB> selectCustomerOwnUidList(List<Integer> customerIdList) throws WmCustomerException {
        return wmLeafCustomerRealService.selectCustomerOwnUidList(customerIdList);
    }

    /**
     * 保存客户共用资质证明
     *
     * @param customerId
     * @param urlSet
     * @param otherUrlSet
     * @param opUid
     * @param opName
     * @throws WmCustomerException
     */
    public void saveOrUpdateCustomerCommonQua(int customerId, Set<String> urlSet, Set<String> otherUrlSet, int opUid, String opName) throws WmCustomerException {
        wmLeafCustomerRealService.saveOrUpdateCustomerCommonQua(customerId, urlSet, otherUrlSet, opUid, opName);
    }

    /**
     * 增加客户共用资质证明
     *
     * @param customerId
     * @param commonQuaUrlSet
     * @param otherUrlSet
     * @param opUid
     * @param opName
     * @throws WmCustomerException
     */
    public void addCustomerCommonQuaAndOther(int customerId, Set<String> commonQuaUrlSet, Set<String> otherUrlSet, int opUid, String opName) throws WmCustomerException {
        wmLeafCustomerRealService.addCustomerCommonQuaAndOther(customerId, commonQuaUrlSet, otherUrlSet, opUid, opName);
    }

    public void saveOrUpdateCustomerCommonQuaOtherToDB(int customerId, Set<String> otherUrlSet) throws WmCustomerException {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.service.customer.WmCustomerService.saveOrUpdateCustomerCommonQuaOtherToDB(int,java.util.Set)");
        wmLeafCustomerRealService.saveOrUpdateCustomerCommonQuaOtherToDB(customerId, otherUrlSet);
    }


    /**
     * 获取客户共用资质证明列表
     *
     * @param customerId
     * @return
     */
    public Set<String> getCustomerQuaList(Integer customerId) {
        return wmLeafCustomerRealService.getCustomerQuaList(customerId);
    }

    /**
     * 获取客户其他附件列表
     *
     * @param customerId
     * @return
     */
    public List<String> getCustomerQuaOtherList(Integer customerId) {
        return wmLeafCustomerRealService.getCustomerQuaOtherList(customerId);
    }

    /**
     * 获取客户其他附件列表
     *
     * @param customerId
     * @return
     */
    public Set<String> getCustomerQuaOtherListMaster(Integer customerId) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.service.customer.WmCustomerService.getCustomerQuaOtherListMaster(java.lang.Integer)");
        return wmLeafCustomerRealService.getCustomerQuaOtherListMaster(customerId);
    }

    /**
     * 发送客户状态MQ
     *
     * @param customerId
     * @param customerMQEventEnum
     * @param wmPoiIdSet
     * @param opUid
     * @param opName
     */
    public void sendCustomerStatusNoticeMQ(Integer customerId, CustomerMQEventEnum customerMQEventEnum, Set<Long> wmPoiIdSet, Integer opUid, String opName) {
        wmLeafCustomerRealService.sendCustomerStatusNoticeMQ(customerId, customerMQEventEnum, wmPoiIdSet, opUid, opName);
    }

    /**
     * 发送客户状态MQ-支持消息体内容扩展
     */
    public void sendCustomerStatusNoticeMQWithExtension(Integer customerId, CustomerMQEventEnum customerMQEventEnum, Set<Long> wmPoiIdSet, Integer opUid, String opName, Map<String, Object> extension) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.customer.WmCustomerService.sendCustomerStatusNoticeMQWithExtension(Integer,CustomerMQEventEnum,Set,Integer,String,Map)");
        wmLeafCustomerRealService.sendCustomerStatusNoticeMQWithExtension(customerId, customerMQEventEnum, wmPoiIdSet, opUid, opName, extension);
    }

    public boolean saveOrUpdateCustomerOtherScan(Integer customerId, List<String> urlList, int opUid, String opName) throws WmCustomerException {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.customer.WmCustomerService.saveOrUpdateCustomerOtherScan(java.lang.Integer,java.util.List,int,java.lang.String)");
        return wmLeafCustomerRealService.saveOrUpdateCustomerOtherScan(customerId, urlList, opUid, opName);
    }

    /**
     * 查询客户类型
     *
     * @param userId
     * @return
     * @throws TException
     * @throws WmCustomerException
     * @throws WmServerException
     */
    public WmCustomerRealTypeAggreDTO getCustomerRealType(Integer userId, int authType) throws TException, WmCustomerException, WmServerException {
        return wmLeafCustomerRealService.getCustomerRealType(userId, authType);
    }

    /**
     * 通过编号和客户类型获取id大于1一千万的客户
     *
     * @param customerNum
     * @param customerType
     * @return
     * @throws TException
     * @throws WmCustomerException
     */
    public WmCustomerBasicBo getNewCustomerByNumberAndType(String customerNum, int customerType) throws TException, WmCustomerException {
        return wmLeafCustomerRealService.getNewCustomerByNumberAndType(customerNum, customerType);
    }

    /**
     * 根据客户资质编号、资质类型以及业务线查询客户信息
     *
     * @param customerNum
     * @param customerType
     * @param bizCode
     * @return
     * @throws TException
     * @throws WmCustomerException
     */
    public WmCustomerBasicBo getCustomerByNumberAndTypeAndBizCode(String customerNum, int customerType, int bizCode) throws TException, WmCustomerException {
        logger.info("getCustomerByNumberAndTypeAndBizCode,查询客户信息,customerNum={},customerType={},bizCode={}", customerNum, customerType, bizCode);
        if (StringUtils.isBlank(customerNum)) {
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "资质编号不能为空");
        }
        if (customerType <= 0 || CustomerType.getByCode(customerType) == null) {
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "客户资质主体不合法");
        }
        if (bizCode <= 0 || CustomerBizOrgEnum.of(bizCode) == null) {
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "业务线不合法");
        }
        return wmLeafCustomerRealService.getCustomerByNumberAndTypeAndBizCode(customerNum, customerType, bizCode);
    }

    public WmCustomerBasicBo getCustomerByIdOrMtCustomerId(long customerId) throws TException, WmCustomerException {
        return wmLeafCustomerRealService.getCustomerByIdOrMtCustomerId(customerId);
    }

    public List<WmCustomerBasicBo> getCustomerListByIdOrMtCustomerId(Set<Long> idSet) throws WmCustomerException {
        return wmLeafCustomerRealService.getCustomerListByIdOrMtCustomerId(idSet);
    }

    /**
     * 更新客户信息到美团客户平台
     *
     * @param customerId
     * @return
     * @throws TException
     */
    public boolean updateCustomerToMtCustomer(long customerId) throws TException, WmCustomerException {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.service.customer.WmCustomerService.updateCustomerToMtCustomer(long)");
        return wmLeafCustomerRealService.updateCustomerToMtCustomer(customerId);
    }

//    public List<WmCustomerDTO> getCustomerListByCustomerNumber(String customerNumber) throws TException, WmCustomerException {
//        return wmLeafCustomerRealService.getCustomerListByCustomerNumber(customerNumber);
//    }

    public Map<Long, WmCustomerBasicBo> getCustomerByWmPoiIds(List<Long> wmPoiIds) throws TException, WmCustomerException {
        return wmLeafCustomerRealService.getCustomerByWmPoiIds(wmPoiIds);
    }

    /**
     * 判断客户资质是否可以修改（蜂鸟系统调用）
     */
    public ValidateResultBo validCustomerUpdate(String data, int customerId, int opUid, String opName) throws TException, WmCustomerException {
        String status = WmCustomerConstant.SUCCESS;
        String reason = "";
        try {
            ValidateResultBo validateResultBo = wmLeafCustomerRealService.validCustomerUpdate(data, customerId, opUid, opName);
            //如果校验不通过，打不通过原因埋点
            if (validateResultBo.getCode() != CustomerConstants.RESULT_CODE_PASS) {
                status = String.valueOf(validateResultBo.getCode());
                reason = validateResultBo.getMsg();
            }
            return validateResultBo;
        } catch (WmCustomerException e) {
            status = WmCustomerConstant.BUSINESS_EXCEPTION;
            reason = e.getMsg();
            throw e;
        } catch (Exception e) {
            status = WmCustomerConstant.SYSTEM_EXCEPTION;
            reason = "未知原因";
            throw e;
        } finally {
            String source = "HUMMINGBIRD";
            Cat.logEvent(CustomerMetricEnum.CUSTOMER_VALID.getName(), source, status, reason);
            MetricHelper.build().name(CustomerMetricEnum.CUSTOMER_VALID.getName()).tag(CustomerMetricEnum.CUSTOMER_VALID.getTag(), source)
                    .tag(CustomerMetricEnum.CUSTOMER_VALID.getStatus(), status).count();
        }
    }

    /**
     * 客户资质修改&&审核回调
     */
    public void updateAndCallBack(WmCustomerAuditBo wCustomerAuditBo, int opUid, String opName) throws TException, WmCustomerException {
        try {
            wmLeafCustomerRealService.updateAndCallBack(wCustomerAuditBo, opUid, opName);
        } catch (WmCustomerException e) {
            logger.error("客户审核回调失败 req:{}", JSONObject.toJSONString(wCustomerAuditBo), e);
            //埋点上报
            MetricHelper.build().name(MetricConstant.METRIC_CUS_AUDIT_CALLBACK_FAIL)
                    .count();
            throw e;
        } catch (TException e) {
            logger.error("客户审核回调失败 req:{}", JSONObject.toJSONString(wCustomerAuditBo), e);
            //埋点上报
            MetricHelper.build().name(MetricConstant.METRIC_CUS_AUDIT_CALLBACK_FAIL)
                    .count();
            throw e;
        } catch (Exception e) {
            logger.error("客户审核回调失败 req:{}", JSONObject.toJSONString(wCustomerAuditBo), e);
            //埋点上报
            MetricHelper.build().name(MetricConstant.METRIC_CUS_AUDIT_CALLBACK_FAIL)
                    .count();
            throw new TException(e);
        }
    }

    public WmCustomerBasicBo getLatestWmCustomerBasicBo(Long wmPoiId) throws TException, WmCustomerException {
        return wmLeafCustomerRealService.getLatestWmCustomerBasicBo(wmPoiId);
    }

    /**
     * 清洗客户类型
     *
     * @param customerIdList
     * @param washNum
     * @param misId
     */
    public void washCustomerRealType(List<Integer> customerIdList, int washNum, String misId) throws TException, WmCustomerException {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.meituan.waimai.customer.service.customer.WmCustomerService.washCustomerRealType(java.util.List,int,java.lang.String)");

        wmLeafCustomerRealService.washCustmerRealType(customerIdList, washNum, misId);

    }

    /**
     * 根据客户ID查询生效客户, 满足客户类型则返回, 否则返回null
     *
     * @param customerId
     * @param realTypes
     * @return
     */
    public WmCustomerBasicBo getEffectByCustomerIdAndRealTypes(Long customerId, List<Integer> realTypes) throws TException, WmCustomerException {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.meituan.waimai.customer.service.customer.WmCustomerService.getEffectByCustomerIdAndRealTypes(java.lang.Long,java.util.List)");
        logger.info("getEffectByCustomerIdAndRealTypes customerId = {}, realTypes = {}", customerId, JSON.toJSONString(realTypes));
        if (CollectionUtils.isEmpty(realTypes)) {
            return null;
        }

        WmCustomerBasicBo wmCustomerBasicBo = getCustomerByIdOrMtCustomerId(customerId);
        if (wmCustomerBasicBo != null && CustomerConstants.EFFECT == wmCustomerBasicBo.getEffective() && realTypes.contains(wmCustomerBasicBo.getCustomerRealType())) {
            return wmCustomerBasicBo;
        }
        return null;
    }

    public Map<Integer, List<WmCustomerInfoBo>> getEffectByBrandIdList(WmCustomerBrandQueryParam queryParam) throws WmCustomerException {
        logger.info("getEffectByBrandIdList WmCustomerBrandQueryParam = {}", JSON.toJSONString(queryParam));
        AssertUtil.assertObjectNotNull(queryParam, "客户品牌查询对象");
        if (CollectionUtils.isEmpty(queryParam.getBrandIdList())) {
            return Maps.newHashMap();
        }

        Map<Integer, List<Integer>> brandCusIdListMap = wmCustomerBrandService.getBrandCusIdListMapByBrandIdList(queryParam.getBrandIdList());
        Set<Integer> customerIdSet = brandCusIdListMap.values().stream().flatMap(Collection::stream).collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(customerIdSet)) {
            return Maps.newHashMap();
        }

        // 查客户 -> Map<客户ID, 客户信息>
        List<WmCustomerDB> wmCustomerDBList = selectCustomerByIds(customerIdSet);
        Map<Integer, WmCustomerDB> customerMap = wmCustomerDBList.stream().collect(Collectors.toMap(item -> item.getId(), item -> item));
        // 查KP -> Map<客户ID, 客户KP信息>
        List<WmCustomerKp> wmCustomerKpList = wmCustomerKpService.batchGetCustomerKpOfEffectiveSigner(Lists.newArrayList(customerIdSet));
        Map<Integer, WmCustomerKp> customerKpMap = wmCustomerKpList.stream().collect(Collectors.toMap(item -> item.getCustomerId(), item -> item));

        Map<Integer, List<WmCustomerInfoBo>> brandCustomerInfoMap = Maps.newHashMap();
        brandCusIdListMap.entrySet().stream().forEach(entry -> {
            Integer brandId = entry.getKey();
            List<Integer> customerIdList = entry.getValue();

            List<WmCustomerInfoBo> wmCustomerInfoBoList = Lists.newArrayList();
            for (Integer customerId : customerIdList) {
                WmCustomerDB wmCustomerDB = customerMap.get(customerId);
                WmCustomerKp wmCustomerKp = customerKpMap.get(customerId);
                if (wmCustomerDB == null || CustomerConstants.EFFECT != wmCustomerDB.getEffective() || wmCustomerKp == null) {
                    continue;
                }

                WmCustomerInfoBo wmCustomerInfoBo = new WmCustomerInfoBo();
                wmCustomerInfoBo.setWmCustomerId(wmCustomerDB.getId());
                wmCustomerInfoBo.setMtCustomerId(wmCustomerDB.getMtCustomerId());
                wmCustomerInfoBo.setCustomerName(wmCustomerDB.getCustomerName());
                wmCustomerInfoBo.setAddress(StringUtils.defaultIfEmpty(wmCustomerDB.getAddress(), StringUtils.EMPTY));
                wmCustomerInfoBo.setCustomerKpName(wmCustomerKp.getCompellation());
                wmCustomerInfoBo.setCustomerKpPhone(wmCustomerKp.getPhoneNum());
                wmCustomerInfoBo.setCustomerKpEmail(StringUtils.defaultIfEmpty(wmCustomerKp.getEmail(), StringUtils.EMPTY));
                wmCustomerInfoBoList.add(wmCustomerInfoBo);
            }
            brandCustomerInfoMap.put(brandId, wmCustomerInfoBoList);
        });
        return brandCustomerInfoMap;
    }


    public long getWmCustomerIdByMtCustomerId(long mtCustomerId) {
        return wmLeafCustomerRealService.getWmCustomerIdByMtCustomerId(mtCustomerId);
    }


    /**
     * 获取客户列表
     *
     * @param queryDTO
     * @return
     */
    public List<WmCustomerBasicBo> getCustomerListByDB(WmCustomerQueryDTO queryDTO) throws WmCustomerException, TException {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.meituan.waimai.customer.service.customer.WmCustomerService.getCustomerListByDB(com.sankuai.meituan.waimai.thrift.customer.domain.customer.WmCustomerQueryDTO)");
        return wmLeafCustomerRealService.getCustomerListByDB(queryDTO);
    }

    /**
     * 判断客户类型是否为医药类型（医药类型包含：总部药品连锁、单店药品）
     */
    public boolean isMedicineTypeCustomer(Integer wmCustomerId) throws WmCustomerException {
        WmCustomerDB wmCustomerDB = selectCustomerById(wmCustomerId);
        if (null == wmCustomerDB) {
            // 默认非医药客户
            return false;
        }
        return wmCustomerDB.getCustomerRealType() == CustomerRealTypeEnum.ZONGBU_YAOPIN_LIANSUO.getValue()
                || wmCustomerDB.getCustomerRealType() == CustomerRealTypeEnum.DANDIAN_YAOPIN.getValue();
    }

    /**
     * 判断客户是否为医药类型并且具有问医寻药标签
     *
     * @param wmCustomerId 客户ID
     * @return 如果客户是医药类型（单店药品或总部药品连锁）且具有问医寻药标签，则返回true；否则返回false
     * @throws WmCustomerException 客户异常
     */
    public boolean isMedicineTypeWithXunYiWenYaoLabel(Integer wmCustomerId) throws WmCustomerException {
        // 首先判断客户是否为医药类型
        if (!isMedicineTypeCustomer(wmCustomerId)) {
            return false;
        }

        // 获取客户信息
        WmCustomerDB wmCustomerDB = selectCustomerById(wmCustomerId);
        if (null == wmCustomerDB) {
            return false;
        }
        // 查询客户是否具有问医寻药标签
        Map<Long, Boolean> resultMap = wmPoiFlowlineLabelThriftServiceAdapter.haveLabelRel(
                Collections.singletonList(wmCustomerDB.getMtCustomerId()),
                LabelSubjectTypeEnum.CUSTOMER,
                MccConfig.getB2CXunYiWenYaoLabelId());

        return Optional.ofNullable(resultMap.get(wmCustomerDB.getMtCustomerId())).orElse(Boolean.FALSE);
    }

    /**
     * 填充甲乙方签约人信息
     */
    public void fillContractSignInfo(List<WmTempletContractSignDB> contractSignDBList) {
        if (CollectionUtils.isEmpty(contractSignDBList)) {
            return;
        }
        //set空字符串，适配db字段notnull和入参不传的差异
        contractSignDBList.stream().forEach(contractSign -> {
            if (StringUtils.isEmpty(contractSign.getSignPeople())) {
                contractSign.setSignPeople("");
            }
            if (StringUtils.isEmpty(contractSign.getSignPhone())) {
                contractSign.setSignPhone("");
            }
        });
    }

    /**
     * 根据客户id查询客户信息
     *
     * @param customerId
     * @return
     * @throws TException
     * @throws WmCustomerException
     */
    public WmCustomerBasicBo getCustomerById(int customerId) throws TException, WmCustomerException {
        logger.info("通过id查客户：{}", customerId);
        WmCustomerDB wmCustomerDB = selectCustomerById(customerId);
        if (wmCustomerDB == null) {
            wmCustomerDB = selectCustomerByIdRT(customerId);
        }
        if (wmCustomerDB == null) {
            logger.warn("通过客户ID查询客户为空，" + customerId);
        }
        return WmCustomerTransUtil.customerDBToBasicBo(wmCustomerDB);
    }

    /**
     * 客户创建或者更新时判定客户是否提审
     *
     * @param isLeaf
     * @param customerRealType
     * @return
     */
    public boolean isCommitAuditWhenUpdate(int isLeaf, int customerRealType) {
        //保存后直接提审的客户类型
        List<Integer> saveCommitCustomerRealTypeList = MccCustomerConfig.getSaveCommitCustomerRealType();
        boolean isSaveCommitCustomerRealType = CollectionUtils.isNotEmpty(saveCommitCustomerRealTypeList) && saveCommitCustomerRealTypeList.contains(customerRealType);
        // 上级客户或特定客户类型保存后直接提审
        if ((isLeaf == CustomerConstants.CUSTOMER_IS_LEAF_NO) || isSaveCommitCustomerRealType) {
            return true;
        }
        return false;
    }

    public ValidateResultBo validateCustomerRealTypeSpInfo(WmCustomerBasicBo wmCustomerBasicBo, Integer opUid) throws TException, WmCustomerException, WmServerException {
        return wmLeafCustomerRealService.validateCustomerRealTypeSpInfo(wmCustomerBasicBo, opUid);
    }

    /**
     * 客户管理批量分配责任人-筛选出的全部数据
     *
     * @param wmCustomerFormBo
     * @return
     * @throws TException
     * @throws WmCustomerException
     */
    public List<Long> getMtCustomerIds(WmCustomerFormBo wmCustomerFormBo) throws TException, WmCustomerException {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.service.customer.WmCustomerService.getMtCustomerIds(com.sankuai.meituan.waimai.thrift.customer.domain.customer.WmCustomerFormBo)");
        return wmLeafCustomerRealService.getMtCustomerIds(wmCustomerFormBo);
    }

    public int getCurrentMaxId(int type) {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.service.customer.WmCustomerService.getCurrentMaxId(int)");
        if (type == CustomerConstants.MODULE_CUSTOMER) {
            return wmCustomerDBMapper.getMaxId();
        } else if (type == CustomerConstants.MODULE_KP) {
            return wmCustomerKpDBMapper.getMaxId();
        }
        return 0;
    }

    /**
     * 根据请求参数查询客户对象
     * minId maxId(左开右闭)
     *
     * @param wmCustomerCleanQuery
     * @return
     */
    public List<WmCustomerDB> listWmCustomerDBByCleanQuery(WmCustomerCleanQuery wmCustomerCleanQuery) {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.service.customer.WmCustomerService.listWmCustomerDBByCleanQuery(com.sankuai.meituan.waimai.customer.domain.WmCustomerCleanQuery)");
        return wmCustomerDBMapper.listCustomerByCleanQueryParams(wmCustomerCleanQuery);
    }

    /**
     * 根据客户ID查询本地存储的客户信息
     *
     * @param wmCustomerId
     * @return
     */
    public WmCustomerDB selectCustomerLocalByIdRT(Integer wmCustomerId) {
        return wmCustomerDBMapper.selectCustomerByIdRT(wmCustomerId);
    }

    public void updateCustomerAuditStatus(WmCustomerDB wmCustomerDB) {
        wmCustomerDBMapper.updateCustomerAuditStatus(wmCustomerDB);
    }

    /**
     * 查询我负责的客户数
     *
     * @param ownerId
     * @return
     * @throws TException
     * @throws WmCustomerException
     */
    public Integer findMyOwnerCustomerCount(Integer ownerId) {
        if (ownerId == null || ownerId <= 0) {
            return 0;
        }
        return wmCustomerDBMapper.countCustomerIdsByOwner(ownerId);
    }

    public void updateCustomer(WmCustomerDB wmCustomerDB, Integer opUid) throws WmCustomerException {
        // 平台更新成功，更新本地
        WmCustomerDB updateCustomer = WmCustomerDB.copyFromAnotherWmCustomerDB(wmCustomerDB);
        logger.info("db update customer info = {}", JSON.toJSONString(updateCustomer));
        wmCustomerDBMapper.updateCustomer(updateCustomer);
    }

    public void updateCustomerInfoById(WmCustomerDB wmCustomerDB){
        WmCustomerDB updateCustomer = WmCustomerDB.copyFromAnotherWmCustomerDB(wmCustomerDB);
        logger.info("updateCustomerInfoById = {}", JSON.toJSONString(updateCustomer));
        wmCustomerDBMapper.updateCustomerInfoById(updateCustomer);
    }

    /**
     * 更新营业执照状态
     *
     * @param customerId
     * @param certificateStatus
     */
    public int updateCustomerCertificateStatus(Integer customerId, Integer certificateStatus) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.customer.WmCustomerService.updateCustomerCertificateStatus(java.lang.Integer,java.lang.Integer)");
        if (customerId == null || customerId <= 0 || certificateStatus == null) {
            return 0;
        }
        return wmCustomerDBMapper.updateCertificateStatus(customerId, certificateStatus);

    }

    public List<WmCustomerDB> getCustomerListByDB(WmCustomerQueryVo vo) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.customer.WmCustomerService.getCustomerListByDB(com.sankuai.meituan.waimai.customer.domain.WmCustomerQueryVo)");
        return wmCustomerDBMapper.selectByQueryVo(vo);
    }

    public void updateCustomerContractor(WmCustomerDB wmCustomerDB, Integer opUid) throws WmCustomerException {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.service.customer.WmCustomerService.updateCustomerContractor(com.sankuai.meituan.waimai.customer.domain.WmCustomerDB,java.lang.Integer)");
        // 平台更新成功，更新本地
        WmCustomerDB updateCustomer = WmCustomerDB.copyFromAnotherWmCustomerDB(wmCustomerDB);
        logger.info("db update contractor customer info = {}", JSON.toJSONString(updateCustomer));
        wmCustomerDBMapper.updateCustomerContractor(updateCustomer);
    }

    /**
     * insertCustomer 适配
     *
     * @param wmCustomerDB
     * @return
     * @throws WmCustomerException
     */
    public Integer insertCustomer(WmCustomerDB wmCustomerDB) throws WmCustomerException {
        WmCustomerDB insertCustomer = WmCustomerDB.parseInsertCustomerInfo(wmCustomerDB);
        wmCustomerDBMapper.insertCustomer(insertCustomer);
        return insertCustomer.getId();
    }

    /**
     * 根据客户ID查询占用档口数信息
     *
     * @param wmCustomerId
     * @return
     */
    public CustomerMscUsedPoiDTO getMscUsedPoiDTO(Integer wmCustomerId) throws WmCustomerException {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.meituan.waimai.customer.service.customer.WmCustomerService.getMscUsedPoiDTO(java.lang.Integer)");
        CustomerMscUsedPoiDTO customerMscUsedPoiDTO = new CustomerMscUsedPoiDTO();
        //客户类型美食城才需要判断
        WmCustomerDB wmCustomerDB = selectPlatformCustomerByIdFromSlave(wmCustomerId);
        if (wmCustomerDB == null) {
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "未查询到客户");
        }
        customerMscUsedPoiDTO.setAllPoiCnt(null);
        //如果美食城则设置档口数量
        if (wmCustomerDB.getCustomerRealType() == CustomerRealTypeEnum.MEISHICHENG.getValue()) {
            String mscSpInfoStr = wmCustomerDB.getCustomerRealTypeSpInfo();
            //美食城档口数
            if (StringUtils.isNotEmpty(mscSpInfoStr)) {
                CustomerRealTypeSpInfoBo customerRealTypeSpInfoBo = JSON.parseObject(mscSpInfoStr, CustomerRealTypeSpInfoBo.class);
                customerMscUsedPoiDTO.setAllPoiCnt(customerRealTypeSpInfoBo.getFoodCityPoiCount() == null ? null : customerRealTypeSpInfoBo.getFoodCityPoiCount());
            }
        }

        //美食城已占用档口数合计
        Integer allUsedPoiCnt = 0;
        List<MscUsedPoiDetailDTO> mscUsedPoiDetailDTOList = Lists.newArrayList();
        for (MscUsedPoiSceneEnum mscUsedPoiSceneEnum : MscUsedPoiSceneEnum.values()) {
            MscUsedPoiDetailDTO mscUsedPoiDetailDTO = getUsedPoiDetailByScene(wmCustomerId, mscUsedPoiSceneEnum);
            if (mscUsedPoiDetailDTO == null || mscUsedPoiDetailDTO.getUsedPoiCnt() == 0) {
                continue;
            }
            mscUsedPoiDetailDTOList.add(mscUsedPoiDetailDTO);
            allUsedPoiCnt = allUsedPoiCnt + mscUsedPoiDetailDTO.getUsedPoiCnt();
        }

        customerMscUsedPoiDTO.setUsedPoiCnt(allUsedPoiCnt);
        customerMscUsedPoiDTO.setUsedPoiDetailList(mscUsedPoiDetailDTOList);
        customerMscUsedPoiDTO.setAllNoMscPoiCntSwitch(MccCustomerConfig.getMscAllowNoPoiCntSwitch());
        return customerMscUsedPoiDTO;

    }

    /**
     * 查询美食城已占用档口数且包含门店品牌信息（供前端展示使用）
     * @param wmCustomerId
     * @return
     * @throws WmCustomerException
     */
    public CustomerMscUsedPoiDetailDTO getMscUsedPoiDetailWithBrand(Integer wmCustomerId) throws WmCustomerException {
        logger.info("查询美食城已占用档口数且包含门店品牌信息-customerId:{}",wmCustomerId);
        CustomerMscUsedPoiDetailDTO mscUsedPoiDetailDTOV2 = getMscUsedPoiDetailDTOV2(wmCustomerId);
        if(!CollectionUtils.isEmpty(mscUsedPoiDetailDTOV2.getMscPoiInfoDTOList())){
            // 补全门店信息列表中的品牌信息
            fillBrandInfo(mscUsedPoiDetailDTOV2.getMscPoiInfoDTOList());
            // 补全责任人姓名和mis号
            fillOwnerInfo(mscUsedPoiDetailDTOV2.getMscPoiInfoDTOList());
        }
        return mscUsedPoiDetailDTOV2;
    }

    public void fillOwnerInfo(List<MscPoiInfoDTO> mscPoiInfoDTOList) throws WmCustomerException {
        Set<Integer> uidSet = new HashSet<>();
        mscPoiInfoDTOList.forEach(dto -> {
            if (dto.getPoiOwnerUid() != null && dto.getPoiOwnerUid() != 0L) {
                uidSet.add(dto.getPoiOwnerUid().intValue());
            }
            if (dto.getPoiSellerUid() != 0) {
                uidSet.add(dto.getPoiSellerUid());
            }
        });
        List<WmEmploy> wmEmploys = employeeService.mgetByUids(new ArrayList<>(uidSet));
        if (!wmEmploys.isEmpty()) {
            Map<Integer,WmEmploy> employMap =  wmEmploys.stream()
                    .collect(Collectors.toMap(WmEmploy::getUid, Function.identity()));
            mscPoiInfoDTOList.forEach(dto -> {
                if(employMap.get(dto.getPoiOwnerUid().intValue()) != null){
                    dto.setPoiOwnerMis(employMap.get(dto.getPoiOwnerUid().intValue()).getMisId());
                    dto.setPoiOwnerName(employMap.get(dto.getPoiOwnerUid().intValue()).getName());
                }
                if(employMap.get(dto.getPoiSellerUid()) != null){
                    dto.setPoiSellerMis(employMap.get(dto.getPoiSellerUid()).getMisId());
                    dto.setPoiSellerName(employMap.get(dto.getPoiSellerUid()).getName());
                }
            });
        }


    }

    /**
     * 将输入的门店信息list中，补全业务品牌名称和物理品牌名称
     * @param mscPoiInfoDTOList
     */
    private void fillBrandInfo(List<MscPoiInfoDTO> mscPoiInfoDTOList) throws WmCustomerException {
        //获取门店信息列表中的业务品牌ID列表
        List<Integer> poiBrandIdList = mscPoiInfoDTOList.stream()
                .filter(dto->dto.getPoiBrandId() != null)
                .map(dto->dto.getPoiBrandId().intValue())
                .distinct()
                .collect(Collectors.toList());
        WmServiceBrandAggreSearchConditionDTO request = new WmServiceBrandAggreSearchConditionDTO();
        request.setServiceBrandIds(poiBrandIdList);

        // 根据业务ID批量查询业务品牌名称和物理品牌名称
        // 物理品牌:业务品牌=1:n
        List<WmServiceBrandAggreDTO> serviceBrandAggreDTOList = wmBrandAdapter.searchServiceBrandAggre(request);
        Map<Integer, WmServiceBrandAggreDTO> serviceBrandAggreMap = serviceBrandAggreDTOList.stream()
                .collect(Collectors.toMap(WmServiceBrandAggreDTO::getServiceBrandId, Function.identity()));
        mscPoiInfoDTOList.forEach(dto->{
            if(dto.getPoiBrandId() != null ){
                WmServiceBrandAggreDTO brandDto = serviceBrandAggreMap.get(dto.getPoiBrandId().intValue());
                if (brandDto != null){
                    dto.setPoiBrandName(brandDto.getServiceBrandName());
                    dto.setPoiOrigBrandName(brandDto.getOriginBrandName());
                }
            }
        });

    }

    public CustomerMscUsedPoiResponse getMscUsedPoiDTOByCustomerIdV2(Integer customerId) throws WmCustomerException {
        logger.info("#getMscUsedPoiDTOByCustomerIdV2-客户ID:{}",customerId);
        CustomerMscUsedPoiResponse response = new CustomerMscUsedPoiResponse();
        CustomerMscUsedPoiDetailDTO mscUsedPoiDetailDTOV2 = getMscUsedPoiDetailDTOV2(customerId);
        response.setCustomerId(customerId);
        response.setAllPoiCnt(mscUsedPoiDetailDTOV2.getAllPoiCnt());
        response.setUsedPoiCnt(mscUsedPoiDetailDTOV2.getUsedPoiCnt());
        logger.info("#getMscUsedPoiDTOByCustomerIdV2-客户ID:{},resp:{}",customerId,JSON.toJSONString(response));
        return response;

    }

    /**
     * 根据客户资质编码和资质主体类型查询已占用档口数
     */
    public CustomerMscUsedPoiResponse getMscUsedPoiDetailByCustomerNumber(String customerNumber,Integer customerType) throws WmCustomerException, TException {
        logger.info("getMscUsedPoiDetailByCustomerNumber-根据客户资质查询美食城已占用档口数-number:{},customerType:{}",customerNumber,customerType);
        //根据资质编码查询客户

        WmCustomerBasicBo wmCustomerBasicBo = wmLeafCustomerRealService.getCustomerByNumberAndTypeAndBizCode(customerNumber, customerType,CustomerBizOrgEnum.WAI_MAI.getCode());
        if (wmCustomerBasicBo == null){
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "未查询到客户");
        }
        return getMscUsedPoiDTOByCustomerIdV2(wmCustomerBasicBo.getId());
    }

    /**
     * 查询美食城已占用档口数V2版本
     * 查询和美食城客户共用资质的门店列表
     * @param wmCustomerId
     * @return
     * @throws WmCustomerException
     */
    public CustomerMscUsedPoiDetailDTO getMscUsedPoiDetailDTOV2(Integer wmCustomerId) throws WmCustomerException {
        logger.info("#getMscUsedPoiDetailDTOV2-查询美食城已占用档口数V2-customerId:{}",wmCustomerId);
        //客户类型美食城才需要判断
        WmCustomerDB wmCustomerDB = selectPlatformCustomerByIdFromSlave(wmCustomerId);
        if (wmCustomerDB == null) {
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "未查询到客户");
        }
        CustomerMscUsedPoiDetailDTO customerMscUsedPoiDetailDTO = new CustomerMscUsedPoiDetailDTO();
        if (wmCustomerDB.getCustomerRealType() == CustomerRealTypeEnum.MEISHICHENG.getValue()){
            customerMscUsedPoiDetailDTO.setAllPoiCnt(getMscAllPoiCnt(wmCustomerDB));
        }
        customerMscUsedPoiDetailDTO.setCustomerId(wmCustomerDB.getId());

        //如果客户的资质类型为个人资质，返回-1，不进行计算，前端特殊处理
        if (wmCustomerDB.getCustomerType() == CustomerType.CUSTOMER_TYPE_IDCARD.getCode()){
            customerMscUsedPoiDetailDTO.setUsedPoiCnt(-1);
            return customerMscUsedPoiDetailDTO;
        }
        customerMscUsedPoiDetailDTO.setUsedPoiCnt(0);
        // 调用门店资质RPC接口 查询共用资质的门店列表
        List<Long> repeatWmPoiId;
        if (MccCustomerConfig.mscUsedPoiCntNewQuaInterfaceSwitch()){
             repeatWmPoiId = getAllRepeatWmPoiId(wmCustomerDB);
        }else{
            repeatWmPoiId = wmQuaAdapter.getRepeatWmPoiId(buildQueryRepeatQuaWmPoiIdDTO(wmCustomerDB))
                    .stream().map(Integer::longValue)
                    .collect(Collectors.toList());;
        }

        if(!repeatWmPoiId.isEmpty()){
            //调用门店基本信息，过滤门店状态和子门店类型，并返回过滤后的门店信息列表
            List<MscPoiInfoDTO> filterWmPoiInfoList = null;

            // 灰度控制，命中灰度的客户会额外排除非外卖门店
            if (wmCustomerGrayService.isGrayMscPoiCntCheckNew(wmCustomerDB.getOwnerUid())) {
                filterWmPoiInfoList = filterWmPoiIdByPoiStatusInfoNew(repeatWmPoiId);
            } else {
                filterWmPoiInfoList = filterWmPoiIdByPoiStatusInfo(repeatWmPoiId);
            }

            if (!filterWmPoiInfoList.isEmpty()){
                customerMscUsedPoiDetailDTO.setUsedPoiCnt(filterWmPoiInfoList.size());
                customerMscUsedPoiDetailDTO.setMscPoiInfoDTOList(filterWmPoiInfoList);
            }
        }
        logger.info("#getMscUsedPoiDetailDTOV2-查询美食城已占用档口数V2-customerId:{},已占用档口数:{},门店列表:{}"
                ,wmCustomerId
                ,customerMscUsedPoiDetailDTO.getUsedPoiCnt()
                ,customerMscUsedPoiDetailDTO.getMscPoiInfoDTOList() == null ? null : customerMscUsedPoiDetailDTO.getMscPoiInfoDTOList().stream().map(MscPoiInfoDTO::getPoiId).collect(Collectors.toList()));
        return customerMscUsedPoiDetailDTO;
    }

    public List<Long> getAllRepeatWmPoiId(WmCustomerDB wmCustomerDB) throws WmCustomerException{
        List<Long> repeatWmPoiIdList = new ArrayList<>();
        repeatWmPoiIdList.addAll(getRepeatWmPoiIdByAuditState(wmCustomerDB,QuaNumberUsedStatusEnum.AUDITING));
        repeatWmPoiIdList.addAll(getRepeatWmPoiIdByAuditState(wmCustomerDB,QuaNumberUsedStatusEnum.USING));
        return repeatWmPoiIdList;
    }

    private List<Long> getRepeatWmPoiIdByAuditState(WmCustomerDB wmCustomerDB,QuaNumberUsedStatusEnum auditState) throws WmCustomerException {
        WmQuaRepeatRequest wmQuaRepeatRequest = new WmQuaRepeatRequest();
        // 处理资质类型
        QuaSubTypeAndQuaTypeRelEnum subType = QuaSubTypeAndQuaTypeRelEnum.getByCustomerType(wmCustomerDB.getCustomerType());
        QuaSecondSubTypeAndQuaTypeRelEnum secondSubTypeRelEnum = QuaSecondSubTypeAndQuaTypeRelEnum.getByCustomerSecondType(wmCustomerDB.getCustomerSecondType());

        if(subType == null || secondSubTypeRelEnum == null){
            logger.error("#getRepeatWmPoiIdByAuditState-客户资质主体类型映射失败 客户:{},主体类型:{}",wmCustomerDB.getId(),wmCustomerDB.getCustomerType());
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR,"客户资质主体类型映射失败");
        }
        wmQuaRepeatRequest.setSubType(subType.getQuaType());
        wmQuaRepeatRequest.setSecondSubType(secondSubTypeRelEnum.getQuaTypeEnum().getSecondSubType());
        wmQuaRepeatRequest.setNumber(wmCustomerDB.getCustomerNumber());
        long startId = 0L;
        wmQuaRepeatRequest.setStartId(startId);
        wmQuaRepeatRequest.setAuditState(auditState.getStatus());
        wmQuaRepeatRequest.setPageSize(MccCustomerConfig.getNumberRepeatPoiPageSize());
        List<WmQuaRepeatResponse> repeatPoi = wmQuaAdapter.getRepeatPoiByPageAndState(wmQuaRepeatRequest);
        List<Long> repeatWmPoiIdList = repeatPoi.stream().map(WmQuaRepeatResponse::getWmPoiId).collect(Collectors.toList());
        int currPageSize = repeatPoi.size();
        while (currPageSize >= MccCustomerConfig.getNumberRepeatPoiPageSize() && repeatWmPoiIdList.size() < MccCustomerConfig.getNumberRepeatPoiMaxSize()){
            startId = repeatPoi.get(currPageSize - 1).getId();
            wmQuaRepeatRequest.setStartId(startId);
            repeatPoi = wmQuaAdapter.getRepeatPoiByPageAndState(wmQuaRepeatRequest);
            repeatWmPoiIdList.addAll(repeatPoi.stream().map(WmQuaRepeatResponse::getWmPoiId).collect(Collectors.toList()));
            currPageSize = repeatPoi.size();
        }
        return repeatWmPoiIdList;
    }

    private List<MscPoiInfoDTO> filterWmPoiIdByPoiStatusInfoNew(List<Long> wmPoiIdList){

        List<WmPoiAggre> wmPoiList = wmPoiQueryAdapter.getWmPoiAggre(wmPoiIdList, MscPoiAggreConstant.MSC_POI_AGGRE_FIELDS);
        // 过滤出所有未下线以及非子门店   的门店
        List<WmPoiAggre> filterWmPoiList =  wmPoiList.stream()
                // 1. 门店状态为上单中 or 审核通过待上线
                .filter(wmPoiAggre -> wmPoiAggre.getValid() != WmPoiValidEnum.OFFLINE.getValue()
                        // 2. 门店不属于子门店
                        && wmPoiAggre.getSub_wm_poi_type() == 0
                        // 3. 门店分类为外卖
                        && PoiOrgEnum.WAI_MAI.getCode() == wmPoiAggre.getBiz_org_code())
                .collect(Collectors.toList());
        //转换
        return convert2MscPoiInfoDTO(filterWmPoiList);

    }

    private List<MscPoiInfoDTO> filterWmPoiIdByPoiStatusInfo(List<Long> wmPoiIdList){
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.service.customer.WmCustomerService.filterWmPoiIdByPoiStatusInfo(java.util.List)");

        List<WmPoiAggre> wmPoiList = wmPoiQueryAdapter.getWmPoiAggre(wmPoiIdList,MscPoiAggreConstant.MSC_POI_AGGRE_FIELDS);
        // 过滤出所有未下线以及非子门店   的门店
        List<WmPoiAggre> filterWmPoiList =  wmPoiList.stream()
                .filter(wmPoiAggre -> wmPoiAggre.getValid() != WmPoiValidEnum.OFFLINE.getValue() && wmPoiAggre.getSub_wm_poi_type() == 0) // 使用filter操作进行过滤
                .collect(Collectors.toList());
        //转换
        return convert2MscPoiInfoDTO(filterWmPoiList);

    }

    private List<MscPoiInfoDTO> convert2MscPoiInfoDTO(List<WmPoiAggre> wmPoiList){
        List<MscPoiInfoDTO> mscPoiInfoDTOList = new ArrayList<>();
        wmPoiList.forEach(poi->{
            MscPoiInfoDTO mscPoiInfoDTO = new MscPoiInfoDTO();
            mscPoiInfoDTO.setPoiId(poi.getWm_poi_id());
            mscPoiInfoDTO.setPoiName(poi.getName());
            mscPoiInfoDTO.setPoiValid(poi.getValid());
            mscPoiInfoDTO.setPoiValidDesc(WmPoiValidEnum.findByValue(poi.getValid()).getDesc());
            mscPoiInfoDTO.setPoiOwnerUid(poi.getOwner_uid());
            mscPoiInfoDTO.setPoiOrigBrandId(poi.getOrigin_brand_id());
            mscPoiInfoDTO.setPoiBrandId(poi.getBrand_id());
            mscPoiInfoDTO.setPoiSellerUid(poi.getSeller_uid());
            mscPoiInfoDTO.setPoiOwnerType(OwnerTypeEnum.of(poi.getOwner_type()) == null?null:OwnerTypeEnum.of(poi.getOwner_type()).getDesc());
            mscPoiInfoDTOList.add(mscPoiInfoDTO);

        });
        return mscPoiInfoDTOList;
    }


    private Integer getMscAllPoiCnt(WmCustomerDB wmCustomerDB){
        String mscSpInfoStr = wmCustomerDB.getCustomerRealTypeSpInfo();
        //美食城档口数
        if (StringUtils.isNotEmpty(mscSpInfoStr)) {
            CustomerRealTypeSpInfoBo customerRealTypeSpInfoBo = JSON.parseObject(mscSpInfoStr, CustomerRealTypeSpInfoBo.class);
            return customerRealTypeSpInfoBo.getFoodCityPoiCount() == null ? null : customerRealTypeSpInfoBo.getFoodCityPoiCount();
        }
        return null;
    }

    private QueryRepeatQuaWmPoiIdDTO buildQueryRepeatQuaWmPoiIdDTO(WmCustomerDB db) throws WmCustomerException {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.customer.WmCustomerService.buildQueryRepeatQuaWmPoiIdDTO(com.sankuai.meituan.waimai.customer.domain.WmCustomerDB)");
        QueryRepeatQuaWmPoiIdDTO queryRepeatQuaWmPoiIdDTO = new QueryRepeatQuaWmPoiIdDTO();
        // 处理资质类型
        QuaSubTypeAndQuaTypeRelEnum subType = QuaSubTypeAndQuaTypeRelEnum.getByCustomerType(db.getCustomerType());
        QuaSecondSubTypeAndQuaTypeRelEnum secondSubTypeRelEnum = QuaSecondSubTypeAndQuaTypeRelEnum.getByCustomerSecondType(db.getCustomerSecondType());

        if(subType == null || secondSubTypeRelEnum == null){
            logger.error("#getMscUsedPoiDetailDTOV2-客户资质主体类型映射失败 客户:{},主体类型:{}",db.getId(),db.getCustomerType());
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR,"客户资质主体类型映射失败");
        }
        queryRepeatQuaWmPoiIdDTO.setSubType(subType.getQuaType());
        queryRepeatQuaWmPoiIdDTO.setSecondSubType(secondSubTypeRelEnum.getQuaTypeEnum().getSecondSubType());
        queryRepeatQuaWmPoiIdDTO.setNumber(db.getCustomerNumber());
        return queryRepeatQuaWmPoiIdDTO;
    }


    /**
     * 根据不同统计场景计算占用档口数信息
     *
     * @param customerId
     * @param mscUsedPoiSceneEnum
     * @return
     */
    private MscUsedPoiDetailDTO getUsedPoiDetailByScene(Integer customerId, MscUsedPoiSceneEnum mscUsedPoiSceneEnum) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.customer.WmCustomerService.getUsedPoiDetailByScene(java.lang.Integer,com.sankuai.meituan.waimai.customer.constant.customer.MscUsedPoiSceneEnum)");
        MscUsedPoiDetailDTO mscUsedPoiDetailDTO = new MscUsedPoiDetailDTO();
        try {
            //客户切换场景
            if (mscUsedPoiSceneEnum.equals(MscUsedPoiSceneEnum.SWITCH_CUSTOMER)) {
                mscUsedPoiDetailDTO = getMscSwitchPoiUsed(customerId);
            } else {
                //开关开启从ES查询，否则直接查询DB
                if (MccCustomerConfig.getBindUsedPoiCntFromEsSwitch()) {
                    mscUsedPoiDetailDTO = getMscBindOrBindingUseFromEs(customerId, mscUsedPoiSceneEnum);
                } else {
                    mscUsedPoiDetailDTO = getMscBindOrBindingUse(customerId, mscUsedPoiSceneEnum);
                }
            }
        } catch (Exception e) {
            logger.error("getUsedPoiDetailByScene,统计不同场景下占用档口数发生异常,customerId={}", customerId, mscUsedPoiDetailDTO.getSceneName(), e);
        }
        return mscUsedPoiDetailDTO;
    }

    /**
     * 从ES获取绑定或绑定中占用门店数
     *
     * @param customerId
     * @param mscUsedPoiSceneEnum
     * @return
     */
    public MscUsedPoiDetailDTO getMscBindOrBindingUseFromEs(Integer customerId, MscUsedPoiSceneEnum mscUsedPoiSceneEnum) {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.service.customer.WmCustomerService.getMscBindOrBindingUseFromEs(java.lang.Integer,com.sankuai.meituan.waimai.customer.constant.customer.MscUsedPoiSceneEnum)");
        MscUsedPoiDetailDTO mscUsedPoiDetailDTO = new MscUsedPoiDetailDTO();
        mscUsedPoiDetailDTO.setUsedPoiCnt(0);
        mscUsedPoiDetailDTO.setSceneName(mscUsedPoiSceneEnum.getName());
        mscUsedPoiDetailDTO.setSceneId(mscUsedPoiSceneEnum.getCode());
        mscUsedPoiDetailDTO.setRelTaskIds(Lists.newArrayList());
        try {

            SearchResponse queryEsResponse = poiListEsService.countByCustomerIdAndRelStatus(customerId,
                    mscUsedPoiSceneEnum.getRelStatus(), mscUsedPoiSceneEnum.getSwitchCustomerFlag());
            //查询结果为空则需要直接返回
            if (queryEsResponse == null || queryEsResponse.getHits() == null
                    || queryEsResponse.getHits().getHits() == null) {
                return mscUsedPoiDetailDTO;
            }
            Long usedPoiCnt = queryEsResponse.getHits().getTotalHits();
            mscUsedPoiDetailDTO.setUsedPoiCnt(usedPoiCnt.intValue());

        } catch (Exception e) {
            logger.error("getMscBindOrBindingUseFromEs,从ES获取绑定或绑定中已占用档口数发生异常,customerId={}", customerId, e);
        }
        return mscUsedPoiDetailDTO;
    }

    /**
     * 获取客户切换场景占用门店信息
     *
     * @param customerId
     * @return
     */
    public MscUsedPoiDetailDTO getMscSwitchPoiUsed(Integer customerId) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.customer.WmCustomerService.getMscSwitchPoiUsed(java.lang.Integer)");
        MscUsedPoiDetailDTO mscUsedPoiDetailDTO = new MscUsedPoiDetailDTO();
        mscUsedPoiDetailDTO.setUsedPoiCnt(0);
        mscUsedPoiDetailDTO.setSceneName(MscUsedPoiSceneEnum.SWITCH_CUSTOMER.getName());
        mscUsedPoiDetailDTO.setSceneId(MscUsedPoiSceneEnum.SWITCH_CUSTOMER.getCode());
        mscUsedPoiDetailDTO.setRelTaskIds(Lists.newArrayList());
        try {
            List<SwitchTaskAndPoiDTO> toList = wmPoiSwitchServiceAdapter.listSwitchTaskAndPoiByToCustomerId(customerId.longValue());
            List<SwitchTaskAndPoiDTO> fromList = wmPoiSwitchServiceAdapter.listSwitchTaskAndPoiByFromCustomerId(customerId.longValue());
            logger.info("getMscSwitchPoiUsed,fromList={},toList={}", JSON.toJSONString(fromList), JSON.toJSONString(toList));
            toList.addAll(fromList);
            if (CollectionUtils.isEmpty(toList)) {
                return mscUsedPoiDetailDTO;
            }
            List<Long> switchTaskIdList = Lists.newArrayList();
            Integer notChildPoiAllCnt = 0;
            for (SwitchTaskAndPoiDTO switchTaskAndPoiDTO : toList) {
                Integer cntNotChild = countNotChildPoi(switchTaskAndPoiDTO.getWmPoiIds());
                switchTaskIdList.add(switchTaskAndPoiDTO.getTaskId());
                notChildPoiAllCnt = notChildPoiAllCnt + cntNotChild;
            }
            mscUsedPoiDetailDTO.setUsedPoiCnt(notChildPoiAllCnt);
            mscUsedPoiDetailDTO.setRelTaskIds(switchTaskIdList);
        } catch (Exception e) {
            logger.error("getMscSwitchPoiUsed,统计切换客户场景下占用档口数发生异常,customerId={}", customerId, e);
        }
        return mscUsedPoiDetailDTO;
    }


    /**
     * 获取绑定解绑场景的占用档口信息
     *
     * @param customerId
     * @param mscUsedPoiSceneEnum
     * @return
     */
    public MscUsedPoiDetailDTO getMscBindOrBindingUse(Integer customerId, MscUsedPoiSceneEnum mscUsedPoiSceneEnum) {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.service.customer.WmCustomerService.getMscBindOrBindingUse(java.lang.Integer,com.sankuai.meituan.waimai.customer.constant.customer.MscUsedPoiSceneEnum)");
        MscUsedPoiDetailDTO mscUsedPoiDetailDTO = new MscUsedPoiDetailDTO();
        mscUsedPoiDetailDTO.setUsedPoiCnt(0);
        mscUsedPoiDetailDTO.setSceneName(mscUsedPoiSceneEnum.getName());
        mscUsedPoiDetailDTO.setSceneId(mscUsedPoiSceneEnum.getCode());
        mscUsedPoiDetailDTO.setRelTaskIds(Lists.newArrayList());

        List<Long> wmPoiIdList = Lists.newArrayList();
        if (mscUsedPoiSceneEnum == MscUsedPoiSceneEnum.TO_BIND_ONLINE) {
            wmPoiIdList = wmCustomerPoiRelService.getBindingNotSwitchPoi(customerId);
        } else if (mscUsedPoiSceneEnum == MscUsedPoiSceneEnum.BIND_ON_LINE) {
            wmPoiIdList = wmCustomerPoiRelService.getBindNotSwitchPoi(customerId);
        }
        if (CollectionUtils.isEmpty(wmPoiIdList)) {
            return mscUsedPoiDetailDTO;
        }
        try {
            List<List<Long>> partWmPoiIds = Lists.partition(wmPoiIdList, MccCustomerConfig.getCalMscUsedCntBatchQueryPoiCnt());
            long notChildOnlineCnt = 0;
            for (List<Long> wmPoiIds : partWmPoiIds) {
                long partNotChildOnlineCnt = 0;
                List<WmPoiAggre> aggreList =
                        wmPoiQueryAdapter.getWmPoiAggreList(wmPoiIds, Sets.newHashSet(WM_POI_FIELD_WM_POI_ID,
                                WmPoiFieldQueryConstant.WM_POI_FIELD_LABEL_IDS, WM_POI_FIELD_VALID, WM_POI_FIELD_SUB_WM_POI_TYPE));
                if (CollectionUtils.isNotEmpty(aggreList)) {
                    //上线的非子门店数计算
                    if (MccCustomerConfig.getSubPoiIdentifySwitch()) {
                        partNotChildOnlineCnt = aggreList.stream()
                                .filter(wmPoiAggre -> wmPoiAggre.getValid() == WmPoiValidEnum.ONLINE.getValue())
                                .filter(wmPoiAggre -> WmCustomerConstant.POI_NOT_SUB_POI_TYPE.equals(wmPoiAggre.getSub_wm_poi_type())).count();
                    } else {
                        partNotChildOnlineCnt = aggreList.stream()
                                .filter(wmPoiAggre -> wmPoiAggre.getValid() == WmPoiValidEnum.ONLINE.getValue())
                                .filter(wmPoiAggre -> StringUtils.isBlank(wmPoiAggre.getLabel_ids())
                                        || Collections.disjoint(
                                        Arrays.stream(wmPoiAggre.getLabel_ids().split(",")).map(Integer::valueOf).collect(Collectors.toList()),
                                        MccCustomerConfig.getSubPoiTagId())).count();
                    }
                    logger.info("getMscBindOrBindingUse,当前批次符合条件的已上线非子门店个数,partNotChildOnlineCnt={}", partNotChildOnlineCnt);
                }
                notChildOnlineCnt = notChildOnlineCnt + partNotChildOnlineCnt;
            }
            mscUsedPoiDetailDTO.setUsedPoiCnt((int) notChildOnlineCnt);
        } catch (Exception e) {
            logger.error("getMscBindOrBindingUse,批量查询门店信息发生异常,customerId={}", customerId, e);
        }
        logger.info("getMscBindOrBindingUse,mscUsedPoiDetailDTO={}", JSON.toJSONString(mscUsedPoiDetailDTO));
        return mscUsedPoiDetailDTO;
    }

    /**
     * 美食城客户新增/修改 是否直接提审
     *
     * @param wmCustomerBasicBo
     * @return
     */
    public boolean checkMscCustomerDir2Audit(WmCustomerBasicBo wmCustomerBasicBo) {
        //美食城类型兼容:如信息完备则可直接提审,如信息缺失则不可直接提审
        if (wmCustomerBasicBo.getCustomerRealType() != CustomerRealTypeEnum.MEISHICHENG.getValue()) {
            return true;
        }
        if (wmCustomerBasicBo.getCustomerRealTypeSpInfoBo() == null) {
            return false;
        }
        try {
            // 20241212 此处之前判断CustomerId如果为0，则校验档口数和视频，但是在调用方法之前，wmCustomerBasicBo实体中，id已被赋值。。因此有bug
            // 本次修改：因为这个方法只有在新增客户的时候会判断，所以去掉了CustomerId==0的判断
            //Integer customerId = wmCustomerBasicBo.getId();
            CustomerRealTypeSpInfoBo customerRealTypeSpInfoBo = wmCustomerBasicBo.getCustomerRealTypeSpInfoBo();
            //新增场景，美食城视频或档口数未录入则不能直接提审
            logger.info("校验新增美食城客户是否直接提审:{}",JSON.toJSONString(customerRealTypeSpInfoBo));
            if (StringUtils.isBlank(customerRealTypeSpInfoBo.getFoodCityVideo()) || customerRealTypeSpInfoBo.getFoodCityPoiCount() == null
                    || customerRealTypeSpInfoBo.getFoodCityPoiCount() <= 0) {
                return false;
            }
            if(!wmCustomerGrayService.isGrayMscPoiCntCheckNew(wmCustomerBasicBo.getOwnerUid())){
                // 理论上不会走到这个分支，此方法只在新增客户的时候才会调用，保险起见还是加灰度控制，新的灰度逻辑不再判断资质共用标签
                //美食城客户更新，无资质共用标要求美食城视频和档口数量必须录入
                if (!wmCustomerMSCLabelService.checkHasQuaComCustomerLabel(wmCustomerBasicBo.getMtCustomerId())
                        && (StringUtils.isBlank(customerRealTypeSpInfoBo.getFoodCityVideo())
                        || customerRealTypeSpInfoBo.getFoodCityPoiCount() <= 0)) {
                    return false;
                }
            }
        } catch (Exception e) {
            logger.error("mscCustomerAddDir2Audit,判断美食城客户新增场景是否直接提审发生异常,wmCustomerBasicBo={}", JSON.toJSONString(wmCustomerBasicBo), e);
        }
        return true;
    }


    /**
     * 直接修改客户类型
     *
     * @param customerId
     * @param newCustomerRealType
     */
    public void updateCustomerRealType(Integer customerId, Integer newCustomerRealType) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.meituan.waimai.customer.service.customer.WmCustomerService.updateCustomerRealType(java.lang.Integer,java.lang.Integer)");
        wmCustomerDBMapper.updateCustomerRealTypeById(customerId, newCustomerRealType);
    }

    /**
     * 更新客户的审核状态
     *
     * @param customerId
     * @param auditStatus
     */
    public void updateAuditStatusByCustomerId(Integer customerId, Integer auditStatus) {
        wmCustomerDBMapper.updateAuditStatus(customerId, auditStatus);
    }


    /**
     * 计算一批次门店中非子门店数
     *
     * @param wmPoiIds
     * @return
     */
    private int countNotChildPoi(List<Long> wmPoiIds) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.customer.WmCustomerService.countNotChildPoi(java.util.List)");
        Integer notChildCnt = wmPoiIds.size();
        try {
            List<List<Long>> partWmPoiIdList = Lists.partition(wmPoiIds, 100);
            for (List<Long> wmPoiIdList : partWmPoiIdList) {
                List<Long> subPoiIdList = Lists.newArrayList();
                subPoiIdList = wmPoiClient.getTargetLablePoiIds(wmPoiIdList, LabelSubjectTypeEnum.POI.getCode(), MccCustomerConfig.getSubPoiTagId());
                if (CollectionUtils.isNotEmpty(subPoiIdList)) {
                    notChildCnt = notChildCnt - subPoiIdList.size();
                }
            }
        } catch (Exception e) {
            logger.error("countNotChildPoi,计算批次门店中非子门店数发生异常,wmPoiIds={}", JSON.toJSONString(wmPoiIds), e);
        }
        return notChildCnt;
    }

    /**
     * 根据客户ID查询客户经营形式列表
     * @param customerId
     * @return
     * @throws WmCustomerException
     * @see BusinessLineEnum
     */
    public Set<Long> getCustomerBusinessLineById(Integer customerId) throws WmCustomerException {
        logger.info("#getCustomerBusinessLineById-根据外卖客户ID查询客户经营形式列表:{}",customerId);
        // 查询客户信息
        if (customerId <= 0){
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR,"客户ID非法");
        }
        WmCustomerDB wmCustomerDB = selectCustomerById(customerId);
        if (wmCustomerDB == null){
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR,"未查询到客户信息");
        }
        return wmLeafCustomerRealService.getCustomerBusinessLine(wmCustomerDB);
    }

    /**
     * 修改客户责任人并添加操作日志
     *
     * @param apply
     * @param opUid
     * @param opName
     * @throws WmCustomerException
     */
    public void changeCustomerOwnerByApply(WmCustomerOwnerApply apply, Integer opUid, String opName) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.customer.WmCustomerService.changeCustomerOwnerByApply(WmCustomerOwnerApply,Integer,String)");
        Integer customerId = apply.getCustomerId();
        wmCustomerPlatformDataParseService.distributeCustomer(Lists.newArrayList(customerId), apply.getApplyUid());

        try {
            WmCustomerOplogBo wmCustomerOplogBo = new WmCustomerOplogBo(customerId,
                    WmCustomerOplogBo.OpModuleType.CUSTOMER, customerId);
            wmCustomerOplogBo.setOpType(WmCustomerOplogBo.OpType.UPDATE.type);

            wmCustomerOplogBo.setLog(String.format(WmCustomerConstant.CustomerOwnerApplyLogStr,
                    (apply.getCustomerOwnerUid() == null || apply.getCustomerOwnerUid() == 0) ? "无"
                            : wmEmployeeService.getUserAndId(apply.getCustomerOwnerUid()),
                    wmEmployeeService.getUserAndId(apply.getApplyUid()), apply.getId()));
            wmCustomerOplogBo.setOpUid(opUid);
            wmCustomerOplogBo.setOpUname(opName);
            wmCustomerOplogService.insert(wmCustomerOplogBo);
        } catch (Exception e) {
            logger.error("changeCustomerOwnerByApply,客户责任人申请通过，修改客户责任人添加操作记录异常,applyId={},customerId={}", apply.getId(),
                    apply.getCustomerId(), e);
        }
    }

}
