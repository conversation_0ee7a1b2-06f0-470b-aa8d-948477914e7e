package com.sankuai.meituan.waimai.customer.service.customer.check.customer;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.MoreObjects;
import com.google.common.base.Preconditions;
import com.google.common.collect.Lists;
import com.sankuai.meituan.util.ConfigUtilAdapter;
import com.sankuai.meituan.waimai.customer.config.MccCustomerConfig;
import com.sankuai.meituan.waimai.customer.contract.domain.WmTempletContractDB;
import com.sankuai.meituan.waimai.customer.contract.service.impl.WmContractService;
import com.sankuai.meituan.waimai.customer.dao.WmCustomerPoiDBMapper;
import com.sankuai.meituan.waimai.customer.ddd.domain.customer.aggre.WmCustomerPoiAggre;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerAuditDB;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerDB;
import com.sankuai.meituan.waimai.customer.service.AutoWireBase;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerPoiService;
import com.sankuai.meituan.waimai.customer.service.kp.WmCustomerKpService;
import com.sankuai.meituan.waimai.customer.settle.service.WmSettleService;
import com.sankuai.meituan.waimai.poilogistics.thrift.domain.WmPoiAuditStatusInfo;
import com.sankuai.meituan.waimai.poilogistics.thrift.exception.WmPoiLogisticsException;
import com.sankuai.meituan.waimai.poilogistics.thrift.service.WmPoiLogisticsProcessInfoThriftService;
import com.sankuai.meituan.waimai.thrift.constant.WmContractErrorCodeConstant;
import com.sankuai.meituan.waimai.thrift.customer.constant.*;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.ValidateResultBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.WmCustomerBasicBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.kp.WmCustomerKp;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.thrift.TException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@NoArgsConstructor
@Slf4j
public class SignModeSwitchCheckerAsy extends AutoWireBase{

    private static Logger logger = LoggerFactory.getLogger(SignModeSwitchCheckerAsy.class);


    private Integer signMode;


    private WmCustomerDB wmCustomerDB;

    private WmCustomerAuditDB wmCustomerAuditDB;

    @Autowired
    private WmPoiLogisticsProcessInfoThriftService.Iface wmPoiLogisticsProcessInfoThriftService;

    @Autowired
    private WmSettleService wmSettleService;

    @Autowired
    private WmContractService wmContractService;

    @Autowired
    private WmCustomerKpService wmCustomerKpService;

    @Autowired
    private WmCustomerPoiDBMapper wmCustomerPoiDBMapper;


    public SignModeSwitchCheckerAsy(Integer signMode, WmCustomerDB wmCustomerDB, WmCustomerAuditDB wmCustomerAuditDB) {
        Preconditions.checkNotNull(signMode);
        Preconditions.checkNotNull(wmCustomerDB);
        this.signMode = signMode;
        this.wmCustomerDB = wmCustomerDB;
        this.wmCustomerAuditDB = wmCustomerAuditDB;
    }

    public String check() throws WmCustomerException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.customer.check.customer.SignModeSwitchCheckerAsy.check()");
        if (wontSwitchSignMode()) {
            return String.format("「客户id%s%s」系统切换签约形式失败，不符合要求“签约模式未修改”", wmCustomerDB.getMtCustomerId(), wmCustomerDB.getCustomerName());
        }
        StringBuffer errorMsg = new StringBuffer();

        // 电子转纸质
        if (toPaperSignMode()) {
            /** 1、聚合配送商和美食城客户不允许改为纸质 **/
            checkConfirmForCustomerRealType(errorMsg);
            /** 2、判断客户的结算信息是否是审核中、确认中、待确认 **/
            checkAuditAndConfirmForSettle(errorMsg);
            /** 3、判断合同信息确认中 **/
            checkConfirmForContract(errorMsg);
            /** 4、判断门店是否解绑确认中 **/
            checkConfirmForUnbindingPoi(errorMsg);
            /** 5、判断客户下门店的配送信息是否是审核中、确认中 **/
            checkAuditAndConfirmForDelivery(errorMsg);
        } else {
            // 纸质转电子
            /** 1、判断客户的结算信息是否是审核中 **/
            checkAuditForSettle(errorMsg);
            /** 2、判断是否有生效的签约人信息 **/
            checkEffectiveForKp(errorMsg);
            /** 3、判断客户下门店的配送信息是否是审核中 **/
            checkAuditForDelivery(errorMsg);
        }

        if (errorMsg != null && errorMsg.length() > 0) {
            return String.format("「客户id%s%s」系统切换签约形式失败，不符合要求“%s”", wmCustomerDB.getMtCustomerId(), wmCustomerDB.getCustomerName(), errorMsg.toString());
        }
        return null;
    }

    /**
     * 是否切换签约模式
     *
     * @return
     */
    public boolean wontSwitchSignMode() {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_1", "com.sankuai.meituan.waimai.customer.service.customer.check.customer.SignModeSwitchCheckerAsy.wontSwitchSignMode()");
        return this.signMode == null
                || this.signMode.equals(wmCustomerDB.getSignMode());
    }

    /**
     * 是否签约模式切纸质
     * @return
     */
    private boolean toPaperSignMode() {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.customer.check.customer.SignModeSwitchCheckerAsy.toPaperSignMode()");
        return this.signMode == CustomerSignMode.PAPER.getCode();
    }


    // 校验客户类型
    private void checkConfirmForCustomerRealType(StringBuffer errorMsg) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_1", "com.sankuai.meituan.waimai.customer.service.customer.check.customer.SignModeSwitchCheckerAsy.checkConfirmForCustomerRealType(java.lang.StringBuffer)");
        if (CustomerRealTypeEnum.AGGREGATE_DISTRIBUTOR.getValue() == wmCustomerDB.getCustomerRealType()) {
            errorMsg.append("客户类型为'聚合配送商'时, 不可选择纸质签约；");
        }
        if (CustomerRealTypeEnum.MEISHICHENG.getValue() == wmCustomerDB.getCustomerRealType()) {
            errorMsg.append("客户类型为'美食城'时, 不可选择纸质签约；");
        }

        if (wmCustomerAuditDB != null) {
            if (CustomerRealTypeEnum.AGGREGATE_DISTRIBUTOR.getValue() == wmCustomerAuditDB.getCustomerRealType()) {
                errorMsg.append("客户类型为'聚合配送商'时, 不可选择纸质签约；");
            }
            if (CustomerRealTypeEnum.MEISHICHENG.getValue() == wmCustomerAuditDB.getCustomerRealType()) {
                errorMsg.append("客户类型为'美食城'时, 不可选择纸质签约；");
            }
        }
    }


    /**
     * 校验结算审核和签约状态
     *
     * @throws WmCustomerException
     */
    private void checkAuditAndConfirmForSettle(StringBuffer errorMsg) throws WmCustomerException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_2", "com.sankuai.meituan.waimai.customer.service.customer.check.customer.SignModeSwitchCheckerAsy.checkAuditAndConfirmForSettle(java.lang.StringBuffer)");
        byte settleStatus = wmSettleService.getSettleStatusByWmCustomerId(wmCustomerDB.getId());
        if (settleStatus == WmSettleConstant.SETTLE_STATUS_TO_AUDIT) {
            errorMsg.append("结算信息正在审核中；");
        }
        if (settleStatus == WmSettleConstant.SETTLE_STATUS_TO_CONFIRM
                || settleStatus == WmSettleConstant.SETTLE_STATUS_WAIT_SIGN) {
            errorMsg.append("结算信息正在确认中；");
        }
    }

    /**
     * 校验C1、C2框架合同
     */
    private void checkConfirmForContract(StringBuffer errorMsg) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.service.customer.check.customer.SignModeSwitchCheckerAsy.checkConfirmForContract(java.lang.StringBuffer)");
        List<WmTempletContractDB> contractList = wmContractService.getContractByCustomerIdAndTypes(wmCustomerDB.getId(),
                Lists.newArrayList(WmTempletContractTypeEnum.C1_E.getCode(), WmTempletContractTypeEnum.C2_E.getCode()));
        for (WmTempletContractDB contractDB : MoreObjects.firstNonNull(contractList, Lists.<WmTempletContractDB>newArrayList())) {
            if (contractDB.getStatus().equals(CustomerContractStatus.SIGNING.getCode())
                    || contractDB.getStatus().equals(CustomerContractStatus.WAITING_SIGN.getCode())) {
                errorMsg.append("合同信息正在确认中；");
                break;
            }
        }
    }

    /**
     * 客户下存在解绑中门店，校验失败
     */
    private void checkConfirmForUnbindingPoi(StringBuffer errorMsg) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.service.customer.check.customer.SignModeSwitchCheckerAsy.checkConfirmForUnbindingPoi(java.lang.StringBuffer)");
        List<Long> unbindingWmPoiIdList = wmCustomerPoiDBMapper.selectUnBindingPoi(wmCustomerDB.getId());
        if (CollectionUtils.isNotEmpty(unbindingWmPoiIdList)) {
            errorMsg.append(String.format("解绑确认信息正在确认中，对应门店%s；", JSONObject.toJSONString(unbindingWmPoiIdList)));
        }
    }

    /**
     * 校验客户下所有门店的配送信息审核和签约状态
     *
     * @throws WmCustomerException
     */
    private void checkAuditAndConfirmForDelivery(StringBuffer errorMsg) throws WmCustomerException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_1", "com.sankuai.meituan.waimai.customer.service.customer.check.customer.SignModeSwitchCheckerAsy.checkAuditAndConfirmForDelivery(java.lang.StringBuffer)");
        List<Long> poiIds = WmCustomerPoiAggre.Factory.make().selectWmPoiIdsByCustomerId(wmCustomerDB.getId());
        if (CollectionUtils.isEmpty(poiIds)) {
            return;
        }
        List<Long> errorAuditList = Lists.newArrayList();
        List<Long> errorConfirmList = Lists.newArrayList();

        List<List<Long>> poiIdBatchList = Lists.partition(poiIds, ConfigUtilAdapter.getInt("check_switch_mode_poibatch_size", 100));
        for (List<Long> oneBtach : poiIdBatchList) {
            Map<Long, WmPoiAuditStatusInfo> statusMap;
            try {
                statusMap = wmPoiLogisticsProcessInfoThriftService.getAuditStatus(oneBtach);
                logger.info("获取配送状态 auditStatusMap：{}", JSON.toJSONString(statusMap));
            } catch (WmPoiLogisticsException e) {
                logger.error("获取门店配送状态失败：oneBtach={},errMsg={}", JSONObject.toJSONString(oneBtach), e.getMsg(), e);
                throw new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR, "系统异常了");
            } catch (TException e) {
                logger.error("获取门店配送状态失败：oneBtach={}", JSONObject.toJSONString(oneBtach), e);
                throw new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR, "系统异常了");
            }
            if (statusMap == null) {
                continue;
            }
            for (Map.Entry<Long, WmPoiAuditStatusInfo> entry : statusMap.entrySet()) {
                if (entry.getValue().auditStatus) {
                    errorAuditList.add(entry.getKey());
                }
                if (entry.getValue().confirmStatus || entry.getValue().waitConfirmStatus) {
                    errorConfirmList.add(entry.getKey());
                }
            }
        }
        if (CollectionUtils.isNotEmpty(errorAuditList)) {
            errorMsg.append(String.format("配送信息正在审核中,对应门店%s；", JSONObject.toJSONString(errorAuditList)));
        }
        if (CollectionUtils.isNotEmpty(errorConfirmList)) {
            errorMsg.append(String.format("配送信息正在确认中,对应门店%s；", JSONObject.toJSONString(errorConfirmList)));
        }
    }

    /**
     * 校验结算审核状态
     *
     * @throws WmCustomerException
     */
    private void checkAuditForSettle(StringBuffer errorMsg) throws WmCustomerException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.customer.check.customer.SignModeSwitchCheckerAsy.checkAuditForSettle(java.lang.StringBuffer)");
        byte settleStatus = wmSettleService.getSettleStatusByWmCustomerId(wmCustomerDB.getId());
        if (settleStatus == WmSettleConstant.SETTLE_STATUS_TO_AUDIT) {
            errorMsg.append("结算信息正在审核中；");
        }
    }

    /**
     * 签约模式为电子签约，必须存在生效签约人KP
     */
    private void checkEffectiveForKp(StringBuffer errorMsg) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_1", "com.sankuai.meituan.waimai.customer.service.customer.check.customer.SignModeSwitchCheckerAsy.checkEffectiveForKp(java.lang.StringBuffer)");
        WmCustomerKp customerSignerKp = wmCustomerKpService.getCustomerKpOfEffectiveSigner(wmCustomerDB.getId());
        if (customerSignerKp == null) {
            errorMsg.append("无生效状态的KP信息；");
        }
    }

    /**
     * 校验客户下所有门店的配送信息审核状态
     *
     * @throws WmCustomerException
     */
    private void checkAuditForDelivery(StringBuffer errorMsg) throws WmCustomerException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.customer.check.customer.SignModeSwitchCheckerAsy.checkAuditForDelivery(java.lang.StringBuffer)");
        List<Long> poiIds = WmCustomerPoiAggre.Factory.make().selectWmPoiIdsByCustomerId(wmCustomerDB.getId());
        if (CollectionUtils.isEmpty(poiIds)) {
            return;
        }
        List<Long> errorWmPoiIdList = Lists.newArrayList();
        List<List<Long>> poiIdBatchList = Lists.partition(poiIds, ConfigUtilAdapter.getInt("check_switch_mode_poibatch_size", 100));
        for (List<Long> oneBtach : poiIdBatchList) {
            Map<Long, WmPoiAuditStatusInfo> auditStatusMap;
            try {
                auditStatusMap = wmPoiLogisticsProcessInfoThriftService.getAuditStatus(oneBtach);
                logger.info("获取配送状态 auditStatusMap：{}", JSON.toJSONString(auditStatusMap));
            } catch (WmPoiLogisticsException e) {
                logger.error("获取门店配送状态失败：oneBtach={},errMsg={}", JSONObject.toJSONString(oneBtach), e.getMsg(), e);
                throw new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR, "系统异常了");
            } catch (TException e) {
                logger.error("获取门店配送状态失败：oneBtach={}", JSONObject.toJSONString(oneBtach), e);
                throw new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR, "系统异常了");
            }
            if (auditStatusMap == null) {
                continue;
            }

            for (Map.Entry<Long, WmPoiAuditStatusInfo> entry : auditStatusMap.entrySet()) {
                if (entry.getValue().auditStatus) {
                    errorWmPoiIdList.add(entry.getKey());
                }
            }
        }
        if (CollectionUtils.isNotEmpty(errorWmPoiIdList)) {
            errorMsg.append(String.format("配送信息正在审核中,对应门店%s；", JSONObject.toJSONString(errorWmPoiIdList)));
        }
    }



}
