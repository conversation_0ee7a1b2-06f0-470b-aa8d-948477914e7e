package com.sankuai.meituan.waimai.customer.service.customer.monitor;

import com.dianping.frog.sdk.alarm.DXUtil;
import com.dianping.frog.sdk.data.BinlogRawData;
import com.dianping.frog.sdk.data.DmlType;
import com.dianping.frog.sdk.data.RawData;
import com.dianping.frog.sdk.rpc.RpcService;
import com.dianping.frog.sdk.rpc.RpcServiceFactory;
import com.dianping.frog.sdk.runner.DefaultRuleRunner;
import com.dianping.frog.sdk.util.BcpLoggerUtils;
import com.dianping.frog.sdk.util.JsonUtils;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.Map;

/**
 * 签约任务一致性监控
 */
public class WmCustomerSMSRuleRunner extends DefaultRuleRunner {


    @Override
    public boolean filter(RawData rawData, RawData... targetData) throws Exception {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.service.customer.monitor.WmCustomerSMSRuleRunner.filter(com.dianping.frog.sdk.data.RawData,com.dianping.frog.sdk.data.RawData[])");
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.service.customer.monitor.WmCustomerSMSRuleRunner.filter(com.dianping.frog.sdk.data.RawData,com.dianping.frog.sdk.data.RawData[])");
        BinlogRawData binlogRawData = (BinlogRawData) rawData;
        if (binlogRawData.getDmlType() == DmlType.DELETE) {
            return false;
        }
        return true;
    }

    @Override
    public String check(RawData rawData, RawData... rawData2) throws Exception {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.customer.monitor.WmCustomerSMSRuleRunner.check(com.dianping.frog.sdk.data.RawData,com.dianping.frog.sdk.data.RawData[])");
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.customer.monitor.WmCustomerSMSRuleRunner.check(com.dianping.frog.sdk.data.RawData,com.dianping.frog.sdk.data.RawData[])");
        BinlogRawData binlogRawData = (BinlogRawData) rawData;
        String wmPoiIds = binlogRawData.getColumnInfoMap().get("wm_poi_ids").getNewValue().toString();
        try {
            Map<String, Object> params = Maps.newHashMap();
            String taskId = binlogRawData.getColumnInfoMap().get("task_id").getNewValue().toString();
            String customerId = binlogRawData.getColumnInfoMap().get("customer_id").getNewValue().toString();
            String ctime = binlogRawData.getColumnInfoMap().get("ctime").getNewValue().toString();
            String type = binlogRawData.getColumnInfoMap().get("type").getNewValue().toString();

            params.put("taskId", Long.valueOf(taskId));
            params.put("customerId", Integer.valueOf(customerId));
            params.put("ctime", Integer.valueOf(ctime) - 5);
            params.put("utime", (System.currentTimeMillis() / 1000 - 55));
            params.put("wmPoiIds", Arrays.asList(wmPoiIds.split(",")));
            params.put("type", Integer.valueOf(type));
            params.put("switchTaskId", 0L);

            if (binlogRawData.getDmlType() == DmlType.INSERT) {
                RpcService rpcService = RpcServiceFactory.getThriftInstance("com.sankuai.meituan.waimai.thrift.customer.service.WmCustomerThriftService",
                        "com.sankuai.waimai.e.customer", 10000, null, "8433");
                String result = rpcService.invoke("monitorCustomerSMS",
                        Lists.newArrayList("com.sankuai.meituan.waimai.thrift.customer.domain.customer.monitor.MonitorCustomerSMSDTO"),
                        Lists.newArrayList(JsonUtils.toJson(params)));
                if (!StringUtils.isBlank(result) && !"\"\"".equals(result) && !"null".equals(result)) {
                    return result;
                }
            } else if (binlogRawData.getDmlType() == DmlType.UPDATE) {
                String taskStatusOld = binlogRawData.getColumnInfoMap().get("task_status").getOldValue().toString();
                String taskStatusNew = binlogRawData.getColumnInfoMap().get("task_status").getNewValue().toString();
                boolean taskStatusOldIsInProcess = taskStatusOld.equals("1") || taskStatusOld.equals("2") || taskStatusOld.equals("3") ? true : false;
                boolean taskStatusNewIsEnd = taskStatusNew.equals("4") || taskStatusNew.equals("5") || taskStatusNew.equals("6") || taskStatusNew.equals("7") ? true : false;
                if (!(taskStatusOldIsInProcess && taskStatusNewIsEnd)) {
                    return String.format("并发操作短信签约任务:taskId:%s,wmPoiIds:%s", taskId, wmPoiIds);
                }
            }
        } catch (Exception e) {
            BcpLoggerUtils.info("类型转换失败:msg={}" + binlogRawData.toString() + ";异常日志:" + e.getStackTrace());    // 打印日志
        }
        return null;
    }

    @Override
    public void alarm(String s, RawData rawData, RawData... rawData2) throws Exception {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.customer.monitor.WmCustomerSMSRuleRunner.alarm(java.lang.String,com.dianping.frog.sdk.data.RawData,com.dianping.frog.sdk.data.RawData[])");
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.customer.monitor.WmCustomerSMSRuleRunner.alarm(java.lang.String,com.dianping.frog.sdk.data.RawData,com.dianping.frog.sdk.data.RawData[])");
        //通过大象公众号发送告警消息, 收件人为告警配置中的告警接收人。
        DXUtil.sendAlarm(s);
    }

}
