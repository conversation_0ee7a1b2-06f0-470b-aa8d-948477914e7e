package com.sankuai.meituan.waimai.customer.service.sign.manualpack.strategy.noticetask;

import com.alibaba.fastjson.JSON;
import com.sankuai.meituan.waimai.customer.contract.config.dto.WmManualSignTaskContext;
import com.sankuai.meituan.waimai.customer.contract.service.impl.WmContractService;
import com.sankuai.meituan.waimai.customer.service.sign.applytask.context.ManualPackNoticeContext;
import com.sankuai.meituan.waimai.thrift.customer.constant.config.ContractSourceEnum;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import lombok.extern.slf4j.Slf4j;
import org.apache.thrift.TException;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * @description: 配置化合同
 * @author: liuyunjie05
 * @create: 2024/6/22 09:32
 */
@Slf4j
@Service
public class CommonConfigContractNoticeTask implements NoticeTask {

    @Resource
    private WmContractService wmContractService;

    @Override
    public void notice(String module, List<Long> bizIdList, ManualPackNoticeContext context, List<Long> taskIds) throws WmCustomerException, TException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.service.sign.manualpack.strategy.noticetask.CommonConfigContractNoticeTask.notice(String,List,ManualPackNoticeContext,List)");
        log.info("CommonConfigContractNoticeTask#notice, module: {}, context: {}, bizIdList: {}", module, JSON.toJSONString(context), JSON.toJSONString(bizIdList));
        for (Long bizId : bizIdList) {
            WmManualSignTaskContext manualSignTaskContext = WmManualSignTaskContext.builder()
                    .customerId(context.getCustomerId())
                    .contractId(bizId)
                    .opUid(context.getCommitUid())
                    .opUname("")
                    .manualBatchId(context.getManualBatchId())
                    .contractSource(ContractSourceEnum.CONFIG.getCode())
                    .build();
            wmContractService.startSignByWaitingSign(manualSignTaskContext);
        }
    }
}
