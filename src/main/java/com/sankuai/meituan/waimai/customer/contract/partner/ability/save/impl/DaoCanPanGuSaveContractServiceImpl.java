package com.sankuai.meituan.waimai.customer.contract.partner.ability.save.impl;

import com.sankuai.meituan.auth.util.UserUtils;
import com.sankuai.meituan.auth.vo.User;
import com.sankuai.meituan.util.DateUtil;
import com.sankuai.meituan.waimai.customer.contract.partner.ability.save.AbstractWmPartnerCustomerContractSaveAbilityService;
import com.sankuai.meituan.waimai.customer.contract.service.impl.WmContractService;
import com.sankuai.meituan.waimai.customer.service.common.EmpServiceAdaptor;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerErrorCodeConstants;
import com.sankuai.meituan.waimai.thrift.customer.constant.SignPackWay;
import com.sankuai.meituan.waimai.thrift.customer.constant.SignSubjectEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.WmTempletContractTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.partner.ContractSaveSceneTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.WmCustomerContractBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.WmTempletContractBasicBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.WmTempletContractSignBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.partnercontract.ContractOperatorDTO;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.partnercontract.CustomerContractSaveRequestDTO;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.partnercontract.CustomerContractSaveResponseDTO;
import com.sankuai.meituan.waimai.thrift.customer.domain.partner.DaoCanContractInfo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.thrift.TException;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * @description: 到餐盘古保存合同
 * @author: liuyunjie05
 * @create: 2024/8/6 11:54
 */
@Slf4j
@Service
public class DaoCanPanGuSaveContractServiceImpl extends AbstractWmPartnerCustomerContractSaveAbilityService {

    @Resource
    private WmContractService wmContractService;

    @Resource
    private EmpServiceAdaptor empServiceAdaptor;

    @Override
    public ContractSaveSceneTypeEnum getSupportSceneType() {
        return ContractSaveSceneTypeEnum.DAOCAN_PAN_GU_SAVE_CONTRACT;
    }

    private DaoCanContractInfo initDaoCanContractInfo(String contractProof, Long mtCustomerId) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.contract.partner.ability.save.impl.DaoCanPanGuSaveContractServiceImpl.initDaoCanContractInfo(java.lang.String,java.lang.Long)");
        DaoCanContractInfo daoCanContractInfo = new DaoCanContractInfo();
        daoCanContractInfo.setNeedCheckC1Renew(false);
        daoCanContractInfo.setContractProof(contractProof);
        daoCanContractInfo.setContractType(WmTempletContractTypeEnum.DAOCAN_SERVICE_C1_CONTRACT.getCode());
        daoCanContractInfo.setNewSignContract(true);
        daoCanContractInfo.setMtCustomerId(mtCustomerId);
        return daoCanContractInfo;
    }

    @Override
    public CustomerContractSaveResponseDTO saveCustomerContract(CustomerContractSaveRequestDTO requestDTO) throws WmCustomerException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.contract.partner.ability.save.impl.DaoCanPanGuSaveContractServiceImpl.saveCustomerContract(CustomerContractSaveRequestDTO)");
        try {
            validateRequestParam(requestDTO);
            Integer contractId = saveDaoCanC1Contract(requestDTO);
            return buildResponseDTO(contractId, requestDTO.getMtCustomerId());
        } catch (WmCustomerException e) {
            log.warn("DaoCanSelfSettleOpenServiceImpl#saveCustomerContract, warn", e);
            return fail(e.getMsg(), requestDTO.getMtCustomerId());
        } catch (Exception e) {
            log.error("DaoCanSelfSettleOpenServiceImpl#saveCustomerContract, error", e);
            return fail("保存失败", requestDTO.getMtCustomerId());
        }
    }

    private void validateRequestParam(CustomerContractSaveRequestDTO requestDTO) throws WmCustomerException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.contract.partner.ability.save.impl.DaoCanPanGuSaveContractServiceImpl.validateRequestParam(CustomerContractSaveRequestDTO)");
        checkCustomerContractParam(requestDTO);
        if (requestDTO.getContractProof() == null) {
            throw new WmCustomerException(CustomerErrorCodeConstants.PARAM_ERROR, "参数异常: 缺少合同凭证");
        }
        if (WmTempletContractTypeEnum.DAOCAN_SERVICE_C1_CONTRACT.getCode() != requestDTO.getContractType()) {
            throw new WmCustomerException(CustomerErrorCodeConstants.PARAM_ERROR, "该渠道暂时只支持发起到餐C1合同");
        }
        if (requestDTO.getSignPackWay() == null || SignPackWay.NONE == requestDTO.getSignPackWay()) {
            throw new WmCustomerException(CustomerErrorCodeConstants.PARAM_ERROR, "参数异常: 签约类型只支持直接签约或者打包签约");
        }
    }

    private CustomerContractSaveResponseDTO buildResponseDTO(Integer contractId, Long mtCustomerId) throws WmCustomerException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.contract.partner.ability.save.impl.DaoCanPanGuSaveContractServiceImpl.buildResponseDTO(java.lang.Integer,java.lang.Long)");
        CustomerContractSaveResponseDTO responseDTO = success(mtCustomerId);
        responseDTO.setSignTaskIdList(Collections.singletonList(contractId));
        return responseDTO;
    }

    private Integer saveDaoCanC1Contract(CustomerContractSaveRequestDTO requestDTO) throws TException, WmCustomerException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.contract.partner.ability.save.impl.DaoCanPanGuSaveContractServiceImpl.saveDaoCanC1Contract(CustomerContractSaveRequestDTO)");
        WmCustomerContractBo contractBo = wrapWmCustomerContractBo(requestDTO);
        Integer contractId = wmContractService.saveAndStartSign(contractBo, requestDTO.getOperatorDTO().getOpId().intValue(), requestDTO.getOperatorDTO().getOpName());
        log.info("DaoCanPanGuSaveContractServiceImpl#saveDaoCanC2Contract, contractId: {}", contractId);
        return contractId;
    }

    private User getCurrentUser() {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_1", "com.sankuai.meituan.waimai.customer.contract.partner.ability.save.impl.DaoCanPanGuSaveContractServiceImpl.getCurrentUser()");
        User user = UserUtils.getUser();
        if (user == null) {
            log.warn("DaoCanPanGuSaveContractServiceImpl#getCurrentUser, 获取当前用户信息失败");
            user = new User();
            user.setId(0);
            user.setName("BD");
        }
        return user;
    }

    private WmCustomerContractBo wrapWmCustomerContractBo(CustomerContractSaveRequestDTO requestDTO) throws WmCustomerException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.contract.partner.ability.save.impl.DaoCanPanGuSaveContractServiceImpl.wrapWmCustomerContractBo(CustomerContractSaveRequestDTO)");
        WmCustomerContractBo contractBo = new WmCustomerContractBo();

        Long mtCustomerId = requestDTO.getMtCustomerId();
        Integer wmCustomerId = getWmCustomerByMtCustomerId(requestDTO.getMtCustomerId());
        WmTempletContractBasicBo basicBo = initBasicBo(WmTempletContractTypeEnum.DAOCAN_SERVICE_C1_CONTRACT.getCode(), wmCustomerId);
        basicBo.setDaoCanContractInfo(initDaoCanContractInfo(requestDTO.getContractProof(), requestDTO.getMtCustomerId()));
        contractBo.setBasicBo(basicBo);

        List<WmTempletContractSignBo> signBoList = new ArrayList<>();
        signBoList.add(initDaoCanPartA(wmCustomerId, mtCustomerId));
        signBoList.add(initDaoCanC1PartB(requestDTO.getOperatorDTO()));
        contractBo.setSignBoList(signBoList);

        contractBo.setIgnoreExistAnotherSignTypeContract(false);
        contractBo.setPackWay(requestDTO.getSignPackWay().getCode());
        contractBo.setManualBatchId(0);
        return contractBo;
    }

    private WmTempletContractSignBo initPartA(int wmCustomerId, String singerName, String signerPhone) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.contract.partner.ability.save.impl.DaoCanPanGuSaveContractServiceImpl.initPartA(int,java.lang.String,java.lang.String)");
        WmTempletContractSignBo signBo = new WmTempletContractSignBo();
        signBo.setSignId(wmCustomerId);
        signBo.setTempletContractId(0);
        signBo.setSignPeople(singerName);
        signBo.setSignPhone(signerPhone);
        signBo.setSignName(StringUtils.defaultIfEmpty(getWmCustomerById(wmCustomerId).getCustomerName(), StringUtils.EMPTY));
        signBo.setSignTime(DateUtil.secondsToString(DateUtil.unixTime()));
        signBo.setSignType("A");
        return signBo;
    }

    private WmTempletContractSignBo initDaoCanC1PartB(ContractOperatorDTO operatorDTO) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_2", "com.sankuai.meituan.waimai.customer.contract.partner.ability.save.impl.DaoCanPanGuSaveContractServiceImpl.initDaoCanC1PartB(ContractOperatorDTO)");
        String today = DateUtil.secondsToString(DateUtil.unixTime());
        WmTempletContractSignBo signBo = new WmTempletContractSignBo();
        signBo.setSignId(0);
        signBo.setTempletContractId(0);
        signBo.setSignName(SignSubjectEnum.BJ_SANKUAI.getDesc());
        signBo.setSignPeople(operatorDTO.getOpName());
        signBo.setSignPhone(empServiceAdaptor.getPhone(operatorDTO.getOpId().intValue()));

        signBo.setSignTime(today);
        signBo.setSignType("B");
        return signBo;
    }

}
