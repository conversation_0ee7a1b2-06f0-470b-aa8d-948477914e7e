package com.sankuai.meituan.waimai.customer.service.kp.statemachine.action.legal;

import com.alibaba.fastjson.JSON;
import com.sankuai.meituan.waimai.customer.service.kp.CustomerKpBusinessService;
import com.sankuai.meituan.waimai.customer.service.kp.DifferentCustomerKpService;
import com.sankuai.meituan.waimai.customer.service.kp.WmCustomerKpLogService;
import com.sankuai.meituan.waimai.customer.service.kp.statemachine.action.KpLegalAbstractAction;
import com.sankuai.meituan.waimai.customer.service.kp.statemachine.constant.event.KpLegalEventEnum;
import com.sankuai.meituan.waimai.customer.service.kp.statemachine.context.KpLegalStatusMachineContext;
import com.sankuai.meituan.waimai.customer.service.kp.statemachine.machine.KpLegalStatusSM;
import com.sankuai.meituan.waimai.thrift.bo.WmCustomerDiffCellBo;
import com.sankuai.meituan.waimai.thrift.customer.constant.KpLegalStateMachine;
import com.sankuai.meituan.waimai.thrift.customer.domain.kp.WmCustomerKp;
import com.sankuai.meituan.waimai.thrift.exception.StatusMachineException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 20240410
 * @desc 已生效KP法人实名认证失败事件
 */
@Service
@Slf4j
public class EffectKpLegalRealNameFailAction extends KpLegalAbstractAction {

    @Autowired
    private CustomerKpBusinessService customerKpBusinessService;
    /**
     * KP法人实名认证失败
     *
     * @param from
     * @param to
     * @param eventEnum
     * @param context
     * @param kpLegalStatusSM
     */
    @Override
    public void execute(KpLegalStateMachine from, KpLegalStateMachine to, KpLegalEventEnum eventEnum,
                        KpLegalStatusMachineContext context, KpLegalStatusSM kpLegalStatusSM) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_4", "c.s.m.waimai.customer.service.kp.statemachine.action.legal.EffectKpLegalRealNameFailAction.execute(KpLegalStateMachine,KpLegalStateMachine,KpLegalEventEnum,KpLegalStatusMachineContext,KpLegalStatusSM)");
        try {
            customerKpBusinessService.updateEffectKp2RealNameFail(context);
        } catch (Exception e) {
            log.error("KpLegalInitCreateAction.updateEffectKp2RealNameFail,已生效KP法人流转到实名认证失败发生异常,context={}", JSON.toJSONString(context), e);
            throw new StatusMachineException("已生效KP法人实名失败事件异常");
        }
    }
}
