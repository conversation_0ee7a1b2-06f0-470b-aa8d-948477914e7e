package com.sankuai.meituan.waimai.customer.service.customer;

import com.alibaba.fastjson.JSON;
import com.dianping.pigeon.util.CollectionUtils;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.sankuai.meituan.waimai.customer.adapter.WmPoiFlowlineLabelThriftServiceAdapter;
import com.sankuai.meituan.waimai.customer.config.MccCustomerConfig;
import com.sankuai.meituan.waimai.customer.contract.service.impl.WmCustomerOplogService;
import com.sankuai.meituan.waimai.label.constants.LabelSubjectTypeEnum;
import com.sankuai.meituan.waimai.label.thrift.domain.WmLabelRel;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerConstants;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerErrorCodeConstants;
import com.sankuai.meituan.waimai.thrift.customer.domain.WmCustomerOplogBo;
import com.sankuai.meituan.waimai.thrift.domain.LabelAndLabelClassificationResult;
import com.sankuai.meituan.waimai.thrift.domain.WmPoiLabelRel;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import com.sankuai.meituan.waimai.thrift.exception.WmServerException;
import com.sankuai.meituan.waimai.thrift.service.WmPoiFlowlineLabelThriftService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service
public class WmCustomerLabelService {

    @Autowired
    private WmPoiFlowlineLabelThriftService.Iface wmPoiFlowlineLabelThriftService;

    @Autowired
    private WmCustomerOplogService wmCustomerOplogService;

    @Autowired
    private WmCustomerESService wmCustomerESService;

    @Autowired
    private WmPoiFlowlineLabelThriftServiceAdapter wmPoiFlowlineLabelThriftServiceAdapter;

    public void customerLabelSync(Long mtCustomerId, Long wmCustomerId) throws TException, WmCustomerException {
        log.info("customerLabelSync mtCustomerId:{},wmCustomerId:{}", mtCustomerId, wmCustomerId);
        // 反查打标平台，获取客户对应的所有标签，labelid传0表示获取全部标签
        List<WmPoiLabelRel> wmPoiLabelRelList;
        try {
            wmPoiLabelRelList = wmPoiFlowlineLabelThriftService.selectLabelRelBySubjectIdAndLabelId(mtCustomerId, 0, LabelSubjectTypeEnum.CUSTOMER.getCode());
        } catch (Exception e) {
            log.error("selectLabelRelBySubjectIdAndLabelId异常，mtCustomerId:{}", mtCustomerId, e);
            throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "客户标签同步至es时异常");
        }
        if (CollectionUtils.isEmpty(wmPoiLabelRelList)) {
            return;
        }
        List<Long> labelIdLongList = new ArrayList<>();
        List<String> labelIdStrList = new ArrayList<>();
        Long opUid = 0L;
        String opUname = "客户系统";
        for (WmPoiLabelRel wmPoiLabelRel : wmPoiLabelRelList) {
            if (wmPoiLabelRel.getWmLabelId() > 0L && wmPoiLabelRel.getSubjectId() == mtCustomerId) {
                labelIdStrList.add(String.valueOf(wmPoiLabelRel.getWmLabelId()));
                labelIdLongList.add(wmPoiLabelRel.getWmLabelId());
                if (wmPoiLabelRel.getOpUid() > 0L) {
                    opUid = wmPoiLabelRel.getOpUid();
                    opUname = wmPoiLabelRel.getOpUname();
                }
            }
        }
        String customerLabelIdsStr = Joiner.on(" ").join(labelIdStrList);
        try {
            wmCustomerESService.syncCustomerLabelToUpsertEs(wmCustomerId, customerLabelIdsStr);
        } catch (Exception e) {
            log.error("customerLabelSync#客户标签同步至es时异常，wmCustomerId:{}", wmCustomerId, e);
            throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, "客户标签同步至es时异常");
        }
        StringBuffer labelOpLog = new StringBuffer();
        log.info("selectWmPoiLabelAndWmPoiLabelClassificationByLabelIds入参:{}", JSON.toJSONString(labelIdLongList));
        try {
            List<LabelAndLabelClassificationResult> labelAndLabelClassificationResults = wmPoiFlowlineLabelThriftService.selectWmPoiLabelAndWmPoiLabelClassificationByLabelIds(labelIdLongList);
            labelAndLabelClassificationResults.stream()
                    .forEach(LabelAndLabelClassificationResult -> {
                        labelOpLog.append("【")
                                .append(LabelAndLabelClassificationResult.getWmPoiLabel().getName())
                                .append("】");
                    });
        } catch (WmServerException e) {
            String errorLabelNames = Joiner.on(",").join(labelIdStrList);
            labelOpLog.append("未知标签名称，标签id为:").append(errorLabelNames);
        }
        //标签日志
        String labelLogStr = String.format(CustomerConstants.CUSTOMER_CREATE_LABEL, labelOpLog.toString());
        WmCustomerOplogBo.OpType opType = WmCustomerOplogBo.OpType.INSERT;
        log.info("insertCustomerOpLog标签名称:{}", labelOpLog.toString());
        if (StringUtils.isNotBlank(labelOpLog)) {
            insertCustomerOpLog(wmCustomerId.intValue(), opUid.intValue(), opUname, opType,
                    String.format(labelLogStr, labelOpLog.toString()));
        }
    }


    public void insertCustomerOpLog(Integer customerId, Integer userId, String userName, WmCustomerOplogBo.OpType opType, String log) throws WmCustomerException {
        if (StringUtils.isBlank(log)) {
            return;
        }
        WmCustomerOplogBo wmCustomerOplogBo = new WmCustomerOplogBo(customerId, WmCustomerOplogBo.OpModuleType.CUSTOMER, customerId);
        wmCustomerOplogBo.setOpType(opType.type);
        wmCustomerOplogBo.setLog(log);
        wmCustomerOplogBo.setOpUid(userId);
        wmCustomerOplogBo.setOpUname(userName);
        wmCustomerOplogBo.setRemark("");
        wmCustomerOplogService.insert(wmCustomerOplogBo);
    }

    /**
     * 给客户打场景标签
     *
     * @param mtCustomerId
     * @param sceneType
     */
    public void addWmSingleCustomerSceneTag(Long mtCustomerId, Integer sceneType, Integer opUid, String opName) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_2", "com.sankuai.meituan.waimai.customer.service.customer.WmCustomerLabelService.addWmSingleCustomerSceneTag(java.lang.Long,java.lang.Integer,java.lang.Integer,java.lang.String)");
        try {
            Map<String, Integer> sceneTagMap = MccCustomerConfig.getCustomerSceneAndTagRel();
            log.info("addWmSingleCustomerSceneTag,新增客户场景标签,mtCustomerId={},sceneType={},sceneTagMap={}", mtCustomerId, sceneType, JSON.toJSONString(sceneTagMap));
            if (MapUtils.isEmpty(sceneTagMap) || sceneTagMap.get(sceneType.toString()) == null) {
                log.info("未匹配到对应的标签，不进行打标处理,mtCustomerId={},sceneType={},sceneTagMap={}", mtCustomerId, sceneType, JSON.toJSONString(sceneTagMap));
                return;
            }
            Integer tagId = sceneTagMap.get(sceneType.toString());

            Boolean checkHasWmSingleCustomerSceneTag = checkCustomerSceneLabel(mtCustomerId, Lists.newArrayList(tagId));
            if (checkHasWmSingleCustomerSceneTag) {
                log.info("addWmSingleCustomerSceneTag,当前客户有个人资质场景标签，不需要打标,mtCustomerId={}", mtCustomerId);
                return;
            }
            //给客户打场景标签
            wmPoiFlowlineLabelThriftServiceAdapter.batchAddLabelRel(tagId, Lists.newArrayList(mtCustomerId), opUid, opName, LabelSubjectTypeEnum.CUSTOMER.getCode());
            log.info("addWmSingleCustomerSceneTag,给客户打场景标签完成,mtCustomerId={},tagId={}", mtCustomerId, tagId);
        } catch (Exception e) {
            log.error("addWmSingleCustomerSceneTag,给客户打场景标签发生异常,mtCustomerId={}", mtCustomerId, e);
        }

    }

    /**
     * 删除客户场景标签
     *
     * @param mtCustomerId 平台客户ID
     * @param opUid
     * @param opName
     *
     */
    public void deleteWmSingleCustomerSceneTag(Long mtCustomerId, Integer opUid, String opName) {
        try {
            Map<String, Integer> sceneTagMap = MccCustomerConfig.getCustomerSceneAndTagRel();
            if (MapUtils.isEmpty(sceneTagMap)) {
                return;
            }
            List<Integer> tagIds = getTagIdsByMtCustomerId(mtCustomerId, Lists.newArrayList(sceneTagMap.values()));
            if (CollectionUtils.isEmpty(tagIds)) {
                log.info("deleteWmSingleCustomerSceneTag,当前客户无个人资质场景标签，不需要删除,mtCustomerId={}", mtCustomerId);
                return;
            }
            for (Integer tagId : tagIds) {
                wmPoiFlowlineLabelThriftServiceAdapter.delCustomerLabelRel(tagId, mtCustomerId, opUid, opName);
                log.info("deleteWmSingleCustomerSceneTag,删除客户的场景标签信息操作完成");
            }
        } catch (Exception e) {
            log.error("deleteWmSingleCustomerSceneTag,删除客户场景标签发生异常,mtCustomerId={}", mtCustomerId, e);
        }
    }

    /**
     * 查询客户下是否有指定标签
     *
     * @param mtCustomerId
     * @return
     * @throws WmCustomerException
     */
    public List<Integer> getTagIdsByMtCustomerId(Long mtCustomerId, List<Integer> tagIds) throws WmCustomerException {
        List<Integer> tagIdList = new ArrayList<>();
        if (mtCustomerId == null || mtCustomerId <= 0L) {
            return tagIdList;
        }
        try {
            List<WmLabelRel> labelRelList = wmPoiFlowlineLabelThriftServiceAdapter.batchQueryLabelRel(mtCustomerId, LabelSubjectTypeEnum.CUSTOMER.getCode(), tagIds);
            if (!CollectionUtils.isEmpty(labelRelList)) {
                tagIdList = labelRelList.stream().map(WmLabelRel::getLabelId).collect(Collectors.toList());
            }
        } catch (Exception e) {
            log.error("getTagIdsByMtCustomerId,查询客户场景标签ID列表发生异常,mtCustomerId={}", mtCustomerId, e);
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "标签服务异常");
        }
        return tagIdList;
    }

    /**
     * 校验客户是否有指定标签
     *
     * @param mtCustomerId
     * @return
     * @throws WmCustomerException
     */
    public boolean checkCustomerSceneLabel(Long mtCustomerId, List<Integer> tagIds) throws WmCustomerException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.customer.WmCustomerLabelService.checkCustomerSceneLabel(java.lang.Long,java.util.List)");
        if (mtCustomerId == null || mtCustomerId <= 0L) {
            return false;
        }
        try {
            List<WmLabelRel> labelRelList = wmPoiFlowlineLabelThriftServiceAdapter.batchQueryLabelRel(mtCustomerId, LabelSubjectTypeEnum.CUSTOMER.getCode(), tagIds);
            if (!CollectionUtils.isEmpty(labelRelList)) {
                return true;
            }
        } catch (Exception e) {
            log.error("checkCustomerSceneLabel,查询客户是否有场景标签发生异常,mtCustomerId={}", mtCustomerId, e);
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "标签服务异常");

        }
        return false;
    }
}
