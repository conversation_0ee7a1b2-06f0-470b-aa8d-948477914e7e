package com.sankuai.meituan.waimai.customer.service.sign.manualpack.strategy.noticetask.partner;

import com.alibaba.fastjson.JSON;
import com.sankuai.meituan.mtcoop.thrift.dto.CoopSignBasicReqInfo;
import com.sankuai.meituan.mtcoop.thrift.dto.TSubmitCoopWithResignRequest;
import com.sankuai.meituan.mtcoop.thrift.enumtype.TCoopSource;
import com.sankuai.meituan.waimai.customer.adapter.daocan.CommonCoopServiceAdapter;
import com.sankuai.meituan.waimai.customer.contract.config.dto.WmManualSignTaskContext;
import com.sankuai.meituan.waimai.customer.contract.service.impl.WmContractService;
import com.sankuai.meituan.waimai.customer.service.sign.applytask.context.DcContractContext;
import com.sankuai.meituan.waimai.customer.service.sign.applytask.context.ManualPackNoticeContext;
import com.sankuai.meituan.waimai.customer.service.sign.manualpack.strategy.noticetask.NoticeTask;
import com.sankuai.meituan.waimai.thrift.customer.constant.config.ContractSourceEnum;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.thrift.TException;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @description:
 * @author: liuyunjie05
 * @create: 2024/8/15 11:01
 */
@Slf4j
@Service
public class DaoCanServiceC1ContractNoticeTask implements NoticeTask {

    @Resource
    private CommonCoopServiceAdapter commonCoopServiceAdapter;

    @Resource
    private WmContractService wmContractService;

    @Override
    public void notice(String module, List<Long> bizIdList, ManualPackNoticeContext context, List<Long> taskIds) throws WmCustomerException, TException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_1", "com.sankuai.meituan.waimai.customer.service.sign.manualpack.strategy.noticetask.partner.DaoCanServiceC1ContractNoticeTask.notice(String,List,ManualPackNoticeContext,List)");
        log.info("DaoCanServiceC1ContractNoticeTask#notice, taskInfo:{}, context: {}",
                JSON.toJSONString(context.getAllTaskInfo()), JSON.toJSONString(context));
        List<DcContractContext> dcContractContextList = context.getDcContractContextList();
        if (CollectionUtils.isEmpty(dcContractContextList)) {
            return;
        }
        TSubmitCoopWithResignRequest request = buildTSubmitCoopWithResignRequest(dcContractContextList, module);
        commonCoopServiceAdapter.submitCoopWithResign(request, context.getCommitUid());
        for (Long bizId : bizIdList) {
            WmManualSignTaskContext manualSignTaskContext = WmManualSignTaskContext.builder()
                    .customerId(context.getCustomerId())
                    .contractId(bizId)
                    .opUid(context.getCommitUid())
                    .opUname("")
                    .manualBatchId(context.getManualBatchId())
                    .contractSource(ContractSourceEnum.CODE.getCode())
                    .module(module)
                    .build();
            wmContractService.startDcSignByWaitingSign(manualSignTaskContext);}
    }

    private TSubmitCoopWithResignRequest buildTSubmitCoopWithResignRequest(List<DcContractContext> dcContractContextList, String contractType) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_2", "com.sankuai.meituan.waimai.customer.service.sign.manualpack.strategy.noticetask.partner.DaoCanServiceC1ContractNoticeTask.buildTSubmitCoopWithResignRequest(java.util.List,java.lang.String)");
        List<CoopSignBasicReqInfo> reqInfoList = dcContractContextList.stream()
                .filter(dcContractContext -> contractType.equals(dcContractContext.getContractType()))
                .map(this::transCoopSignBasicReqInfo)
                .collect(Collectors.toList());

        TSubmitCoopWithResignRequest request = new TSubmitCoopWithResignRequest();
        request.setCoopSignBasicReqInfoList(reqInfoList);
        request.setTCoopSource(TCoopSource.WAIMAI_XIANFU);
        return request;
    }

    private CoopSignBasicReqInfo transCoopSignBasicReqInfo(DcContractContext dcContractContext) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.sign.manualpack.strategy.noticetask.partner.DaoCanServiceC1ContractNoticeTask.transCoopSignBasicReqInfo(DcContractContext)");
        CoopSignBasicReqInfo reqInfo = new CoopSignBasicReqInfo();
        reqInfo.setCoopId(dcContractContext.getContractProof());
        reqInfo.setIsNewSign(dcContractContext.isNewSignContract());
        // 换签场景必传
        if (!dcContractContext.isNewSignContract()) {
            reqInfo.setCoopType(dcContractContext.getCoopType());
        }
        return reqInfo;
    }
}
