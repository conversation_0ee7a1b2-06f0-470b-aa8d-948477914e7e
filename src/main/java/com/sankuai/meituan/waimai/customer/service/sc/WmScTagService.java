package com.sankuai.meituan.waimai.customer.service.sc;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.meituan.common.json.JSONUtil;
import com.sankuai.meituan.waimai.customer.adapter.WmPoiFlowlineLabelThriftServiceAdapter;
import com.sankuai.meituan.waimai.customer.config.MccConfig;
import com.sankuai.meituan.waimai.customer.constant.sc.WmScGradeLabelConstant;
import com.sankuai.meituan.waimai.customer.constant.sc.WmScLableEnum;
import com.sankuai.meituan.waimai.customer.domain.sc.WmCanteenDB;
import com.sankuai.meituan.waimai.customer.domain.sc.WmScCanteenPoiAuditDetailDB;
import com.sankuai.meituan.waimai.customer.domain.sc.WmScCanteenPoiLabelDB;
import com.sankuai.meituan.waimai.customer.service.sc.dao.WmScCanteenDao;
import com.sankuai.meituan.waimai.customer.service.sc.dao.WmScCanteenPoiAuditDetailDao;
import com.sankuai.meituan.waimai.customer.service.sc.dao.WmScCanteenPoiLabelDao;
import com.sankuai.meituan.waimai.customer.util.WmScTransUtil;
import com.sankuai.meituan.waimai.label.constants.LabelSubjectTypeEnum;
import com.sankuai.meituan.waimai.label.thrift.domain.WmLabelRel;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.CanteenGradeEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.WmScCodeConstants;
import com.sankuai.meituan.waimai.thrift.exception.WmSchCantException;
import com.sankuai.meituan.waimai.thrift.exception.WmServerException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * 打标服务
 */
@Slf4j
@Service
public class WmScTagService {

    @Autowired
    private WmScCanteenPoiAuditDetailDao wmScCanteenPoiAuditDetailDao;

    @Autowired
    private WmScCanteenDao wmScCanteenDao;

    @Autowired
    private WmScCanteenPoiLabelDao wmScCanteenPoiLabelDao;

    @Autowired
    private WmPoiFlowlineLabelThriftServiceAdapter wmPoiFlowlineLabelThriftServiceAdapter;

    /**
     * 强制解绑，去除打标
     *
     * @param canteenId 食堂ID
     * @param poiId     门店ID
     */
    public void delCanteenPoiTag(Integer canteenId, Long poiId, Integer opUid, String opUname) throws WmSchCantException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.service.sc.WmScTagService.delCanteenPoiTag(java.lang.Integer,java.lang.Long,java.lang.Integer,java.lang.String)");
        try {
            List<WmScCanteenPoiLabelDB> labelDBs = wmScCanteenPoiLabelDao.getLabelsByCanteenIdAndPoiId(canteenId, poiId);
            //批量去标
            batchDeletePoiLabelsByCanteenId(labelDBs, opUid, opUname);
        } catch (WmServerException | TException e) {
            log.error("[食堂管理]调用标签系统去标异常,Method:DelPoiTag", e);
            throw new WmSchCantException(WmScCodeConstants.TAG_EXCEPTION, "调用标签系统去标异常");
        }
    }


    /**
     * 承包商等级更新，需要重新打标
     *
     * @param contractorId 承包商ID
     * @param grade 承包商供给分级
     * @param userId       uid
     * @param userName     uname
     * @throws TException         TException
     * @throws WmSchCantException WmSchCantException
     */
    public void updatePoiGradeByContractId(int contractorId, int grade, int userId, String userName) throws TException, WmSchCantException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_2", "com.sankuai.meituan.waimai.customer.service.sc.WmScTagService.updatePoiGradeByContractId(int,int,int,java.lang.String)");
        log.info("[WmScTagService.updatePoiGradeByContractId] contractorId = {}, grade = {}, userId = {}, userName = {}",
                contractorId, grade, userId, userName);
        List<WmCanteenDB> canteenDBS = wmScCanteenDao.getByContractId(contractorId);
        try {
            for (WmCanteenDB wmCanteenDB : canteenDBS) {
                List<WmScCanteenPoiLabelDB> labelList = wmScCanteenPoiLabelDao.getLabelsByCanteenIdAndLabelType(wmCanteenDB.getId(), (int) WmScLableEnum.LabelTypeEnum.B.getLabelType());
                log.info("校园食堂项目:承包商等级更新，重新打标:updatePoiGradeByContractId:labelList={},", JSONUtil.toJSONString(labelList));
                // 批量去标
                batchDeletePoiLabelsByCanteenId(labelList, userId, userName);
                // 批量打标
                batchAddPoiLabelWhenGradeChange(wmCanteenDB.getId(), userId, userName, WmScGradeLabelConstant.getLabelIdByGrade(grade));
            }
        } catch (WmServerException e) {
            log.error("[食堂管理]调用标签系统打标异常:Method:UpdatePoiGradeByContractId", e);
            throw new WmSchCantException(WmScCodeConstants.TAG_EXCEPTION, "调用标签系统打标异常");
        }
    }

    /**
     * 修改食堂打标,打新标B类，去旧标B类
     * @param wmCanteenDB 食堂
     * @param grade       食堂等级
     */
    public void editCanteenAddTag(WmCanteenDB wmCanteenDB, int grade, int userId, String userName) throws TException, WmSchCantException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.service.sc.WmScTagService.editCanteenAddTag(com.sankuai.meituan.waimai.customer.domain.sc.WmCanteenDB,int,int,java.lang.String)");
        log.info("[WmScTagService.editCanteenAddTag] input param: wmCanteenDB = {}, grade = {}, userId = {}, userName = {}",
                JSONObject.toJSONString(wmCanteenDB), grade, userId, userName);
        try {
            List<WmScCanteenPoiLabelDB> delLabelList = wmScCanteenPoiLabelDao.getLabelsByCanteenIdAndLabelType(wmCanteenDB.getId(), (int) WmScLableEnum.LabelTypeEnum.B.getLabelType());
            log.info("[WmScTagService.editCanteenAddTag] delLabelList = {},", JSONUtil.toJSONString(delLabelList));
            // 批量去B标
            batchDeletePoiLabelsByCanteenId(delLabelList, userId, userName);
            // 批量打B标
            batchAddPoiLabelWhenGradeChange(wmCanteenDB.getId(), userId, userName, WmScGradeLabelConstant.getLabelIdByGrade(grade));
        } catch (WmServerException e) {
            log.error("[食堂管理]调用标签系统打标异常:Method:EditCanteenAddTag:", e);
            throw new WmSchCantException(WmScCodeConstants.TAG_EXCEPTION, "调用标签系统打标异常");
        }
    }

    /**
     * 供给分级为空时，食堂去旧标B类
     *
     * @param wmCanteenDB 食堂
     */
    public void editCanteenDeleteTag(WmCanteenDB wmCanteenDB, int userId, String userName) throws TException, WmSchCantException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.service.sc.WmScTagService.editCanteenDeleteTag(com.sankuai.meituan.waimai.customer.domain.sc.WmCanteenDB,int,java.lang.String)");
        try {
            List<WmScCanteenPoiLabelDB> delLabelList = wmScCanteenPoiLabelDao.getLabelsByCanteenIdAndLabelType(wmCanteenDB.getId(), (int) WmScLableEnum.LabelTypeEnum.B.getLabelType());
            // 批量去B标
            batchDeletePoiLabelsByCanteenId(delLabelList, userId, userName);
        } catch (WmServerException e) {
            log.error("[食堂管理]调用标签系统打标异常:Method:EditCanteenAddTag:", e);
            throw new WmSchCantException(WmScCodeConstants.TAG_EXCEPTION, "调用标签系统打标异常");
        }
    }


    /**
     * 批量去标
     */
    private void batchDeletePoiLabelsByCanteenId(List<WmScCanteenPoiLabelDB> delLabelList, int userId, String userName) throws TException, WmServerException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_2", "com.sankuai.meituan.waimai.customer.service.sc.WmScTagService.batchDeletePoiLabelsByCanteenId(java.util.List,int,java.lang.String)");
        // 批量去标只能去相同的标
        Map<Long, List<Long>> tmp = Maps.newHashMap();//labelId -> poiIdList
        for (WmScCanteenPoiLabelDB labelDB : delLabelList) {
            List<Long> poiIdList = tmp.get(labelDB.getLabelId());
            if (poiIdList == null) poiIdList = Lists.newArrayList();
            poiIdList.add(labelDB.getWmPoiId());
            tmp.put(labelDB.getLabelId(), poiIdList);
        }

        for (Map.Entry<Long, List<Long>> entry : tmp.entrySet()) {
            // 去标
            log.info("[食堂管理]批量去标参数:标签ID={},门店ID={},userId={},userName={}", entry.getKey(), entry.getValue(),userId, userName);
            wmPoiFlowlineLabelThriftServiceAdapter.batchDeleteLabelToPois(entry.getKey(), entry.getValue(), userId, userName);
        }
        // 去记录
        wmScCanteenPoiLabelDao.batchDeleteLable(delLabelList);
    }

    /**
     * 当因食堂分级变更时的批量打标
     *
     * @param canteenId 食堂ID
     * @param labelIds  标签
     * @param userId    uid
     * @param userName  uname
     * @throws TException        TException
     * @throws WmServerException WmServerException
     */
    public void batchAddPoiLabelWhenGradeChange(Integer canteenId, int userId, String userName, Long... labelIds) throws TException, WmServerException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.service.sc.WmScTagService.batchAddPoiLabelWhenGradeChange(java.lang.Integer,int,java.lang.String,java.lang.Long[])");
        log.info("[WmScTagService.batchAddPoiLabelWhenGradeChange] canteenId = {}, userId = {}, userName = {}, labelIds = {}",
                canteenId, userId, userName, labelIds);
        // 查询食堂下所有门店列表
        List<Long> poiIdList = wmScCanteenPoiAuditDetailDao.getEffectPoiIdListByCanteenId(canteenId);
        log.info("[食堂管理]批量打标batchAddPoiLabelWhenGradeChange:poiIdList={}", JSONUtil.toJSONString(poiIdList) );
        List<WmScCanteenPoiLabelDB> result = Lists.newArrayList();
        for (Long labelId : labelIds) {
            log.info("[食堂管理]批量打标参数:标签ID={},门店ID={}", labelId, poiIdList);
            wmPoiFlowlineLabelThriftServiceAdapter.batchAddLabelToPois(labelId, poiIdList, userId, userName);
            result.addAll(WmScTransUtil.toPoiLabelList(canteenId, poiIdList, labelId));
        }
        wmScCanteenPoiLabelDao.batchSave(result);
    }

    /**
     * 刷新食堂门店标签
     * @param canteenId 食堂物理ID
     * @param userId    操作人ID
     * @param userName  操作人名字
     * @return 需要重新绑定标签的门店ID
     * @throws TException org.apache.thrift.TException
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    public List<Long> refreshCanteenPoiTag(Integer canteenId, int userId, String userName) throws TException, WmSchCantException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_2", "com.sankuai.meituan.waimai.customer.service.sc.WmScTagService.refreshCanteenPoiTag(java.lang.Integer,int,java.lang.String)");
        log.info("[WmScTagService.refreshCanteenPoiTag] input param: canteenId = {}, userId = {}, userName = {}", canteenId, userId, userName);
        try {
            if (canteenId == null || canteenId <= 0) {
                return Lists.newArrayList();
            }
            WmCanteenDB wmCanteenDB = wmScCanteenDao.getById(canteenId);
            if (wmCanteenDB == null) {
                return Lists.newArrayList();
            }
            List<Long> labelIds = new ArrayList<>();
            // A类标签
            labelIds.add(WmScGradeLabelConstant.getLabelA());
            // B类标签
            if (CanteenGradeEnum.getByType(wmCanteenDB.getGrade()) != null && WmScGradeLabelConstant.getLabelIdByGrade(wmCanteenDB.getGrade()) != null) {
                labelIds.add(WmScGradeLabelConstant.getLabelIdByGrade(wmCanteenDB.getGrade()));
            }
            // 查询食堂下生效的门店ID
            List<Long> poiIdList = wmScCanteenPoiAuditDetailDao.getEffectPoiIdListByCanteenId(canteenId);
            if (CollectionUtils.isEmpty(poiIdList)) {
                return Lists.newArrayList();
            }

            // 生效门店中去掉已有打标正确的门店
            poiIdList.removeIf(id -> hasTag(id, labelIds));
            if (CollectionUtils.isEmpty(poiIdList)) {
                return Lists.newArrayList();
            }

            log.info("[WmScTagService.refreshCanteenPoiTag] canteenId = {}, 需要重新绑定标签的门店ID::poiIdList={}, labelIds = {}",
                    canteenId, JSONUtil.toJSONString(poiIdList), JSONObject.toJSONString(labelIds));
            batchAddPoiLabelRemoteAndLocal(canteenId, poiIdList, userId, userName, labelIds);
            return poiIdList;
        } catch (WmServerException e) {
            log.error("refreshCanteenPoiTag::canteenId = {}, userId = {}, userName = {}", canteenId, userId, userName, e);
            throw new WmSchCantException(WmScCodeConstants.TAG_EXCEPTION, "重新打标异常");
        }
    }

    /**
     * 判断门店是否有绑定指定的全部标签
     * @param wmPoiId 门店ID
     * @param labelIds 标签ID列表
     * @return true: 绑定指定的全部标签 / false: 没有绑定指定的全部标签
     */
    private boolean hasTag(Long wmPoiId, List<Long> labelIds) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.service.sc.WmScTagService.hasTag(java.lang.Long,java.util.List)");
        if (wmPoiId == null || CollectionUtils.isEmpty(labelIds)) {
            return false;
        }
        boolean flag = true;
        for (Long labelId : labelIds) {
            if (!hasTag(wmPoiId, labelId)) {
                flag = false;
            }
        }
        return flag;
    }

    /**
     * 判断门店是否有标签
     * @param wmPoiId
     * @param labelId
     * @return
     * @throws TException
     * @throws WmServerException
     */
    public boolean hasTag(Long wmPoiId, Long labelId) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_2", "com.sankuai.meituan.waimai.customer.service.sc.WmScTagService.hasTag(java.lang.Long,java.lang.Long)");
        if (wmPoiId == null || labelId == null) {
            return false;
        }

        List<WmLabelRel> list = wmPoiFlowlineLabelThriftServiceAdapter.selectLabelRel(wmPoiId, LabelSubjectTypeEnum.POI.getCode(), labelId.intValue());
        if (CollectionUtils.isNotEmpty(list)) {
            return true;
        }
        return false;
    }

    /**
     * 批量食堂门店打标(远程+本地)
     *
     * @param canteenId 食堂物理ID
     * @param labelIds  标签ID
     * @param userId    操作人ID
     * @param userName  操作人名字
     * @throws TException        TException
     * @throws WmServerException WmServerException
     */
    private void batchAddPoiLabelRemoteAndLocal(Integer canteenId, List<Long> poiIdList, int userId, String userName, List<Long> labelIds)
            throws TException, WmServerException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.sc.WmScTagService.batchAddPoiLabelRemoteAndLocal(java.lang.Integer,java.util.List,int,java.lang.String,java.util.List)");
        if (CollectionUtils.isEmpty(poiIdList) || canteenId == null || CollectionUtils.isEmpty(labelIds)) {
            return;
        }
        log.info("[WmScTagService.batchAddPoiLabelRemoteAndLocal] canteenId = {}, poiIdList = {}, userId = {}, userName = {}, labelIds = {}",
                canteenId, JSONObject.toJSONString(poiIdList), userId, userName, JSONObject.toJSONString(labelIds));
        List<WmScCanteenPoiLabelDB> result = Lists.newArrayList();
        // 标签系统门店打标(远程)
        for (Long labelId : labelIds) {
            wmPoiFlowlineLabelThriftServiceAdapter.batchAddLabelToPois(labelId, poiIdList, userId, userName);
            result.addAll(WmScTransUtil.toPoiLabelList(canteenId, poiIdList, labelId));
        }
        // 更新到食堂门店标签表(本地)
        insertCanteenPoiLocal(canteenId, result);
    }

    /**
     * 维护本地食堂门店标签关系
     *
     * @param canteenId           食堂物理ID
     * @param insertRelationLocal 新增的本地关系记录
     */
    private void insertCanteenPoiLocal(Integer canteenId, List<WmScCanteenPoiLabelDB> insertRelationLocal) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.sc.WmScTagService.insertCanteenPoiLocal(java.lang.Integer,java.util.List)");
        if (CollectionUtils.isEmpty(insertRelationLocal) || canteenId == null) {
            return;
        }
        log.info("[WmScTagService.insertCanteenPoiLocal] canteenId = {}, insertRelationLocal = {}", canteenId, JSONObject.toJSONString(insertRelationLocal));
        // 查询本地已经存在的记录
        List<WmScCanteenPoiLabelDB> labelDBs = wmScCanteenPoiLabelDao.getLabelsByCanteenId(canteenId);
        // 如果本地记录已经存在则移除后再存储新增的记录
        if (CollectionUtils.isNotEmpty(labelDBs)) {
            Iterator<WmScCanteenPoiLabelDB> it = insertRelationLocal.iterator();
            while (it.hasNext()) {
                WmScCanteenPoiLabelDB one = it.next();
                for (WmScCanteenPoiLabelDB wmScCanteenPoiLabelDB : labelDBs) {
                    if (one.getWmPoiId().equals(wmScCanteenPoiLabelDB.getWmPoiId())
                            && one.getLabelId().equals(wmScCanteenPoiLabelDB.getLabelId())) {
                        it.remove();
                    }
                }
            }
        }
        if (CollectionUtils.isNotEmpty(insertRelationLocal)) {
            // 存标签（本地记录）
            wmScCanteenPoiLabelDao.batchSave(insertRelationLocal);
        }
    }
}
