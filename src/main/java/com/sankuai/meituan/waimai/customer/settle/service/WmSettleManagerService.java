package com.sankuai.meituan.waimai.customer.settle.service;

import cn.hutool.core.collection.CollectionUtil;
import com.dianping.cat.Cat;
import com.meituan.pay.mwallet.thrift.req.SettleInfoBatchQueryReq;
import com.meituan.pay.mwallet.thrift.resp.SettleInfoListRes;
import com.meituan.pay.mwallet.thrift.resp.data.SettleInfoData;
import com.sankuai.meituan.waimai.customer.adapter.MerchantSettleQueryProxyServiceAdaptor;
import com.sankuai.meituan.waimai.customer.config.MccConfig;
import com.sankuai.meituan.waimai.customer.config.MccCustomerConfig;
import com.sankuai.meituan.waimai.customer.service.customer.Impl.AbstractWmCustomerRealService;
import com.sankuai.meituan.waimai.customer.settle.daoadapter.WmSettleAuditedDBMapperAdapter;
import com.sankuai.meituan.waimai.heron.settle.constants.CBizTypeEnum;

import java.util.*;
import java.util.Map.Entry;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

import javax.annotation.Nullable;
import javax.annotation.Resource;

import com.sankuai.meituan.waimai.infra.constants.WmOrgConstant;
import com.sankuai.meituan.waimai.infra.constants.WmVirtualOrgRecursiveTypeEnum;
import com.sankuai.meituan.waimai.infra.service.WmVirtualOrgService;
import com.sankuai.meituan.waimai.mtauth.util.WmEmployUtils;
import com.sankuai.meituan.waimai.thrift.constants.*;
import com.sankuai.meituan.waimai.thrift.exception.WmServerException;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections.Predicate;
import org.apache.commons.lang3.StringUtils;
import org.apache.thrift.TException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.google.common.base.Function;
import com.google.common.base.MoreObjects;
import com.google.common.collect.ArrayListMultimap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Multimap;
import com.google.common.collect.Sets;
import com.sankuai.meituan.util.ConfigUtilAdapter;
import com.sankuai.meituan.waimai.customer.adapter.WmPoiClient;
import com.sankuai.meituan.waimai.customer.bo.WmPoiDomain;
import com.sankuai.meituan.waimai.customer.contract.dao.WmContractVersionDBMapper;
import com.sankuai.meituan.waimai.customer.contract.domain.WmContractVersionDB;
import com.sankuai.meituan.waimai.customer.contract.service.impl.WmContractVersionService;
import com.sankuai.meituan.waimai.customer.domain.BatchPdfInfo;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerDB;
import com.sankuai.meituan.waimai.customer.mq.service.WmCustomerUpdateSendService;
import com.sankuai.meituan.waimai.customer.service.common.ProduceNotifyService;
import com.sankuai.meituan.waimai.customer.service.common.SettleProduceNotifyService;
import com.sankuai.meituan.waimai.customer.service.common.WmEmployeeService;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerPoiService;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerService;
import com.sankuai.meituan.waimai.customer.service.sign.WmEcontractSignBzService;
import com.sankuai.meituan.waimai.customer.settle.dao.WmPoiSettleAuditedDBMapper;
import com.sankuai.meituan.waimai.customer.settle.dao.WmPoiSettleDBMapper;
import com.sankuai.meituan.waimai.customer.settle.dao.WmSettleAuditedDBMapper;
import com.sankuai.meituan.waimai.customer.settle.dao.WmSettleDBMapper;
import com.sankuai.meituan.waimai.customer.settle.ddd.common.atom.service.WmSettleCommonAtomService;
import com.sankuai.meituan.waimai.customer.settle.ddd.domain.effect.dservice.WmSettleEffectDomainService;
import com.sankuai.meituan.waimai.customer.settle.domain.WmPoiSettleAuditedDB;
import com.sankuai.meituan.waimai.customer.settle.domain.WmPoiSettleDB;
import com.sankuai.meituan.waimai.customer.settle.domain.WmSettleAuditedDB;
import com.sankuai.meituan.waimai.customer.settle.domain.WmSettleDB;
import com.sankuai.meituan.waimai.customer.settle.grey.WmHeronSettleGrayService;
import com.sankuai.meituan.waimai.customer.settle.grey.WmSettleGreyService;
import com.sankuai.meituan.waimai.customer.settle.message.WmSettleApproveModifyMsgToSettleService;
import com.sankuai.meituan.waimai.customer.settle.message.WmSettleComplianceNotice;
import com.sankuai.meituan.waimai.customer.settle.message.WmSettleMsgSender;
import com.sankuai.meituan.waimai.customer.settle.message.diff.bean.WmPoiSettleDBDiff;
import com.sankuai.meituan.waimai.customer.settle.message.diff.bean.WmSettleDBDiff;
import com.sankuai.meituan.waimai.customer.settle.service.notice.BusinessLoansNoticeService;
import com.sankuai.meituan.waimai.customer.settle.service.paycenter.WmBankCardValidationService;
import com.sankuai.meituan.waimai.customer.settle.service.wallet.WmSettleWalletService;
import com.sankuai.meituan.waimai.customer.util.PageUtil;
import com.sankuai.meituan.waimai.customer.util.SetUtil;
import com.sankuai.meituan.waimai.customer.util.base.AssertUtil;
import com.sankuai.meituan.waimai.customer.util.diff.BeanDiffUtil;
import com.sankuai.meituan.waimai.customer.util.trans.PdfUrlTransUtils;
import com.sankuai.meituan.waimai.customer.util.trans.WmPoiSettleTransUtil;
import com.sankuai.meituan.waimai.customer.util.trans.WmSettleTransUtil;
import com.sankuai.meituan.waimai.customer.util.trans.WmTempletContractTransUtil;
import com.sankuai.meituan.waimai.heron.settle.constants.CChannelTypeEnum;
import com.sankuai.meituan.waimai.heron.settle.dto.base.BaseOperateDataInputDTO;
import com.sankuai.meituan.waimai.heron.settle.exception.WmHeronSettleException;
import com.sankuai.meituan.waimai.heron.settle.service.WmSettleExportThriftService;
import com.sankuai.meituan.waimai.oplog.thrift.domain.WmPoiOplog;
import com.sankuai.meituan.waimai.oplog.thrift.service.WmPoiOplogThriftService;
import com.sankuai.meituan.waimai.poi.domain.WmPoiOpLog;
import com.sankuai.meituan.waimai.settle.WmSettleMsgBo;
import com.sankuai.meituan.waimai.thrift.constant.WmContractConstant;
import com.sankuai.meituan.waimai.thrift.constant.WmContractErrorCodeConstant;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerConstants;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerContractConstant;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractTaskApplyTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.EcontractTaskStateEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.WmSettleConstant;
import com.sankuai.meituan.waimai.thrift.customer.domain.PageData;
import com.sankuai.meituan.waimai.thrift.customer.domain.WmCustomerOplogBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.base.BooleanResult;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.ContractVersionPageData;
import com.sankuai.meituan.waimai.thrift.customer.domain.contract.WmCustomerContractVersionBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.diff.ChangedEvent;
import com.sankuai.meituan.waimai.thrift.customer.domain.diff.CustomerUpdateMessage;
import com.sankuai.meituan.waimai.thrift.customer.domain.diff.DiffValue;
import com.sankuai.meituan.waimai.thrift.customer.domain.diff.WmCustomerHistVersionConstant;
import com.sankuai.meituan.waimai.thrift.customer.domain.settle.WmSettle;
import com.sankuai.meituan.waimai.thrift.customer.domain.settle.WmSettleAudited;
import com.sankuai.meituan.waimai.thrift.customer.domain.settle.WmSettlePoiWalletBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.settle.WmSettleWalletPreauthResultBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractCallbackBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.EcontractCustomerKPBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sign.RetrySmsResponse;
import com.sankuai.meituan.waimai.thrift.customer.service.WmEcontractSignThriftService;
import com.sankuai.meituan.waimai.thrift.dto.PublishNoticeDto;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import com.sankuai.meituan.waimai.thrift.service.WmNoticePublishThriftService;
import com.sankuai.meituan.waimai.util.DateUtil;
import com.sankuai.meituan.waimai.wallet.WalletContext;
import com.sankuai.meituan.waimai.wallet.WmWalletConstant;

@Service
public class WmSettleManagerService {

    private static final Logger LOGGER = LoggerFactory.getLogger(WmSettleManagerService.class);


    private final static ExecutorService handleService =  new ThreadPoolExecutor(10, 200,
            0L, TimeUnit.MILLISECONDS,
            new LinkedBlockingQueue(1024), new ThreadPoolExecutor.AbortPolicy());

    @Autowired
    private WmSettleService wmSettleService;
    @Autowired
    private WmSettleWalletService wmSettleWalletService;
    @Autowired
    private WmSettleApproveModifyMsgToSettleService wmSettleApproveModifyMsgToSettleService;
    @Autowired
    private WmEcontractSignThriftService wmEcontractSignThriftService;
    @Autowired
    private WmContractVersionDBMapper wmContractVersionDBMapper;

    @Autowired
    private WmSettleDBMapper wmSettleDBMapper;
    @Autowired
    private WmSettleAuditedDBMapper wmSettleAuditedDBMapper;
    @Autowired
    private WmSettleAuditedDBMapperAdapter wmSettleAuditedDBMapperAdapter;
    @Autowired
    private WmPoiSettleDBMapper wmPoiSettleDBMapper;
    @Autowired
    private WmPoiSettleAuditedDBMapper wmPoiSettleAuditedDBMapper;

    @Autowired
    private WmSettleLogService wmSettleLogService;
    @Autowired
    private WmContractVersionService wmContractVersionService;
    @Autowired
    private WmBankCardValidationService wmBankCardValidationService;
    @Autowired
    private SettleProduceNotifyService settleProduceNotifyService;
    @Autowired
    private WmPoiClient wmPoiClient;
    @Autowired
    private WmCustomerUpdateSendService wmCustomerUpdateSendService;
    @Autowired
    private WmPoiOplogThriftService.Iface wmPoiOplogThriftService;
    @Autowired
    WmCustomerService wmCustomerService;
    @Autowired
    WmEmployeeService wmEmployeeService;
    @Autowired
    private WmCustomerPoiService wmCustomerPoiService;
    @Resource
    WmSettleComplianceNotice wmSettleComplianceNotice;
    @Autowired
    WmNoticePublishThriftService.Iface wmNoticePublishThriftService;

    @Autowired
    WmEcontractSignBzService wmEcontractSignBzService;
    @Autowired
    private WmSettleGreyService wmSettleGreyService;
    @Resource(name = "wmSettleEffectDomainServiceImpl")
    private WmSettleEffectDomainService wmSettleEffectDomainService;
    @Autowired
    WmSettleCommonAtomService wmSettleCommonAtomService;
    @Autowired
    private WmHeronSettleGrayService wmHeronSettleGrayService;
    @Autowired
    private WmSettleExportThriftService wmSettleExportThriftService;
    @Autowired
    private BusinessLoansNoticeService businessLoansNoticeService;
    @Autowired
    private WmVirtualOrgService.Iface wmVirtualOrgService;

    @Autowired
    private MerchantSettleQueryProxyServiceAdaptor merchantSettleQueryProxyServiceAdaptor;

    private static final String REAL_NAME_SUCCESS = "【钱包操作】钱包实名：成功      结算ID：%d\n" +
          "  钱包ID：%d;   钱包账户：%s";
    private static final String REAL_NAME_FAILED = "【钱包操作】钱包实名：失败      结算ID：%d\n" +
          "  钱包ID：%d;   钱包账户：%s\n" +
          "\n" +
          "  失败原因：%s";

    private static final String BIND_CARD_SUCCESS = "【钱包操作】钱包绑卡：成功      结算ID：%d\n" +
          "  钱包ID：%d;   钱包账户：%s";
    private static final String BIND_CARD_FAILED = "【钱包操作】钱包绑卡：失败      结算ID：%d\n" +
          "  钱包ID：%d;   钱包账户：%s\n" +
          "\n" +
          "  失败原因：%s";

    private static final String WALLET_PREAUTH_SUCCESS = "【钱包操作】钱包预认证：成功，结算ID：%d\n";

    private static final String WALLET_PREAUTH_FAILED = "【钱包操作】钱包预认证：失败，结算ID：%d\n"
            +"失败原因：[%s]";

    private static final int WM_POI_ID_SIZE = 5;

    /**
   * 客户id-写-设置关联结算生效
   */
  public BooleanResult setupEffect(int wmCustomerId, int opUid, String opUname)
      throws WmCustomerException {
    AssertUtil.assertIntegerMoreThan0(wmCustomerId, "客户id");

    if(wmSettleGreyService.routeDDDGrey(wmCustomerId,opUid)){
        return wmSettleEffectDomainService.setupEffect(wmCustomerId,opUid,opUname);
    }

    List<WmSettleAudited> oldWmSettleAuditedList = wmSettleService
        .getWmSettleAuditedByWmCustomerId(wmCustomerId,false);
    List<WmSettle> newWmSettleList = wmSettleService.getWmSettleByWmCustomerId(wmCustomerId,false);
    List<WmSettleAudited> newWmSettleAuditedList = WmSettleTransUtil
        .transWmSettleList2WmSettleAuditedList(newWmSettleList);
    //1.线下表拷贝到线上表
    List<ChangedEvent> poi = Lists.newArrayList();
    List<ChangedEvent> settle = Lists.newArrayList();
    Set<Integer> deletePoiSet = Sets.newHashSet();

        List<WmSettleDB> wmSettleByCustomerIdMaster = wmSettleDBMapper
                .getWmSettleByCustomerIdMaster(wmCustomerId);
        if (CollectionUtils.isNotEmpty(wmSettleByCustomerIdMaster)) {
            byte status = wmSettleByCustomerIdMaster.get(0).getStatus();
            if (!canSetupEffect(status)) {
                throw new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR, "当前状态不允许生效");
            }
        }
        wmSettleService.saveOrUpdateWmSettleProtocolAudited(wmCustomerId);
        wmSettleService.updateStatusByWmCustomerId(wmCustomerId, WmSettleConstant.SETTLE_STATUS_EFFECT);

        effectPoiSettle(wmCustomerId, newWmSettleList, oldWmSettleAuditedList, poi,
                deletePoiSet);
        effectSettle(wmCustomerId, newWmSettleList, oldWmSettleAuditedList, settle);

        wmSettleLogService.insertWmSettleLog(wmCustomerId,
                WmCustomerOplogBo.OpType.CHANGESTATUS,
                0,
                "系统",
                "结算生效"
        );

        //非开通钱包的结算需要触发支付合规，开通钱包的结算需要等待开通钱包之后进行支付合规
        List<WmSettle> bankSettleList = Lists.newArrayList();
        for (WmSettle wmSettle : MoreObjects.firstNonNull(newWmSettleList, Lists.<WmSettle>newArrayList())) {
            if (wmSettle.getCard_type() == WmWalletConstant.CardType.BANK.getIndex()) {
                bankSettleList.add(wmSettle);
            }
        }

        //2.支付合规
        wmSettleComplianceNotice.noticeBankSettleCompliance(wmCustomerId, bankSettleList, opUid, opUname);

        //3.钱包处理
        WalletContext walletContext = new WalletContext(wmCustomerId, oldWmSettleAuditedList,
                newWmSettleAuditedList, opUid, opUname);
        wmSettleWalletService.processWallet(walletContext);
        //4.消息通知
        wmSettleApproveModifyMsgToSettleService
                .publish(wmCustomerId, newWmSettleList, oldWmSettleAuditedList);


        //5.记录produce log
        insertSetupProduceLog(
                wmSettleService.getWmPoiIdListFromWmSettleList(newWmSettleList), wmSettleService
                        .getWmPoiIdListFromWmSettleAuditedList(oldWmSettleAuditedList), opUid, opUname);

        //处理下线门店
        checkAndOfflinePoi(wmCustomerId,deletePoiSet, opUid, opUname);

        wmCustomerUpdateSendService.sendSettleMsg(genDiffMessage(wmCustomerId, settle, opUid, opUname,
                WmCustomerHistVersionConstant.VersionMessageBizTypeEnum.CUTOMER_SETTLE_EFFECT.getIndex()));

        return new BooleanResult(true);
    }

    private void checkAndOfflinePoi(int wmCustomerId,Set<Integer> deletePoiSet, int opUid, String opUname) {
        if (deletePoiSet.size() == 0) {
            return;
        }
        List<Long> customerRelPoiList = wmCustomerPoiService.selectWmPoiIdsByCustomerIdFromDBAndSwitchCentre(wmCustomerId);
        Set<Long> customerRelPoiSet = Sets.newHashSet(customerRelPoiList);

        List<Long> search = Lists.newArrayList();
        for (Integer temp : deletePoiSet) {
            search.add(temp.longValue());
        }
        List<Long> hasOfflinePoiList = wmPoiSettleAuditedDBMapper.batchGetEffectiveWmPoiIdList(search);
        search.removeAll(hasOfflinePoiList);
        for (Long temp : search) {
            //待下线门店不在客户关联的门店中-兼容历史切换不下线数据-跳过下线逻辑
            if(!customerRelPoiSet.contains(temp)){
                continue;
            }
            wmPoiClient.offlinePoi(temp.intValue(), "结算删除门店,导致门店下线", opUid, opUname,
                    100);
        }
    }

    private void insertCancelProduceLog(List<Integer> wmPoiIdList, int opUid, String opUname) {
        if (CollectionUtils.isNotEmpty(wmPoiIdList)) {
            for (Integer wmPoiId : wmPoiIdList) {
                settleProduceNotifyService
                        .notifyCancelWithRemark(wmPoiId, ProduceNotifyService.SETTLEMENT, opUid, opUname,
                                "");
            }
        }
    }

    public void insertCancelProduceLogAsy(List<Integer> wmPoiIdList, int opUid, String opUname) {
        handleService.execute(new Runnable() {
            @Override
            public void run() {
                insertCancelProduceLog(wmPoiIdList, opUid, opUname);
            }
        });
    }

    public void insertSetupProduceLog(List<Integer> wmPoiIdList,
                                       List<Integer> wmPoiIdAuditedList, int opUid, String opUname) {
        for (Integer wmPoiId : wmPoiIdList) {
            settleProduceNotifyService
                    .notifyEffectWithRemark(wmPoiId, ProduceNotifyService.SETTLEMENT, opUid, opUname,
                            "结算商家已签约");
        }

        List<Integer> deleteList = BeanDiffUtil.genDeleteList(wmPoiIdList, wmPoiIdAuditedList);
        for (Integer delete : deleteList) {
            settleProduceNotifyService
                    .notifyDeleteWithRemark(delete, ProduceNotifyService.SETTLEMENT, opUid, opUname, "");
        }
    }

    public void insertSetupProduceLogAsy(List<Integer> wmPoiIdList,
            List<Integer> wmPoiIdAuditedList, int opUid, String opUname){
        handleService.execute(new Runnable() {
            @Override
            public void run() {
                insertSetupProduceLog(wmPoiIdList,wmPoiIdAuditedList,opUid,opUname);
            }
        });
    }


    public boolean effectSettle(int wmCustomerId, List<WmSettle> newWmSettleList,
                                List<WmSettleAudited> oldWmSettleAuditedList, List<ChangedEvent> changedEventList)
            throws WmCustomerException {
        LOGGER.info("effectSettle wmCustomerId = {}", wmCustomerId);
        // 拷贝结算线下表到线上表
        List<WmSettleDB> wmSettleList = WmSettleTransUtil.wmSettleThriftList2DBList(newWmSettleList);
        List<WmSettleAuditedDB> wmSettleAuditedList = WmSettleTransUtil
                .wmSettleAuditedThriftList2DBList(oldWmSettleAuditedList);

        if (!insertWmSettle(wmCustomerId, wmSettleList, wmSettleAuditedList, changedEventList)) {
            return false;
        } else if (!updateWmSettle(wmCustomerId, wmSettleList, wmSettleAuditedList, changedEventList)) {
            return false;
        } else if (!deleteWmSettle(wmCustomerId, wmSettleList, wmSettleAuditedList, changedEventList)) {
            return false;
        }
        return true;
    }

    private boolean insertWmSettle(Integer wmCustomerId, List<WmSettleDB> wmSettleList,
                                   List<WmSettleAuditedDB> wmSettleAuditedList, List<ChangedEvent> changedEventList) {
        Set<Integer> wmSettleIdSet = WmSettleTransUtil.transWmSettleList2WmSettleIdSet(wmSettleList);
        Set<Integer> wmSettleAuditedIdSet = WmSettleTransUtil
                .transWmSettleAudited2WmSettleIdSet(wmSettleAuditedList);
        Set<Integer> addSet = SetUtil.genAddSet(wmSettleIdSet, wmSettleAuditedIdSet);

        LOGGER.info("insertWmSettle 新增线上结算 wmCustomerId={},addList = {}", wmCustomerId,
                JSON.toJSONString(addSet));
        Map<Integer, WmSettleDB> map = WmSettleTransUtil.transWmSettleDBList2Map(wmSettleList);
        for (Integer add : addSet) {
            WmSettleAuditedDB wmSettleAuditedDB = WmSettleTransUtil.transWmSettleDB(map.get(add));
            wmSettleAuditedDBMapperAdapter.insertSelective(wmSettleAuditedDB);
            diffSettle(changedEventList, wmSettleAuditedDB, new WmSettleAuditedDB(), ChangedEvent.CREATE);
        }
        return true;
    }

    private void diffSettle(List<ChangedEvent> changedEventList, WmSettleAuditedDB newDB,
                            WmSettleAuditedDB oldDB, String type) {
        WmSettleDBDiff newValue = WmSettleTransUtil.transWmSettleDB2DiffBean(newDB);
        WmSettleDBDiff oldValue = WmSettleTransUtil.transWmSettleDB2DiffBean(oldDB);
        Map<String, DiffValue> diffValues = BeanDiffUtil.diffValueMap(newValue, oldValue);
        if (MapUtils.isNotEmpty(diffValues)) {
            changedEventList.add(new ChangedEvent(newDB.getWm_settle_id(), type, diffValues));
        }
    }

    private boolean updateWmSettle(Integer wmCustomerId, List<WmSettleDB> wmSettleList,
                                   List<WmSettleAuditedDB> wmSettleAuditedList, List<ChangedEvent> changedEventList) {
        Set<Integer> wmSettleIdSet = WmSettleTransUtil.transWmSettleList2WmSettleIdSet(wmSettleList);
        Set<Integer> wmSettleAuditedIdSet = WmSettleTransUtil
                .transWmSettleAudited2WmSettleIdSet(wmSettleAuditedList);
        Set<Integer> commonSet = SetUtil.genCommonSet(wmSettleIdSet, wmSettleAuditedIdSet);

        LOGGER.info("updateWmSettle 修改线上结算,wmCustomerId={},commonSet={}", wmCustomerId,
                JSON.toJSONString(commonSet));
        Map<Integer, WmSettleDB> mapNew = WmSettleTransUtil.transWmSettleDBList2Map(wmSettleList);
        Map<Integer, WmSettleAuditedDB> mapOld = WmSettleTransUtil
                .transWmSettleAuditedDBList2Map(wmSettleAuditedList);
        for (Integer common : commonSet) {
            WmSettleAuditedDB wmSettleAuditedDB = WmSettleTransUtil.transWmSettleDB(mapNew.get(common));
            wmSettleAuditedDB.setWm_wallet_id(mapOld.get(common).getWm_wallet_id());
            wmSettleAuditedDBMapperAdapter.updateByWmSettleId(wmSettleAuditedDB);
            diffSettle(changedEventList, wmSettleAuditedDB,
                    mapOld.get(wmSettleAuditedDB.getWm_settle_id()), ChangedEvent.UPDATE);
        }

        return true;
    }

    public boolean deleteWmSettle(Integer wmCustomerId, List<WmSettleDB> wmSettleList,
                                  List<WmSettleAuditedDB> wmSettleAuditedList, List<ChangedEvent> changedEventList) {
        Set<Integer> wmSettleIdSet = WmSettleTransUtil.transWmSettleList2WmSettleIdSet(wmSettleList);
        Set<Integer> wmSettleAuditedIdSet = WmSettleTransUtil
                .transWmSettleAudited2WmSettleIdSet(wmSettleAuditedList);
        Set<Integer> deleteSet = SetUtil.gendeleteSet(wmSettleIdSet, wmSettleAuditedIdSet);

        LOGGER.info("deleteWmSettle 删除线上结算,wmCustomerId={},deleteSet = {}", wmCustomerId,
                JSON.toJSONString(deleteSet));
        if (CollectionUtils.isEmpty(deleteSet)) {
            return true;
        }
        try {
            String wmSettleIds = StringUtils.join(deleteSet, ",");
            wmSettleAuditedDBMapper.deleteByWmContractIdAndWmSettleId(wmCustomerId, wmSettleIds);
            for (Integer id : deleteSet) {
                changedEventList.add(new ChangedEvent(id, ChangedEvent.DELETE, null));
            }
        } catch (Exception e) {
            LOGGER.error("更新线上结算失败, wmCustomerId = {}", wmCustomerId, e);
            return false;
        }
        return true;
    }

    private boolean effectPoiSettle(int wmCustomerId, List<WmSettle> newWmSettleList,
                                    List<WmSettleAudited> oldWmSettleAuditedList, List<ChangedEvent> changedEventList,
                                    Set<Integer> deletePoiSet) {
        LOGGER.info("effectPoiSettle wmCustomerId = {}", wmCustomerId);
        List<WmPoiSettleDB> wmPoiSettleDBList = WmSettleTransUtil.extractWmPoiSettleDB(newWmSettleList);
        List<WmPoiSettleAuditedDB> wmPoiSettleAuditedDBList = WmSettleTransUtil
                .extractWmPoiSettleAuditedDB(oldWmSettleAuditedList);

        // diff mq
        Map<Integer, ChangedEvent> changedEventMap = Maps.newHashMap();
        for (ChangedEvent changedEvent : changedEventList) {
            changedEventMap.put(changedEvent.getId(), changedEvent);
        }

        saveWmSettlePoiWalletHistoryRel(wmPoiSettleAuditedDBList);
        if (!insertPoiSettle(wmCustomerId, wmPoiSettleDBList, wmPoiSettleAuditedDBList, changedEventMap,
                changedEventList)) {
            return false;
        } else if (!updatePoiSettle(wmCustomerId, wmPoiSettleDBList, wmPoiSettleAuditedDBList,
                changedEventMap, changedEventList)) {
            return false;
        } else if (!deletePoiSettle(wmCustomerId, wmPoiSettleDBList, wmPoiSettleAuditedDBList,
                changedEventMap, changedEventList, deletePoiSet)) {
            return false;
        }
        return true;
    }

    private boolean updatePoiSettle(int wmCustomerId, List<WmPoiSettleDB> wmPoiSettleList,
                                    List<WmPoiSettleAuditedDB> wmPoiSettleAuditedList,
                                    Map<Integer, ChangedEvent> changedEventMap, List<ChangedEvent> changedEventList) {
        //求增加的关联门店
        Set<Integer> wmPoiIdSet = WmPoiSettleTransUtil.transWmPoiSettleList2WmPoiIdSet(wmPoiSettleList);
        Set<Integer> wmPoiIdAuditedSet = WmPoiSettleTransUtil
                .transWmPoiSettleAuditedList2WmPoiIdSet(wmPoiSettleAuditedList);
        Set<Integer> commonSet = SetUtil
                .genCommonSet(wmPoiIdSet, wmPoiIdAuditedSet);//wmPoiIdAuditedSet比wmPoiIdSet多的门店

        //得出对应的关联关系WmPoiSettle
        LOGGER.info("WmUnaudited2Audited 更新结算门店关联关系,wmCustomerId={},commonSet={}", wmCustomerId,
                JSON.toJSONString(commonSet));
        Map<Integer, WmPoiSettleDB> mapNew = WmPoiSettleTransUtil
                .transWmPoiSettleList2Map(wmPoiSettleList);
        Map<Integer, WmPoiSettleAuditedDB> mapOld = WmPoiSettleTransUtil
                .transWmPoiSettleAuditedList2Map(wmPoiSettleAuditedList);
        Map<String, DiffValue> diffValueMap;
        for (Integer common : commonSet) {
            WmPoiSettleAuditedDB wmPoiSettleAudited = WmPoiSettleTransUtil
                    .transWmPoiSettleDB(mapNew.get(common));
            // 此处有一小坑：在WmPoiSettle中新增有效信息字段时，必须做diff，不然可能拷贝不到线上表
            diffValueMap = diffPoiSettle(changedEventMap, changedEventList, wmPoiSettleAudited,
                    mapOld.get(common));
            if (MapUtils.isEmpty(diffValueMap)) {
                continue;
            }
            wmPoiSettleAuditedDBMapper.updateByWmContractIdAndWmPoiId(wmPoiSettleAudited);
        }

        return true;
    }

    private boolean deletePoiSettle(int wmContractId, List<WmPoiSettleDB> wmPoiSettleList,
                                    List<WmPoiSettleAuditedDB> wmPoiSettleAuditedList,
                                    Map<Integer, ChangedEvent> changedEventMap, List<ChangedEvent> changedEventList,
                                    Set<Integer> deletePoiSet) {
        //求增加的关联门店
        Set<Integer> wmPoiIdSet = WmPoiSettleTransUtil.transWmPoiSettleList2WmPoiIdSet(wmPoiSettleList);
        Set<Integer> wmPoiIdAuditedSet = WmPoiSettleTransUtil
                .transWmPoiSettleAuditedList2WmPoiIdSet(wmPoiSettleAuditedList);
        Set<Integer> deleteSet = SetUtil
                .gendeleteSet(wmPoiIdSet, wmPoiIdAuditedSet);//wmPoiIdAuditedSet比wmPoiIdSet多的门店

        LOGGER.info("WmUnaudited2Audited 删除结算门店关联关系,wmContractId={},deleteList={}", wmContractId,
                JSON.toJSONString(deleteSet));
        if (CollectionUtils.isEmpty(deleteSet)) {
            return true;
        }
        deletePoiSet.addAll(deleteSet);
        String wmPoiIds = StringUtils.join(deleteSet, ",");
        wmPoiSettleAuditedDBMapper.deleteByWmContractIdAndWmPoiIds(wmContractId, wmPoiIds);

        Map<Integer, WmPoiSettleAuditedDB> mapOld = WmPoiSettleTransUtil
                .transWmPoiSettleAuditedList2Map(wmPoiSettleAuditedList);
        WmPoiSettleAuditedDB newDB = new WmPoiSettleAuditedDB();
        for (Integer id : deleteSet) {
            newDB.setWm_poi_id(id);
            diffPoiSettle(changedEventMap, changedEventList, newDB, mapOld.get(id));
        }

        return true;
    }

    private void saveWmSettlePoiWalletHistoryRel(List<WmPoiSettleAuditedDB> wmPoiSettleAuditedList) {
        LOGGER.info("saveWmSettlePoiWalletRel wmPoiSettleAuditedList = {}", JSON.toJSONString(wmPoiSettleAuditedList));
        if (CollectionUtils.isEmpty(wmPoiSettleAuditedList)) {
            return;
        }

        try {
            Map<Integer, WmPoiSettleAuditedDB> wmPoiSettleAuditedDBMap = Maps.uniqueIndex(wmPoiSettleAuditedList, new Function<WmPoiSettleAuditedDB, Integer>() {
                @Nullable
                @Override
                public Integer apply(@Nullable WmPoiSettleAuditedDB input) {
                    return input.getWm_poi_id();
                }
            });

            Multimap<Integer, Integer> wmSettlePoiMultimap = ArrayListMultimap.create();
            for (Integer wmPoiId : wmPoiSettleAuditedDBMap.keySet()) {
                WmPoiSettleAuditedDB wmPoiSettleAuditedDB = wmPoiSettleAuditedDBMap.get(wmPoiId);
                if (wmPoiSettleAuditedDB != null) {
                    wmSettlePoiMultimap.put(wmPoiSettleAuditedDB.getWm_settle_id(), wmPoiId);
                }
            }
            Set<Integer> wmSettleIdSet = wmSettlePoiMultimap.keySet();
            if (CollectionUtils.isEmpty(wmSettleIdSet)) {
                return;
            }

            List<WmSettleAuditedDB> wmSettleAuditedDBS = Lists.newArrayList();
            List<List<Integer>> lists = Lists.partition(Lists.newArrayList(wmSettleIdSet), 300);
            for (List<Integer> list : lists) {
                String wmSettleIds = StringUtils.join(list, ",");
                wmSettleAuditedDBS.addAll(wmSettleAuditedDBMapper.batchGetWmSettleAuditedByWmSettleIds(wmSettleIds));
            }

            for (WmSettleAuditedDB wmSettleAuditedDB : wmSettleAuditedDBS) {
                if (wmSettleAuditedDB == null || wmSettleAuditedDB.getWm_wallet_id() <= 0) {
                    LOGGER.info("绑定门店、结算、钱包关联关系失败，没有生效结算/不存在钱包/结算没有绑定门店 wmSettleAuditedDB = {}", JSON.toJSONString(wmSettleAuditedDB));
                    return;
                }
                Collection<Integer> wmPoiIdList = wmSettlePoiMultimap.get(wmSettleAuditedDB.getWm_settle_id());

                WmSettlePoiWalletBo wmSettlePoiWalletBo = new WmSettlePoiWalletBo();
                wmSettlePoiWalletBo.setWm_settle_id(wmSettleAuditedDB.getWm_settle_id());
                wmSettlePoiWalletBo.setWmPoiIdList(Lists.newArrayList(wmPoiIdList));
                wmSettlePoiWalletBo.setWm_wallet_id(wmSettleAuditedDB.getWm_wallet_id());
                wmSettlePoiWalletBo.setCtime(DateUtil.unixTime());
                wmSettlePoiWalletBo.setUtime(DateUtil.unixTime());
                wmSettlePoiWalletBo.setValid((byte) 1);
                wmSettleWalletService.saveWmSettlePoiWalletRel(wmSettlePoiWalletBo);
            }
        } catch (Exception e) {
            LOGGER.error("绑定门店、结算、钱包关联关系失败 ", e);
        }
    }

    private boolean insertPoiSettle(int wmCustomerId, List<WmPoiSettleDB> wmPoiSettleList,
                                    List<WmPoiSettleAuditedDB> wmPoiSettleAuditedList,
                                    Map<Integer, ChangedEvent> changedEventMap, List<ChangedEvent> changedEventList) {
        //求增加的关联门店
        Set<Integer> wmPoiIdSet = WmPoiSettleTransUtil.transWmPoiSettleList2WmPoiIdSet(wmPoiSettleList);
        Set<Integer> wmPoiIdAuditedSet = WmPoiSettleTransUtil
                .transWmPoiSettleAuditedList2WmPoiIdSet(wmPoiSettleAuditedList);
        Set<Integer> addSet = SetUtil
                .genAddSet(wmPoiIdSet, wmPoiIdAuditedSet);//wmPoiIdAuditedSet比wmPoiIdSet多的门店

        //得出对应的关联关系WmPoiSettle
        LOGGER.info("WmUnaudited2Audited 新增结算门店关联关系wmCustomerId={},addSet={}", wmCustomerId,
                JSON.toJSONString(addSet));
        Map<Integer, WmPoiSettleDB> map = WmPoiSettleTransUtil
                .transWmPoiSettleList2Map(wmPoiSettleList);
        for (Integer add : addSet) {
            WmPoiSettleAuditedDB wmPoiSettleAudited = WmPoiSettleTransUtil
                    .transWmPoiSettleDB(map.get(add));
            wmPoiSettleAuditedDBMapper.insertSelective(wmPoiSettleAudited);
            diffPoiSettle(changedEventMap, changedEventList, wmPoiSettleAudited,
                    new WmPoiSettleAuditedDB());
        }

        return true;
    }

    private Map<String, DiffValue> diffPoiSettle(Map<Integer, ChangedEvent> changedEventMap,
                                                 List<ChangedEvent> changedEventList,
                                                 WmPoiSettleAuditedDB newDB, WmPoiSettleAuditedDB oldDB) {
        WmPoiSettleDBDiff newValue = WmPoiSettleTransUtil.transWmPoiSettleDB2DiffBean(newDB);
        WmPoiSettleDBDiff oldValue = WmPoiSettleTransUtil.transWmPoiSettleDB2DiffBean(oldDB);

        Map<String, DiffValue> diffValues = BeanDiffUtil.diffValueMap(newValue, oldValue);
        ChangedEvent changedEvent = changedEventMap.get(newDB.getWm_poi_id());
        if (changedEvent != null && (!changedEvent.getOperation().equals(ChangedEvent.DELETE))) {
            // 如果有该商家的变更，且为更新，则直接加入
            changedEvent.getDiffValues().putAll(diffValues);
        } else if (changedEvent == null && MapUtils.isNotEmpty(diffValues)) {
            // 如果没有该商家的变更，则直接新增
            changedEventList.add(new ChangedEvent(newDB.getWm_poi_id(), ChangedEvent.UPDATE, diffValues));
        }
        return diffValues;
    }

    private long getLatestTransactionId(int wmCustomerId) throws WmCustomerException {
        WmContractVersionDB wmContractVersionDB = wmContractVersionDBMapper.
                getLastWmContractVersionByWmContractIdAndTypesMaster(wmCustomerId,
                        Lists.newArrayList(CustomerContractConstant.NORMAL_SETTLE_VERSION_TYPE,
                                CustomerContractConstant.MOON_SETTLE_VERSION_TYPE)
                );
        if (wmContractVersionDB == null) {
            throw new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR, "没有流水信息");
        }
        return Long.parseLong(wmContractVersionDB.getTransaction_id());
    }

    public WmContractVersionDB getWmCustomerIdByTransactionId(long transactionId)
            throws WmCustomerException {
        WmContractVersionDB wmContractVersionDB = wmContractVersionDBMapper
                .getByTransactionIdMaster(transactionId + "");
        if (wmContractVersionDB == null) {
            throw new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR, "没有流水信息");
        }
        return wmContractVersionDB;
    }

    private boolean canCancel(byte status) {
        return status == WmSettleConstant.SETTLE_STATUS_TO_CONFIRM
                || status == WmSettleConstant.SETTLE_STATUS_WAIT_SIGN;
    }

    private boolean canReSendMsg(byte status) {
        return status == WmSettleConstant.SETTLE_STATUS_TO_CONFIRM;
    }

    public BooleanResult cancelConfirm(int wmCustomerId) throws WmCustomerException {
        //校验状态
        byte status = wmSettleService.getSettleStatusByWmCustomerId(wmCustomerId);
        if (!canCancel(status)) {
            throw new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR, "当前状态不能取消");
        }
        if (status == WmSettleConstant.SETTLE_STATUS_WAIT_SIGN) {
            cancelWaitSign(wmCustomerId);
            return new BooleanResult(true);
        }

        try {
            //获取流水ID
            long transactionId = getLatestTransactionId(wmCustomerId);
            wmEcontractSignThriftService.cancelSign(Long.valueOf(transactionId));
        } catch (TException ex) {
            LOGGER.error("cancelSign", ex);
            return new BooleanResult(false);
        } catch(WmCustomerException e){
            //没有流水信息的异常场景,允许结算状态强制重置
            if (ConfigUtilAdapter.getBoolean("cancelConfirm_ignore_no_flow", false) && "没有流水信息".equals(e.getMsg())) {
                LOGGER.error("cancelSign WmCustomerException",e);
                wmSettleService.resetWmSettleStatus(wmCustomerId,WmSettleConstant.SETTLE_STATUS_TO_COMPLETE);
            }else{
                throw e;
            }
        }
        return new BooleanResult(true);
    }

    private void cancelWaitSign(int wmCustomerId) throws WmCustomerException {
      LOGGER.info("结算待发起签约取消 customerId:{}", wmCustomerId);

        if( wmHeronSettleGrayService.routHeronSettleGray(wmCustomerId,0)) {
            LOGGER.info("结算待发起签约取消灰度 wmCustomerId:[{}]", wmCustomerId);
            BaseOperateDataInputDTO baseOperateDataInputDTO = new BaseOperateDataInputDTO();
            baseOperateDataInputDTO.setOpId(0);
            baseOperateDataInputDTO.setOpName("系统");
            baseOperateDataInputDTO.setChannel(CChannelTypeEnum.CHANNEL_TYPE_WAIMAI.getValue());
            baseOperateDataInputDTO.setCBizTypeEnumList(Lists.newArrayList(CBizTypeEnum.CUSTOMER_SWITCH));
            try {
                if (MccConfig.notCallSettleModifySettleSignFail()) {
                    return;
                }
                wmSettleExportThriftService.modifySettleSignFail(wmCustomerId, "BD主动取消签约",
                        baseOperateDataInputDTO);
            } catch (WmHeronSettleException e) {
                LOGGER.error("结算待发起签约取消灰度异常 wmCustomerId:[{}]",  wmCustomerId,
                        e);
            } catch (TException e) {
                LOGGER.error("结算待发起签约取消灰度异常 wmCustomerId:[{}]",  wmCustomerId, e);
            }
        }else{
            wmSettleService.updateStatusByWmCustomerId(wmCustomerId,
                    WmSettleConstant.SETTLE_STATUS_CONFIRM_FAIL);
            try {
                insertCancelProduceLogAsy(wmSettleService
                        .getOfflineWmPoiIdListByWmCustomerId(wmCustomerId), 0, "系统");
            } catch (Exception e) {
                LOGGER.error("insertCancelProduceLog异常", e);
            }

            wmSettleLogService.insertWmSettleLog(wmCustomerId,
                    WmCustomerOplogBo.OpType.CHANGESTATUS,
                    0,
                    "系统",
                    "BD主动取消签约"
            );

            wmSettleCommonAtomService.noticeSwitchCentre(wmCustomerId);
        }

        wmEcontractSignBzService.cancelManualTask(wmCustomerId, EcontractTaskApplyTypeEnum.SETTLE);
    }

  public RetrySmsResponse reSendMsg(int wmCustomerId) throws WmCustomerException {
    //校验状态
    byte status = wmSettleService.getSettleStatusByWmCustomerId(wmCustomerId);
    if (!canReSendMsg(status)) {
      throw new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR, "当前状态不能重发短信");
    }
    //获取流水ID
    long transactionId = getLatestTransactionId(wmCustomerId);
    try {
      return wmEcontractSignThriftService.resendMsgWithResponse(Long.valueOf(transactionId));
    } catch (TException ex) {
      LOGGER.error("resendMsg", ex);
      return new RetrySmsResponse(false);
    }
  }

    /**
     * 电子合同回调
     *
     * @param callBackBo
     * @throws WmCustomerException
     */
    public void wmSettleConfirmFlowCallback(EcontractCallbackBo callBackBo,
            EcontractCustomerKPBo kpBo)
            throws WmCustomerException {
        LOGGER.info("#wmSettleConfirmFlowCallback,callBackBo={}", JSONObject.toJSONString(callBackBo));
        // 结算流程和数据已经迁移到金融侧，此接口不再处理流程中任务（20年左右的旧结算流程任务）的接口回调
        if (MccConfig.getOfflineSettleSwitchV3()) {
            LOGGER.info("wmSettleConfirmFlowCallback offline");
            return;
        }
        long transactionId = callBackBo.getTaskId();
        AssertUtil.assertLongMoreThan0(transactionId, "transactionId");
        String pdfUrl = callBackBo.getPdfUrl();
        String other = callBackBo.getOther();
        EcontractTaskStateEnum state = callBackBo.getState();
        WmContractVersionDB wmContractVersionDB = getWmCustomerIdByTransactionId(transactionId);
        long latestTransactionId = getLatestTransactionId(wmContractVersionDB.getWm_contract_id());
        //回调不是最新的流水号,不做处理
        if(latestTransactionId != transactionId){
            LOGGER.error("#wmSettleConfirmFlowCallback,不是最新的流水号回调,latestTransactionId={},transactionId={}",latestTransactionId,transactionId);
            return;
        }
        switch (state) {
            case IN_PROCESSING:
                handleInProcessing(wmContractVersionDB, pdfUrl);
                break;
            case SUCCESS:
                handleSuccess(wmContractVersionDB, pdfUrl, kpBo);
                break;
            case FAIL:
                handleFail(callBackBo.getFailMsg(), other, wmContractVersionDB);
                break;
            case CANCEL:
                handleCancel(other, wmContractVersionDB);
                break;
            default:
                break;
        }
    }

    private void handleCancel(String other, WmContractVersionDB wmContractVersionDB)
            throws WmCustomerException {
        wmContractVersionDB.setStatus(WmSettleConstant.SETTLE_STATUS_CONFIRM_FAIL);
        wmContractVersionDBMapper.updateStatus(wmContractVersionDB);

        if( wmHeronSettleGrayService.routHeronSettleGray(wmContractVersionDB.getWm_contract_id(),wmContractVersionDB
                .getOp_uid())) {
            LOGGER.info("电子签约回调更新取消签约状态灰度 wmContractVersionDB:[{}]", JSON.toJSONString(wmContractVersionDB));
            BaseOperateDataInputDTO baseOperateDataInputDTO = new BaseOperateDataInputDTO();
            baseOperateDataInputDTO.setOpId(0);
            baseOperateDataInputDTO.setOpName("系统");
            baseOperateDataInputDTO.setChannel(CChannelTypeEnum.CHANNEL_TYPE_WAIMAI.getValue());
            baseOperateDataInputDTO.setCBizTypeEnumList(Lists.newArrayList(CBizTypeEnum.CUSTOMER_SWITCH));
            try {
                if (MccConfig.notCallSettleModifySettleSignFail()) {
                    return;
                }
                wmSettleExportThriftService.modifySettleSignFail(wmContractVersionDB.getWm_contract_id(), "BD主动取消确认",
                        baseOperateDataInputDTO);
            } catch (WmHeronSettleException e) {
                LOGGER.error("结算电子签约回调，更新为取消签约状态异常 wmContractVersionDB:[{}]",  JSON.toJSONString(wmContractVersionDB),
                        e);
            } catch (TException e) {
                LOGGER.error("结算电子签约回调，更新为取消签约状态异常 wmContractVersionDB:[{}]",  JSON.toJSONString(wmContractVersionDB), e);
            }

        }else{
            wmSettleService.updateStatusByWmCustomerId(wmContractVersionDB.getWm_contract_id(), WmSettleConstant.SETTLE_STATUS_CONFIRM_FAIL);
            try {
                insertCancelProduceLogAsy(wmSettleService.getOfflineWmPoiIdListByWmCustomerId(wmContractVersionDB.getWm_contract_id()), 0, "系统");
            } catch (Exception e) {
                LOGGER.error("insertCancelProduceLog异常", e);
            }

            wmSettleLogService.insertWmSettleLog(wmContractVersionDB.getWm_contract_id(), WmCustomerOplogBo.OpType.CHANGESTATUS, 0, "系统", "BD主动取消确认");

            noticeSwitchCentreFail(wmContractVersionDB.getWm_contract_id());
        }
    }

    private void handleFail(String failStr, String other, WmContractVersionDB wmContractVersionDB)
            throws WmCustomerException {
        wmContractVersionDB.setStatus(WmSettleConstant.SETTLE_STATUS_CONFIRM_FAIL);
        wmContractVersionDBMapper.updateStatus(wmContractVersionDB);

       if( wmHeronSettleGrayService.routHeronSettleGray(wmContractVersionDB.getWm_contract_id(),wmContractVersionDB
               .getOp_uid())){

           LOGGER.info("电子签约回调更新失败状态灰度 wmContractVersionDB:[{}]",JSON.toJSONString(wmContractVersionDB));
           BaseOperateDataInputDTO baseOperateDataInputDTO=new BaseOperateDataInputDTO();
           baseOperateDataInputDTO.setOpId(0);
           baseOperateDataInputDTO.setOpName("系统");
           baseOperateDataInputDTO.setChannel(CChannelTypeEnum.CHANNEL_TYPE_WAIMAI.getValue());
           baseOperateDataInputDTO.setCBizTypeEnumList(Lists.newArrayList(CBizTypeEnum.CUSTOMER_SWITCH));
           try {
               if (MccConfig.notCallSettleModifySettleSignFail()) {
                   return;
               }
               wmSettleExportThriftService.modifySettleSignFail(wmContractVersionDB.getWm_contract_id(),failStr,
                       baseOperateDataInputDTO);
           } catch (WmHeronSettleException e) {
              LOGGER.error("结算电子签约回调，更新为失败状态异常 wmContractVersionDB:[{}]",JSON.toJSONString(wmContractVersionDB),e);
           } catch (TException e) {
               LOGGER.error("结算电子签约回调，更新为失败状态异常 wmContractVersionDB:[{}]",JSON.toJSONString(wmContractVersionDB),e);
           }


       }else{
           wmSettleService.updateStatusByWmCustomerId(wmContractVersionDB.getWm_contract_id(), WmSettleConstant.SETTLE_STATUS_CONFIRM_FAIL);
           try {
               insertCancelProduceLogAsy(wmSettleService.getOfflineWmPoiIdListByWmCustomerId(wmContractVersionDB.getWm_contract_id()), 0, "系统");
           } catch (Exception e) {
               LOGGER.error("insertCancelProduceLog异常", e);
           }

           if (StringUtils.isNotEmpty(failStr)) {
               wmSettleLogService.insertWmSettleLog(wmContractVersionDB.getWm_contract_id(), WmCustomerOplogBo.OpType.CHANGESTATUS, 0, "系统", failStr);
           }
           noticeSwitchCentreFail(wmContractVersionDB.getWm_contract_id());
       }
    }

    private void noticeSwitchCentreFail(int wmCustomerId) {
        handleService.execute(new Runnable() {
            @Override
            public void run() {
                try {
                    noticeSwitchCentre(wmCustomerId);
                } catch (WmCustomerException e) {
                    LOGGER.error("noticeSwitchCentre,wmCustomerId={}", wmCustomerId, e);
                }
            }
        });
    }

    private void noticeSwitchCentre(int wmCustomerId) throws WmCustomerException {
        wmSettleEffectDomainService.noticeSwitchCentre(wmCustomerId);
    }

    private void handleSuccess(WmContractVersionDB wmContractVersionDB, String pdfUrl, EcontractCustomerKPBo kpBo) {
        final int wmCustomerId = wmContractVersionDB.getWm_contract_id();
        if (StringUtils.isNotEmpty(pdfUrl)) {
            wmContractVersionDBMapper.updatePdfUrl(wmContractVersionDB.getId(), pdfUrl, "");
            wmContractVersionDB.setStatus(WmSettleConstant.SETTLE_STATUS_EFFECT);
            wmContractVersionDBMapper.updateStatus(wmContractVersionDB);
        }
        handleService.execute(new Runnable() {
            @Override
            public void run() {
                try {
                    setupEffect(wmCustomerId, 0, "系统");
                    //生意贷协议签署通知
                    noticeBusinessLoansSignEvent(wmCustomerId,pdfUrl,kpBo);
                    noticeCusOwner();
                } catch (WmCustomerException e) {
                    LOGGER.error("setupEffect异常,wmCustomerId={}", wmCustomerId, e);
                }
            }

            private void noticeCusOwner() throws WmCustomerException {
                List<Integer> poiIds = wmSettleService.getOfflineWmPoiIdListByWmCustomerId(wmCustomerId);
                if (CollectionUtils.isNotEmpty(poiIds)) {
                    WmCustomerDB customerDB = wmCustomerService.selectCustomerById(wmCustomerId);
                    WmPoiDomain poiInfo = wmPoiClient.getWmPoiById(poiIds.get(0));

                    try {
                        if(customerDB.getOwnerUid()==null||customerDB.getOwnerUid().intValue()==0){
                            LOGGER.info("noticeCusOwner 客户所属uid为空 customerId:[{}],customerName:[{}]", customerDB
                                    .getId(),customerDB.getCustomerName());
                            return;
                        }

                        PublishNoticeDto publishNoticeDto = new PublishNoticeDto();

                        publishNoticeDto.setUid(0);
                        publishNoticeDto.setReceiveUids(String.valueOf(customerDB.getOwnerUid()));
                        publishNoticeDto.setMsgFormat(WmNoticeMsgFormatEnum.MSG_FORMAT_PLAIN.getValue());
                        String content = String.format("【结算信息签约成功】客户：%s（%s），门店：%s（%s）", customerDB.getCustomerName(), customerDB.getId(),
                                poiInfo.getName(), poiInfo.getWmPoiId());
                        JSONArray jsonArray = new JSONArray();
                        JSONObject jsonObject = new JSONObject();
                        jsonObject.put("title", "【结算信息签约成功】");
                        jsonObject.put("content", content);
                        jsonArray.add(jsonObject);
                        String message = jsonArray.toJSONString();

                        publishNoticeDto.setMsg(message);
                        publishNoticeDto.setWmNoticeTypeId(ConfigUtilAdapter.getInt("settle_notice_type", 0));
                        publishNoticeDto.setMustRead(WmNoticeMustReadEnum.NO.getValue());
                        String pushMedia = WmNoticePushMediaEnum.PUSH_MEDIA_DAXIANG.getValue() + ","
                                + WmNoticePushMediaEnum.PUSH_MEDIA_BEE_APP.getValue();
                        publishNoticeDto.setPushMedia(ConfigUtilAdapter.getString("receive_terminal", pushMedia));
                        Map<String, String> map = Maps.newHashMap();
                        map.put(WmNoticeMainBodyTypeEnum.WM_POI_ID.getMainBodyType(), String.valueOf(poiInfo.getWmPoiId()));
                        publishNoticeDto.setBusinessMainBody(map);

                        LOGGER.info("publishNoticeDto={}", publishNoticeDto);
                        wmNoticePublishThriftService.publishNotice(publishNoticeDto);
                    } catch (Exception e) {
                        LOGGER.error("发送【结算信息签约成功】失败", e);
                    }
                }
            }
        });
    }

    private void noticeBusinessLoansSignEvent(int wmCustomerId, String pdfUrl, EcontractCustomerKPBo kpBo)
            throws WmCustomerException {
        LOGGER.info("#noticeBusinessLoansSignEvent,wmCustomerId={},pdfUrl={}",wmCustomerId,pdfUrl);
        if (PdfUrlTransUtils.isPdfJsonFormat(pdfUrl)){
            BatchPdfInfo batchPdfInfo = JSONObject
                    .parseObject(pdfUrl, BatchPdfInfo.class);
            //已签署生意贷协议
            if(batchPdfInfo!=null && StringUtils.isNotEmpty(batchPdfInfo.getSettle_businessloans())){
                businessLoansNoticeService.noticeSignBusinessLoans(wmCustomerId,kpBo);
            }
        }
    }

    private void handleInProcessing(WmContractVersionDB wmContractVersionDB, String pdfUrl) {
        if (StringUtils.isNotEmpty(pdfUrl)) {
            wmContractVersionDBMapper.updatePdfUrl(wmContractVersionDB.getId(), pdfUrl, "");
            wmContractVersionDB.setStatus(WmSettleConstant.SETTLE_STATUS_TO_CONFIRM);
            wmContractVersionDBMapper.updateStatus(wmContractVersionDB);
        }
    }

    private boolean canCommit(byte status) {
        return status != WmSettleConstant.SETTLE_STATUS_TO_CONFIRM && status != WmSettleConstant.SETTLE_STATUS_TO_AUDIT
                && status != WmSettleConstant.SETTLE_STATUS_WAIT_SIGN;
    }

    public void commitWmSettle(int wmCustomerId, int signPackWay, int opUid, String opUname)
            throws WmCustomerException, TException {
        byte status = wmSettleService.getSettleStatusByWmCustomerId(wmCustomerId);
        if (!canCommit(status)) {
            throw new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR, "当前状态不能提交确认");
        }
        checkWmPoiOnlineStatus(wmCustomerId);
        wmSettleService.commitSettleInfo(wmCustomerId, signPackWay, opUid, opUname,0);
    }

    public void commitAuditWmSettle(int wmCustomerId, String wmCustomerName, int opUid, String opUname, String supplementalUrl, String qdbUrl)
            throws WmCustomerException, TException {
        byte status = wmSettleService.getSettleStatusByWmCustomerId(wmCustomerId);
        if (!canCommit(status)) {
            throw new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR, "当前状态不能提交审核");
        }
        checkWmPoiOnlineStatus(wmCustomerId);
        wmSettleService.commitSettleInfoToAudit(wmCustomerId, wmCustomerName, opUid, opUname, supplementalUrl, qdbUrl);
    }

    private void checkWmPoiOnlineStatus(int wmCustomerId) throws TException, WmCustomerException {
        List<Long> wmPoiIdList = wmCustomerPoiService.selectWmPoiIdsByCustomerIdFromDBAndSwitchCentre(wmCustomerId);
        List<WmPoiDomain> wmPoiDomainList = wmPoiClient.pageGetWmPoiByWmPoiIdList(wmPoiIdList);
        CollectionUtils.filter(wmPoiDomainList, new Predicate() {
            @Override
            public boolean evaluate(Object object) {
                WmPoiDomain wmPoiDomain = (WmPoiDomain) object;
                return wmPoiDomain.getValid() == 1;
            }
        });
        List<Long> onlinePoiIdList = Lists.transform(wmPoiDomainList, new Function<WmPoiDomain, Long>() {
            @Nullable
            @Override
            public Long apply(@Nullable WmPoiDomain input) {
                return (long)input.getWmPoiId();
            }
        });
        List<Long> existSettlePoiIdList = Lists.newArrayList(batchGetSettleWmPoiIdSet(onlinePoiIdList,wmCustomerId));
        onlinePoiIdList.removeAll(existSettlePoiIdList);
        if (CollectionUtils.isNotEmpty(onlinePoiIdList)) {
            String errorMsg = String.format("提交失败:门店id %s 未录入结算信息", StringUtils.join(onlinePoiIdList, "、"));
            if (onlinePoiIdList.size() > WM_POI_ID_SIZE) {
                List<Long> notExistSettlePoiIdList = Lists.newArrayList();
                for (int i = 0; i < WM_POI_ID_SIZE; i++) {
                    notExistSettlePoiIdList.add(onlinePoiIdList.get(i));
                }
                errorMsg = String.format("提交失败:门店id %s 等未录入结算信息", StringUtils.join(notExistSettlePoiIdList, "、"));
            }
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, errorMsg);
        }
    }

    public ContractVersionPageData getSettleVersions(int wmCustomerId, int pageNo, int pageSize,
                                                     int opUid, String opName) {
        PageHelper.startPage(pageNo, pageSize);
        List<WmContractVersionDB> versionDBList = wmContractVersionService
                .getByWmContractIdAndTypeAndStatus(wmCustomerId,
                        Lists.newArrayList(
                                CustomerContractConstant.MOON_SETTLE_VERSION_TYPE,
                                CustomerContractConstant.NORMAL_SETTLE_VERSION_TYPE),
                        Lists.newArrayList(
                                WmSettleConstant.SETTLE_STATUS_TO_CONFIRM, WmSettleConstant.SETTLE_STATUS_EFFECT,
                                WmSettleConstant.SETTLE_STATUS_CONFIRM_FAIL
                        )
                );
        List<WmCustomerContractVersionBo> resData = Lists.newArrayList();
        for (WmContractVersionDB db : MoreObjects
                .firstNonNull(versionDBList, Lists.<WmContractVersionDB>newArrayList())) {
            resData.add(WmTempletContractTransUtil.transVersionDbToBo(db));
        }
        PageData<WmCustomerContractVersionBo> page = PageUtil.page(versionDBList, resData);
        return new ContractVersionPageData(page.getPageInfo(), page.getList());
    }

    public Set<Long> batchGetSettleEffectiveWmPoiIdSet(List<Long> wmPoiIdList,int customerId) throws WmCustomerException {
        List<Long> result = Lists.newArrayList();
        if (CollectionUtils.isEmpty(wmPoiIdList)) {
            return Sets.newHashSet();
        }
        if (customerId % 100 < MccCustomerConfig.settleDataTransferPercent()) {
            LOGGER.info("customerId:{}命中结算数据迁移灰度,启用金服结算查询接口", customerId);
            SettleInfoBatchQueryReq settleInfoBatchQueryReq = new SettleInfoBatchQueryReq();
            settleInfoBatchQueryReq.setIphPayMerchantNo(MccCustomerConfig.getIphPayMerchantNo());
            settleInfoBatchQueryReq.setCustomerId((long) customerId);
            settleInfoBatchQueryReq.setPoiIdList(wmPoiIdList);
            List<SettleInfoData> settleInfoDataIncludeInValid = merchantSettleQueryProxyServiceAdaptor.querySettleInfoByPoiIdListAndCustomerId(settleInfoBatchQueryReq);
            //if门店集合in(wmPoiIdSet) Then添加到effectiveSettle
            if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(settleInfoDataIncludeInValid)) {
                settleInfoDataIncludeInValid.stream()
                        .map(SettleInfoData::getRelationPoiList)
                        .flatMap(Collection::stream)
                        .filter(wmPoiIdList::contains)
                        .forEach(result::add);
            }
            //开启流量对比
            if(MccCustomerConfig.settleDataTransferDiffFlag()){
                List<Long> oldRes = wmPoiSettleAuditedDBMapper.batchGetEffectiveWmPoiIdList(wmPoiIdList);
                if(oldRes == null){
                    oldRes = Lists.newArrayList();
                }
                if(!result.containsAll(oldRes) || !oldRes.containsAll(result)){
                    LOGGER.warn("结算数据迁移,流量对比不通过,新逻辑返回结果:{},老逻辑返回结果:{},逻辑降级,启用老结果",JSONObject.toJSONString(result),JSONObject.toJSONString(oldRes));
                    Cat.logEvent("settleDataTransfer","fail");
                    return Sets.newHashSet(oldRes);
                }
            }
            Cat.logEvent("settleDataTransfer","success");

        }else {
            result = wmPoiSettleAuditedDBMapper.batchGetEffectiveWmPoiIdList(wmPoiIdList);

        }
        return Sets.newHashSet(result);
    }

    public Set<Long> batchGetSettleWmPoiIdSet(List<Long> wmPoiIdList,int wmCustomerId) {
        List<Long> result = Lists.newArrayList();
        if (CollectionUtils.isEmpty(wmPoiIdList)) {
            return Sets.newHashSet();
        }
        result = wmPoiSettleDBMapper.batchGetSettleWmPoiIdSet(wmPoiIdList,wmCustomerId);
        return Sets.newHashSet(result);
    }

    public BooleanResult bankCardValidateCallback(String partner, String serialNo, String status,
                                                  String reason) throws TException, WmCustomerException {
        wmBankCardValidationService.validateCallback(partner, serialNo, status, reason);
        return new BooleanResult(true);
    }

  public boolean updateWmSettleWallet(int wmCustomerId, int wmSettleId, long mwalletId,
      int walletOpType, boolean result, String reason, int opUid, String opUname, String loginName) {
    opUid = Integer.MAX_VALUE;
    opUname = "系统";
    LOGGER.info(
        "updateWmSettleWallet 更新钱包, wmCustomerId = {},wmSettleId = {},  mwalletId = {},walletOpType = {}, result = {}, reason = {} , opUid = {}, opUname = {}",
        wmCustomerId, wmSettleId, mwalletId, walletOpType, result, reason, opUid, opUname);
    updateWmSettleWalletAndSendMsg(wmCustomerId, wmSettleId, mwalletId, opUid, opUname);
    if(mwalletId==0){
      return true;//PM确认：删除钱包则不记录任何日志
    }
    String diff;
    //主要更新操作日志
    switch (walletOpType) {
      case WmContractConstant.OPEN_WALLET:
        diff = result ? "【钱包操作】钱包开通：成功 \n 钱包ID："+mwalletId +";   钱包账户：" + loginName
                : "【钱包操作】钱包开通：失败      \n失败原因：" + reason;
        wmSettleLogService
                .insertWmSettleLog(wmCustomerId, WmCustomerOplogBo.OpType.WALLET, opUid, opUname, diff);
        return true;
      case WmContractConstant.UPDATE_WALLET:
//                String diff2 = (result ? "[开钱包]修改卡成功" : ("[开钱包]修改卡失败, 失败原因:" + reason)) + ", 结算id:" + wmSettleId;
        diff = result ? "【钱包操作】钱包绑卡：成功 结算ID："+ wmSettleId +"\n 钱包ID："+mwalletId +";   钱包账户：" + loginName
                : "【钱包操作】钱包绑卡：失败 结算ID："+ wmSettleId+"\n 钱包ID："+mwalletId +";   钱包账户：" + loginName+"\n失败原因："+reason;
        wmSettleLogService
                .insertWmSettleLog(wmCustomerId, WmCustomerOplogBo.OpType.WALLET, opUid, opUname,
                        diff);
        return true;
      default:
        return true;
    }
  }

  public boolean updateWmSettleWalletWithContext(int wmCustomerId, int wmSettleId, long mwalletId,
                                      int walletOpType, boolean result, String reason, int opUid, String opUname, long mwalletIdContext, String longinNameContext) {
    opUid = Integer.MAX_VALUE;
    opUname = "系统";
    LOGGER.info(
            "updateWmSettleWallet 更新钱包, wmCustomerId = {},wmSettleId = {},  mwalletId = {},walletOpType = {}, result = {}, reason = {} , opUid = {}, opUname = {}",
            wmCustomerId, wmSettleId, mwalletId, walletOpType, result, reason, opUid, opUname);
    updateWmSettleWalletAndSendMsgWithContext(wmCustomerId, wmSettleId, mwalletId, opUid, opUname, result?0:1, reason, longinNameContext);
    String diff="";
    switch (walletOpType) {
      case WmContractConstant.OPEN_WALLET:
//        String diff =
//                (result ? "[开钱包]开钱包成功" : ("[开钱包]开钱包失败, 失败原因:" + reason)) + ", 结算id:" + wmSettleId;
        diff = result ? "【钱包操作】钱包开通：成功 结算ID："+ wmSettleId+"\n 钱包ID："+mwalletIdContext +";   钱包账户：" + longinNameContext
                : "【钱包操作】钱包开通：失败      结算ID：" + wmSettleId + "\n失败原因：" + reason;
        wmSettleLogService
                .insertWmSettleLog(wmCustomerId, WmCustomerOplogBo.OpType.WALLET, opUid, opUname, diff);
        break;
      case WmContractConstant.UPDATE_WALLET:
        diff = result ? "【钱包操作】钱包绑卡：成功 结算ID："+ wmSettleId+"\n 钱包ID："+mwalletIdContext +";   钱包账户：" + longinNameContext
                : "【钱包操作】钱包绑卡：失败      结算ID：" + wmSettleId + "\n失败原因：" + reason;
        wmSettleLogService
                .insertWmSettleLog(wmCustomerId, WmCustomerOplogBo.OpType.WALLET, opUid, opUname,
                        diff);
        break;
      default:
    }
    if(StringUtils.isNotBlank(diff)) {//开钱包日志同步到poioplog
      List<Integer> wmPoiIdList = wmPoiSettleAuditedDBMapper.getPoiIdsBySettleId(wmSettleId);
      for (Integer wmPoiId : wmPoiIdList) {
        WmPoiOplog wmPoiOplog = new WmPoiOplog();
        wmPoiOplog.setWm_poi_id(wmPoiId);
        wmPoiOplog.setOp_type(WmPoiOpLog.OpType.WALLET_INFO.getId());
        wmPoiOplog.setDiff(diff);
        wmPoiOplog.setRemark("");
        wmPoiOplog.setOp_uid(opUid);
        wmPoiOplog.setOp_uname(opUname);
//                wmPoiOplog.setPre_value(before);
//                wmPoiOplog.setCurrent_value(after);
        try {
          wmPoiOplogThriftService.insertWithoutMQByAsync(wmPoiOplog);
        } catch (Exception e) {
          LOGGER.warn("记录门店操作日志异常", e);
        }
      }
    }
    return true;
  }

  public boolean updateWmSettleWalletRealName(int wmCustomerId, int wmSettleId, int walletOpType, boolean result, String reason, int opUid, String opUname, long mwalletIdContext, String longinNameContext){
    LOGGER.info(
            "updateWmSettleWalletRealName 更新实名状态, wmCustomerId = {},wmSettleId = {},  mwalletId = {},walletOpType = {}, result = {}, reason = {} , opUid = {}, opUname = {}",
            wmCustomerId, wmSettleId, walletOpType, result, reason, opUid, opUname);
//    wmSettleAuditedDBMapper.updateWmSettleWalletRealName(wmCustomerId, wmSettleId, result?0:1, reason);

    Integer latestedLogId = wmSettleAuditedDBMapper.getLatestedLogId(wmCustomerId, wmSettleId);
    LOGGER.info("contract:{}_settle:{}, latestedId={}", wmCustomerId, wmSettleId, latestedLogId);
    if(latestedLogId!=null) {
      wmSettleAuditedDBMapper.updateWmSettleWalletRealNameLog(latestedLogId, result?0:1, reason);
    }

    String diff = result?String.format(REAL_NAME_SUCCESS, wmSettleId, mwalletIdContext, longinNameContext):String.format(REAL_NAME_FAILED, wmSettleId, mwalletIdContext, longinNameContext, reason);
    wmSettleLogService
            .insertWmSettleLog(wmCustomerId, WmCustomerOplogBo.OpType.WALLET, opUid, opUname, diff);
    return true;
  }

  public boolean updateWmSettleWalletBindCard(int wmCustomerId, int wmSettleId, int walletOpType, boolean result, String reason, int opUid, String opUname, long mwalletIdContext, String longinNameContext){
    LOGGER.info(
            "updateWmSettleWalletBindCard 更新卡绑定状态, wmCustomerId = {},wmSettleId = {},  mwalletId = {},walletOpType = {}, result = {}, reason = {} , opUid = {}, opUname = {}",
            wmCustomerId, wmSettleId, walletOpType, result, reason, opUid, opUname);

    Integer latestedLogId = wmSettleAuditedDBMapper.getLatestedLogId(wmCustomerId, wmSettleId);
    LOGGER.info("contract:{}_settle:{}, latestedId={}", wmCustomerId, wmSettleId, latestedLogId);
    if(latestedLogId!=null) {
      wmSettleAuditedDBMapper.updateWmSettleWalletBindCardLog(latestedLogId, result?0:1, reason);
    }

    String diff = result?String.format(BIND_CARD_SUCCESS, wmSettleId, mwalletIdContext, longinNameContext):String.format(BIND_CARD_FAILED, wmSettleId, mwalletIdContext, longinNameContext, reason);
    wmSettleLogService
            .insertWmSettleLog(wmCustomerId, WmCustomerOplogBo.OpType.WALLET, opUid, opUname, diff);
    return true;
  }

    /**
     * 换绑手机号-记录操作日志
     * @param wmCustomerId
     * @param wmSettleId
     * @param mwalletId
     * @param walletOpType
     * @param result
     * @param reason
     * @param opUid
     * @param opUname
     * @param longinNameContext
     * @return
     */
    public boolean updateReservePhone(int wmCustomerId, int wmSettleId, long mwalletId,
            int walletOpType, boolean result, String reason, int opUid, String opUname, String longinNameContext,String extension) {
        String diff = result ? "【钱包操作】钱包换绑手机号：成功 结算ID："+ wmSettleId+"\n 钱包ID："+ mwalletId +";   钱包账户：" + longinNameContext + " 手机号:"+extension
                : "【钱包操作】钱包换绑手机号：失败      结算ID：" + wmSettleId + "\n 钱包ID："+ mwalletId +";   钱包账户：" + longinNameContext
                        + "\n失败原因：" + reason;
        wmSettleLogService
                .insertWmSettleLog(wmCustomerId, WmCustomerOplogBo.OpType.WALLET, opUid, opUname,
                        diff);
        if(StringUtils.isNotBlank(diff)) {//开钱包日志同步到poioplog
            List<Integer> wmPoiIdList = wmPoiSettleAuditedDBMapper.getPoiIdsBySettleId(wmSettleId);
            for (Integer wmPoiId : wmPoiIdList) {
                WmPoiOplog wmPoiOplog = new WmPoiOplog();
                wmPoiOplog.setWm_poi_id(wmPoiId);
                wmPoiOplog.setOp_type(WmPoiOpLog.OpType.WALLET_INFO.getId());
                wmPoiOplog.setDiff(diff);
                wmPoiOplog.setRemark("");
                wmPoiOplog.setOp_uid(opUid);
                wmPoiOplog.setOp_uname(opUname);
                try {
                    wmPoiOplogThriftService.insertWithoutMQByAsync(wmPoiOplog);
                } catch (Exception e) {
                    LOGGER.warn("记录门店操作日志异常", e);
                }
            }
        }
        return true;
    }

    /**
     * 钱包预认证日志记录
     */
    public boolean logWalletPreAuthResult(WmSettleWalletPreauthResultBo wmSettleWalletPreauthResultBo){
        Integer wmCustomerId = wmSettleWalletPreauthResultBo.getWmCustomerId();
        Boolean preAuthResult = wmSettleWalletPreauthResultBo.getPreAuthResult();
        String failReason = wmSettleWalletPreauthResultBo.getFailReason();
        String opUname = wmSettleWalletPreauthResultBo.getOpUname();
        Integer wmSettleId = wmSettleWalletPreauthResultBo.getWmSettleId();
        String diff = preAuthResult ? String.format(WALLET_PREAUTH_SUCCESS, wmSettleId) : String.format(WALLET_PREAUTH_FAILED, wmSettleId, failReason);
        wmSettleLogService.insertWmSettleLog(wmCustomerId, WmCustomerOplogBo.OpType.WALLET, 0, opUname, diff);
        return true;
    }


    /**
   * 验卡状态双更新
   * @param wmCustomerId
   * @param wmSettleId
   * @param card_valid
   * @param card_invalid_reason
   * @return
   */
  public boolean updateWmSettleCardValid(int wmCustomerId, int wmSettleId, int card_valid, String card_invalid_reason){
    wmSettleDBMapper.updateWmSettleCardValid(wmSettleId, card_valid, card_invalid_reason);
//    wmSettleAuditedDBMapper.updateWmSettleCardValid(wmCustomerId, wmSettleId, card_valid, card_invalid_reason);
    return true;
  }

    private void updateWmSettleWalletAndSendMsg(int wmCustomerId, int wmSettleId, long mwalletId,
                                                int opUid, String opUname) {
        WmSettleAuditedDB oldWmSettleAudited = wmSettleAuditedDBMapper.getBySettleIdMaster(wmSettleId);
        wmSettleAuditedDBMapper.updateWmSettleWalletByCustomerId(wmCustomerId, wmSettleId, mwalletId);

    WmSettleAuditedDB newWmSettleAudited = wmSettleAuditedDBMapper.getBySettleIdMaster(wmSettleId);
//    WmContractAudited wmContractAudited = wmContractAuditedService.getByContractId(wmContractId);

    // 发送结算变更消息
    WmSettleMsgSender wmSettleMsgSenderService = new WmSettleMsgSender();
    WmSettleMsgBo wmSettleMsgBo = new WmSettleMsgBo();
//    wmSettleMsgBo.setWmContractId(newWmSettleAudited.getWm_contract_id());
//    wmSettleMsgBo.setWmContractAuditedId(wmContractAudited.getId());
    wmSettleMsgBo.setWmCustomerId(wmCustomerId);
    wmSettleMsgBo.setWmSettleId(newWmSettleAudited.getWm_settle_id());
    wmSettleMsgBo.setWmSettleAuditedId(newWmSettleAudited.getId());
    wmSettleMsgBo.setType(WmSettleMsgBo.OpType.CHANGE);
    wmSettleMsgSenderService.addSettleMsg(wmSettleMsgBo);

    wmSettleMsgSenderService.send();

    //mafka发送消息
    List<ChangedEvent> settleChangedEventList = Lists.newArrayList();
    diffSettle(settleChangedEventList, newWmSettleAudited, oldWmSettleAudited, ChangedEvent.UPDATE);

    CustomerUpdateMessage customerUpdateMessage = new CustomerUpdateMessage(
        WmCustomerHistVersionConstant.VersionMessageBizTypeEnum.CUTOMER_SETTLE_EFFECT_OPEN_WALLET
            .getIndex());
    customerUpdateMessage.setWmCustomerId(wmCustomerId);
    customerUpdateMessage.setCtime((int) (System.currentTimeMillis() / 1000));
    customerUpdateMessage.getChangeEvents()
        .put(CustomerUpdateMessage.SETTLE, settleChangedEventList);
    customerUpdateMessage.setOpUid(opUid);
    customerUpdateMessage.setOpUname(opUname);
    wmCustomerUpdateSendService.sendSettleMsg(customerUpdateMessage);
  }

  /**
   * 更新结算信息上下文版
   * @param wmCustomerId
   * @param wmSettleId
   * @param mwalletId
   * @param opUid
   * @param opUname
   * @param status
   * @param reason
   */
  private void updateWmSettleWalletAndSendMsgWithContext(int wmCustomerId, int wmSettleId, long mwalletId,
                                              int opUid, String opUname, int status, String reason, String login_name) {
    WmSettleAuditedDB oldWmSettleAudited = wmSettleAuditedDBMapper.getBySettleIdMaster(wmSettleId);
//    wmSettleAuditedDBMapper.updateWmSettleWalletByCustomerIdWithContext(wmCustomerId, wmSettleId, mwalletId, status, reason);
    wmSettleAuditedDBMapper.updateWmSettleWalletByCustomerId(wmCustomerId, wmSettleId, mwalletId);
    wmSettleAuditedDBMapper.insertWalletOpenStatus(wmCustomerId, wmSettleId, mwalletId, login_name, status, reason);

    WmSettleAuditedDB newWmSettleAudited = wmSettleAuditedDBMapper.getBySettleIdMaster(wmSettleId);
//    WmContractAudited wmContractAudited = wmContractAuditedService.getByContractId(wmContractId);

        // 发送结算变更消息
        WmSettleMsgSender wmSettleMsgSenderService = new WmSettleMsgSender();
        WmSettleMsgBo wmSettleMsgBo = new WmSettleMsgBo();
//    wmSettleMsgBo.setWmContractId(newWmSettleAudited.getWm_contract_id());
//    wmSettleMsgBo.setWmContractAuditedId(wmContractAudited.getId());
        wmSettleMsgBo.setWmCustomerId(wmCustomerId);
        wmSettleMsgBo.setWmSettleId(newWmSettleAudited.getWm_settle_id());
        wmSettleMsgBo.setWmSettleAuditedId(newWmSettleAudited.getId());
        wmSettleMsgBo.setType(WmSettleMsgBo.OpType.CHANGE);
        wmSettleMsgSenderService.addSettleMsg(wmSettleMsgBo);

        wmSettleMsgSenderService.send();

        //mafka发送消息
        List<ChangedEvent> settleChangedEventList = Lists.newArrayList();
        diffSettle(settleChangedEventList, newWmSettleAudited, oldWmSettleAudited, ChangedEvent.UPDATE);

    CustomerUpdateMessage customerUpdateMessage = new CustomerUpdateMessage(
            WmCustomerHistVersionConstant.VersionMessageBizTypeEnum.CUTOMER_SETTLE_EFFECT_OPEN_WALLET
                    .getIndex());
    customerUpdateMessage.setWmCustomerId(wmCustomerId);
    customerUpdateMessage.setCtime((int) (System.currentTimeMillis() / 1000));
    customerUpdateMessage.getChangeEvents()
            .put(CustomerUpdateMessage.SETTLE, settleChangedEventList);
    customerUpdateMessage.setOpUid(opUid);
    customerUpdateMessage.setOpUname(opUname);
    wmCustomerUpdateSendService.sendSettleMsg(customerUpdateMessage);
  }

    public CustomerUpdateMessage genDiffMessage(int wmCustomerId,
                                                List<ChangedEvent> settle, int opUid, String opUname, int bizType) {
        CustomerUpdateMessage customerUpdateMessage = new CustomerUpdateMessage(bizType);
        customerUpdateMessage.setWmCustomerId(wmCustomerId);
        customerUpdateMessage.setCtime((int) (System.currentTimeMillis() / 1000));
        customerUpdateMessage.getChangeEvents().put(CustomerUpdateMessage.SETTLE, settle);
        customerUpdateMessage.setOpUid(opUid);
        customerUpdateMessage.setOpUname(opUname);
        return customerUpdateMessage;
    }

    public void unbindSettle(int wmCustomerId, int opUid, String opUname) throws WmCustomerException {

      if(wmHeronSettleGrayService.routHeronSettleGray(wmCustomerId,opUid)){
          LOGGER.info("unbindSettle灰度 wmCustomerId:[{}]", wmCustomerId);
          BaseOperateDataInputDTO baseOperateDataInputDTO = new BaseOperateDataInputDTO();
          baseOperateDataInputDTO.setOpId(opUid);
          baseOperateDataInputDTO.setOpName(opUname);
          baseOperateDataInputDTO.setChannel(CChannelTypeEnum.CHANNEL_TYPE_WAIMAI.getValue());

          try {
              wmSettleExportThriftService.unbindSettle(wmCustomerId,baseOperateDataInputDTO);
          } catch (WmHeronSettleException | TException e) {
              LOGGER.error("unbindSettle灰度异常 wmCustomerId:[{}]", wmCustomerId,e);
          }


      }else{

        LOGGER.info("#unbindSettle,wmCustomerId={},opUid={},opUname={}", wmCustomerId, opUid, opUname);

        List<WmSettleAudited> toDeleteAuditedList = wmSettleService.getWmSettleAuditedByWmCustomerId(wmCustomerId,false);

        List<Integer> wmSettleAuditedIdList = wmSettleAuditedDBMapper
                .getWmSettleIdListByWmCustomerId(wmCustomerId);

    List<Long> wmPoiIdList = Lists.newArrayList();

    if (CollectionUtils.isNotEmpty(wmSettleAuditedIdList)) {
      //原线上结算关联的门店
      wmPoiIdList = Lists.newArrayList(Lists.transform(wmPoiSettleAuditedDBMapper
          .getWmPoiIdListByWmSettleIdList(wmSettleAuditedIdList), new Function<Integer, Long>() {
        @Nullable
        @Override
        public Long apply(@Nullable Integer input) {
          return input.longValue();
        }
      }));

      //删线上结算-门店关联关系
      wmPoiSettleAuditedDBMapper.deleteByWmSettleIdList(wmSettleAuditedIdList);
    }
    //删线上结算
    wmSettleAuditedDBMapper.deleteWmSettleByWmCustomerId(wmCustomerId);

        List<Integer> wmSettleIdList =
                wmSettleDBMapper.getWmSettleIdListByCustomerId(wmCustomerId);

        if (CollectionUtils.isNotEmpty(wmSettleIdList)) {
            //删除线下结算-门店关联关系
            wmPoiSettleDBMapper.deleteByWmSettleIdList(wmSettleIdList);
            //删除线下结算
            wmSettleDBMapper.batchDeleteByWmSettleIdList(wmSettleIdList);
        }

        //消息通知结算
        if (CollectionUtils.isNotEmpty(toDeleteAuditedList)) {
            WmSettleMsgBo wmSettleMsgBo = null;
            WmSettleMsgSender wmSettleMsgSenderService = new WmSettleMsgSender();
            for (WmSettleAudited toDeleteWmSettleAudited : toDeleteAuditedList) {
                wmSettleMsgBo = new WmSettleMsgBo();
                wmSettleMsgBo.setWmContractId(wmCustomerId);
                wmSettleMsgBo.setWmCustomerId(wmCustomerId);
                wmSettleMsgBo.setWmSettleAuditedId(toDeleteWmSettleAudited.getId());
                wmSettleMsgBo.setWmSettleId(toDeleteWmSettleAudited.getWm_settle_id());
                wmSettleMsgBo.setType(WmSettleMsgBo.OpType.DELETE);
                //settle
                wmSettleMsgSenderService.addSettleMsg(wmSettleMsgBo);
                //poi_settle
                wmSettleMsgSenderService.addPoiSettleMsg(wmSettleMsgBo, toDeleteWmSettleAudited.getWmPoiIdList(), Lists.<Integer>newArrayList());
            }
            wmSettleMsgSenderService.send();
        }

    //producelog
    for (Long temp : wmPoiIdList) {
      settleProduceNotifyService
          .notifyDeleteWithRemark(temp, ProduceNotifyService.SETTLEMENT, opUid, opUname,"");
    }
      }
  }

    public void unbindSettlePoi(int wmCustomerId, List<Long> offlineWmPoiIdList, int opUid,
                                String opUname) throws WmCustomerException {
        if (wmHeronSettleGrayService.routHeronSettleGray(wmCustomerId, opUid)) {
            LOGGER.info("unbindSettlePoi灰度 wmCustomerId:[{}],offlineWmPoiIdList:[{}]", wmCustomerId, JSON.toJSONString(offlineWmPoiIdList));
            // 结算接口迁移，下掉此接口
            if (MccConfig.getOfflineSettleSwitch()) {
                return;
            }

            BaseOperateDataInputDTO baseOperateDataInputDTO = new BaseOperateDataInputDTO();
            baseOperateDataInputDTO.setOpId(opUid);
            baseOperateDataInputDTO.setOpName(opUname);
            baseOperateDataInputDTO.setChannel(CChannelTypeEnum.CHANNEL_TYPE_WAIMAI.getValue());

            try {
               List<Integer> wmPoiIdList = Lists.newArrayList(Lists.transform(offlineWmPoiIdList, new Function<Long, Integer>() {
                   @Nullable
                   @Override
                   public Integer apply(@Nullable Long input) {
                       return input.intValue();
                   }
               }));

                wmSettleExportThriftService.unbindSettlePoi(wmCustomerId, wmPoiIdList,baseOperateDataInputDTO);
            } catch (WmHeronSettleException | TException e) {
                LOGGER.error("unbindSettlePoi灰度异常 wmCustomerId:[{}]", wmCustomerId, e);
            }

        }else{

            LOGGER.info("#unbindSettlePoi,wmCustomerId={},offlineWmPoiIdList={},opUid={},opUname={}",
                wmCustomerId, offlineWmPoiIdList, opUid, opUname);
        List<Long> offlineWmPoiIdListBak = Lists.newArrayList(offlineWmPoiIdList);

    List<Integer> wmSettleAuditedIdList = wmSettleAuditedDBMapper
        .getWmSettleIdListByWmCustomerId(wmCustomerId);
    List<Long> wmPoiIdList = Lists.newArrayList();
    List<WmPoiSettleAuditedDB> matchList = Lists.newArrayList();

        List<Integer> toDeleteWmSettleIdList = Lists.newArrayList();
        List<Integer> toDeleteWmSettleIdAuditedList = Lists.newArrayList();

    if (CollectionUtils.isNotEmpty(wmSettleAuditedIdList)) {
      //原线上结算关联的门店
      wmPoiIdList = Lists.newArrayList(Lists.transform(wmPoiSettleAuditedDBMapper
          .getWmPoiIdListByWmSettleIdList(wmSettleAuditedIdList), new Function<Integer, Long>() {
        @Nullable
        @Override
        public Long apply(@Nullable Integer input) {
          return input.longValue();
        }
      }));
      offlineWmPoiIdListBak.retainAll(wmPoiIdList);

            if (CollectionUtils.isNotEmpty(offlineWmPoiIdListBak)) {

                //构造wmSettleId-wmPoiId映射关系
                List<WmPoiSettleAuditedDB> poiSettleAuditedDBList = wmPoiSettleAuditedDBMapper.getBySettleIdList(wmSettleAuditedIdList);
                Map<Integer, List<Long>> settlePoiAggr = Maps.newHashMap();
                Integer wmSettleIdTemp = null;
                List<Long> wmPoiIdTempList = null;
                for (WmPoiSettleAuditedDB temp : poiSettleAuditedDBList) {
                    wmSettleIdTemp = temp.getWm_settle_id();
                    wmPoiIdTempList = settlePoiAggr.get(wmSettleIdTemp);
                    if (wmPoiIdTempList == null) {
                        settlePoiAggr.put(wmSettleIdTemp, Lists.<Long>newArrayList((long) temp.getWm_poi_id()));
                    } else {
                        wmPoiIdTempList.add((long) temp.getWm_poi_id());
                    }
                }

                //构造待删除的线上结算id
                for (Entry<Integer, List<Long>> temp : settlePoiAggr.entrySet()) {
                    wmPoiIdTempList = temp.getValue();
                    wmPoiIdTempList.removeAll(offlineWmPoiIdListBak);
                    if (CollectionUtils.isEmpty(wmPoiIdTempList)) {
                        toDeleteWmSettleIdAuditedList.add(temp.getKey());
                    }
                }

                LOGGER.info("#offlineWmPoiIdListBak={}", JSONObject.toJSON(offlineWmPoiIdListBak));
                LOGGER.info("#toDeleteWmSettleIdAuditedList={}", JSONObject.toJSON(toDeleteWmSettleIdAuditedList));

                matchList = wmPoiSettleAuditedDBMapper
                        .getByWmSettleIdListAndWmPoiIdList(wmSettleAuditedIdList, offlineWmPoiIdListBak);

                //解绑线上关联门店
                wmPoiSettleAuditedDBMapper
                        .deleteByWmSettleIdListAndWmPoiIdList(wmSettleAuditedIdList, offlineWmPoiIdListBak);

                //删除线上结算
                if (CollectionUtils.isNotEmpty(toDeleteWmSettleIdAuditedList)) {
                    wmSettleAuditedDBMapper.deleteByWmSettleIdList(toDeleteWmSettleIdAuditedList);
                }
            }
    }


    List<Integer> wmSettleIdList =
        wmSettleDBMapper.getWmSettleIdListByCustomerId(wmCustomerId);
    if (CollectionUtils.isNotEmpty(wmSettleIdList)) {
      wmPoiIdList = Lists.newArrayList(
          Lists.transform(wmPoiSettleDBMapper.getWmPoiIdListByWmSettleIdList(wmSettleIdList),
              new Function<Integer, Long>() {
                @Nullable
                @Override
                public Long apply(@Nullable Integer input) {
                  return input.longValue();
                }
              }));
      offlineWmPoiIdList.retainAll(wmPoiIdList);
      if (CollectionUtils.isNotEmpty(offlineWmPoiIdList))
      {
        //构造wmSettleId-wmPoiId映射关系
        List<WmPoiSettleDB> poiSettleDBList = wmPoiSettleDBMapper
            .getByWmSettleIdList(wmSettleIdList);
        Map<Integer,List<Long>> settlePoiAggr = Maps.newHashMap();
        Integer wmSettleIdTemp = null;
        List<Long> wmPoiIdTempList = null;
        for(WmPoiSettleDB temp : poiSettleDBList){
          wmSettleIdTemp = temp.getWm_settle_id();
          wmPoiIdTempList = settlePoiAggr.get(wmSettleIdTemp);
          if(wmPoiIdTempList == null){
            settlePoiAggr.put(wmSettleIdTemp,Lists.<Long>newArrayList((long)temp.getWm_poi_id()));
          }else{
            wmPoiIdTempList.add((long)temp.getWm_poi_id());
          }
        }

                //构造待删除的线下结算id
                for (Entry<Integer, List<Long>> temp : settlePoiAggr.entrySet()) {
                    wmPoiIdTempList = temp.getValue();
                    wmPoiIdTempList.removeAll(offlineWmPoiIdList);
                    if (CollectionUtils.isEmpty(wmPoiIdTempList)) {
                        toDeleteWmSettleIdList.add(temp.getKey());
                    }
                }

                LOGGER.info("#offlineWmPoiIdList={}", JSONObject.toJSON(offlineWmPoiIdList));
                LOGGER.info("#toDeleteWmSettleIdList={}", JSONObject.toJSON(toDeleteWmSettleIdList));

                //解绑线下关联门店
                wmPoiSettleDBMapper
                        .deleteByWmSettleIdListAndWmPoiIdList(wmSettleIdList, offlineWmPoiIdList);

                //删除线下结算
                if (CollectionUtils.isNotEmpty(toDeleteWmSettleIdList)) {
                    wmSettleDBMapper.batchDeleteByWmSettleIdList(toDeleteWmSettleIdList);
                }

                //删除待发起的手动任务
                wmEcontractSignBzService.cancelManualTaskByCustomerIdAndModule(wmCustomerId,
                  EcontractTaskApplyTypeEnum.SETTLE.getName());
            }
        }

        //消息通知结算
        if (CollectionUtils.isNotEmpty(matchList)) {
            Map<Integer, List<Integer>> settlePoiMap = Maps.newHashMap();
            for (WmPoiSettleAuditedDB temp : matchList) {
                if (settlePoiMap.containsKey(temp.getWm_settle_id())) {
                    settlePoiMap.get(temp.getWm_settle_id()).add(temp.getWm_poi_id());
                } else {
                    settlePoiMap
                            .put(temp.getWm_settle_id(), Lists.<Integer>newArrayList(temp.getWm_poi_id()));
                }
            }
            WmSettleMsgSender wmSettleMsgSenderService = new WmSettleMsgSender();
            WmSettleMsgBo wmSettleMsgBo = null;
            for (Entry<Integer, List<Integer>> wmSettleIdEntry : settlePoiMap.entrySet()) {
                wmSettleMsgBo = new WmSettleMsgBo();
                wmSettleMsgBo.setWmCustomerId(wmCustomerId);
                wmSettleMsgBo.setWmContractId(wmCustomerId);
                wmSettleMsgBo.setWmSettleId(wmSettleIdEntry.getKey());
                //poiSettle
                wmSettleMsgSenderService.addPoiSettleMsg(wmSettleMsgBo, wmSettleIdEntry.getValue(),
                        Lists.<Integer>newArrayList());
            }

            for (Integer temp : toDeleteWmSettleIdAuditedList) {
                wmSettleMsgBo = new WmSettleMsgBo();
                wmSettleMsgBo.setWmContractId(wmCustomerId);
                wmSettleMsgBo.setWmCustomerId(wmCustomerId);
                wmSettleMsgBo.setWmSettleId(temp);
                wmSettleMsgBo.setType(WmSettleMsgBo.OpType.DELETE);
                //settle
                wmSettleMsgSenderService.addSettleMsg(wmSettleMsgBo);
            }
            wmSettleMsgSenderService.send();
        }

        //producelog
        for (Long temp : offlineWmPoiIdListBak) {
            settleProduceNotifyService
                    .notifyDeleteWithRemark(temp, ProduceNotifyService.SETTLEMENT, opUid, opUname, "");
        }
        }
    }

    private boolean canSetupEffect(byte status) {
        return status != WmSettleConstant.SETTLE_STATUS_EFFECT && status != WmSettleConstant.SETTLE_STATUS_CONFIRM_FAIL;
    }

    public RetrySmsResponse reSendMsgByWmPoiId(long wmPoiId) throws WmCustomerException {
        WmCustomerDB wmCustomerDB = wmCustomerService.selectCustomerByWmPoiId(wmPoiId);
        if (wmCustomerDB == null) {
            throw new WmCustomerException(WmContractErrorCodeConstant.BUSINESS_ERROR, "门店没有关联客户");
        }
        return reSendMsg(wmCustomerDB.getId());
    }

    public BooleanResult bankCardValidateCallbackByThrift(int wmSetleId, String status, String reason) throws TException, WmCustomerException{
        wmBankCardValidationService.validateCallbackByThrift(wmSetleId, status, reason);
        return new BooleanResult(true);
    }

    /**
     * 支付结算ID更新
     *
     * @param wmSettleId
     * @return
     */
    public boolean updateWmSettlePaySettleId(int wmSettleId, long paySettleId, int opUid, String opUname){
        LOGGER.info("更新支付结算ID wmSettleId:{} paySettleId:{} opUid:{} opUname:{}", wmSettleId, paySettleId, opUid, opUname);
        wmSettleAuditedDBMapper.updateWmSettlePaySettleId(wmSettleId, paySettleId);
        return true;
    }

    public List<WmPoiSettleAuditedDB> batchQueryPoiSettleAudited(List<Long> poiList) {
        return wmPoiSettleAuditedDBMapper.batchQueryPoiSettleAudited(poiList);
    }

    public void batchUnbindOfflinePoi(int wmCustomerId, String wmPoiIds, int opUid, String opUname)
            throws WmCustomerException {
        LOGGER.info("#batchUnbindOfflinePoi,wmCustomerId={},wmPoiIds={},opUid={},opUname={}",
                wmCustomerId, wmPoiIds, opUid, opUname);
        wmSettleService.batchUnbindOfflinePoi(wmCustomerId, wmPoiIds,opUid,opUname);
    }

    public BooleanResult processSwitchCustomerEffect(int wmCustomerId,
            Map<Long, String> wmSettleSwitchInfo, int opUid, String opUname, long switchTaskId) throws WmCustomerException{
        return wmSettleEffectDomainService.processSwitchCustomerEffect(wmCustomerId,wmSettleSwitchInfo,opUid,opUname,switchTaskId);
    }

    public boolean checkSettleInputAuthority(int wmCustomerId, int userId) throws WmCustomerException, TException, WmServerException {
        WmCustomerDB wmCustomerDB = wmCustomerService.selectCustomerById(wmCustomerId);
        if (wmCustomerDB == null) {
            return false;
        }
        //1.判断用户是否有总部权限
        if (WmEmployUtils.isHQ(userId)) {
            LOGGER.info("用户为总部权限,userId={}", userId);
            return true;
        }
        //2.判断客户责任人是否是自己和自己的子集
        List<Integer> downUidList = wmVirtualOrgService.getUidsByUid(userId, WmOrgConstant.QUERY_ALL_SOURCE, WmVirtualOrgRecursiveTypeEnum.TOP_DOWN_NO_SELF.getType());
        downUidList.add(userId);
        LOGGER.info("用户userId={},查询用户子集downUidList={}", userId, JSONObject.toJSONString(downUidList));
        if (downUidList.contains(wmCustomerDB.getOwnerUid())) {
            return true;
        }
        //3.判断该客户是否绑定唯一门店，并且门店责任人是该用户
        List<Long> wmPoiIdList = wmCustomerPoiService.selectWmPoiIdsByCustomerIdFromDBAndSwitchCentre(wmCustomerDB.getId());
        if (CollectionUtils.isNotEmpty(wmPoiIdList) && wmPoiIdList.size() == 1) {
            List<WmPoiDomain> wmPoiDomains = wmPoiClient.pageGetWmPoiByWmPoiIdList(wmPoiIdList, Sets.newHashSet(WmPoiFieldQueryConstant.WM_POI_FIELD_OWNER_UID, WmPoiFieldQueryConstant.WM_POI_FIELD_SELLER_UID));
            if (wmPoiDomains.size() != 1) {
                return false;
            }
            WmPoiDomain wmPoiDomainEntity = wmPoiDomains.get(0);
            if (wmPoiDomainEntity.getOwnerUid() == userId) {
                LOGGER.info("该客户仅绑定一家门店且门店责任人是该用户,userId={}", userId);
                return true;
            }
            if (downUidList.contains(wmPoiDomainEntity.getSellerUid())) {
                LOGGER.info("该客户仅绑定一家门店且品牌责任人是该用户,seller userId={}", wmPoiDomainEntity.getSellerUid());
                return true;
            }
        } else if (CollectionUtils.isNotEmpty(wmPoiIdList) && wmPoiIdList.size() > 1) {
            Boolean needCheck = true;
            Long poiOwnerUid = null;
            Set<Integer> sellerIdSet = Sets.newHashSet();
            List<WmPoiDomain> wmPoiDomainEntities = wmPoiClient.pageGetWmPoiByWmPoiIdList(wmPoiIdList, Sets.newHashSet(WmPoiFieldQueryConstant.WM_POI_FIELD_OWNER_UID, WmPoiFieldQueryConstant.WM_POI_FIELD_SELLER_UID));
            for (WmPoiDomain temp : wmPoiDomainEntities) {
                if (temp.getSellerUid() > 0) {
                    sellerIdSet.add(temp.getSellerUid());
                }
                if (poiOwnerUid == null) {
                    poiOwnerUid = temp.getOwnerUid();
                } else if (poiOwnerUid != temp.getOwnerUid()) {
                    LOGGER.info("该客户绑定多家门店， 门店责任人不是同一个人");
                    needCheck = false;
                }
            }
            if (needCheck && poiOwnerUid != null && poiOwnerUid == userId) {
                LOGGER.info("该客户绑定多家门店，门店责任人是同一个人，且是该用户userId={}", userId);
                return true;
            }
            LOGGER.info("用户userId={}, 查询门店品牌责任人列表={}", userId, JSONObject.toJSONString(sellerIdSet));
            if (!Sets.intersection(Sets.newHashSet(downUidList), sellerIdSet).isEmpty()) {
                LOGGER.info("该客户绑定多家门店，登录人或者登录人下级为门店品牌责任人 userId={}", userId);
                return true;
            }
        }
        return false;
    }
}
