package com.sankuai.meituan.waimai.customer.service.customer;

import com.alibaba.fastjson.JSON;
import com.sankuai.meituan.waimai.customer.config.MccCustomerConfig;
import com.sankuai.meituan.waimai.customer.constant.WmCustomerConstant;
import com.sankuai.meituan.waimai.customer.ddd.adapter.WmPoiQueryAdapter;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerDB;
import com.sankuai.meituan.waimai.customer.service.gray.WmCustomerGrayService;
import com.sankuai.meituan.waimai.thrift.constants.WmPoiFieldQueryConstant;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerConstants;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerRealTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.CustomerMscUsedPoiDTO;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.CustomerRealTypeSpInfoBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.msc.MscCustomerPoiCheckDetail;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.msc.MscUsedPoiCntCheckReqDTO;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.msc.MscUsedPoiCntCheckResDTO;
import com.sankuai.meituan.waimai.thrift.domain.WmPoiAggre;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.curator.shaded.com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 20230922
 * @desc 美食城客户公共校验服务
 */
@Slf4j
@Service
public class MscCustomerCheckService {

    @Autowired
    private WmCustomerPlatformDataParseService wmCustomerPlatformDataParseService;

    @Autowired
    private WmCustomerGrayService wmCustomerGrayService;

    @Autowired
    private WmCustomerMSCLabelService customerMSCLabelService;

    @Autowired
    private WmPoiQueryAdapter wmPoiQueryAdapter;

    @Autowired
    private WmCustomerService wmCustomerService;

    protected static final Set<String> WM_POI_FIELDS = com.google.common.collect.Sets.newHashSet(WmPoiFieldQueryConstant.WM_POI_FIELD_WM_POI_ID,
            WmPoiFieldQueryConstant.WM_POI_FIELD_NAME,
            WmPoiFieldQueryConstant.WM_POI_FIELD_FIRST_TAG,
            WmPoiFieldQueryConstant.WM_POI_FIELD_POI_BIZ_TYPES,
            WmPoiFieldQueryConstant.WM_POI_FIELD_BIZ_ORG_CODE,
            WmPoiFieldQueryConstant.WM_POI_FIELD_LABEL_IDS,
            WmPoiFieldQueryConstant.WM_POI_FIELD_CITY_LOCATION_ID,
            WmPoiFieldQueryConstant.WM_POI_FIELD_VALID,
            WmPoiFieldQueryConstant.WM_POI_FIELD_SUB_WM_POI_TYPE
    );


    /**
     * 获取客户ID的已占用档口数校验
     *
     * @param mscUsedPoiCntCheckReqDTO
     * @return
     */
    public MscUsedPoiCntCheckResDTO getMscUsedPoiCntCheckRes(MscUsedPoiCntCheckReqDTO mscUsedPoiCntCheckReqDTO) throws WmCustomerException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.service.customer.MscCustomerCheckService.getMscUsedPoiCntCheckRes(com.sankuai.meituan.waimai.thrift.customer.domain.customer.msc.MscUsedPoiCntCheckReqDTO)");
        MscUsedPoiCntCheckResDTO mscUsedPoiCntCheckResDTO = new MscUsedPoiCntCheckResDTO();
        mscUsedPoiCntCheckResDTO.setCheckResult(true);
        Integer customerId = mscUsedPoiCntCheckReqDTO.getCustomerId();

        //命中美食城一店多开灰度则不执行校验
        if (wmCustomerGrayService.checkHitMscCustomerUsedPoiGray()) {
            return mscUsedPoiCntCheckResDTO;
        }

        WmCustomerDB wmCustomerDB = wmCustomerPlatformDataParseService.selectCustomerFromPlatformByIdFromSlave(customerId);
        if (wmCustomerDB == null) {
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "未查询到有效客户");
        }
        //非美食城客户类型不校验
        if (wmCustomerDB.getCustomerRealType() != null
                && !wmCustomerDB.getCustomerRealType().equals(CustomerRealTypeEnum.MEISHICHENG.getValue())) {
            return mscUsedPoiCntCheckResDTO;
        }
        //美食城客户如果有资质共用标则不校验
        if (customerMSCLabelService.checkHasQuaComCustomerLabel(wmCustomerDB.getMtCustomerId())) {
            return mscUsedPoiCntCheckResDTO;
        }

        Long notChildPoiCnt = countNotChildPoi(mscUsedPoiCntCheckReqDTO.getWmPoiIdList());
        //非子门店总数为0则直接返回
        if (notChildPoiCnt == 0L) {
            return mscUsedPoiCntCheckResDTO;
        }

        //判断美食城客户类型是否录入档口数
        String customerRealTypeSpInfoStr = wmCustomerDB.getCustomerRealTypeSpInfo();
        CustomerRealTypeSpInfoBo customerRealTypeSpInfoBo = JSON.parseObject(customerRealTypeSpInfoStr, CustomerRealTypeSpInfoBo.class);

        //美食城信息为空 或 档口数未填写
        if (customerRealTypeSpInfoBo == null || customerRealTypeSpInfoBo.getFoodCityPoiCount() == null) {
            mscUsedPoiCntCheckResDTO.setBindPoiCnt(mscUsedPoiCntCheckReqDTO.getWmPoiIdList().size());
            //允许档口数不填写开关开启
            if (MccCustomerConfig.getMscAllowNoPoiCntSwitch()) {
                return mscUsedPoiCntCheckResDTO;
            } else {
                mscUsedPoiCntCheckResDTO.setCheckResult(false);
                mscUsedPoiCntCheckResDTO.setFailMsg(String.format("客户(%s)未填写档口数或填写的档口数未生效，请确保客户档口数量维护完成后再进行门店绑定", wmCustomerDB.getMtCustomerId()));
            }
        } else {
            //档口数已填写的校验
            mscUsedPoiCntCheckResDTO = getRecordedMsdUsePoiCntCheckResult(mscUsedPoiCntCheckReqDTO, wmCustomerDB.getMtCustomerId());
        }
        return mscUsedPoiCntCheckResDTO;
    }


    /**
     * 根据门店当前标签情况判断是否子门店
     *
     * @param poiLabels
     * @return
     */
    public Boolean checkChildPoiByTagId(String poiLabels) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_2", "com.sankuai.meituan.waimai.customer.service.customer.MscCustomerCheckService.checkChildPoiByTagId(java.lang.String)");
        if (StringUtils.isEmpty(poiLabels)) {
            return false;
        }
        List<Integer> poiRelTagIds = Arrays.stream(poiLabels.split(",")).map(Integer::valueOf).collect(Collectors.toList());
        if (!Collections.disjoint(poiRelTagIds, MccCustomerConfig.getSubPoiTagId())) {
            return true;
        }
        return false;
    }

    /**
     * 根据门店子门店类型判断是否子门店
     */
    public Boolean checkChildPoiBySubPoiType(Integer subPoiType) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.service.customer.MscCustomerCheckService.checkChildPoiBySubPoiType(java.lang.Integer)");
        if (subPoiType == null) {
            return false;
        }
        if (!WmCustomerConstant.POI_NOT_SUB_POI_TYPE.equals(subPoiType)) {
            return true;
        }
        return false;
    }

    /**
     * 根据门店ID列表设置返回结果
     *
     * @param aggreList
     * @return
     */
    private List<MscCustomerPoiCheckDetail> listMscCustomerPoiCheckDetail(List<WmPoiAggre> aggreList) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_1", "com.sankuai.meituan.waimai.customer.service.customer.MscCustomerCheckService.listMscCustomerPoiCheckDetail(java.util.List)");
        List<MscCustomerPoiCheckDetail> mscCustomerPoiCheckDetailList = Lists.newArrayList();
        for (WmPoiAggre wmPoiAggre : aggreList) {
            MscCustomerPoiCheckDetail mscCustomerPoiCheckDetail = new MscCustomerPoiCheckDetail();
            Boolean childPoiFlag = false;
            if (MccCustomerConfig.getSubPoiIdentifySwitch()) {
                childPoiFlag = checkChildPoiBySubPoiType(wmPoiAggre.getSub_wm_poi_type());
            } else {
                childPoiFlag = checkChildPoiByTagId(wmPoiAggre.getLabel_ids());
            }

            mscCustomerPoiCheckDetail.setWmPoiId(wmPoiAggre.getWm_poi_id());
            mscCustomerPoiCheckDetail.setWmPoiName(wmPoiAggre.getName());
            mscCustomerPoiCheckDetail.setChildPoiFlag(childPoiFlag);
            mscCustomerPoiCheckDetailList.add(mscCustomerPoiCheckDetail);
        }
        return mscCustomerPoiCheckDetailList;
    }

    /**
     * 已录入情况下获取校验结果
     *
     * @return
     */
    private MscUsedPoiCntCheckResDTO getRecordedMsdUsePoiCntCheckResult(MscUsedPoiCntCheckReqDTO mscUsedPoiCntCheckReqDTO, Long mtCustomerId)
            throws WmCustomerException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_1", "com.sankuai.meituan.waimai.customer.service.customer.MscCustomerCheckService.getRecordedMsdUsePoiCntCheckResult(MscUsedPoiCntCheckReqDTO,Long)");
        MscUsedPoiCntCheckResDTO mscUsedPoiCntCheckResDTO = new MscUsedPoiCntCheckResDTO();
        mscUsedPoiCntCheckResDTO.setCheckResult(true);
        mscUsedPoiCntCheckResDTO.setBindPoiCnt(mscUsedPoiCntCheckReqDTO.getWmPoiIdList().size());
        //本批次操作门店详细信息,计算子门店数
        List<MscCustomerPoiCheckDetail> customerPoiCheckDetails = Lists.newArrayList();
        List<List<Long>> wmPoiIdPartList = Lists.partition(mscUsedPoiCntCheckReqDTO.getWmPoiIdList(), 100);
        long notChildPoiCntAll = 0;
        for (List<Long> wmPoiIds : wmPoiIdPartList) {
            List<WmPoiAggre> wmPoiAggreList = wmPoiQueryAdapter.getWmPoiAggre(wmPoiIds, WM_POI_FIELDS);
            //计算非子门店数
            long notChildPoiCnt = wmPoiAggreList.stream()
                    .filter(this::isNotSubPoi)
                    .count();
            //设置所有门店的列表信息
            customerPoiCheckDetails.addAll(listMscCustomerPoiCheckDetail(wmPoiAggreList));
            notChildPoiCntAll = notChildPoiCntAll + notChildPoiCnt;
        }
        //非子门店总数=0则直接返回
        if (notChildPoiCntAll == 0) {
            return mscUsedPoiCntCheckResDTO;
        }

        CustomerMscUsedPoiDTO customerMscUsedPoiDTO = wmCustomerService.getMscUsedPoiDTO(mscUsedPoiCntCheckReqDTO.getCustomerId());
        //查不到占用门店数则不卡点
        if (customerMscUsedPoiDTO == null || customerMscUsedPoiDTO.getUsedPoiCnt() == null) {
            return mscUsedPoiCntCheckResDTO;
        }
        Integer usedPoiCnt = customerMscUsedPoiDTO.getUsedPoiCnt().intValue();
        Integer foodCityPoiCount = customerMscUsedPoiDTO.getAllPoiCnt().intValue();
        //非子门店数>美食城档口数-已占用档口数
        if (notChildPoiCntAll > (foodCityPoiCount - usedPoiCnt)) {
            mscUsedPoiCntCheckResDTO.setCheckResult(false);
            if (mscUsedPoiCntCheckReqDTO.getWmPoiIdList().size() == 1) {
                String failMsg = String.format("客户档口数%s，已占用档口数%s，此门店为非子门店，无法绑定。请调整客户档口数量，或下线需下线门店，或取消相关且换任务后再继续",
                        foodCityPoiCount, usedPoiCnt);
                mscUsedPoiCntCheckResDTO.setFailMsg(failMsg);
            } else {
                //需要按照是否子门重排序
                Collections.sort(customerPoiCheckDetails, (o1, o2) -> o1.getChildPoiFlag().compareTo(o2.getChildPoiFlag()));
                mscUsedPoiCntCheckResDTO.setBindPoiList(customerPoiCheckDetails);
                String failMsg = String.format("客户ID(%s)档口数%s，已占用档口数%s，本次任务涉及%s个非子门店，超出可用档口数，无法创建。\n" +
                        "以下为本次绑定门店，请确认是否调整", mtCustomerId, foodCityPoiCount, usedPoiCnt, notChildPoiCntAll);
                mscUsedPoiCntCheckResDTO.setFailMsg(failMsg);
            }
        }
        return mscUsedPoiCntCheckResDTO;
    }

    /**
     * 计算非子门店总数
     *
     * @param wmPoiIdList
     * @return
     */
    private Long countNotChildPoi(List<Long> wmPoiIdList) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.service.customer.MscCustomerCheckService.countNotChildPoi(java.util.List)");
        List<List<Long>> wmPoiIdPartList = Lists.partition(wmPoiIdList, 100);
        Long notChildPoiCntAll = 0L;
        for (List<Long> wmPoiIds : wmPoiIdPartList) {
            List<WmPoiAggre> wmPoiAggreList = wmPoiQueryAdapter.getWmPoiAggre(wmPoiIds, WM_POI_FIELDS);
            //计算非子门店数
            long notChildPoiCnt = wmPoiAggreList.stream()
                    .filter(this::isNotSubPoi).count();
            notChildPoiCntAll = notChildPoiCntAll + notChildPoiCnt;
        }

        return notChildPoiCntAll;
    }

    /**
     * 判断是否非子门店
     * @param wmPoiAggre 门店聚合信息
     * @return true-非子门店 false-子门店
     */
    private boolean isNotSubPoi(WmPoiAggre wmPoiAggre) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.customer.MscCustomerCheckService.isNotSubPoi(com.sankuai.meituan.waimai.thrift.domain.WmPoiAggre)");
        // 根据开关判断使用哪种方式识别子门店
        if (MccCustomerConfig.getSubPoiIdentifySwitch()) {
            return WmCustomerConstant.POI_NOT_SUB_POI_TYPE.equals(wmPoiAggre.getSub_wm_poi_type());
        }

        // 通过标签判断是否子门店
        if (StringUtils.isEmpty(wmPoiAggre.getLabel_ids())) {
            return true;
        }

        List<Integer> poiLabelIds = Arrays.stream(wmPoiAggre.getLabel_ids().split(","))
                .map(Integer::valueOf)
                .collect(Collectors.toList());

        return Collections.disjoint(poiLabelIds, MccCustomerConfig.getSubPoiTagId());
    }
}
