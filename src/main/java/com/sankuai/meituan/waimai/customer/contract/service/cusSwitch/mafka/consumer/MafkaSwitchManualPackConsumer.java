package com.sankuai.meituan.waimai.customer.contract.service.cusSwitch.mafka.consumer;


import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.alibaba.fastjson.JSON;
import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.meituan.mafka.client.consumer.IMessageListener;
import com.meituan.mafka.client.message.MafkaMessage;
import com.meituan.mafka.client.message.MessagetContext;
import com.sankuai.meituan.util.ConfigUtilAdapter;
import com.sankuai.meituan.waimai.customer.contract.service.cusSwitch.WmContractForSwitchService;
import com.sankuai.meituan.waimai.poibizflow.constant.customerSwitch.CustomerSwitchOptionsEnum;
import com.sankuai.meituan.waimai.poibizflow.thrift.switcher.mq.CustomerSwitchModuleReadyMsg;
import com.sankuai.meituan.waimai.poibizflow.thrift.switcher.mq.CustomerSwitchPackInfo;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class MafkaSwitchManualPackConsumer implements IMessageListener {

    private static final Logger LOG = LoggerFactory.getLogger(MafkaSwitchManualPackConsumer.class);

    @Autowired
    private WmContractForSwitchService wmContractForSwitchService;

    /**
     * 客户切换不下线-发起手动打包
     *
     * @return
     */
    @Override
    public ConsumeStatus recvMessage(MafkaMessage mafkaMessage, MessagetContext context) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_1", "com.sankuai.meituan.waimai.customer.contract.service.cusSwitch.mafka.consumer.MafkaSwitchManualPackConsumer.recvMessage(MafkaMessage,MessagetContext)");
        if (!ConfigUtilAdapter.getBoolean("if_receive_switch_manual_pack_data", true)) {
            return ConsumeStatus.CONSUME_SUCCESS;
        }
        if (mafkaMessage != null) {
            String messageBody = (String) mafkaMessage.getBody();
            LOG.info("MafkaSwitchManualPackConsumer#recvMessage, messageBody:{}", messageBody);
            try {
                CustomerSwitchModuleReadyMsg readyMsg = JSON.parseObject(messageBody, CustomerSwitchModuleReadyMsg.class);
                //无自动打包
                if (!readyMsg.getSwitchOptions().contains(CustomerSwitchOptionsEnum.TO_CUSTOMER_PACK_SIGN_AUTO)) {
                    return ConsumeStatus.CONSUME_SUCCESS;
                }
                List<CustomerSwitchPackInfo> packInfos = readyMsg.getSignTasks();
                List<Long> manualTaskIds = new ArrayList<>();
                List<String> manualTaskIdStrs = new ArrayList<>();
                for (CustomerSwitchPackInfo packInfo : packInfos) {
                    manualTaskIdStrs.addAll(Arrays.asList(packInfo.getIds().split(",")));
                }
                manualTaskIds = manualTaskIdStrs.stream().map(s -> Long.parseLong(s.trim())).collect(Collectors.toList());
                wmContractForSwitchService.handleManualPack(readyMsg.getTaskId(), manualTaskIds, readyMsg.getOpUid());
            } catch (Exception e) {
                LOG.error("MafkaSwitchManualPackConsumer#recvMessage Exception messageBody={}", messageBody, e);
                return ConsumeStatus.RECONSUME_LATER;
            }
        }
        return ConsumeStatus.CONSUME_SUCCESS;
    }
}
