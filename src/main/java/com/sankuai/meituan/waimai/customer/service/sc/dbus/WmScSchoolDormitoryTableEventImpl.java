package com.sankuai.meituan.waimai.customer.service.sc.dbus;

import com.alibaba.fastjson.JSON;
import com.meituan.dbus.common.StaticUtils;
import com.sankuai.meituan.waimai.customer.constant.sc.WmScTableDbusEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2021/8/31
 */
@Service
@Slf4j
public class WmScSchoolDormitoryTableEventImpl implements ITableEvent {

    @Override
    public WmScTableDbusEnum getTable() {
        return WmScTableDbusEnum.TABLE_WM_SC_SCHOOL_DORMITORY;
    }

    @Override
    public String handleUpdate(TableEvent tableEvent) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.service.sc.dbus.WmScSchoolDormitoryTableEventImpl.handleUpdate(com.sankuai.meituan.waimai.customer.service.sc.dbus.TableEvent)");
        log.info("监听宿舍楼信息变更handleUpdate::tableEvent = {}", JSON.toJSONString(tableEvent));
        return StaticUtils.ok;
    }

    @Override
    public String handleInsert(TableEvent tableEvent) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_1", "com.sankuai.meituan.waimai.customer.service.sc.dbus.WmScSchoolDormitoryTableEventImpl.handleInsert(com.sankuai.meituan.waimai.customer.service.sc.dbus.TableEvent)");
        log.info("监听宿舍楼信息变更handleUpdate::tableEvent = {}", JSON.toJSONString(tableEvent));
        return StaticUtils.ok;
    }

    @Override
    public String handleDelete(TableEvent tableEvent) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.service.sc.dbus.WmScSchoolDormitoryTableEventImpl.handleDelete(com.sankuai.meituan.waimai.customer.service.sc.dbus.TableEvent)");
        // 无物理删除，不会走到这里
        return StaticUtils.ok;
    }
}
