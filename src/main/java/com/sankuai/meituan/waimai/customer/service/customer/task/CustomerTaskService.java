package com.sankuai.meituan.waimai.customer.service.customer.task;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.dianping.cat.util.MetricHelper;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.meituan.waimai.customer.adapter.WmEmployClient;
import com.sankuai.meituan.waimai.customer.constant.WmCustomerConstant;
import com.sankuai.meituan.waimai.customer.constant.customer.CustomerMetricEnum;
import com.sankuai.meituan.waimai.customer.dao.WmCustomerTaskMapper;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerTaskQueryBO;
import com.sankuai.meituan.waimai.customer.domain.task.CustomerOperateBO;
import com.sankuai.meituan.waimai.customer.domain.task.WmCustomerTask;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerConstants;
import com.sankuai.meituan.waimai.thrift.customer.constant.ValidEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.customer.task.*;
import com.sankuai.meituan.waimai.thrift.customer.domain.task.CustomerTaskQueryRes;
import com.sankuai.meituan.waimai.thrift.customer.domain.task.WmCustomerTaskDTO;
import com.sankuai.meituan.waimai.thrift.customer.domain.task.WmCustomerTaskQueryDTO;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import com.sankuai.meituan.waimai.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;

/**
 * <AUTHOR>
 * @date 20230321
 * @desc 客户任务服务
 */
@Slf4j
@Service
public class CustomerTaskService {

    @Autowired
    private WmCustomerTaskMapper wmCustomerTaskMapper;

    @Autowired
    private WmEmployClient wmEmployClient;


    /**
     * 客户任务列表-查询返回结果
     *
     * @param customerTaskQueryDTO
     * @return
     */
    public CustomerTaskQueryRes listCustomerTask(WmCustomerTaskQueryDTO customerTaskQueryDTO) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.customer.task.CustomerTaskService.listCustomerTask(com.sankuai.meituan.waimai.thrift.customer.domain.task.WmCustomerTaskQueryDTO)");
        log.info("listCustomerTask,customerTaskQueryDTO={}", JSON.toJSONString(customerTaskQueryDTO));
        CustomerTaskQueryRes customerTaskQueryRes = new CustomerTaskQueryRes();
        customerTaskQueryRes.setTotal(0);
        customerTaskQueryRes.setList(Lists.newArrayList());
        customerTaskQueryRes.setPageNo(customerTaskQueryDTO.getPageNo());
        customerTaskQueryRes.setPageSize(customerTaskQueryDTO.getPageSize());

        try {
            Integer count = countTaskByParams(customerTaskQueryDTO);
            if (count == null || count <= 0) {
                return customerTaskQueryRes;
            }
            customerTaskQueryRes.setTotal(count);
            List<WmCustomerTaskDTO> list = listTaskByParams(customerTaskQueryDTO);
            if (!CollectionUtils.isEmpty(list)) {
                customerTaskQueryRes.setList(list);
            }
        } catch (Exception e) {
            log.error("listCustomerTask,根据参数查询客户任务列表发生异常,customerTaskQueryDTO={}",
                    JSON.toJSONString(customerTaskQueryDTO), e);
        }
        return customerTaskQueryRes;
    }


    /**
     * 根据条件查询总记录数
     *
     * @param queryDTO
     * @return
     */
    public Integer countTaskByParams(WmCustomerTaskQueryDTO queryDTO) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_1", "com.sankuai.meituan.waimai.customer.service.customer.task.CustomerTaskService.countTaskByParams(com.sankuai.meituan.waimai.thrift.customer.domain.task.WmCustomerTaskQueryDTO)");
        WmCustomerTaskQueryBO queryBO = new WmCustomerTaskQueryBO();
        queryBO.setCustomerId(queryDTO.getCustomerId());
        queryBO.setOpSource(queryDTO.getOpSource());
        queryBO.setSceneType(queryDTO.getSceneType());
        queryBO.setBizId(queryDTO.getBizId());
        queryBO.setTaskType(queryDTO.getTaskType());
        queryBO.setCreateTimeEnd(queryDTO.getCreateTimeEnd());
        queryBO.setCreateTimeStart(queryDTO.getCreateTimeStart());
        queryBO.setStatus(queryDTO.getStatus());
        queryBO.setBizTaskId(queryDTO.getBizTaskId());
        Integer count = wmCustomerTaskMapper.countByQueryParams(queryBO);
        log.info("countTaskByParams,count={},queryBO={}", count, JSON.toJSONString(queryBO));
        return count;
    }

    /**
     * 根据查询条件返回客户任务列表信息
     *
     * @param queryDTO
     * @return
     */
    public List<WmCustomerTaskDTO> listTaskByParams(WmCustomerTaskQueryDTO queryDTO) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.service.customer.task.CustomerTaskService.listTaskByParams(com.sankuai.meituan.waimai.thrift.customer.domain.task.WmCustomerTaskQueryDTO)");
        List<WmCustomerTaskDTO> result = new ArrayList<>();
        // 无页数默认查询20条
        Integer pageSize = queryDTO.getPageSize();
        if (pageSize == null || pageSize <= 0) {
            pageSize = 20;
        }
        //无页码默认查询第一页
        Integer pageNo = queryDTO.getPageNo();
        if (pageNo == null || pageNo <= 0) {
            pageNo = 1;
        }
        WmCustomerTaskQueryBO queryBO = new WmCustomerTaskQueryBO();
        queryBO.setCustomerId(queryDTO.getCustomerId());
        queryBO.setOpSource(queryDTO.getOpSource());
        queryBO.setSceneType(queryDTO.getSceneType());
        queryBO.setBizId(queryDTO.getBizId());
        queryBO.setBizTaskId(queryDTO.getBizTaskId());
        queryBO.setTaskType(queryDTO.getTaskType());
        queryBO.setCreateTimeEnd(queryDTO.getCreateTimeEnd());
        queryBO.setCreateTimeStart(queryDTO.getCreateTimeStart());
        queryBO.setStatus(queryDTO.getStatus());
        queryBO.setPageSize(pageSize);
        queryBO.setOffset((pageNo - 1) * pageSize);

        Integer maxId = wmCustomerTaskMapper.countMaxIdByQueryParams(queryBO);
        queryBO.setMaxId(maxId);

        List<WmCustomerTask> list = wmCustomerTaskMapper.listCustomerTaskByParams(queryBO);
        log.info("listTaskByParams,queryBO={},list={}", JSON.toJSONString(queryBO), JSON.toJSONString(list));
        for (WmCustomerTask task : list) {
            WmCustomerTaskDTO wmCustomerTaskDTO = new WmCustomerTaskDTO();
            wmCustomerTaskDTO.setId(task.getId());
            wmCustomerTaskDTO.setCustomerId(task.getCustomerId());
            wmCustomerTaskDTO.setState(task.getStatus() == null ? "" : CustomerTaskStatusEnum.of(task.getStatus()).getDesc());
            wmCustomerTaskDTO.setTaskType(task.getTaskType() == null ? "" : CustomerTaskTypeEnum.of(task.getTaskType()).getDesc());
            wmCustomerTaskDTO.setTaskSourceName(task.getOpSource() == null ? "" : CustomerTaskSourceEnum.of(task.getOpSource()).getDesc());
            wmCustomerTaskDTO.setTaskDetailSourceName(task.getOpDetailSource());
            wmCustomerTaskDTO.setOpSystemName(task.getOpSystem());
            wmCustomerTaskDTO.setBizType(task.getBizType() == null ? "" : CustomerTaskBizTypeEnum.of(task.getBizType()).getDesc());
            wmCustomerTaskDTO.setBizId(task.getBizId());
            wmCustomerTaskDTO.setCtime(DateUtil.seconds2TimeFormat(task.getCtime(), DateUtil.DefaultLongFormat));
            wmCustomerTaskDTO.setUtime(DateUtil.seconds2TimeFormat(task.getUtime(), DateUtil.DefaultLongFormat));
            wmCustomerTaskDTO.setCreateUserName(task.getCuid() == 0 ? "系统" : wmEmployClient.getNameMsiById(task.getCuid()));
            wmCustomerTaskDTO.setSignTaskId(task.getSignTaskId());
            wmCustomerTaskDTO.setBizTaskId(task.getBizTaskId());
            wmCustomerTaskDTO.setProcessMsg(task.getProcessMsg());
            wmCustomerTaskDTO.setTaskSceneName(task.getSceneType() == null ? "" : CustomerTaskSceneType.of(task.getSceneType()).getDesc());
            result.add(wmCustomerTaskDTO);
        }
        return result;
    }

    /**
     * 新增客户门店绑定类型任务
     *
     * @param customerId
     * @param wmPoiIds
     * @param customerOperateBO
     */
    public Map<Long, Integer> batchAddCustomerBindTask(Integer customerId, Set<Long> wmPoiIds, CustomerOperateBO customerOperateBO) {
        // 客户ID为空或门店ID列表为空则不处理
        Map<Long, Integer> maps = new HashMap<>();
        if (customerId == null || customerId < 0 || CollectionUtils.isEmpty(wmPoiIds) || customerOperateBO == null) {
            return maps;
        }
        try {
            List<Integer> taskIds = new ArrayList<>();
            for (Long wmPoiId : wmPoiIds) {
                WmCustomerTask task = initCustomerTaskDTO(customerId, wmPoiId, customerOperateBO);
                wmCustomerTaskMapper.insertSelective(task);
                taskIds.add(task.getId());
                maps.put(wmPoiId, task.getId());
            }
            log.info("本次批量新增的任务主键ID分别为,taskIds={},customerId={},wmPoiIds={}", JSON.toJSONString(taskIds), customerId, wmPoiIds);
            return maps;
        } catch (Exception e) {
            log.error("addCustomerBindTask,客户门店关系绑定记录任务发生异常,customerId={},wmPoiIds={},customerOperateDTO={}",
                    customerId, JSON.toJSONString(wmPoiIds), JSON.toJSONString(customerOperateBO), e);
        }
        return maps;
    }


    /**
     * 新增单条解绑任务记录
     *
     * @param customerId
     * @param wmPoiId
     * @param customerOperateBO
     * @return
     */
    public Integer addCustomerUnBindTask(Integer customerId, Long wmPoiId, CustomerOperateBO customerOperateBO) {
        // 客户ID为空或门店ID列表为空则不处理
        if (customerId == null || customerId <= 0
                || wmPoiId == null || wmPoiId <= 0 || customerOperateBO == null) {
            return null;
        }
        try {
            WmCustomerTask task = initCustomerTaskDTO(customerId, wmPoiId, customerOperateBO);
            wmCustomerTaskMapper.insertSelective(task);
            return task.getId();
        } catch (Exception e) {
            log.error("addCustomerUnBindTask,客户门店关系解绑记录任务发生异常,customerId={},wmPoiId={},customerOperateDTO={}",
                    customerId, wmPoiId, JSON.toJSONString(customerOperateBO), e);
        }
        return null;
    }


    /**
     * 新增客户门店绑定类型任务
     *
     * @param customerId        客户ID
     * @param wmPoiIds          门店ID列表
     * @param customerOperateBO 操作来源
     */
    public Map<Long, Integer> batchAddCustomerUnBindTask(Integer customerId, Set<Long> wmPoiIds, CustomerOperateBO customerOperateBO) {
        // 客户ID为空或门店ID列表为空则不处理
        Map<Long, Integer> maps = new HashMap<>();
        if (customerId == null || customerId < 0 || CollectionUtils.isEmpty(wmPoiIds) || customerOperateBO == null) {
            return maps;
        }
        try {
            for (Long wmPoiId : wmPoiIds) {
                WmCustomerTask task = initCustomerTaskDTO(customerId, wmPoiId, customerOperateBO);
                wmCustomerTaskMapper.insertSelective(task);
                maps.put(wmPoiId, task.getId());
            }
            log.info("batchAddCustomerUnBindTask,本次批量新增的任务主键ID分别为,maps={}", JSON.toJSONString(maps));
            return maps;
        } catch (Exception e) {
            log.error("batchAddCustomerUnBindTask,客户门店关系解绑记录任务发生异常,customerId={},wmPoiIds={},customerOperateDTO={}",
                    customerId, JSON.toJSONString(wmPoiIds), JSON.toJSONString(customerOperateBO), e);
        }
        return maps;
    }

    /**
     * 客户门店关系绑定更新状态-只更新进行中状态
     *
     * @param customerId
     * @param wmPoiIds
     */
    public void updateTaskStatus(Integer customerId, Set<Long> wmPoiIds, List<Integer> taskIds) {
        if (customerId == null || CollectionUtils.isEmpty(wmPoiIds) || CollectionUtils.isEmpty(taskIds)) {
            return;
        }
        try {
            WmCustomerTaskQueryBO customerTaskQueryBo = new WmCustomerTaskQueryBO();
            customerTaskQueryBo.setCustomerId(customerId);
            customerTaskQueryBo.setWmPoiIds(Lists.newArrayList(wmPoiIds.iterator()));
            customerTaskQueryBo.setStatus(CustomerTaskStatusEnum.SUC.getCode());
            customerTaskQueryBo.setIds(taskIds);
            wmCustomerTaskMapper.updateTaskStatusByQueryBo(customerTaskQueryBo);
            log.info("updateTaskStatus,根据参数将任务更新成完成,customerId={},wmPoiIds={},taskIds={}", customerId, JSON.toJSONString(wmPoiIds), JSON.toJSONString(taskIds));
        } catch (Exception e) {
            log.error("updateTaskStatus,根据参数更新任务状态发生异常,customerId={},wmPoiIds={}", customerId, wmPoiIds, e);
        }
    }

    /**
     * 客户门店关系绑定更新状态-只更新进行中状态
     *
     * @param customerId
     * @param wmPoiIds
     */
    public void updateStatusByCustomerIdAndPoiIdAndType(Integer customerId, Set<Long> wmPoiIds, Long bizTaskId,
                                                        Integer taskType, Integer status) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.service.customer.task.CustomerTaskService.updateStatusByCustomerIdAndPoiIdAndType(Integer,Set,Long,Integer,Integer)");
        if (customerId == null || CollectionUtils.isEmpty(wmPoiIds) || taskType == null || taskType <= 0) {
            return;
        }
        try {
            WmCustomerTaskQueryBO customerTaskQueryBo = new WmCustomerTaskQueryBO();
            customerTaskQueryBo.setCustomerId(customerId);
            customerTaskQueryBo.setWmPoiIds(Lists.newArrayList(wmPoiIds.iterator()));
            customerTaskQueryBo.setStatus(status);
            customerTaskQueryBo.setBizTaskId(bizTaskId == null ? 0 : bizTaskId.intValue());
            customerTaskQueryBo.setTaskType(taskType);
            wmCustomerTaskMapper.updateTaskStatusByQueryBo(customerTaskQueryBo);
            log.info("updateStatusByCustomerIdAndPoiIdAndType,根据参数将任务更新成完成,customerId={},wmPoiIds={},taskType={}", customerId, JSON.toJSONString(wmPoiIds), taskType);
        } catch (Exception e) {
            log.error("updateStatusByCustomerIdAndPoiIdAndType,根据参数更新任务状态发生异常,customerId={},wmPoiIds={},status={},taskType={}", customerId, wmPoiIds, status, taskType, e);
        }
    }


    /**
     * 更新任务的签约任务ID
     *
     * @param customerId
     * @param taskIds
     * @param signTaskId
     */
    public void updateTaskSignTaskId(Integer customerId, List<Integer> taskIds, Integer signTaskId) {
        if (customerId == null || CollectionUtils.isEmpty(taskIds)
                || signTaskId == null || signTaskId <= 0) {
            return;
        }
        try {
            wmCustomerTaskMapper.updateTaskSignTaskIdByIds(signTaskId, taskIds);
            log.info("批量更新客户任务的签约记录ID信息完成,customerId={},signTaskId={},taskIds={}", customerId, signTaskId, JSON.toJSONString(taskIds));
        } catch (Exception e) {
            log.error("updateTaskSignTaskId,根据参数更新任务签约任务ID发生异常,customerId={},signTaskId={},taskIds={}", customerId, signTaskId, JSON.toJSONString(taskIds), e);
        }
    }

    /**
     * 根据客户ID、门店ID、任务类型查询处理中记录
     *
     * @param customerId 客户ID
     * @param wmPoiId    门店ID
     * @param taskType   任务类型
     * @return
     */
    public Integer getCustomerTaskDoing(Integer customerId, Integer wmPoiId, Integer taskType, Long bizTaskId) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.service.customer.task.CustomerTaskService.getCustomerTaskDoing(java.lang.Integer,java.lang.Integer,java.lang.Integer,java.lang.Long)");
        if (customerId == null || wmPoiId == null || taskType == null) {
            return null;
        }
        return wmCustomerTaskMapper.getLatestByCustomerIdAndWmPoiIdFromRT(customerId, wmPoiId, taskType, bizTaskId);
    }

    /**
     * 根据客户ID和门店以及任务类型以及关联任务ID 查询处理中任务ID
     *
     * @param customerId
     * @param wmPoiIds
     * @param taskType
     * @param switchTaskId
     * @return
     */
    public List<Integer> listDoingCustomerTaskIds(Integer customerId, List<Long> wmPoiIds, Integer taskType, Long switchTaskId) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.service.customer.task.CustomerTaskService.listDoingCustomerTaskIds(java.lang.Integer,java.util.List,java.lang.Integer,java.lang.Long)");
        if (customerId == null || CollectionUtils.isEmpty(wmPoiIds) || taskType == null) {
            return null;
        }
        return wmCustomerTaskMapper.listIdsByCustomerIdAndWmPoiIdAndTaskTypeFromRT(customerId, wmPoiIds, taskType, switchTaskId);
    }


    /**
     * 根据签约任务ID和任务类型查询处理中解绑任务-强制解绑查询使用
     *
     * @param signTaskId
     * @param customerId
     * @return
     */
    public List<WmCustomerTask> listDoingTaskByCusIdAndSignIdAndTaskType(Integer customerId, Integer signTaskId, Integer taskType) {
        if (signTaskId == null || customerId == null) {
            return Lists.newArrayList();
        }
        List<WmCustomerTask> taskList = new ArrayList<>();
        try {
            taskList = wmCustomerTaskMapper.listDoingTaskBySignIdAndCusIdAndTaskTypeFromRT(customerId, signTaskId, taskType);
            log.info("listDoingTaskByCusIdAndSignIdAndTaskType,根据短信签约任务ID查询到记录为,taskList={},customerId={},signTaskId={},taskType={}", JSON.toJSONString(taskList), customerId, signTaskId, taskType);
        } catch (Exception e) {
            log.error("listDoingTaskByCusIdAndSignIdAndTaskType,根据参数查询处理中的任务列表发生异常,signTaskId={},customerId={}",
                    signTaskId, customerId, e);
        }
        return taskList;
    }

    /**
     * 获取任务对象信息
     *
     * @param customerId
     * @param wmPoiId
     * @param customerOperateBO
     * @return
     */
    private WmCustomerTask initCustomerTaskDTO(Integer customerId, Long wmPoiId, CustomerOperateBO customerOperateBO) {
        WmCustomerTask task = new WmCustomerTask();
        task.setCustomerId(customerId);
        task.setTaskType(customerOperateBO.getTaskType() != null ? customerOperateBO.getTaskType() : CustomerTaskTypeEnum.UN_KNOW.getCode());
        task.setBizType(CustomerTaskBizTypeEnum.BIZ_POI.getCode());
        task.setBizId(wmPoiId.intValue());
        task.setSceneType(customerOperateBO.getTaskSceneType() != null ? customerOperateBO.getTaskSceneType() : CustomerTaskSceneType.UN_KNOW.getCode());
        task.setOpSource(customerOperateBO.getOpSource() != null ? customerOperateBO.getOpSource() : CustomerTaskSourceEnum.UN_KNOWN.getCode());
        task.setOpDetailSource(customerOperateBO.getOpDetailSource() != null ? customerOperateBO.getOpDetailSource() : CustomerTaskDetailSourceEnum.UN_KNOWN.getDesc());
        task.setOpSystem(customerOperateBO.getOpSystem() != null ? customerOperateBO.getOpSystem() : CustomerTaskDetailSourceEnum.UN_KNOWN.getDesc());
        task.setCuid(customerOperateBO.getOpUserId() != null ? customerOperateBO.getOpUserId() : 0);
        task.setBizTaskId(customerOperateBO.getBizTaskId() != null ? customerOperateBO.getBizTaskId() : 0);
        task.setCtime(DateUtil.unixTime());
        task.setUtime(DateUtil.unixTime());
        task.setValid(ValidEnum.VALID_YES.getValue());
        task.setStatus(CustomerTaskStatusEnum.DOING.getCode());
        task.setProcessMsg(customerOperateBO.getRemark());
        return task;
    }

    /**
     * 根据短信记录ID和任务类型获取切换任务ID
     *
     * @param smsId        短信记录id：wm_customer_poi_sms_record的id
     * @param taskTypeEnum 任务类型：com.sankuai.meituan.waimai.thrift.customer.constant.customer.task.CustomerTaskTypeEnum
     * @return
     * @throws WmCustomerException
     */
    public Integer getSwitchTaskIdBySmsIdAndTaskType(Integer smsId, CustomerTaskTypeEnum taskTypeEnum) throws WmCustomerException {
        List<Integer> bizTaskList = wmCustomerTaskMapper.getDistinctBizTask(smsId, taskTypeEnum.getCode());
        log.info("getSwitchTaskIdBySmsIdAndTaskType smsId={},taskTypeEnum={},bizTaskList={}", smsId, JSONObject.toJSONString(taskTypeEnum), JSON.toJSONString(bizTaskList));
        if (CollectionUtils.isEmpty(bizTaskList)) {
            return null;
        }
        if (bizTaskList.size() > 1) {
            log.error("getSwitchTaskIdBySmsIdAndTaskType 客户任务存在多个 smsId={},taskTypeEnum={},bizTaskList={}", smsId, JSONObject.toJSONString(taskTypeEnum), JSON.toJSONString(bizTaskList));
            MetricHelper.build().name(CustomerMetricEnum.CUSTOMER_TASK_CHECK.getName())
                    .tag(CustomerMetricEnum.CUSTOMER_TASK_CHECK.getTag(), taskTypeEnum.getDesc())
                    .tag(CustomerMetricEnum.CUSTOMER_TASK_CHECK.getStatus(), WmCustomerConstant.SYSTEM_EXCEPTION).count();
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "客户任务存在多个");
        }
        return bizTaskList.get(0);
    }

    /**
     * 取消进行中客户门店解绑任务
     *
     * @param customerId
     * @param wmPoiId
     */
    public void cancelDoingUnBindTask(Integer customerId, Long wmPoiId, String scene) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_2", "com.sankuai.meituan.waimai.customer.service.customer.task.CustomerTaskService.cancelDoingUnBindTask(java.lang.Integer,java.lang.Long,java.lang.String)");
        try {
            List<Integer> taskIds = wmCustomerTaskMapper.listUnBindingTaskIdsByCustomerIdAndBizId(customerId, wmPoiId);
            if (CollectionUtils.isEmpty(taskIds)) {
                return;
            }
            wmCustomerTaskMapper.updateDoingTask2CancelWithRemark(taskIds, String.format("%s门店自动取消", scene));
            log.info("cancelDoingUnBindTask,{}取消客户与门店处理中任务完成,taskIds={},customerId={},wmPoiId={}"
                    , scene, JSON.toJSONString(taskIds), customerId, wmPoiId);
        } catch (Exception e) {
            log.error("cancelDoingUnBindTask,取消处理中客户门店解绑任务发生异常,customerId={},wmPoiId={}", customerId, wmPoiId, e);
        }
    }

    /**
     * 根据客户ID和门店ID查询处理中绑定任务
     *
     * @param customerId
     * @param wmPoiId
     * @return
     */
    public List<WmCustomerTask> listBindingTaskByCustomerIdAndBizId(Integer customerId, Long wmPoiId) throws WmCustomerException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_2", "com.sankuai.meituan.waimai.customer.service.customer.task.CustomerTaskService.listBindingTaskByCustomerIdAndBizId(java.lang.Integer,java.lang.Long)");
        if (customerId == null || wmPoiId == null) {
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "客户ID或门店ID不能为空");
        }

        return wmCustomerTaskMapper.listBindingTaskByCustomerIdAndBizId(customerId, wmPoiId);
    }

    /**
     * 根据主键ID查询任务信息
     *
     * @param id
     * @return
     * @throws WmCustomerException
     */
    public WmCustomerTask getTaskById(Long id) throws WmCustomerException {
        if (id == null) {
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "ID不能为空");
        }
        return wmCustomerTaskMapper.selectByPrimaryKey(id.intValue());
    }

    /**
     * 根据客户ID、门店ID、任务类型查询处理中记录数
     *
     * @param customerId
     * @param bizId
     * @param taskType
     * @return
     */
    public Integer countByCustomerIdAndBizIdAndTaskType(Integer customerId, Long bizId, Integer taskType) throws WmCustomerException {
        if (customerId == null || bizId == null) {
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "客户ID或门店ID不能为空");
        }
        return wmCustomerTaskMapper.countByCustomerIdAndWmPoiIdAndTaskType(customerId, bizId, taskType);
    }

    /**
     * 根据签约任务ID查询处理中切换任务ID列表
     *
     * @param signTaskId
     * @return
     */
    public List<Integer> listDoingBizTaskIdBySignTaskId(Integer signTaskId) throws WmCustomerException {
        if (signTaskId == null || signTaskId <= 0) {
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "签约任务ID不合法");
        }
        return wmCustomerTaskMapper.getRelBizTaskIdsBySignTaskId(signTaskId);
    }
    /**
     * 根据条件获取门店任务映射关系
     * @param customerTaskQueryBo
     * @return
     */
    public Map<Long, Integer> getTaskWmPoiMapByCondition(WmCustomerTaskQueryBO customerTaskQueryBo) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.service.customer.task.CustomerTaskService.getTaskWmPoiMapByCondition(com.sankuai.meituan.waimai.customer.domain.WmCustomerTaskQueryBO)");
        Map<Long, Integer> map = Maps.newHashMap();

        List<WmCustomerTask> wmCustomerTaskList = wmCustomerTaskMapper.listCustomerTaskByParams(customerTaskQueryBo);
        if (CollectionUtils.isEmpty(wmCustomerTaskList)) {
            return map;
        }

        for (WmCustomerTask wmCustomerTask : wmCustomerTaskList) {
            map.put(Long.valueOf(wmCustomerTask.getBizId()), wmCustomerTask.getId());
        }
        return map;
    }

    /**
     * 根据ID列表批量删除任务
     *
     * @param ids
     * @param msg
     * @throws WmCustomerException
     */
    public void deleteTaskByIds(List<Integer> ids, String msg) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_2", "com.sankuai.meituan.waimai.customer.service.customer.task.CustomerTaskService.deleteTaskByIds(java.util.List,java.lang.String)");
        if (CollectionUtils.isEmpty(ids)) {
            return;
        }
        try {
            wmCustomerTaskMapper.deleteByIdsWithMsg(ids, msg);
        } catch (Exception e) {
            log.error("deleteTaskByIds,根据ids删除客户任务发生异常，ids={},msg={}", JSON.toJSONString(ids), msg, e);
        }
    }
}
