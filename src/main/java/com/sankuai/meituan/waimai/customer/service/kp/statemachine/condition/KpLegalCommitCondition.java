package com.sankuai.meituan.waimai.customer.service.kp.statemachine.condition;


import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class KpLegalCommitCondition extends CommitCondition {
    @Override
    public String name() {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.service.kp.statemachine.condition.KpLegalCommitCondition.name()");
        return null;
    }


}
