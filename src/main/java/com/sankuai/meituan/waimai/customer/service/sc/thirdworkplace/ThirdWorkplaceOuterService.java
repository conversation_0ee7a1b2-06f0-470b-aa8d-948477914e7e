package com.sankuai.meituan.waimai.customer.service.sc.thirdworkplace;

import cn.hutool.core.collection.CollectionUtil;
import com.google.common.base.Predicate;
import com.google.common.collect.Iterators;
import com.sankuai.meituan.auth.util.CollectionUtils;
import com.sankuai.meituan.waimai.customer.adapter.WmAorServiceAdapter;
import com.sankuai.meituan.waimai.customer.adapter.WmEmployClient;
import com.sankuai.meituan.waimai.customer.adapter.WmOpenCityServiceAdapter;
import com.sankuai.meituan.waimai.customer.adapter.WmVirtualOrgServiceAdaptor;
import com.sankuai.meituan.waimai.customer.dao.sc.WmScEnumDictMapper;
import com.sankuai.meituan.waimai.customer.dao.sc.WmSchoolMapper;
import com.sankuai.meituan.waimai.customer.domain.sc.WmSchoolDB;
import com.sankuai.meituan.waimai.customer.domain.sc.WmSchoolSearchCondition;
import com.sankuai.meituan.waimai.gis.client.thrift.dto.city.WmOpenCity;
import com.sankuai.meituan.waimai.infra.constants.WmOrgConstant;
import com.sankuai.meituan.waimai.infra.constants.WmVirtualOrgRecursiveTypeEnum;
import com.sankuai.meituan.waimai.infra.constants.WmVirtualOrgSourceEnum;
import com.sankuai.meituan.waimai.infra.domain.WmEmploy;
import com.sankuai.meituan.waimai.infra.domain.WmUniAor;
import com.sankuai.meituan.waimai.infra.domain.WmVirtualOrg;
import com.sankuai.meituan.waimai.thrift.exception.WmSchCantException;
import com.sankuai.meituan.waimai.thrift.exception.WmServerException;
import lombok.extern.slf4j.Slf4j;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Nullable;
import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

@Component
@Slf4j
public class ThirdWorkplaceOuterService {

    @Autowired
    private WmEmployClient employClient;

    @Autowired
    private WmVirtualOrgServiceAdaptor wmVirtualOrgServiceAdaptor;

    @Autowired
    private WmAorServiceAdapter wmAorServiceAdapter;

    @Autowired
    private WmOpenCityServiceAdapter wmOpenCityServiceAdapter;

    @Resource
    private WmSchoolMapper wmSchoolMapper;


    public String buildOrgInfo(WmEmploy employ,
                               WmVirtualOrgSourceEnum wmVirtualOrgSourceEnum,
                               WmVirtualOrgRecursiveTypeEnum wmVirtualOrgRecursiveTypeEnum) {
        if (Objects.isNull(employ)){
            return "未知";
        }
        List<WmVirtualOrg> orgList = null;
        try {
            orgList = wmVirtualOrgServiceAdaptor.getOrgsByUid(employ.getUid(),
                    wmVirtualOrgSourceEnum.getSource(),
                    wmVirtualOrgRecursiveTypeEnum.getType());
        } catch (WmSchCantException e) {
            log.error("WmScThirdWorkplaceThriftAssemble.buildOrgInfo 根据用户获取组织节点失败:{}", e.getMsg());
            return "未知";
        }
        if (CollectionUtil.isEmpty(orgList)){
            return "未知";
        }
        return orgList.stream()
                .sorted(Comparator.comparing(WmVirtualOrg::getLevel, Comparator.nullsLast(Comparator.naturalOrder())))
                .map(WmVirtualOrg::getName).collect(Collectors.joining("/"));
    }

    public Map<Integer, WmUniAor> batchGetAorInfo(Set<Integer> aorIds) {
        if (aorIds.isEmpty()) return Collections.emptyMap();

        Map<Integer, WmUniAor> result = new ConcurrentHashMap<>();
        try {
            for (Integer aorId : aorIds) {
                try {
                    WmUniAor aor = wmAorServiceAdapter.getAorInfoById(aorId);
                    if (aor != null) {
                        result.put(aorId, aor);
                    }
                } catch (Exception e) {
                    log.warn("获取蜂窝信息失败, aorId: {}", aorId, e);
                }
            }
        } catch (Exception e) {
            log.error("批量获取蜂窝信息失败", e);
        }
        return result;
    }

    /**
     * 批量获取城市信息
     */
    public Map<Integer, WmOpenCity> batchGetCityInfo(Set<Integer> cityIds) {
        if (cityIds.isEmpty()) return Collections.emptyMap();

        try {
            Map<Integer, WmOpenCity> cityMap = wmOpenCityServiceAdapter.getCityMapByCityIdList(new ArrayList<>(cityIds));
            log.info("批量获取城市信息完成，查询{}个，返回{}个", cityIds.size(), cityMap.size());
            return CollectionUtil.isNotEmpty(cityMap) ? cityMap : Collections.emptyMap();
        } catch (Exception e) {
            log.error("批量获取城市信息失败", e);
            return Collections.emptyMap();
        }
    }

    /**
     * 批量获取员工信息
     */
    public Map<Integer, WmEmploy> batchGetEmployInfo(Set<Integer> employIds) {
        if (employIds.isEmpty()) return Collections.emptyMap();

        Map<Integer, WmEmploy> result = new ConcurrentHashMap<>();
        try {
            CompletableFuture.allOf(employIds.stream()
                    .map(employId -> CompletableFuture.runAsync(() -> {
                        try {
                            WmEmploy employ = employClient.getEmployById(employId);
                            if (employ != null) {
                                result.put(employId, employ);
                            }
                        } catch (Exception e) {
                            log.warn("获取员工信息失败, employId: {}", employId, e);
                        }
                    })).toArray(CompletableFuture[]::new)).join();
            log.info("批量获取员工信息完成，查询{}个，返回{}个", employIds.size(), result.size());
        } catch (Exception e) {
            log.error("批量获取员工信息失败", e);
        }
        return result;
    }

    /**
     * 批量获取学校信息
     */
    public Map<Integer, WmSchoolDB> batchGetSchoolInfo(Set<Integer> schoolIds) {
        if (schoolIds.isEmpty()) return Collections.emptyMap();

        Map<Integer, WmSchoolDB> result = new ConcurrentHashMap<>();
        try {
            WmSchoolSearchCondition condition = new WmSchoolSearchCondition();
            condition.setSchoolIdList(new ArrayList<>(schoolIds));
            List<WmSchoolDB> schools = wmSchoolMapper.selectByCondition(condition);

            if (CollectionUtil.isNotEmpty(schools)) {
                for (WmSchoolDB school : schools) {
                    result.put(school.getSchoolId(), school);
                }
            }
            log.info("批量获取学校信息完成，查询{}个，返回{}个", schoolIds.size(), result.size());
        } catch (Exception e) {
            log.error("批量获取学校信息失败", e);
        }
        return result;
    }

    /**
     * 批量获取组织信息
     */
    public Map<Integer, List<WmVirtualOrg>> batchGetOrgInfoByUid(Map<Integer, WmEmploy> employMap, WmVirtualOrgSourceEnum sourceEnum) {
        if (employMap.isEmpty()) return Collections.emptyMap();

        Map<Integer, List<WmVirtualOrg>> result;
        result = new ConcurrentHashMap<>();

        CompletableFuture.allOf(employMap.entrySet().stream()
                .map(entry -> CompletableFuture.runAsync(() -> {
                    try {
                        Integer employId = entry.getKey();
                        WmEmploy employ = entry.getValue();

                        List<WmVirtualOrg> orgList = wmVirtualOrgServiceAdaptor.getOrgsByUid(
                                employ.getUid(),
                                sourceEnum.getSource(),
                                WmVirtualOrgRecursiveTypeEnum.BOTTOM_UP.getType()
                        );

                        if (CollectionUtil.isNotEmpty(orgList)) {
                            result.put(employId, orgList);
                        }
                    } catch (Exception e) {
                        log.warn("获取组织信息失败, employId: {}", entry.getKey(), e);
                    }
                })).toArray(CompletableFuture[]::new)).join();
        log.info("批量获取组织信息完成，查询{}个员工，返回{}个组织信息", employMap.size(), result.size());

        return result;
    }

    /**
     * 批量根据员工MIS和组织源查询组织信息
     */
    public Map<String, List<WmVirtualOrg>> batchGetOrgInfoByMisAndSource(Map<String, WmEmploy> employMisMap, WmVirtualOrgSourceEnum orgSource) {
        if (employMisMap.isEmpty()) return Collections.emptyMap();

        Map<String, List<WmVirtualOrg>> result = new ConcurrentHashMap<>();

        CompletableFuture.allOf(employMisMap.entrySet().stream()
                .map(entry -> CompletableFuture.runAsync(() -> {
                    try {
                        String misId = entry.getKey();
                        WmEmploy employ = entry.getValue();
                        // 校企场景仅展示上级节点，不展示自身，目前仅校企经理场景用到mis查询
                        List<WmVirtualOrg> orgList = wmVirtualOrgServiceAdaptor.getOrgsByUid(
                                employ.getUid(),
                                orgSource.getSource(),
                                WmVirtualOrgRecursiveTypeEnum.BOTTOM_UP_NO_SELF.getType()
                        );

                        if (CollectionUtil.isNotEmpty(orgList)) {
                            result.put(misId, orgList);
                        }
                    } catch (Exception e) {
                        log.warn("获取组织信息失败, misId: {}, orgSource: {}", entry.getKey(), orgSource, e);
                    }
                })).toArray(CompletableFuture[]::new)).join();

        log.info("批量获取组织信息（MisId）完成，查询{}个员工，返回{}个组织信息，组织源: {}", employMisMap.size(), result.size(), orgSource);
        return result;
    }


    public Map<String, WmEmploy> batchGetEmployInfoByMis(Set<String> employMisIds) {
        if (employMisIds.isEmpty()) return Collections.emptyMap();

        Map<String, WmEmploy> result = new ConcurrentHashMap<>();
        try {
            CompletableFuture.allOf(employMisIds.stream()
                    .map(misId -> CompletableFuture.runAsync(() -> {
                        try {
                            WmEmploy employ = employClient.getEmployByMisId(misId);
                            if (employ != null) {
                                result.put(misId, employ);
                            }
                        } catch (Exception e) {
                            log.warn("获取员工信息失败, misId: {}", misId, e);
                        }
                    })).toArray(CompletableFuture[]::new)).join();
            log.info("批量获取员工信息（MisId）完成，查询{}个，返回{}个", employMisIds.size(), result.size());
        } catch (Exception e) {
            log.error("批量获取员工信息（MisId）失败", e);
        }
        return result;
    }

    public Map<String, List<WmVirtualOrg>> batchGetOrgInfoByMis(Map<String, WmEmploy> employMisMap) {
        if (employMisMap.isEmpty()) return Collections.emptyMap();

        Map<String, List<WmVirtualOrg>> result = new ConcurrentHashMap<>();

        CompletableFuture.allOf(employMisMap.entrySet().stream()
                .map(entry -> CompletableFuture.runAsync(() -> {
                    try {
                        String misId = entry.getKey();
                        WmEmploy employ = entry.getValue();

                        List<WmVirtualOrg> orgList = wmVirtualOrgServiceAdaptor.getOrgsByUid(
                                employ.getUid(),
                                WmVirtualOrgSourceEnum.WAIMAI.getSource(),
                                WmVirtualOrgRecursiveTypeEnum.BOTTOM_UP.getType()
                        );

                        if (CollectionUtil.isNotEmpty(orgList)) {
                            result.put(misId, orgList);
                        }
                    } catch (Exception e) {
                        log.warn("获取组织信息失败, misId: {}", entry.getKey(), e);
                    }
                })).toArray(CompletableFuture[]::new)).join();
        log.info("批量获取组织信息（MisId）完成，查询{}个员工，返回{}个组织信息", employMisMap.size(), result.size());

        return result;
    }

    public Integer getClueIdBySchoolId(Integer schoolId) {
        if (Objects.isNull(schoolId)){
            return null;
        }
        wmSchoolMapper.selectByCondition(new WmSchoolSearchCondition());
        //通过线索id查询学校列表
        WmSchoolDB wmSchoolDB = wmSchoolMapper.selectSchoolBySchoolId(schoolId);
        if (Objects.isNull(wmSchoolDB)){
            log.error("getClueIdBySchoolId 未查询到学校信息");
            return 0;
        }
        Long wdcClueId = wmSchoolDB.getWdcClueId();
        if (Objects.isNull(wdcClueId)){
            log.error("getClueIdBySchoolId 未查询到wdcClueId");
            return 0;
        }
        return wdcClueId.intValue();
    }

    public WmEmploy getEmployInfoByUid(Integer uid){
        if (Objects.isNull(uid)){
            return null;
        }
        WmEmploy employ = employClient.getEmployById(uid);
        if (Objects.isNull(employ)){
            log.error("WmScThirdWorkplaceThriftAssemble.getEmployInfoByUid 未查询到用户信息,uid:{}", uid);
        }
        return employ;
    }

    public WmEmploy getEmployInfoByMis(String mis){
        WmEmploy employ = employClient.getEmployByMisId(mis);
        if (Objects.isNull(employ)){
            log.error("WmScThirdWorkplaceThriftAssemble.getEmployInfoByMis 未查询到用户信息,mis:{}", mis);
        }
        return employ;
    }

    public String getCityNameById(Integer cityId) {
        try {
            WmOpenCity city = wmOpenCityServiceAdapter.getCityByCityId(cityId);
            if (Objects.nonNull(city)) {
                return city.getCityName();
            }
        } catch (WmSchCantException e) {
            log.error("获取城市信息失败,id:{}", cityId);
        }
        return "";
    }


    public Map<Integer, List<WmVirtualOrg>> batchGetOrgInfoByAorId(Set<Integer> aorIds, WmVirtualOrgSourceEnum wmVirtualOrgSourceEnum) {
        if (aorIds.isEmpty()) return Collections.emptyMap();

        Map<Integer, List<WmVirtualOrg>> result = new ConcurrentHashMap<>();

        CompletableFuture.allOf(aorIds.stream()
                .map(aorId -> CompletableFuture.runAsync(() -> {
                    try {
                        List<WmVirtualOrg> orgList = getOrgsByAorId(aorId, wmVirtualOrgSourceEnum);
                        if (CollectionUtil.isNotEmpty(orgList)) {
                            // 按照level正序排列，确保顺序一致
                            List<WmVirtualOrg> sortedOrgList = orgList.stream()
                                    .sorted(Comparator.comparing(WmVirtualOrg::getLevel, Comparator.nullsLast(Comparator.naturalOrder())))
                                    .collect(Collectors.toList());
                            result.put(aorId, sortedOrgList);
                        }
                    } catch (Exception e) {
                        log.warn("获取蜂窝组织信息失败, aorId: {}, sourceEnum: {}", aorId, wmVirtualOrgSourceEnum, e);
                    }
                })).toArray(CompletableFuture[]::new)).join();

        log.info("批量获取蜂窝组织信息完成，查询{}个蜂窝，返回{}个组织信息，组织源: {}", aorIds.size(), result.size(), wmVirtualOrgSourceEnum);
        return result;
    }


    public Map<Integer, WmEmploy> batchGetEmployByAorId(Set<Integer> aorIds) {
        if (aorIds.isEmpty()) return Collections.emptyMap();

        Map<Integer, WmEmploy> result = new ConcurrentHashMap<>();

        CompletableFuture.allOf(aorIds.stream()
                .map(aorId -> CompletableFuture.runAsync(() -> {
                    try {
                        WmEmploy wmEmploy = getEmployByAorId(aorId);
                        result.put(aorId, wmEmploy);
                    } catch (Exception e) {
                        log.warn("根据蜂窝id获取负责人信息失败, aorId: {}", aorId, e);
                    }
                })).toArray(CompletableFuture[]::new)).join();

        log.info("批量根据蜂窝id获取负责人信息，查询{}个蜂窝，返回{}个人员信息", aorIds.size(), result.size());
        return result;
    }


    public WmEmploy getEmployByAorId(Integer aorId){
        WmUniAor aorInfo = wmAorServiceAdapter.getAorInfoById(aorId);
        if (Objects.isNull(aorInfo)){
            return null;
        }
        WmVirtualOrg wmVirtualOrg = wmVirtualOrgServiceAdaptor.getVirtualOrgByCode(WmOrgConstant.OrgType.WM_AOR, aorId);
        if (Objects.isNull(wmVirtualOrg)){
            return null;
        }
        WmEmploy wmEmploy = null;
        try {
            wmEmploy = wmVirtualOrgServiceAdaptor.getMajorOwnerByOrgId(wmVirtualOrg.getId());
        } catch (WmSchCantException e) {
            log.error("ThirdWorkplaceOuterService.getEmployByAorId error aorId:{}", aorId);
        }
        return wmEmploy;
    }

    /**
     * 根据蜂窝ID获取组织信息字符串
     */
    public String buildOrgInfoByAorId(Integer aorId, WmVirtualOrgSourceEnum wmVirtualOrgSourceEnum) {
        if (Objects.isNull(aorId) || aorId <= 0){
            return "未知";
        }
        List<WmVirtualOrg> orgList = null;
        try {
            orgList = getOrgsByAorId(aorId, wmVirtualOrgSourceEnum);
        } catch (Exception e) {
            log.error("buildOrgInfoByAorId 根据蜂窝ID获取组织节点失败, aorId: {}, error: {}", aorId, e.getMessage());
            return "未知";
        }
        if (CollectionUtil.isEmpty(orgList)){
            return "未知";
        }
        return orgList.stream()
                .sorted(Comparator.comparing(WmVirtualOrg::getLevel, Comparator.nullsLast(Comparator.naturalOrder())))
                .map(WmVirtualOrg::getName).collect(Collectors.joining("/"));
    }

    /**
     * 根据蜂窝ID获取组织列表
     */
    private List<WmVirtualOrg> getOrgsByAorId(Integer aorId, WmVirtualOrgSourceEnum wmVirtualOrgSourceEnum) {
        if (aorId == null || aorId <= 0) {
            return Collections.emptyList();
        }

        try {
            // 先获取蜂窝对应的虚拟组织节点
            WmVirtualOrg wmVirtualOrg = wmVirtualOrgServiceAdaptor.getVirtualOrgByCode(WmOrgConstant.OrgType.WM_AOR, aorId);
            if (wmVirtualOrg == null) {
                log.info("根据蜂窝ID获取虚拟节点为空, aorId: {}", aorId);
                return Collections.emptyList();
            }

            // 获取该节点的上级组织结构
            List<WmVirtualOrg> wmVirtualOrgs = wmVirtualOrgServiceAdaptor.getOrgsByOrgId(
                    wmVirtualOrg.getId(),
                    WmVirtualOrgRecursiveTypeEnum.BOTTOM_UP_NO_SELF.getType()
            );

            if (CollectionUtils.isEmpty(wmVirtualOrgs)) {
                log.info("根据蜂窝ID获取组织列表为空, aorId: {}", aorId);
                return Collections.emptyList();
            }

            // 过滤出指定组织源的组织
            return wmVirtualOrgs.stream()
                    .filter(org -> org.getSource() == wmVirtualOrgSourceEnum.getSource())
                    .collect(Collectors.toList());

        } catch (Exception e) {
            log.warn("根据蜂窝ID获取组织信息失败, aorId: {}, sourceEnum: {}", aorId, wmVirtualOrgSourceEnum, e);
            return Collections.emptyList();
        }
    }
}
