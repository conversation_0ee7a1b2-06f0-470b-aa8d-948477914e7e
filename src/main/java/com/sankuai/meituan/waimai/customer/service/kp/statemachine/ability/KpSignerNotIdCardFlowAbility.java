package com.sankuai.meituan.waimai.customer.service.kp.statemachine.ability;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.sankuai.meituan.waimai.customer.config.MccCustomerConfig;
import com.sankuai.meituan.waimai.customer.constant.KpAuditResultConstants;
import com.sankuai.meituan.waimai.customer.constant.WmCustomerConstant;
import com.sankuai.meituan.waimai.customer.dao.WmCustomerKpTempDBMapper;
import com.sankuai.meituan.waimai.customer.domain.KpAuditResultBody;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerDB;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerSensitiveWordsService;
import com.sankuai.meituan.waimai.customer.service.gray.WmCustomerGrayService;
import com.sankuai.meituan.waimai.customer.service.kp.statemachine.constant.event.KpSignerEventEnum;
import com.sankuai.meituan.waimai.customer.service.kp.statemachine.context.KpSignerStatusMachineContext;
import com.sankuai.meituan.waimai.customer.service.kp.statemachine.trigger.KpSignerNotIdCardStatusTrigger;
import com.sankuai.meituan.waimai.thrift.customer.constant.*;
import com.sankuai.meituan.waimai.thrift.customer.constant.customer.KpVersionEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.kp.WmCustomerKp;
import com.sankuai.meituan.waimai.thrift.customer.domain.kp.WmCustomerKpTemp;
import com.sankuai.meituan.waimai.thrift.exception.StatusMachineException;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 20240416
 * @desc KP签约人非身份证流程能力
 */
@Service
@Slf4j
public class KpSignerNotIdCardFlowAbility extends KpSignerCommonService implements KpSignerFlowAbility {

    @Autowired
    private KpSignerNotIdCardStatusTrigger kpSignerNotIdCardStatusTrigger;

    @Autowired
    private WmCustomerSensitiveWordsService wmCustomerSensitiveWordsService;

    @Autowired
    private WmCustomerKpTempDBMapper wmCustomerKpTempDBMapper;
    
    @Autowired
    private WmCustomerGrayService wmCustomerGrayService;

    /**
     * KP签约人KP状态模型新增流程
     *
     * @param context
     * @return
     * @throws WmCustomerException
     */
    @Override
    public Object insertKpSignerWithSM(KpSignerStatusMachineContext context) throws WmCustomerException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_2", "com.sankuai.meituan.waimai.customer.service.kp.statemachine.ability.KpSignerNotIdCardFlowAbility.insertKpSignerWithSM(KpSignerStatusMachineContext)");

        log.info("KpSignerNotIdCardFlowAbility.insertKpSignerWithSM,context={}", JSON.toJSONString(context));
        try {
            //KP敏感词设置以及前置校验
            wmCustomerSensitiveWordsService.writeKpWhenInsertOrUpdate(context.getWmCustomerKp());
            //1-初始化创建签约人KP记录
            initCreate(context);

            WmCustomerDB wmCustomer = context.getWmCustomerDB();
            //2-判断是否能记录执行流程
            Boolean checkContinueFlow = checkCanContinueFlow(context.getWmCustomerKp(), wmCustomer);
            if (!checkContinueFlow) {
                return null;
            }

            //KP非身份证类型 && 允许不上传银行流水业务未命中灰度 && 银行流水为空不允许继续流程
            if (!CertTypeEnum.checkIdCardSet(context.getWmCustomerKp().getCertType())
                    && !wmCustomerGrayService.checkHitAllowNoBankStatementKpOperate(context.getCustomerId().longValue())
                    && StringUtils.isBlank(context.getWmCustomerKp().getSpecialAttachment())) {
                log.info("KP非身份证类型，银行流水为空不允许继续流程,customerId:{},id:{} info:{}", wmCustomer.getId(),
                        context.getWmCustomerKp().getId(), JSON.toJSONString(context.getWmCustomerKp()));
                context.getWmCustomerKp().setFailReason(WmCustomerConstant.KP_NO_BANK_STATEMENT_ERROR_MSG);
                throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR,
                        WmCustomerConstant.KP_NO_BANK_STATEMENT_ERROR_MSG);
            }


            //3-发起特批
            startKpSpecialAudit(context);
        } catch (StatusMachineException statusMachineException) {
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, statusMachineException.getMessage());
        } catch (Exception e) {
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, e.getMessage());
        }

        return null;
    }

    /**
     * KP签约人状态模型更新流程
     *
     * @param context
     * @return
     * @throws WmCustomerException
     */
    @Override
    public Object updateKpSignerWithSM(KpSignerStatusMachineContext context) throws WmCustomerException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.service.kp.statemachine.ability.KpSignerNotIdCardFlowAbility.updateKpSignerWithSM(KpSignerStatusMachineContext)");

        log.info("KpSignerNotIdCardFlowAbility.updateKpSignerWithSM,context={}", JSON.toJSONString(context));

        //KP敏感词设置以及前置校验
        wmCustomerSensitiveWordsService.writeKpWhenInsertOrUpdate(context.getWmCustomerKp());

        boolean needTempSave = context.isNeedTempSaveFlag();
        //1-暂存标为true则需要执行暂存即可
        if (needTempSave) {
            //重置状态为 已录入
            context.getWmCustomerKp().setState(KpSignerStateMachine.RECORDED.getState());
            context.getWmCustomerKp().setValid(context.getOldCustomerKp().getValid());
            tempSave(context);
            return null;
        }
        //2-更新KP至提交特批审核
        startKpSpecialAudit(context);
        return null;
    }

    /**
     * 是否匹配当前服务
     *
     * @param certType
     * @return
     */
    @Override
    public Boolean matchKpSignerAbility(byte certType) throws WmCustomerException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.kp.statemachine.ability.KpSignerNotIdCardFlowAbility.matchKpSignerAbility(byte)");
        return !CertTypeEnum.checkIdCardSet(certType);
    }

    /**
     * 处理审核结果
     *
     * @param context
     * @return
     * @throws WmCustomerException
     */
    @Override
    public Boolean dealSignerKpAuditResult(KpSignerStatusMachineContext context) throws WmCustomerException {
        log.info("KpSignerNotIdCardFlowAbility.dealSignerKpAuditResult,开始处理签约人KP审核结果,context={}", JSON.toJSONString(context));
        KpAuditResultBody kpAuditResultBody = context.getKpAuditResultBody();
        WmCustomerKp wmCustomerKp = context.getWmCustomerKp();
        wmCustomerSensitiveWordsService.readKpWhenSelect(wmCustomerKp);

        KpSignerEventEnum kpSignerEventEnum = null;
        boolean needMsgAuthFlag = checkNeedMsgAuth(context.getWmCustomerKp(), context.getWmCustomerKpAudit());
        //校验存在生效的KP法人，否的话则直接授权失败
        boolean existEffectKpLegal = checkKpLegalEffective(context.getCustomerId());
        //设置上下文
        context.setNeedLegalMsfAuth(needMsgAuthFlag);
        //审核驳回->特批驳回
        if (kpAuditResultBody.getAuditResult() == KpAuditResultConstants.AUDIT_RESULT_TYPE_REJECT) {
            kpSignerEventEnum = KpSignerEventEnum.SPECIAL_AUDIT_FAIL;
        } else {
            //KP未生效过
            if (context.getOldCustomerKp().getState() != KpSignerStateMachine.EFFECT.getState()) {
                if (needMsgAuthFlag) {
                    //1-需要短信授权&&不存在生效KP法人->授权失败
                    if (!existEffectKpLegal) {
                        context.getWmCustomerKp().setFailReason("无有效KP法人授权失败");
                        kpSignerEventEnum = KpSignerEventEnum.SIGN_AUTH_FAIL;
                    } else {
                        //2-需短信授权&&存在生效法人->短信授权
                        context.getWmCustomerKp().setFailReason("");
                        kpSignerEventEnum = KpSignerEventEnum.AUDIT_SUC_2_LEGAL_AUTH;
                    }
                } else {
                    //3-不需要短信授权->直接生效
                    kpSignerEventEnum = KpSignerEventEnum.AUDIT_SUC_2_EFFECT;
                    context.setSendEffectiveMq(true);
                }
            } else {
                //已生效过KP代理人审核通过后续流程
                WmCustomerKpTemp kpTemp = wmCustomerKpTempDBMapper.selectByKpId(wmCustomerKp.getId());
                wmCustomerSensitiveWordsService.readKpWhenSelect(kpTemp);
                needMsgAuthFlag = (kpTemp.getLegalAuthType() != null && kpTemp.getLegalAuthType() == LegalAuthTypeEnum.MESSAGE_AUTH.getCode());
                //设置上下文
                context.setNeedLegalMsfAuth(needMsgAuthFlag);
                //需要法人授权
                if (needMsgAuthFlag) {
                    if (!existEffectKpLegal) {
                        kpTemp.setState(KpSignerStateMachine.CHANGE_ORIGIN_SIGNER_CANCEL.getState());
                        kpTemp.setFailReason("无有效KP法人授权失败");
                        kpSignerEventEnum = KpSignerEventEnum.SIGN_AUTH_FAIL;
                    } else {
                        //2-需短信授权&&存在生效法人->短信授权
                        //更新签约人KP数据状态
                        kpTemp.setFailReason("");
                        kpTemp.setState(KpSignerStateMachine.LEGAL_AUTH_AUTHORIZE_ING.getState());
                        kpSignerEventEnum = KpSignerEventEnum.AUDIT_SUC_2_LEGAL_AUTH;
                    }
                } else {
                    boolean isNewProcess = wmCustomerKp.getVersion() != null && (wmCustomerKp.getVersion() == KpVersionEnum.V2.getCode() || wmCustomerKp.getVersion() == KpVersionEnum.V3.getCode());
                    boolean fromUnSignerToSigner = isFromUnSignerToSigner(wmCustomerKp.getSignerType(), kpTemp.getSignerType());
                    boolean fromUnAgentToAgent = isFromUnAgentToAgent(wmCustomerKp.getSignerType(), kpTemp.getSignerType());
                    boolean fromAgentToAgent = isFromAgentToAgent(wmCustomerKp.getSignerType(), kpTemp.getSignerType());
                    boolean isProcessUpdate = fromUnAgentToAgent || (fromAgentToAgent && !wmCustomerKp.getCertNumber().equals(kpTemp.getCertNumber()));
                    boolean isAgentAuthHave = isAgentAuthHave(kpTemp.getHaveAgentAuth());
                    //如果KP的签约人由非代理人变更为代理人/或代理人的身份证号发生变更，无授权书则需要走原签约人授权流程
                    if (isNewProcess && isProcessUpdate && !isAgentAuthHave) {
                        //更新临时数据状态
                        kpTemp.setFailReason("");
                        kpTemp.setState(KpSignerStateMachine.CHANGE_ORIGIN_SIGNER_AUTHING.getState());
                        kpSignerEventEnum = KpSignerEventEnum.AUDIT_SUC_2_OLD_SIGNER_AUTH;
                    } else if (!isNewProcess && !fromUnSignerToSigner
                            && (wmCustomerKp.getCertType() != kpTemp.getCertType() || !wmCustomerKp.getCertNumber().equals(kpTemp.getCertNumber()))) {
                        //变动了证件类型或证件编码需要原签约人授权
                        //更新临时数据状态
                        kpTemp.setFailReason("");
                        kpTemp.setState(KpSignerStateMachine.CHANGE_ORIGIN_SIGNER_AUTHING.getState());
                        kpSignerEventEnum = KpSignerEventEnum.AUDIT_SUC_2_OLD_SIGNER_AUTH;
                    } else {
                        //3-不需要短信授权->直接生效
                        kpSignerEventEnum = KpSignerEventEnum.AUDIT_SUC_2_EFFECT;
                        context.setSendEffectiveMq(true);
                    }
                }
                //设置临时KP
                context.setTempKp(kpTemp);
            }
        }
        //开始执行特批结果处理
        try {
            log.info("KpSignerNotIdCardFlowAbility.dealSignerKpAuditResult,开始执行特批审核结果通知,kpSignerEventEnum={},context={}", kpSignerEventEnum, JSON.toJSONString(context));
            kpSignerNotIdCardStatusTrigger.trigger(kpSignerEventEnum, context);
            log.info("KpSignerNotIdCardFlowAbility.dealSignerKpAuditResult,特批审核结果通知完成,kpSignerEventEnum={},context={}", kpSignerEventEnum, JSON.toJSONString(context));
            return true;
        } catch (Exception e) {
            log.error("KpSignerNotIdCardFlowAbility.dealSignerKpAuditResult,特批审核驳回处理异常,context={}", JSON.toJSONString(context), e);
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "特批结果处理异常");
        }
    }


    /**
     * 短信授权结果处理
     *
     * @param context
     * @return
     * @throws WmCustomerException
     */
    @Override
    public Boolean dealMsgAuthResult(KpSignerStatusMachineContext context) throws WmCustomerException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.service.kp.statemachine.ability.KpSignerNotIdCardFlowAbility.dealMsgAuthResult(KpSignerStatusMachineContext)");

        KpSignerEventEnum kpSignerEventEnum = null;
        if (context.getEcontractTaskStateEnum() == EcontractTaskStateEnum.FAIL) {
            kpSignerEventEnum = KpSignerEventEnum.SIGN_AUTH_FAIL;
        } else if (context.getEcontractTaskStateEnum() == EcontractTaskStateEnum.SUCCESS) {
            kpSignerEventEnum = KpSignerEventEnum.SIGN_AUTH_PASS;
        }

        //校验是否合法事件
        if (kpSignerEventEnum == null) {
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "未知授权通知状态");
        }
        try {
            log.info("KpSignerNotIdCardFlowAbility.dealMsgAuthResult,开始根据授权结果处理KP流程,kpSignerEventEnum={},context={}", kpSignerEventEnum, JSON.toJSONString(context));
            kpSignerNotIdCardStatusTrigger.trigger(kpSignerEventEnum, context);
            log.info("KpSignerNotIdCardFlowAbility.dealMsgAuthResult,授权结果KP流转完成,kpSignerEventEnum={},context={}", kpSignerEventEnum, JSON.toJSONString(context));
            return true;
        } catch (Exception e) {
            log.error("KpSignerNotIdCardFlowAbility.dealMsgAuthResult,授权结果通知处理异常,context={}", JSON.toJSONString(context), e);
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "授权结果处理异常");
        }
    }

    /**
     * 初始化创建
     *
     * @param context
     */
    public void initCreate(KpSignerStatusMachineContext context) throws WmCustomerException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_1", "com.sankuai.meituan.waimai.customer.service.kp.statemachine.ability.KpSignerNotIdCardFlowAbility.initCreate(KpSignerStatusMachineContext)");
        log.info("KpSignerNotIdCardFlowAbility.initCreate,开始执行初始化创建能力,context={}", JSON.toJSONString(context));
        kpSignerNotIdCardStatusTrigger.trigger(KpSignerEventEnum.INIT_CREATE, context);
        log.info("KpSignerNotIdCardFlowAbility.initCreate,初始化创建完成,context={}", JSON.toJSONString(context));
    }

    /**
     * 发起KP特批提审
     *
     * @param context
     * @return
     */
    public Object startKpSpecialAudit(KpSignerStatusMachineContext context) throws WmCustomerException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.service.kp.statemachine.ability.KpSignerNotIdCardFlowAbility.startKpSpecialAudit(KpSignerStatusMachineContext)");
        //构建更新KP对象
        WmCustomerKp oldCustomerKp = context.getOldCustomerKp();
        KpSignerStateMachine newState = KpSignerStateMachine.SPECILA_AUDIT_ING;
        if (oldCustomerKp != null && oldCustomerKp.getState() == KpSignerStateMachine.EFFECT.getState()) {
            newState = KpSignerStateMachine.CHANGE_SPECILA_AUDIT_ING;
        }
        WmCustomerKp updateKp = buildUpdateCustomerKp(context.getWmCustomerKp(), newState.getState());
        context.setOldCustomerKp(context.getWmCustomerKp());
        context.setWmCustomerKp(updateKp);
        log.info("KpSignerNotIdCardFlowAbility.startKpSpecialAudit,开始执行发起KP特批提审,context={}", JSON.toJSONString(context));
        kpSignerNotIdCardStatusTrigger.trigger(KpSignerEventEnum.COMMIT_SPECIAL_AUDIT, context);
        log.info("KpSignerNotIdCardFlowAbility.startKpSpecialAudit,发起KP特批提审完成,context={}", JSON.toJSONString(context));
        return null;
    }

    /**
     * KP签约人暂存
     *
     * @param context
     */
    public void tempSave(KpSignerStatusMachineContext context) throws WmCustomerException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.kp.statemachine.ability.KpSignerNotIdCardFlowAbility.tempSave(KpSignerStatusMachineContext)");
        log.info("KpSignerNotIdCardFlowAbility.tempSave,开始执行KP签约人暂存,context={}", JSON.toJSONString(context));
        kpSignerNotIdCardStatusTrigger.trigger(KpSignerEventEnum.TEMP_STORE, context);
        log.info("KpSignerNotIdCardFlowAbility.tempSave,KP签约人暂存完成,context={}", JSON.toJSONString(context));
    }

}
