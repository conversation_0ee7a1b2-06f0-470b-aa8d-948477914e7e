package com.sankuai.meituan.waimai.customer.service.kp.statemachine.action.signer;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.sankuai.meituan.waimai.customer.dao.WmCustomerKpAuditMapper;
import com.sankuai.meituan.waimai.customer.dao.WmCustomerKpDBMapper;
import com.sankuai.meituan.waimai.customer.dao.WmCustomerKpTempDBMapper;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerKpAudit;
import com.sankuai.meituan.waimai.customer.mq.service.MafkaMessageSendManager;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerSensitiveWordsService;
import com.sankuai.meituan.waimai.customer.service.kp.*;
import com.sankuai.meituan.waimai.customer.service.kp.statemachine.action.KpSignerAbstractAction;
import com.sankuai.meituan.waimai.customer.service.kp.statemachine.constant.event.KpSignerEventEnum;
import com.sankuai.meituan.waimai.customer.service.kp.statemachine.context.KpSignerStatusMachineContext;
import com.sankuai.meituan.waimai.customer.service.kp.statemachine.machine.KpSignerBaseSM;
import com.sankuai.meituan.waimai.thrift.bo.WmCustomerDiffCellBo;
import com.sankuai.meituan.waimai.thrift.customer.constant.CertTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.KpAuditConstants;
import com.sankuai.meituan.waimai.thrift.customer.constant.KpSignerStateMachine;
import com.sankuai.meituan.waimai.thrift.customer.constant.KpSignerTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.customer.CertNumberModifyEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.kp.WmCustomerKp;
import com.sankuai.meituan.waimai.thrift.customer.domain.kp.WmCustomerKpTemp;
import com.sankuai.meituan.waimai.thrift.customer.domain.mq.CustomerMQBody;
import com.sankuai.meituan.waimai.thrift.customer.domain.mq.CustomerMQEventEnum;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import com.sankuai.meituan.waimai.thrift.util.DiffUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.xml.ws.Action;

import java.util.Collections;
import java.util.List;

import static com.sankuai.meituan.waimai.thrift.customer.constant.KpConstants.EFFECTIVE;
import static com.sankuai.meituan.waimai.thrift.customer.constant.KpConstants.UN_VALID;

/**
 * <AUTHOR>
 * @date 20230828
 * @desc 授权成功直接生效
 */
@Service
@Slf4j
public class AuthSuc2EffectAction extends KpSignerAbstractAction {

    @Autowired
    private WmCustomerKpLogService wmCustomerKpLogService;

    @Autowired
    private WmCustomerKpDBMapper wmCustomerKpDBMapper;

    @Autowired
    private WmCustomerKpTempDBMapper wmCustomerKpTempDBMapper;

    @Autowired
    private WmCustomerKpAuditMapper wmCustomerKpAuditMapper;

    @Autowired
    private WmCustomerSensitiveWordsService wmCustomerSensitiveWordsService;

    @Autowired
    private WmCustomerKpBuryingPointService wmCustomerKpBuryingPointService;

    @Autowired
    private WmCustomerKpAuditService wmCustomerKpAuditService;

    @Autowired
    private MafkaMessageSendManager mafkaMessageSendManager;

    @Autowired
    private WmCustomerKpRealAuthService wmCustomerKpRealAuthService;

    @Autowired
    private WmCustomerKpService wmCustomerKpService;

    @Autowired
    private DifferentCustomerKpService differentCustomerKpService;

    /**
     * 证件类型为身份证
     */
    private static final List<Integer> ID_CERT_TYPE = Lists.newArrayList(Integer.valueOf(CertTypeEnum.ID_CARD.getType()),
            Integer.valueOf(CertTypeEnum.ID_CARD_TEMP.getType()),
            Integer.valueOf(CertTypeEnum.ID_CARD_COPY.getType()));


    /**
     * 动作执行
     *
     * @param fromState      原状态
     * @param toState        新状态
     * @param eventEnum      事件
     * @param context        上下文
     * @param kpSignerBaseSM 状态机
     */
    @Override
    public void execute(KpSignerStateMachine fromState, KpSignerStateMachine toState,
                        KpSignerEventEnum eventEnum, KpSignerStatusMachineContext context,
                        KpSignerBaseSM kpSignerBaseSM) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "c.s.meituan.waimai.customer.service.kp.statemachine.action.signer.AuthSuc2EffectAction.execute(KpSignerStateMachine,KpSignerStateMachine,KpSignerEventEnum,KpSignerStatusMachineContext,KpSignerBaseSM)");
        log.info("AuthSuc2EffectAction.execute,授权成功直接生效action,from={},to={},context={}", fromState, toState, JSON.toJSONString(context));
        try {
            boolean haveEffectFlag = context.getExistEffectiveFlag();
            WmCustomerKpAudit wmCustomerKpAudit = context.getWmCustomerKpAudit();
            WmCustomerKp customerKp = context.getWmCustomerKp();
            Integer customerId = context.getCustomerId();
            WmCustomerKpTemp kpTemp = context.getTempKp();
            WmCustomerKp newCustomerKp = null;

            List<WmCustomerDiffCellBo> diffCellBos = null;
            String stateMsg = "";
            //未生效过
            if (!haveEffectFlag) {
                stateMsg = "授权成功";
                //更新审核表
                wmCustomerKpAudit.setResult(stateMsg);
                wmCustomerKpAudit.setValid(UN_VALID);
                customerKp.setState(KpSignerStateMachine.EFFECT.getState());
                customerKp.setFailReason(stateMsg);
                customerKp.setEffective(EFFECTIVE);

                wmCustomerKpAuditMapper.updateByPrimaryKey(wmCustomerKpAudit);
                //更新签约人KP表
                wmCustomerSensitiveWordsService.writeKpWhenInsertOrUpdate(customerKp);
                wmCustomerKpDBMapper.updateByPrimaryKey(customerKp);
                log.info("AuthSuc2EffectAction.execute,未生效KP短信授权成功直接生效完成，customerKp={},wmCustomerKpAudit={}", JSON.toJSONString(customerKp), JSON.toJSONString(wmCustomerKpAudit));
            } else {
                //已生效
                wmCustomerKpAudit.setResult("授权成功");
                stateMsg = "变更授权成功";
                //重置法人授权的状态说明
                if (wmCustomerKpAudit.getType() == KpAuditConstants.TYPE_AUTH_FOR_SIGNER_AGENT_LEGAL) {
                    stateMsg = "授权成功";
                }

                try {
                    diffCellBos = DiffUtil.compare(customerKp, kpTemp, differentCustomerKpService.getKpDiffFieldsMap());
                } catch (WmCustomerException e) {
                    log.warn("diff 异常, wmCustomerKp={}, kpTemp={}", JSON.toJSONString(customerKp), JSON.toJSONString(kpTemp), e);
                    diffCellBos = Collections.emptyList();
                }
                String diffLog = DiffUtil.getDiffLog(diffCellBos);
                newCustomerKp = wmCustomerKpAuditService.transformWmCustomerKpTemp(kpTemp);
                newCustomerKp.setEffective(EFFECTIVE);

                //使临时签约人变为生效签约人
                wmCustomerKpService.tempKpEffect(kpTemp, customerKp);
                //更新临时变更数据并删除
                kpTemp.setState(KpSignerStateMachine.EFFECT.getState());
                kpTemp.setFailReason("授权成功");
                kpTemp.setValid(UN_VALID);
                //记录操作日志
                wmCustomerKpLogService.insertOplog(customerId, customerKp, "【" + stateMsg + "】\n" + diffLog, 0, "短信授权");

                wmCustomerKpDBMapper.updateCertNumberModify(CertNumberModifyEnum.YES.getCode(), customerKp.getId());

                //更新审核表
                wmCustomerKpAuditMapper.updateByPrimaryKey(wmCustomerKpAudit);
                //更新临时签约人数据
                wmCustomerSensitiveWordsService.writeKpSourceWhenUpdate(kpTemp);
                wmCustomerKpTempDBMapper.updateByPrimaryKey(kpTemp);
                log.info("AuthSuc2EffectAction.execute,生效KP短信授权成功直接生效完成，customerKp={},wmCustomerKpAudit={}", JSON.toJSONString(customerKp), JSON.toJSONString(wmCustomerKpAudit));
            }

            //添加操作日志
            wmCustomerKpLogService.changeState(customerId, customerKp, stateMsg, 0, "短信授权");
            //埋点打点
            wmCustomerKpBuryingPointService.afterSingKp(diffCellBos, wmCustomerKpAudit.getAcctId(), newCustomerKp, customerKp);
            //生效则发送消息
            if (context.isSendEffectiveMq()) {
                mafkaMessageSendManager.send(new CustomerMQBody(customerId, CustomerMQEventEnum.CUSTOMER_KP_EFFECTIVE, ""));
            }
            /**
             * 满足如下条件掉客户四要素标签
             *  1.授权通过
             *  2.生效
             *  3.签约类型为非签约人或者证件类型为非身份证
             */
            if (customerKp.getSignerType() != KpSignerTypeEnum.SIGNER.getType()
                    || !ID_CERT_TYPE.contains((int) customerKp.getCertType())) {
                wmCustomerKpRealAuthService.deleteFourEleTag(customerKp.getCustomerId());
            }

        } catch (Exception e) {
            log.error("AuthSuc2EffectAction.execute,短信授权成功直接生效发生异常,context={}", JSON.toJSONString(context));
        }

    }
}
