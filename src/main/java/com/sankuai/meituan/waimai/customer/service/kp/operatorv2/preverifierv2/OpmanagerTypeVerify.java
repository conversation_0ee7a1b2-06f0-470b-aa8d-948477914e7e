package com.sankuai.meituan.waimai.customer.service.kp.operatorv2.preverifierv2;

import com.sankuai.meituan.waimai.customer.domain.WmCustomerDB;
import com.sankuai.meituan.waimai.thrift.customer.domain.kp.WmCustomerKp;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Author: 窦旭蒙
 * MIS: douxumeng
 * Date: 2020-07-03 22:12
 * Email: <EMAIL>
 * Desc:
 */
@Service
public class OpmanagerTypeVerify extends KpPreverify {

    @Override
    public Object verify(WmCustomerDB wmCustomer, List<WmCustomerKp> oldCustomerKpList, WmCustomerKp insertKp, WmCustomerKp updateKp, WmCustomerKp deleteKp, int uid, String uname) throws WmCustomerException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.service.kp.operatorv2.preverifierv2.OpmanagerTypeVerify.verify(WmCustomerDB,List,WmCustomerKp,WmCustomerKp,WmCustomerKp,int,String)");
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.service.kp.operatorv2.preverifierv2.OpmanagerTypeVerify.verify(WmCustomerDB,List,WmCustomerKp,WmCustomerKp,WmCustomerKp,int,String)");
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.service.kp.operatorv2.preverifierv2.OpmanagerTypeVerify.verify(WmCustomerDB,List,WmCustomerKp,WmCustomerKp,WmCustomerKp,int,String)");
        return new Object();
    }
}
