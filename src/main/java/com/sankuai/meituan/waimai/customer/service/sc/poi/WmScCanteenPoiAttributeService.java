package com.sankuai.meituan.waimai.customer.service.sc.poi;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.sankuai.inf.octo.mns.model.HostEnv;
import com.sankuai.inf.octo.mns.util.ProcessInfoUtil;
import com.sankuai.meituan.waimai.customer.config.MccCustomerConfig;
import com.sankuai.meituan.waimai.customer.dao.sc.WmCanteenMapper;
import com.sankuai.meituan.waimai.customer.dao.sc.WmScCanteenPoiAttributeMapper;
import com.sankuai.meituan.waimai.customer.ddd.adapter.WmPoiQueryAdapter;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerDB;
import com.sankuai.meituan.waimai.customer.domain.sc.*;
import com.sankuai.meituan.waimai.customer.domain.sc.canteenstall.WmCanteenStallBindDO;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerService;
import com.sankuai.meituan.waimai.customer.service.sc.WmScCanteenService;
import com.sankuai.meituan.waimai.customer.service.sc.WmScSchoolService;
import com.sankuai.meituan.waimai.customer.service.sc.canteenstall.WmCanteenStallBindService;
import com.sankuai.meituan.waimai.customer.service.sc.dao.WmScCanteenPoiAuditDetailDao;
import com.sankuai.meituan.waimai.thrift.constants.WmPoiFieldQueryConstant;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.ValidEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.sc.WmScCodeConstants;
import com.sankuai.meituan.waimai.thrift.customer.domain.sc.WmScCanteenPoiAttributeRefreshResultBO;
import com.sankuai.meituan.waimai.thrift.domain.WmPoiAggre;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import com.sankuai.meituan.waimai.thrift.exception.WmSchCantException;
import com.sankuai.meituan.waimai.util.DaxiangUtilV2;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 食堂门店属性业务
 */
@Slf4j
@Service
public class WmScCanteenPoiAttributeService {

    @Autowired
    private WmScCanteenPoiAttributeMapper wmScCanteenPoiAttributeMapper;

    @Autowired
    private WmCanteenMapper wmCanteenMapper;

    @Autowired
    private WmScSchoolService wmScSchoolService;

    @Autowired
    private WmCustomerService wmCustomerService;

    @Autowired
    private WmScCanteenService wmScCanteenService;

    @Autowired
    private WmCanteenStallBindService wmCanteenStallBindService;

    @Autowired
    private WmScCanteenPoiAuditDetailDao wmScCanteenPoiAuditDetailDao;

    @Autowired
    private WmPoiQueryAdapter wmPoiQueryAdapter;

    private final String LOG_PREFIX = "WmScCanteenPoiAttributeService";

    /**
     * 食堂信息变更时（变更学校或者食堂承包商）
     */
    public void updateForCanteenUpdate(WmCanteenDB canteen) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.sc.poi.WmScCanteenPoiAttributeService.updateForCanteenUpdate(com.sankuai.meituan.waimai.customer.domain.sc.WmCanteenDB)");
        log.info("[WmScCanteenPoiAttributeService.updateForCanteenUpdate] canteen = {}", JSONObject.toJSONString(canteen));
        if (canteen == null || canteen.getId() == null || canteen.getId() <= 0) {
            return;
        }

        List<WmScCanteenPoiAttributeDO> wmScCanteenPoiAttributeDoList = selectByCanteenPrimaryIdByMaster(canteen.getId());
        if (CollectionUtils.isEmpty(wmScCanteenPoiAttributeDoList)) {
            return;
        }

        WmSchoolDB wmSchoolDb = wmScSchoolService.findByIdList(canteen.getSchoolId());
        WmCustomerDB wmCustomerDb = getCustomer(canteen.getContractorId());
        int schoolPrimaryIdNew = (wmSchoolDb == null ? 0 : wmSchoolDb.getId());
        int customerPrimaryId = (wmCustomerDb == null ? 0 : wmCustomerDb.getId());
        List<WmScCanteenPoiAttributeDO> updateList = new ArrayList<>();
        for (WmScCanteenPoiAttributeDO wmScCanteenPoiAttributeDo : wmScCanteenPoiAttributeDoList) {
            if (wmScCanteenPoiAttributeDo.getValid() == ValidEnum.INVALID.getTypeInt()) {
                continue;
            }
            if (schoolPrimaryIdNew != wmScCanteenPoiAttributeDo.getSchoolPrimaryId()
                    || customerPrimaryId != wmScCanteenPoiAttributeDo.getCustomerPrimaryId()) {
                //食堂的学校和承包商发生变化
                WmScCanteenPoiAttributeDO update = new WmScCanteenPoiAttributeDO();
                update.setId(wmScCanteenPoiAttributeDo.getId());
                update.setSchoolPrimaryId(schoolPrimaryIdNew);
                update.setSchoolId((wmSchoolDb == null ? 0 : wmSchoolDb.getSchoolId()));
                update.setCustomerPrimaryId(customerPrimaryId);
                update.setMtCustomerId((wmCustomerDb == null ? 0 : wmCustomerDb.getMtCustomerId()));
                updateList.add(update);
            }
        }
        doUpdate(updateList);
    }

    /**
     * 食堂强制解绑门店时删除食堂门店属性记录
     *
     * @param canteenPrimaryId 食堂物理主键ID
     * @param wmPoiId          外卖门店ID
     */
    public void deleteCanteenPoiAttribute(Integer canteenPrimaryId, Long wmPoiId) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.service.sc.poi.WmScCanteenPoiAttributeService.deleteCanteenPoiAttribute(java.lang.Integer,java.lang.Long)");
        log.info("[WmScCanteenPoiAttributeService.deleteCanteenPoiAttribute] input param: canteenPrimaryId={}, wmPoiId={}",
                canteenPrimaryId, wmPoiId);
        if (canteenPrimaryId == null || canteenPrimaryId <= 0 || wmPoiId == null || wmPoiId <= 0) {
            return;
        }

        WmScCanteenPoiAttributeSearchCondition condition = new WmScCanteenPoiAttributeSearchCondition();
        condition.setCanteenPrimaryId(canteenPrimaryId);
        condition.setValid(ValidEnum.VALID.getTypeInt());
        condition.setWmPoiId(wmPoiId);
        List<WmScCanteenPoiAttributeDO> list = wmScCanteenPoiAttributeMapper.selectByCondition(condition);
        if (CollectionUtils.isEmpty(list)) {
            return;
        }

        for (WmScCanteenPoiAttributeDO wmScCanteenPoiAttributeDo : list) {
            WmScCanteenPoiAttributeDO update = new WmScCanteenPoiAttributeDO();
            update.setId(wmScCanteenPoiAttributeDo.getId());
            update.setValid(ValidEnum.INVALID.getTypeInt());
            wmScCanteenPoiAttributeMapper.updateByPrimaryKeySelective(update);
        }
    }

    /**
     * 查询承包商
     */
    private WmCustomerDB getCustomer(Integer customerPrimaryId) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.service.sc.poi.WmScCanteenPoiAttributeService.getCustomer(java.lang.Integer)");
        try {
            if (customerPrimaryId == null || customerPrimaryId <= 0) {
                return null;
            }
            return wmCustomerService.selectCustomerById(customerPrimaryId);
        } catch (Exception e) {
            log.error("{} getCustomer customerPrimaryId={}", LOG_PREFIX, JSON.toJSONString(customerPrimaryId), e);
        }
        return null;
    }

    /**
     * 刷新食堂门店属性表(全量)
     * 调用场景如下：
     * 1.食堂绑定门店电子签约成功后更新食堂门店属性表
     * 2.兜底逻辑调用
     *
     * @param canteen      食堂对象
     * @param finalPoiList 食堂最终绑定的门店ID集合
     * @param isInit       是否初始化逻辑
     */
    public WmScCanteenPoiAttributeRefreshResultBO refreshCanteenPoiAttribute(WmCanteenDB canteen, List<Long> finalPoiList, boolean isInit) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_2", "com.sankuai.meituan.waimai.customer.service.sc.poi.WmScCanteenPoiAttributeService.refreshCanteenPoiAttribute(com.sankuai.meituan.waimai.customer.domain.sc.WmCanteenDB,java.util.List,boolean)");
        log.info("[WmScCanteenPoiAttributeService.refreshCanteenPoiAttribute] canteen = {}, finalPoiList = {}",
                JSON.toJSONString(canteen), JSON.toJSONString(finalPoiList));
        if (canteen == null || canteen.getId() == null || canteen.getId() <= 0) {
            return null;
        }

        WmScCanteenPoiAttributeRefreshResultBO result = new WmScCanteenPoiAttributeRefreshResultBO();
        result.setCanteenPrimaryId(canteen.getId());

        // 获取该食堂下所有已经绑定的门店
        List<WmScCanteenPoiAttributeDO> allValidWmPoiIdList = getValidPoiAttributeDOListByCanteenPrimaryIdAndWmPoiIdList(canteen.getId(), finalPoiList);
        // 如果当前食堂没有绑定门店，需要进行清空
        if (CollectionUtils.isEmpty(finalPoiList)) {
            // 门店全部删除掉了
            result.setDeleteWmPoiId(doDelete(allValidWmPoiIdList));
            log.error("[WmScCanteenPoiAttributeService.refreshCanteenPoiAttribute] result = {}", JSONObject.toJSONString(result));
            return result;
        }

        WmSchoolDB wmSchoolDb = wmScSchoolService.findByIdList(canteen.getSchoolId());
        WmCustomerDB wmCustomerDb = null;
        if (canteen.getContractorId() != null && canteen.getContractorId() > 0) {
            wmCustomerDb = getCustomer(canteen.getContractorId());
            if (wmCustomerDb == null) {
                log.error("refreshCanteenPoiAttribute，查询不到客户，客户ID={}", canteen.getContractorId());
            }
        }
        int schoolPrimaryId = (wmSchoolDb == null ? 0 : wmSchoolDb.getId());
        int customerPrimaryId = (wmCustomerDb == null ? 0 : wmCustomerDb.getId());

        // 新增、修改的食堂门店属性集合
        List<WmScCanteenPoiAttributeDO> insertList = new ArrayList<>();
        List<WmScCanteenPoiAttributeDO> updateList = new ArrayList<>();
        // 新增、修改、删除的门店ID集合
        List<Long> insertWmPoiIdList = new ArrayList<>();
        List<Long> updateWmPoiIdList = new ArrayList<>();
        List<Long> deleteWmPoiIdList = new ArrayList<>();

        Set<Long> finalPoiSet = Sets.newHashSet(finalPoiList);
        // 已存在有效的更新
        List<WmScCanteenPoiAttributeDO> existValidWmPoiIdList = getValidPoiAttributeDOListByCanteenPrimaryIdAndWmPoiIdList(canteen.getId(), finalPoiList);

        Map<Long, WmScCanteenPoiAttributeDO> existValidRelationMap = existValidWmPoiIdList.stream()
                .collect(Collectors.toMap(WmScCanteenPoiAttributeDO::getWmPoiId, x -> x));

        // 已存在无效的改为有效
        List<WmScCanteenPoiAttributeDO> existUnValidWmPoiIdList = getInValidPoiAttributeDOListByCanteenPrimaryIdAndWmPoiIdList(canteen.getId(), finalPoiList);

        Map<Long, WmScCanteenPoiAttributeDO> existUnValidRelationMap = existUnValidWmPoiIdList.stream()
                .collect(Collectors.toMap(WmScCanteenPoiAttributeDO::getWmPoiId, x -> x));

        List<WmScCanteenPoiAttributeDO> realDeleteList = Lists.newArrayList();
        for (Long wmPoiId : finalPoiSet) {
            if (existValidRelationMap.get(wmPoiId) != null) {
                WmScCanteenPoiAttributeDO wmScCanteenPoiAttributeDO = existValidRelationMap.get(wmPoiId);
                // 如果食堂门店属性记录表已经存在，则判断学校和承包商是否有变更
                if (schoolPrimaryId != wmScCanteenPoiAttributeDO.getSchoolPrimaryId()
                        || customerPrimaryId != wmScCanteenPoiAttributeDO.getCustomerPrimaryId()) {
                    refreshSchoolCustomer(wmScCanteenPoiAttributeDO, wmSchoolDb, wmCustomerDb);
                    updateList.add(wmScCanteenPoiAttributeDO);
                    updateWmPoiIdList.add(wmPoiId);
                }
                // 数据库有错误数据，需要物理删除
                if (existUnValidRelationMap.get(wmPoiId) != null) {
                    realDeleteList.add(existUnValidRelationMap.get(wmPoiId));
                }
            } else if (existUnValidRelationMap.get(wmPoiId) != null) {
                WmScCanteenPoiAttributeDO wmScCanteenPoiAttributeDO = existUnValidRelationMap.get(wmPoiId);
                // 已经删除的记录重新启用
                reValid(wmScCanteenPoiAttributeDO);
                refreshSchoolCustomer(wmScCanteenPoiAttributeDO, wmSchoolDb, wmCustomerDb);
                updateList.add(wmScCanteenPoiAttributeDO);
                insertWmPoiIdList.add(wmPoiId);
            } else {
                // 新门店构建新对象
                insertList.add(createNewDo(wmPoiId, canteen, wmSchoolDb, wmCustomerDb, isInit));
                insertWmPoiIdList.add(wmPoiId);
            }
        }

        if (CollectionUtils.isNotEmpty(allValidWmPoiIdList)) {
            for (WmScCanteenPoiAttributeDO wmScCanteenPoiAttributeDoExist : allValidWmPoiIdList) {
                if (!finalPoiList.contains(wmScCanteenPoiAttributeDoExist.getWmPoiId()) && reUnValid(wmScCanteenPoiAttributeDoExist)) {
                    // 已经删除的食堂门店需要删除食堂门店属性表记录
                    updateList.add(wmScCanteenPoiAttributeDoExist);
                    deleteWmPoiIdList.add(wmScCanteenPoiAttributeDoExist.getWmPoiId());
                }
            }
        }

        doRealDelete(realDeleteList);
        doUpdate(updateList);
        doInsert(insertList);
        result.setNewWmPoiId(insertWmPoiIdList);
        result.setUpdateWmPoiId(updateWmPoiIdList);
        result.setDeleteWmPoiId(deleteWmPoiIdList);

        if (CollectionUtils.isNotEmpty(result.getNewWmPoiId())
                || CollectionUtils.isNotEmpty(result.getUpdateWmPoiId())
                || CollectionUtils.isNotEmpty(result.getDeleteWmPoiId())) {
            log.error("[WmScCanteenPoiAttributeService.refreshCanteenPoiAttribute] result = {}", JSONObject.toJSONString(result));
        }
        return result;
    }


    private List<WmScCanteenPoiAttributeDO> getValidPoiAttributeDOListByCanteenPrimaryIdAndWmPoiIdList(Integer canteenPrimaryId, List<Long> wmPoiIdList) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_2", "com.sankuai.meituan.waimai.customer.service.sc.poi.WmScCanteenPoiAttributeService.getValidPoiAttributeDOListByCanteenPrimaryIdAndWmPoiIdList(java.lang.Integer,java.util.List)");
        WmScCanteenPoiAttributeSearchCondition condition = new WmScCanteenPoiAttributeSearchCondition();
        condition.setCanteenPrimaryId(canteenPrimaryId);
        condition.setWmPoiIdList(wmPoiIdList);
        condition.setValid(ValidEnum.VALID.getTypeInt());

        return wmScCanteenPoiAttributeMapper.selectByCondition(condition);
    }

    private List<WmScCanteenPoiAttributeDO> getInValidPoiAttributeDOListByCanteenPrimaryIdAndWmPoiIdList(Integer canteenPrimaryId, List<Long> wmPoiIdList) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.sc.poi.WmScCanteenPoiAttributeService.getInValidPoiAttributeDOListByCanteenPrimaryIdAndWmPoiIdList(java.lang.Integer,java.util.List)");
        WmScCanteenPoiAttributeSearchCondition condition = new WmScCanteenPoiAttributeSearchCondition();
        condition.setCanteenPrimaryId(canteenPrimaryId);
        condition.setWmPoiIdList(wmPoiIdList);
        condition.setValid(ValidEnum.INVALID.getTypeInt());

        return wmScCanteenPoiAttributeMapper.selectByCondition(condition);
    }

    /**
     * 保存食堂门店属性表(增量)
     * 调用场景如下：
     * 绑定时使用
     *
     * @param canteen 食堂对象
     * @param poiList 食堂最终绑定的门店ID集合
     */
    public void saveCanteenPoiAttribute(WmCanteenDB canteen, List<Long> poiList) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_2", "com.sankuai.meituan.waimai.customer.service.sc.poi.WmScCanteenPoiAttributeService.saveCanteenPoiAttribute(com.sankuai.meituan.waimai.customer.domain.sc.WmCanteenDB,java.util.List)");
        log.info("[WmScCanteenPoiAttributeService.saveCanteenPoiAttribute] input param: canteen = {}, poiList = {}",
                JSON.toJSONString(canteen), JSON.toJSONString(poiList));
        if (canteen == null || canteen.getId() == null || canteen.getId() <= 0 || poiList.isEmpty()) {
            return;
        }
        WmSchoolDB wmSchoolDb = wmScSchoolService.findByIdList(canteen.getSchoolId());
        WmCustomerDB wmCustomerDb = null;
        if (canteen.getContractorId() != null && canteen.getContractorId() > 0) {
            wmCustomerDb = getCustomer(canteen.getContractorId());
            if (wmCustomerDb == null) {
                log.error("refreshCanteenPoiAttribute，查询不到客户，客户ID={}", canteen.getContractorId());
            }
        }

        int schoolPrimaryId = (wmSchoolDb == null ? 0 : wmSchoolDb.getId());
        int customerPrimaryId = (wmCustomerDb == null ? 0 : wmCustomerDb.getId());
        // 新增、修改的食堂门店属性集合
        List<WmScCanteenPoiAttributeDO> insertList = new ArrayList<>();
        List<WmScCanteenPoiAttributeDO> updateList = new ArrayList<>();

        List<WmScCanteenPoiAttributeDO> wmScCanteenPoiAttributeDoList = selectByCanteenPrimaryIdByMaster(canteen.getId());
        for (Long poiId : poiList) {
            if (poiId == null || poiId <= 0) {
                continue;
            }
            WmScCanteenPoiAttributeDO wmScCanteenPoiAttributeDoExist = getExistPoiAttribute(wmScCanteenPoiAttributeDoList, poiId);
            if (wmScCanteenPoiAttributeDoExist != null) {
                if (reValid(wmScCanteenPoiAttributeDoExist)) {
                    // 已经删除的记录重新启用
                    refreshSchoolCustomer(wmScCanteenPoiAttributeDoExist, wmSchoolDb, wmCustomerDb);
                    updateList.add(wmScCanteenPoiAttributeDoExist);
                } else {
                    // 如果食堂门店属性记录表已经存在，则判断学校和承包商是否有变更
                    if (schoolPrimaryId != wmScCanteenPoiAttributeDoExist.getSchoolPrimaryId()
                            || customerPrimaryId != wmScCanteenPoiAttributeDoExist.getCustomerPrimaryId()) {
                        refreshSchoolCustomer(wmScCanteenPoiAttributeDoExist, wmSchoolDb, wmCustomerDb);
                        updateList.add(wmScCanteenPoiAttributeDoExist);
                    }
                }
            } else {
                // 新门店构建新对象
                insertList.add(createNewDo(poiId, canteen, wmSchoolDb, wmCustomerDb, false));
            }
        }
        doUpdate(updateList);
        doInsert(insertList);
    }

    /**
     * 换绑成功后 修改食堂门店属性表(增量)
     * 调用场景如下：
     * 换绑时使用
     *
     * @param canteenIdTo   换绑前食堂对象
     * @param canteenIdFrom 换绑后食堂对象
     * @param poiList       食堂最终绑定的门店ID集合
     */
    public void modifyCanteenPoiAttribute(int canteenIdTo, int canteenIdFrom, List<Long> poiList) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.service.sc.poi.WmScCanteenPoiAttributeService.modifyCanteenPoiAttribute(int,int,java.util.List)");
        if (canteenIdFrom <= 0 || canteenIdTo <= 0 || CollectionUtils.isEmpty(poiList)) {
            return;
        }
        log.info("modifyCanteenPoiAttribute canteenIdTo={},canteenIdFrom={},poiList={}", canteenIdTo, canteenIdFrom, JSONObject.toJSONString(poiList));
        // 1. 获取目标食堂的详细信息
        WmCanteenDB canteenTo = wmCanteenMapper.selectCanteenById(canteenIdTo);
        WmCustomerDB wmCustomerToDb = null;
        if (canteenTo.getContractorId() != null && canteenTo.getContractorId() > 0) {
            wmCustomerToDb = getCustomer(canteenTo.getContractorId());
            if (wmCustomerToDb == null) {
                log.error("modifyCanteenPoiAttribute，查询不到客户，客户ID={}", canteenTo.getContractorId());
            }
        }
        int canteenId = canteenTo == null ? 0 : canteenTo.getCanteenId();
        int canteenPrimaryId = canteenTo == null ? 0 : canteenTo.getId();
        int customerPrimaryId = wmCustomerToDb == null ? 0 : wmCustomerToDb.getId();
        long mtCustomerId = wmCustomerToDb == null ? 0L : wmCustomerToDb.getMtCustomerId();
        int time = (int) (System.currentTimeMillis() / 1000L);

        // 3. 准备换绑操作
        // 3.1 获取门店与新食堂的关系-无效
        WmScCanteenPoiAttributeSearchCondition conditionTo = new WmScCanteenPoiAttributeSearchCondition();
        conditionTo.setCanteenPrimaryId(canteenIdTo);
        conditionTo.setWmPoiIdList(poiList);
        conditionTo.setValid(ValidEnum.INVALID.getTypeInt());
        List<WmScCanteenPoiAttributeDO> existUnValidTo = wmScCanteenPoiAttributeMapper.selectByCondition(conditionTo);
        Map<Long, WmScCanteenPoiAttributeDO> existUnValidToMap = existUnValidTo.stream().collect(Collectors.toMap(WmScCanteenPoiAttributeDO::getWmPoiId, x -> x));

        // 3.2 获取门店与原食堂的关系-有效
        WmScCanteenPoiAttributeSearchCondition conditionFrom = new WmScCanteenPoiAttributeSearchCondition();
        conditionFrom.setCanteenPrimaryId(canteenIdFrom);
        conditionFrom.setWmPoiIdList(poiList);
        conditionFrom.setValid(ValidEnum.VALID.getTypeInt());
        List<WmScCanteenPoiAttributeDO> wmScCanteenPoiAttributeDoList = wmScCanteenPoiAttributeMapper.selectByCondition(conditionFrom);
        Map<Long, WmScCanteenPoiAttributeDO> existValidToMap = wmScCanteenPoiAttributeDoList.stream().collect(Collectors.toMap(WmScCanteenPoiAttributeDO::getWmPoiId, x -> x));

        Set<Long> wmPoiIdSet = Sets.newHashSet(poiList);
        for (Long wmPoiId : wmPoiIdSet) {
            WmScCanteenPoiAttributeDO existUnValid = existUnValidToMap.get(wmPoiId);
            WmScCanteenPoiAttributeDO existValid = existValidToMap.get(wmPoiId);
            if (existValid == null) {
                log.warn("换绑食堂未找到原食堂门店绑定关系 canteenIdTo={},canteenIdFrom={},wmPoiId={}", canteenIdTo, canteenIdFrom, wmPoiId);
                continue;
            }
            // 4. 执行换绑操作
            // 4.1 如果门店与新食堂在关联属性表中没有无效记录，则直接更新原食堂的有效绑定关系
            if (existUnValid == null) {
                // 换绑食堂信息
                existValid.setCanteenId(canteenId);
                existValid.setCanteenPrimaryId(canteenPrimaryId);
                existValid.setCustomerPrimaryId(customerPrimaryId);
                existValid.setMtCustomerId(mtCustomerId);
                existValid.setUtime(time);
                wmScCanteenPoiAttributeMapper.updateByPrimaryKeySelective(existValid);
                log.info("换绑食堂门店绑定关系 canteenIdTo={},canteenIdFrom={},wmPoiId={}", canteenIdTo, canteenIdFrom, wmPoiId);
            } else {
                // 4.2 如果门店与新食堂在关联属性表中有无效记录，则将有效的原食堂绑定关系置为无效，并将无效的新食堂绑定关系置为有效
                // 将有效的原食堂绑定关系置为无效
                existValid.setValid(ValidEnum.INVALID.getTypeInt());
                existValid.setUtime(time);
                wmScCanteenPoiAttributeMapper.updateByPrimaryKeySelective(existValid);

                // 将无效的新食堂绑定关系置为有效
                existUnValid.setValid(ValidEnum.VALID.getTypeInt());
                existUnValid.setCanteenId(canteenId);
                existUnValid.setCanteenPrimaryId(canteenPrimaryId);
                existUnValid.setCustomerPrimaryId(customerPrimaryId);
                existUnValid.setMtCustomerId(mtCustomerId);
                existUnValid.setUtime(time);
                wmScCanteenPoiAttributeMapper.updateByPrimaryKeySelective(existUnValid);
                log.info("换绑-更新食堂门店绑定关系 canteenIdTo={},canteenIdFrom={},wmPoiId={}", canteenIdTo, canteenIdFrom, wmPoiId);
            }
        }
    }

    /**
     * 更新食堂门店属性记录的学校和承包商信息
     */
    private void refreshSchoolCustomer(WmScCanteenPoiAttributeDO wmScCanteenPoiAttributeDoExist, WmSchoolDB wmSchoolDb, WmCustomerDB wmCustomerDb) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.sc.poi.WmScCanteenPoiAttributeService.refreshSchoolCustomer(WmScCanteenPoiAttributeDO,WmSchoolDB,WmCustomerDB)");
        wmScCanteenPoiAttributeDoExist.setCustomerPrimaryId(wmCustomerDb == null ? 0 : wmCustomerDb.getId());
        wmScCanteenPoiAttributeDoExist.setSchoolPrimaryId(wmSchoolDb == null ? 0 : wmSchoolDb.getId());
        wmScCanteenPoiAttributeDoExist.setSchoolId(wmSchoolDb == null ? 0 : wmSchoolDb.getSchoolId());
        wmScCanteenPoiAttributeDoExist.setMtCustomerId(wmCustomerDb == null ? 0 : wmCustomerDb.getMtCustomerId());
    }

    /**
     * 新增食堂门店属性记录
     *
     * @param insertList 新增集合
     */
    public void doInsert(List<WmScCanteenPoiAttributeDO> insertList) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_1", "com.sankuai.meituan.waimai.customer.service.sc.poi.WmScCanteenPoiAttributeService.doInsert(java.util.List)");
        log.info("[WmScCanteenPoiAttributeService.doInsert] input param: insertList = {}", JSONObject.toJSONString(insertList));
        if (CollectionUtils.isEmpty(insertList)) {
            return;
        }

        for (WmScCanteenPoiAttributeDO wmScCanteenPoiAttributeDo : insertList) {
            //冗余食堂品类
            Integer canteenPrimaryId = wmScCanteenPoiAttributeDo.getCanteenPrimaryId();
            WmCanteenDB wmCanteenDB = wmCanteenMapper.selectCanteenByIdIgnoreValid(canteenPrimaryId);
            wmScCanteenPoiAttributeDo.setCanteenCategory(wmCanteenDB.getCategory());

            wmScCanteenPoiAttributeMapper.insertSelective(wmScCanteenPoiAttributeDo);
        }
    }

    /**
     * 更新食堂门店属性记录
     *
     * @param updateList 更新集合
     */
    public void doUpdate(List<WmScCanteenPoiAttributeDO> updateList) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_1", "com.sankuai.meituan.waimai.customer.service.sc.poi.WmScCanteenPoiAttributeService.doUpdate(java.util.List)");
        log.info("[WmScCanteenPoiAttributeService.doUpdate] input param: updateList = {}", JSONObject.toJSONString(updateList));
        if (CollectionUtils.isEmpty(updateList)) {
            return;
        }

        for (WmScCanteenPoiAttributeDO wmScCanteenPoiAttributeDo : updateList) {
            //冗余食堂品类
            Integer canteenPrimaryId = wmScCanteenPoiAttributeDo.getCanteenPrimaryId();
            WmCanteenDB wmCanteenDB = wmCanteenMapper.selectCanteenByIdIgnoreValid(canteenPrimaryId);
            wmScCanteenPoiAttributeDo.setCanteenCategory(wmCanteenDB.getCategory());

            wmScCanteenPoiAttributeMapper.updateByPrimaryKeySelective(wmScCanteenPoiAttributeDo);
        }
    }

    /**
     * 已经有效的记录设置删除状态
     *
     * @param wmScCanteenPoiAttributeDoExist wmScCanteenPoiAttributeDoExist
     */
    private boolean reUnValid(WmScCanteenPoiAttributeDO wmScCanteenPoiAttributeDoExist) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_1", "com.sankuai.meituan.waimai.customer.service.sc.poi.WmScCanteenPoiAttributeService.reUnValid(com.sankuai.meituan.waimai.customer.domain.sc.WmScCanteenPoiAttributeDO)");
        if (ValidEnum.VALID.getTypeInt() == wmScCanteenPoiAttributeDoExist.getValid()) {
            wmScCanteenPoiAttributeDoExist.setValid(ValidEnum.INVALID.getTypeInt());
            return true;
        }
        return false;
    }

    /**
     * 已经删除的记录重新启用
     *
     * @param wmScCanteenPoiAttributeDoExist wmScCanteenPoiAttributeDoExist
     */
    private boolean reValid(WmScCanteenPoiAttributeDO wmScCanteenPoiAttributeDoExist) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.service.sc.poi.WmScCanteenPoiAttributeService.reValid(com.sankuai.meituan.waimai.customer.domain.sc.WmScCanteenPoiAttributeDO)");
        if (ValidEnum.INVALID.getTypeInt() == wmScCanteenPoiAttributeDoExist.getValid()) {
            wmScCanteenPoiAttributeDoExist.setValid(ValidEnum.VALID.getTypeInt());
            return true;
        }
        return false;
    }

    /**
     * 创建新的门店属性对象
     *
     * @param wmPoiId      门店Id
     * @param canteen      食堂对象
     * @param wmSchoolDb   学校对象
     * @param wmCustomerDb 客户承包商对象
     * @param isInit       是否初始化
     * @return
     */
    private WmScCanteenPoiAttributeDO createNewDo(Long wmPoiId, WmCanteenDB canteen, WmSchoolDB wmSchoolDb, WmCustomerDB wmCustomerDb, boolean isInit) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.service.sc.poi.WmScCanteenPoiAttributeService.createNewDo(Long,WmCanteenDB,WmSchoolDB,WmCustomerDB,boolean)");
        WmScCanteenPoiAttributeDO wmScCanteenPoiAttributeDoNew = new WmScCanteenPoiAttributeDO();
        wmScCanteenPoiAttributeDoNew.setValid(ValidEnum.VALID.getTypeInt());
        wmScCanteenPoiAttributeDoNew.setCanteenPrimaryId(canteen.getId());
        wmScCanteenPoiAttributeDoNew.setCanteenId(canteen.getCanteenId());
        wmScCanteenPoiAttributeDoNew.setCustomerPrimaryId(wmCustomerDb == null ? 0 : wmCustomerDb.getId());
        wmScCanteenPoiAttributeDoNew.setSchoolPrimaryId(canteen.getSchoolId());
        wmScCanteenPoiAttributeDoNew.setSchoolId(wmSchoolDb == null ? 0 : wmSchoolDb.getSchoolId());
        wmScCanteenPoiAttributeDoNew.setMtCustomerId(wmCustomerDb == null ? 0 : wmCustomerDb.getMtCustomerId());
        wmScCanteenPoiAttributeDoNew.setWmPoiId(wmPoiId);
        if (isInit) {
            List<WmScCanteenPoiAuditDetailDB> list = wmScCanteenPoiAuditDetailDao.getByWmPoiId(wmPoiId);
            if (CollectionUtils.isNotEmpty(list)) {
                wmScCanteenPoiAttributeDoNew.setCtime(list.get(0).getCtime());
                wmScCanteenPoiAttributeDoNew.setUtime(list.get(0).getUtime());
            }
        }
        return wmScCanteenPoiAttributeDoNew;
    }

    /**
     * 从集合中获取已经存在的记录，如果存在则返回，不存在则返回null
     *
     * @param wmScCanteenPoiAttributeDoList 要获取的集合范围
     * @param wmPoiId                       门店ID
     * @return
     */
    private WmScCanteenPoiAttributeDO getExistPoiAttribute(List<WmScCanteenPoiAttributeDO> wmScCanteenPoiAttributeDoList, Long wmPoiId) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.service.sc.poi.WmScCanteenPoiAttributeService.getExistPoiAttribute(java.util.List,java.lang.Long)");
        if (CollectionUtils.isEmpty(wmScCanteenPoiAttributeDoList) || wmPoiId == null) {
            return null;
        }
        for (WmScCanteenPoiAttributeDO wmScCanteenPoiAttributeDO : wmScCanteenPoiAttributeDoList) {
            if (wmPoiId.equals(wmScCanteenPoiAttributeDO.getWmPoiId())) {
                return wmScCanteenPoiAttributeDO;
            }
        }
        return null;
    }

    /**
     * 删除食堂门店属性（置为无效）
     *
     * @param wmScCanteenPoiAttributeDoList 食堂门店属性列表
     */
    private List<Long> doDelete(List<WmScCanteenPoiAttributeDO> wmScCanteenPoiAttributeDoList) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_1", "com.sankuai.meituan.waimai.customer.service.sc.poi.WmScCanteenPoiAttributeService.doDelete(java.util.List)");
        List<Long> deleteWmPoiId = new ArrayList<>();
        if (CollectionUtils.isEmpty(wmScCanteenPoiAttributeDoList)) {
            return deleteWmPoiId;
        }
        for (WmScCanteenPoiAttributeDO wmScCanteenPoiAttributeDo : wmScCanteenPoiAttributeDoList) {
            if (ValidEnum.INVALID.getTypeInt() == wmScCanteenPoiAttributeDo.getValid()) {
                continue;
            }
            WmScCanteenPoiAttributeDO wmScCanteenPoiAttributeDUpdate = new WmScCanteenPoiAttributeDO();
            wmScCanteenPoiAttributeDUpdate.setId(wmScCanteenPoiAttributeDo.getId());
            wmScCanteenPoiAttributeDUpdate.setValid(ValidEnum.INVALID.getTypeInt());
            deleteWmPoiId.add(wmScCanteenPoiAttributeDo.getWmPoiId());
            wmScCanteenPoiAttributeMapper.updateByPrimaryKeySelective(wmScCanteenPoiAttributeDUpdate);
        }
        return deleteWmPoiId;
    }

    /**
     * 删除食堂门店属性(物理删除)
     *
     * @param wmScCanteenPoiAttributeDoList 食堂门店属性列表
     */
    private void doRealDelete(List<WmScCanteenPoiAttributeDO> wmScCanteenPoiAttributeDoList) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.service.sc.poi.WmScCanteenPoiAttributeService.doRealDelete(java.util.List)");
        log.info("[WmScCanteenPoiAttributeService.doRealDelete] wmScCanteenPoiAttributeDoList = {}", JSONObject.toJSONString(wmScCanteenPoiAttributeDoList));
        if (CollectionUtils.isEmpty(wmScCanteenPoiAttributeDoList)) {
            return;
        }

        HostEnv env = ProcessInfoUtil.getHostEnv();
        for (WmScCanteenPoiAttributeDO wmScCanteenPoiAttributeDo : wmScCanteenPoiAttributeDoList) {
            DaxiangUtilV2.push(String.format("[%s]食堂门店错误关系物理删除:wmPoiId:%s,canteenPrimaryId:%s,id:%s",
                            env.name(), wmScCanteenPoiAttributeDo.getWmPoiId(), wmScCanteenPoiAttributeDo.getCanteenPrimaryId(), wmScCanteenPoiAttributeDo.getId()),
                    MccCustomerConfig.getAuditAlarmList());
            wmScCanteenPoiAttributeMapper.deleteByPrimaryKey(wmScCanteenPoiAttributeDo.getId());
        }
    }

    /**
     * 根据食堂物理主键ID查询关联的门店信息（包括已经删除的）
     *
     * @param canteenPrimaryId 食堂主键ID
     * @return List<WmScCanteenPoiAttributeDO>
     */
    public List<WmScCanteenPoiAttributeDO> selectByCanteenPrimaryIdByMaster(Integer canteenPrimaryId) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.service.sc.poi.WmScCanteenPoiAttributeService.selectByCanteenPrimaryIdByMaster(java.lang.Integer)");
        log.info("[WmScCanteenPoiAttributeService.selectByCanteenPrimaryIdByMaster] canteenPrimaryId = {}", canteenPrimaryId);
        if (canteenPrimaryId == null || canteenPrimaryId <= 0) {
            return Lists.newArrayList();
        }

        return wmScCanteenPoiAttributeMapper.selectByCanteenPrimaryIdByMaster(canteenPrimaryId);
    }

    /**
     * 根据食堂物理主键ID查询关联的门店信息
     *
     * @param canteenPrimaryId
     * @return
     */
    public List<WmScCanteenPoiAttributeDO> selectEffectByCanteenPrimaryId(Integer canteenPrimaryId) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.service.sc.poi.WmScCanteenPoiAttributeService.selectEffectByCanteenPrimaryId(java.lang.Integer)");
        log.info("selectByCanteenPrimaryId canteenPrimaryId={}", canteenPrimaryId);
        if (canteenPrimaryId == null || canteenPrimaryId <= 0) {
            return Lists.newArrayList();
        }
        WmScCanteenPoiAttributeSearchCondition condition = new WmScCanteenPoiAttributeSearchCondition();
        condition.setCanteenPrimaryId(canteenPrimaryId);
        condition.setValid(ValidEnum.VALID.getTypeInt());
        return wmScCanteenPoiAttributeMapper.selectByCondition(condition);
    }

    /**
     * 刷新食堂门店属性，用于兜底策略
     *
     * @param canteenPrimaryId 食堂ID
     */
    public WmScCanteenPoiAttributeRefreshResultBO refreshForCoverBottom(Integer canteenPrimaryId) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.service.sc.poi.WmScCanteenPoiAttributeService.refreshForCoverBottom(java.lang.Integer)");
        log.info("doRefreshForCoverBottom canteenPrimaryId={}", canteenPrimaryId);
        if (canteenPrimaryId == null || canteenPrimaryId <= 0) {
            return null;
        }
        WmScCanteenPoiAttributeRefreshResultBO result = new WmScCanteenPoiAttributeRefreshResultBO();
        result.setCanteenPrimaryId(canteenPrimaryId);
        WmCanteenDB wmCanteenDb = wmScCanteenService.getById(canteenPrimaryId, false);
        if (wmCanteenDb == null || wmCanteenDb.getValid() == ValidEnum.INVALID.getTypeInt()) {
            return result;
        }

        // 查询食堂绑定的门店
        List<WmCanteenStallBindDO> bindDOList = wmCanteenStallBindService.getStallBindListByCanteenPrimaryIdWmPoiBindingStatus(canteenPrimaryId);
        List<Long> finalPoiList = bindDOList.stream()
                .map(WmCanteenStallBindDO::getWmPoiId)
                .filter(wmPoiId -> wmPoiId != null && wmPoiId > 0)
                .collect(Collectors.toList());

        return refreshCanteenPoiAttribute(wmCanteenDb, finalPoiList, true);
    }

    /**
     * 根据门店ID查询门店食堂属性记录
     */
    public List<WmScCanteenPoiAttributeDO> selectEffectByWmPoiId(Long wmPoiId) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.service.sc.poi.WmScCanteenPoiAttributeService.selectEffectByWmPoiId(java.lang.Long)");
        if (wmPoiId == null || wmPoiId <= 0) {
            return Lists.newArrayList();
        }
        WmScCanteenPoiAttributeSearchCondition condition = new WmScCanteenPoiAttributeSearchCondition();
        condition.setWmPoiId(wmPoiId);
        condition.setValid(ValidEnum.VALID.getTypeInt());
        return wmScCanteenPoiAttributeMapper.selectByCondition(condition);
    }

    /**
     * 根据食堂主键ID查询关联的门店ID列表(查主库)
     *
     * @param canteenPrimaryId 食堂主键ID
     * @return 门店ID列表
     */
    public List<Long> getWmPoiIdListByCanteenPrimaryIdByMaster(Integer canteenPrimaryId) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.service.sc.poi.WmScCanteenPoiAttributeService.getWmPoiIdListByCanteenPrimaryIdByMaster(java.lang.Integer)");
        log.info("[WmScCanteenPoiAttributeService.getWmPoiIdListByCanteenPrimaryIdByMaster] input param: canteenPrimaryId = {}", canteenPrimaryId);
        if (canteenPrimaryId == null || canteenPrimaryId <= 0) {
            return new ArrayList<>();
        }

        List<Long> wmPoiIdList = wmScCanteenPoiAttributeMapper.selectWmPoiIdListByCanteenPrimaryIdByMaster(canteenPrimaryId);
        log.info("[WmScCanteenPoiAttributeService.getWmPoiIdListByCanteenPrimaryIdByMaster] wmPoiIdList = {}", JSONObject.toJSONString(wmPoiIdList));
        return wmPoiIdList;
    }

    /**
     * 根据食堂主键ID查询非子门店的门店ID列表
     *
     * @param canteenPrimaryId 食堂主键ID
     * @return 门店ID列表
     * @throws WmSchCantException com.sankuai.meituan.waimai.thrift.exception.WmSchCantException
     */
    public List<Long> getNonSubWmPoiIdListByCanteenPrimaryId(Integer canteenPrimaryId) throws WmSchCantException {
        log.info("[WmScCanteenPoiAttributeService.getNonSubWmPoiIdListByCanteenPrimaryId] input param: canteenPrimaryId = {}", canteenPrimaryId);
        if (canteenPrimaryId == null || canteenPrimaryId <= 0) {
            return new ArrayList<>();
        }

        List<Long> wmPoiIdList = wmScCanteenPoiAttributeMapper.selectWmPoiIdListByCanteenPrimaryId(canteenPrimaryId);
        if (CollectionUtils.isEmpty(wmPoiIdList)) {
            return new ArrayList<>();
        }

        return getNonSubWmPoiIdListByWmPoiIdList(wmPoiIdList);
    }


    public List<Long> getNonSubWmPoiIdListBySchoolPrimaryId(Integer schoolPrimaryId) throws WmSchCantException {
        log.info("[WmScCanteenPoiAttributeService.getNonSubWmPoiIdListBySchoolPrimaryId] input param: schoolPrimaryId = {}", schoolPrimaryId);
        if (schoolPrimaryId == null || schoolPrimaryId <= 0) {
            return new ArrayList<>();
        }

        List<Long> wmPoiIdList = wmScCanteenPoiAttributeMapper.selectWmPoiIdListBySchoolPrimaryId(schoolPrimaryId);
        if (CollectionUtils.isEmpty(wmPoiIdList)) {
            return new ArrayList<>();
        }

        return getNonSubWmPoiIdListByWmPoiIdList(wmPoiIdList);
    }


    private List<Long> getNonSubWmPoiIdListByWmPoiIdList(List<Long> wmPoiIdList) throws WmSchCantException {
        try {
            if (CollectionUtils.isEmpty(wmPoiIdList)) {
                return new ArrayList<>();
            }

            List<WmPoiAggre> wmPoiAggreList = wmPoiQueryAdapter.getWmPoiAggreList(wmPoiIdList, Sets.newHashSet(
                    WmPoiFieldQueryConstant.WM_POI_FIELD_WM_POI_ID,
                    WmPoiFieldQueryConstant.WM_POI_FIELD_NAME,
                    WmPoiFieldQueryConstant.WM_POI_FIELD_SUB_WM_POI_TYPE)
            );

            List<Long> wmPoiIdListResult = wmPoiAggreList.stream()
                    .filter(wmPoiAggre -> wmPoiAggre.getSub_wm_poi_type() <= 0)
                    .map(WmPoiAggre::getWm_poi_id)
                    .collect(Collectors.toList());

            log.info("[WmScCanteenPoiAttributeService.getNonSubWmPoiIdListByWmPoiIdList] wmPoiIdListResult = {}", JSONObject.toJSONString(wmPoiIdListResult));
            return wmPoiIdListResult;
        } catch (WmCustomerException e) {
            log.error("[WmScCanteenPoiAttributeService.getNonSubWmPoiIdListByWmPoiIdList] WmCustomerException. wmPoiIdList = {}",
                    JSONObject.toJSONString(wmPoiIdList), e);
            throw new WmSchCantException(WmScCodeConstants.SERVER_ERROR, e.getMsg());
        }
    }


}
