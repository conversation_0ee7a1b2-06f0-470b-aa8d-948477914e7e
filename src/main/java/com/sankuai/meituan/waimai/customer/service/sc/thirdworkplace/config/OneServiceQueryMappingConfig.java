package com.sankuai.meituan.waimai.customer.service.sc.thirdworkplace.config;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

@AllArgsConstructor
@NoArgsConstructor
@Data
public class OneServiceQueryMappingConfig {
    /**
     * 三方平台列表查询指标列表
     */
    private List<String> itemIndicatorList;

    /**
     * 三方平台列表查询结果映射配置
     * key-oneService返回指标字段
     * value-bo对象字段名称
     */
    private Map<String, String> itemMappingConfig;

    /**
     * 三方平台列表查询环比配置
     * key-查询维度 日、周、月->dt、wk、mo
     * value-环比配置
     */
    private Map<String, OneServicePopParamWrapper> itemMoMDataConfig;

    /**
     * 三方平台列表查询维度列表
     */
    private List<String> itemQueryDimensionList;

    /**
     * 三方平台指标查询指标列表
     */
    private List<String> metricIndicatorList;

    /**
     * 三方平台指标查询结果映射配置
     * key-oneService返回指标字段
     * value-bo对象字段名称
     */
    private Map<String, String> metricMappingConfig;

    /**
     * 三方平台指标查询环比配置
     * key-查询维度 日、周、月->dt、wk、mo
     * value-环比配置
     */
    private Map<String, OneServicePopParamWrapper> metricMoMDataConfig;

    /**
     * 三方平台OneService数据集配置
     * key-查询维度 日、周、月->dt、wk、mo
     * value-起源数据集ID
     */
    private Map<String, String> originDsMappingConfig;

}
