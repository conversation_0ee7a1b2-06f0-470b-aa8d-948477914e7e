package com.sankuai.meituan.waimai.customer.service.customer;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.dianping.cat.Cat;
import com.dianping.cat.util.MetricHelper;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.meituan.mtrace.thread.TraceRunnable;
import com.meituan.mtrace.thread.pool.TraceExecutors;
import com.sankuai.meituan.waimai.customer.adapter.*;
import com.sankuai.meituan.waimai.customer.bo.WmCustomerRealTypeConfigBo;
import com.google.common.collect.Sets;
import com.sankuai.meituan.waimai.customer.bo.WmCustomerRealTypeResult;
import com.sankuai.meituan.waimai.customer.config.MccConfig;
import com.sankuai.meituan.waimai.customer.config.MccCustomerConfig;
import com.sankuai.meituan.waimai.customer.constant.WmOrgConstant;
import com.sankuai.meituan.waimai.customer.constant.customer.CustomerMetricEnum;
import com.sankuai.meituan.waimai.customer.ddd.adapter.WmPoiQueryAdapter;
import com.sankuai.meituan.waimai.customer.ddd.domain.customer.aggre.WmCustomerPoiAggre;
import com.sankuai.meituan.waimai.customer.domain.CustomerRealTypeConfigBo;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerDB;
import com.sankuai.meituan.waimai.customer.service.customer.check.WmCustomerAuthService;
import com.sankuai.meituan.waimai.customer.service.gray.WmCustomerGrayService;
import com.sankuai.meituan.waimai.infra.constants.WmVirtualOrgBizTypeEnum;
import com.sankuai.meituan.waimai.infra.constants.WmVirtualOrgTeamTypeEnum;
import com.sankuai.meituan.waimai.infra.domain.WmEmploy;
import com.sankuai.meituan.waimai.infra.service.WmVirtualOrgService;
import com.sankuai.meituan.waimai.label.constants.LabelSubjectTypeEnum;
import com.sankuai.meituan.waimai.poibaseinfo.thrift.domain.WmPhysicalPoiRel;
import com.sankuai.meituan.waimai.qualification.WmQualificationThriftService;
import com.sankuai.meituan.waimai.thrift.constant.QuaTypeEnum;
import com.sankuai.meituan.waimai.thrift.constants.WmPoiFieldQueryConstant;
import com.sankuai.meituan.waimai.thrift.customer.constant.*;
import com.sankuai.meituan.waimai.thrift.customer.constant.customer.CustomerBizOrgEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.customer.SingleCustomerSceneEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.*;
import com.sankuai.meituan.waimai.thrift.domain.WmPoiAggre;
import com.sankuai.meituan.waimai.thrift.domain.WmPoiQualificationInfoBo;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import com.sankuai.meituan.waimai.thrift.exception.WmServerException;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.thrift.TException;
import org.jeasy.rules.annotation.Fact;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.sankuai.meituan.waimai.customer.constant.WmCustomerConstant;

import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;

/**
 * 客户类型的处理类
 */
@Service
public class WmCustomerRealTypeService {

    private static Logger logger = LoggerFactory.getLogger(WmCustomerRealTypeService.class);

    @Autowired
    private WmVirtualOrgService.Iface wmVirtualOrgService;
    @Autowired
    private WmCustomerService wmCustomerService;

    @Autowired
    private WmQualificationThriftService.Iface wmQuaThriftService;

    @Autowired
    private WmPoiQueryAdapter wmPoiQueryAdapter;

    @Autowired
    private WmOrgClient wmOrgClient;

    @Autowired
    private WmPoiClient wmPoiClient;

    @Autowired
    private BaseInfoQueryPhysicalPoiThriftServiceAdapter baseInfoQueryPhysicalPoiThriftServiceAdapter;

    @Autowired
    private WmCustomerGrayService wmCustomerGrayService;

    @Autowired
    private UpmAuthCheckService upmAuthCheckService;

    private static final int MED_CATEGORY_ID = 22;

    private static final List<Integer> DRUG_CUSTOMER_REAL_TYPE = Lists.newArrayList(CustomerRealTypeEnum.DANDIAN_YAOPIN.getValue(),
            CustomerRealTypeEnum.ZONGBU_YAOPIN_LIANSUO.getValue(), CustomerRealTypeEnum.B2C_DRUG.getValue());

    private static Splitter SPLITTER = Splitter.on(",").omitEmptyStrings().trimResults();

    /**
     * 客户场景信息不能修改的状态
     */
    private static List<Integer> sceneInfoNotUpdateStatus = Lists.newArrayList(CustomerAuditStatus.CUSTOMER_AUDIT_NEW.getCode(),
            CustomerAuditStatus.CUSTOMER_AUDIT_AUDITING.getCode(),
            CustomerAuditStatus.CUSTOMER_AUDIT_PASS.getCode(),
            CustomerAuditStatus.CUSTOMER_AUDIT_REJECT.getCode());


    @Autowired
    private WmCustomerAuthService wmCustomerAuthService;

    private static ExecutorService executorService = TraceExecutors.getTraceExecutorService(
            new ThreadPoolExecutor(100, 200, 30L, TimeUnit.MINUTES, new ArrayBlockingQueue<Runnable>(20000), new RejectedExecutionHandler() {
                @Override
                public void rejectedExecution(Runnable r, ThreadPoolExecutor executor) {
                    if (!executorService.isShutdown()) {
                        try {
                            executor.getQueue().put(r);
                        } catch (InterruptedException e) {
                            throw new RejectedExecutionException("Reject from " + executor.toString());
                        }
                    } else {
                        throw new RejectedExecutionException("Executor " + executor.toString() + " have shutdown.");
                    }
                }
            }));

    /**
     * 查询该用户可以看到的客户类型
     *
     * @param employ
     * @return
     * @throws WmCustomerException
     * @throws TException
     * @throws WmServerException
     */
    public WmCustomerRealTypeResult getCustomerRealType(WmEmploy employ, int authType) throws TException, WmServerException {
        logger.info("getCustomerRealType, employ = {}", employ.toString());

        Set<Integer> value = Sets.newTreeSet();
        List<Integer> bizTypes = Lists.newArrayList();
        // 总部权限/总部白名单权限映射客户类型
        List<Integer> hqCustomerRealType = wmCustomerAuthService.getAuthCustomerRealType(employ.getUid(), authType);
        if (!CollectionUtils.isEmpty(hqCustomerRealType)) {
            value.addAll(hqCustomerRealType);
        }

        List<Integer> userBizTypes = wmVirtualOrgService.getUserBizTypes(employ.getUid());
        logger.info("getCustomerRealType, userBizTypes = {}", JSON.toJSONString(userBizTypes));
        bizTypes.addAll(userBizTypes);
        if (CollectionUtils.isEmpty(bizTypes) && CollectionUtils.isEmpty(value)) {
            return null;
        }
        //根据组织节点加载权限
        if (!MccCustomerConfig.useConfigGetSpecialCustomerRealType() &&
                CollectionUtils.isNotEmpty(userBizTypes) && userBizTypes.contains(WmVirtualOrgBizTypeEnum.TUAN_CAN.getBizType())) {
            //功能开启 && 团餐业务类型下, 团餐BD能单独获取能看到的外卖单店客户类型
            if (MccCustomerConfig.enableTuancanbdAuth() && wmOrgClient.isUserInTuanCanSaleTeam(employ.getUid())) {
                value.add(CustomerRealTypeEnum.DANDIAN.getValue());
            }
        }

        for (Integer userBizType : bizTypes) {
            WmCustomerRealTypeConfigBo bo = getCustomerRealTypeByBizType(userBizType, employ.getUid());
            if (bo != null) {
                value.addAll(bo.getValue());
            } else if (MccCustomerConfig.useConfigGetSpecialCustomerRealType()) {
                List<Integer> customerRealTypes = getCustomerRealTypeByBizTypeAndOrgId(userBizType, employ);
                if (CollectionUtils.isNotEmpty(customerRealTypes)) {
                    value.addAll(customerRealTypes);
                }
            }
        }

        if (CollectionUtils.isEmpty(value)) {
            return null;
        }

        List<WmCustomerSimpleDTO> realTypeList = value.stream().map(x -> {
            WmCustomerSimpleDTO wmCustomerSimpleDTO = new WmCustomerSimpleDTO();
            wmCustomerSimpleDTO.setValue(x);
            wmCustomerSimpleDTO.setName(CustomerRealTypeEnum.getNameByValue(x));
            wmCustomerSimpleDTO.setBizOrgCode(0);
            wmCustomerSimpleDTO.setBizOrgCodeDesc("未知");
            Integer bizOrgCode = CustomerRealTypeEnum.getBizOrgCodeByValue(x);
            // 加工客户类型所属业务线
            if (bizOrgCode != null) {
                CustomerBizOrgEnum bizOrgEnum = CustomerBizOrgEnum.of(bizOrgCode);
                if (bizOrgEnum != null) {
                    wmCustomerSimpleDTO.setBizOrgCode(bizOrgEnum.getCode());
                    wmCustomerSimpleDTO.setBizOrgCodeDesc(bizOrgEnum.getDesc());
                }
            }
            return wmCustomerSimpleDTO;
        }).collect(Collectors.toList());
        WmCustomerRealTypeResult result = new WmCustomerRealTypeResult();
        result.setCustomerRealTypeList(realTypeList);
        result.setDefaultVal(CustomerRealTypeEnum.QINGXUANZE.getValue());
        return result;
    }

    /**
     * 根据业务类型、组织节点获取客户类型
     * @param bizType
     * @param employ
     * @return
     */
    private List<Integer> getCustomerRealTypeByBizTypeAndOrgId(int bizType, WmEmploy employ) {
        List<Integer> customerRealTypes = Lists.newArrayList();
        try {
            Map<String, Object> config = MccCustomerConfig.bizTypeAndOrgIdMapperCustomerRealType();
            Object configObject = config.get(String.valueOf(bizType));
            if (configObject == null) {
                return customerRealTypes;
            }
            List<CustomerRealTypeConfigBo> boList = JSONObject.parseArray(configObject.toString(), CustomerRealTypeConfigBo.class);
            if (CollectionUtils.isEmpty(boList)) {
                return customerRealTypes;
            }
            for (CustomerRealTypeConfigBo bo : boList) {
                if (bo == null || CollectionUtils.isEmpty(bo.getOrgIds()) ||
                        bo.getSourceType() == null || CollectionUtils.isEmpty(bo.getCustomerRealTypes())) {
                    continue;
                }

                List<Integer> currenOrgIds = wmOrgClient.getSuperiorBySource(employ.getUid(), bo.getSourceType());
                if (CollectionUtils.isEmpty(currenOrgIds)) {
                    continue;
                }

                if (!Collections.disjoint(currenOrgIds, bo.getOrgIds()) &&
                        (CollectionUtils.isEmpty(bo.getGrayMisIds()) || bo.getGrayMisIds().contains(employ.getMisId()))) {
                    customerRealTypes.addAll(bo.getCustomerRealTypes());
                }
            }

        } catch (Exception e) {
            logger.error("getCustomerRealTypeByBizTypeAndOrgId error bizType={},employ={}", bizType, JSONObject.toJSONString(employ), e);
        }
        return customerRealTypes;
    }


    private WmCustomerRealTypeConfigBo getCustomerRealTypeByBizType(int bizType, int uid) throws TException, WmServerException {
        // 获取不同业务线能看到的客户类型
        Map<Integer, WmCustomerRealTypeConfigBo> customerRealTypeMap = getCustomerRealTypeConfiguration(WmOrgConstant.OrgType.BIZ_TYPE);
        WmCustomerRealTypeConfigBo configBo = customerRealTypeMap.get(bizType);
        if (configBo == null) {
            return null;
        }

        // 外卖业务类型下, "校园"团队类型单独获取能看到的客户类型
        if (WmVirtualOrgBizTypeEnum.WAIMAI.getBizType() == bizType && (wmOrgClient.isUserInWmOrgTeam(uid, MccCustomerConfig.getSchoolTeamOrgTypes()))) {
            customerRealTypeMap = getCustomerRealTypeConfiguration(WmOrgConstant.OrgType.TEAM_TYPE);
            if (customerRealTypeMap != null) {
                WmCustomerRealTypeConfigBo schoolRealTypeConfigBo = customerRealTypeMap.get(WmVirtualOrgTeamTypeEnum.XIAO_YUAN.getCode());
                if (schoolRealTypeConfigBo != null && !CollectionUtils.isEmpty(schoolRealTypeConfigBo.getValue())) {
                    configBo.getValue().addAll(schoolRealTypeConfigBo.getValue());
                }
            }
        }
        return configBo;
    }

    private Map<Integer, WmCustomerRealTypeConfigBo> getCustomerRealTypeConfiguration(WmOrgConstant.OrgType orgType) {
        String customerRealType;
        if (WmOrgConstant.OrgType.BIZ_TYPE == orgType) {
            customerRealType = MccConfig.getCustomerRealTypeForBizType();
        } else {
            customerRealType = MccConfig.getCustomerRealTypeForTeamType();
        }

        logger.info("customerRealType = {}", customerRealType);
        try {
            List<WmCustomerRealTypeConfigBo> cusRealTypeConfigBoList = JSONArray.parseArray(customerRealType, WmCustomerRealTypeConfigBo.class);
            return Maps.uniqueIndex(cusRealTypeConfigBoList, configBo -> configBo.getType());
        } catch (Exception e) {
            logger.error("每种组织架构用户能够查看的客户类型解析出错, 请尽快处理, customerRealType = {}", customerRealType, e);
            return Maps.newHashMap();
        }
    }

    //校验客户类型是否可以切换
    private Boolean checkCanSwitchCustomerType(WmCustomerBasicBo wmCustomerBasicBo) throws WmCustomerException {
        List<Integer> canSwitchCustomerTypeList = SPLITTER.splitToList(MccConfig.getCanSwitchCustomerTypeList()).stream().map(Integer::parseInt).collect(Collectors.toList());
        List<Integer> canSwitchCustomerTypeYaoPinList = SPLITTER.splitToList(MccConfig.getCanSwitchCustomerTypeYaoPinList()).stream().map(Integer::parseInt).collect(Collectors.toList());
        List<Integer> cantSwitchList = SPLITTER.splitToList(MccConfig.getCanNotSwitchCustomerTypeList()).stream().map(Integer::parseInt).collect(Collectors.toList());

        WmCustomerDB wmCustomerDB = wmCustomerService.selectCustomerById(wmCustomerBasicBo.getId());
        if (wmCustomerDB == null) {
            return false;
        }

        // 先判断不可切换的客户类型
        if (cantSwitchList.contains(wmCustomerBasicBo.getCustomerRealType()) || cantSwitchList.contains(wmCustomerDB.getCustomerRealType())) {
            return wmCustomerBasicBo.getCustomerRealType() == wmCustomerDB.getCustomerRealType();
        }

        if (!MccCustomerConfig.getCheckRuleSwitch()) {
            // 拥有"客户类型医药类与非医药类切换管理员"角色可切换医药类和非医药类客户类型
            List<Integer> canSwitchListForUserRole = ListUtils.union(canSwitchCustomerTypeList, canSwitchCustomerTypeYaoPinList);
            if (!CollectionUtils.isEmpty(wmCustomerBasicBo.getUserRoleList()) && wmCustomerBasicBo.getUserRoleList().contains(UserRoleEnum.MEDICINE_AND_NON_MEDICAL_SWITCH_MANAGER)) {
                return canSwitchListForUserRole.contains(wmCustomerBasicBo.getCustomerRealType()) && canSwitchListForUserRole.contains(wmCustomerDB.getCustomerRealType());
            }
            if (canSwitchCustomerTypeList.contains(wmCustomerBasicBo.getCustomerRealType())) {
                // 非医药类客户类型判断
                return canSwitchCustomerTypeList.contains(wmCustomerBasicBo.getCustomerRealType()) && canSwitchCustomerTypeList.contains(wmCustomerDB.getCustomerRealType());
            } else if (canSwitchCustomerTypeYaoPinList.contains(wmCustomerBasicBo.getCustomerRealType())) {
                // 医药类客户类型判断
                return canSwitchCustomerTypeYaoPinList.contains(wmCustomerBasicBo.getCustomerRealType()) && canSwitchCustomerTypeYaoPinList.contains(wmCustomerDB.getCustomerRealType());
            }
        }
        return true;
    }

    /**
     * 验证客户资质与门店资质是否一致
     *
     * @param wmCustomerBasicBo
     * @param wmPoiIdList
     * @return
     * @throws TException
     * @throws WmServerException
     */
    private ValidateResultBo validateQua(WmCustomerBasicBo wmCustomerBasicBo, List<Long> wmPoiIdList) {
        ValidateResultBo validateResultBo = new ValidateResultBo();
        validateResultBo.setCode(CustomerConstants.RESULT_CODE_PASS);
        if (CollectionUtils.isEmpty(wmPoiIdList)) {
            return validateResultBo;
        }
        String customerNumber = wmCustomerBasicBo.getCustomerNumber();
        //资质类型
        int quaType = wmCustomerBasicBo.getCustomerType();
        int quaSecendType = wmCustomerBasicBo.getCustomerSecondType();
        logger.info("资质编号重复校验：customerNumber={},quaType={},quaSecendType={}", customerNumber, quaType, quaSecendType);

        List<List<Long>> allPoiIds = Lists.partition(wmPoiIdList, MccConfig.checkQuaPageSize());


        Set<Long> failWmPoiIds = Sets.newHashSet();

        CountDownLatch countDownLatch = new CountDownLatch(allPoiIds.size());


        for (final List<Long> wmPoiIs : allPoiIds) {
//            rateLimiter.acquire();

            try {
                executorService.execute(new TraceRunnable(new Runnable() {

                    @Override
                    public void run() {
                        try {
                            Map<Long, List<WmPoiQualificationInfoBo>> resultMap = wmQuaThriftService.getWmPoiQualificationInfosByWmPoiIds(wmPoiIs);
//                            logger.info("调用门店资质接口获取门店资质:resultMap={}", JSON.toJSONString(resultMap));

                            for (Map.Entry<Long, List<WmPoiQualificationInfoBo>> map : resultMap.entrySet()) {
                                Long wmPoiId = map.getKey();
                                for (WmPoiQualificationInfoBo wmPoiAuditObjectBo : map.getValue()) {
                                    if (wmPoiAuditObjectBo.getType() == QuaTypeEnum.QUA_SUBTYPE_CID.getSubType() && wmPoiAuditObjectBo.getSecondSubType() == QuaTypeEnum.QUA_SUBTYPE_CID.getSecondSubType()
                                            &&
                                            (quaType == CustomerType.CUSTOMER_TYPE_IDCARD.getCode() && quaSecendType == CertTypeEnum.ID_CARD.getType())) {
                                        //个人身份证
                                        if (!wmPoiAuditObjectBo.getNumber().equals(customerNumber)) {
                                            failWmPoiIds.add(wmPoiId);
                                        }
                                    } else if (wmPoiAuditObjectBo.getType() == QuaTypeEnum.QUA_SUBTYPE_BUSINESSLICENSE.getSubType() && wmPoiAuditObjectBo.getSecondSubType() == QuaTypeEnum.QUA_SUBTYPE_BUSINESSLICENSE.getSecondSubType()
                                            && quaType == CustomerType.CUSTOMER_TYPE_BUSINESS.getCode()) {
                                        //营业执照
                                        if (!wmPoiAuditObjectBo.getNumber().equals(customerNumber)) {
                                            failWmPoiIds.add(wmPoiId);
                                        }
                                    }
                                }
                            }

                        } catch (WmServerException e) {
                            logger.warn("wmQuaThriftService.getUpQua wmPoiIs:{} ", JSONObject.toJSONString(wmPoiIs), e);
                        } catch (TException e) {
                            logger.warn("wmQuaThriftService.getUpQua wmPoiIs:{} ", JSONObject.toJSONString(wmPoiIs), e);
                        } finally {
                            countDownLatch.countDown();
                        }

                    }
                }));

            } catch (Exception e) {
                logger.error("validateQua 多线程执行异常，wmPoiIdList={} ", JSONObject.toJSONString(wmPoiIdList), e);
            }


        }// end for

        try {
            countDownLatch.await(5, TimeUnit.SECONDS);
        } catch (InterruptedException e) {
            logger.error("validateQua 多线程执行超时，wmPoiIdList={} ", JSONObject.toJSONString(wmPoiIdList), e);
        } finally {
            logger.info("validateQua #end");
        }


        if (!CollectionUtils.isEmpty(failWmPoiIds)) {
            validateResultBo.setCode(CustomerConstants.RESULT_CODE_CUSTOMER_POI_REAL_TYPE_DIFF_ERROR);
            StringBuffer failStr = new StringBuffer();
            int num = 0;
            for (Long wmPoiId : failWmPoiIds) {
                if (num > 0) {
                    failStr.append("、");
                }
                failStr.append(wmPoiId);
                num++;
            }
            validateResultBo.setMsg(String.format("客户资质与门店资质不一致,无法保存。不一致门店:%s。", failStr.toString()));
        }
        return validateResultBo;    //校验通过
    }

    /**
     * 校验门店绑定合规性与资质一致性
     * @param wmCustomerBasicBo
     * @param wmPoiIds
     * @return
     * @throws TException
     * @throws WmCustomerException
     */
    private ValidateResultBo validatePoiBindAndQua(WmCustomerBasicBo wmCustomerBasicBo, List<Long> wmPoiIds) throws TException, WmCustomerException {
        ValidateResultBo validateResultBo = new ValidateResultBo();
        validateResultBo.setCode(CustomerConstants.RESULT_CODE_PASS);
        //获取经营门店和物理门店的绑定关系
        List<WmPhysicalPoiRel> physicalPoiRelList = baseInfoQueryPhysicalPoiThriftServiceAdapter.getPhysicalPoiRelList(wmPoiIds);
        //走主子门店模型校验条件：1，经营门店均绑定相应物理门店 2，命中新主子门店开关
        if (MccConfig.openNewModeDandianBindCheck() && exitPhysicalPoi(wmPoiIds, physicalPoiRelList)) {
            if (wmCustomerBasicBo.getCustomerRealType() == CustomerRealTypeEnum.DANDIAN.getValue() ||
                    wmCustomerBasicBo.getCustomerRealType() == CustomerRealTypeEnum.DANDIAN_YAOPIN.getValue()
                    || wmCustomerBasicBo.getCustomerRealType() == CustomerRealTypeEnum.DANDIAN_SG.getValue()
            ) {
                //1、单店、单店药品、闪购单店只允许绑定一个物理门店
                if (getPhysicalPoiCount(physicalPoiRelList) > 1) {
                    validateResultBo.setCode(CustomerConstants.RESULT_CODE_CUSTOMER_REAL_TYPE_ERROR);
                    validateResultBo.setMsg("单店客户下只可绑定相同物理门店的经营门店");
                    return validateResultBo;
                }
                //待校验的门店集合
                List<Long> validPoiIds = getValidPoiIds(wmPoiIds, wmPoiIds);
                //2、 单店/单店药品/闪购单店:校验客户资质与上线主站门店或普通门店资质内容是否一致；
                return validateQua(wmCustomerBasicBo, validPoiIds);
            }
            // 2、 校验客户资质与门店资质内容是否一致
            return validateQua(wmCustomerBasicBo, wmPoiIds);
        } else {
            //1、单店、单店药品、闪购单店只允许绑定一个门店
            if ((wmCustomerBasicBo.getCustomerRealType() == CustomerRealTypeEnum.DANDIAN.getValue() ||
                    wmCustomerBasicBo.getCustomerRealType() == CustomerRealTypeEnum.DANDIAN_YAOPIN.getValue()
                    || wmCustomerBasicBo.getCustomerRealType() == CustomerRealTypeEnum.DANDIAN_SG.getValue())
                    && wmPoiIds.size() > 1) {
                validateResultBo.setCode(CustomerConstants.RESULT_CODE_CUSTOMER_REAL_TYPE_ERROR);
                validateResultBo.setMsg("客户绑定了多个门店, 无法切换为单店类型客户");
                return validateResultBo;
            }

            //2、 校验客户资质与门店资质内容是否一致
            return validateQua(wmCustomerBasicBo, wmPoiIds);
        }
    }


    /**
     * 校验customerRealType是否合规
     *
     * @param wmCustomerBasicBo
     * @return
     */
    public ValidateResultBo validateCustomerRealType(WmCustomerBasicBo wmCustomerBasicBo, Integer opUid) throws WmCustomerException {
        logger.info("validateCustomerRealType:wmCustomerBasicBo={}", JSONObject.toJSONString(wmCustomerBasicBo));
        ValidateResultBo validateResultBo = new ValidateResultBo();
        validateResultBo.setCode(CustomerConstants.RESULT_CODE_PASS);
        String status = WmCustomerConstant.SUCCESS;
        String reason = "";
        try {
            // 1、新建客户类型不能为空
            if (wmCustomerBasicBo.getId() == 0) {
                CustomerRealTypeEnum typeEnum = CustomerRealTypeEnum.getByValue(wmCustomerBasicBo.getCustomerRealType());
                if (typeEnum == CustomerRealTypeEnum.QINGXUANZE || typeEnum == CustomerRealTypeEnum.DEFAULT_TYPE) {
                    validateResultBo.setCode(CustomerConstants.RESULT_CODE_CUSTOMER_REAL_TYPE_ERROR);
                    validateResultBo.setMsg("客户类型不能为空");
                    return validateResultBo;
                }

                //外卖单店新增场景-客户类型以及资质校验
                String checkResultStr = checkWmSingleSceneInsert(wmCustomerBasicBo, opUid);
                if (StringUtils.isNotEmpty(checkResultStr)) {
                    validateResultBo.setCode(CustomerConstants.RESULT_CODE_CUSTOMER_REAL_TYPE_ERROR);
                    validateResultBo.setMsg(checkResultStr);
                    return validateResultBo;
                }
                return validateResultBo;
            }
            // 2、客户类型切换校验
            if (!checkCanSwitchCustomerType(wmCustomerBasicBo)) {
                validateResultBo.setCode(CustomerConstants.RESULT_CODE_CUSTOMER_REAL_TYPE_ERROR);
                if (!MccCustomerConfig.getCheckRuleSwitch()) {
                    validateResultBo.setMsg("客户类型在医药与非医药间切换，需要权限");
                } else {
                    validateResultBo.setMsg("客户类型不可切换为聚合配送商、食堂承包商、跨境B2C药品\n");
                }
                return validateResultBo;
            }


            WmCustomerDB wmCustomerDB = wmCustomerService.selectCustomerById(wmCustomerBasicBo.getId());
            // 仅到餐客户不允许编辑
            if(CustomerRealTypeEnum.DAOCAN.getValue() == wmCustomerDB.getCustomerRealType()){
                validateResultBo.setCode(CustomerConstants.RESULT_CODE_CUSTOMER_REAL_TYPE_ERROR);
                validateResultBo.setMsg("不允许编辑到餐客户\n");
                return validateResultBo;
            }
            if (MccCustomerConfig.getCheckRuleSwitch() && wmCustomerDB != null && wmCustomerBasicBo.getCustomerRealType() != wmCustomerDB.getCustomerRealType()) {
                // 门店业务线校验：门店与客户按业务线绑定
                if (wmCustomerBasicBo.getId() % 100 <= MccCustomerConfig.getCheckBizOrgCodeShowWmPoiIdGrayPercent()) {
                    String errMsg = validMatchBizOrgCodeForCustomerAndPoiForCustomerShowWmPoiId(wmCustomerBasicBo.getId(), wmCustomerBasicBo.getBizOrgCode());
                    if (StringUtils.isNotBlank(errMsg)) {
                        validateResultBo.setCode(CustomerConstants.RESULT_CODE_CUSTOMER_BIZ_ORG_CODE_ERROR);
                        validateResultBo.setMsg(errMsg);
                        return validateResultBo;
                    }
                } else {
                    if (!validMatchBizOrgCodeForCustomerAndPoiForCustomer(wmCustomerBasicBo.getId(), wmCustomerBasicBo.getBizOrgCode())) {
                        validateResultBo.setCode(CustomerConstants.RESULT_CODE_CUSTOMER_BIZ_ORG_CODE_ERROR);
                        validateResultBo.setMsg("门店所属业务线与客户业务线不一致，无法绑定，请修改客户类型或修改门店品类后再操作绑定，保证外卖门店绑定外卖客户、闪购门店绑定闪购客户、医药门店绑定医药客户。");
                        return validateResultBo;
                    }
                }


            }

            //3、非单店、单店药品、闪购单店、美食城、食堂不需要后续校验
            if (wmCustomerBasicBo.getCustomerRealType() != CustomerRealTypeEnum.DANDIAN.getValue() &&
                    wmCustomerBasicBo.getCustomerRealType() != CustomerRealTypeEnum.DANDIAN_YAOPIN.getValue() &&
                    wmCustomerBasicBo.getCustomerRealType() != CustomerRealTypeEnum.DANDIAN_SG.getValue() &&
                    wmCustomerBasicBo.getCustomerRealType() != CustomerRealTypeEnum.MEISHICHENG.getValue() &&
                    wmCustomerBasicBo.getCustomerRealType() != CustomerRealTypeEnum.SHITANG.getValue()) {
                return validateResultBo;
            }

            //外卖单店修改场景下的客户类型以及资质校验
            String checkUpdateResultStr = checkWmSingleSceneUpdate(wmCustomerBasicBo, wmCustomerDB, opUid);
            if (StringUtils.isNotEmpty(checkUpdateResultStr)) {
                validateResultBo.setCode(CustomerConstants.RESULT_CODE_CUSTOMER_REAL_TYPE_ERROR);
                validateResultBo.setMsg(checkUpdateResultStr);
                return validateResultBo;
            }

            List<Long> wmPoiIds = WmCustomerPoiAggre.Factory.make().selectWmPoiIdsByCustomerId(wmCustomerBasicBo.getId());
            //3.1、美食城/食堂
            if (wmCustomerBasicBo.getCustomerRealType() == CustomerRealTypeEnum.MEISHICHENG.getValue() ||
                    wmCustomerBasicBo.getCustomerRealType() == CustomerRealTypeEnum.SHITANG.getValue()) {
                // 【单店】切换为【食堂】需在【客户-门店列表】页中上传资质共用证明或美食城档口列表
                // 【单店】切换为【美食城】无需校验资质共用证明或美食城档口列表（虚假美食城项目上线后，会打开MCC配置，忽略校验）
                if (null != wmCustomerDB && wmCustomerDB.getCustomerRealType() == CustomerRealTypeEnum.DANDIAN.getValue()) {
                    //单店->美食城，不校验
                    if (wmCustomerBasicBo.getCustomerRealType() == CustomerRealTypeEnum.MEISHICHENG.getValue() &&
                            MccCustomerConfig.closeDDSwitchToMSCQuaListCheck()) {
                        //do nothing
                    } else {
                        //单店->食堂
                        Set<String> commonQuaSet = wmCustomerService.getCustomerQuaList(wmCustomerBasicBo.getId());
                        if (commonQuaSet.size() == 0) {
                            //未上传"美食城档口列表或资质共用证明"
                            validateResultBo.setCode(CustomerConstants.RESULT_CODE_CUSTOMER_REAL_TYPE_ERROR);
                            validateResultBo.setMsg("需要在客户-门店列表中上传资质共用证明及附件处上传美食城档口列表");
                            return validateResultBo;
                        }
                    }
                }

            }
            // 3.2 校验单店、单店药品、闪购单店、美食城、食堂门店绑定合规性与资质一致性
            return validatePoiBindAndQua(wmCustomerBasicBo, wmPoiIds);

        } catch (WmCustomerException e) {
            status = WmCustomerConstant.BUSINESS_EXCEPTION;
            reason = e.getMsg();
            throw e;
        } catch (TException e) {
            status = WmCustomerConstant.BUSINESS_EXCEPTION;
            reason = e.getMessage();
            throw new WmCustomerException(CustomerConstants.RESULT_CODE_ERROR, "系统异常请稍后重试");
        } catch (Exception e) {
            status = WmCustomerConstant.SYSTEM_EXCEPTION;
            reason = "未知原因";
            throw e;
        } finally {
            String source = "customer";
            //如果校验不通过，打不通过原因埋点
            if (validateResultBo.getCode() != CustomerConstants.RESULT_CODE_PASS) {
                status = String.valueOf(validateResultBo.getCode());
                reason = validateResultBo.getMsg();
            }
            Cat.logEvent(CustomerMetricEnum.CUSTOMER_VALID.getName(), source, status, reason);
            MetricHelper.build().name(CustomerMetricEnum.CUSTOMER_VALID.getName()).tag(CustomerMetricEnum.CUSTOMER_VALID.getTag(), source)
                    .tag(CustomerMetricEnum.CUSTOMER_VALID.getStatus(), status).count();
        }
    }


    // 校验品类
    public boolean checkPoiCategory(int customerRealType, long firstCategory) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.service.customer.WmCustomerRealTypeService.checkPoiCategory(int,long)");
        //客户类型是单店药品/总部药品连锁/跨境B2C药品
        boolean isDrug = DRUG_CUSTOMER_REAL_TYPE.contains(customerRealType);
        // 品类是医药品类
        boolean isDrugCategory = (firstCategory == MED_CATEGORY_ID);
        if (isDrug ^ isDrugCategory) {
            return false;
        }
        return true;
    }

    /**
     * 基于客户realType的扩展信息,进行特殊校验
     *
     * @param wmCustomerBasicBo
     * @param opUid
     * @return
     */
    public ValidateResultBo validateCustomerRealTypeSpInfo(WmCustomerBasicBo wmCustomerBasicBo, Integer opUid) {
        ValidateResultBo validateResultBo = new ValidateResultBo();
        validateResultBo.setCode(CustomerConstants.RESULT_CODE_PASS);
        //美食城客户类型,检验部分必填信息
        if (wmCustomerBasicBo.getCustomerRealType() == CustomerRealTypeEnum.MEISHICHENG.getValue()) {
            CustomerRealTypeSpInfoBo customerRealTypeSpInfoBo = wmCustomerBasicBo.getCustomerRealTypeSpInfoBo();
            Integer ownerUid = getOwnerUid(wmCustomerBasicBo, opUid);
            if (wmCustomerGrayService.isGrayMscPoiCntCheckNew(ownerUid)){
                // 命中灰度之后不在校验美食城图片
                if (customerRealTypeSpInfoBo == null || StringUtils.isBlank(customerRealTypeSpInfoBo.getFoodCityName())) {
                    validateResultBo.setCode(CustomerConstants.RESULT_CODE_CUSTOMER_REAL_TYPE_SP_INFO_ERROR);
                    validateResultBo.setMsg("美食城类型客户扩展信息不能为空");
                    return validateResultBo;
                }
            } else if (customerRealTypeSpInfoBo == null || StringUtils.isBlank(customerRealTypeSpInfoBo.getFoodCityName()) || StringUtils.isBlank(customerRealTypeSpInfoBo.getFoodCityPic())) {
                validateResultBo.setCode(CustomerConstants.RESULT_CODE_CUSTOMER_REAL_TYPE_SP_INFO_ERROR);
                validateResultBo.setMsg("美食城类型客户扩展信息不能为空");
                return validateResultBo;
            }
        }
        return validateResultBo;
    }

    private Integer getOwnerUid(WmCustomerBasicBo wmCustomerBasicBo, Integer opUid){
        try{
            if (wmCustomerBasicBo.getId()>0){
                WmCustomerBasicBo currentBo = wmCustomerService.getCustomerById(wmCustomerBasicBo.getId());
                return currentBo.getOwnerUid();
            }
            return opUid;
        }catch (Exception e){
            logger.error("validateCustomerRealTypeSpInfo-获取客户责任人异常，灰度判断按照操作人Id:{}",JSON.toJSONString(wmCustomerBasicBo));
            return opUid;
        }

    }

    /**
     * 门店客户业务线匹配性校验
     * @param customerId
     * @param bizOrgCode
     * @return
     */
    public boolean validMatchBizOrgCodeForCustomerAndPoiForCustomer(int customerId, Integer bizOrgCode) {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.service.customer.WmCustomerRealTypeService.validMatchBizOrgCodeForCustomerAndPoiForCustomer(int,java.lang.Integer)");
        if (bizOrgCode == null || customerId <= 0) {
            return true;
        }

        List<Long> wmPoiIdList = WmCustomerPoiAggre.Factory.make().selectWmPoiIdsByCustomerId(customerId);
        if (CollectionUtils.isEmpty(wmPoiIdList)) {
            return true;
        }
        List<List<Long>> allPoiIds = Lists.partition(wmPoiIdList, 100);
        for (List<Long> wmPoiIs : allPoiIds) {
            List<WmPoiAggre> wmPoiAggreList = wmPoiQueryAdapter.getWmPoiAggre(wmPoiIs, Sets.newHashSet(WmPoiFieldQueryConstant.WM_POI_FIELD_WM_POI_ID, WmPoiFieldQueryConstant.WM_POI_FIELD_BIZ_ORG_CODE));
            Optional<WmPoiAggre> errorObject = wmPoiAggreList.stream().filter(x -> x.getBiz_org_code() != bizOrgCode.intValue()).findFirst();
            if (errorObject.isPresent()) {
                return false;
            }
        }
        return true;
    }


    /**
     * 门店客户业务线匹配性校验
     * @param customerId
     * @param bizOrgCode
     * @return
     */
    public String validMatchBizOrgCodeForCustomerAndPoiForCustomerShowWmPoiId(int customerId, Integer bizOrgCode) throws WmCustomerException {
        if (bizOrgCode == null || customerId <= 0) {
            return null;
        }
        List<Long> wmPoiIdList = wmPoiClient.getWmPoiIdListByNotBizOrgCode(customerId, bizOrgCode, 0, MccCustomerConfig.getCheckBizOrgCodeShowWmPoiIdSize());
        if (CollectionUtils.isEmpty(wmPoiIdList)) {
            return null;
        } else {
            return String.format("门店所属业务线与客户业务线不一致，无法绑定，请修改客户类型或修改门店品类后再操作绑定，保证外卖门店绑定外卖客户、" +
                    "闪购门店绑定闪购客户、医药门店绑定医药客户，不一致门店：%s", StringUtils.join(wmPoiIdList, "、"));
        }
    }

    /**
     * 门店客户业务线匹配性校验
     *
     * @param wmPoiId
     * @param bizOrgCode
     * @return
     */
    public boolean validMatchBizOrgCodeForCustomerAndPoiForPoi(long wmPoiId, Integer bizOrgCode) {
        if (bizOrgCode == null) {
            return true;
        }
        WmPoiAggre wmPoiAggre = wmPoiQueryAdapter.getWmPoiAggre(wmPoiId, Sets.newHashSet(WmPoiFieldQueryConstant.WM_POI_FIELD_WM_POI_ID, WmPoiFieldQueryConstant.WM_POI_FIELD_BIZ_ORG_CODE));
        if (wmPoiAggre == null) {
            return false;
        }
        if (wmPoiAggre.getBiz_org_code() != bizOrgCode.intValue()) {
            return false;
        }
        return true;
    }

    /**
     * 校验门店是否都存在物理门店
     * @param wmPoiIds
     * @param physicalPoiRelList
     * @return
     */
    private boolean exitPhysicalPoi(List<Long> wmPoiIds, List<WmPhysicalPoiRel> physicalPoiRelList) {
        if (CollectionUtils.isEmpty(physicalPoiRelList)) {
            return false;
        }
        Map<Long, Long> map = new HashMap<>();
        for (WmPhysicalPoiRel wmPhysicalPoiRel : physicalPoiRelList) {
            if (wmPhysicalPoiRel.getWmPhysicalPoiId() != null) {
                map.put(wmPhysicalPoiRel.getWmPoiId(), wmPhysicalPoiRel.getWmPhysicalPoiId());
            }
        }
        for (Long wmPoiId : wmPoiIds) {
            if (!map.containsKey(wmPoiId)) {
                return false;
            }
        }
        return true;
    }


    /**
     * 获取客户已绑定与将绑定经营门店绑定不同物理门店数量
     * @param physicalPoiRelList
     * @return
     */
    private Integer getPhysicalPoiCount(List<WmPhysicalPoiRel> physicalPoiRelList) {
        //2. 对经营门店绑定的物理门店去重
        HashSet<Long> physicalPoiIds = new HashSet<>();
        for (WmPhysicalPoiRel wmPhysicalPoiRel : physicalPoiRelList) {
            physicalPoiIds.add(wmPhysicalPoiRel.getWmPhysicalPoiId());
        }
        return physicalPoiIds.size();
    }

    /**
     * 获取待校验的门店集合
     * 如门店没有“子门店”标，则资质校验与原逻辑一致：门店资质须与客户资质一致
     * 如门店有"子门店"标，则判断该客户下是否绑定有：与此子门店同一个物理门店、且有"主站子门店（即主站经营门店）"标、且状态为"上线"的门店。
     * - 如有，则不检验该子门店资质
     * - 如没有，则须遵循原资质校验逻辑：门店资质须与客户资质一致
     * @param bindPoiIds
     * @param unBindPoiIds
     * @return
     * @throws WmCustomerException
     */
    private List<Long> getValidPoiIds(List<Long> bindPoiIds, List<Long> unBindPoiIds) throws WmCustomerException {
        // 1. 获取待绑定的子门店标签的经营门店id集合
        List<Long> subPoiIdList = wmPoiClient.getTargetLablePoiIds(unBindPoiIds, LabelSubjectTypeEnum.POI.getCode(), MccCustomerConfig.getSubPoiTagId());
        // 2. 判断是否存在未绑定的子门店标签的经营门店，不存在则所有门店资质须与客户资质一致
        if (subPoiIdList.size() == 0) {
            return unBindPoiIds;
        }
        // 3. 获取已绑定门店带主站门店标签且为上线状态的经营门店id集合
        List<Long> onlineMainPoiIds = wmPoiClient.getOnlineMainPoiIds(bindPoiIds);
        if (onlineMainPoiIds.size() > 1) {
            logger.error("获取待校验的门店集合异常, 单店客户包含上线的主站经营门店数量大于1， bindPoiIds={}, unBindPoiIds={}", JSON.toJSONString(bindPoiIds), JSON.toJSONString(unBindPoiIds));
        }
        // 4. 判断是否存在已绑定门店带主站门店标签且为上线状态的经营门店，不存在则所有门店资质须与客户资质一致
        if(onlineMainPoiIds.size() == 0) {
            return unBindPoiIds;
        }
        // 存在则校验没有带子门店id集合资质资质
        return unBindPoiIds.stream().filter(id -> !subPoiIdList.contains(id)).collect(Collectors.toList());
    }

    /**
     * 外卖单店校验场景信息
     *
     * @param basicBo
     * @param opUid
     */
    private String checkWmSingleSceneInsert(WmCustomerBasicBo basicBo, Integer opUid) throws WmCustomerException {
        //非外卖单店不校验
        if (basicBo.getCustomerRealType() != CustomerRealTypeEnum.DANDIAN.getValue()) {
            return null;
        }
        //非个人证件不校验
        if (basicBo.getCustomerType() != CustomerType.CUSTOMER_TYPE_IDCARD.getCode()) {
            return null;
        }
        //ID不等于0则直接返回，等于0标识新增
        if (basicBo.getId() != 0) {
            return null;
        }

        CustomerSceneInfoBO customerSceneInfoBO = basicBo.getSceneInfoBO();
        //判断是否命中灰度
        boolean hitGray = wmCustomerGrayService.hitWmSinglePerCertifyGray(opUid, basicBo);
        if (!hitGray && customerSceneInfoBO == null) {
            return null;
        }
        //非BD上单渠道，不允许新增个人资质客户
        if ((basicBo.getCustomerSource() == null || (basicBo.getCustomerSource() != null && basicBo.getCustomerSource() != CustomerSource.WAIMAI_BD))
                && basicBo.getCustomerType() == CustomerType.CUSTOMER_TYPE_IDCARD.getCode()) {
            return "不支持创建外卖单店类型的个人证件客户";
        }
        //先富渠道，无个人资质权限
        if (basicBo.getCustomerSource() != null
                && basicBo.getCustomerSource() == CustomerSource.WAIMAI_BD
                && !checkWmSinglePerCertifyAuth(opUid)) {
            return "无权创建“外卖单店”的“个人证件”客户";
        }
        //外卖单店&个人资质 需要校验场景信息合法性
        if (basicBo.getSceneInfoBO() != null
                && basicBo.getCustomerSource() != null
                && basicBo.getCustomerSource() == CustomerSource.WAIMAI_BD
                && basicBo.getCustomerType() == CustomerType.CUSTOMER_TYPE_IDCARD.getCode()) {
            return checkSceneInfo(basicBo.getSceneInfoBO());
        }
        return null;
    }

    /**
     * 修改场景校验-外卖单店服务
     *
     * @param basicBo
     * @param opUid
     * @return
     */
    private String checkWmSingleSceneUpdate(WmCustomerBasicBo basicBo, WmCustomerDB wmCustomerDB, Integer opUid) throws WmCustomerException{

        //非外卖单店不校验
        if (basicBo.getCustomerRealType() != CustomerRealTypeEnum.DANDIAN.getValue()) {
            return null;
        }
        //非个人资质不校验
        if (basicBo.getCustomerType() != CustomerType.CUSTOMER_TYPE_IDCARD.getCode()) {
            return null;
        }
        //客户ID小于等于0则直接返回
        if (basicBo.getId() <= 0) {
            return null;
        }

        //判断是否命中灰度
        boolean hitGray = wmCustomerGrayService.hitWmSinglePerCertifyGray(opUid, basicBo);
        if (!hitGray) {
            return null;
        }

        //先富渠道，无个人资质权限&特批驳回-不允许修改客户
        if (basicBo.getCustomerSource() != null
                && basicBo.getCustomerSource() == CustomerSource.WAIMAI_BD
                && basicBo.getCustomerType() == CustomerType.CUSTOMER_TYPE_IDCARD.getCode()
                && !checkWmSinglePerCertifyAuth(opUid)
                && wmCustomerDB.getAuditStatus() != null
                && wmCustomerDB.getAuditStatus() == CustomerAuditStatus.CUSTOMER_SPECIAL_AUDIT_REJECT.getCode()) {
            return "无权创建“外卖单店”的“个人证件”客户";
        }

        //由非外卖单店修改为外卖单店个人资质，则不允许
        if (basicBo.getCustomerRealType() != wmCustomerDB.getCustomerRealType()
                && basicBo.getCustomerRealType() == CustomerRealTypeEnum.DANDIAN.getValue()
                && basicBo.getCustomerType() == CustomerType.CUSTOMER_TYPE_IDCARD.getCode()) {
            return "【客户资质主体】为“个人证件”的客户，【客户类型】不支持由非外卖单店修改为“外卖单店”";
        }
        //外卖单店客户，资质类型由营业执照修改为个人资质，则不允许
        if (basicBo.getCustomerRealType() == wmCustomerDB.getCustomerRealType()
                && basicBo.getCustomerRealType() == CustomerRealTypeEnum.DANDIAN.getValue()
                && basicBo.getCustomerType() == CustomerType.CUSTOMER_TYPE_IDCARD.getCode()
                && wmCustomerDB.getCustomerType() == CustomerType.CUSTOMER_TYPE_BUSINESS.getCode()) {
            return "“外卖单店”类型的客户，【客户资质主体】不支持由“营业执照”修改为“个人证件”";
        }

        //外卖单店&个人资质 需要校验场景信息合法性
        if (basicBo.getSceneInfoBO() != null
                && basicBo.getCustomerSource() == CustomerSource.WAIMAI_BD
                && basicBo.getCustomerType() == CustomerType.CUSTOMER_TYPE_IDCARD.getCode()) {
            return checkSceneInfo(basicBo.getSceneInfoBO());
        }

        //定义场景信息字符串属性
        String sceneInfoStr = "";
        if (basicBo.getSceneInfoBO() != null) {
            sceneInfoStr = JSON.toJSONString(basicBo.getSceneInfoBO());
        }

        //非外卖单店则直接返回
        if (checkModifySameDanDianAndPerson(wmCustomerDB, basicBo)
                && sceneInfoNotUpdateStatus.contains(wmCustomerDB.getAuditStatus())
                && !wmCustomerDB.getSceneInfo().equals(sceneInfoStr)) {
            return "特批通过后场景信息不能修改";
        }

        return null;
    }

    /**
     * 校验修改前后都是外卖单店个人资质
     *
     * @param wmCustomerDB
     * @param wmCustomerBasicBo
     */
    private boolean checkModifySameDanDianAndPerson(WmCustomerDB wmCustomerDB, WmCustomerBasicBo wmCustomerBasicBo) {
        if (wmCustomerDB.getCustomerRealType() == wmCustomerBasicBo.getCustomerRealType()
                && wmCustomerDB.getCustomerType() == wmCustomerBasicBo.getCustomerType()
                && wmCustomerDB.getCustomerType() == CustomerType.CUSTOMER_TYPE_IDCARD.getCode()
                && wmCustomerDB.getCustomerRealType() == CustomerRealTypeEnum.DANDIAN.getValue()) {
            return true;
        }
        return false;
    }

    /**
     * 是否有外卖单店个人客户资质权限
     *
     * @param opUid
     * @return
     */
    private boolean checkWmSinglePerCertifyAuth(Integer opUid) throws WmCustomerException {
        boolean hasPerCertifyAdminAuth = upmAuthCheckService.checkUpmRoleByIdAndUserId(opUid, MccCustomerConfig.getPersonCertifyAdminRoleId());
        boolean hasPerCertifyUserAuth = upmAuthCheckService.checkUpmRoleByIdAndUserId(opUid, MccCustomerConfig.getPersonCertifyUserRoleId());
        if (hasPerCertifyUserAuth || hasPerCertifyAdminAuth) {
            return true;
        }
        return false;
    }

    /**
     * 校验场景信息是否合法
     *
     * @param customerSceneInfoBO
     * @return
     */
    private String checkSceneInfo(CustomerSceneInfoBO customerSceneInfoBO) {
        //场景非法校验
        if (customerSceneInfoBO.getSceneType() == null || SingleCustomerSceneEnum.of(customerSceneInfoBO.getSceneType()) == null) {
            return "场景选择非法，请选择有效";
        }
        //场景说明校验
        if (StringUtils.isBlank(customerSceneInfoBO.getDescription()) || customerSceneInfoBO.getDescription().length() > 100) {
            return "场景说明不能为空且最多字数为100字";
        }
        //证明材料校验
        if (StringUtils.isBlank(customerSceneInfoBO.getSceneProveFiles()) || customerSceneInfoBO.getSceneProveFiles().split(",").length > 3) {
            return "证件材料不能为空且不能超过3个";
        }
        //重复客户ID校验
        if (customerSceneInfoBO.getSceneType() == SingleCustomerSceneEnum.ONE_CERTIFY_2_MANY.getCode()
                && (StringUtils.isBlank(customerSceneInfoBO.getDuplicateCustomerIds()) || customerSceneInfoBO.getDuplicateCustomerIds().length() > 100)) {
            return "重复客户ID不能为空且最多字数为100字";
        }
        return null;
    }
}
