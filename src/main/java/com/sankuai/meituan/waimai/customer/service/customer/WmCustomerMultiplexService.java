package com.sankuai.meituan.waimai.customer.service.customer;

import com.alibaba.fastjson.JSON;
import com.sankuai.meituan.waimai.customer.config.MccConfig;
import com.sankuai.meituan.waimai.customer.config.MccCustomerConfig;
import com.sankuai.meituan.waimai.customer.constant.customer.DeleteSignerSceneEnum;
import com.sankuai.meituan.waimai.customer.ddd.adapter.MtCustomerThriftServiceAdapterImpl;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerDB;
import com.sankuai.meituan.waimai.customer.service.kp.WmCustomerKpService;
import com.sankuai.meituan.waimai.customer.util.trans.WmCustomerKpTransUtil;
import com.sankuai.meituan.waimai.customer.util.trans.WmCustomerTransUtil;
import com.sankuai.meituan.waimai.thrift.customer.constant.*;
import com.sankuai.meituan.waimai.thrift.customer.constant.customer.CustomerMultiplexEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.WmCustomerBasicBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.customer.WmCustomerMultiplexDTO;
import com.sankuai.meituan.waimai.thrift.customer.domain.kp.WmCustomerKp;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import com.sankuai.meituan.waimai.thrift.exception.WmServerException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 客户复用逻辑处理
 */
@Service
@Slf4j
public class WmCustomerMultiplexService {


    @Autowired
    private MtCustomerThriftServiceAdapterImpl mtCustomerThriftServiceAdapterImpl;

    @Autowired
    private WmCustomerKpService wmCustomerKpService;


    @Autowired
    protected WmCustomerPlatformDataParseService wmCustomerPlatformDataParseService;


    private long DEFAULT_LONG_ZERO = 0L;

    /**
     * 判断客户端操作入口是否允许操作客户复用
     * 目前允许操作客户复用的操作入口：BD上单（包含PC/APP）
     *
     * @return
     */
    public boolean isAllowOperateMultiplexCustomer(CustomerSource customerSource, Integer customerDeviceType) {
        //操作渠道来源必须为【BD上单】
        if (customerSource == null || !customerSource.equals(CustomerSource.WAIMAI_BD)) {
            return false;
        }
        // 由于customerDeviceType是本次客户复用需求新增字段，蜜蜂历史包无法兼容，待蜜蜂历史包全量升级后，再开启
        if (MccCustomerConfig.useCustomerDeviceType()) {
            if (customerDeviceType == null) {
                return false;
            }
            // 操作入口必须为PC/APP
            if (!customerDeviceType.equals(CustomerDeviceType.PC.getCode()) && !customerDeviceType.equals(CustomerDeviceType.APP.getCode())) {
                return false;
            }
        }
        return true;
    }

    /**
     * 更新客户时处理客户复用字段（数据补全逻辑）
     *
     * @param wmCustomerBasicBo
     * @param wmCustomerDb
     */
    public void dealCustomerMultiplexFieldWhenCustomerUpdate(WmCustomerBasicBo wmCustomerBasicBo, WmCustomerDB wmCustomerDb) {
        if (wmCustomerBasicBo == null || wmCustomerDb == null) {
            return;
        }
        //如果客户端操作入口处于操作客户复用的范围内
        if (isAllowOperateMultiplexCustomer(wmCustomerBasicBo.getCustomerSource(), wmCustomerBasicBo.getCustomerDeviceType())) {
            //如果客户修改为复用则设置复用标记为复用
            if (isMultiplex(wmCustomerBasicBo.getMultiplexCustomerId())) {
                wmCustomerBasicBo.setMultiplex(CustomerMultiplexEnum.YES.getType());
                //未变更复用
                // TODO: 2022/8/2 此处逻辑的意义？新建客户场景没有
                if (!isMultiplexChange(wmCustomerBasicBo.getMultiplexCustomerId(), wmCustomerDb.getMultiplexCustomerId())) {
                    wmCustomerBasicBo.setMultiplexBusinessLineId(wmCustomerDb.getMultiplexBusinessLineId());
                }
            } else {
                //修改客户为非复用时则设置复用标记为非复用，设置其他复用字段为默认值
                wmCustomerBasicBo.setMultiplex(CustomerMultiplexEnum.NO.getType());
                wmCustomerBasicBo.setMultiplexCustomerId(DEFAULT_LONG_ZERO);
                wmCustomerBasicBo.setMultiplexBusinessLineId(DEFAULT_LONG_ZERO);
            }
        } else {
            //不允许操作客户复用的客户端操作入口，设置客户复用字段为原值
            wmCustomerBasicBo.setMultiplex(wmCustomerDb.getMultiplex());
            wmCustomerBasicBo.setMultiplexCustomerId(wmCustomerDb.getMultiplexCustomerId());
            wmCustomerBasicBo.setMultiplexBusinessLineId(wmCustomerDb.getMultiplexBusinessLineId());
        }
    }

    /**
     * 创建客户时处理客户复用字段
     *
     * @param wmCustomerBasicBo
     */
    public void dealCustomerMultiplexFieldWhenCustomerNew(WmCustomerBasicBo wmCustomerBasicBo) {
        if (wmCustomerBasicBo == null) {
            return;
        }
        //如果客户端操作操作入口处于操作客户复用的范围内
        if (isAllowOperateMultiplexCustomer(wmCustomerBasicBo.getCustomerSource(), wmCustomerBasicBo.getCustomerDeviceType())) {
            //创建复用客户则设置复用标记为复用
            if (isMultiplex(wmCustomerBasicBo.getMultiplexCustomerId())) {
                wmCustomerBasicBo.setMultiplex(CustomerMultiplexEnum.YES.getType());
            } else {
                //如果客户为非复用则设置复用标记为非复用，设置其他复用字段为默认值
                wmCustomerBasicBo.setMultiplex(CustomerMultiplexEnum.NO.getType());
                wmCustomerBasicBo.setMultiplexCustomerId(DEFAULT_LONG_ZERO);
                wmCustomerBasicBo.setMultiplexBusinessLineId(DEFAULT_LONG_ZERO);
            }
        } else {
            //不允许操作客户复用的客户端操作入口，设置客户复用字段为默认值
            wmCustomerBasicBo.setMultiplex(CustomerMultiplexEnum.NO.getType());
            wmCustomerBasicBo.setMultiplexCustomerId(DEFAULT_LONG_ZERO);
            wmCustomerBasicBo.setMultiplexBusinessLineId(DEFAULT_LONG_ZERO);
        }
    }


    /**
     * 客户复用自动同步复用签约人
     *
     * @param wmCustomerBasicBo
     * @param isNew
     * @param opUid
     * @param opName
     */
    public void synCustomerMultiplexSigner(WmCustomerBasicBo wmCustomerBasicBo, boolean isNew, WmCustomerDB wmCustomerDbBefore, int opUid, String opName) {
        log.info("synCustomerMultiplexSigner wmCustomerBasicBo={},isNew={},wmCustomerDbBefore={},opUid={},opName={}", JSON.toJSONString(wmCustomerBasicBo), JSON.toJSONString(wmCustomerDbBefore), isNew, opUid, opName);
        try {
            if (wmCustomerBasicBo == null) {
                return;
            }
            if (!isAllowOperateMultiplexCustomer(wmCustomerBasicBo.getCustomerSource(), wmCustomerBasicBo.getCustomerDeviceType())) {
                return;
            }
            CustomerType customerType = CustomerType.getByCode(wmCustomerBasicBo.getCustomerType());
            if (!CustomerType.CUSTOMER_TYPE_BUSINESS.equals(customerType) && !CustomerType.CUSTOMER_TYPE_IDCARD.equals(customerType)) {
                return;
            }
            if (isNew) {
                if (!isMultiplex(wmCustomerBasicBo.getMultiplexCustomerId())) {
                    return;
                }
                createCustomerMultiplexSigner(wmCustomerBasicBo, opUid, opName);
            } else {
                if (wmCustomerDbBefore == null) {
                    return;
                }
                boolean isMultiplexChange = isMultiplexChange(wmCustomerBasicBo.getMultiplexCustomerId(), wmCustomerDbBefore.getMultiplexCustomerId());
                if (!isMultiplexChange) {
                    return;
                }
                WmCustomerKp wmCustomerKp = wmCustomerKpService.getCustomerSignerKp(wmCustomerBasicBo.getId());
                if (!isMultiplex(wmCustomerBasicBo.getMultiplexCustomerId())) {
                    //复用变为未复用,删除原签约人
                    wmCustomerKpService.deleteKpSignerForCustomerUpdate(DeleteSignerSceneEnum.CUSTOMER_MULTIPLEX, wmCustomerKp, customerType, opUid, opName);
                } else {
                    //未复用变为复用，删除原签约人新增复用签约人
                    wmCustomerKpService.deleteKpSignerForCustomerUpdate(DeleteSignerSceneEnum.CUSTOMER_MULTIPLEX, wmCustomerKp, customerType, opUid, opName);
                    createCustomerMultiplexSigner(wmCustomerBasicBo, opUid, opName);
                }
            }
        } catch (Exception e) {
            log.error("synCustomerMultiplexSigner wmCustomerBasicBo={},isNew={},opUid={},opName={}", JSON.toJSONString(wmCustomerBasicBo), opUid, opName, e);
        }
    }

    /**
     * 是否复用客户
     *
     * @param multiplexCustomerId
     * @return
     */
    public boolean isMultiplex(Long multiplexCustomerId) {
        if (multiplexCustomerId == null || multiplexCustomerId <= 0) {
            return false;
        }
        return true;
    }

    /**
     * 创建复用客户的复用签约人
     *
     * @param wmCustomerBasicBo
     * @param opUid
     * @param opName
     */
    private void createCustomerMultiplexSigner(WmCustomerBasicBo wmCustomerBasicBo, int opUid, String opName) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.service.customer.WmCustomerMultiplexService.createCustomerMultiplexSigner(WmCustomerBasicBo,int,String)");
        try {
            WmCustomerMultiplexDTO wmCustomerMultiplexDto = mtCustomerThriftServiceAdapterImpl.getMultiplexCustomer(wmCustomerBasicBo.getMultiplexCustomerId()
                    , wmCustomerBasicBo.getMultiplexBusinessLineId(), true);
            if (wmCustomerMultiplexDto == null || wmCustomerMultiplexDto.getSigner() == null) {
                return;
            }
            WmCustomerDB wmCustomerDb = WmCustomerTransUtil.customerBoToDB(wmCustomerBasicBo);
            WmCustomerKp wmCustomerKp = WmCustomerKpTransUtil.multiplexSignerDtoToCustomerKp(wmCustomerMultiplexDto.getSigner());
            wmCustomerKp.setCustomerId(wmCustomerDb.getId());
            wmCustomerKp.setValid(KpConstants.VALID);
            wmCustomerKp.setState(KpSignerStateMachine.NO_DATA.getState());
            wmCustomerKpService.modifyKpInfo(wmCustomerDb, wmCustomerKp, KpOperationTypeConstant.INSERT, opUid, opName);
        } catch (WmCustomerException e) {
            log.error("synCustomerMultiplexSigner wmCustomerBasicBo={},opUid={},opName={},code={},msg={}", JSON.toJSONString(wmCustomerBasicBo), opUid, opName, e.getCode(), e.getCode());
        } catch (WmServerException e) {
            log.error("synCustomerMultiplexSigner wmCustomerBasicBo={},opUid={},opName={},code={},msg={}", JSON.toJSONString(wmCustomerBasicBo), opUid, opName, e.getCode(), e.getCode());
        } catch (Exception e) {
            log.error("synCustomerMultiplexSigner wmCustomerBasicBo={},opUid={},opName={}", JSON.toJSONString(wmCustomerBasicBo), opUid, opName, e);
        }
    }

    /**
     * 复用客户变更
     * 复用客户变更指如下三种情况：
     * 1 复用-》未复用
     * 2 未复用-》复用
     * 3 复用客户A-》复用客户B
     *
     * @param afterCustomerMultiplexId
     * @param beforeCustomerMultiplexId
     * @return
     */
    public boolean isMultiplexChange(Long afterCustomerMultiplexId, Long beforeCustomerMultiplexId) {
        // TODO: 2022/8/2 直接比对两个参数值是不是就可以了
        //复用-》未复用
        if (beforeCustomerMultiplexId != null && beforeCustomerMultiplexId > 0
                && (afterCustomerMultiplexId == null || afterCustomerMultiplexId <= 0)) {
            return true;
        }
        //未复用-》复用
        if ((beforeCustomerMultiplexId == null || beforeCustomerMultiplexId <= 0)
                && (afterCustomerMultiplexId != null && afterCustomerMultiplexId > 0)) {
            return true;
        }
        //复用客户A-》复用客户B
        if (afterCustomerMultiplexId != null
                && beforeCustomerMultiplexId != null
                && !afterCustomerMultiplexId.equals(beforeCustomerMultiplexId)) {
            return true;
        }
        return false;
    }
}
