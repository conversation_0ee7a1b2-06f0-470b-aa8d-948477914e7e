package com.sankuai.meituan.waimai.customer.service.sc.thirdworkplace.config;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@AllArgsConstructor
@NoArgsConstructor
@Data
public class CustomerPopParam {
    /**
     * 比率类型：1-比率计算，2-差值计算
     */
    private Integer rateType;

    /**
     * 维度：dt-日期，mo-月份
     */
    private String dim;

    /**
     * 偏移量
     */
    private Integer offset;

    /**
     * 环比信息列表
     */
    private List<ReteIndexInfo> reteIndexInfo;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class ReteIndexInfo {

        /**
         * 指标索引
         */
        private String index;

        /**
         * 派生指标名称
         */
        private String derivedIndexName;

        /**
         * 派生指标ID
         */
        private String derivedIndexId;
    }
}
