package com.sankuai.meituan.waimai.customer.service.kp.operatorv2;

import com.sankuai.meituan.waimai.customer.domain.WmCustomerDB;
import com.sankuai.meituan.waimai.customer.service.kp.operatorv2.dboperatorv2.BusinessSignerDBOperator;
import com.sankuai.meituan.waimai.customer.service.kp.operatorv2.preverifierv2.KpPreverify;
import com.sankuai.meituan.waimai.thrift.customer.constant.KPSource;
import com.sankuai.meituan.waimai.thrift.customer.domain.kp.WmCustomerKp;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import com.sankuai.meituan.waimai.thrift.exception.WmServerException;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;

/**
 * Desc: 商家端 签约人KP操作
 */
@Service
public class BusinessSignerOperate extends WmCustomerKpOperate {

    @Autowired
    private BusinessSignerDBOperator businessSignerDBOperator;

    @Override
    public void insert(WmCustomerDB wmCustomer, List<WmCustomerKp> oldCustomerKpList, WmCustomerKp operateKp, int uid, String uname) throws WmCustomerException, TException, WmServerException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.kp.operatorv2.BusinessSignerOperate.insert(WmCustomerDB,List,WmCustomerKp,int,String)");

    }

    @Override
    public void update(WmCustomerDB wmCustomer, List<WmCustomerKp> oldCustomerKpList, WmCustomerKp operateKp, int uid, String uname) throws WmCustomerException, TException, WmServerException {
        // 数据校验
        List<KpPreverify> verifyList;
        if (operateKp.getKpSource() == KPSource.WAIMAI_ZRZ || operateKp.getKpSource() == KPSource.SHANGOU_ZRZ) {
            verifyList = Arrays.asList(signerAuditStatusVerify, signerTypeChangeVerify, kpDataVerify, kpTypeNumVerify, signerCertTypeVerify, signerKpOperateVerify, agentAuthPreVerify);
        } else {
            verifyList = Arrays.asList(kpDataVerify, kpTypeNumVerify, signerCertTypeVerify, signerKpOperateVerify, agentAuthPreVerify);
        }
        
        for (KpPreverify kpPreverify : verifyList) {
            kpPreverify.verify(wmCustomer, oldCustomerKpList, null, operateKp, null, uid, uname);
        }
        // DB操作
        businessSignerDBOperator.update(wmCustomer, oldCustomerKpList, kpTransList(operateKp), uid, uname);
    }

    @Override
    public void delete(WmCustomerDB wmCustomer, List<WmCustomerKp> oldCustomerKpList, WmCustomerKp operateKp, int uid, String uname) throws WmCustomerException, TException, WmServerException {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.service.kp.operatorv2.BusinessSignerOperate.delete(WmCustomerDB,List,WmCustomerKp,int,String)");

    }
}
