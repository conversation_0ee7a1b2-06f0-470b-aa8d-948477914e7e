package com.sankuai.meituan.waimai.customer.service.dbus.customerPoiList;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.meituan.dbus.common.DbusUtils;
import com.meituan.dbus.thriftV2.utils.ThriftV2Utils;
import com.sankuai.meituan.waimai.customer.constant.WmCustomerPoiListESFields;
import com.sankuai.meituan.waimai.customer.constant.customer.WmCustomerRelTableDbusEnum;
import com.sankuai.meituan.waimai.customer.dao.WmCustomerDBMapper;
import com.sankuai.meituan.waimai.customer.dao.WmCustomerPoiDBMapper;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerPoiDB;
import com.sankuai.meituan.waimai.customer.domain.WmCustomerPoiQueryCondtionVo;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerESService;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerPoiAttributeService;
import com.sankuai.meituan.waimai.customer.service.customer.WmCustomerPoiListEsService;
import com.sankuai.meituan.waimai.customer.service.kp.WmCustomerKpPoiService;
import com.sankuai.meituan.waimai.thrift.customer.constant.KpConstants;
import com.sankuai.meituan.waimai.thrift.customer.constant.KpSignerStateMachine;
import com.sankuai.meituan.waimai.thrift.customer.constant.KpTypeEnum;
import com.sankuai.meituan.waimai.thrift.customer.constant.ValidEnum;
import com.sankuai.meituan.waimai.thrift.customer.domain.kp.WmCustomerKp;
import com.sankuai.meituan.waimai.thrift.customer.domain.kp.WmCustomerKpPoi;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.elasticsearch.action.bulk.BulkResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@Slf4j
public class WmCustomerKpTableEventImpl implements ICustomerRelTableEvent {

    private static final String VALID = "valid";

    @Autowired
    private WmCustomerPoiDBMapper wmCustomerPoiDBMapper;

    @Autowired
    private WmCustomerKpPoiService wmCustomerKpPoiService;

    @Autowired
    private WmCustomerPoiListEsService esService;

    @Autowired
    private WmCustomerESService wmCustomerESService;

    @Autowired
    private WmCustomerPoiAttributeService wmCustomerPoiAttributeService;


    @Override
    public WmCustomerRelTableDbusEnum getTable() {
        return WmCustomerRelTableDbusEnum.TABLE_WM_CUSTOMER_KP;
    }

    @Override
    public String handleUpdate(Map<String, String> metaJsonData, String dataMapJson, String diffJson) {
        log.info("WmCustomerKpTableEventImpl handleUpdate::dataMapJson = {},diffJson={}", dataMapJson, diffJson);
        DbusUtils utils = ThriftV2Utils.newThriftV2InstanceForUpdate(metaJsonData, dataMapJson, diffJson);
        //签约人KP变更更新客户ES中
        updateSignerKpCustomerEs(utils.getAftMap());
        //签约人变更的时候更新客户门店属性表的签约人信息
        updateCustomerPoiAttribute(utils);
        WmCustomerKp bean = transToBo(utils.getAftMap());
        if (bean == null) {
            return null;
        }
        boolean isUpdate = checkUpdate(utils.getDiffMap());
        if (!isUpdate) {
            return null;
        }

        List<WmCustomerPoiDB> list = getWmCustomerPoi(bean.getId(), bean.getCustomerId());
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        String compellation = bean.getCompellation();
        Integer kpId = bean.getId();
        if (bean.getValid() == ValidEnum.VALID_NO.getValue()) {
            compellation = "";
            kpId = 0;
        }
        Map<Object, Map<String, Object>> map = Maps.newHashMap();
        for (WmCustomerPoiDB db : list) {
            log.info("db={},kpId={},compellation={}", JSONObject.toJSONString(db), kpId, compellation);
            map.put(db.getId(), esService.makeMap(new String[]{WmCustomerPoiListESFields.OP_MANAGER_ID.getField(), WmCustomerPoiListESFields.OP_MANAGER_NAME.getField()},
                    new Object[]{kpId, compellation}));
        }
        BulkResponse response = esService.bulkUpdate(map);
        if (response == null) {
            return "kp信息修改失败";
        } else {
            return null;
        }
    }

    @Override
    public String handleInsert(Map<String, String> metaJsonData, String dataMapJson) {
        DbusUtils utils = ThriftV2Utils.newThriftV2InstanceForInsert(metaJsonData, dataMapJson);
        //创建签约人KP更新客户ES中
        insertSignerKpCustomerEs(utils.getAftMap());
        //创建签约人KP时更新客户门店属性
        updateCustomerPoiAttribute(utils);
        WmCustomerKp bean = transToBo(utils.getAftMap());
        if (bean == null) {
            return null;
        }
        List<WmCustomerPoiDB> list = getWmCustomerPoi(bean.getId(), bean.getCustomerId());
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        String compellation = bean.getCompellation();
        Integer kpId = bean.getId();
        Map<Object, Map<String, Object>> map = Maps.newHashMap();
        for (WmCustomerPoiDB db : list) {
            log.info("db={},kpId={},compellation={}", kpId, compellation);
            map.put(db.getId(), esService.makeMap(new String[]{WmCustomerPoiListESFields.OP_MANAGER_ID.getField(), WmCustomerPoiListESFields.OP_MANAGER_NAME.getField()},
                    new Object[]{kpId, compellation}));
        }
        BulkResponse response = esService.bulkUpdate(map);
        if (response == null) {
            return "kp信息新增失败";
        } else {
            return null;
        }
    }

    @Override
    public String handleDelete(Map<String, String> metaJsonData, String dataMapJson) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_1", "com.sankuai.meituan.waimai.customer.service.dbus.customerPoiList.WmCustomerKpTableEventImpl.handleDelete(java.util.Map,java.lang.String)");
        DbusUtils utils = ThriftV2Utils.newThriftV2InstanceForDelete(metaJsonData, dataMapJson);
        //签约人KP变更更新客户ES中
        deleteSignerKpCustomerEs(utils.getPreMap());
        //删除签约人KP时更新客户门店属性
        deleteCustomerPoiAttribute(utils);
        WmCustomerKp bean = transToBo(utils.getPreMap());

        if (bean == null) {
            return null;
        }
        List<WmCustomerPoiDB> list = getWmCustomerPoi(bean.getId(), bean.getCustomerId());
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        String compellation = "";
        Integer kpId = 0;
        Map<Object, Map<String, Object>> map = Maps.newHashMap();
        for (WmCustomerPoiDB db : list) {
            log.info("db={},kpId={},compellation={}", kpId, compellation);
            map.put(db.getId(), esService.makeMap(new String[]{WmCustomerPoiListESFields.OP_MANAGER_ID.getField(), WmCustomerPoiListESFields.OP_MANAGER_NAME.getField()},
                    new String[]{kpId.toString(), compellation}));
        }
        BulkResponse response = esService.bulkUpdate(map);
        if (response == null) {
            return "kp信息新增失败";
        } else {
            return null;
        }
    }

    private WmCustomerKp transToBo(Map<String, Object> aftMap) {
        String jsonString = JSON.toJSONString(aftMap);
        WmCustomerKp wmCustomerKp = JSON.parseObject(jsonString, WmCustomerKp.class);
        if (wmCustomerKp.getKpType() != KpTypeEnum.OPMANAGER.getType()) {
            return null;
        }
        return wmCustomerKp;
    }


    private WmCustomerKp commonTransToBo(Map<String, Object> aftMap) {
        String jsonString = JSON.toJSONString(aftMap);
        return JSON.parseObject(jsonString, WmCustomerKp.class);
    }

    private boolean checkUpdate(Map<String, Object> diffMap) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.dbus.customerPoiList.WmCustomerKpTableEventImpl.checkUpdate(java.util.Map)");
        if (MapUtils.isEmpty(diffMap) || !(diffMap.containsKey(WmCustomerPoiListESFields.OP_MANAGER_NAME.getDbField()) || diffMap.containsKey(VALID))) {
            //修改，判断范围是否发生了变化，如果未发生变化则不更新范围的rtree
            return false;
        }
        return true;
    }


    private List<WmCustomerPoiDB> getWmCustomerPoi(Integer kpId, Integer customerId) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_2", "com.sankuai.meituan.waimai.customer.service.dbus.customerPoiList.WmCustomerKpTableEventImpl.getWmCustomerPoi(java.lang.Integer,java.lang.Integer)");
        List<WmCustomerKpPoi> wmCustomerKpPoiList = wmCustomerKpPoiService.getKpRelPoiInfoByKpId(kpId);
        if (CollectionUtils.isEmpty(wmCustomerKpPoiList)) {
            return Lists.newArrayList();
        }

        List<Long> wmPoiIds = wmCustomerKpPoiList.stream().map(x -> x.getWmPoiId()).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(wmCustomerKpPoiList)) {
            return Lists.newArrayList();
        }
        WmCustomerPoiQueryCondtionVo vo = new WmCustomerPoiQueryCondtionVo();
        vo.setCustomerId(customerId);
        vo.setWmPoiIds(wmPoiIds);
        return wmCustomerPoiDBMapper.selectCustomerPoiRelByCondition(vo);
    }


    /**
     * KP变更时更新客户ES
     *
     * @param beanMap
     */
    private void updateSignerKpCustomerEs(Map<String, Object> beanMap) {
        WmCustomerKp wmCustomerKp = commonTransToBo(beanMap);
        if (wmCustomerKp == null || wmCustomerKp.getCustomerId() <= 0) {
            return;
        }
        if (wmCustomerKp.getKpType() != KpTypeEnum.SIGNER.getType()
                || KpSignerStateMachine.EFFECT.getState() != wmCustomerKp.getState()) {
            //非生效签约人变更则不处理
            return;
        }
        try {
            if (wmCustomerKp.getValid() == ValidEnum.VALID_NO.getValue()) {
                //如果签约人KP逻辑删除则需要重新查询签约人KP后再更新ES
                wmCustomerESService.syncCustomerKpToUpsertEs(wmCustomerKp.getCustomerId(), null);
            } else {
                wmCustomerESService.syncCustomerKpToUpsertEs(wmCustomerKp.getCustomerId(), wmCustomerKp);
            }
        } catch (Exception e) {
            log.error("updateCustomerEsWhenKpUpdate 同步更新客户es数据失败 kpId={} ", wmCustomerKp.getId(), e);
        }
        return;
    }

    /**
     * KP变更时更新客户ES
     *
     * @param beanMap
     */
    private void deleteSignerKpCustomerEs(Map<String, Object> beanMap) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.service.dbus.customerPoiList.WmCustomerKpTableEventImpl.deleteSignerKpCustomerEs(java.util.Map)");
        WmCustomerKp wmCustomerKp = commonTransToBo(beanMap);
        if (wmCustomerKp == null || wmCustomerKp.getCustomerId() <= 0) {
            return;
        }
        if (wmCustomerKp.getKpType() != KpTypeEnum.SIGNER.getType()
                || KpSignerStateMachine.EFFECT.getState() != wmCustomerKp.getState()) {
            //非生效签约人变更则不处理
            return;
        }
        try {
            wmCustomerESService.syncCustomerKpToUpsertEs(wmCustomerKp.getCustomerId(), null);
        } catch (Exception e) {
            log.error("updateCustomerEsWhenKpUpdate 同步更新客户es数据失败 kpId={} ", wmCustomerKp.getId(), e);
        }
        return;
    }

    /**
     * KP变更时更新客户ES
     *
     * @param beanMap
     */
    private void insertSignerKpCustomerEs(Map<String, Object> beanMap) {
        WmCustomerKp wmCustomerKp = commonTransToBo(beanMap);
        if (wmCustomerKp == null || wmCustomerKp.getCustomerId() <= 0) {
            return;
        }
        if (wmCustomerKp.getKpType() != KpTypeEnum.SIGNER.getType()
                || KpSignerStateMachine.EFFECT.getState() != wmCustomerKp.getState()) {
            //非生效签约人变更则不处理
            return;
        }
        try {
            wmCustomerESService.syncCustomerKpToUpsertEs(wmCustomerKp.getCustomerId(), wmCustomerKp);
        } catch (Exception e) {
            log.error("updateCustomerEsWhenKpUpdate 同步更新客户es数据失败 kpId={} ", wmCustomerKp.getId(), e);
        }
        return;
    }

    /**
     * KP新增或者修改时更新客户门店属性
     *
     * @param utils
     */
    private void updateCustomerPoiAttribute(DbusUtils utils) {
        if (utils == null || MapUtils.isEmpty(utils.getAftMap())) {
            return;
        }
        WmCustomerKp bean = commonTransToBo(utils.getAftMap());
        if (bean == null) {
            return;
        }
        wmCustomerPoiAttributeService.updateForKpUpdateAsy(bean, false);
    }

    /**
     * KP删除时更新客户门店属性
     *
     * @param utils
     */
    private void deleteCustomerPoiAttribute(DbusUtils utils) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_1", "com.sankuai.meituan.waimai.customer.service.dbus.customerPoiList.WmCustomerKpTableEventImpl.deleteCustomerPoiAttribute(com.meituan.dbus.common.DbusUtils)");
        if (utils == null || MapUtils.isEmpty(utils.getAftMap())) {
            return;
        }
        WmCustomerKp bean = commonTransToBo(utils.getAftMap());
        if (bean == null) {
            return;
        }
        bean.setValid(KpConstants.UN_VALID);
        wmCustomerPoiAttributeService.updateForKpUpdateAsy(bean, false);
    }

}
