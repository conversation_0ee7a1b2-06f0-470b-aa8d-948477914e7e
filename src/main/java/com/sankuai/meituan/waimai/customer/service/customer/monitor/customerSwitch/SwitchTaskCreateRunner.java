package com.sankuai.meituan.waimai.customer.service.customer.monitor.customerSwitch;

import com.dianping.frog.sdk.alarm.DXUtil;
import com.dianping.frog.sdk.data.BinlogRawData;
import com.dianping.frog.sdk.data.DmlType;
import com.dianping.frog.sdk.data.RawData;
import com.dianping.frog.sdk.rpc.RpcService;
import com.dianping.frog.sdk.rpc.RpcServiceFactory;
import com.dianping.frog.sdk.runner.DefaultRuleRunner;
import com.dianping.frog.sdk.util.BcpLoggerUtils;
import com.dianping.frog.sdk.util.JsonUtils;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.reflect.TypeToken;
import com.meituan.service.mobile.mtthrift.util.json.JacksonUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Map;

public class SwitchTaskCreateRunner extends DefaultRuleRunner {

    private Gson gson = new GsonBuilder().create();


    @Override
    public boolean filter(RawData rawData, RawData... targetData) throws Exception {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_3", "com.sankuai.meituan.waimai.customer.service.customer.monitor.customerSwitch.SwitchTaskCreateRunner.filter(com.dianping.frog.sdk.data.RawData,com.dianping.frog.sdk.data.RawData[])");
        BinlogRawData binlogRawData = (BinlogRawData) rawData;
        if (binlogRawData.getDmlType() != DmlType.UPDATE) {
            return false;
        }
        String statusOld = binlogRawData.getColumnInfoMap().get("status").getOldValue().toString();
        String statusNew = binlogRawData.getColumnInfoMap().get("status").getNewValue().toString();
        if (statusOld.equals(statusNew) || !"100110".equals(statusNew)) {
            return false;
        }
        return true;
    }

    @Override
    public String check(RawData rawData, RawData... rawData2) throws Exception {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.customer.monitor.customerSwitch.SwitchTaskCreateRunner.check(com.dianping.frog.sdk.data.RawData,com.dianping.frog.sdk.data.RawData[])");
        BinlogRawData binlogRawData = (BinlogRawData) rawData;
        String switchBasicId = binlogRawData.getColumnInfoMap().get("id").getNewValue().toString();
        String fromCustomerId = binlogRawData.getColumnInfoMap().get("from_biz_id").getNewValue().toString();

        try {
            RpcService rpcService = RpcServiceFactory.getThriftInstance("com.sankuai.meituan.waimai.poibizflow.thrift.switcher.WmPoiSwitchThriftService",
                    "com.sankuai.waimai.e.poibizflow", 10000, null, "8436");
            String result = rpcService.invoke("getSwitchTaskByTaskId",
                    Lists.newArrayList("java.lang.Long", "java.lang.Integer", "java.lang.String"),
                    Lists.newArrayList(switchBasicId, "0", JacksonUtils.serialize("BCP监控")));
            if (StringUtils.isBlank(result)) {
                return String.format("任务创建流程长期未流转任务未找到%s", switchBasicId);
            }
            Map<String, Object> map = toMap(result);
            String afterStatus = map.get("status").toString();
            if (!"100110".equals(afterStatus)) {
                return null;
            }

            Map<String, Object> params = Maps.newHashMap();

            params.put("taskId", 0L);
            params.put("customerId", Integer.valueOf(fromCustomerId));
            params.put("ctime", (System.currentTimeMillis() / 1000 - 65));
            params.put("utime", (System.currentTimeMillis() / 1000 - 55));
            params.put("wmPoiIds", null);
            params.put("switchTaskId", Long.valueOf(switchBasicId));

            rpcService = RpcServiceFactory.getThriftInstance("com.sankuai.meituan.waimai.thrift.customer.service.WmCustomerThriftService",
                    "com.sankuai.waimai.e.customer", 10000, null, "8433");
            result = rpcService.invoke("monitorCustomerSMS",
                    Lists.newArrayList("com.sankuai.meituan.waimai.thrift.customer.domain.customer.monitor.MonitorCustomerSMSDTO"),
                    Lists.newArrayList(JsonUtils.toJson(params)));
            if (!StringUtils.isBlank(result) && !"\"\"".equals(result)) {
                return result;
            }

        } catch (Exception e) {
            BcpLoggerUtils.info("类型转换失败:msg={}" + binlogRawData.toString() + ";异常日志:" + e.getStackTrace());    // 打印日志
        }
        return null;
    }

    @Override
    public void alarm(String s, RawData rawData, RawData... rawData2) throws Exception {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.service.customer.monitor.customerSwitch.SwitchTaskCreateRunner.alarm(java.lang.String,com.dianping.frog.sdk.data.RawData,com.dianping.frog.sdk.data.RawData[])");
        //通过大象公众号发送告警消息, 收件人为告警配置中的告警接收人。
        DXUtil.sendAlarm(s);
    }

    private Map<String, Object> toMap(String json) {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.customer.monitor.customerSwitch.SwitchTaskCreateRunner.toMap(java.lang.String)");
        TypeToken<?> parameterized = TypeToken.getParameterized(Map.class, String.class, Object.class);
        return gson.fromJson(json, parameterized.getType());
    }
}
