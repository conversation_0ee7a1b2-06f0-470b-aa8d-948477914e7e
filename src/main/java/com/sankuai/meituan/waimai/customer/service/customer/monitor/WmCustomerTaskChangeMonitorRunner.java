package com.sankuai.meituan.waimai.customer.service.customer.monitor;

import com.dianping.frog.sdk.alarm.DXUtil;
import com.dianping.frog.sdk.data.BinlogRawData;
import com.dianping.frog.sdk.data.ColumnInfo;
import com.dianping.frog.sdk.data.DmlType;
import com.dianping.frog.sdk.data.RawData;
import com.dianping.frog.sdk.rpc.RpcService;
import com.dianping.frog.sdk.rpc.RpcServiceFactory;
import com.dianping.frog.sdk.runner.DefaultRuleRunner;
import com.dianping.lion.client.util.JsonUtils;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.lang3.StringUtils;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 20230802
 * @desc 客户任务记录表变更对客户任务数据进行监控
 */
public class WmCustomerTaskChangeMonitorRunner extends DefaultRuleRunner {

    /**
     * 检测规则
     *
     * @param triggerData 触发数据：触发数据源的数据，即填写的消息1，但如果是双向校验，则两个流都会触发校验，因此triggerData可能是其中任一个
     * @param targetData  目标数据：根据触发数据源的设置的key对应的其他源数据源的数据；单流校验为空，多流校验则为所有数据源数据除去触发数据
     *                    单流时targetData为length为0的数组；多流时如果匹配阶段没有匹配上，targetData为length为0的数组
     * @return 为null则不进行告警，非null则调用alarm，并把该方法的返回结果作为参数checkResult传入alarm方法
     * 注意：代码内部不要修改RawData及其内部引用所指向的数据，如需要修改，请自行copy RawData对象修改
     */
    @Override
    public String check(RawData triggerData, RawData... targetData) throws Exception {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_5", "com.sankuai.meituan.waimai.customer.service.customer.monitor.WmCustomerTaskChangeMonitorRunner.check(com.dianping.frog.sdk.data.RawData,com.dianping.frog.sdk.data.RawData[])");
        BinlogRawData binlogRawData = (BinlogRawData) triggerData;
        String tableName = binlogRawData.getRealTableName();
        Map<String, ColumnInfo> columnInfoMap = binlogRawData.getColumnInfoMap();
        RpcService rpcService = RpcServiceFactory.getThriftInstance("com.sankuai.meituan.waimai.thrift.customer.service.WmCustomerThriftService",
                "com.sankuai.waimai.e.customer", 3000, "10.24.131.145", "8433");
        Map<String, Object> params = Maps.newHashMap();
        params.put("tableName", tableName);
        params.put("id", columnInfoMap.get("id").getNewValue());
        params.put("customerId", columnInfoMap.get("customer_id").getNewValue());
        params.put("sceneType", columnInfoMap.get("scene_type").getNewValue());
        params.put("taskType", columnInfoMap.get("task_type").getNewValue());
        params.put("bizTaskId", columnInfoMap.get("biz_task_id").getNewValue());
        params.put("bizType", columnInfoMap.get("biz_type").getNewValue());
        params.put("bizId", columnInfoMap.get("biz_id").getNewValue());
        params.put("status", columnInfoMap.get("status").getNewValue());
        params.put("signTaskId", columnInfoMap.get("sign_task_id").getNewValue());
        if (binlogRawData.getDmlType() == DmlType.UPDATE) {
            params.put("oldValid", columnInfoMap.get("valid").getOldValue());
            params.put("oldStatus", columnInfoMap.get("status").getOldValue());
            params.put("oldSignTaskId", columnInfoMap.get("sign_task_id").getOldValue());
        }


        //操作类型
        params.put("operateType", binlogRawData.getDmlType());

        String result = rpcService.invoke("monitorCustomerTaskByTaskChange",
                Lists.newArrayList("com.sankuai.meituan.waimai.thrift.customer.domain.customer.monitor.MonitorCustomerTaskDTO"),
                Lists.newArrayList(JsonUtils.toJson(params)));

        if (!StringUtils.isBlank(result) && !result.equals("null") && !result.equals("\"\"")) {
            return result;
        }

        return null;
    }

    /**
     * 默认情况下alarm方法会自动发送大象告警(不要覆盖该方法)，如需通过mafka或者泛化调用等方式请复写alarm方法
     *
     * @param checkResult 是check方法的返回结果，传入alarm方便进行告警
     * @param triggerData 触发数据：触发数据源的数据
     * @param targetData  目标数据：根据触发数据源的设置的key对应的其他源数据源的数据；单流校验为空，多流校验则为所有数据源数据除去触发数据
     */
    @Override
    public void alarm(String checkResult, RawData triggerData, RawData... targetData) throws Exception {
        com.dianping.cat.Cat.logEvent("INVALID_METHOD_4", "com.sankuai.meituan.waimai.customer.service.customer.monitor.WmCustomerTaskChangeMonitorRunner.alarm(java.lang.String,com.dianping.frog.sdk.data.RawData,com.dianping.frog.sdk.data.RawData[])");
        DXUtil.sendAlarm(checkResult);
    }
}