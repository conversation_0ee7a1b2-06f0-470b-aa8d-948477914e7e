package com.sankuai.meituan.waimai.customer.service.sc.thirdworkplace.config;

import com.sankuai.djdata.readata.openapi.enums.PopRateType;
import com.sankuai.djdata.readata.openapi.model.PopParam;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.List;

@RunWith(MockitoJUnitRunner.class)
public class OneServicePopParamWrapperTest {

    @Test
    public void testBuildDiffPopParam_DiffRateInfoIsNull() {
        // mock
        OneServicePopParamWrapper wrapper = new OneServicePopParamWrapper();
        wrapper.setDiffRateInfo(null);

        // execute
        PopParam result = wrapper.buildDiffPopParam();

        // assert
        Assert.assertNull(result);
    }

    @Test
    public void testBuildDiffPopParam_RadioRateInfoReteIndexInfoIsEmpty() {
        // mock
        CustomerPopParam diffRateInfo = new CustomerPopParam();
        diffRateInfo.setDim("test_dim");
        diffRateInfo.setOffset(7);

        CustomerPopParam radioRateInfo = new CustomerPopParam();
        radioRateInfo.setReteIndexInfo(new ArrayList<>());

        OneServicePopParamWrapper wrapper = new OneServicePopParamWrapper();
        wrapper.setDiffRateInfo(diffRateInfo);
        wrapper.setRadioRateInfo(radioRateInfo);

        // execute
        PopParam result = wrapper.buildDiffPopParam();

        // assert
        Assert.assertNull(result);
    }

    @Test
    public void testBuildDiffPopParam_RadioRateInfoReteIndexInfoIsNull() {
        // mock
        CustomerPopParam diffRateInfo = new CustomerPopParam();
        diffRateInfo.setDim("test_dim");
        diffRateInfo.setOffset(7);

        CustomerPopParam radioRateInfo = new CustomerPopParam();
        radioRateInfo.setReteIndexInfo(null);

        OneServicePopParamWrapper wrapper = new OneServicePopParamWrapper();
        wrapper.setDiffRateInfo(diffRateInfo);
        wrapper.setRadioRateInfo(radioRateInfo);

        // execute
        PopParam result = wrapper.buildDiffPopParam();

        // assert
        Assert.assertNull(result);
    }

    @Test
    public void testBuildDiffPopParam_Success() {
        // mock
        List<CustomerPopParam.ReteIndexInfo> reteIndexInfoList = new ArrayList<>();
        CustomerPopParam.ReteIndexInfo reteIndexInfo = new CustomerPopParam.ReteIndexInfo();
        reteIndexInfo.setIndex("test_index");
        reteIndexInfo.setDerivedIndexName("test_derived_name");
        reteIndexInfo.setDerivedIndexId("test_derived_id");
        reteIndexInfoList.add(reteIndexInfo);

        CustomerPopParam diffRateInfo = new CustomerPopParam();
        diffRateInfo.setDim("test_dim");
        diffRateInfo.setOffset(7);
        diffRateInfo.setReteIndexInfo(reteIndexInfoList);

        CustomerPopParam radioRateInfo = new CustomerPopParam();
        radioRateInfo.setReteIndexInfo(reteIndexInfoList);

        OneServicePopParamWrapper wrapper = new OneServicePopParamWrapper();
        wrapper.setDiffRateInfo(diffRateInfo);
        wrapper.setRadioRateInfo(radioRateInfo);

        // execute
        PopParam result = wrapper.buildDiffPopParam();

        // assert
        Assert.assertNotNull(result);
        Assert.assertEquals(PopRateType.RISE_PP_VALUE.getCode(), result.getRateInfo().getRateType());
        Assert.assertEquals("test_dim", result.getRateInfo().getDim());
        Assert.assertEquals(7, result.getRateInfo().getOffset());
    }

    @Test
    public void testBuildRadioPopParam_RadioRateInfoIsNull() {
        // mock
        OneServicePopParamWrapper wrapper = new OneServicePopParamWrapper();
        wrapper.setRadioRateInfo(null);

        // execute
        PopParam result = wrapper.buildRadioPopParam();

        // assert
        Assert.assertNull(result);
    }

    @Test
    public void testBuildRadioPopParam_RadioRateInfoReteIndexInfoIsEmpty() {
        // mock
        CustomerPopParam radioRateInfo = new CustomerPopParam();
        radioRateInfo.setDim("test_dim");
        radioRateInfo.setOffset(7);
        radioRateInfo.setReteIndexInfo(new ArrayList<>());

        OneServicePopParamWrapper wrapper = new OneServicePopParamWrapper();
        wrapper.setRadioRateInfo(radioRateInfo);

        // execute
        PopParam result = wrapper.buildRadioPopParam();

        // assert
        Assert.assertNull(result);
    }

    @Test
    public void testBuildRadioPopParam_RadioRateInfoReteIndexInfoIsNull() {
        // mock
        CustomerPopParam radioRateInfo = new CustomerPopParam();
        radioRateInfo.setDim("test_dim");
        radioRateInfo.setOffset(7);
        radioRateInfo.setReteIndexInfo(null);

        OneServicePopParamWrapper wrapper = new OneServicePopParamWrapper();
        wrapper.setRadioRateInfo(radioRateInfo);

        // execute
        PopParam result = wrapper.buildRadioPopParam();

        // assert
        Assert.assertNull(result);
    }

    @Test
    public void testBuildRadioPopParam_Success() {
        // mock
        List<CustomerPopParam.ReteIndexInfo> reteIndexInfoList = new ArrayList<>();
        CustomerPopParam.ReteIndexInfo reteIndexInfo = new CustomerPopParam.ReteIndexInfo();
        reteIndexInfo.setIndex("test_index");
        reteIndexInfo.setDerivedIndexName("test_derived_name");
        reteIndexInfo.setDerivedIndexId("test_derived_id");
        reteIndexInfoList.add(reteIndexInfo);

        CustomerPopParam radioRateInfo = new CustomerPopParam();
        radioRateInfo.setDim("test_dim");
        radioRateInfo.setOffset(7);
        radioRateInfo.setReteIndexInfo(reteIndexInfoList);

        OneServicePopParamWrapper wrapper = new OneServicePopParamWrapper();
        wrapper.setRadioRateInfo(radioRateInfo);

        // execute
        PopParam result = wrapper.buildRadioPopParam();

        // assert
        Assert.assertNotNull(result);
        Assert.assertEquals(PopRateType.RISE_RATIO.getCode(), result.getRateInfo().getRateType());
        Assert.assertEquals("test_dim", result.getRateInfo().getDim());
        Assert.assertEquals(7, result.getRateInfo().getOffset());
    }
}
