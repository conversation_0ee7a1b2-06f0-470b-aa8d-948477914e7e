package com.sankuai.meituan.waimai.customer.service.sc.thirdworkplace;

import com.sankuai.meituan.waimai.customer.adapter.WmEmployClient;
import com.sankuai.meituan.waimai.customer.adapter.WmVirtualOrgServiceAdaptor;
import com.sankuai.meituan.waimai.customer.dao.sc.WmSchoolMapper;
import com.sankuai.meituan.waimai.customer.domain.sc.WmSchoolDB;
import com.sankuai.meituan.waimai.customer.domain.sc.WmSchoolSearchCondition;
import com.sankuai.meituan.waimai.infra.constants.WmVirtualOrgSourceEnum;
import com.sankuai.meituan.waimai.infra.domain.WmEmploy;
import com.sankuai.meituan.waimai.infra.domain.WmVirtualOrg;

import com.sankuai.meituan.waimai.thrift.exception.WmSchCantException;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

@RunWith(MockitoJUnitRunner.class)
public class ThirdWorkplaceOuterServiceTest {

    @InjectMocks
    private ThirdWorkplaceOuterService thirdWorkplaceOuterService;

    @Mock
    private WmEmployClient employClient;

    @Mock
    private WmVirtualOrgServiceAdaptor wmVirtualOrgServiceAdaptor;

    @Mock
    private WmSchoolMapper wmSchoolMapper;

    @Before
    public void setUp() throws WmSchCantException {
        setupBasicMocks();
    }

    private void setupBasicMocks() throws WmSchCantException {
        // Mock 员工信息
        WmEmploy employ = new WmEmploy();
        employ.setName("测试员工");
        employ.setMisId("test123");
        employ.setUid(123);
        Mockito.when(employClient.getEmployById(Mockito.anyInt())).thenReturn(employ);
        Mockito.when(employClient.getEmployByMisId(Mockito.anyString())).thenReturn(employ);

        // Mock 学校信息
        WmSchoolDB school = new WmSchoolDB();
        school.setSchoolId(1);
        school.setSchoolName("测试学校");
        Mockito.when(wmSchoolMapper.selectSchoolBySchoolId(Mockito.anyInt())).thenReturn(school);

        List<WmSchoolDB> schools = new ArrayList<>();
        schools.add(school);
        Mockito.when(wmSchoolMapper.selectByCondition(Mockito.any(WmSchoolSearchCondition.class))).thenReturn(schools);

        // Mock 组织信息
        List<WmVirtualOrg> orgList = new ArrayList<>();
        WmVirtualOrg org = new WmVirtualOrg();
        org.setId(1);
        org.setName("测试组织");
        orgList.add(org);
        Mockito.when(wmVirtualOrgServiceAdaptor.getOrgsByUid(Mockito.anyInt(), Mockito.anyByte(), Mockito.anyInt())).thenReturn(orgList);
    }

    @Test
    public void testGetEmployInfoByUid_Success() {
        // mock
        WmEmploy mockEmploy = new WmEmploy();
        mockEmploy.setUid(123);
        mockEmploy.setName("测试用户");
        Mockito.when(employClient.getEmployById(123)).thenReturn(mockEmploy);

        // execute
        WmEmploy result = thirdWorkplaceOuterService.getEmployInfoByUid(123);

        // assert
        Assert.assertNotNull(result);
        Assert.assertEquals(123, result.getUid());
        Mockito.verify(employClient).getEmployById(123);
    }

    @Test
    public void testGetEmployInfoByUid_NullInput() {
        // execute
        WmEmploy result = thirdWorkplaceOuterService.getEmployInfoByUid(null);

        // assert
        Assert.assertNull(result);
    }

    @Test
    public void testGetEmployInfoByMis_Success() {
        // mock
        WmEmploy mockEmploy = new WmEmploy();
        mockEmploy.setMisId("test123");
        mockEmploy.setName("测试用户");
        Mockito.when(employClient.getEmployByMisId("test123")).thenReturn(mockEmploy);

        // execute
        WmEmploy result = thirdWorkplaceOuterService.getEmployInfoByMis("test123");

        // assert
        Assert.assertNotNull(result);
        Assert.assertEquals("test123", result.getMisId());
        Mockito.verify(employClient).getEmployByMisId("test123");
    }

    @Test
    public void testGetEmployInfoByMis_NullInput() {
        // execute
        WmEmploy result = thirdWorkplaceOuterService.getEmployInfoByMis(null);

        // assert
        Assert.assertNull(result);
    }

    @Test
    public void testBuildOrgInfo_Success() throws WmSchCantException {
        // mock
        WmEmploy mockEmploy = new WmEmploy();
        mockEmploy.setUid(123);
        mockEmploy.setName("测试用户");

        List<WmVirtualOrg> orgList = new ArrayList<>();
        WmVirtualOrg org1 = new WmVirtualOrg();
        org1.setName("组织1");
        WmVirtualOrg org2 = new WmVirtualOrg();
        org2.setName("组织2");
        orgList.add(org1);
        orgList.add(org2);

        Mockito.when(wmVirtualOrgServiceAdaptor.getOrgsByUid(Mockito.anyInt(), Mockito.anyByte(), Mockito.anyInt()))
                .thenReturn(orgList);

        // execute
        String result = thirdWorkplaceOuterService.buildOrgInfo(mockEmploy, WmVirtualOrgSourceEnum.WAIMAI);

        // assert
        Assert.assertEquals("组织1/组织2", result);
    }

    @Test
    public void testBuildOrgInfo_NullEmploy() {
        // execute
        String result = thirdWorkplaceOuterService.buildOrgInfo(null, WmVirtualOrgSourceEnum.WAIMAI);

        // assert
        Assert.assertEquals("未知", result);
    }

    @Test
    public void testBatchGetEmployInfo() throws Exception {
        // 使用反射测试私有方法
        Method method = ThirdWorkplaceOuterService.class.getDeclaredMethod("batchGetEmployInfo", Set.class);
        method.setAccessible(true);

        // mock
        Set<Integer> employIds = new HashSet<>(Arrays.asList(1, 2));

        // execute
        @SuppressWarnings("unchecked")
        Map<Integer, WmEmploy> result = (Map<Integer, WmEmploy>) method.invoke(thirdWorkplaceOuterService, employIds);

        // assert
        Assert.assertNotNull(result);
        Assert.assertEquals(2, result.size());
    }

    @Test
    public void testBatchGetEmployInfoByMis() throws Exception {
        // 使用反射测试私有方法
        Method method = ThirdWorkplaceOuterService.class.getDeclaredMethod("batchGetEmployInfoByMis", Set.class);
        method.setAccessible(true);

        // mock
        Set<String> employMisIds = new HashSet<>(Arrays.asList("test1", "test2"));

        // execute
        @SuppressWarnings("unchecked")
        Map<String, WmEmploy> result = (Map<String, WmEmploy>) method.invoke(thirdWorkplaceOuterService, employMisIds);

        // assert
        Assert.assertNotNull(result);
        Assert.assertEquals(2, result.size());
    }

    @Test
    public void testBatchGetSchoolInfo() throws Exception {
        // 使用反射测试私有方法
        Method method = ThirdWorkplaceOuterService.class.getDeclaredMethod("batchGetSchoolInfo", Set.class);
        method.setAccessible(true);

        // mock
        Set<Integer> schoolIds = new HashSet<>(Arrays.asList(1, 2));

        // Mock 学校信息
        WmSchoolDB school1 = new WmSchoolDB();
        school1.setSchoolId(1);
        school1.setSchoolName("测试学校1");
        WmSchoolDB school2 = new WmSchoolDB();
        school2.setSchoolId(2);
        school2.setSchoolName("测试学校2");
        List<WmSchoolDB> schools = Arrays.asList(school1, school2);

        Mockito.when(wmSchoolMapper.selectByCondition(Mockito.any(WmSchoolSearchCondition.class))).thenReturn(schools);

        // execute
        @SuppressWarnings("unchecked")
        Map<Integer, WmSchoolDB> result = (Map<Integer, WmSchoolDB>) method.invoke(thirdWorkplaceOuterService, schoolIds);

        // assert
        Assert.assertNotNull(result);
        Assert.assertEquals(2, result.size());
    }

    @Test
    public void testBatchGetOrgInfo() throws WmSchCantException {
        // mock
        Map<Integer, WmEmploy> employMap = new HashMap<>();
        WmEmploy employ1 = new WmEmploy();
        employ1.setUid(1);
        WmEmploy employ2 = new WmEmploy();
        employ2.setUid(2);
        employMap.put(1, employ1);
        employMap.put(2, employ2);

        List<WmVirtualOrg> orgList = new ArrayList<>();
        WmVirtualOrg org = new WmVirtualOrg();
        org.setId(1);
        org.setName("测试组织");
        orgList.add(org);

        Mockito.when(wmVirtualOrgServiceAdaptor.getOrgsByUid(Mockito.anyInt(), Mockito.anyByte(), Mockito.anyInt()))
                .thenReturn(orgList);

        // execute
        Map<Integer, List<WmVirtualOrg>> result = thirdWorkplaceOuterService.batchGetOrgInfoByUid(employMap,WmVirtualOrgSourceEnum.WAIMAI);

        // assert
        Assert.assertNotNull(result);
        Assert.assertEquals(2, result.size());
    }

    @Test
    public void testBatchGetOrgInfoByMis() throws WmSchCantException {
        // mock
        Map<String, WmEmploy> employMisMap = new HashMap<>();
        WmEmploy employ1 = new WmEmploy();
        employ1.setUid(1);
        employ1.setMisId("test1");
        WmEmploy employ2 = new WmEmploy();
        employ2.setUid(2);
        employ2.setMisId("test2");
        employMisMap.put("test1", employ1);
        employMisMap.put("test2", employ2);

        List<WmVirtualOrg> orgList = new ArrayList<>();
        WmVirtualOrg org = new WmVirtualOrg();
        org.setId(1);
        org.setName("测试组织");
        orgList.add(org);

        Mockito.when(wmVirtualOrgServiceAdaptor.getOrgsByUid(Mockito.anyInt(), Mockito.anyByte(), Mockito.anyInt()))
                .thenReturn(orgList);

        // execute
        Map<String, List<WmVirtualOrg>> result = thirdWorkplaceOuterService.batchGetOrgInfoByMis(employMisMap);

        // assert
        Assert.assertNotNull(result);
        Assert.assertEquals(2, result.size());
    }

    @Test
    public void testBatchGetOrgInfo_EmptyInput() {
        // execute
        Map<Integer, List<WmVirtualOrg>> result = thirdWorkplaceOuterService.batchGetOrgInfoByUid(new HashMap<>(), WmVirtualOrgSourceEnum.WAIMAI);

        // assert
        Assert.assertNotNull(result);
        Assert.assertEquals(0, result.size());
    }

    @Test
    public void testBatchGetOrgInfoByMis_EmptyInput() {
        // execute
        Map<String, List<WmVirtualOrg>> result = thirdWorkplaceOuterService.batchGetOrgInfoByMis(new HashMap<>());

        // assert
        Assert.assertNotNull(result);
        Assert.assertEquals(0, result.size());
    }
}