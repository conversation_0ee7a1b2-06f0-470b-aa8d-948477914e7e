package com.sankuai.meituan.waimai.customer.service.sc.thrift;

import com.sankuai.djdata.readata.openapi.QueryContext;
import com.sankuai.meituan.waimai.customer.service.sc.thirdworkplace.*;
import com.sankuai.meituan.waimai.customer.util.SSOUtil;
import com.sankuai.meituan.waimai.customer.adapter.*;
import com.sankuai.meituan.waimai.customer.config.MccConfig;
import com.sankuai.meituan.waimai.customer.service.sc.thirdworkplace.config.OneServiceQueryMappingConfig;
import com.sankuai.meituan.waimai.customer.service.sc.thirdworkplace.wrapper.OneServiceDataConverter;
import com.sankuai.meituan.waimai.customer.service.sc.thirdworkplace.wrapper.OneServiceQueryResultBo;
import com.sankuai.meituan.waimai.customer.service.sc.thirdworkplace.wrapper.QueryContextFactory;
import com.sankuai.meituan.waimai.gis.client.thrift.dto.city.WmOpenCity;
import com.sankuai.meituan.waimai.infra.domain.WmUniAor;
import com.sankuai.meituan.waimai.thrift.customer.domain.sc.thirdworkplace.WmScThirdWorkplaceQueryBo;
import com.sankuai.meituan.waimai.thrift.customer.domain.sc.thirdworkplace.WmScThirdWorkplaceQueryListDTO;
import com.sankuai.meituan.waimai.thrift.customer.domain.sc.thirdworkplace.WmScThirdWorkplaceQueryMetricsDTO;
import com.sankuai.meituan.waimai.thrift.exception.WmSchCantException;

import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RunWith(MockitoJUnitRunner.class)
public class WmScThirdWorkplaceThriftAssembleTest {

    @InjectMocks
    private WmScThirdWorkplaceThriftAssemble wmScThirdWorkplaceThriftAssemble;

    @Mock
    private OneServiceDataConverter oneServiceDataConverter;

    @Mock
    private WmAorServiceAdapter wmAorServiceAdapter;

    @Mock
    private WmOpenCityServiceAdapter wmOpenCityServiceAdapter;

    @Mock
    private QueryContextFactory queryContextFactory;

    @Mock
    private ThirdWorkplaceOuterService thirdWorkplaceOuterService;

    private MockedStatic<MccConfig> mccConfigMockedStatic;
    private MockedStatic<SSOUtil> ssoUtilMockedStatic;

    @Before
    public void setUp() throws Exception {
        // 确保之前的静态 mock 已经关闭
        if (mccConfigMockedStatic != null) {
            mccConfigMockedStatic.close();
        }
        if (ssoUtilMockedStatic != null) {
            ssoUtilMockedStatic.close();
        }

        // Mock MccConfig 静态方法
        mccConfigMockedStatic = Mockito.mockStatic(MccConfig.class);
        OneServiceQueryMappingConfig mockConfig = createMockConfig();
        mccConfigMockedStatic.when(MccConfig::getOneServiceQueryMappingConfig).thenReturn(mockConfig);
        mccConfigMockedStatic.when(MccConfig::getAdminDefaultQueryOrgId).thenReturn(1001);
        mccConfigMockedStatic.when(MccConfig::getThirdWorkplaceOrgIdMapping).thenReturn(new HashMap<>());
        mccConfigMockedStatic.when(MccConfig::thirdWorkplaceConcurrentSwitch).thenReturn(false);

        SSOUtil.SsoUser ssoUser = new SSOUtil.SsoUser();
        ssoUser.setName("wdy");
        ssoUser.setId(1);
        ssoUser.setLogin("wdy");
        ssoUser.setCode("wdy");
        ssoUser.setEmail("<EMAIL>");
        // Mock SSOUtil 静态方法
        ssoUtilMockedStatic = Mockito.mockStatic(SSOUtil.class);
        ssoUtilMockedStatic.when(SSOUtil::getUser).thenReturn(ssoUser);

        // 设置基础Mock
        setupBasicMocks();
    }

    @After
    public void tearDown() {
        if (mccConfigMockedStatic != null) {
            mccConfigMockedStatic.close();
            mccConfigMockedStatic = null;
        }
        if (ssoUtilMockedStatic != null) {
            ssoUtilMockedStatic.close();
            ssoUtilMockedStatic = null;
        }
    }

    private void setupBasicMocks() throws WmSchCantException {
        // Mock QueryContextFactory
        QueryContext mockQueryContext = Mockito.mock(QueryContext.class);
        Mockito.when(queryContextFactory.buildListQueryContext(Mockito.any())).thenReturn(mockQueryContext);
        Mockito.when(queryContextFactory.buildCountQueryContext(Mockito.any())).thenReturn(mockQueryContext);
        Mockito.when(queryContextFactory.buildMetricsQueryContext(Mockito.any())).thenReturn(mockQueryContext);

        // Mock 蜂窝信息
        WmUniAor wmUniAor = new WmUniAor();
        wmUniAor.setName("测试蜂窝");
        wmUniAor.setId(1);
        Mockito.when(wmAorServiceAdapter.getAorInfoById(Mockito.anyInt())).thenReturn(wmUniAor);

        // Mock 城市信息
        WmOpenCity city = new WmOpenCity();
        city.setCityName("北京");
        city.setCityId(1);
        Mockito.when(wmOpenCityServiceAdapter.getCityByCityId(Mockito.anyInt())).thenReturn(city);
    }

    @Test
    public void testWmScThirdWorkplaceQueryBO2ListQueryContext_Success() throws WmSchCantException {
        // mock
        WmScThirdWorkplaceQueryBo queryBo = createValidQueryBo();
        QueryContext mockQueryContext = Mockito.mock(QueryContext.class);
        Mockito.when(queryContextFactory.buildListQueryContext(queryBo)).thenReturn(mockQueryContext);

        // execute
        QueryContext result = wmScThirdWorkplaceThriftAssemble.wmScThirdWorkplaceQueryBO2ListQueryContext(queryBo);

        // assert
        Assert.assertNotNull(result);
        Mockito.verify(queryContextFactory).buildListQueryContext(queryBo);
    }

    @Test
    public void testWmScThirdWorkplaceQueryBO2CountQueryContext_Success() throws WmSchCantException {
        // mock
        WmScThirdWorkplaceQueryBo queryBo = createValidQueryBo();
        QueryContext mockQueryContext = Mockito.mock(QueryContext.class);
        Mockito.when(queryContextFactory.buildCountQueryContext(queryBo)).thenReturn(mockQueryContext);

        // execute
        QueryContext result = wmScThirdWorkplaceThriftAssemble.wmScThirdWorkplaceQueryBO2CountQueryContext(queryBo);

        // assert
        Assert.assertNotNull(result);
        Mockito.verify(queryContextFactory).buildCountQueryContext(queryBo);
    }

    @Test
    public void testWmScThirdWorkplaceQueryBO2MetricsQueryContext_Success() throws WmSchCantException {
        // mock
        WmScThirdWorkplaceQueryBo queryBo = createValidQueryBo();
        QueryContext mockQueryContext = Mockito.mock(QueryContext.class);
        Mockito.when(queryContextFactory.buildMetricsQueryContext(queryBo)).thenReturn(mockQueryContext);

        // execute
        QueryContext result = wmScThirdWorkplaceThriftAssemble.wmScThirdWorkplaceQueryBO2MetricsQueryContext(queryBo);

        // assert
        Assert.assertNotNull(result);
        Mockito.verify(queryContextFactory).buildMetricsQueryContext(queryBo);
    }

    @Test
    public void testToQueryListDTO_WithTotalCount() throws WmSchCantException {
        // mock
        OneServiceQueryResultBo oneServiceQueryResultBo = new OneServiceQueryResultBo();
        oneServiceQueryResultBo.setDataList(new ArrayList<>());
        Long totalCount = 100L;

        List<WmScThirdWorkplaceQueryListItem> businessItems = new ArrayList<>();
        WmScThirdWorkplaceQueryListItem item = createTestBusinessItem();
        businessItems.add(item);

        Mockito.when(oneServiceDataConverter.convertToWorkplaceListItems(
                Mockito.any(OneServiceQueryMappingConfig.class),
                Mockito.eq(oneServiceQueryResultBo))).thenReturn(businessItems);

        // execute
        WmScThirdWorkplaceQueryListDTO result = wmScThirdWorkplaceThriftAssemble.toQueryListDTO(oneServiceQueryResultBo, totalCount);

        // assert
        Assert.assertNotNull(result);
        Assert.assertEquals(totalCount, result.getTotal());
        Assert.assertEquals(Integer.valueOf(200), result.getCode());
        Assert.assertEquals("success", result.getMessage());
        Assert.assertNotNull(result.getList());
        Assert.assertEquals(1, result.getList().size());
    }

    @Test
    public void testToQueryListDTO_WithConcurrentSwitch() throws WmSchCantException {
        // mock 并发开关为true
        mccConfigMockedStatic.when(MccConfig::thirdWorkplaceConcurrentSwitch).thenReturn(true);

        OneServiceQueryResultBo oneServiceQueryResultBo = new OneServiceQueryResultBo();
        oneServiceQueryResultBo.setDataList(new ArrayList<>());
        Long totalCount = 100L;

        List<WmScThirdWorkplaceQueryListItem> businessItems = new ArrayList<>();
        WmScThirdWorkplaceQueryListItem item = createTestBusinessItem();
        businessItems.add(item);

        Mockito.when(oneServiceDataConverter.convertToWorkplaceListItems(
                Mockito.any(OneServiceQueryMappingConfig.class),
                Mockito.eq(oneServiceQueryResultBo))).thenReturn(businessItems);

        // execute
        WmScThirdWorkplaceQueryListDTO result = wmScThirdWorkplaceThriftAssemble.toQueryListDTO(oneServiceQueryResultBo, totalCount);

        // assert
        Assert.assertNotNull(result);
        Assert.assertEquals(totalCount, result.getTotal());
        Assert.assertEquals(Integer.valueOf(200), result.getCode());
        Assert.assertEquals("success", result.getMessage());
        Assert.assertNotNull(result.getList());
        Assert.assertEquals(1, result.getList().size());
    }

    @Test
    public void testToQueryListDTO_WithConcurrentSwitch_MultipleItems() throws WmSchCantException {
        // mock 并发开关为true
        mccConfigMockedStatic.when(MccConfig::thirdWorkplaceConcurrentSwitch).thenReturn(true);

        OneServiceQueryResultBo oneServiceQueryResultBo = new OneServiceQueryResultBo();
        oneServiceQueryResultBo.setDataList(new ArrayList<>());
        Long totalCount = 300L;

        // 创建多个测试数据项
        List<WmScThirdWorkplaceQueryListItem> businessItems = new ArrayList<>();
        for (int i = 1; i <= 3; i++) {
            WmScThirdWorkplaceQueryListItem item = createTestBusinessItem();
            item.setSchoolId((long) i);
            item.setSchoolName("测试学校" + i);
            item.setFirstPhysicalCity(i);
            item.setAor(i);
            item.setSchoolResponsiblePerson("test" + i);
            item.setChannelOwnerMis(100 + i);
            businessItems.add(item);
        }

        Mockito.when(oneServiceDataConverter.convertToWorkplaceListItems(
                Mockito.any(OneServiceQueryMappingConfig.class),
                Mockito.eq(oneServiceQueryResultBo))).thenReturn(businessItems);

        // execute
        WmScThirdWorkplaceQueryListDTO result = wmScThirdWorkplaceThriftAssemble.toQueryListDTO(oneServiceQueryResultBo, totalCount);

        // assert
        Assert.assertNotNull(result);
        Assert.assertEquals(totalCount, result.getTotal());
        Assert.assertEquals(Integer.valueOf(200), result.getCode());
        Assert.assertEquals("success", result.getMessage());
        Assert.assertNotNull(result.getList());
        Assert.assertEquals(3, result.getList().size());
    }

    @Test
    public void testToQueryListDTO_WithConcurrentSwitch_EmptyList() throws WmSchCantException {
        // mock 并发开关为true
        mccConfigMockedStatic.when(MccConfig::thirdWorkplaceConcurrentSwitch).thenReturn(true);

        OneServiceQueryResultBo oneServiceQueryResultBo = new OneServiceQueryResultBo();
        oneServiceQueryResultBo.setDataList(new ArrayList<>());
        Long totalCount = 0L;

        List<WmScThirdWorkplaceQueryListItem> businessItems = new ArrayList<>();

        Mockito.when(oneServiceDataConverter.convertToWorkplaceListItems(
                Mockito.any(OneServiceQueryMappingConfig.class),
                Mockito.eq(oneServiceQueryResultBo))).thenReturn(businessItems);

        // execute
        WmScThirdWorkplaceQueryListDTO result = wmScThirdWorkplaceThriftAssemble.toQueryListDTO(oneServiceQueryResultBo, totalCount);

        // assert
        Assert.assertNotNull(result);
        Assert.assertEquals(totalCount, result.getTotal());
        Assert.assertEquals(Integer.valueOf(200), result.getCode());
        Assert.assertEquals("success", result.getMessage());
        Assert.assertNotNull(result.getList());
        Assert.assertEquals(0, result.getList().size());
    }

    @Test
    public void testToQueryListDTO_WithConcurrentSwitch_Exception() throws WmSchCantException {
        // mock 并发开关为true
        mccConfigMockedStatic.when(MccConfig::thirdWorkplaceConcurrentSwitch).thenReturn(true);

        OneServiceQueryResultBo oneServiceQueryResultBo = new OneServiceQueryResultBo();
        oneServiceQueryResultBo.setDataList(new ArrayList<>());
        Long totalCount = 100L;

        // Mock 转换过程中抛出异常
        Mockito.when(oneServiceDataConverter.convertToWorkplaceListItems(
                Mockito.any(OneServiceQueryMappingConfig.class),
                Mockito.eq(oneServiceQueryResultBo))).thenThrow(new RuntimeException("并发转换失败"));

        // execute
        WmScThirdWorkplaceQueryListDTO result = wmScThirdWorkplaceThriftAssemble.toQueryListDTO(oneServiceQueryResultBo, totalCount);

        // assert
        Assert.assertNotNull(result);
        Assert.assertEquals(Long.valueOf(0L), result.getTotal());
        Assert.assertEquals(Integer.valueOf(500), result.getCode());
        Assert.assertTrue(result.getMessage().contains("数据转换失败"));
    }

    @Test
    public void testToQueryListDTO_WithException() throws WmSchCantException {
        // mock
        OneServiceQueryResultBo oneServiceQueryResultBo = new OneServiceQueryResultBo();
        oneServiceQueryResultBo.setDataList(new ArrayList<>());
        Long totalCount = 100L;

        Mockito.when(oneServiceDataConverter.convertToWorkplaceListItems(
                Mockito.any(OneServiceQueryMappingConfig.class),
                Mockito.eq(oneServiceQueryResultBo))).thenThrow(new RuntimeException("转换失败"));

        // execute
        WmScThirdWorkplaceQueryListDTO result = wmScThirdWorkplaceThriftAssemble.toQueryListDTO(oneServiceQueryResultBo, totalCount);

        // assert
        Assert.assertNotNull(result);
        Assert.assertEquals(Long.valueOf(0L), result.getTotal());
        Assert.assertEquals(Integer.valueOf(500), result.getCode());
        Assert.assertTrue(result.getMessage().contains("数据转换失败"));
    }

    @Test
    public void testToQueryListDTO_NullInput() {
        // execute
        WmScThirdWorkplaceQueryListDTO result = wmScThirdWorkplaceThriftAssemble.toQueryListDTO(null, 0L);

        // assert
        Assert.assertNotNull(result);
        Assert.assertEquals(Long.valueOf(0L), result.getTotal());
        Assert.assertEquals(Integer.valueOf(200), result.getCode());
    }

    @Test
    public void testToQueryListDTO_NullInput_WithConcurrentSwitch() {
        // mock 并发开关为true
        mccConfigMockedStatic.when(MccConfig::thirdWorkplaceConcurrentSwitch).thenReturn(true);

        // execute
        WmScThirdWorkplaceQueryListDTO result = wmScThirdWorkplaceThriftAssemble.toQueryListDTO(null, 0L);

        // assert
        Assert.assertNotNull(result);
        Assert.assertEquals(Long.valueOf(0L), result.getTotal());
        Assert.assertEquals(Integer.valueOf(200), result.getCode());
    }

    @Test
    public void testToQueryMetricsDTO_Success() throws WmSchCantException {
        // mock
        OneServiceQueryResultBo oneServiceQueryResultBo = new OneServiceQueryResultBo();
        WmScThirdWorkplaceQueryMetrics businessItem = createTestMetrics();

        Mockito.when(oneServiceDataConverter.convertToWorkplaceMetrics(
                Mockito.any(OneServiceQueryMappingConfig.class),
                Mockito.eq(oneServiceQueryResultBo))).thenReturn(businessItem);

        // execute
        WmScThirdWorkplaceQueryMetricsDTO result = wmScThirdWorkplaceThriftAssemble.toQueryMetricsDTO(oneServiceQueryResultBo);

        // assert
        Assert.assertNotNull(result);
    }

    @Test
    public void testToQueryMetricsDTO_WithException() throws WmSchCantException {
        // mock
        OneServiceQueryResultBo oneServiceQueryResultBo = new OneServiceQueryResultBo();

        Mockito.when(oneServiceDataConverter.convertToWorkplaceMetrics(
                Mockito.any(OneServiceQueryMappingConfig.class),
                Mockito.eq(oneServiceQueryResultBo))).thenThrow(new RuntimeException("转换失败"));

        // execute
        WmScThirdWorkplaceQueryMetricsDTO result = wmScThirdWorkplaceThriftAssemble.toQueryMetricsDTO(oneServiceQueryResultBo);

        // assert
        Assert.assertNull(result);
    }

    @Test
    public void testConvertToListItemExcelModel_Success() {
        // mock
        WmScThirdWorkplaceQueryListItem businessItem = createTestBusinessItem();

        // execute
        WmScThirdWorkplaceQueryListItemExcelModel result =
                wmScThirdWorkplaceThriftAssemble.convertToListItemExcelModel(businessItem);

        // assert
        Assert.assertNotNull(result);
    }

    @Test
    public void testConvertToListItemExcelModel_NullInput() {
        // execute
        WmScThirdWorkplaceQueryListItemExcelModel result =
                wmScThirdWorkplaceThriftAssemble.convertToListItemExcelModel(null);

        // assert
        Assert.assertNull(result);
    }

    @Test
    public void testConvertToListItemExcelModel_WithCityException() throws WmSchCantException {
        // mock
        WmScThirdWorkplaceQueryListItem businessItem = createTestBusinessItem();

        Mockito.when(wmOpenCityServiceAdapter.getCityByCityId(1))
                .thenThrow(new WmSchCantException(1, "城市查询失败"));

        // execute
        WmScThirdWorkplaceQueryListItemExcelModel result =
                wmScThirdWorkplaceThriftAssemble.convertToListItemExcelModel(businessItem);

        // assert
        Assert.assertNotNull(result);
    }

    @Test
    public void testConvertToMetricsExcelModel_Success() {
        // mock
        WmScThirdWorkplaceQueryMetrics businessItem = createTestMetrics();

        // execute
        WmScThirdWorkplaceQueryMetricsExcelModel result =
                wmScThirdWorkplaceThriftAssemble.convertToMetricsExcelModel(businessItem);

        // assert
        Assert.assertNotNull(result);
    }

    @Test
    public void testConvertToMetricsExcelModel_NullInput() {
        // execute
        WmScThirdWorkplaceQueryMetricsExcelModel result =
                wmScThirdWorkplaceThriftAssemble.convertToMetricsExcelModel(null);

        // assert
        Assert.assertNull(result);
    }

    @Test
    public void testConvertToListItemExcelModels_Success() {
        // mock
        List<WmScThirdWorkplaceQueryListItem> businessItems = createTestBusinessItems();

        // execute
        List<WmScThirdWorkplaceQueryListItemExcelModel> result =
                wmScThirdWorkplaceThriftAssemble.convertToListItemExcelModels(businessItems);

        // assert
        Assert.assertNotNull(result);
        Assert.assertEquals(1, result.size());
    }

    @Test
    public void testConvertToListItemExcelModels_EmptyList() {
        // mock
        List<WmScThirdWorkplaceQueryListItem> businessItems = new ArrayList<>();

        // execute
        List<WmScThirdWorkplaceQueryListItemExcelModel> result =
                wmScThirdWorkplaceThriftAssemble.convertToListItemExcelModels(businessItems);

        // assert
        Assert.assertNotNull(result);
        Assert.assertEquals(0, result.size());
    }

    @Test
    public void testConvertToListItemExcelModels_NullList() {
        // execute
        List<WmScThirdWorkplaceQueryListItemExcelModel> result =
                wmScThirdWorkplaceThriftAssemble.convertToListItemExcelModels(null);

        // assert
        Assert.assertNotNull(result);
        Assert.assertEquals(0, result.size());
    }

    @Test
    public void testGetPublicOneServiceDataConverter() {
        // execute
        OneServiceDataConverter result = wmScThirdWorkplaceThriftAssemble.getPublicOneServiceDataConverter();

        // assert
        Assert.assertNotNull(result);
        Assert.assertEquals(oneServiceDataConverter, result);
    }

    @Test
    public void testGetPublicOneServiceQueryMappingConfig() throws WmSchCantException {
        // execute
        OneServiceQueryMappingConfig result = wmScThirdWorkplaceThriftAssemble.getPublicOneServiceQueryMappingConfig();

        // assert
        Assert.assertNotNull(result);
    }

    // 创建有效的查询对象
    private WmScThirdWorkplaceQueryBo createValidQueryBo() {
        WmScThirdWorkplaceQueryBo queryBo = new WmScThirdWorkplaceQueryBo();
        queryBo.setPageNo(1);
        queryBo.setPageSize(10);
        queryBo.setStatisticalDataStr("20250702");
        queryBo.setStatisticalDimension("dt");
        queryBo.setSchoolTrilateralOrgType(1);
        return queryBo;
    }

    private WmScThirdWorkplaceQueryListItem createTestBusinessItem() {
        WmScThirdWorkplaceQueryListItem item = new WmScThirdWorkplaceQueryListItem();
        item.setSchoolId(1L);
        item.setSchoolName("测试学校");
        item.setFirstPhysicalCity(1);
        item.setAor(1);
        item.setSchoolResponsiblePerson("test123");
        item.setChannelOwnerMis(123);
        return item;
    }

    private List<WmScThirdWorkplaceQueryListItem> createTestBusinessItems() {
        return Arrays.asList(createTestBusinessItem());
    }

    private WmScThirdWorkplaceQueryMetrics createTestMetrics() {
        WmScThirdWorkplaceQueryMetrics metrics = new WmScThirdWorkplaceQueryMetrics();
        metrics.setSchoolNum("100");
        metrics.setCanteenNum("50");
        metrics.setCluePartnerNum("10");
        metrics.setCluePartnerNumMom("5.0");
        metrics.setIntentionPartnerNum("8");
        metrics.setIntentionPartnerNumMom("3.0");
        metrics.setEntrustPartnerNum("6");
        metrics.setEntrustPartnerNumMom("2.0");
        metrics.setSignPartnerNum("4");
        metrics.setSignPartnerNumMom("1.0");
        metrics.setStallNum("20");
        metrics.setStallNumMom("10.0");
        metrics.setCafeteriaOfflineOpenStallNum("15");
        metrics.setCafeteriaOfflineOpenStallNumMom("8.0");
        metrics.setCanteenPreOnlineStallNum("12");
        metrics.setCanteenPreOnlineStallNumMom("6.0");
        metrics.setOnlinePoiNum("30");
        metrics.setOnlinePoiNumMom("15.0");
        metrics.setOpenPoiNum("25");
        metrics.setOpenPoiNumMom("12.0");
        metrics.setFinOrdNum("100");
        metrics.setFinOrdNumMom("50.0");
        metrics.setUsrAvgFinOrdNum("5.5");
        metrics.setUsrAvgFinOrdNumMom("2.5");
        metrics.setPoiAvgSettleOrdNum("3.2");
        metrics.setPoiAvgSettleOrdNumMom("1.6");
        metrics.setCafeteriaNewUserNum("80");
        metrics.setCafeteriaNewUserNumMom("40.0");
        metrics.setTeaStuNum("2000");
        metrics.setTeaStuNumMom("100.0");
        metrics.setTxnPoiNum("18");
        metrics.setTxnPoiNumMom("9.0");
        return metrics;
    }

    // 创建 Mock 配置对象
    private OneServiceQueryMappingConfig createMockConfig() {
        OneServiceQueryMappingConfig config = new OneServiceQueryMappingConfig();
        config.setItemIndicatorList(Arrays.asList("indicator1", "indicator2"));
        config.setItemQueryDimensionList(Arrays.asList("dimension1", "dimension2"));
        config.setMetricIndicatorList(Arrays.asList("metric1", "metric2"));
        return config;
    }
}