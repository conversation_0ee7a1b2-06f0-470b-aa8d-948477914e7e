package com.sankuai.meituan.waimai.customer.service.sc.thirdworkplace.wrapper;

import com.sankuai.meituan.waimai.customer.service.sc.thirdworkplace.WmScThirdWorkplaceQueryListItem;
import com.sankuai.meituan.waimai.customer.service.sc.thirdworkplace.WmScThirdWorkplaceQueryMetrics;
import com.sankuai.meituan.waimai.customer.service.sc.thirdworkplace.config.OneServiceQueryMappingConfig;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.*;

@RunWith(MockitoJUnitRunner.class)
public class OneServiceDataConverterTest {

    @InjectMocks
    private OneServiceDataConverter oneServiceDataConverter;

    @Test
    public void testColumnsToRows_NullInput() {
        // execute
        List<Map<String, String>> result = oneServiceDataConverter.columnsToRows(null);

        // assert
        Assert.assertTrue(result.isEmpty());
    }

    @Test
    public void testColumnsToRows_EmptyInput() {
        // mock
        Map<String, List<String>> columnData = new HashMap<>();

        // execute
        List<Map<String, String>> result = oneServiceDataConverter.columnsToRows(columnData);

        // assert
        Assert.assertTrue(result.isEmpty());
    }

    @Test
    public void testColumnsToRows_Success() {
        // mock
        Map<String, List<String>> columnData = new HashMap<>();
        columnData.put("name", Arrays.asList("张三", "李四"));
        columnData.put("age", Arrays.asList("25", "30"));
        columnData.put("city", Arrays.asList("北京", "上海"));

        // execute
        List<Map<String, String>> result = oneServiceDataConverter.columnsToRows(columnData);

        // assert
        Assert.assertEquals(2, result.size());
        Assert.assertEquals("张三", result.get(0).get("name"));
        Assert.assertEquals("25", result.get(0).get("age"));
        Assert.assertEquals("北京", result.get(0).get("city"));
        Assert.assertEquals("李四", result.get(1).get("name"));
        Assert.assertEquals("30", result.get(1).get("age"));
        Assert.assertEquals("上海", result.get(1).get("city"));
    }

    @Test
    public void testColumnsToRows_UnequalLengths() {
        // mock
        Map<String, List<String>> columnData = new HashMap<>();
        columnData.put("name", Arrays.asList("张三", "李四", "王五"));
        columnData.put("age", Arrays.asList("25", "30"));

        // execute
        List<Map<String, String>> result = oneServiceDataConverter.columnsToRows(columnData);

        // assert
        Assert.assertEquals(3, result.size());
        Assert.assertEquals("张三", result.get(0).get("name"));
        Assert.assertEquals("25", result.get(0).get("age"));
        Assert.assertEquals("王五", result.get(2).get("name"));
        Assert.assertNull(result.get(2).get("age"));
    }

    @Test
    public void testColumnsToRows_SingleColumn() {
        // mock
        Map<String, List<String>> columnData = new HashMap<>();
        columnData.put("name", Arrays.asList("张三"));

        // execute
        List<Map<String, String>> result = oneServiceDataConverter.columnsToRows(columnData);

        // assert
        Assert.assertEquals(1, result.size());
        Assert.assertEquals("张三", result.get(0).get("name"));
    }

    @Test
    public void testColumnsToRows_EmptyLists() {
        // mock
        Map<String, List<String>> columnData = new HashMap<>();
        columnData.put("name", new ArrayList<>());
        columnData.put("age", new ArrayList<>());

        // execute
        List<Map<String, String>> result = oneServiceDataConverter.columnsToRows(columnData);

        // assert
        Assert.assertTrue(result.isEmpty());
    }

    @Test
    public void testColumnsToRows_NullValues() {
        // mock
        Map<String, List<String>> columnData = new HashMap<>();
        columnData.put("name", Arrays.asList("张三", null, "王五"));
        columnData.put("age", Arrays.asList(null, "30", "35"));

        // execute
        List<Map<String, String>> result = oneServiceDataConverter.columnsToRows(columnData);

        // assert
        Assert.assertEquals(3, result.size());
        Assert.assertEquals("张三", result.get(0).get("name"));
        Assert.assertNull(result.get(0).get("age"));
        Assert.assertNull(result.get(1).get("name"));
        Assert.assertEquals("30", result.get(1).get("age"));
        Assert.assertEquals("王五", result.get(2).get("name"));
        Assert.assertEquals("35", result.get(2).get("age"));
    }

    @Test
    public void testConvertToWorkplaceListItems_NullResultBo() {
        // mock
        OneServiceQueryMappingConfig mappingConfig = new OneServiceQueryMappingConfig();

        // execute
        List<WmScThirdWorkplaceQueryListItem> result = oneServiceDataConverter.convertToWorkplaceListItems(mappingConfig, null);

        // assert
        Assert.assertTrue(result.isEmpty());
    }

    @Test
    public void testConvertToWorkplaceListItems_NullMappingConfig() {
        // mock
        OneServiceQueryResultBo resultBo = new OneServiceQueryResultBo();
        resultBo.setDataList(new ArrayList<>());

        // execute
        List<WmScThirdWorkplaceQueryListItem> result = oneServiceDataConverter.convertToWorkplaceListItems(null, resultBo);

        // assert
        Assert.assertTrue(result.isEmpty());
    }

    @Test
    public void testConvertToWorkplaceListItems_NullDataList() {
        // mock
        OneServiceQueryMappingConfig mappingConfig = new OneServiceQueryMappingConfig();
        OneServiceQueryResultBo resultBo = new OneServiceQueryResultBo();
        resultBo.setDataList(null);

        // execute
        List<WmScThirdWorkplaceQueryListItem> result = oneServiceDataConverter.convertToWorkplaceListItems(mappingConfig, resultBo);

        // assert
        Assert.assertTrue(result.isEmpty());
    }

    @Test
    public void testConvertToWorkplaceListItems_Success() {
        // mock
        OneServiceQueryMappingConfig mappingConfig = new OneServiceQueryMappingConfig();
        mappingConfig.setItemIndicatorList(Arrays.asList("indicator1", "indicator2"));
        Map<String, String> fieldMapping = new HashMap<>();
        fieldMapping.put("source_field", "targetField");
        mappingConfig.setItemMappingConfig(fieldMapping);

        Map<String, String> rowData = new HashMap<>();
        rowData.put("source_field", "test_value");

        OneServiceQueryResultBo resultBo = new OneServiceQueryResultBo();
        resultBo.setDataList(Arrays.asList(rowData));

        // execute
        List<WmScThirdWorkplaceQueryListItem> result = oneServiceDataConverter.convertToWorkplaceListItems(mappingConfig, resultBo);

        // assert
        Assert.assertEquals(1, result.size());
        Assert.assertNotNull(result.get(0));
    }

    @Test
    public void testConvertToWorkplaceListItems_WithComplexMapping() {
        // mock
        OneServiceQueryMappingConfig mappingConfig = new OneServiceQueryMappingConfig();
        mappingConfig.setItemIndicatorList(Arrays.asList("indicator1", "indicator2"));
        Map<String, String> fieldMapping = new HashMap<>();
        fieldMapping.put("workplace_id", "workplaceId");
        fieldMapping.put("workplace_name", "workplaceName");
        fieldMapping.put("status", "status");
        mappingConfig.setItemMappingConfig(fieldMapping);

        Map<String, String> rowData1 = new HashMap<>();
        rowData1.put("workplace_id", "123");
        rowData1.put("workplace_name", "测试工作场所");
        rowData1.put("status", "1");

        Map<String, String> rowData2 = new HashMap<>();
        rowData2.put("workplace_id", "456");
        rowData2.put("workplace_name", "另一个工作场所");
        rowData2.put("status", "0");

        OneServiceQueryResultBo resultBo = new OneServiceQueryResultBo();
        resultBo.setDataList(Arrays.asList(rowData1, rowData2));

        // execute
        List<WmScThirdWorkplaceQueryListItem> result = oneServiceDataConverter.convertToWorkplaceListItems(mappingConfig, resultBo);

        // assert
        Assert.assertEquals(2, result.size());
        Assert.assertNotNull(result.get(0));
        Assert.assertNotNull(result.get(1));
    }

    @Test
    public void testConvertToWorkplaceListItems_EmptyDataList() {
        // mock
        OneServiceQueryMappingConfig mappingConfig = new OneServiceQueryMappingConfig();
        mappingConfig.setItemIndicatorList(Arrays.asList("indicator1"));
        mappingConfig.setItemMappingConfig(new HashMap<>());

        OneServiceQueryResultBo resultBo = new OneServiceQueryResultBo();
        resultBo.setDataList(new ArrayList<>());

        // execute
        List<WmScThirdWorkplaceQueryListItem> result = oneServiceDataConverter.convertToWorkplaceListItems(mappingConfig, resultBo);

        // assert
        Assert.assertTrue(result.isEmpty());
    }

    @Test
    public void testConvertToWorkplaceMetrics_NullResultBo() {
        // mock
        OneServiceQueryMappingConfig mappingConfig = new OneServiceQueryMappingConfig();

        // execute
        WmScThirdWorkplaceQueryMetrics result = oneServiceDataConverter.convertToWorkplaceMetrics(mappingConfig, null);

        // assert
        Assert.assertNull(result);
    }

    @Test
    public void testConvertToWorkplaceMetrics_NullMappingConfig() {
        // mock
        OneServiceQueryResultBo resultBo = new OneServiceQueryResultBo();
        resultBo.setDataList(new ArrayList<>());

        // execute
        WmScThirdWorkplaceQueryMetrics result = oneServiceDataConverter.convertToWorkplaceMetrics(null, resultBo);

        // assert
        Assert.assertNull(result);
    }

    @Test
    public void testConvertToWorkplaceMetrics_EmptyDataList() {
        // mock
        OneServiceQueryMappingConfig mappingConfig = new OneServiceQueryMappingConfig();
        mappingConfig.setMetricIndicatorList(Arrays.asList("metric1"));
        mappingConfig.setMetricMappingConfig(new HashMap<>());

        OneServiceQueryResultBo resultBo = new OneServiceQueryResultBo();
        resultBo.setDataList(new ArrayList<>());

        // execute
        WmScThirdWorkplaceQueryMetrics result = oneServiceDataConverter.convertToWorkplaceMetrics(mappingConfig, resultBo);

        // assert
        Assert.assertNull(result);
    }

    @Test
    public void testConvertToWorkplaceMetrics_Success() {
        // mock
        OneServiceQueryMappingConfig mappingConfig = new OneServiceQueryMappingConfig();
        mappingConfig.setMetricIndicatorList(Arrays.asList("metric1"));
        Map<String, String> fieldMapping = new HashMap<>();
        fieldMapping.put("source_metric", "targetMetric");
        mappingConfig.setMetricMappingConfig(fieldMapping);

        Map<String, String> rowData = new HashMap<>();
        rowData.put("source_metric", "100");

        OneServiceQueryResultBo resultBo = new OneServiceQueryResultBo();
        resultBo.setDataList(Arrays.asList(rowData));

        // execute
        WmScThirdWorkplaceQueryMetrics result = oneServiceDataConverter.convertToWorkplaceMetrics(mappingConfig, resultBo);

        // assert
        Assert.assertNotNull(result);
    }

    @Test
    public void testConvertToWorkplaceMetrics_WithComplexMapping() {
        // mock
        OneServiceQueryMappingConfig mappingConfig = new OneServiceQueryMappingConfig();
        mappingConfig.setMetricIndicatorList(Arrays.asList("metric1", "metric2"));
        Map<String, String> fieldMapping = new HashMap<>();
        fieldMapping.put("total_count", "totalCount");
        fieldMapping.put("active_count", "activeCount");
        mappingConfig.setMetricMappingConfig(fieldMapping);

        Map<String, String> rowData = new HashMap<>();
        rowData.put("total_count", "100");
        rowData.put("active_count", "80");

        OneServiceQueryResultBo resultBo = new OneServiceQueryResultBo();
        resultBo.setDataList(Arrays.asList(rowData));

        // execute
        WmScThirdWorkplaceQueryMetrics result = oneServiceDataConverter.convertToWorkplaceMetrics(mappingConfig, resultBo);

        // assert
        Assert.assertNotNull(result);
    }

    @Test
    public void testConvertToWorkplaceMetrics_NullDataList() {
        // mock
        OneServiceQueryMappingConfig mappingConfig = new OneServiceQueryMappingConfig();
        mappingConfig.setMetricIndicatorList(Arrays.asList("metric1"));
        mappingConfig.setMetricMappingConfig(new HashMap<>());

        OneServiceQueryResultBo resultBo = new OneServiceQueryResultBo();
        resultBo.setDataList(null);

        // execute
        WmScThirdWorkplaceQueryMetrics result = oneServiceDataConverter.convertToWorkplaceMetrics(mappingConfig, resultBo);

        // assert
        Assert.assertNull(result);
    }
}
