package com.sankuai.meituan.waimai.customer.service.sc.thirdworkplace.wrapper;

import com.sankuai.djdata.readata.openapi.QueryContext;
import com.sankuai.djdata.readata.openapi.enums.QueryMode;
import com.sankuai.djdata.readata.openapi.enums.QueryType;
import com.sankuai.meituan.waimai.customer.adapter.WmOpenCityServiceAdapter;
import com.sankuai.meituan.waimai.customer.adapter.WmOrgClient;
import com.sankuai.meituan.waimai.customer.config.MccConfig;
import com.sankuai.meituan.waimai.customer.service.sc.thirdworkplace.config.OneServiceQueryMappingConfig;
import com.sankuai.meituan.waimai.customer.service.sc.thirdworkplace.config.OneServicePopParamWrapper;
import com.sankuai.meituan.waimai.customer.util.SSOUtil;
import com.sankuai.meituan.waimai.gis.client.thrift.dto.city.WmOpenCity;
import com.sankuai.meituan.waimai.thrift.customer.domain.sc.thirdworkplace.WmScThirdWorkplaceQueryBo;
import com.sankuai.meituan.waimai.thrift.exception.WmSchCantException;

import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

@RunWith(MockitoJUnitRunner.class)
public class QueryContextFactoryTest {

    @InjectMocks
    private QueryContextFactory queryContextFactory;

    @Mock
    private OneServiceWrapper oneServiceWrapper;

    @Mock
    private WmOrgClient wmOrgClient;

    @Mock
    private WmOpenCityServiceAdapter wmOpenCityServiceAdapter;

    private MockedStatic<MccConfig> mccConfigMockedStatic;
    private MockedStatic<SSOUtil> ssoUtilMockedStatic;

    @Before
    public void setUp() throws Exception {
        // 确保之前的静态 mock 已经关闭
        if (mccConfigMockedStatic != null) {
            mccConfigMockedStatic.close();
        }
        if (ssoUtilMockedStatic != null) {
            ssoUtilMockedStatic.close();
        }

        // Mock MccConfig 静态方法
        mccConfigMockedStatic = Mockito.mockStatic(MccConfig.class);
        OneServiceQueryMappingConfig mockConfig = createMockConfig();
        mccConfigMockedStatic.when(MccConfig::getOneServiceQueryMappingConfig).thenReturn(mockConfig);
        mccConfigMockedStatic.when(MccConfig::getAdminDefaultQueryOrgId).thenReturn(1001);
        mccConfigMockedStatic.when(MccConfig::getThirdWorkplaceOrgIdMapping).thenReturn(new HashMap<>());

        // Mock SSOUtil 静态方法
        SSOUtil.SsoUser ssoUser = new SSOUtil.SsoUser();
        ssoUser.setName("wdy");
        ssoUser.setId(1);
        ssoUser.setLogin("wdy");
        ssoUser.setCode("wdy");
        ssoUser.setEmail("<EMAIL>");
        ssoUtilMockedStatic = Mockito.mockStatic(SSOUtil.class);
        ssoUtilMockedStatic.when(SSOUtil::getUser).thenReturn(ssoUser);

        // 设置基础Mock
        setupBasicMocks();
    }

    @After
    public void tearDown() {
        if (mccConfigMockedStatic != null) {
            mccConfigMockedStatic.close();
            mccConfigMockedStatic = null;
        }
        if (ssoUtilMockedStatic != null) {
            ssoUtilMockedStatic.close();
            ssoUtilMockedStatic = null;
        }
    }

    private void setupBasicMocks() throws WmSchCantException {
        // Mock OneServiceWrapper
        QueryContext mockQueryContext = Mockito.mock(QueryContext.class);
        Mockito.when(oneServiceWrapper.initQueryContext()).thenReturn(mockQueryContext);
    }

    @Test
    public void testBuildListQueryContext_Success() throws WmSchCantException {
        // mock
        WmScThirdWorkplaceQueryBo queryBo = createValidQueryBo();

        // execute
        QueryContext result = queryContextFactory.buildListQueryContext(queryBo);

        // assert
        Assert.assertNotNull(result);
        Mockito.verify(oneServiceWrapper).initQueryContext();
    }

    @Test
    public void testBuildListQueryContext_WithAllFields() throws WmSchCantException {
        // mock
        WmScThirdWorkplaceQueryBo queryBo = createComplexQueryBo();

        // execute
        QueryContext result = queryContextFactory.buildListQueryContext(queryBo);

        // assert
        Assert.assertNotNull(result);
        Mockito.verify(oneServiceWrapper).initQueryContext();
    }

    @Test
    public void testBuildListQueryContext_WithWeekDimension() throws WmSchCantException {
        // mock
        WmScThirdWorkplaceQueryBo queryBo = createValidQueryBo();
        queryBo.setStatisticalDimension("wk");

        // execute
        QueryContext result = queryContextFactory.buildListQueryContext(queryBo);

        // assert
        Assert.assertNotNull(result);
        Mockito.verify(oneServiceWrapper).initQueryContext();
    }

    @Test
    public void testBuildListQueryContext_WithMonthDimension() throws WmSchCantException {
        // mock
        WmScThirdWorkplaceQueryBo queryBo = createValidQueryBo();
        queryBo.setStatisticalDimension("mo");

        // execute
        QueryContext result = queryContextFactory.buildListQueryContext(queryBo);

        // assert
        Assert.assertNotNull(result);
        Mockito.verify(oneServiceWrapper).initQueryContext();
    }

    @Test
    public void testBuildListQueryContext_NullQueryBo() {
        // execute & assert
        try {
            queryContextFactory.buildListQueryContext(null);
            Assert.fail("Expected exception");
        } catch (Exception e) {
            Assert.assertTrue(e instanceof IllegalArgumentException || e instanceof NullPointerException);
        }
    }

    @Test
    public void testBuildCountQueryContext_Success() throws WmSchCantException {
        // mock
        WmScThirdWorkplaceQueryBo queryBo = createValidQueryBo();
        QueryContext mockQueryContext = Mockito.mock(QueryContext.class);
        Mockito.when(oneServiceWrapper.initQueryContext()).thenReturn(mockQueryContext);

        // execute
        QueryContext result = queryContextFactory.buildCountQueryContext(queryBo);

        // assert
        Assert.assertNotNull(result);
        Mockito.verify(oneServiceWrapper).initQueryContext();
        Mockito.verify(mockQueryContext).setQueryMode(QueryMode.AGGREGATION);
        Mockito.verify(mockQueryContext).setQueryType(QueryType.QUERY_COUNT);
        Mockito.verify(mockQueryContext).setPageInfo(1, 1);
    }

    @Test
    public void testBuildCountQueryContext_NullQueryBo() {
        // execute & assert
        try {
            queryContextFactory.buildCountQueryContext(null);
            Assert.fail("Expected exception");
        } catch (Exception e) {
            Assert.assertTrue(e instanceof IllegalArgumentException || e instanceof NullPointerException);
        }
    }

    @Test
    public void testBuildMetricsQueryContext_Success() throws WmSchCantException {
        // mock
        WmScThirdWorkplaceQueryBo queryBo = createValidQueryBo();

        // execute
        QueryContext result = queryContextFactory.buildMetricsQueryContext(queryBo);

        // assert
        Assert.assertNotNull(result);
        Mockito.verify(oneServiceWrapper).initQueryContext();
    }

    @Test
    public void testBuildMetricsQueryContext_NullQueryBo() {
        // execute & assert
        try {
            queryContextFactory.buildMetricsQueryContext(null);
            Assert.fail("Expected exception");
        } catch (Exception e) {
            Assert.assertTrue(e instanceof IllegalArgumentException || e instanceof NullPointerException);
        }
    }

    @Test
    public void testCheckParam_InvalidPageNo() {
        // mock
        WmScThirdWorkplaceQueryBo queryBo = new WmScThirdWorkplaceQueryBo();
        queryBo.setPageSize(10);
        queryBo.setStatisticalDataStr("20250702");
        queryBo.setStatisticalDimension("dt");
        queryBo.setSchoolTrilateralOrgType(1);

        // execute & assert
        try {
            queryContextFactory.buildListQueryContext(queryBo);
            Assert.fail("Expected exception");
        } catch (IllegalArgumentException e) {
            Assert.assertTrue(e.getMessage().contains("分页参数必填"));
        } catch (WmSchCantException e) {
            throw new RuntimeException(e);
        }
    }

    @Test
    public void testCheckParam_InvalidStatisticalData() {
        // mock
        WmScThirdWorkplaceQueryBo queryBo = new WmScThirdWorkplaceQueryBo();
        queryBo.setPageNo(1);
        queryBo.setPageSize(10);
        queryBo.setStatisticalDimension("dt");
        queryBo.setSchoolTrilateralOrgType(1);

        // execute & assert
        try {
            queryContextFactory.buildListQueryContext(queryBo);
            Assert.fail("Expected exception");
        } catch (IllegalArgumentException | WmSchCantException e) {
            Assert.assertTrue(e.getMessage().contains("日期维度参数必填"));
        }
    }

    @Test
    public void testCheckParam_InvalidOrgType() {
        // mock
        WmScThirdWorkplaceQueryBo queryBo = new WmScThirdWorkplaceQueryBo();
        queryBo.setPageNo(1);
        queryBo.setPageSize(10);
        queryBo.setStatisticalDataStr("20250702");
        queryBo.setStatisticalDimension("dt");

        // execute & assert
        try {
            queryContextFactory.buildListQueryContext(queryBo);
            Assert.fail("Expected exception");
        } catch (IllegalArgumentException | WmSchCantException e) {
            Assert.assertTrue(e.getMessage().contains("学校归属组织架构类型参数必填"));
        }
    }

    // 创建有效的查询对象
    private WmScThirdWorkplaceQueryBo createValidQueryBo() {
        WmScThirdWorkplaceQueryBo queryBo = new WmScThirdWorkplaceQueryBo();
        queryBo.setPageNo(1);
        queryBo.setPageSize(10);
        queryBo.setStatisticalDataStr("20250702");
        queryBo.setStatisticalDimension("dt");
        queryBo.setSchoolTrilateralOrgType(1);
        return queryBo;
    }

    // 创建复杂的查询对象
    private WmScThirdWorkplaceQueryBo createComplexQueryBo() {
        WmScThirdWorkplaceQueryBo queryBo = createValidQueryBo();
        queryBo.setPageSize(20);
        queryBo.setStatisticalDimension("mo");
        queryBo.setSchoolTrilateralOrgType(2);
        queryBo.setCityOrgStructure(Arrays.asList(1001, 1002));
        queryBo.setSchoolEnterpriseOrgStructure(Arrays.asList(2001, 2002));
        queryBo.setSchoolEnterpriseManagers(Arrays.asList("zhangsan", "lisi"));
        queryBo.setCampusDeliveryOrgStructure(Arrays.asList(3001, 3002));
        queryBo.setCities(Arrays.asList(10011, 510002));
        queryBo.setSchools(Arrays.asList(11223, 2002));
        queryBo.setSchoolBusinessType(1);
        queryBo.setSchoolCategory(Arrays.asList(1, 2, 3));
        queryBo.setCooperationStatus(Arrays.asList(1, 0));
        queryBo.setContractTypes(Arrays.asList(0, 1, 2, 3));
        queryBo.setContractMethods(Arrays.asList(1, 2, 3));
        queryBo.setContractOneToOne(1);
        queryBo.setRealOneToOne(0);
        queryBo.setPartnerStage(Arrays.asList(0, 1, 3, 4, -1));
        queryBo.setDeliveryStatus(Arrays.asList(1, 2, 3));
        queryBo.setChurnStatus(Arrays.asList(1, 2, 3));
        return queryBo;
    }

    // 创建 Mock 配置对象
    private OneServiceQueryMappingConfig createMockConfig() {
        OneServiceQueryMappingConfig config = new OneServiceQueryMappingConfig();
        config.setItemIndicatorList(Arrays.asList("indicator1", "indicator2"));
        config.setItemQueryDimensionList(Arrays.asList("dimension1", "dimension2"));
        config.setMetricIndicatorList(Arrays.asList("metric1", "metric2"));

        // 添加 originDsMappingConfig 的 mock
        Map<String, String> originDsMappingConfig = new HashMap<>();
        originDsMappingConfig.put("dt", "dt_dataset_id");
        originDsMappingConfig.put("wk", "wk_dataset_id");
        originDsMappingConfig.put("mo", "mo_dataset_id");
        config.setOriginDsMappingConfig(originDsMappingConfig);

        // 添加 itemMoMDataConfig 的 mock
        Map<String, OneServicePopParamWrapper> itemMoMDataConfig = new HashMap<>();

        OneServicePopParamWrapper dtItemMoMConfig = new OneServicePopParamWrapper();
        dtItemMoMConfig.setRadioRateInfo(null);
        dtItemMoMConfig.setDiffRateInfo(null);
        itemMoMDataConfig.put("dt", dtItemMoMConfig);

        OneServicePopParamWrapper wkItemMoMConfig = new OneServicePopParamWrapper();
        dtItemMoMConfig.setRadioRateInfo(null);
        dtItemMoMConfig.setDiffRateInfo(null);
        itemMoMDataConfig.put("wk", wkItemMoMConfig);

        OneServicePopParamWrapper moItemMoMConfig = new OneServicePopParamWrapper();
        dtItemMoMConfig.setRadioRateInfo(null);
        dtItemMoMConfig.setDiffRateInfo(null);
        itemMoMDataConfig.put("mo", moItemMoMConfig);

        config.setItemMoMDataConfig(itemMoMDataConfig);

        // 添加 metricMoMDataConfig 的 mock
        Map<String, OneServicePopParamWrapper> metricMoMDataConfig = new HashMap<>();

        OneServicePopParamWrapper dtMetricMoMConfig = new OneServicePopParamWrapper();
        dtItemMoMConfig.setRadioRateInfo(null);
        dtItemMoMConfig.setDiffRateInfo(null);
        metricMoMDataConfig.put("dt", dtMetricMoMConfig);

        OneServicePopParamWrapper wkMetricMoMConfig = new OneServicePopParamWrapper();
        dtItemMoMConfig.setRadioRateInfo(null);
        dtItemMoMConfig.setDiffRateInfo(null);
        metricMoMDataConfig.put("wk", wkMetricMoMConfig);

        OneServicePopParamWrapper moMetricMoMConfig = new OneServicePopParamWrapper();
        dtItemMoMConfig.setRadioRateInfo(null);
        dtItemMoMConfig.setDiffRateInfo(null);
        metricMoMDataConfig.put("mo", moMetricMoMConfig);

        config.setMetricMoMDataConfig(metricMoMDataConfig);

        return config;
    }
}