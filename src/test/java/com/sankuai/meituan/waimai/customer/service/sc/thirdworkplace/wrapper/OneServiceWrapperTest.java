package com.sankuai.meituan.waimai.customer.service.sc.thirdworkplace.wrapper;

import com.dianping.lion.client.util.JsonUtils;
import com.meituan.waimai.common.utils.AppKeyUtils;
import com.meituan.waimai.common.utils.JacksonUtils;
import com.sankuai.djdata.readata.openapi.QueryContext;
import com.sankuai.djdata.readata.openapi.enums.BusiUnit;
import com.sankuai.djdata.readata.protocol.openapi.entity.OneServiceResponse;
import com.sankuai.djdata.readata.protocol.openapi.entity.OneServiceResult;
import com.sankuai.meituan.waimai.customer.config.MccConfig;
import com.sankuai.meituan.waimai.thrift.exception.WmSchCantException;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.*;

@RunWith(MockitoJUnitRunner.class)
public class OneServiceWrapperTest {

    @InjectMocks
    private OneServiceWrapper oneServiceWrapper;

    @Mock
    private OneServiceDataConverter oneServiceDataConverter;

    @Test
    public void testQueryDateFromOneService_Success() {
        // mock
        QueryContext queryContext = Mockito.mock(QueryContext.class);
        OneServiceResponse response = Mockito.mock(OneServiceResponse.class);
        OneServiceResult oneServiceResult = Mockito.mock(OneServiceResult.class);

        response.code = 0;
        Map<String, List<String>> data = new HashMap<>();
        data.put("column1", Arrays.asList("value1", "value2"));
        List<Map<String, String>> resultData = Arrays.asList(
                new HashMap<String, String>() {{ put("key1", "value1"); }}
        );

        Mockito.when(queryContext.submit()).thenReturn(response);
        Mockito.when(response.getData()).thenReturn(oneServiceResult);
        Mockito.when(oneServiceResult.getData()).thenReturn(data);
        Mockito.when(oneServiceDataConverter.columnsToRows(data)).thenReturn(resultData);

        try (MockedStatic<AppKeyUtils> appKeyUtilsMock = Mockito.mockStatic(AppKeyUtils.class);
             MockedStatic<MccConfig> mccConfigMock = Mockito.mockStatic(MccConfig.class);
             MockedStatic<JsonUtils> jsonUtilsMock = Mockito.mockStatic(JsonUtils.class);
             MockedStatic<JacksonUtils> jacksonUtilsMock = Mockito.mockStatic(JacksonUtils.class)) {

            appKeyUtilsMock.when(AppKeyUtils::getAppKey).thenReturn("testAppKey");
            mccConfigMock.when(MccConfig::getOneServiceToken).thenReturn("testToken");
            jsonUtilsMock.when(() -> JsonUtils.toJson(Mockito.any())).thenReturn("{}");
            jacksonUtilsMock.when(() -> JacksonUtils.toJson(Mockito.any())).thenReturn("{}");

            // execute
            OneServiceQueryResultBo result = oneServiceWrapper.queryDateFromOneService(queryContext);

            // assert
            Assert.assertNotNull(result);
            Mockito.verify(queryContext).setCaller("testAppKey", "学校食堂系统", "testToken");
            Mockito.verify(queryContext).setBusiUnit(BusiUnit.WAIMAI);
            Mockito.verify(queryContext).submit();
            Mockito.verify(oneServiceDataConverter).columnsToRows(data);
        } catch (WmSchCantException e) {
            throw new RuntimeException(e);
        }
    }

    @Test
    public void testQueryDateFromOneService_ResponseCodeNotZero() {
        // mock
        QueryContext queryContext = Mockito.mock(QueryContext.class);
        OneServiceResponse response = Mockito.mock(OneServiceResponse.class);

        response.code = 1;

        Mockito.when(queryContext.submit()).thenReturn(response);

        try (MockedStatic<AppKeyUtils> appKeyUtilsMock = Mockito.mockStatic(AppKeyUtils.class);
             MockedStatic<MccConfig> mccConfigMock = Mockito.mockStatic(MccConfig.class);
             MockedStatic<JsonUtils> jsonUtilsMock = Mockito.mockStatic(JsonUtils.class);
             MockedStatic<JacksonUtils> jacksonUtilsMock = Mockito.mockStatic(JacksonUtils.class)) {

            appKeyUtilsMock.when(AppKeyUtils::getAppKey).thenReturn("testAppKey");
            mccConfigMock.when(MccConfig::getOneServiceToken).thenReturn("testToken");
            jsonUtilsMock.when(() -> JsonUtils.toJson(Mockito.any())).thenReturn("{}");
            jacksonUtilsMock.when(() -> JacksonUtils.toJson(Mockito.any())).thenReturn("{}");

            // execute
            OneServiceQueryResultBo result = oneServiceWrapper.queryDateFromOneService(queryContext);

            // assert
            Assert.assertNull(result);
            Mockito.verify(queryContext).setCaller("testAppKey", "学校食堂系统", "testToken");
            Mockito.verify(queryContext).setBusiUnit(BusiUnit.WAIMAI);
            Mockito.verify(queryContext).submit();
        } catch (WmSchCantException e) {
            throw new RuntimeException(e);
        }
    }

    @Test
    public void testQueryDateFromOneService_OneServiceResultNull() {
        // mock
        QueryContext queryContext = Mockito.mock(QueryContext.class);
        OneServiceResponse response = Mockito.mock(OneServiceResponse.class);

        response.code = 0;

        Mockito.when(queryContext.submit()).thenReturn(response);
        Mockito.when(response.getData()).thenReturn(null);

        try (MockedStatic<AppKeyUtils> appKeyUtilsMock = Mockito.mockStatic(AppKeyUtils.class);
             MockedStatic<MccConfig> mccConfigMock = Mockito.mockStatic(MccConfig.class);
             MockedStatic<JsonUtils> jsonUtilsMock = Mockito.mockStatic(JsonUtils.class);
             MockedStatic<JacksonUtils> jacksonUtilsMock = Mockito.mockStatic(JacksonUtils.class)) {

            appKeyUtilsMock.when(AppKeyUtils::getAppKey).thenReturn("testAppKey");
            mccConfigMock.when(MccConfig::getOneServiceToken).thenReturn("testToken");
            jsonUtilsMock.when(() -> JsonUtils.toJson(Mockito.any())).thenReturn("{}");
            jacksonUtilsMock.when(() -> JacksonUtils.toJson(Mockito.any())).thenReturn("{}");

            // execute
            OneServiceQueryResultBo result = oneServiceWrapper.queryDateFromOneService(queryContext);

            // assert
            Assert.assertNull(result);
            Mockito.verify(queryContext).setCaller("testAppKey", "学校食堂系统", "testToken");
            Mockito.verify(queryContext).setBusiUnit(BusiUnit.WAIMAI);
            Mockito.verify(queryContext).submit();
        } catch (WmSchCantException e) {
            throw new RuntimeException(e);
        }
    }

    @Test
    public void testInitQueryContext() {
        // execute
        QueryContext result = oneServiceWrapper.initQueryContext();

        // assert
        Assert.assertNotNull(result);
    }

    @Test
    public void testQueryTotalCountFromOneService_Success() {
        // mock
        QueryContext queryContext = Mockito.mock(QueryContext.class);
        OneServiceResponse response = Mockito.mock(OneServiceResponse.class);
        OneServiceResult oneServiceResult = Mockito.mock(OneServiceResult.class);

        response.code = 0;
        Map<String, List<String>> data = new HashMap<>();
        data.put("total", Arrays.asList("7666"));

        List<Map<String, String>> resultData = Arrays.asList(
                new HashMap<String, String>() {{ put("total", "7666"); }}
        );

        Mockito.when(queryContext.submit()).thenReturn(response);
        Mockito.when(response.getData()).thenReturn(oneServiceResult);
        Mockito.when(oneServiceResult.getData()).thenReturn(data);
        Mockito.when(oneServiceDataConverter.columnsToRows(data)).thenReturn(resultData);

        try (MockedStatic<AppKeyUtils> appKeyUtilsMock = Mockito.mockStatic(AppKeyUtils.class);
             MockedStatic<MccConfig> mccConfigMock = Mockito.mockStatic(MccConfig.class);
             MockedStatic<JsonUtils> jsonUtilsMock = Mockito.mockStatic(JsonUtils.class);
             MockedStatic<JacksonUtils> jacksonUtilsMock = Mockito.mockStatic(JacksonUtils.class)) {

            appKeyUtilsMock.when(AppKeyUtils::getAppKey).thenReturn("testAppKey");
            mccConfigMock.when(MccConfig::getOneServiceToken).thenReturn("testToken");
            jsonUtilsMock.when(() -> JsonUtils.toJson(Mockito.any())).thenReturn("{}");
            jacksonUtilsMock.when(() -> JacksonUtils.toJson(Mockito.any())).thenReturn("{}");

            // execute
            Long result = oneServiceWrapper.queryTotalCountFromOneService(queryContext);

            // assert
            Assert.assertEquals(Long.valueOf(7666L), result);
            Mockito.verify(queryContext).setCaller("testAppKey", "学校食堂系统", "testToken");
            Mockito.verify(queryContext).setBusiUnit(BusiUnit.WAIMAI);
            Mockito.verify(queryContext).submit();
            Mockito.verify(oneServiceDataConverter).columnsToRows(data);
        }
    }

    @Test
    public void testQueryTotalCountFromOneService_NoTotalField() {
        // mock
        QueryContext queryContext = Mockito.mock(QueryContext.class);
        OneServiceResponse response = Mockito.mock(OneServiceResponse.class);
        OneServiceResult oneServiceResult = Mockito.mock(OneServiceResult.class);

        response.code = 0;
        Map<String, List<String>> data = new HashMap<>();
        data.put("other_field", Arrays.asList("value1"));

        List<Map<String, String>> resultData = Arrays.asList(
                new HashMap<String, String>() {{ put("other_field", "value1"); }}
        );

        Mockito.when(queryContext.submit()).thenReturn(response);
        Mockito.when(response.getData()).thenReturn(oneServiceResult);
        Mockito.when(oneServiceResult.getData()).thenReturn(data);
        Mockito.when(oneServiceDataConverter.columnsToRows(data)).thenReturn(resultData);

        try (MockedStatic<AppKeyUtils> appKeyUtilsMock = Mockito.mockStatic(AppKeyUtils.class);
             MockedStatic<MccConfig> mccConfigMock = Mockito.mockStatic(MccConfig.class);
             MockedStatic<JsonUtils> jsonUtilsMock = Mockito.mockStatic(JsonUtils.class);
             MockedStatic<JacksonUtils> jacksonUtilsMock = Mockito.mockStatic(JacksonUtils.class)) {

            appKeyUtilsMock.when(AppKeyUtils::getAppKey).thenReturn("testAppKey");
            mccConfigMock.when(MccConfig::getOneServiceToken).thenReturn("testToken");
            jsonUtilsMock.when(() -> JsonUtils.toJson(Mockito.any())).thenReturn("{}");
            jacksonUtilsMock.when(() -> JacksonUtils.toJson(Mockito.any())).thenReturn("{}");

            // execute
            Long result = oneServiceWrapper.queryTotalCountFromOneService(queryContext);

            // assert
            Assert.assertEquals(Long.valueOf(0L), result);
        }
    }

    @Test
    public void testQueryTotalCountFromOneService_EmptyTotalField() {
        // mock
        QueryContext queryContext = Mockito.mock(QueryContext.class);
        OneServiceResponse response = Mockito.mock(OneServiceResponse.class);
        OneServiceResult oneServiceResult = Mockito.mock(OneServiceResult.class);

        response.code = 0;
        Map<String, List<String>> data = new HashMap<>();
        data.put("total", Arrays.asList(""));

        List<Map<String, String>> resultData = Arrays.asList(
                new HashMap<String, String>() {{ put("total", ""); }}
        );

        Mockito.when(queryContext.submit()).thenReturn(response);
        Mockito.when(response.getData()).thenReturn(oneServiceResult);
        Mockito.when(oneServiceResult.getData()).thenReturn(data);
        Mockito.when(oneServiceDataConverter.columnsToRows(data)).thenReturn(resultData);

        try (MockedStatic<AppKeyUtils> appKeyUtilsMock = Mockito.mockStatic(AppKeyUtils.class);
             MockedStatic<MccConfig> mccConfigMock = Mockito.mockStatic(MccConfig.class);
             MockedStatic<JsonUtils> jsonUtilsMock = Mockito.mockStatic(JsonUtils.class);
             MockedStatic<JacksonUtils> jacksonUtilsMock = Mockito.mockStatic(JacksonUtils.class)) {

            appKeyUtilsMock.when(AppKeyUtils::getAppKey).thenReturn("testAppKey");
            mccConfigMock.when(MccConfig::getOneServiceToken).thenReturn("testToken");
            jsonUtilsMock.when(() -> JsonUtils.toJson(Mockito.any())).thenReturn("{}");
            jacksonUtilsMock.when(() -> JacksonUtils.toJson(Mockito.any())).thenReturn("{}");

            // execute
            Long result = oneServiceWrapper.queryTotalCountFromOneService(queryContext);

            // assert
            Assert.assertEquals(Long.valueOf(0L), result);
        }
    }

    @Test
    public void testQueryTotalCountFromOneService_QueryException() {
        // mock
        QueryContext queryContext = Mockito.mock(QueryContext.class);

        Mockito.when(queryContext.submit()).thenThrow(new RuntimeException("OneService查询异常"));

        try (MockedStatic<AppKeyUtils> appKeyUtilsMock = Mockito.mockStatic(AppKeyUtils.class);
             MockedStatic<MccConfig> mccConfigMock = Mockito.mockStatic(MccConfig.class);
             MockedStatic<JsonUtils> jsonUtilsMock = Mockito.mockStatic(JsonUtils.class);
             MockedStatic<JacksonUtils> jacksonUtilsMock = Mockito.mockStatic(JacksonUtils.class)) {

            appKeyUtilsMock.when(AppKeyUtils::getAppKey).thenReturn("testAppKey");
            mccConfigMock.when(MccConfig::getOneServiceToken).thenReturn("testToken");
            jsonUtilsMock.when(() -> JsonUtils.toJson(Mockito.any())).thenReturn("{}");
            jacksonUtilsMock.when(() -> JacksonUtils.toJson(Mockito.any())).thenReturn("{}");

            // execute
            Long result = oneServiceWrapper.queryTotalCountFromOneService(queryContext);

            // assert
            Assert.assertEquals(Long.valueOf(0L), result);
        }
    }

    @Test
    public void testQueryTotalCountFromOneService_NullResult() {
        // mock
        QueryContext queryContext = Mockito.mock(QueryContext.class);
        OneServiceResponse response = Mockito.mock(OneServiceResponse.class);

        response.code = 1; // 非成功状态码，会导致返回null

        Mockito.when(queryContext.submit()).thenReturn(response);

        try (MockedStatic<AppKeyUtils> appKeyUtilsMock = Mockito.mockStatic(AppKeyUtils.class);
             MockedStatic<MccConfig> mccConfigMock = Mockito.mockStatic(MccConfig.class);
             MockedStatic<JsonUtils> jsonUtilsMock = Mockito.mockStatic(JsonUtils.class);
             MockedStatic<JacksonUtils> jacksonUtilsMock = Mockito.mockStatic(JacksonUtils.class)) {

            appKeyUtilsMock.when(AppKeyUtils::getAppKey).thenReturn("testAppKey");
            mccConfigMock.when(MccConfig::getOneServiceToken).thenReturn("testToken");
            jsonUtilsMock.when(() -> JsonUtils.toJson(Mockito.any())).thenReturn("{}");
            jacksonUtilsMock.when(() -> JacksonUtils.toJson(Mockito.any())).thenReturn("{}");

            // execute
            Long result = oneServiceWrapper.queryTotalCountFromOneService(queryContext);

            // assert
            Assert.assertEquals(Long.valueOf(0L), result);
        }
    }

    @Test
    public void testQueryTotalCountFromOneService_EmptyDataList() {
        // mock
        QueryContext queryContext = Mockito.mock(QueryContext.class);
        OneServiceResponse response = Mockito.mock(OneServiceResponse.class);
        OneServiceResult oneServiceResult = Mockito.mock(OneServiceResult.class);

        response.code = 0;
        Map<String, List<String>> data = new HashMap<>();
        List<Map<String, String>> resultData = Collections.emptyList(); // 空数据列表

        Mockito.when(queryContext.submit()).thenReturn(response);
        Mockito.when(response.getData()).thenReturn(oneServiceResult);
        Mockito.when(oneServiceResult.getData()).thenReturn(data);
        Mockito.when(oneServiceDataConverter.columnsToRows(data)).thenReturn(resultData);

        try (MockedStatic<AppKeyUtils> appKeyUtilsMock = Mockito.mockStatic(AppKeyUtils.class);
             MockedStatic<MccConfig> mccConfigMock = Mockito.mockStatic(MccConfig.class);
             MockedStatic<JsonUtils> jsonUtilsMock = Mockito.mockStatic(JsonUtils.class);
             MockedStatic<JacksonUtils> jacksonUtilsMock = Mockito.mockStatic(JacksonUtils.class)) {

            appKeyUtilsMock.when(AppKeyUtils::getAppKey).thenReturn("testAppKey");
            mccConfigMock.when(MccConfig::getOneServiceToken).thenReturn("testToken");
            jsonUtilsMock.when(() -> JsonUtils.toJson(Mockito.any())).thenReturn("{}");
            jacksonUtilsMock.when(() -> JacksonUtils.toJson(Mockito.any())).thenReturn("{}");

            // execute
            Long result = oneServiceWrapper.queryTotalCountFromOneService(queryContext);

            // assert
            Assert.assertEquals(Long.valueOf(0L), result);
        }
    }

    @Test
    public void testQueryTotalCountFromOneService_InvalidNumberFormat() {
        // mock
        QueryContext queryContext = Mockito.mock(QueryContext.class);
        OneServiceResponse response = Mockito.mock(OneServiceResponse.class);
        OneServiceResult oneServiceResult = Mockito.mock(OneServiceResult.class);

        response.code = 0;
        Map<String, List<String>> data = new HashMap<>();
        data.put("total", Arrays.asList("invalid_number"));

        List<Map<String, String>> resultData = Arrays.asList(
                new HashMap<String, String>() {{ put("total", "invalid_number"); }}
        );

        Mockito.when(queryContext.submit()).thenReturn(response);
        Mockito.when(response.getData()).thenReturn(oneServiceResult);
        Mockito.when(oneServiceResult.getData()).thenReturn(data);
        Mockito.when(oneServiceDataConverter.columnsToRows(data)).thenReturn(resultData);

        try (MockedStatic<AppKeyUtils> appKeyUtilsMock = Mockito.mockStatic(AppKeyUtils.class);
             MockedStatic<MccConfig> mccConfigMock = Mockito.mockStatic(MccConfig.class);
             MockedStatic<JsonUtils> jsonUtilsMock = Mockito.mockStatic(JsonUtils.class);
             MockedStatic<JacksonUtils> jacksonUtilsMock = Mockito.mockStatic(JacksonUtils.class)) {

            appKeyUtilsMock.when(AppKeyUtils::getAppKey).thenReturn("testAppKey");
            mccConfigMock.when(MccConfig::getOneServiceToken).thenReturn("testToken");
            jsonUtilsMock.when(() -> JsonUtils.toJson(Mockito.any())).thenReturn("{}");
            jacksonUtilsMock.when(() -> JacksonUtils.toJson(Mockito.any())).thenReturn("{}");

            // execute
            Long result = oneServiceWrapper.queryTotalCountFromOneService(queryContext);

            // assert
            Assert.assertEquals(Long.valueOf(0L), result);
        }
    }
}