package com.sankuai.meituan.waimai.customer.service.sc.thirdworkplace;

import com.sankuai.djdata.readata.openapi.QueryContext;
import com.sankuai.meituan.waimai.customer.config.MccConfig;
import com.sankuai.meituan.waimai.customer.service.common.ExcelExportService;
import com.sankuai.meituan.waimai.customer.service.sc.thirdworkplace.config.OneServiceQueryMappingConfig;
import com.sankuai.meituan.waimai.customer.service.sc.thirdworkplace.wrapper.OneServiceDataConverter;
import com.sankuai.meituan.waimai.customer.service.sc.thirdworkplace.wrapper.OneServiceQueryResultBo;
import com.sankuai.meituan.waimai.customer.service.sc.thirdworkplace.wrapper.OneServiceWrapper;
import com.sankuai.meituan.waimai.customer.service.sc.thrift.WmScThirdWorkplaceThriftAssemble;
import com.sankuai.meituan.waimai.customer.util.SSOUtil;
import com.sankuai.meituan.waimai.thrift.customer.domain.sc.thirdworkplace.*;
import com.sankuai.meituan.waimai.thrift.exception.WmSchCantException;
import com.sankuai.waimai.crm.authenticate.client.service.AuthenticateRoleService;
import com.sankuai.waimai.crm.authenticate.client.service.response.AuthenticateUserRoleAssertResponse;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

@RunWith(MockitoJUnitRunner.class)
public class ThirdWorkplaceServiceTest {

    @InjectMocks
    private ThirdWorkplaceService thirdWorkplaceService;

    @Mock
    private OneServiceWrapper oneServiceWrapper;

    @Mock
    private WmScThirdWorkplaceThriftAssemble wmScThirdWorkplaceThriftAssemble;

    @Mock
    private AuthenticateRoleService authenticateRoleService;

    @Mock
    private ExcelExportService excelExportService;

    @Test
    public void testQueryThirdWorkplaceListDTO() throws WmSchCantException {
        // mock
        WmScThirdWorkplaceQueryBo queryBo = new WmScThirdWorkplaceQueryBo();
        QueryContext listQueryContext = Mockito.mock(QueryContext.class);
        QueryContext countQueryContext = Mockito.mock(QueryContext.class);
        OneServiceQueryResultBo oneServiceQueryResultBo = new OneServiceQueryResultBo();
        WmScThirdWorkplaceQueryListDTO expectedResult = new WmScThirdWorkplaceQueryListDTO();
        expectedResult.setTotal(7666L); // 设置期望的总数

        // Mock 列表查询上下文构建
        Mockito.when(wmScThirdWorkplaceThriftAssemble.wmScThirdWorkplaceQueryBO2ListQueryContext(queryBo))
                .thenReturn(listQueryContext);

        // Mock 总数查询上下文构建（使用任意参数匹配，因为会使用深拷贝的对象）
        Mockito.when(wmScThirdWorkplaceThriftAssemble.wmScThirdWorkplaceQueryBO2CountQueryContext(Mockito.any(WmScThirdWorkplaceQueryBo.class)))
                .thenReturn(countQueryContext);

        // Mock OneService查询调用
        Mockito.when(oneServiceWrapper.queryDateFromOneService(listQueryContext))
                .thenReturn(oneServiceQueryResultBo);

        // Mock 总数查询调用
        Mockito.when(oneServiceWrapper.queryTotalCountFromOneService(countQueryContext))
                .thenReturn(7666L);

        // Mock 结果转换（现在需要传入总数参数）
        Mockito.when(wmScThirdWorkplaceThriftAssemble.toQueryListDTO(oneServiceQueryResultBo, 7666L))
                .thenReturn(expectedResult);

        // execute
        WmScThirdWorkplaceQueryListDTO result = thirdWorkplaceService.queryThirdWorkplaceListDTO(queryBo);

        // assert
        Assert.assertEquals(expectedResult, result);
        Assert.assertEquals(Long.valueOf(7666L), result.getTotal());

        // 验证方法调用
        Mockito.verify(wmScThirdWorkplaceThriftAssemble).wmScThirdWorkplaceQueryBO2ListQueryContext(queryBo);
        Mockito.verify(wmScThirdWorkplaceThriftAssemble).wmScThirdWorkplaceQueryBO2CountQueryContext(Mockito.any(WmScThirdWorkplaceQueryBo.class));
        Mockito.verify(oneServiceWrapper).queryDateFromOneService(listQueryContext);
        Mockito.verify(oneServiceWrapper).queryTotalCountFromOneService(countQueryContext);
        Mockito.verify(wmScThirdWorkplaceThriftAssemble).toQueryListDTO(oneServiceQueryResultBo, 7666L);
    }

    @Test
    public void testQueryThirdWorkplaceMetricsDTO() throws WmSchCantException {
        // mock
        WmScThirdWorkplaceQueryBo queryBo = new WmScThirdWorkplaceQueryBo();
        QueryContext queryContext = Mockito.mock(QueryContext.class);
        OneServiceQueryResultBo oneServiceQueryResultBo = new OneServiceQueryResultBo();
        WmScThirdWorkplaceQueryMetricsDTO expectedResult = new WmScThirdWorkplaceQueryMetricsDTO();

        Mockito.when(wmScThirdWorkplaceThriftAssemble.wmScThirdWorkplaceQueryBO2MetricsQueryContext(queryBo))
                .thenReturn(queryContext);
        Mockito.when(oneServiceWrapper.queryDateFromOneService(queryContext))
                .thenReturn(oneServiceQueryResultBo);
        Mockito.when(wmScThirdWorkplaceThriftAssemble.toQueryMetricsDTO(oneServiceQueryResultBo))
                .thenReturn(expectedResult);

        // execute
        WmScThirdWorkplaceQueryMetricsDTO result = thirdWorkplaceService.queryThirdWorkplaceMetricsDTO(queryBo);

        // assert
        Assert.assertEquals(expectedResult, result);
        Mockito.verify(wmScThirdWorkplaceThriftAssemble).wmScThirdWorkplaceQueryBO2MetricsQueryContext(queryBo);
        Mockito.verify(oneServiceWrapper).queryDateFromOneService(queryContext);
        Mockito.verify(wmScThirdWorkplaceThriftAssemble).toQueryMetricsDTO(oneServiceQueryResultBo);
    }



    @Test(expected = WmSchCantException.class)
    public void testGetThirdWorkplaceQueryEnum_IOException() throws WmSchCantException, IOException {
        // mock
        Mockito.mockStatic(MccConfig.class);
        Mockito.when(MccConfig.getThirdWorkplaceQueryEnum()).thenThrow(new IOException("配置解析错误"));

        // execute
        thirdWorkplaceService.getThirdWorkplaceQueryEnum();
    }

    @Test
    public void testExportThirdWorkplaceListData_FullExport() throws WmSchCantException {
        // mock
        WmScThirdWorkplaceQueryBo queryBo = new WmScThirdWorkplaceQueryBo();
        SSOUtil.SsoUser user = Mockito.mock(SSOUtil.SsoUser.class);
        QueryContext queryContext = Mockito.mock(QueryContext.class);
        OneServiceQueryResultBo oneServiceQueryResultBo = new OneServiceQueryResultBo();
        List<WmScThirdWorkplaceQueryListItem> businessItems = new ArrayList<>();
        List<WmScThirdWorkplaceQueryListItemExcelModel> excelModels = new ArrayList<>();

        Mockito.when(wmScThirdWorkplaceThriftAssemble.wmScThirdWorkplaceQueryBO2ListQueryContext(Mockito.any()))
                .thenReturn(queryContext);
        Mockito.when(oneServiceWrapper.queryDateFromOneService(queryContext))
                .thenReturn(oneServiceQueryResultBo);
        Mockito.when(wmScThirdWorkplaceThriftAssemble.getPublicOneServiceDataConverter())
                .thenReturn(Mockito.mock(OneServiceDataConverter.class));
        Mockito.when(wmScThirdWorkplaceThriftAssemble.getPublicOneServiceQueryMappingConfig())
                .thenReturn(Mockito.mock(OneServiceQueryMappingConfig.class));
        Mockito.when(wmScThirdWorkplaceThriftAssemble.convertToListItemExcelModels(Mockito.anyList()))
                .thenReturn(Collections.emptyList());
        Mockito.when(excelExportService.exportExcel(Mockito.anyList(), Mockito.eq(WmScThirdWorkplaceQueryListItemExcelModel.class),
                        Mockito.any(ByteArrayOutputStream.class), Mockito.eq(user)))
                .thenReturn("http://download.url");

        // execute
        String result = thirdWorkplaceService.exportThirdWorkplaceListData(queryBo, user);

        // assert
        Assert.assertEquals("http://download.url", result);
    }


    @Test(expected = WmSchCantException.class)
    public void testExportThirdWorkplaceListData_NullQueryBo() throws WmSchCantException {
        // mock
        SSOUtil.SsoUser user = Mockito.mock(SSOUtil.SsoUser.class);

        // execute
        thirdWorkplaceService.exportThirdWorkplaceListData(null, user);
    }

    @Test(expected = WmSchCantException.class)
    public void testExportThirdWorkplaceMetricsData_NullQueryBo() throws WmSchCantException {
        // mock
        SSOUtil.SsoUser user = Mockito.mock(SSOUtil.SsoUser.class);

        // execute
        thirdWorkplaceService.exportThirdWorkplaceMetricsData(null, user);
    }

    @Test
    public void testGetUserAssertResult_AdminRole() throws WmSchCantException {
        // mock
        SSOUtil.SsoUser user = Mockito.mock(SSOUtil.SsoUser.class);
        Mockito.when(user.getId()).thenReturn(123L);

        AuthenticateUserRoleAssertResponse adminResponse = new AuthenticateUserRoleAssertResponse();
        adminResponse.setCode(0);
        adminResponse.setAssertResult(true);

        AuthenticateUserRoleAssertResponse otherResponse = new AuthenticateUserRoleAssertResponse();
        otherResponse.setCode(0);
        otherResponse.setAssertResult(false);

        Mockito.when(authenticateRoleService.userRoleAssert(1000008, 123, "campus_third_workplace_admin"))
                .thenReturn(adminResponse);
        Mockito.when(authenticateRoleService.userRoleAssert(1000008, 123, "campus_third_workplace_city"))
                .thenReturn(otherResponse);
        Mockito.when(authenticateRoleService.userRoleAssert(1000008, 123, "campus_third_workplace_ka"))
                .thenReturn(otherResponse);
        Mockito.when(authenticateRoleService.userRoleAssert(1000008, 123, "campus_third_workplace_ps"))
                .thenReturn(otherResponse);

        // execute
        AssertUserRoleDTO result = thirdWorkplaceService.getUserAssertResult(user);

        // assert
        Assert.assertTrue(result.getIsHq());
        Assert.assertNull(result.getIsCity());
        Assert.assertNull(result.getIsCampus());
        Assert.assertNull(result.getIsPs());
        Assert.assertEquals(ThirdWorkplaceQueryRoleEnum.ADMIN.getCode(), result.getSchoolTrilateralOrgType());
    }

    @Test
    public void testGetUserAssertResult_CityRole() throws WmSchCantException {
        // mock
        SSOUtil.SsoUser user = Mockito.mock(SSOUtil.SsoUser.class);
        Mockito.when(user.getId()).thenReturn(123L);

        AuthenticateUserRoleAssertResponse cityResponse = new AuthenticateUserRoleAssertResponse();
        cityResponse.setCode(0);
        cityResponse.setAssertResult(true);

        AuthenticateUserRoleAssertResponse otherResponse = new AuthenticateUserRoleAssertResponse();
        otherResponse.setCode(0);
        otherResponse.setAssertResult(false);

        Mockito.when(authenticateRoleService.userRoleAssert(1000008, 123, "campus_third_workplace_admin"))
                .thenReturn(otherResponse);
        Mockito.when(authenticateRoleService.userRoleAssert(1000008, 123, "campus_third_workplace_city"))
                .thenReturn(cityResponse);
        Mockito.when(authenticateRoleService.userRoleAssert(1000008, 123, "campus_third_workplace_ka"))
                .thenReturn(otherResponse);
        Mockito.when(authenticateRoleService.userRoleAssert(1000008, 123, "campus_third_workplace_ps"))
                .thenReturn(otherResponse);

        // execute
        AssertUserRoleDTO result = thirdWorkplaceService.getUserAssertResult(user);

        // assert
        Assert.assertNull(result.getIsHq());
        Assert.assertTrue(result.getIsCity());
        Assert.assertNull(result.getIsCampus());
        Assert.assertNull(result.getIsPs());
        Assert.assertEquals(ThirdWorkplaceQueryRoleEnum.CITY.getCode(), result.getSchoolTrilateralOrgType());
    }

    @Test
    public void testGetUserAssertResult_KaRole() throws WmSchCantException {
        // mock
        SSOUtil.SsoUser user = Mockito.mock(SSOUtil.SsoUser.class);
        Mockito.when(user.getId()).thenReturn(123L);

        AuthenticateUserRoleAssertResponse kaResponse = new AuthenticateUserRoleAssertResponse();
        kaResponse.setCode(0);
        kaResponse.setAssertResult(true);

        AuthenticateUserRoleAssertResponse otherResponse = new AuthenticateUserRoleAssertResponse();
        otherResponse.setCode(0);
        otherResponse.setAssertResult(false);

        Mockito.when(authenticateRoleService.userRoleAssert(1000008, 123, "campus_third_workplace_admin"))
                .thenReturn(otherResponse);
        Mockito.when(authenticateRoleService.userRoleAssert(1000008, 123, "campus_third_workplace_city"))
                .thenReturn(otherResponse);
        Mockito.when(authenticateRoleService.userRoleAssert(1000008, 123, "campus_third_workplace_ka"))
                .thenReturn(kaResponse);
        Mockito.when(authenticateRoleService.userRoleAssert(1000008, 123, "campus_third_workplace_ps"))
                .thenReturn(otherResponse);

        // execute
        AssertUserRoleDTO result = thirdWorkplaceService.getUserAssertResult(user);

        // assert
        Assert.assertNull(result.getIsHq());
        Assert.assertNull(result.getIsCity());
        Assert.assertTrue(result.getIsCampus());
        Assert.assertNull(result.getIsPs());
        Assert.assertEquals(ThirdWorkplaceQueryRoleEnum.KA.getCode(), result.getSchoolTrilateralOrgType());
    }

    @Test
    public void testGetUserAssertResult_PsRole() throws WmSchCantException {
        // mock
        SSOUtil.SsoUser user = Mockito.mock(SSOUtil.SsoUser.class);
        Mockito.when(user.getId()).thenReturn(123L);

        AuthenticateUserRoleAssertResponse psResponse = new AuthenticateUserRoleAssertResponse();
        psResponse.setCode(0);
        psResponse.setAssertResult(true);

        AuthenticateUserRoleAssertResponse otherResponse = new AuthenticateUserRoleAssertResponse();
        otherResponse.setCode(0);
        otherResponse.setAssertResult(false);

        Mockito.when(authenticateRoleService.userRoleAssert(1000008, 123, "campus_third_workplace_admin"))
                .thenReturn(otherResponse);
        Mockito.when(authenticateRoleService.userRoleAssert(1000008, 123, "campus_third_workplace_city"))
                .thenReturn(otherResponse);
        Mockito.when(authenticateRoleService.userRoleAssert(1000008, 123, "campus_third_workplace_ka"))
                .thenReturn(otherResponse);
        Mockito.when(authenticateRoleService.userRoleAssert(1000008, 123, "campus_third_workplace_ps"))
                .thenReturn(psResponse);

        // execute
        AssertUserRoleDTO result = thirdWorkplaceService.getUserAssertResult(user);

        // assert
        Assert.assertNull(result.getIsHq());
        Assert.assertNull(result.getIsCity());
        Assert.assertNull(result.getIsCampus());
        Assert.assertTrue(result.getIsPs());
        Assert.assertEquals(ThirdWorkplaceQueryRoleEnum.PS.getCode(), result.getSchoolTrilateralOrgType());
    }

    @Test
    public void testGetUserAssertResult_NoRole() throws WmSchCantException {
        // mock
        SSOUtil.SsoUser user = Mockito.mock(SSOUtil.SsoUser.class);
        Mockito.when(user.getId()).thenReturn(123L);

        AuthenticateUserRoleAssertResponse falseResponse = new AuthenticateUserRoleAssertResponse();
        falseResponse.setCode(0);
        falseResponse.setAssertResult(false);

        Mockito.when(authenticateRoleService.userRoleAssert(Mockito.anyInt(), Mockito.anyInt(), Mockito.anyString()))
                .thenReturn(falseResponse);

        // execute
        AssertUserRoleDTO result = thirdWorkplaceService.getUserAssertResult(user);

        // assert
        Assert.assertNull(result.getIsHq());
        Assert.assertNull(result.getIsCity());
        Assert.assertNull(result.getIsCampus());
        Assert.assertNull(result.getIsPs());
        Assert.assertEquals(ThirdWorkplaceQueryRoleEnum.UNKNOWN.getCode(), result.getSchoolTrilateralOrgType());
    }

    @Test(expected = RuntimeException.class)
    public void testGetUserAssertResult_AuthServiceException() throws WmSchCantException {
        // mock
        SSOUtil.SsoUser user = Mockito.mock(SSOUtil.SsoUser.class);
        Mockito.when(user.getId()).thenReturn(123L);

        Mockito.when(authenticateRoleService.userRoleAssert(Mockito.anyInt(), Mockito.anyInt(), Mockito.anyString()))
                .thenThrow(new RuntimeException("权限服务异常"));

        // execute
        thirdWorkplaceService.getUserAssertResult(user);
    }
}