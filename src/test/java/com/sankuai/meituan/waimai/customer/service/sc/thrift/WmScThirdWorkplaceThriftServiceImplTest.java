package com.sankuai.meituan.waimai.customer.service.sc.thrift;

import com.sankuai.meituan.waimai.customer.adapter.WmEmployClient;
import com.sankuai.meituan.waimai.customer.service.sc.thirdworkplace.ThirdWorkplaceService;
import com.sankuai.meituan.waimai.customer.util.SSOUtil;
import com.sankuai.meituan.waimai.thrift.customer.domain.sc.CommonResponse;
import com.sankuai.meituan.waimai.thrift.customer.domain.sc.thirdworkplace.*;
import com.sankuai.meituan.waimai.thrift.exception.WmSchCantException;
import org.apache.thrift.TException;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;

import static com.sankuai.meituan.waimai.thrift.customer.constant.sc.WmScCodeConstants.BIZ_PARA_ERROR;

@RunWith(MockitoJUnitRunner.class)
public class WmScThirdWorkplaceThriftServiceImplTest {

    @InjectMocks
    private WmScThirdWorkplaceThriftServiceImpl wmScThirdWorkplaceThriftServiceImpl;

    @Mock
    private ThirdWorkplaceService thirdWorkplaceService;

    @Mock
    private WmEmployClient wmEmployClient;

    @Test
    public void testQueryThirdWorkplaceList_Success() {
        // mock
        WmScThirdWorkplaceQueryBo queryBo = new WmScThirdWorkplaceQueryBo();
        queryBo.setPageNo(1);
        queryBo.setPageSize(10);

        WmScThirdWorkplaceQueryListDTO mockDTO = new WmScThirdWorkplaceQueryListDTO();
        mockDTO.setCode(200);
        mockDTO.setMessage("success");
        mockDTO.setList(new ArrayList<>());

        try {
            Mockito.when(thirdWorkplaceService.queryThirdWorkplaceListDTO(queryBo)).thenReturn(mockDTO);
        } catch (WmSchCantException e) {
            // ignore
        }

        // execute
        WmScThirdWorkplaceQueryListResp result = wmScThirdWorkplaceThriftServiceImpl.queryThirdWorkplaceList(queryBo);

        // assert
        Assert.assertNotNull(result);
        Assert.assertNotNull(result.getData());
        Assert.assertEquals(Integer.valueOf(200), result.getData().getCode());
        Assert.assertEquals("success", result.getData().getMessage());
    }

    @Test
    public void testQueryThirdWorkplaceList_Exception() {
        // mock
        WmScThirdWorkplaceQueryBo queryBo = new WmScThirdWorkplaceQueryBo();

        try {
            Mockito.when(thirdWorkplaceService.queryThirdWorkplaceListDTO(queryBo))
                    .thenThrow(new WmSchCantException(BIZ_PARA_ERROR, "查询失败"));
        } catch (WmSchCantException e) {
            // ignore
        }

        // execute
        WmScThirdWorkplaceQueryListResp result = wmScThirdWorkplaceThriftServiceImpl.queryThirdWorkplaceList(queryBo);

        // assert
        Assert.assertNotNull(result);
    }

    @Test
    public void testQueryThirdWorkplaceMetrics_Success() {
        // mock
        WmScThirdWorkplaceQueryBo queryBo = new WmScThirdWorkplaceQueryBo();
        queryBo.setPageNo(1);
        queryBo.setPageSize(10);

        WmScThirdWorkplaceQueryMetricsDTO mockDTO = new WmScThirdWorkplaceQueryMetricsDTO();
        mockDTO.setSchoolCount("100");
        mockDTO.setCanteenCount("50");

        try {
            Mockito.when(thirdWorkplaceService.queryThirdWorkplaceMetricsDTO(queryBo)).thenReturn(mockDTO);
        } catch (WmSchCantException e) {
            // ignore
        }

        // execute
        WmScThirdWorkplaceQueryMetricsResp result = wmScThirdWorkplaceThriftServiceImpl.queryThirdWorkplaceMetrics(queryBo);

        // assert
        Assert.assertNotNull(result);
        Assert.assertNotNull(result.getData());
        Assert.assertEquals("100", result.getData().getSchoolCount());
        Assert.assertEquals("50", result.getData().getCanteenCount());
    }

    @Test
    public void testQueryThirdWorkplaceMetrics_Exception() {
        // mock
        WmScThirdWorkplaceQueryBo queryBo = new WmScThirdWorkplaceQueryBo();

        try {
            Mockito.when(thirdWorkplaceService.queryThirdWorkplaceMetricsDTO(queryBo))
                    .thenThrow(new WmSchCantException(BIZ_PARA_ERROR, "查询失败"));
        } catch (WmSchCantException e) {
            // ignore
        }

        // execute
        WmScThirdWorkplaceQueryMetricsResp result = wmScThirdWorkplaceThriftServiceImpl.queryThirdWorkplaceMetrics(queryBo);

        // assert
        Assert.assertNotNull(result);
    }

    @Test
    public void testExportThirdWorkplaceListData_Success() throws WmSchCantException, TException {
        // mock
        WmScThirdWorkplaceQueryBo queryBo = new WmScThirdWorkplaceQueryBo();

        SSOUtil.SsoUser mockUser = Mockito.mock(SSOUtil.SsoUser.class);
        Mockito.when(mockUser.getId()).thenReturn(123L);

        try (MockedStatic<SSOUtil> mockedSSOUtil = Mockito.mockStatic(SSOUtil.class)) {
            mockedSSOUtil.when(SSOUtil::getUser).thenReturn(mockUser);

            Mockito.when(thirdWorkplaceService.exportThirdWorkplaceListData(queryBo, mockUser))
                    .thenReturn("http://test.com/download");
            Mockito.when(wmEmployClient.getMisById(123)).thenReturn("test.mis");

            // execute
            CommonResponse result = wmScThirdWorkplaceThriftServiceImpl.exportThirdWorkplaceListData(queryBo);

            // assert
            Assert.assertNotNull(result);
            Assert.assertNotNull(result.getData());
            Assert.assertTrue(result.getData().toString().contains("异步导出"));
        }
    }

    @Test
    public void testExportThirdWorkplaceMetricsData_Success() throws WmSchCantException, TException {
        // mock
        WmScThirdWorkplaceQueryBo queryBo = new WmScThirdWorkplaceQueryBo();

        SSOUtil.SsoUser mockUser = Mockito.mock(SSOUtil.SsoUser.class);
        Mockito.when(mockUser.getId()).thenReturn(123L);

        try (MockedStatic<SSOUtil> mockedSSOUtil = Mockito.mockStatic(SSOUtil.class)) {
            mockedSSOUtil.when(SSOUtil::getUser).thenReturn(mockUser);

            Mockito.when(thirdWorkplaceService.exportThirdWorkplaceMetricsData(queryBo, mockUser))
                    .thenReturn("http://test.com/download");
            Mockito.when(wmEmployClient.getMisById(123)).thenReturn("test.mis");

            // execute
            CommonResponse result = wmScThirdWorkplaceThriftServiceImpl.exportThirdWorkplaceMetricsData(queryBo);

            // assert
            Assert.assertNotNull(result);
            Assert.assertNotNull(result.getData());
            Assert.assertTrue(result.getData().toString().contains("异步导出"));
        }
    }

    @Test
    public void testGetThirdWorkplaceQueryEnums_Success() throws WmSchCantException, TException {
        // mock
        WmScThirdWorkplaceQueryEnumDTO mockDTO = new WmScThirdWorkplaceQueryEnumDTO();
        // 注意：这里需要根据实际的DTO字段来设置，暂时注释掉不存在的方法
        // mockDTO.setSchoolBusinessTypes(new ArrayList<>());
        // mockDTO.setSchoolCategories(new ArrayList<>());

        Mockito.when(thirdWorkplaceService.getThirdWorkplaceQueryEnum()).thenReturn(mockDTO);

        // execute
        WmScThirdWorkplaceQueryEnumResp result = wmScThirdWorkplaceThriftServiceImpl.getThirdWorkplaceQueryEnums();

        // assert
        Assert.assertNotNull(result);
        Assert.assertNotNull(result.getData());
        // Assert.assertNotNull(result.getData().getSchoolBusinessTypes());
        // Assert.assertNotNull(result.getData().getSchoolCategories());
    }

    @Test
    public void testGetThirdWorkplaceQueryEnums_Exception() throws WmSchCantException, TException {
        // mock
        Mockito.when(thirdWorkplaceService.getThirdWorkplaceQueryEnum())
                .thenThrow(new WmSchCantException(BIZ_PARA_ERROR, "获取枚举失败"));

        // execute
        WmScThirdWorkplaceQueryEnumResp result = wmScThirdWorkplaceThriftServiceImpl.getThirdWorkplaceQueryEnums();

        // assert
        Assert.assertNotNull(result);
    }

    @Test
    public void testAssertUserRole_Success() throws WmSchCantException, TException {
        // mock
        SSOUtil.SsoUser mockUser = Mockito.mock(SSOUtil.SsoUser.class);

        AssertUserRoleDTO mockDTO = new AssertUserRoleDTO();
        // 注意：这里需要根据实际的DTO字段来设置，暂时注释掉不存在的方法
        // mockDTO.setCanQuery(true);
        // mockDTO.setRoleType(1);

        try (MockedStatic<SSOUtil> mockedSSOUtil = Mockito.mockStatic(SSOUtil.class)) {
            mockedSSOUtil.when(SSOUtil::getUser).thenReturn(mockUser);
            Mockito.when(thirdWorkplaceService.getUserAssertResult(mockUser)).thenReturn(mockDTO);

            // execute
            AssertUserRoleResp result = wmScThirdWorkplaceThriftServiceImpl.assertUserRole();

            // assert
            Assert.assertNotNull(result);
            Assert.assertNotNull(result.getData());
            // Assert.assertTrue(result.getData().getCanQuery());
            // Assert.assertEquals(Integer.valueOf(1), result.getData().getRoleType());
        }
    }

    @Test
    public void testAssertUserRole_Exception() throws WmSchCantException, TException {
        // mock
        SSOUtil.SsoUser mockUser = Mockito.mock(SSOUtil.SsoUser.class);

        try (MockedStatic<SSOUtil> mockedSSOUtil = Mockito.mockStatic(SSOUtil.class)) {
            mockedSSOUtil.when(SSOUtil::getUser).thenReturn(mockUser);
            Mockito.when(thirdWorkplaceService.getUserAssertResult(mockUser))
                    .thenThrow(new WmSchCantException(BIZ_PARA_ERROR, "权限验证失败"));

            // execute
            AssertUserRoleResp result = wmScThirdWorkplaceThriftServiceImpl.assertUserRole();

            // assert
            Assert.assertNotNull(result);
        }
    }
}

